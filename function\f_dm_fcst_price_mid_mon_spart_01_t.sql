-- Name: f_dm_fcst_price_mid_mon_spart_01_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_mid_mon_spart_01_t(f_period_year bigint DEFAULT NULL::bigint, f_version_id bigint DEFAULT NULL::bigint, f_year_diff integer DEFAULT NULL::integer, OUT x_result_status text)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
创建时间：20241106
创建人  ：qwx1110218
背景描述：定价指数ICT-定价指数-MID_MON_SPART表，只保留1个版本，取数逻辑:
          将做完ETL处理后的源表数据，做如下处理：
          ① 先按合同号层级，分年对每个月的数据进行月累计，月累计之后的值，需要将累计结果值，量：为0、为空、为负数的；额：为空、为负数的值作无效化处理，不参与后续所有逻辑计算。（额为0需要保留）
          只要满足量的条件或者额的条件之一，整条数据作无效化处理；
          并对缺失月份的合同号数据进行补齐（不跨年）；
          ② 将累计完的每个月的合同号层级的数据，按月卷积到SPART层级；
          ③ 路径1：国内海外、地区部、代表处等3个字段值需要造一版全球/全选的数据；
          路径2：子网系统部需要造一版全选数据
【对于此表的补齐】举例：如果某合同202401无金额/数量发生，202402有金额/数量发生，则1月累计金额与数量不会替换为2月的发生累计金额和数量；
                        如某合同1~3月有金额/数量发生，5月有金额/数量发生，4月无金额/数量发生，则需要对4月进行累计金额/数量前向不跨年补齐;
参数描述：参数一： f_period_year 年份
          参数二： f_version_id 版本号
          参数五： x_result_status 运行状态返回值 'SUCCESS'为成功，'FAILED'为失败
事例：SELECT FIN_DM_OPT_FOI.f_dm_fcst_price_mid_mon_spart_01_t()
*/

DECLARE
  V_SP_NAME    VARCHAR2(100) := 'FIN_DM_OPT_FOI.f_dm_fcst_price_mid_mon_spart_01_t'; --存储过程名称
  V_STEP_MUM   BIGINT; --步骤号
  V_VERSION_ID BIGINT; --新的版本号
  V_SOURCE_VERSION_ID BIGINT; --目标表版本号
  V_PERIOD_YEAR BIGINT; -- 年份
  V_BEGIN_DATE TIMESTAMP;  -- 取4年的数据
  V_SQL  TEXT; -- SQL逻辑
  V_FROM_TABLE     VARCHAR(100);
  V_TO_TABLE       VARCHAR(100);
  V_TEMP_TABLE     VARCHAR(100);
  V_YEAR           INT;

BEGIN
  X_RESULT_STATUS = 'SUCCESS';

  --1.开始日志
  V_STEP_MUM := 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
     F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_MUM,
     F_CAL_LOG_DESC => V_SP_NAME||'开始执行'
  );

  V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_PRICE_GROUP_ORD_SPART_SRAN_T';
  V_TO_TABLE   := 'FIN_DM_OPT_FOI.DM_FCST_PRICE_MID_MON_SPART_01_T';
  V_TEMP_TABLE := 'ACTUAL_APD_TEMP';

 if MONTH(CURRENT_TIMESTAMP) = 1
  then V_YEAR = YEAR(NOW()) -1;
  ELSE
  V_YEAR = YEAR(NOW());
  END IF ;
  
  V_BEGIN_DATE := TO_DATE(TO_CHAR(V_YEAR-3)||'01','YYYYMM');
  

  -- 更新来源表的“审视结果（Y、业务补录异常数据）”字段
  UPDATE FIN_DM_OPT_FOI.DM_FCST_PRICE_GROUP_ORD_SPART_SRAN_T SET EXAMINE_RESULT = NULL WHERE EXAMINE_RESULT = 'Y';


  -- 不为8号时按选定版本（包括正在running的版本）更新异常标识
  IF(DAY(CURRENT_TIMESTAMP) <> 8) THEN
    UPDATE FIN_DM_OPT_FOI.DM_FCST_PRICE_GROUP_ORD_SPART_SRAN_T T1 SET EXAMINE_RESULT = 'Y'
     WHERE EXISTS(SELECT 1 FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_EXAMINE_RESULT_T T2
                   WHERE T1.SPART_CODE = T2.SPART_CODE
                     AND T1.LV1_PROD_LIST_CODE = T2.LV1_PROD_LIST_CODE
                     AND T1.LV2_PROD_LIST_CODE = T2.LV2_PROD_LIST_CODE
                     AND T1.LV3_PROD_LIST_CODE = T2.LV3_PROD_LIST_CODE
                     AND T1.LV4_PROD_LIST_CODE = T2.LV4_PROD_LIST_CODE
	                   AND T1.PERIOD_ID BETWEEN T2.BEGIN_DATE  AND T2.END_DATE
                     AND T1.REGION_HRMS_ORG_CODE = T2.REGION_CODE
                     AND T1.REPOFFICE_ORG_CODE = T2.REPOFFICE_CODE
                     AND T1.OVERSEAS_FLAG = T2.OVERSEA_FLAG
                     AND NVL(T1.HW_CONTRACT_NUM,1) = NVL(T2.HW_CONTRACT_NUM,1)
                     AND UPPER(T2.PAGE_FLAG) = 'ABNORMAL'
                     AND T2.VERSION_ID = (SELECT VERSION_ID
                                            FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
                                           WHERE DEL_FLAG = 'N'
                                             AND STATUS = 0
	                  	                       AND IS_RUNNING = 'Y'
                                             AND UPPER(DATA_TYPE) = 'DATA_REVIEW'
                                           ORDER BY LAST_UPDATE_DATE DESC
                                           LIMIT 1
                                         )
                 )
    ;

  -- 为8号时按最新版本（不包括正在running的版本）更新异常标识
  ELSEIF(DAY(CURRENT_TIMESTAMP) = 8) THEN
    UPDATE FIN_DM_OPT_FOI.DM_FCST_PRICE_GROUP_ORD_SPART_SRAN_T T1 SET EXAMINE_RESULT = 'Y'
     WHERE EXISTS(SELECT 1 FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_EXAMINE_RESULT_T T2
                   WHERE T1.SPART_CODE = T2.SPART_CODE
                     AND T1.LV1_PROD_LIST_CODE = T2.LV1_PROD_LIST_CODE
                     AND T1.LV2_PROD_LIST_CODE = T2.LV2_PROD_LIST_CODE
                     AND T1.LV3_PROD_LIST_CODE = T2.LV3_PROD_LIST_CODE
                     AND T1.LV4_PROD_LIST_CODE = T2.LV4_PROD_LIST_CODE
	                   AND T1.PERIOD_ID BETWEEN T2.BEGIN_DATE  AND T2.END_DATE
                     AND T1.REGION_HRMS_ORG_CODE = T2.REGION_CODE
                     AND T1.REPOFFICE_ORG_CODE = T2.REPOFFICE_CODE
                     AND T1.OVERSEAS_FLAG = T2.OVERSEA_FLAG
                     AND NVL(T1.HW_CONTRACT_NUM,1) = NVL(T2.HW_CONTRACT_NUM,1)
                     AND UPPER(T2.PAGE_FLAG) = 'ABNORMAL'
                     AND T2.VERSION_ID = (SELECT VERSION_ID
                                            FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
                                           WHERE DEL_FLAG = 'N'
                                             AND UPPER(DATA_TYPE) = 'DATA_REVIEW'
                                           ORDER BY LAST_UPDATE_DATE DESC
                                           LIMIT 1
                                         )
                 )
    ;

  END IF;

  -- 从版本表取最新版本
  IF(F_VERSION_ID IS NULL) THEN
    SELECT VERSION_ID INTO V_VERSION_ID
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'ANNUAL'  -- 用年度版本号
     ORDER BY LAST_UPDATE_DATE DESC
     LIMIT 1
    ;

  ELSE
    V_VERSION_ID := F_VERSION_ID;
  END IF;
  
  RAISE NOTICE'V_VERSION_ID：%',V_VERSION_ID;

  -- 创建临时表
  DROP TABLE IF EXISTS ALL_ACTUAL_ITEM_TEMP1;
  CREATE TEMPORARY TABLE ALL_ACTUAL_ITEM_TEMP1(
			   PERIOD_YEAR BIGINT
			 , PERIOD_ID   BIGINT
       , LV0_PROD_LIST_CODE VARCHAR(50)
       , LV1_PROD_LIST_CODE VARCHAR(50)
       , LV2_PROD_LIST_CODE VARCHAR(50)
       , LV3_PROD_LIST_CODE VARCHAR(50)
       , LV4_PROD_LIST_CODE VARCHAR(50)
       , LV0_PROD_LIST_CN_NAME VARCHAR(200)
       , LV1_PROD_LIST_CN_NAME VARCHAR(200)
       , LV2_PROD_LIST_CN_NAME VARCHAR(200)
       , LV3_PROD_LIST_CN_NAME VARCHAR(200)
       , LV4_PROD_LIST_CN_NAME VARCHAR(200)
       , OVERSEAS_FLAG VARCHAR(20)
       , REGION_HRMS_ORG_CODE VARCHAR(50)
       , REGION_HRMS_ORG_CN_NAME VARCHAR(200)
       , REPOFFICE_ORG_CODE VARCHAR(50)
       , REPOFFICE_ORG_CN_NAME VARCHAR(200)
       , SIGN_TOP_CUST_CATEGORY_CODE VARCHAR(50)
       , SIGN_TOP_CUST_CATEGORY_CN_NAME VARCHAR(200)
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME VARCHAR(100)
       , SPART_CODE VARCHAR(40)
       , SPART_DESC VARCHAR(2000)
       , QTY NUMERIC
       , USD_PNP_AMT NUMERIC
       , RMB_PNP_AMT NUMERIC
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,SPART_CODE)
  ;

	RAISE NOTICE'11111111111111';

  -- 创建临时表
  DROP TABLE IF EXISTS PERIOD_DIM_TEMP;
  CREATE TEMPORARY TABLE PERIOD_DIM_TEMP(
         PERIOD_ID BIGINT
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY REPLICATION
  ;

  RAISE NOTICE'222222222222';
  
  V_PERIOD_YEAR := V_YEAR - 3; -- 取4年的数据（包括当年）
  
  -- 从来源表取数入到临时表
  V_SQL := '
  INSERT INTO ALL_ACTUAL_ITEM_TEMP1(
			   PERIOD_YEAR
			 , PERIOD_ID
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEAS_FLAG
       , REGION_HRMS_ORG_CODE
       , REGION_HRMS_ORG_CN_NAME
       , REPOFFICE_ORG_CODE
       , REPOFFICE_ORG_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_DESC
       , QTY
       , USD_PNP_AMT
       , RMB_PNP_AMT
  )
  WITH ALL_ACTUAL_ITEM_TEMP2 AS(
  SELECT PERIOD_YEAR
       , PERIOD_ID
       , lv0_prod_list_code
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEAS_FLAG
       , REGION_HRMS_ORG_CODE
       , REGION_HRMS_ORG_CN_NAME
       , REPOFFICE_ORG_CODE
       , REPOFFICE_ORG_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_DESC
       , SUM(NVL(QTY,0)) AS QTY
       , SUM(NVL(USD_PNP_AMT,0)) AS USD_PNP_AMT
       , SUM(NVL(RMB_PNP_AMT,0)) AS RMB_PNP_AMT
    FROM '||V_FROM_TABLE||'
   WHERE PERIOD_YEAR >= '||V_PERIOD_YEAR||'
     AND PERIOD_ID < CAST(TO_CHAR(CURRENT_TIMESTAMP,''YYYYMM'') AS BIGINT)
     AND VERSION_ID = '||V_VERSION_ID ||'
     AND EXAMINE_RESULT = ''N''  -- 审视结果（Y、业务补录异常数据  N、非异常数据）
     AND SPECIAL_TAG = ''N''  -- 特殊编码标识(Y、特殊编码(CA999999以及00000064，00000065)  N、非特殊编码)
     AND DEL_FLAG = ''N''
   GROUP BY PERIOD_YEAR
       , PERIOD_ID
       , lv0_prod_list_code
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEAS_FLAG
       , REGION_HRMS_ORG_CODE
       , REGION_HRMS_ORG_CN_NAME
       , REPOFFICE_ORG_CODE
       , REPOFFICE_ORG_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_DESC
  ),
  ALL_ACTUAL_ITEM_TEMP3 AS(
  SELECT DISTINCT PERIOD_ID, SPART_CODE, SPART_DESC FROM ALL_ACTUAL_ITEM_TEMP2
  ),
  ALL_ACTUAL_ITEM_TEMP4 AS(
  SELECT SPART_CODE, SPART_DESC
    FROM (SELECT SPART_CODE, SPART_DESC
               , ROW_NUMBER() OVER(PARTITION BY SPART_CODE ORDER BY SPART_DESC DESC, PERIOD_ID DESC) AS RN -- 单个spart对应多个描述，取最近会计期的中文
            FROM ALL_ACTUAL_ITEM_TEMP3
         )
  WHERE RN = 1
  )
  SELECT T1.PERIOD_YEAR
       , T1.PERIOD_ID
       , T1.lv0_prod_list_code
       , T1.LV1_PROD_LIST_CODE
       , T1.LV2_PROD_LIST_CODE
       , T1.LV3_PROD_LIST_CODE
       , T1.LV4_PROD_LIST_CODE
       , T1.LV0_PROD_LIST_CN_NAME
       , T1.LV1_PROD_LIST_CN_NAME
       , T1.LV2_PROD_LIST_CN_NAME
       , T1.LV3_PROD_LIST_CN_NAME
       , (CASE WHEN T1.LV3_PROD_LIST_CODE = T1.LV4_PROD_LIST_CODE THEN T1.LV4_PROD_LIST_CN_NAME||''(公共)'' ELSE T1.LV4_PROD_LIST_CN_NAME END) AS LV4_PROD_LIST_CN_NAME
       , T1.OVERSEAS_FLAG
       , T1.REGION_HRMS_ORG_CODE
       , T1.REGION_HRMS_ORG_CN_NAME
       , T1.REPOFFICE_ORG_CODE
       , T1.REPOFFICE_ORG_CN_NAME
       , T1.SIGN_TOP_CUST_CATEGORY_CODE
       , T1.SIGN_TOP_CUST_CATEGORY_CN_NAME
       , T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , T1.SPART_CODE
       , T4.SPART_DESC
       , T1.QTY
       , T1.USD_PNP_AMT
       , T1.RMB_PNP_AMT
    FROM ALL_ACTUAL_ITEM_TEMP2 T1
    JOIN ALL_ACTUAL_ITEM_TEMP4 T4
      ON T1.SPART_CODE = T4.SPART_CODE
  ';

  RAISE NOTICE'44444444444';

  DBMS_OUTPUT.PUT_LINE(V_SQL);

  EXECUTE IMMEDIATE V_SQL;

  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
     F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_MUM,
     F_CAL_LOG_DESC => '版本：'||V_VERSION_ID||'，从来源表取数入到临时表',
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS'
  );
  
  
  -- 创建合同层临时表,用于接受全会计期结果
  DROP TABLE IF EXISTS ACTUAL_APD_TEMP;
  CREATE TEMPORARY TABLE ACTUAL_APD_TEMP(
			   PERIOD_YEAR NUMERIC
			 , PERIOD_ID NUMERIC
       , LV0_PROD_LIST_CODE VARCHAR(50)
       , LV1_PROD_LIST_CODE VARCHAR(50)
       , LV2_PROD_LIST_CODE VARCHAR(50)
       , LV3_PROD_LIST_CODE VARCHAR(50)
       , LV4_PROD_LIST_CODE VARCHAR(50)
       , LV0_PROD_LIST_CN_NAME VARCHAR(200)
       , LV1_PROD_LIST_CN_NAME VARCHAR(200)
       , LV2_PROD_LIST_CN_NAME VARCHAR(200)
       , LV3_PROD_LIST_CN_NAME VARCHAR(200)
       , LV4_PROD_LIST_CN_NAME VARCHAR(200)
       , OVERSEAS_FLAG VARCHAR(20)
       , REGION_HRMS_ORG_CODE VARCHAR(50)
       , REGION_HRMS_ORG_CN_NAME VARCHAR(200)
       , REPOFFICE_ORG_CODE VARCHAR(50)
       , REPOFFICE_ORG_CN_NAME VARCHAR(200)
       , SIGN_TOP_CUST_CATEGORY_CODE VARCHAR(50)
       , SIGN_TOP_CUST_CATEGORY_CN_NAME VARCHAR(200)
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME VARCHAR(100)
       , SPART_CODE VARCHAR(40)
       , SPART_DESC VARCHAR(2000)
       , QTY NUMERIC
       , USD_PNP_AMT NUMERIC
       , RMB_PNP_AMT NUMERIC
       , APPEND_FLAG VARCHAR(5)
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,SPART_CODE)
  ;

	RAISE NOTICE'88888888888';

  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
     F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_MUM,
     F_CAL_LOG_DESC => '合同层临时表创建完成',
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS'
  );
  
  
  -- 年份
  IF(F_PERIOD_YEAR IS NULL and  F_YEAR_DIFF is null) THEN
    -- 生成连续月份, 三年前第1月至当前系统月(不含)
    V_SQL := '
    INSERT INTO PERIOD_DIM_TEMP(PERIOD_ID)
    SELECT CAST(TO_CHAR(ADD_MONTHS('''||V_BEGIN_DATE||''',NUM.VAL - 1),''YYYYMM'') AS BIGINT) AS PERIOD_ID
      FROM GENERATE_SERIES(1,TO_NUMBER(TIMESTAMPDIFF(MONTH,'''||V_BEGIN_DATE||''',CURRENT_TIMESTAMP)),1) NUM(VAL)
    ';

    RAISE NOTICE'33333333333333';
    
    DBMS_OUTPUT.PUT_LINE(V_SQL);

    EXECUTE IMMEDIATE V_SQL;

    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
       F_SP_NAME => V_SP_NAME,
       F_STEP_NUM =>  V_STEP_MUM,
       F_CAL_LOG_DESC => '生成连续月份：'||V_BEGIN_DATE||','||CURRENT_TIMESTAMP,
       F_RESULT_STATUS => X_RESULT_STATUS,
       F_ERRBUF => 'SUCCESS'
    );

    -- 1.清空目标表数据:
    EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_TO_TABLE;

    --1.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
     F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_MUM,
     F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据',
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS'
    );

    
    -- 数据入到临时表
    V_SQL := '
    INSERT INTO ACTUAL_APD_TEMP(
           PERIOD_YEAR
         , PERIOD_ID
         , LV0_PROD_LIST_CODE
         , LV1_PROD_LIST_CODE
         , LV2_PROD_LIST_CODE
         , LV3_PROD_LIST_CODE
         , LV4_PROD_LIST_CODE
         , LV0_PROD_LIST_CN_NAME
         , LV1_PROD_LIST_CN_NAME
         , LV2_PROD_LIST_CN_NAME
         , LV3_PROD_LIST_CN_NAME
         , LV4_PROD_LIST_CN_NAME
         , OVERSEAS_FLAG
         , REGION_HRMS_ORG_CODE
         , REGION_HRMS_ORG_CN_NAME
         , REPOFFICE_ORG_CODE
         , REPOFFICE_ORG_CN_NAME
         , SIGN_TOP_CUST_CATEGORY_CODE
         , SIGN_TOP_CUST_CATEGORY_CN_NAME
         , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
         , SPART_CODE
         , SPART_DESC
         , QTY
         , USD_PNP_AMT
         , RMB_PNP_AMT
         , APPEND_FLAG
    )
    -- 实际数历史表中出现的取数范围：两年前第1月至当前系统月(不含)2023202220212020
  	WITH ACTUAL_ITEM_TEMP AS(
    SELECT DISTINCT PERIOD_ID
         , LV0_PROD_LIST_CODE
         , LV1_PROD_LIST_CODE
         , LV2_PROD_LIST_CODE
         , LV3_PROD_LIST_CODE
         , LV4_PROD_LIST_CODE
         , LV0_PROD_LIST_CN_NAME
         , LV1_PROD_LIST_CN_NAME
         , LV2_PROD_LIST_CN_NAME
         , LV3_PROD_LIST_CN_NAME
         , LV4_PROD_LIST_CN_NAME
         , OVERSEAS_FLAG
         , REGION_HRMS_ORG_CODE
         , REGION_HRMS_ORG_CN_NAME
         , REPOFFICE_ORG_CODE
         , REPOFFICE_ORG_CN_NAME
         , SIGN_TOP_CUST_CATEGORY_CODE
         , SIGN_TOP_CUST_CATEGORY_CN_NAME
         , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
         , SPART_CODE
         , SPART_DESC
      FROM ALL_ACTUAL_ITEM_TEMP1 B  -- 不包括全球、全选数据
    ),
    -- 生成连续年月的发散维
    CROSS_JOIN_TEMP AS(
    SELECT DISTINCT CAST(SUBSTR(B.PERIOD_ID,1,4) AS BIGINT) AS PERIOD_YEAR
         , B.PERIOD_ID
         , A.lv0_prod_list_code
         , A.LV1_PROD_LIST_CODE
         , A.LV2_PROD_LIST_CODE
         , A.LV3_PROD_LIST_CODE
         , A.LV4_PROD_LIST_CODE
         , A.LV0_PROD_LIST_CN_NAME
         , A.LV1_PROD_LIST_CN_NAME
         , A.LV2_PROD_LIST_CN_NAME
         , A.LV3_PROD_LIST_CN_NAME
         , A.LV4_PROD_LIST_CN_NAME
         , A.OVERSEAS_FLAG
         , A.REGION_HRMS_ORG_CODE
         , A.REGION_HRMS_ORG_CN_NAME
         , A.REPOFFICE_ORG_CODE
         , A.REPOFFICE_ORG_CN_NAME
         , A.SIGN_TOP_CUST_CATEGORY_CODE
         , A.SIGN_TOP_CUST_CATEGORY_CN_NAME
         , A.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
         , A.SPART_CODE
         , A.SPART_DESC
      FROM PERIOD_DIM_TEMP B, ACTUAL_ITEM_TEMP A
    )
    SELECT A.PERIOD_YEAR
         , A.PERIOD_ID
         , A.lv0_prod_list_code
         , A.LV1_PROD_LIST_CODE
         , A.LV2_PROD_LIST_CODE
         , A.LV3_PROD_LIST_CODE
         , A.LV4_PROD_LIST_CODE
         , A.LV0_PROD_LIST_CN_NAME
         , A.LV1_PROD_LIST_CN_NAME
         , A.LV2_PROD_LIST_CN_NAME
         , A.LV3_PROD_LIST_CN_NAME
         , A.LV4_PROD_LIST_CN_NAME
         , A.OVERSEAS_FLAG
         , A.REGION_HRMS_ORG_CODE
         , A.REGION_HRMS_ORG_CN_NAME
         , A.REPOFFICE_ORG_CODE
         , A.REPOFFICE_ORG_CN_NAME
         , A.SIGN_TOP_CUST_CATEGORY_CODE
         , A.SIGN_TOP_CUST_CATEGORY_CN_NAME
         , A.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
         , A.SPART_CODE
         , A.SPART_DESC
         , B.QTY
         , B.USD_PNP_AMT
         , B.RMB_PNP_AMT
  			 , DECODE(B.RMB_PNP_AMT,NULL,''Y'',''N'') AS APPEND_FLAG --补齐标识：Y为补齐，N为原始
      FROM CROSS_JOIN_TEMP A
      LEFT JOIN ALL_ACTUAL_ITEM_TEMP1 B
        ON A.LV0_PROD_LIST_CODE = B.LV0_PROD_LIST_CODE
       AND A.PERIOD_ID = B.PERIOD_ID
       AND A.LV1_PROD_LIST_CODE = B.LV1_PROD_LIST_CODE
       AND A.LV2_PROD_LIST_CODE = B.LV2_PROD_LIST_CODE
       AND A.LV3_PROD_LIST_CODE = B.LV3_PROD_LIST_CODE
       AND A.LV4_PROD_LIST_CODE = B.LV4_PROD_LIST_CODE
       AND A.OVERSEAS_FLAG = B.OVERSEAS_FLAG
       AND A.REGION_HRMS_ORG_CODE  = B.REGION_HRMS_ORG_CODE
       AND A.REPOFFICE_ORG_CODE  = B.REPOFFICE_ORG_CODE
       AND A.SIGN_TOP_CUST_CATEGORY_CODE  = B.SIGN_TOP_CUST_CATEGORY_CODE
       AND A.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME  = B.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       AND A.SPART_CODE  = B.SPART_CODE
    ';
  
    RAISE NOTICE'99999999999';
  
    DBMS_OUTPUT.PUT_LINE(V_SQL);
  
    EXECUTE IMMEDIATE V_SQL;
  
    -- 写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
       F_SP_NAME => V_SP_NAME,
       F_STEP_NUM => V_STEP_MUM,
       F_CAL_LOG_DESC => '插入数据到合同层临时表',
       F_FORMULA_SQL_TXT => V_SQL,
       F_DML_ROW_COUNT => SQL%ROWCOUNT,
       F_RESULT_STATUS => X_RESULT_STATUS,
       F_ERRBUF => 'SUCCESS'
    );

  -- 根据传入的年份取值
  ELSE
  
    V_PERIOD_YEAR := V_YEAR - F_YEAR_DIFF;
    
    -- 如果不是当前年份，连续月生成全年
    IF(V_PERIOD_YEAR <> YEAR(CURRENT_TIMESTAMP)) THEN
      -- 生成连续月份, 三年前第1月至当前系统月(不含)
      V_SQL := '
      INSERT INTO PERIOD_DIM_TEMP(PERIOD_ID)
      SELECT CAST(TO_CHAR(ADD_MONTHS(TO_DATE('''||V_PERIOD_YEAR||''',''YYYYMMDD''),NUM.VAL - 1),''YYYYMM'') AS BIGINT) AS PERIOD_ID
        FROM GENERATE_SERIES(1,TO_NUMBER(TIMESTAMPDIFF(MONTH,TO_DATE('''||V_PERIOD_YEAR||''',''YYYYMMDD''),TO_DATE('''||V_PERIOD_YEAR+1||''',''YYYYMMDD''))),1) NUM(VAL)
      ';
  
      RAISE NOTICE'V_PERIOD_YEAR:%',V_PERIOD_YEAR;
      
      DBMS_OUTPUT.PUT_LINE(V_SQL);
  
      EXECUTE IMMEDIATE V_SQL;
  
      V_STEP_MUM := V_STEP_MUM + 1;
      PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
         F_SP_NAME => V_SP_NAME,
         F_STEP_NUM =>  V_STEP_MUM,
         F_CAL_LOG_DESC => '生成连续月份：'||V_PERIOD_YEAR||'01,'||V_PERIOD_YEAR||'12',
         F_RESULT_STATUS => X_RESULT_STATUS,
         F_ERRBUF => 'SUCCESS'
      );
    
    -- 如果是当前年份，连续月只生成当前系统月（不含）
    ELSE

      V_SQL := '
      INSERT INTO PERIOD_DIM_TEMP(PERIOD_ID)
      SELECT CAST(TO_CHAR(ADD_MONTHS(TO_DATE('''||V_PERIOD_YEAR||''',''YYYYMMDD''),NUM.VAL - 1),''YYYYMM'') AS BIGINT) AS PERIOD_ID
        FROM GENERATE_SERIES(1,TO_NUMBER(TIMESTAMPDIFF(MONTH,TO_DATE('''||V_PERIOD_YEAR||''',''YYYYMMDD''),CURRENT_TIMESTAMP)),1) NUM(VAL)
      ';
  
      RAISE NOTICE'V_PERIOD_YEAR:%',V_PERIOD_YEAR;
      
      DBMS_OUTPUT.PUT_LINE(V_SQL);
  
      EXECUTE IMMEDIATE V_SQL;
  
      V_STEP_MUM := V_STEP_MUM + 1;
      PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
         F_SP_NAME => V_SP_NAME,
         F_STEP_NUM =>  V_STEP_MUM,
         F_CAL_LOG_DESC => '生成连续月份：'||V_PERIOD_YEAR||'01,'||V_PERIOD_YEAR||'12',
         F_RESULT_STATUS => X_RESULT_STATUS,
         F_ERRBUF => 'SUCCESS'
      );
      
    END IF;
    
    
    -- 按年删数
    EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' AND PERIOD_YEAR = '||V_PERIOD_YEAR;  -- 按年清理目标表新版本的数据

    --1.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
     F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_MUM,
     F_CAL_LOG_DESC => '清理'||V_TO_TABLE||'数据',
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS'
    );
    
    -- 数据入到临时表
    V_SQL := '
    INSERT INTO ACTUAL_APD_TEMP(
         PERIOD_YEAR
       , PERIOD_ID
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEAS_FLAG
       , REGION_HRMS_ORG_CODE
       , REGION_HRMS_ORG_CN_NAME
       , REPOFFICE_ORG_CODE
       , REPOFFICE_ORG_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_DESC
       , QTY
       , USD_PNP_AMT
       , RMB_PNP_AMT
       , APPEND_FLAG
  )
  -- 实际数历史表中出现的取数范围：两年前第1月至当前系统月(不含)2023202220212020
	WITH ALL_ACTUAL_ITEM_TEMP5 AS(
	SELECT PERIOD_YEAR
  			 , PERIOD_ID
         , LV0_PROD_LIST_CODE
         , LV1_PROD_LIST_CODE
         , LV2_PROD_LIST_CODE
         , LV3_PROD_LIST_CODE
         , LV4_PROD_LIST_CODE
         , LV0_PROD_LIST_CN_NAME
         , LV1_PROD_LIST_CN_NAME
         , LV2_PROD_LIST_CN_NAME
         , LV3_PROD_LIST_CN_NAME
         , LV4_PROD_LIST_CN_NAME
         , OVERSEAS_FLAG
         , REGION_HRMS_ORG_CODE
         , REGION_HRMS_ORG_CN_NAME
         , REPOFFICE_ORG_CODE
         , REPOFFICE_ORG_CN_NAME
         , SIGN_TOP_CUST_CATEGORY_CODE
         , SIGN_TOP_CUST_CATEGORY_CN_NAME
         , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
         , SPART_CODE
         , SPART_DESC
         , QTY
         , USD_PNP_AMT
         , RMB_PNP_AMT
	  FROM ALL_ACTUAL_ITEM_TEMP1
	 WHERE PERIOD_YEAR = '||V_PERIOD_YEAR||'
	),
	ACTUAL_ITEM_TEMP AS(
  SELECT DISTINCT PERIOD_ID
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEAS_FLAG
       , REGION_HRMS_ORG_CODE
       , REGION_HRMS_ORG_CN_NAME
       , REPOFFICE_ORG_CODE
       , REPOFFICE_ORG_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_DESC
    FROM ALL_ACTUAL_ITEM_TEMP1 B  -- 不包括全球、全选数据
  ),
  -- 生成连续年月的发散维
  CROSS_JOIN_TEMP AS(
  SELECT DISTINCT CAST(SUBSTR(B.PERIOD_ID,1,4) AS BIGINT) AS PERIOD_YEAR
       , B.PERIOD_ID
       , A.lv0_prod_list_code
       , A.LV1_PROD_LIST_CODE
       , A.LV2_PROD_LIST_CODE
       , A.LV3_PROD_LIST_CODE
       , A.LV4_PROD_LIST_CODE
       , A.LV0_PROD_LIST_CN_NAME
       , A.LV1_PROD_LIST_CN_NAME
       , A.LV2_PROD_LIST_CN_NAME
       , A.LV3_PROD_LIST_CN_NAME
       , A.LV4_PROD_LIST_CN_NAME
       , A.OVERSEAS_FLAG
       , A.REGION_HRMS_ORG_CODE
       , A.REGION_HRMS_ORG_CN_NAME
       , A.REPOFFICE_ORG_CODE
       , A.REPOFFICE_ORG_CN_NAME
       , A.SIGN_TOP_CUST_CATEGORY_CODE
       , A.SIGN_TOP_CUST_CATEGORY_CN_NAME
       , A.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , A.SPART_CODE
       , A.SPART_DESC
    FROM PERIOD_DIM_TEMP B, ACTUAL_ITEM_TEMP A
  )
  SELECT A.PERIOD_YEAR
       , A.PERIOD_ID
       , A.lv0_prod_list_code
       , A.LV1_PROD_LIST_CODE
       , A.LV2_PROD_LIST_CODE
       , A.LV3_PROD_LIST_CODE
       , A.LV4_PROD_LIST_CODE
       , A.LV0_PROD_LIST_CN_NAME
       , A.LV1_PROD_LIST_CN_NAME
       , A.LV2_PROD_LIST_CN_NAME
       , A.LV3_PROD_LIST_CN_NAME
       , A.LV4_PROD_LIST_CN_NAME
       , A.OVERSEAS_FLAG
       , A.REGION_HRMS_ORG_CODE
       , A.REGION_HRMS_ORG_CN_NAME
       , A.REPOFFICE_ORG_CODE
       , A.REPOFFICE_ORG_CN_NAME
       , A.SIGN_TOP_CUST_CATEGORY_CODE
       , A.SIGN_TOP_CUST_CATEGORY_CN_NAME
       , A.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , A.SPART_CODE
       , A.SPART_DESC
       , B.QTY
       , B.USD_PNP_AMT
       , B.RMB_PNP_AMT
			 , DECODE(B.RMB_PNP_AMT,NULL,''Y'',''N'') AS APPEND_FLAG --补齐标识：Y为补齐，N为原始
    FROM CROSS_JOIN_TEMP A
    LEFT JOIN ALL_ACTUAL_ITEM_TEMP5 B
      ON A.LV0_PROD_LIST_CODE = B.LV0_PROD_LIST_CODE
     AND A.PERIOD_ID = B.PERIOD_ID
     AND A.LV1_PROD_LIST_CODE = B.LV1_PROD_LIST_CODE
     AND A.LV2_PROD_LIST_CODE = B.LV2_PROD_LIST_CODE
     AND A.LV3_PROD_LIST_CODE = B.LV3_PROD_LIST_CODE
     AND A.LV4_PROD_LIST_CODE = B.LV4_PROD_LIST_CODE
     AND A.OVERSEAS_FLAG = B.OVERSEAS_FLAG
     AND A.REGION_HRMS_ORG_CODE  = B.REGION_HRMS_ORG_CODE
     AND A.REPOFFICE_ORG_CODE  = B.REPOFFICE_ORG_CODE
     AND A.SIGN_TOP_CUST_CATEGORY_CODE  = B.SIGN_TOP_CUST_CATEGORY_CODE
     AND A.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME  = B.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
     AND A.SPART_CODE  = B.SPART_CODE
  ';

  RAISE NOTICE'99999999999';

  DBMS_OUTPUT.PUT_LINE(V_SQL);

  EXECUTE IMMEDIATE V_SQL;

  -- 写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
     F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_MUM,
     F_CAL_LOG_DESC => '插入数据到合同层临时表',
     F_FORMULA_SQL_TXT => V_SQL,
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS'
    );
    
  END IF;


  -- 创建SPART层卷积临时表用于接收月累积数据和均价和去除合同号
  DROP TABLE IF EXISTS SUMMARY_APD_TEMP;
  CREATE TEMPORARY TABLE SUMMARY_APD_TEMP(
         PERIOD_YEAR NUMERIC
       , PERIOD_ID   NUMERIC
       , LV0_PROD_LIST_CODE VARCHAR(50)
       , LV1_PROD_LIST_CODE VARCHAR(50)
       , LV2_PROD_LIST_CODE VARCHAR(50)
       , LV3_PROD_LIST_CODE VARCHAR(50)
       , LV4_PROD_LIST_CODE VARCHAR(50)
       , LV0_PROD_LIST_CN_NAME VARCHAR(200)
       , LV1_PROD_LIST_CN_NAME VARCHAR(200)
       , LV2_PROD_LIST_CN_NAME VARCHAR(200)
       , LV3_PROD_LIST_CN_NAME VARCHAR(200)
       , LV4_PROD_LIST_CN_NAME VARCHAR(200)
       , OVERSEAS_FLAG   VARCHAR(20)
       , REGION_HRMS_ORG_CODE    VARCHAR(50)
       , REGION_HRMS_ORG_CN_NAME VARCHAR(200)
       , REPOFFICE_ORG_CODE      VARCHAR(50)
       , REPOFFICE_ORG_CN_NAME   VARCHAR(200)
       , SIGN_TOP_CUST_CATEGORY_CODE      VARCHAR(50)
       , SIGN_TOP_CUST_CATEGORY_CN_NAME   VARCHAR(200)
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME VARCHAR(100)
       , SPART_CODE VARCHAR(40)
       , SPART_DESC VARCHAR(2000)
       , QTY         NUMERIC
       , USD_PNP_AMT NUMERIC
       , RMB_PNP_AMT NUMERIC
       , APPEND_FLAG VARCHAR(5)
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,SPART_CODE)
  ;

  RAISE NOTICE'1000000000';

   --进行合同层月累积
   V_SQL := '
   INSERT INTO SUMMARY_APD_TEMP(
          PERIOD_YEAR
        , PERIOD_ID
        , LV0_PROD_LIST_CODE
        , LV1_PROD_LIST_CODE
        , LV2_PROD_LIST_CODE
        , LV3_PROD_LIST_CODE
        , LV4_PROD_LIST_CODE
        , LV0_PROD_LIST_CN_NAME
        , LV1_PROD_LIST_CN_NAME
        , LV2_PROD_LIST_CN_NAME
        , LV3_PROD_LIST_CN_NAME
        , LV4_PROD_LIST_CN_NAME
        , OVERSEAS_FLAG
        , REGION_HRMS_ORG_CODE
        , REGION_HRMS_ORG_CN_NAME
        , REPOFFICE_ORG_CODE
        , REPOFFICE_ORG_CN_NAME
        , SIGN_TOP_CUST_CATEGORY_CODE
        , SIGN_TOP_CUST_CATEGORY_CN_NAME
        , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
        , SPART_CODE
        , SPART_DESC
        , QTY
        , USD_PNP_AMT
        , RMB_PNP_AMT
        , APPEND_FLAG
  )
  WITH FILL_UP AS(
  SELECT PERIOD_YEAR
       , PERIOD_ID
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEAS_FLAG
       , REGION_HRMS_ORG_CODE
       , REGION_HRMS_ORG_CN_NAME
       , REPOFFICE_ORG_CODE
       , REPOFFICE_ORG_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_DESC
       , (CASE WHEN APPEND_FLAG = ''Y'' THEN NULL ELSE QTY END) AS QTY  -- 补齐的置空值
       , (CASE WHEN APPEND_FLAG = ''Y'' THEN NULL ELSE USD_PNP_AMT END) AS USD_PNP_AMT -- 补齐的置空值
       , (CASE WHEN APPEND_FLAG = ''Y'' THEN NULL ELSE RMB_PNP_AMT END) AS RMB_PNP_AMT -- 补齐的置空值
       , APPEND_FLAG -- 补齐标识：Y为补齐，N为原始
    FROM ACTUAL_APD_TEMP
  ),
	ADD_UP_TEMP AS(
	SELECT PERIOD_YEAR
       , PERIOD_ID
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEAS_FLAG
       , REGION_HRMS_ORG_CODE
       , REGION_HRMS_ORG_CN_NAME
       , REPOFFICE_ORG_CODE
       , REPOFFICE_ORG_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_DESC
			 , SUM(QTY) OVER(PARTITION BY PERIOD_YEAR, LV0_PROD_LIST_CODE, LV1_PROD_LIST_CODE, LV2_PROD_LIST_CODE, LV3_PROD_LIST_CODE, LV4_PROD_LIST_CODE, OVERSEAS_FLAG,REGION_HRMS_ORG_CODE,
						REPOFFICE_ORG_CODE,SIGN_TOP_CUST_CATEGORY_CODE,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,SPART_CODE ORDER BY PERIOD_ID)  AS QTY
			 , SUM(USD_PNP_AMT) OVER(PARTITION BY PERIOD_YEAR,LV0_PROD_LIST_CODE ,LV1_PROD_LIST_CODE ,LV2_PROD_LIST_CODE ,LV3_PROD_LIST_CODE ,LV4_PROD_LIST_CODE, OVERSEAS_FLAG,REGION_HRMS_ORG_CODE,
						REPOFFICE_ORG_CODE,SIGN_TOP_CUST_CATEGORY_CODE,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,SPART_CODE ORDER BY PERIOD_ID)  AS USD_PNP_AMT
			 , SUM(RMB_PNP_AMT) OVER(PARTITION BY PERIOD_YEAR,LV0_PROD_LIST_CODE ,LV1_PROD_LIST_CODE ,LV2_PROD_LIST_CODE ,LV3_PROD_LIST_CODE ,LV4_PROD_LIST_CODE, OVERSEAS_FLAG,REGION_HRMS_ORG_CODE,
						REPOFFICE_ORG_CODE,SIGN_TOP_CUST_CATEGORY_CODE,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,SPART_CODE ORDER BY PERIOD_ID)  AS RMB_PNP_AMT
			 , APPEND_FLAG --补齐标识：Y为补齐，N为原始
    FROM FILL_UP
  )
  SELECT PERIOD_YEAR
       , PERIOD_ID
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEAS_FLAG
       , REGION_HRMS_ORG_CODE
       , REGION_HRMS_ORG_CN_NAME
       , REPOFFICE_ORG_CODE
       , REPOFFICE_ORG_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_DESC
       , SUM(QTY) AS QTY
       , SUM(USD_PNP_AMT) AS USD_PNP_AMT
       , SUM(RMB_PNP_AMT) AS RMB_PNP_AMT
       , APPEND_FLAG
    FROM ADD_UP_TEMP
   GROUP BY PERIOD_YEAR
       , PERIOD_ID
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEAS_FLAG
       , REGION_HRMS_ORG_CODE
       , REGION_HRMS_ORG_CN_NAME
       , REPOFFICE_ORG_CODE
       , REPOFFICE_ORG_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_DESC
       , APPEND_FLAG
   ';

  RAISE NOTICE'11000000000';

  RAISE NOTICE '月卷积完成';
  RAISE NOTICE '%',V_SQL;
  EXECUTE IMMEDIATE V_SQL;

  -- 写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
     F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_MUM,
     F_CAL_LOG_DESC => '月累积卷积完成',
     F_FORMULA_SQL_TXT => V_SQL,
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS'
  );

  -- 2.数据入到目标表
  V_SQL := '
  INSERT INTO '||V_TO_TABLE||'(
         VERSION_ID
       , PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
       , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , ENABLE_FLAG
       , APPEND_FLAG
       , PNP_QTY
       , USD_PNP_AMT
       , RMB_PNP_AMT
       , CREATED_BY
       , CREATION_DATE
       , LAST_UPDATED_BY
       , LAST_UPDATE_DATE
       , DEL_FLAG
  )
  SELECT '||V_VERSION_ID||' AS VERSION_ID
       , PERIOD_YEAR
       , PERIOD_ID
       , LV0_PROD_LIST_CODE AS BG_CODE
       , LV0_PROD_LIST_CN_NAME AS BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEAS_FLAG
       , REGION_HRMS_ORG_CODE
       , REGION_HRMS_ORG_CN_NAME
       , REPOFFICE_ORG_CODE
       , REPOFFICE_ORG_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_DESC
       , (CASE WHEN (QTY = 0 OR QTY IS NULL OR QTY < 0 OR USD_PNP_AMT IS NULL OR USD_PNP_AMT < 0) THEN ''N'' ELSE ''Y'' END) AS ENABLE_FLAG
       , APPEND_FLAG
       , QTY
       , USD_PNP_AMT
       , RMB_PNP_AMT
       , -1 AS CREATED_BY
       , CURRENT_TIMESTAMP AS CREATION_DATE
       , -1 AS LAST_UPDATED_BY
       , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
       , ''N'' AS DEL_FLAG
    FROM SUMMARY_APD_TEMP
  ';
  
  RAISE NOTICE'14000000000';
    
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;


  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '数据入到目标表：'||V_TO_TABLE,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;

  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := 'FAILED';

  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败',
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

