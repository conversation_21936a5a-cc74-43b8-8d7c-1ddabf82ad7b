-- Name: f_dm_foi_cust_wei_index; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_cust_wei_index(f_caliber_flag character varying, f_item_version bigint DEFAULT NULL::bigint, f_customization_id bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间：2023-01-09
创建人  ：黄心蕊 hwx1187045
背景描述：自定义品类组合数据初始化
参数描述：参数一(f_item_version)：通用版本号，一般多为规格品清单最新版本号，或取TOP品类清单版本号
					参数二(f_customization_id)：自定义品类组合ID
					参数三(x_success_flag)：运行状态返回值-成功或者失败
事例    ：select FIN_DM_OPT_FOI.f_dm_foi_cust_wei_index('E',12); --一个版本的数据
		select FIN_DM_OPT_FOI.f_dm_foi_cust_wei_index('E',12,53); --修改入参f_customization_id的权重及指数
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME        		VARCHAR(50):= 'FIN_DM_OPT_FOI.F_DM_FOI_CUST_WEI_INDEX';
  V_VERSION        		BIGINT;
  V_PERIOD				VARCHAR(20) := YEAR(CURRENT_DATE)-3||'-'||YEAR(CURRENT_DATE);
  V_BASE_PERIOD_ID 		INT DEFAULT (SELECT TO_NUMBER(SUBSTR(CURRENT_DATE,1,4)-3 || '01'));
  V_DML_ROW_COUNT  		INT DEFAULT 0 ;
  V_EXCEPTION_FLAG 		VARCHAR(20):='0';--异常定点
  V_UPDATE_FLAG			VARCHAR(10); --增量更新标识
  V_INSERT_FLAG			VARCHAR(10); --全量刷新标识
  V_DEL_WEI_SQL			TEXT:= NULL; --权重删数SQL
  V_INS_WEI_SQL			TEXT:= NULL; --权重插数SQL
  V_DEL_IND_SQL			TEXT:= NULL; --指数删数SQL
  V_INS_IND_SQL			TEXT:= NULL; --指数插数SQL
  V_CUSTOMIZATION_ID	INT := F_CUSTOMIZATION_ID;
  V_PART1_PUBLIC		TEXT:= NULL; 
  V_EXECUTE_SQL			TEXT:= NULL; 
  V_RECEIVE_AMT         VARCHAR(20);
  V_YEAR                VARCHAR(20);
  
  V_FROM_TABLE1 VARCHAR(100); -- 来源表1
  V_FROM_TABLE2 VARCHAR(100); -- 来源表2
  V_FROM_TABLE3 VARCHAR(100); -- 来源表3
  V_FROM_TABLE4 VARCHAR(100); -- 来源表4
  V_TO_TABLE1   VARCHAR(100); -- 目标表
  V_TO_TABLE2   VARCHAR(100); -- 目标表
  --202407版本 新增华东采购与IAS
  V_CALIBER_FLAG    TEXT;
  V_IN_CALIBER      TEXT;
  V_IAS_ECPQC_SQL   TEXT;
  V_VERSION_TABLE 	VARCHAR(100);
  V_JOIN_CALIBER	TEXT;
  V_JOIN_CALIBER2	TEXT;
	  
BEGIN
--0.日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => -1,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');	 
X_SUCCESS_FLAG = '1';

--通过F_DIMENSION_TYPE传参,确认来源表和目标表
  IF F_CALIBER_FLAG = 'I' THEN
    -- ICT采购
    V_TO_TABLE1   := 'FIN_DM_OPT_FOI.DM_FOI_CATE_GROUP_WEIGHT_T'; --目标表1 ICT采购_品类组合页权重表
    V_TO_TABLE2   := 'FIN_DM_OPT_FOI.DM_FOI_PRICE_INDEX_T'; --目标表2 价格指数总览表
    V_FROM_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOI_LEV_REC_AMT_T'; --来源表1  到货金额表(热力图结果表)
    V_FROM_TABLE2 := 'FIN_DM_OPT_FOI.DM_DIM_FOI_CUSTOM_CATG_COMB_T'; --来源表2 自定义品类组合维表
  ELSIF F_CALIBER_FLAG = 'E' THEN
    -- 数字能源
    V_TO_TABLE1   := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_CATE_GROUP_WEIGHT_T'; --目标表1 数字能源_品类组合页权重表
    V_TO_TABLE2   := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_PRICE_INDEX_T'; --目标表2 数字能源_价格指数表
    V_FROM_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_ACTUAL_COST_T'; --来源表1 数字能源_到货金额明细表(热力图结果表)
    V_FROM_TABLE2 := 'FIN_DM_OPT_FOI.DM_DIM_FOI_ENERGY_CUSTOM_CATG_COMB_T'; --来源表2 数字能源自定义品类组合维表
  ELSE
    --202407版本 新增华东采购与IAS
    V_TO_TABLE1   := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_CATE_GROUP_WEIGHT_T'; --目标表1 数字能源_品类组合页权重表
    V_TO_TABLE2   := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_MONTH_PRICE_INDEX_T'; --目标表2 数字能源_价格指数表
    V_FROM_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_ACTUAL_COST_T'; --来源表1 数字能源_到货金额明细表(热力图结果表)
    V_FROM_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_CUSTOM_CATG_COMB_T'; --来源表2 数字能源自定义品类组合维表
    V_CALIBER_FLAG  := 'CALIBER_FLAG,';
    V_IN_CALIBER    := ' '''||F_CALIBER_FLAG||''' AS CALIBER_FLAG,';
    V_IAS_ECPQC_SQL := ' AND T1.CALIBER_FLAG = '''|| F_CALIBER_FLAG||''' ';
	V_JOIN_CALIBER	:=' AND T1.CALIBER_FLAG = T2.CALIBER_FLAG ';

    IF F_CALIBER_FLAG = 'IAS' THEN
      -- IAS
      V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_PLAN_VERSION_T';
    ELSIF F_CALIBER_FLAG = 'EAST_CHINA_PQC' THEN
      -- 华东采购
      V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ECPQC_PLAN_VERSION_T';
    END IF;
  END IF;
  


  -- 无论是季调还是月调，都通过sql从变量参数表（dm_foi_plan_var_para_t）表中取出对应的规格品版本号
  IF F_CUSTOMIZATION_ID IS NULL AND F_ITEM_VERSION IS NULL THEN
    --通过F_DIMENSION_TYPE传参,确认是ICT采购还是数字能源
    IF F_CALIBER_FLAG = 'I' THEN
      -- ICT采购
      -- 将查询到的数据放到变量中的公共sql
      V_PART1_PUBLIC := '
                           SELECT VALUE 
                               FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T
                               WHERE ENABLE_FLAG = ''Y''
                               AND UPPER(PARA_NAME) = ''$PARA_NAME$'';
                             ';
      V_EXECUTE_SQL  := REPLACE(V_PART1_PUBLIC,
                                '$PARA_NAME$',
                                'VERSION_ID-ITEM'); -- 规格品版本号
      EXECUTE V_EXECUTE_SQL
        INTO V_VERSION;
      V_INSERT_FLAG := 'Y';
    
    ELSIF F_CALIBER_FLAG = 'E' THEN
      -- 数字能源
      SELECT T.VERSION_ID
        INTO V_VERSION
        FROM FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T T
       WHERE SUBSTR(VERSION, 1, 6) =
             (SELECT MAX(SUBSTR(VERSION, 1, 6))
                FROM FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T
               WHERE UPPER(DATA_TYPE) IN ('CATEGORY', 'ITEM'))
         AND UPPER(DEL_FLAG) = 'N'
         AND STATUS = 1
         AND UPPER(DATA_TYPE) = 'ITEM'
         AND UPPER(version_type) = 'AUTO';
      V_INSERT_FLAG := 'Y';
    
    ELSE
	--202407版本 新增华东采购与IAS
      V_EXECUTE_SQL := '
    SELECT VERSION_ID
      FROM ' || V_VERSION_TABLE || '
     WHERE DEL_FLAG = ''N''
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = ''ITEM''
       AND UPPER(VERSION_TYPE) IN (''AUTO'', ''FINAL'')
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
    ';
      EXECUTE V_EXECUTE_SQL
        INTO V_VERSION;
    END IF;
  
    -- 业务在前台配置页面调整规格品数据时，Java传参，优先取规格品版本号，若无，则取TOP品类版本号作为生成本次数据的版本号
  ELSIF F_ITEM_VERSION IS NOT NULL AND F_CUSTOMIZATION_ID IS NULL THEN
    V_VERSION     := F_ITEM_VERSION;
    V_INSERT_FLAG := 'Y';
    -- 业务在品类组合页面编辑自选组合时，传入自选组合ID，初始化传参数据
  ELSIF F_ITEM_VERSION IS NOT NULL AND F_CUSTOMIZATION_ID IS NOT NULL THEN
    V_VERSION     := F_ITEM_VERSION;
    V_UPDATE_FLAG := 'Y';
  ELSE
    NULL;
  END IF;

--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 0,
  F_CAL_LOG_DESC => '领域：'||F_CALIBER_FLAG||', 版本号为：'||V_VERSION||' ,自定义品类ID: '||F_CUSTOMIZATION_ID,
	F_FORMULA_SQL_TXT => V_EXECUTE_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

  --取不同的字段名：到货金额表中字段在两张表中的字段名不一致，所以需要再次做一下处理
	IF  F_CALIBER_FLAG = 'I' THEN -- ICT采购
		V_RECEIVE_AMT:='RECEIVE_AMT_CNY';
		V_YEAR:='YEAR';
	ELSE	  --数字能源 --202407版本 新增华东采购与IAS
		V_RECEIVE_AMT:='RECEIVE_AMT_CNY';
		V_YEAR:='PERIOD_YEAR';
	END IF;
	 
V_DEL_WEI_SQL:='DELETE FROM '||V_TO_TABLE1||' T1  WHERE VERSION_ID = '||V_VERSION||' '||V_IAS_ECPQC_SQL||' AND UPPER(PARENT_TYPE) = ''CUSTOMIZATION'';';	
																						--202407版本 新增华东采购与IAS
									
V_INS_WEI_SQL:='INSERT INTO '||V_TO_TABLE1||'
  (PERIOD_YEAR,
   VERSION_ID,/*版本号*/
   GROUP_CODE,/*品类编码*/
   GROUP_CN_NAME,/*品类名称*/
   PARENT_CODE,/*自定义品类ID*/
   PARENT_CN_NAME,
   PARENT_TYPE,/*父级类型(TOP_TYPE：TOP分类；CATEGORY_TYPE：品类分类；CUSTOMIZATION：品类组合)*/
   WEIGHT_RATE,/*权重*/
   '||V_CALIBER_FLAG||'		--202407版本 新增华东采购与IAS
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG
	 )
SELECT '''||V_PERIOD||''' AS PERIOD_YEAR,  
       '||V_VERSION||' AS VERSION_ID,
       T1.GROUP_CODE,
       T1.GROUP_CN_NAME,
       T2.CUSTOMIZATION_ID AS PARENT_CODE,
       T2.CUSTOMIZATION_NAME AS PARENT_CN_NAME,
	   ''CUSTOMIZATION'' AS PARENT_TYPE,
       T1.CATE_AMT /
       NULLIF(SUM(T1.CATE_AMT) OVER(PARTITION BY T2.CUSTOMIZATION_ID),0) AS WEIGHT_RATE,
	   '||V_IN_CALIBER||'		--202407版本 新增华东采购与IAS
       -1 AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       -1 AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       ''N'' AS DEL_FLAG
  FROM (SELECT GROUP_CODE,
               GROUP_CN_NAME,
               SUM('||V_RECEIVE_AMT||') AS CATE_AMT,
			   '||V_CALIBER_FLAG||'		--202407版本 新增华东采购与IAS
               L2_CEG_CODE,
               L2_CEG_CN_NAME
          FROM '||V_FROM_TABLE1||' T1
         WHERE VERSION_ID = '||V_VERSION||'
           AND UPPER(GROUP_LEVEL) = ''CATEGORY''
           AND TOP_FLAG = ''Y''
		   '||V_IAS_ECPQC_SQL||'		--202407版本 新增华东采购与IAS
         GROUP BY GROUP_CODE, GROUP_CN_NAME,  '||V_CALIBER_FLAG||' L2_CEG_CODE, L2_CEG_CN_NAME) T1
  JOIN '||V_FROM_TABLE2||' T2
    ON T1.GROUP_CODE = T2.CATEGORY_CODE
	'||V_JOIN_CALIBER||'	--202407版本 新增华东采购与IAS
 WHERE T2.ENABLE_FLAG = ''Y''
	 AND T2.DEL_FLAG = ''N''
   AND T2.CATG_ENABLE_FLAG = ''Y''
 GROUP BY T1.GROUP_CODE,
          T1.GROUP_CN_NAME,
		  T1.CATE_AMT,
          T1.L2_CEG_CN_NAME,
          T1.L2_CEG_CODE,
          T2.CUSTOMIZATION_ID,
		  T2.CUSTOMIZATION_NAME;';
		  
		  
		  
V_DEL_IND_SQL:='DELETE FROM '||V_TO_TABLE2||' T1
								 WHERE VERSION_ID ='||V_VERSION||'
									 AND BASE_PERIOD_ID = '''||V_BASE_PERIOD_ID||'''
									 '||V_IAS_ECPQC_SQL||'		--202407版本 新增华东采购与IAS
									 AND GROUP_LEVEL = ''CUSTOMIZATION'';';
V_INS_IND_SQL:='INSERT INTO '||V_TO_TABLE2||'
  (YEAR,
   PERIOD_ID,
   BASE_PERIOD_ID,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   PARENT_CODE,
   PRICE_INDEX,
   '||V_CALIBER_FLAG||'		--202407版本 新增华东采购与IAS
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   VERSION_ID)
WITH CATE_PRICE AS
 (--取出品类在默认基期下的指数
  SELECT T1.YEAR,
         T1.PERIOD_ID,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         NULLIF(T1.PRICE_INDEX, 0) AS PRICE_INDEX,
		 '||V_IN_CALIBER||'		--202407版本 新增华东采购与IAS
		 T2.customization_ID AS PARENT_CODE,
		 T2.customization_name AS PARENT_NAME,
         T1.VERSION_ID
    FROM '||V_TO_TABLE2||' T1
	JOIN '||V_FROM_TABLE2||' T2
		ON T1.GROUP_CODE = T2.CATEGORY_CODE
		'||V_JOIN_CALIBER||'		--202407版本 新增华东采购与IAS
   WHERE T1.VERSION_ID = '||V_VERSION||'
		 AND UPPER(T2.ENABLE_FLAG) = ''Y''
     AND UPPER(T2.CATG_ENABLE_FLAG) = ''Y''
     AND UPPER(T2.DEL_FLAG) = ''N''
	 '||V_IAS_ECPQC_SQL||'	--202407版本 新增华东采购与IAS
     AND UPPER(T1.GROUP_LEVEL) = ''CATEGORY''
		 AND T1.BASE_PERIOD_ID = '''||V_BASE_PERIOD_ID||'''
)
SELECT  T.YEAR,
       T.PERIOD_ID,
       '''||V_BASE_PERIOD_ID||''' AS BASE_PERIODE_ID,
       T.PARENT_CODE AS GROUP_CODE,
       T.PARENT_NAME AS GROUP_CN_NAME,
       ''CUSTOMIZATION'' AS GROUP_LEVEL,
       T.PARENT_CODE AS PARENT_CODE,
       SUM(T.PRICE_INDEX) * 100 AS PRICE_INDEX,
	   '||V_IN_CALIBER||'		--202407版本 新增华东采购与IAS
       -1 AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       -1 AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       ''N'' AS DEL_FLAG,
	     '||V_VERSION||' AS VERSION_ID
  FROM (SELECT T1.YEAR,
               T1.PERIOD_ID,
               '''||V_BASE_PERIOD_ID||''' AS BASE_PERIOD_ID,
               T1.GROUP_CODE,
               T1.GROUP_CN_NAME,
               T2.PARENT_CODE AS PARENT_CODE,
               T1.PRICE_INDEX / T3.PRICE_INDEX * T2.WEIGHT_RATE AS PRICE_INDEX,
							 T1.PARENT_NAME
          FROM '||V_TO_TABLE1||' T2 
          JOIN CATE_PRICE T1
            ON (T1.GROUP_CODE = T2.GROUP_CODE AND T1.VERSION_ID = T2.VERSION_ID AND T1.PARENT_CODE = T2.PARENT_CODE)
			'||V_JOIN_CALIBER||'		--202407版本 新增华东采购与IAS
          LEFT JOIN CATE_PRICE T3
            ON (T1.GROUP_CODE = T3.GROUP_CODE AND T1.VERSION_ID = T3.VERSION_ID AND T1.PARENT_CODE = T3.PARENT_CODE)
         WHERE T2.VERSION_ID = '||V_VERSION||'
           AND T3.PERIOD_ID = '''||V_BASE_PERIOD_ID||'''
		   '||V_IAS_ECPQC_SQL||'	--202407版本 新增华东采购与IAS
		   AND T2.PARENT_TYPE = ''CUSTOMIZATION'') T
 GROUP BY T.YEAR,
          T.PERIOD_ID,
          T.PARENT_CODE,
		  T.PARENT_NAME;';
/*****************************************************全量数据初始化*****************************************************/
IF V_INSERT_FLAG = 'Y' THEN
--季度或月度调度时，初始化一版全量数据

V_EXCEPTION_FLAG := '1';
V_EXECUTE_SQL:= V_DEL_WEI_SQL;
EXECUTE IMMEDIATE V_DEL_WEI_SQL;				
 --1.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '自定义组合权重删除完成',
	F_FORMULA_SQL_TXT => V_EXECUTE_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
V_EXCEPTION_FLAG := '2';
V_EXECUTE_SQL:= V_INS_WEI_SQL;

EXECUTE IMMEDIATE V_INS_WEI_SQL;

 --2.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 2,
  F_CAL_LOG_DESC => '自定义组合权重插数完成',
	F_FORMULA_SQL_TXT => V_EXECUTE_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');


  
V_EXCEPTION_FLAG := '3';
V_EXECUTE_SQL:= V_DEL_IND_SQL;
EXECUTE IMMEDIATE V_DEL_IND_SQL;

 --3.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 3,
  F_CAL_LOG_DESC => '自定义组合默认基期指数删除完成',
	F_FORMULA_SQL_TXT => V_EXECUTE_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
  
  
  
V_EXCEPTION_FLAG := '4';
V_EXECUTE_SQL:= V_INS_IND_SQL;
EXECUTE IMMEDIATE V_INS_IND_SQL;



 --4.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 4,
  F_CAL_LOG_DESC => '自定义组合默认基期指数插数完成',
	F_FORMULA_SQL_TXT => V_EXECUTE_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
END IF ;
/*****************************************************增量数据初始化*****************************************************/
IF V_UPDATE_FLAG = 'Y' THEN
--品类组合页面自定义品类组合增改逻辑
V_EXCEPTION_FLAG := '5';
V_DEL_WEI_SQL := REPLACE(V_DEL_WEI_SQL,'AND UPPER(PARENT_TYPE) = ''CUSTOMIZATION''','AND UPPER(PARENT_TYPE) = ''CUSTOMIZATION'' AND PARENT_CODE = '||V_CUSTOMIZATION_ID||'');
V_EXECUTE_SQL := V_DEL_WEI_SQL;
EXECUTE IMMEDIATE V_DEL_WEI_SQL;
 --5.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 5,
  F_CAL_LOG_DESC => '自定义组合id为#'||V_CUSTOMIZATION_ID||'#权重删数完成',
	F_FORMULA_SQL_TXT => V_EXECUTE_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
V_EXCEPTION_FLAG := '6';
V_INS_WEI_SQL := REPLACE(V_INS_WEI_SQL,'AND T2.CATG_ENABLE_FLAG = ''Y''','AND T2.CATG_ENABLE_FLAG = ''Y'' AND T2.CUSTOMIZATION_ID = '||V_CUSTOMIZATION_ID||'');
V_EXECUTE_SQL := V_INS_WEI_SQL;
EXECUTE IMMEDIATE V_INS_WEI_SQL;
 --6.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 6,
  F_CAL_LOG_DESC => '自定义组合id为#'||V_CUSTOMIZATION_ID||'#权重插数完成',	
	F_FORMULA_SQL_TXT => V_EXECUTE_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
V_EXCEPTION_FLAG := '7';
V_DEL_IND_SQL := REPLACE(V_DEL_IND_SQL,'AND GROUP_LEVEL = ''CUSTOMIZATION''','AND GROUP_LEVEL = ''CUSTOMIZATION'' AND GROUP_CODE = '||V_CUSTOMIZATION_ID||'');
V_EXECUTE_SQL:=V_DEL_IND_SQL;
EXECUTE IMMEDIATE V_DEL_IND_SQL;
 --7.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 7,
  F_CAL_LOG_DESC => '自定义组合id为#'||V_CUSTOMIZATION_ID||'#指数删数完成',
	F_FORMULA_SQL_TXT => V_EXECUTE_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
V_EXCEPTION_FLAG := '8';
V_INS_IND_SQL := REPLACE(V_INS_IND_SQL,'AND T2.PARENT_TYPE = ''CUSTOMIZATION''','AND T2.PARENT_TYPE = ''CUSTOMIZATION'' AND T2.PARENT_CODE = '||V_CUSTOMIZATION_ID||'');
V_EXECUTE_SQL:=V_INS_IND_SQL;
EXECUTE IMMEDIATE V_INS_IND_SQL;
 --8.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 8,
  F_CAL_LOG_DESC => '自定义组合id为#'||V_CUSTOMIZATION_ID||'#指数插数完成',
	F_FORMULA_SQL_TXT => V_EXECUTE_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
END IF ;
 --9.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 9,
  F_CAL_LOG_DESC => V_SP_NAME||'跑数成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');	
RETURN 'SUCCESS';
EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := '2001';  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_EXCEPTION_FLAG,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; $$
/

