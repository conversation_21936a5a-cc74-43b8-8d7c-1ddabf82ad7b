-- Name: f_dm_fcst_price_mtd_avg_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_mtd_avg_t(OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：20241109
创建人  ：qwx1110218
背景描述：来源：月累计均本补齐表：月累计均本补齐_01表 DM_FCST_PRICE_MTD_AVG_01_T 关联年度均本补齐表 DM_FCST_PRICE_ANNL_AVG_T，给Spart打上“有效数据标识（Y、有效  N、无效）”
参数描述：输出参数： x_result_status 运行状态返回值 'SUCCESS'为成功，'FAILED'为失败
事例    ：select fin_dm_opt_foi.f_dm_fcst_price_mtd_avg_t()
变更记录：

*/


declare
	v_sp_name     varchar(200) := 'fin_dm_opt_foi.f_dm_fcst_price_mtd_avg_t';
	v_to_table    varchar(100) := 'fin_dm_opt_foi.dm_fcst_price_mtd_avg_t';  -- 目标表
	v_annual_version_id  bigint;       -- 年度版本号
	v_month_version_id  bigint;        -- 月度版本号
	v_sql         text;
	v_step_num    int;
	v_cn          int;


begin

	x_result_status := 'SUCCESS';        --1表示成功
	v_step_num = 1;

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
      f_sp_name => v_sp_name,    --sp名称
      f_step_num => v_step_num,
      f_cal_log_desc => '函数 '||v_sp_name||' 开始运行',--日志描述
      f_formula_sql_txt  => v_sql,
      f_dml_row_count => sql%rowcount,
      f_result_status => x_result_status,
      f_errbuf => 'SUCCESS'
  );

  raise notice'1111111111';

  -- 获取版本
  select version_id into v_month_version_id
    from fin_dm_opt_foi.dm_fcst_price_version_info_t
   where del_flag = 'N'
     and status = 1
     and upper(data_type) = 'MONTH'  -- 用月度版本号
   order by last_update_date desc
   limit 1
  ;

  select version_id into v_annual_version_id
    from fin_dm_opt_foi.dm_fcst_price_version_info_t
   where del_flag = 'N'
     and status = 1
     and upper(data_type) = 'ANNUAL'  -- 用年度版本号
   order by last_update_date desc
   limit 1
  ;

  raise notice'月度版本：%',v_month_version_id;
  raise notice'年度版本：%',v_annual_version_id;

  perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
      f_sp_name => v_sp_name,    --sp名称
      f_step_num => v_step_num,
      f_cal_log_desc => '月度版本： '||v_month_version_id||'，年度版本：'||v_annual_version_id,--日志描述
      f_formula_sql_txt  => v_sql,
      f_dml_row_count => sql%rowcount,
      f_result_status => x_result_status,
      f_errbuf => 'SUCCESS'
  );


  -- 清理数据
  truncate table fin_dm_opt_foi.dm_fcst_price_mtd_avg_t;

  raise notice'222222222';

  -- 数据入到目标表
  insert into fin_dm_opt_foi.dm_fcst_price_mtd_avg_t(
         version_id                        -- 版本ID
       , period_year                       -- 会计年
       , period_id                         -- 会计月
       , bg_code                           -- BG编码
       , bg_cn_name                        -- BG中文名称
       , lv0_prod_list_code                -- 产品LV0编码
       , lv1_prod_list_code                -- 产品LV1编码
       , lv2_prod_list_code                -- 产品LV2编码
       , lv3_prod_list_code                -- 产品LV3编码
       , lv4_prod_list_code                -- 产品LV4编码
       , lv0_prod_list_cn_name             -- 产品LV0中文名称
       , lv1_prod_list_cn_name             -- 产品LV1中文名称
       , lv2_prod_list_cn_name             -- 产品LV2中文名称
       , lv3_prod_list_cn_name             -- 产品LV3中文名称
       , lv4_prod_list_cn_name             -- 产品LV4中文名称
       , oversea_flag                      -- 国内海外标识（海外：Y ， 国内：N  ，全球：G）
       , region_code                       -- 地区部编码
       , region_cn_name                    -- 地区部名称
       , repoffice_code                    -- 代表处编码
       , repoffice_cn_name                 -- 代表处名称
       , sign_top_cust_category_code       -- 签约客户_大T系统部编码
       , sign_top_cust_category_cn_name    -- 签约客户_大T系统部名称
       , sign_subsidiary_custcatg_cn_name  -- 签约客户_子网系统部名称
       , spart_code                        -- SPART编码
       , spart_cn_name                     -- SPART中文名称
       , view_flag                         -- 视角标识，用于区分不同视角下的数据(地代办 LOCAL_AGENT（国内海外、地区部、代表处）；系统部 SYS_DEPT（大T系统、子网系统）)
       , enable_flag                       -- 有效标识（Y：有效数据、N：无效数据）
       , append_flag                       -- 补齐标识（Y：补齐数据、N：真实数据）
       , append_period                     -- 补齐月份
       , usd_pnp_amt                       -- PNP(CNP)_美元
       , rmb_pnp_amt                       -- PNP(CNP)_人民币
       , usd_pnp_avg                       -- PNP(CNP)均价_美元
       , rmb_pnp_avg                       -- PNP(CNP)均价_人民币
       , created_by                        -- 创建人
       , creation_date                     -- 创建时间
       , last_updated_by                   -- 修改人
       , last_update_date                  -- 修改时间
       , del_flag                          -- 删除标识(未删除：N，已删除：Y)
  )
  with price_mtd_avg_01_tmp as(
  select period_year
       , period_id
       , bg_code
       , bg_cn_name
       , lv0_prod_list_code
       , lv1_prod_list_code
       , lv2_prod_list_code
       , lv3_prod_list_code
       , lv4_prod_list_code
       , lv0_prod_list_cn_name
       , lv1_prod_list_cn_name
       , lv2_prod_list_cn_name
       , lv3_prod_list_cn_name
       , lv4_prod_list_cn_name
       , oversea_flag
       , region_code
       , region_cn_name
       , repoffice_code
       , repoffice_cn_name
       , sign_top_cust_category_code
       , sign_top_cust_category_cn_name
       , sign_subsidiary_custcatg_cn_name
       , spart_code
       , spart_cn_name
       , view_flag
       , append_flag
       , append_period
       , usd_pnp_amt
       , rmb_pnp_amt
       , usd_pnp_avg
       , rmb_pnp_avg
    from fin_dm_opt_foi.dm_fcst_price_mtd_avg_01_t
   where version_id = v_month_version_id
     and del_flag = 'N'
  ),
  price_annl_avg_tmp as(
  select period_year
       , bg_code
       , bg_cn_name
       , lv0_prod_list_code
       , lv1_prod_list_code
       , lv2_prod_list_code
       , lv3_prod_list_code
       , lv4_prod_list_code
       , lv0_prod_list_cn_name
       , lv1_prod_list_cn_name
       , lv2_prod_list_cn_name
       , lv3_prod_list_cn_name
       , lv4_prod_list_cn_name
       , oversea_flag
       , region_code
       , region_cn_name
       , repoffice_code
       , repoffice_cn_name
       , sign_top_cust_category_code
       , sign_top_cust_category_cn_name
       , sign_subsidiary_custcatg_cn_name
       , spart_code
       , spart_cn_name
       , view_flag
       , append_flag
       , enable_flag
       , usd_pnp_amt
       , usd_pnp_avg
    from fin_dm_opt_foi.dm_fcst_price_annl_avg_t t1  -- 年度均本补齐表
   where t1.del_flag = 'N'
     and t1.version_id = v_annual_version_id
  )
  select v_month_version_id as version_id
       , t1.period_year
       , t1.period_id
       , t1.bg_code
       , t1.bg_cn_name
       , t1.lv0_prod_list_code
       , t1.lv1_prod_list_code
       , t1.lv2_prod_list_code
       , t1.lv3_prod_list_code
       , t1.lv4_prod_list_code
       , t1.lv0_prod_list_cn_name
       , t1.lv1_prod_list_cn_name
       , t1.lv2_prod_list_cn_name
       , t1.lv3_prod_list_cn_name
       , t1.lv4_prod_list_cn_name
       , t1.oversea_flag
       , t1.region_code
       , t1.region_cn_name
       , t1.repoffice_code
       , t1.repoffice_cn_name
       , t1.sign_top_cust_category_code
       , t1.sign_top_cust_category_cn_name
       , t1.sign_subsidiary_custcatg_cn_name
       , t1.spart_code
       , t1.spart_cn_name
       , t1.view_flag
       , t2.enable_flag
       , t1.append_flag
       , t1.append_period
       , t1.usd_pnp_amt
       , t1.rmb_pnp_amt
       , nvl(t1.usd_pnp_avg,t2.usd_pnp_avg) as usd_pnp_avg
       , t1.rmb_pnp_avg
       , -1 as created_by
			 , current_timestamp as creation_date
			 , -1 as last_updated_by
			 , current_timestamp as last_update_date
			 , 'N' as del_flag
    from price_mtd_avg_01_tmp t1
    left join price_annl_avg_tmp t2
      on t1.period_year = t2.period_year
     and t1.bg_code = t2.bg_code
     and t1.lv0_prod_list_code = t2.lv0_prod_list_code
     and t1.lv1_prod_list_code = t2.lv1_prod_list_code
     and t1.lv2_prod_list_code = t2.lv2_prod_list_code
     and t1.lv3_prod_list_code = t2.lv3_prod_list_code
     and t1.lv4_prod_list_code = t2.lv4_prod_list_code
     and t1.spart_code = t2.spart_code
     and nvl(t1.oversea_flag,'A') = nvl(t2.oversea_flag,'A')
     and nvl(t1.region_code,'A') = nvl(t2.region_code,'A')
     and nvl(t1.repoffice_code,'A') = nvl(t2.repoffice_code,'A')
     and nvl(t1.sign_top_cust_category_code,'A') = nvl(t2.sign_top_cust_category_code,'A')
     and nvl(t1.sign_subsidiary_custcatg_cn_name,'A') = nvl(t2.sign_subsidiary_custcatg_cn_name,'A')
     and t1.view_flag = t2.view_flag
  ;

  raise notice'333333333';

  v_step_num := v_step_num+1;
  perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
      f_sp_name => v_sp_name,    --sp名称
      f_step_num => v_step_num,
      f_cal_log_desc => '数据入到目标表，数据量：'||sql%rowcount||'，运行结束！',--日志描述
      f_formula_sql_txt  => v_sql,
      f_dml_row_count => sql%rowcount,
      f_result_status => x_result_status,
      f_errbuf => 'SUCCESS'
  );


  --收集统计信息
	v_sql := 'analyse '||v_to_table;
	execute v_sql;


  exception
  	when others then

      perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
         f_sp_name => v_sp_name,    -- sp名称
         f_step_num => v_step_num,
         f_cal_log_desc => v_sp_name||'：运行错误',-- 日志描述
         f_formula_sql_txt  => v_sql,
         f_dml_row_count => sql%rowcount,
         f_result_status => '0',
         f_errbuf => sqlstate  -- 错误编码
      ) ;

      x_result_status := 'FAILED';

end
$$
/

