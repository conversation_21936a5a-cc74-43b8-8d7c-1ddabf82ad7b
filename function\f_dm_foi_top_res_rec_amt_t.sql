-- Name: f_dm_foi_top_res_rec_amt_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_top_res_rec_amt_t(f_cate_version text, f_item_version text, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$ 
	/*
创建时间：2022-11-17
创建人  ：songhui swx1182801
背景描述：规格品到货总金额数据表,然后调用该函数的版本将相对应的数据生成导入到目标表中
参数描述：参数一(f_cate_version)：top品类清单表最新版本号
					参数一(f_item_version)：导入通用版本号（规格品清单版本号）
					参数三(x_success_flag)  ：运行状态返回值-成功或者失败
事例    ：select FIN_DM_OPT_FOI.f_dm_foi_top_res_rec_amt_t(202101)	--一个版本的数据

*/
        

declare
	v_sp_name varchar(50) := 'dm_foi_top_res_rec_amt_t';
	v_dml_row_count  number default 0 ;
	v_version_id numeric := nvl(f_item_version,f_cate_version);
begin
	x_success_flag := '1';
	
	 --写日志,开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => v_sp_name,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => '规格品总金额数据表：开始运行');

	---支持重跑，清除目标表要插入会计期的数据
	delete from FIN_DM_OPT_FOI.dm_foi_top_res_rec_amt_t where version_id=v_version_id ;
	---插入目标表数据（dm_foi_top_res_rec_amt_t） 
	
---------将供应商层实际数据直取到到规格品总金额数据表
insert into FIN_DM_OPT_FOI.dm_foi_top_res_rec_amt_t (
			      id,--（主键）
            year,--会计期(年)
            period_id,--会计期
            group_code,--分层级code编码
            group_cn_name,--分层级中文名称
            group_level,--Group层级（LV4：生产采购、LV3：专家团、LV4：模块、CATEGORY：品类、ITEM：规格品、SUPPLIER：供应商）
            receive_qty,--到货数量
            receive_amt_usd,--到货总金额(USD)
            receive_amt_cny,--到货总金额(CNY)
            --avg_price_cny,--到货均价
			      --avg_price_usd,--到货均价
            item_code,--ITEM编码
            item_name,--ITEM名称
            category_code,--品类编码
            category_name,--品类名称
            l2_ceg_cn_name,--生产采购
            l3_ceg_short_cn_name,--专家团（Group Lv3简称）
            l4_ceg_short_cn_name,--模块（Group Lv4简称）
            l2_ceg_code,--生产编码
            l3_ceg_code,--专家团编码（Group Lv3简称）
            l4_ceg_code,----模块编码（Group Lv4简称）
			      l4_ceg_cn_name,--模块中文名称
            l3_ceg_cn_name,--专家团中文名称
            created_by,--创建人
            creation_date,--创建时间
            last_updated_by,--修改人
            last_update_date,--修改时间
            del_flag,--删除标识(未删除：N，已删除：Y)
            top_flag,--top标识(Y:是规格品、N：非规格品、：非item层级)	
            version_id,--版本
            parent_code,--父层级编码
			      append_flag--均价_是否补录(Y:是补录数据、N：真实数据)


			) 
	select 
           FIN_DM_OPT_FOI.dm_foi_top_res_rec_amt_s.nextval id,
           horr.year,
           horr.period_id,
           horr.supplier_code as group_code,
           horr.supplier_cn_name as group_cn_name,
           'SUPPLIER' as group_level,
           horr.receive_qty,
           horr.receive_amt_usd,
           horr.receive_amt_cny,
           --case when nvl (horr.receive_qty,0) = 0 then 0 
		        --    else ROUND(horr.receive_amt_cny  / horr.receive_qty,6) end as avg_price_cny,--到货均价(CNY)
		       --null as avg_price_usd,			
           horr.item_code,
           horr.item_name ,
           horr.category_code,
           horr.category_name ,
           horr.l2_ceg_cn_name,
           horr.l3_ceg_short_cn_name,
           horr.l4_ceg_short_cn_name,
           horr.l2_ceg_code,
           horr.l3_ceg_code,
           horr.l4_ceg_code,
		       horr.l4_ceg_cn_name,
           horr.l3_ceg_cn_name,
		       - 1 AS created_by,
			     current_timestamp AS creation_date,
		      	- 1 AS last_updated_by,
			     current_timestamp AS last_update_date,
			     'N' AS del_flag,
	         null as top_flag, 
		       v_version_id as version_id,
           horr.item_code as parent_code,
            horr.append_flag    --均价_是否补录(Y:是补录数据、N：真实数据) 
	    from FIN_DM_OPT_FOI.dm_foi_his_ord_rec_report_t	horr 
			left join   FIN_DM_OPT_FOI.dm_foi_top_item_info_t tii  
         on  horr.item_code = tii.item_code
		  where tii.version_id = v_version_id
			;
				
--commit;			
				
---------将ITEM层规格品数据插入到到货金额表		实际数+预测数

insert into FIN_DM_OPT_FOI.dm_foi_top_res_rec_amt_t (
			      id,--（主键）
            year,--会计期(年)
            period_id,--会计期
            group_code,--分层级code编码
            group_cn_name,--分层级中文名称
            group_level,--Group层级（LV4：生产采购、LV3：专家团、LV4：模块、CATEGORY：品类、ITEM：规格品、SUPPLIER：供应商）
            receive_qty,--到货数量
            receive_amt_usd,--到货总金额(USD)
            receive_amt_cny,--到货总金额(CNY)
            --avg_price_cny,--到货均价
			      --avg_price_usd,--到货均价
            item_code,--ITEM编码
            item_name,--ITEM名称
            category_code,--品类编码
            category_name,--品类名称
            l2_ceg_cn_name,--生产采购
            l3_ceg_short_cn_name,--专家团（Group LV3简称）
            l4_ceg_short_cn_name,--模块（Group LV4简称）
            l2_ceg_code,--生产编码
            l3_ceg_code,--专家团编码（Group LV3简称）
            l4_ceg_code,----模块编码（Group LV4简称）
			      l4_ceg_cn_name,--模块中文名称
            l3_ceg_cn_name,--专家团中文名称
            created_by,--创建人
            creation_date,--创建时间
            last_updated_by,--修改人
            last_update_date,--修改时间
            del_flag,--删除标识(未删除：N，已删除：Y)
            top_flag,--top标识(Y:是规格品、N：非规格品、：非item层级)	
            version_id,--版本
            parent_code,--父层级编码
			      append_flag--均价_是否补录(Y:是补录数据、N：真实数据)
						
			) 
				
select 
		        FIN_DM_OPT_FOI.dm_foi_lev_rec_amt_s.nextval id,
            lra.year,
            lra.period_id,
            lra.group_code,
            lra.group_cn_name,
            lra.group_level,--Group层级（LV4：生产采购、LV3：专家团、LV4：模块、CATEGORY：品类、ITEM：规格品、SUPPLIER：供应商）
            lra.receive_qty ,
            lra.receive_amt_usd,
            lra.receive_amt_cny,
           -- lra.avg_price_cny,--到货均价(CNY)
		       --null as avg_price_usd ,	
           lra.item_code,
           lra.item_name,
           lra.category_code,
           lra.category_name,
           lra.l2_ceg_cn_name,
           lra.l3_ceg_short_cn_name,
           lra.l4_ceg_short_cn_name,
           lra.l2_ceg_code,
           lra.l3_ceg_code,
           lra.l4_ceg_code,
		       lra.l4_ceg_cn_name,
           lra.l3_ceg_cn_name,
           lra.created_by,
           lra.creation_date,
           lra.last_updated_by,
           lra.last_update_date,
           lra.del_flag,
           case when   tii.item_code is not NULL    then 'Y'  else  'N' end  as top_flag,
           v_version_id as version_id, 
			     lra.category_code as parent_code,
           lra.append_flag 
from  (
---直取实际数据收敛
		SELECT
			h0rr.YEAR,
			h0rr.period_id,
			h0rr.item_code AS group_code,
			h0rr.item_name AS group_cn_name,
			'ITEM' AS group_level,--Group层级（LV4：生产采购、LV3：专家团、LV4：模块、CATEGORY：品类、ITEM：规格品、SUPPLIER：供应商）
			SUM ( h0rr.receive_qty ) as receive_qty,
			SUM ( h0rr.receive_amt_usd ) as receive_amt_usd,
			SUM ( h0rr.receive_amt_cny ) AS receive_amt_cny,
			--case when sum(nvl(h0rr.receive_qty,0)) = 0 then 0 
		            --else ROUND(sum(h0rr.receive_amt_cny)  / sum(h0rr.receive_qty),6) end as avg_price_cny,--到货均价(CNY)
			NULL AS item_code,
			NULL AS item_name,
			h0rr.category_code,
			h0rr.category_name ,
			h0rr.l2_ceg_cn_name ,
			h0rr.l3_ceg_short_cn_name,
			h0rr.l4_ceg_short_cn_name,
			h0rr.l2_ceg_code,
			h0rr.l3_ceg_code,
			h0rr.l4_ceg_code,
			h0rr.l4_ceg_cn_name,
			h0rr.l3_ceg_cn_name,
			- 1 AS created_by,
			current_timestamp AS creation_date,
			- 1 AS last_updated_by,
			current_timestamp AS last_update_date,
			'N' AS del_flag,
		  h0rr.append_flag	
			
		FROM
			FIN_DM_OPT_FOI.dm_foi_his_ord_rec_report_t h0rr
		WHERE
			h0rr.period_id <= to_number( to_char( current_timestamp, 'yyyymm' ))
			AND h0rr.del_flag = 'N' 
		GROUP BY
			h0rr.YEAR,
			h0rr.period_id,
			h0rr.item_code,
			h0rr.item_name,
			h0rr.category_code,
			h0rr.l2_ceg_code,
			h0rr.l3_ceg_code,
			h0rr.l4_ceg_code,
			h0rr.category_name,
			h0rr.l2_ceg_cn_name,
			h0rr.l3_ceg_short_cn_name,
			h0rr.l4_ceg_short_cn_name,
			h0rr.l4_ceg_cn_name,
			h0rr.l3_ceg_cn_name,
			h0rr.append_flag	

union all

---直取预测数据
				 
		SELECT 
			ifs.YEAR,
			ifs.period_id,
			ifs.item_code AS group_code,
			ifs.item_name AS group_cn_name,
			'ITEM' AS group_level,--Group层级（LV2：生产采购、LV3：专家团、LV4：模块、CATEGORY：品类、ITEM：规格品、SUPPLIER：供应商）
		  ifs.receive_qty,
			ifs.receive_amt_usd,
			ifs.receive_amt_cny,
			--case when avg_price_cny is  null and ifs.receive_qty <>0   then ifs.receive_amt_cny / ifs.receive_qty 
		      -- else ifs.avg_price_cny end as avg_price_cny,--到货均价(CNY)
			NULL AS item_code,
			NULL AS item_name,
			ifs.category_code,
			ifs.category_name,
			case when ifs.l2_ceg_cn_name is not null then ifs.l2_ceg_cn_name   else icmc.l2_ceg_cn_name  end  as l2_ceg_cn_name,
			case when ifs.l3_ceg_short_cn_name is not null then ifs.l3_ceg_short_cn_name   else icmc.l3_ceg_short_cn_name end as l3_ceg_short_cn_name,
			case when ifs.l4_ceg_short_cn_name is not null then ifs.l4_ceg_short_cn_name   else icmc.l4_ceg_short_cn_name  end as  l4_ceg_short_cn_name,
			case when ifs.l2_ceg_code is not null then ifs.l2_ceg_code   else icmc.l2_ceg_code  end  as l2_ceg_code,
			case when ifs.l3_ceg_code is not null then ifs.l3_ceg_code   else icmc.l3_ceg_code  end  as l3_ceg_code,
			case when ifs.l4_ceg_code is not null then ifs.l4_ceg_code   else icmc.l4_ceg_code  end  as l4_ceg_code,
			case when ifs.l4_ceg_cn_name is not null then ifs.l4_ceg_cn_name   else icmc.l4_ceg_cn_name  end  as l4_ceg_cn_name,
			case when ifs.l3_ceg_cn_name is not null then ifs.l3_ceg_cn_name   else icmc.l3_ceg_cn_name  end  as l3_ceg_cn_name,
			- 1 AS created_by,
			current_timestamp AS creation_date,
			- 1 AS last_updated_by,
			current_timestamp AS last_update_date,
			'N' AS del_flag,
			 ifs.append_flag	
		FROM
			FIN_DM_OPT_FOI.dm_foi_item_fcst_sum_t ifs --预测表
		left join FIN_DM_OPT_FOI.dm_dim_foi_item_catg_modl_ceg_t icmc --关联维表
			   on ifs.category_code=icmc.category_code and ifs.item_code=icmc.item_code
		where ifs.append_flag='N'	

		) lra
		LEFT JOIN FIN_DM_OPT_FOI.dm_foi_top_item_info_t tii ON lra.group_code = tii.item_code 
		AND tii.version_id = v_version_id ;
		
--commit;			
				
-----------从到货金额表的 ITEM CATEGORY（品类）------------------


				
insert into FIN_DM_OPT_FOI.dm_foi_top_res_rec_amt_t (
			      id,--（主键）
            year,--会计期(年)
            period_id,--会计期
            group_code,--分层级code编码
            group_cn_name,--分层级中文名称
            group_level,--Group层级（LV4：生产采购、LV3：专家团、LV4：模块、CATEGORY：品类、ITEM：规格品、SUPPLIER：供应商）
            receive_qty,--到货数量
            receive_amt_usd,--到货总金额(USD)
            receive_amt_cny,--到货总金额(CNY)
            --avg_price_cny,--到货均价
            item_code,--ITEM编码
            item_name,--ITEM名称
            category_code,--品类编码
            category_name,--品类名称
            l2_ceg_cn_name,--生产采购
            l3_ceg_short_cn_name,--专家团（Group LV3简称）
            l4_ceg_short_cn_name,--模块（Group LV4简称）
            l2_ceg_code,--生产编码
            l3_ceg_code,--专家团编码（Group LV3简称）
            l4_ceg_code,----模块编码（Group LV4简称）
			      l4_ceg_cn_name,--模块中文名称
            l3_ceg_cn_name,--专家团中文名称
            created_by,--创建人
            creation_date,--创建时间
            last_updated_by,--修改人
            last_update_date,--修改时间
            del_flag,--删除标识(未删除：N，已删除：Y)
            top_flag,--top标识(Y:是规格品、N：非规格品、：非item层级)	
            version_id,--版本
            parent_code,--父层级编码
						append_flag--均价_是否补录(Y:是补录数据、N：真实数据)

			) 
	select 
		         FIN_DM_OPT_FOI.dm_foi_lev_rec_amt_s.nextval id,
            lrat.year,
            lrat.period_id,
            lrat.group_code,
            lrat.group_cn_name,
            lrat.group_level,
            lrat.receive_qty,
            lrat.receive_amt_usd,
            lrat.receive_amt_cny,
            --lrat.avg_price_cny,
            lrat.item_code,
            lrat.item_name,
            lrat.category_code,
            lrat.category_name,
            lrat.l2_ceg_cn_name,
            lrat.l3_ceg_short_cn_name,
            lrat.l4_ceg_short_cn_name,
            lrat.l2_ceg_code,
            lrat.l3_ceg_code,
            lrat.l4_ceg_code,
	          lrat.l4_ceg_cn_name,
            lrat.l3_ceg_cn_name,
            - 1 AS created_by,
			current_timestamp AS creation_date,
			- 1 AS last_updated_by,
			current_timestamp AS last_update_date,
			'N' AS del_flag,
            lrat.top_flag,	
            v_version_id as version_id,
            lrat.l4_ceg_code as parent_code,
            lrat.append_flag  --均价_是否补录(Y:是补录数据、N：真实数据)
	    
	
	FROM
		(
		SELECT
			lra.YEAR,
			lra.period_id,
			lra.category_code AS group_code,
			lra.category_name AS group_cn_name,
			'CATEGORY' AS group_level,
			SUM ( lra.receive_qty ) as  receive_qty,
			SUM ( lra.receive_amt_usd ) as  receive_amt_usd,
			SUM ( lra.receive_amt_cny ) AS receive_amt_cny,
		 --CASE
			--	WHEN nvl ( SUM ( lra.receive_qty ), 0 ) = 0 THEN
			--	0 ELSE ROUND( SUM ( lra.receive_amt_cny ) / SUM ( lra.receive_qty ), 6 ) 
			--END AS avg_price_cny,
			--NULL AS avg_price_usd,
			NULL AS item_code,
			NULL AS item_name,
			NULL AS category_code,
			NULL AS category_name,
			lra.l2_ceg_cn_name,
			lra.l3_ceg_short_cn_name,
			lra.l4_ceg_short_cn_name,
			lra.l2_ceg_code,
			lra.l3_ceg_code,
			lra.l4_ceg_code,
			lra.l4_ceg_cn_name,
			lra.l3_ceg_cn_name,
			lra.top_flag, ----top标识(Y:是规格品、N：非规格品)--top标识(Y:是规格品、N：非规格品、：非item层级)
			lra.append_flag
		FROM
			FIN_DM_OPT_FOI.dm_foi_top_res_rec_amt_t lra 
		WHERE
			lra.version_id = v_version_id 
			AND lra.group_level = 'ITEM'  and lra.append_flag='N'
		GROUP BY
			lra.YEAR,
			lra.period_id,
			lra.category_code,
			lra.category_name,
			lra.l2_ceg_code,
			lra.l3_ceg_code,
			lra.l4_ceg_code,
			lra.l2_ceg_cn_name,
			lra.l3_ceg_short_cn_name,
			lra.l4_ceg_short_cn_name,
			lra.l4_ceg_cn_name,
			lra.l3_ceg_cn_name,
			lra.top_flag,
			lra.append_flag
		) lrat;
--commit;
-----------从到货金额表的 category（品类） 层级收敛到  l4层级（模块）


				
insert into FIN_DM_OPT_FOI.dm_foi_top_res_rec_amt_t (
			      id,--（主键）
            year,--会计期(年)
            period_id,--会计期
            group_code,--分层级code编码
            group_cn_name,--分层级中文名称
            group_level,--Group层级（LV4：生产采购、LV3：专家团、LV4：模块、CATEGORY：品类、ITEM：规格品、SUPPLIER：供应商）
            receive_qty,--到货数量
            receive_amt_usd,--到货总金额(USD)
            receive_amt_cny,--到货总金额(CNY)
            --avg_price_cny,--到货均价
            item_code,--ITEM编码
            item_name,--ITEM名称
            category_code,--品类编码
            category_name,--品类名称
            l2_ceg_cn_name,--生产采购
            l3_ceg_short_cn_name,--专家团（Group LV3简称）
            l4_ceg_short_cn_name,--模块（Group LV4简称）
            l2_ceg_code,--生产编码
            l3_ceg_code,--专家团编码（Group LV3简称）
            l4_ceg_code,----模块编码（Group LV4简称）
			     l4_ceg_cn_name,--模块中文名称
            l3_ceg_cn_name,--专家团中文名称
            created_by,--创建人
            creation_date,--创建时间
            last_updated_by,--修改人
            last_update_date,--修改时间
            del_flag,--删除标识(未删除：N，已删除：Y)
            top_flag,--top标识(Y:是规格品、N：非规格品、：非item层级)	
            version_id,--版本
            parent_code,--父层级编码
			append_flag--均价_是否补录(Y:是补录数据、N：真实数据)

			) 
	select 
		        FIN_DM_OPT_FOI.dm_foi_lev_rec_amt_s.nextval id,
            lrat.year,
            lrat.period_id,
            lrat.group_code,
            lrat.group_cn_name,
            lrat.group_level,
            lrat.receive_qty,
            lrat.receive_amt_usd,
            lrat.receive_amt_cny,
            --lrat.avg_price_cny,
            lrat.item_code,
            lrat.item_name,
            lrat.category_code,
            lrat.category_name,
            lrat.l2_ceg_cn_name,
            lrat.l3_ceg_short_cn_name,
            lrat.l4_ceg_short_cn_name,
            lrat.l2_ceg_code,
            lrat.l3_ceg_code,
            lrat.l4_ceg_code,
			      lrat.l4_ceg_cn_name,
            lrat.l3_ceg_cn_name,
            - 1 AS created_by,
		        current_timestamp AS creation_date,
		        - 1 AS last_updated_by,
		        current_timestamp AS last_update_date,
		        'N' AS del_flag,
            lrat.top_flag,	
            v_version_id as version_id,	
            lrat.l3_ceg_code as parent_code,
            lrat.append_flag --均价_是否补录(Y:是补录数据、N：真实数据)
						
	
	FROM
		(
		SELECT
			lra.YEAR,
			lra.period_id,
			lra.l4_ceg_code AS group_code,
			lra.l4_ceg_short_cn_name AS group_cn_name,
			'LV4' AS group_level,
			SUM ( lra.receive_qty ) as receive_qty,
			SUM ( lra.receive_amt_usd ) as receive_amt_usd,
			SUM ( lra.receive_amt_cny ) AS receive_amt_cny,
		--CASE
		--		WHEN nvl ( SUM ( lra.receive_qty ), 0 ) = 0 THEN
		--		0 ELSE ROUND( SUM ( lra.receive_amt_cny ) / SUM ( lra.receive_qty ), 6 ) 
		--	END AS avg_price_cny,
			--NULL AS avg_price_usd,
			NULL AS item_code,
			NULL AS item_name,
			NULL AS category_code,
			NULL AS category_name,
			lra.l2_ceg_cn_name,
			lra.l3_ceg_short_cn_name,
			NULL AS l4_ceg_short_cn_name,
			lra.l2_ceg_code,
			lra.l3_ceg_code,
			lra.l4_ceg_code,
			lra.l4_ceg_cn_name,
			lra.l3_ceg_cn_name,
			lra.top_flag,
			lra.append_flag
		FROM
			FIN_DM_OPT_FOI.dm_foi_top_res_rec_amt_t lra 
		WHERE
			 lra.version_id = v_version_id
			AND group_level = 'CATEGORY'  and lra.append_flag='N'
		GROUP BY
			lra.YEAR,
			lra.period_id,
			lra.l4_ceg_code,
			lra.l4_ceg_short_cn_name,
			lra.l2_ceg_cn_name,
			lra.l3_ceg_short_cn_name,
			lra.l2_ceg_code,
			lra.l3_ceg_code,
			lra.l3_ceg_cn_name,
			lra.l4_ceg_cn_name,
			lra.top_flag,
			lra.append_flag	
                 ) lrat ;	
				
--commit;				
-----------从到货金额表的 l4层级（模块） 层级收敛到  l3层级（专家团）


				
insert into FIN_DM_OPT_FOI.dm_foi_top_res_rec_amt_t (
			      id,--（主键）
            year,--会计期(年)
            period_id,--会计期
            group_code,--分层级code编码
            group_cn_name,--分层级中文名称
            group_level,--Group层级（LV4：生产采购、LV3：专家团、LV4：模块、CATEGORY：品类、ITEM：规格品、SUPPLIER：供应商）
            receive_qty,--到货数量
            receive_amt_usd,--到货总金额(USD)
            receive_amt_cny,--到货总金额(CNY)
            --avg_price_cny,--到货均价
            item_code,--ITEM编码
            item_name,--ITEM名称
            category_code,--品类编码
            category_name,--品类名称
            l2_ceg_cn_name,--生产采购
            l3_ceg_short_cn_name,--专家团（Group LV3简称）
            l4_ceg_short_cn_name,--模块（Group LV4简称）
            l2_ceg_code,--生产编码
            l3_ceg_code,--专家团编码（Group LV3简称）
            l4_ceg_code,----模块编码（Group LV4简称）
			      l4_ceg_cn_name,--模块中文名称
            l3_ceg_cn_name,--专家团中文名称
            created_by,--创建人
            creation_date,--创建时间
            last_updated_by,--修改人
            last_update_date,--修改时间
            del_flag,--删除标识(未删除：N，已删除：Y)
            top_flag,--top标识(Y:是规格品、N：非规格品、：非item层级)	
            version_id,--版本
            parent_code,--父层级编码
						append_flag--均价_是否补录(Y:是补录数据、N：真实数据)

			) 
	select 
		        FIN_DM_OPT_FOI.dm_foi_lev_rec_amt_s.nextval id,
            lrat.year,
            lrat.period_id,
            lrat.group_code,
            lrat.group_cn_name,
            lrat.group_level,
            lrat.receive_qty,
            lrat.receive_amt_usd,
            lrat.receive_amt_cny,
            --lrat.avg_price_cny,
            lrat.item_code,
            lrat.item_name,
            lrat.category_code,
            lrat.category_name,
            lrat.l2_ceg_cn_name,
            lrat.l3_ceg_short_cn_name,
            lrat.l4_ceg_short_cn_name,
            lrat.l2_ceg_code,
            lrat.l3_ceg_code,
            lrat.l4_ceg_code,
	         lrat.l4_ceg_cn_name,
            lrat.l3_ceg_cn_name,
            -1 as created_by,
            current_timestamp as creation_date,
            -1 as last_updated_by,
            current_timestamp as last_update_date,
            'N' as  del_flag,
            lrat.top_flag,	
            v_version_id as version_id,
            lrat.l2_ceg_code as parent_code,
            lrat.append_flag --均价_是否补录(Y:是补录数据、N：真实数据)
						
from  (	select   
           lra.year,
           lra.period_id,
           lra.l3_ceg_code as group_code,
           lra.l3_ceg_short_cn_name as group_cn_name,
           'LV3' as group_level,
           sum(lra.receive_qty) as receive_qty,
           sum(lra.receive_amt_usd) as receive_amt_usd,
           sum(lra.receive_amt_cny) as receive_amt_cny,
           --case when nvl (sum(lra.receive_qty),0) = 0 then 0 
		       --        else ROUND(sum(lra.receive_amt_cny)  / sum(lra.receive_qty),6) end as avg_price_cny,--到货均价(CNY)
			     --		 null as avg_price_usd,			
           null as item_code,
           null as item_name,
           null as category_code,
           null as category_name,
           lra.l2_ceg_cn_name as l2_ceg_cn_name,
           null as  l3_ceg_short_cn_name,
           null as l4_ceg_short_cn_name,
           lra.l2_ceg_code,
           lra.l3_ceg_code,
           null as l4_ceg_code,
		       null as l4_ceg_cn_name,
           lra.l3_ceg_cn_name,
	         lra.top_flag,
	         lra.append_flag
	from FIN_DM_OPT_FOI.dm_foi_top_res_rec_amt_t	lra	
		where 	 lra.version_id=v_version_id and group_level='LV4' and lra.append_flag='N'
	    group by 
		        lra.year,
		        lra.period_id,
            lra.l2_ceg_code,
            lra.l3_ceg_code,
            lra.l2_ceg_cn_name,
            lra.l3_ceg_short_cn_name,
            lra.l3_ceg_cn_name,
		        lra.top_flag,
		        lra.append_flag
				
                 ) lrat;


	--commit;	
	-----------从到货金额表的 LV3层级（专家团） 层级收敛到  LV4层级（生产采购）
						
insert into FIN_DM_OPT_FOI.dm_foi_top_res_rec_amt_t (
			      id,--（主键）
            year,--会计期(年)
            period_id,--会计期
            group_code,--分层级code编码
            group_cn_name,--分层级中文名称
            group_level,--Group层级（LV4：生产采购、LV3：专家团、LV4：模块、CATEGORY：品类、ITEM：规格品、SUPPLIER：供应商）
            receive_qty,--到货数量
            receive_amt_usd,--到货总金额(USD)
            receive_amt_cny,--到货总金额(CNY)
            --avg_price_cny,--到货均价
            item_code,--ITEM编码
            item_name,--ITEM名称
            category_code,--品类编码
            category_name,--品类名称
            l2_ceg_cn_name,--生产采购
            l3_ceg_short_cn_name,--专家团（Group LV3简称）
            l4_ceg_short_cn_name,--模块（Group LV4简称）
            l2_ceg_code,--生产编码
            l3_ceg_code,--专家团编码（Group LV3简称）
            l4_ceg_code,----模块编码（Group LV4简称）
			     l4_ceg_cn_name,--模块中文名称
            l3_ceg_cn_name,--专家团中文名称
            created_by,--创建人
            creation_date,--创建时间
            last_updated_by,--修改人
            last_update_date,--修改时间
            del_flag,--删除标识(未删除：N，已删除：Y)
            top_flag,--top标识(Y:是规格品、N：非规格品、：非item层级)	
            version_id,--版本
            parent_code,--父层级编码
			     append_flag--均价_是否补录(Y:是补录数据、N：真实数据)

			) 
select 
		        FIN_DM_OPT_FOI.dm_foi_lev_rec_amt_s.nextval id,
            lrat.year,
            lrat.period_id,
            lrat.group_code,
            lrat.group_cn_name,
            lrat.group_level,
            lrat.receive_qty,
            lrat.receive_amt_usd,
            lrat.receive_amt_cny,
            --lrat.avg_price_cny,
            lrat.item_code,
            lrat.item_name,
            lrat.category_code,
            lrat.category_name,
            lrat.l2_ceg_cn_name,
            lrat.l3_ceg_short_cn_name,
            lrat.l4_ceg_short_cn_name,
            lrat.l2_ceg_code,
            lrat.l3_ceg_code,
            lrat.l4_ceg_code,
	          lrat.l4_ceg_cn_name,
            lrat.l3_ceg_cn_name,
            -1 as created_by,
            current_timestamp as creation_date,
            -1 as last_updated_by,
            current_timestamp as last_update_date,
            'N' as  del_flag,
            lrat.top_flag,	
            v_version_id as version_id,
            null as parent_code,
            lrat.append_flag --'N' as append_flag--均价_是否补录(Y:是补录数据、N：真实数据)
						
from  (	select   
           lra.year,
           lra.period_id,
           lra.l2_ceg_code as group_code,
           lra.l2_ceg_cn_name as group_cn_name,
           'LV2' as group_level,
           sum(lra.receive_qty) as receive_qty,
           sum(lra.receive_amt_usd) as receive_amt_usd,
           sum(lra.receive_amt_cny) as receive_amt_cny,
          -- case when nvl (sum(lra.receive_qty),0) = 0 then 0 
		     --       else ROUND(sum(lra.receive_amt_cny)  / sum(lra.receive_qty),6) end as avg_price_cny,--到货均价(CNY)
		       null as l4_ceg_cn_name ,
           null as l3_ceg_cn_name,
					 null as avg_price_usd,			
           null as item_code,
           null as item_name,
           null as category_code,
           null as category_name,
           null as l2_ceg_cn_name,
           null as l3_ceg_short_cn_name,
           null as l4_ceg_short_cn_name,
           lra.l2_ceg_code,
           null as l3_ceg_code,
           null as l4_ceg_code,
	       lra.top_flag,
	       lra.append_flag
	from FIN_DM_OPT_FOI.dm_foi_top_res_rec_amt_t	lra	
	where 	lra.group_level='LV3' and lra.version_id=v_version_id and lra.append_flag='N'
	group by 
		    lra.year,
		    lra.period_id,
            lra.l2_ceg_code,
            lra.l2_ceg_cn_name,
		    lra.top_flag,
		    lra.append_flag
                 ) lrat;				 
				

-- 3.收集信息
  ANALYSE FIN_DM_OPT_FOI.dm_foi_top_res_rec_amt_t;	
	
	 -- 写结束日志
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 1,
   F_CAL_LOG_DESC => '规格品金额函数计算完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => x_success_flag,
   F_ERRBUF => 'SUCCESS');
		 
	return 'SUCCESS';		 

exception 
  	when others then 
	
  x_success_flag := 0;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   f_step_num => v_exception_flag,
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => x_success_flag, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );	 

end;
$$
/

