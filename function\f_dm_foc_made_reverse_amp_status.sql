-- Name: f_dm_foc_made_reverse_amp_status; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_made_reverse_amp_status(f_industry_flag character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间2024年4月26日
  创建人  ：唐钦
  背景描述：通用颗粒度下，反向的3个视角，年度分析页面：单年权重、涨跌幅、状态码逻辑计算
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_MADE_REVERSE_AMP_STATUS()
*/
DECLARE
  V_SP_NAME           VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_MADE_REVERSE_AMP_STATUS'; --存储过程名称
  V_STEP_NUM          BIGINT := 0; --步骤号
  V_VERSION_ID        BIGINT; --版本号ID
  V_SQL_CONDITION     TEXT; -- 筛选条件SQL
  V_SQL               TEXT; --执行语句
  V_YEAR              BIGINT := YEAR(CURRENT_TIMESTAMP); -- 当前年份
  V_LV1_PROD_RND_TEAM TEXT;
  V_LV2_PROD_RND_TEAM TEXT;
  V_LV3_PROD_RND_TEAM TEXT;
  V_CEG_TOTAL         TEXT;
  V_MANUFACTURE_TOTAL        TEXT;
  V_SHIPPING_TOTAL        TEXT;
  V_IN_MANUFACTURE_TOTAL     TEXT;
  V_IN_SHIPPING_TOTAL     TEXT;
  V_MANUFACTURE_TOTAL_BAK    TEXT;
  V_SHIPPING_TOTAL_BAK    TEXT;
  V_GROUP_TOTAL       TEXT;
  V_MADE_TOTAL         TEXT;
  V_PARENT_CODE       TEXT;
  V_PARENT_CN_NAME       TEXT;
  V_REL_MADE_CODE      TEXT;
  V_GROUP_LEVEL       TEXT;
  V_MADE_CODE          TEXT;
  V_GROUP_CODE        TEXT;
  V_REL_VIEW_FLAG     TEXT;
  V_CHILD_LEVEL       TEXT;
  V_VIEW_NUM          BIGINT;
  V_JOIN_PARA          TEXT;
  
  -- 202405版本新增
  V_VERSION_TABLE VARCHAR(100);
  V_TO_AMP_TABLE VARCHAR(100);
  V_TO_STATUS_TABLE VARCHAR(100);
  V_FROM_AMP_TABLE VARCHAR(100);
  V_FROM_STATUS_TABLE VARCHAR(100);
  V_FROM_WEIGHT_TABLE VARCHAR(100);
  -- 202407版本新增
  V_VIEW_FIXED BIGINT;   -- 固定视角值
  V_BEGIN_NUM INT;
  V_LV4_PROD_RND_TEAM VARCHAR(500);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
 -- 判断产业项目为ICT还是数字能源
  IF F_INDUSTRY_FLAG = 'I' THEN   -- ICT  
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T';
     V_FROM_AMP_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_MID_GROUP_AMP_T';
     V_FROM_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_REV_ANNUAL_WEIGHT_T';
     V_FROM_STATUS_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DIM_ITEM_STATUS_T';
     V_TO_AMP_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_REV_ANNUAL_AMP_T';
     V_TO_STATUS_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_REV_ANNUAL_STATUS_CODE_T';
  ELSIF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源 
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T';
     V_FROM_AMP_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_MID_GROUP_AMP_T';
     V_FROM_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_REV_ANNUAL_WEIGHT_T';
     V_FROM_STATUS_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DIM_ITEM_STATUS_T';
     V_TO_AMP_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_REV_ANNUAL_AMP_T';
     V_TO_STATUS_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_REV_ANNUAL_STATUS_CODE_T';
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 数字能源 
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T';
     V_FROM_AMP_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_MID_GROUP_AMP_T';
     V_FROM_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_REV_ANNUAL_WEIGHT_T';
     V_FROM_STATUS_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DIM_ITEM_STATUS_T';
     V_TO_AMP_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_REV_ANNUAL_AMP_T';
     V_TO_STATUS_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_REV_ANNUAL_STATUS_CODE_T';
  END IF;
   
  --版本号入参判断，当入参为空，取TOP规格品清单最新版本号
  IF F_VERSION_ID IS NULL THEN
     V_SQL := '
       SELECT VERSION_ID 
       FROM
           '||V_VERSION_TABLE||'
       WHERE
           DEL_FLAG = ''N''
           AND STATUS = 1
           AND UPPER(DATA_TYPE) = ''CATEGORY''
           ORDER BY LAST_UPDATE_DATE DESC
           LIMIT 1';
    EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
  --入参不为空，则以入参为版本号
  ELSE 
    V_VERSION_ID := F_VERSION_ID;
  END IF;
    
  --1.删除目标表数据:

     EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_AMP_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;
     EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_STATUS_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;
     
--1.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除产业标识为：'||F_INDUSTRY_FLAG||'，版本号为：'||V_VERSION_ID||'的'||V_TO_AMP_TABLE||'/'||V_TO_STATUS_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
    
    ------------------------------------------------------年度涨跌幅计算逻辑--------------------------------------------------------------------
-- 创建涨跌幅临时表
  DROP TABLE IF EXISTS GROUP_AMP_TMP;
  CREATE TEMPORARY TABLE GROUP_AMP_TMP(
  VIEW_FLAG VARCHAR(2),
  PERIOD_YEAR BIGINT,
  MADE_CODE VARCHAR(200),
  MADE_CN_NAME VARCHAR(500),
  GROUP_CODE VARCHAR(200),
  GROUP_CN_NAME VARCHAR(2000),
  GROUP_LEVEL VARCHAR(50),
  PARENT_CODE VARCHAR(200),
  PARENT_CN_NAME VARCHAR(50),
  LV0_PROD_RND_TEAM_CODE VARCHAR(50),
  LV0_PROD_RD_TEAM_CN_NAME VARCHAR(500),
  LV1_PROD_RND_TEAM_CODE VARCHAR(50),
  LV1_PROD_RD_TEAM_CN_NAME VARCHAR(500),
  LV2_PROD_RND_TEAM_CODE VARCHAR(50),
  LV2_PROD_RD_TEAM_CN_NAME VARCHAR(500),
  LV3_PROD_RND_TEAM_CODE VARCHAR(50),
  LV3_PROD_RD_TEAM_CN_NAME VARCHAR(500),
  LV4_PROD_RND_TEAM_CODE VARCHAR(50),
  LV4_PROD_RD_TEAM_CN_NAME VARCHAR(500),
  SHIPPING_OBJECT_CODE VARCHAR(200),
  SHIPPING_OBJECT_CN_NAME VARCHAR(200),
  MANUFACTURE_OBJECT_CODE VARCHAR(200),
  MANUFACTURE_OBJECT_CN_NAME VARCHAR(200),
  ANNUAL_AMP NUMERIC,
  CALIBER_FLAG VARCHAR(2),
  OVERSEA_FLAG VARCHAR(2),
  LV0_PROD_LIST_CODE VARCHAR(50),
  LV0_PROD_LIST_CN_NAME VARCHAR(500)) 
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY HASH(GROUP_CODE,PARENT_CODE);
  DBMS_OUTPUT.PUT_LINE('涨跌幅临时表创建成功');
 
   -- 定义变量
  V_LV1_PROD_RND_TEAM := 'LV1_PROD_RND_TEAM_CODE , LV1_PROD_RD_TEAM_CN_NAME ,';
  V_LV2_PROD_RND_TEAM := 'LV2_PROD_RND_TEAM_CODE , LV2_PROD_RD_TEAM_CN_NAME ,';
  V_MANUFACTURE_TOTAL        := 'MANUFACTURE_OBJECT_CODE ,MANUFACTURE_OBJECT_CN_NAME ,';
  V_SHIPPING_TOTAL        := 'SHIPPING_OBJECT_CODE,SHIPPING_OBJECT_CN_NAME,';
  V_MANUFACTURE_TOTAL_BAK    := V_MANUFACTURE_TOTAL;
  V_SHIPPING_TOTAL_BAK    := V_SHIPPING_TOTAL;  
  V_IN_MANUFACTURE_TOTAL     := 'MANUFACTURE_OBJECT_CODE ,MANUFACTURE_OBJECT_CN_NAME ,';
  V_IN_SHIPPING_TOTAL     := 'SHIPPING_OBJECT_CODE,SHIPPING_OBJECT_CN_NAME,';
  
  -- 将视角4：view_flag = 3的ITEM层级的总金额数据取出来存放到会话级临时表
      V_VIEW_NUM       := 5;
      V_MADE_TOTAL      := 'MANUFACTURE_OBJECT_CODE AS MADE_CODE,MANUFACTURE_OBJECT_CN_NAME AS MADE_CN_NAME,';
  IF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407新增IAS
     V_BEGIN_NUM := 1;   -- 最细粒度-LV4层级
     V_VIEW_FIXED := 7;   -- 最细粒度视角
     V_LV4_PROD_RND_TEAM := 'LV4_PROD_RND_TEAM_CODE ,
                             LV4_PROD_RD_TEAM_CN_NAME ,';
     V_PARENT_CODE := 'LV4_PROD_RND_TEAM_CODE AS PARENT_CODE ,';
     V_PARENT_CN_NAME := 'LV4_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
  ELSE   -- ICT/数字能源
     V_BEGIN_NUM := 2;   -- 最细粒度-LV3层级
     V_VIEW_FIXED := 3;
     V_PARENT_CODE := 'LV3_PROD_RND_TEAM_CODE AS PARENT_CODE ,';
     V_PARENT_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
  END IF;

-- 将VIEW_FLAG = 3/7 的ITEN层级的涨跌幅数据提取到临时表中
V_SQL := '
  INSERT INTO GROUP_AMP_TMP
    (PERIOD_YEAR,
     MADE_CODE,
     MADE_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     PARENT_CODE,
     PARENT_CN_NAME,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE,
     LV2_PROD_RD_TEAM_CN_NAME,
     LV3_PROD_RND_TEAM_CODE,
     LV3_PROD_RD_TEAM_CN_NAME,
     '||V_LV4_PROD_RND_TEAM||'
     '||V_MANUFACTURE_TOTAL_BAK
     ||V_SHIPPING_TOTAL_BAK||'
     ANNUAL_AMP,
     VIEW_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME)
    SELECT PERIOD_YEAR,
           '||V_MADE_TOTAL||'
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           '||V_PARENT_CODE||'
           '||V_PARENT_CN_NAME||'
           LV0_PROD_RND_TEAM_CODE,
           LV0_PROD_RD_TEAM_CN_NAME,
           LV1_PROD_RND_TEAM_CODE,
           LV1_PROD_RD_TEAM_CN_NAME,
           LV2_PROD_RND_TEAM_CODE,
           LV2_PROD_RD_TEAM_CN_NAME,
           LV3_PROD_RND_TEAM_CODE,
           LV3_PROD_RD_TEAM_CN_NAME,
           '||V_LV4_PROD_RND_TEAM||'
           '||V_MANUFACTURE_TOTAL_BAK
           ||V_SHIPPING_TOTAL_BAK||'
           ANNUAL_AMP,
           '''||V_VIEW_NUM||''' AS VIEW_FLAG,
           CALIBER_FLAG,
           OVERSEA_FLAG,
           LV0_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME
      FROM '||V_FROM_AMP_TABLE||'
     WHERE VIEW_FLAG = '''||V_VIEW_FIXED||'''   -- 取固定视角数据
       AND VERSION_ID = '||V_VERSION_ID||'
       AND GROUP_LEVEL = ''ITEM''';
       
   DBMS_OUTPUT.PUT_LINE(V_SQL);      
     EXECUTE IMMEDIATE V_SQL; 
            
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '产业标识为：'||F_INDUSTRY_FLAG||'，ITEM层级的涨跌幅数据存放到临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');     

    ------------------------------------------------------状态码计算逻辑--------------------------------------------------------------------    

-- 创建状态码临时表
  DROP TABLE IF EXISTS MID_STATUS_TMP;
  CREATE TEMPORARY TABLE MID_STATUS_TMP(
    VIEW_FLAG VARCHAR(2),
    PERIOD_YEAR BIGINT,
    MADE_CODE VARCHAR(200),
    MADE_CN_NAME VARCHAR(500),
    GROUP_CODE VARCHAR(200),
    GROUP_CN_NAME VARCHAR(2000),
    GROUP_LEVEL VARCHAR(50),
    PARENT_CODE VARCHAR(200),
    PARENT_CN_NAME VARCHAR(50),
    LV0_PROD_RND_TEAM_CODE VARCHAR(50),
    LV0_PROD_RD_TEAM_CN_NAME VARCHAR(500),
    LV1_PROD_RND_TEAM_CODE VARCHAR(50),
    LV1_PROD_RD_TEAM_CN_NAME VARCHAR(500),
    LV2_PROD_RND_TEAM_CODE VARCHAR(50),
    LV2_PROD_RD_TEAM_CN_NAME VARCHAR(500),
    LV3_PROD_RND_TEAM_CODE VARCHAR(50),
    LV3_PROD_RD_TEAM_CN_NAME VARCHAR(500),
    LV4_PROD_RND_TEAM_CODE VARCHAR(50),
    LV4_PROD_RD_TEAM_CN_NAME VARCHAR(500),
    SHIPPING_OBJECT_CODE VARCHAR(200),
    SHIPPING_OBJECT_CN_NAME VARCHAR(200),
    MANUFACTURE_OBJECT_CODE VARCHAR(200),
    MANUFACTURE_OBJECT_CN_NAME VARCHAR(200),
    STATUS_CODE BIGINT,
    CALIBER_FLAG VARCHAR(2),
    OVERSEA_FLAG VARCHAR(2),
    LV0_PROD_LIST_CODE VARCHAR(50),
    LV0_PROD_LIST_CN_NAME VARCHAR(500),
    APPEND_YEAR BIGINT) 
    ON COMMIT PRESERVE ROWS 
    DISTRIBUTE BY HASH(GROUP_CODE,PARENT_CODE);
  DBMS_OUTPUT.PUT_LINE('状态码临时表创建成功');
   
  -- 重置变量  
  V_MANUFACTURE_TOTAL_BAK    := V_MANUFACTURE_TOTAL;
  V_SHIPPING_TOTAL_BAK    := V_SHIPPING_TOTAL;

    -- 将视角4：view_flag = 3的ITEM层级的总金额数据取出来存放到会话级临时表
      V_VIEW_NUM       := 5;
      V_MADE_TOTAL := 'MANUFACTURE_OBJECT_CODE AS MADE_CODE,MANUFACTURE_OBJECT_CN_NAME AS MADE_CN_NAME,';
  
  -- 将VIEW_FLAG = 3/7 的ITEN层级的涨跌幅数据提取到临时表中
V_SQL := '
 INSERT INTO MID_STATUS_TMP(
        PERIOD_YEAR ,
        MADE_CODE,
        MADE_CN_NAME,
        GROUP_CODE ,
        GROUP_CN_NAME ,
        GROUP_LEVEL ,
        PARENT_CODE ,
        PARENT_CN_NAME,
        LV0_PROD_RND_TEAM_CODE ,
        LV0_PROD_RD_TEAM_CN_NAME ,
        LV1_PROD_RND_TEAM_CODE ,
        LV1_PROD_RD_TEAM_CN_NAME ,
        LV2_PROD_RND_TEAM_CODE ,
        LV2_PROD_RD_TEAM_CN_NAME ,
        LV3_PROD_RND_TEAM_CODE ,
        LV3_PROD_RD_TEAM_CN_NAME ,
        '||V_LV4_PROD_RND_TEAM||'
         '||V_MANUFACTURE_TOTAL_BAK
         ||V_SHIPPING_TOTAL_BAK||'
        STATUS_CODE ,
        VIEW_FLAG ,
        CALIBER_FLAG ,
        OVERSEA_FLAG ,
        LV0_PROD_LIST_CODE ,
        LV0_PROD_LIST_CN_NAME,
        APPEND_YEAR )
        
    SELECT PERIOD_YEAR ,
            '||V_MADE_TOTAL||'
            ITEM_CODE AS GROUP_CODE ,
            ITEM_CN_NAME AS GROUP_CN_NAME ,
            ''ITEM'' AS GROUP_LEVEL ,
            '||V_PARENT_CODE||'
            '||V_PARENT_CN_NAME||'
            LV0_PROD_RND_TEAM_CODE ,
            LV0_PROD_RD_TEAM_CN_NAME ,
            LV1_PROD_RND_TEAM_CODE ,
            LV1_PROD_RD_TEAM_CN_NAME ,
            LV2_PROD_RND_TEAM_CODE ,
            LV2_PROD_RD_TEAM_CN_NAME ,
            LV3_PROD_RND_TEAM_CODE ,
            LV3_PROD_RD_TEAM_CN_NAME ,
            '||V_LV4_PROD_RND_TEAM||'
            '||V_MANUFACTURE_TOTAL_BAK
             ||V_SHIPPING_TOTAL_BAK||'
            STATUS_CODE ,
            '''||V_VIEW_NUM||''' AS VIEW_FLAG,
            CALIBER_FLAG ,
            OVERSEA_FLAG ,
            LV0_PROD_LIST_CODE ,
            LV0_PROD_LIST_CN_NAME,
            APPEND_YEAR
       FROM '||V_FROM_STATUS_TABLE||'
      WHERE VIEW_FLAG = '''||V_VIEW_FIXED||'''';
      
    DBMS_OUTPUT.PUT_LINE(V_SQL);      
    EXECUTE IMMEDIATE V_SQL; 
    DBMS_OUTPUT.PUT_LINE('状态码数据计算成功');      
            
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '产业标识为：'||F_INDUSTRY_FLAG||'，ITEM层级的涨跌幅数据存放到临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');                 

    ------------------------------------------------------涨跌幅计算逻辑--------------------------------------------------------------------    

-- 重定义变量
  V_LV3_PROD_RND_TEAM := 'LV3_PROD_RND_TEAM_CODE ,
                          LV3_PROD_RD_TEAM_CN_NAME ,';
  V_REL_VIEW_FLAG     := 'AND T1.VIEW_FLAG = T2.VIEW_FLAG
                        ';
  V_REL_MADE_CODE      := 'AND T1.MADE_CODE = T2.MADE_CODE ';
  V_CHILD_LEVEL       := '''ITEM''';
  V_MADE_TOTAL         := 'T1.MANUFACTURE_OBJECT_CODE AS MADE_CODE,T1.MANUFACTURE_OBJECT_CN_NAME AS MADE_CN_NAME,';
  V_SHIPPING_TOTAL        := 'T1.SHIPPING_OBJECT_CODE,T1.SHIPPING_OBJECT_CN_NAME,';
  V_MANUFACTURE_TOTAL        := 'T1.MANUFACTURE_OBJECT_CODE,T1.MANUFACTURE_OBJECT_CN_NAME,';
  V_JOIN_PARA   := ' AND T1.SHIPPING_OBJECT_CODE   =  T2.SHIPPING_OBJECT_CODE
                     AND T1.MANUFACTURE_OBJECT_CODE   =  T2.MANUFACTURE_OBJECT_CODE';
-- 对于不同颗粒度、不同视角的不同层级进行循环计算
  FOR LEV_NUM IN V_BEGIN_NUM .. 7 LOOP
    IF LEV_NUM = 1 THEN
      -- LV4层级
      V_GROUP_LEVEL       := '''LV4''';
      V_GROUP_TOTAL       := 'LV4_PROD_RND_TEAM_CODE AS GROUP_CODE,
                              LV4_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME, 
                              ''LV4'' AS GROUP_LEVEL,
                              ';
      V_PARENT_CODE       := 'LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_PARENT_CN_NAME       := 'LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
    ELSIF LEV_NUM = 2 THEN
      -- LV3层级
      V_GROUP_LEVEL       := '''LV3''';
      V_GROUP_TOTAL       := 'LV3_PROD_RND_TEAM_CODE AS GROUP_CODE,
                              LV3_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME, 
                              ''LV3'' AS GROUP_LEVEL,
                              ';
      V_PARENT_CODE       := 'LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_PARENT_CN_NAME       := 'LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      V_LV4_PROD_RND_TEAM := '';
      IF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407新增IAS
         V_CHILD_LEVEL       := '''LV4''';
      ELSE   -- ICT/数字能源
         V_CHILD_LEVEL       := '''ITEM''';
      END IF;
    ELSIF LEV_NUM = 3 THEN
      -- LV2层级
      V_LV3_PROD_RND_TEAM := '';
      V_CHILD_LEVEL       := '''LV3''';
      V_GROUP_TOTAL       := 'LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,
                              LV2_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME, 
                            ''LV2'' AS GROUP_LEVEL,
                           ';
      V_PARENT_CODE       := 'LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_PARENT_CN_NAME       := 'LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      V_GROUP_LEVEL       := '''LV2''';
    ELSIF LEV_NUM = 4 THEN
      -- LV1层级
      V_LV2_PROD_RND_TEAM := '';
      V_CHILD_LEVEL       := '''LV2''';
      V_GROUP_TOTAL       := 'LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,
                              LV1_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME, 
                             ''LV1'' AS GROUP_LEVEL,
                           ';
      V_PARENT_CODE       := 'MANUFACTURE_OBJECT_CODE  AS PARENT_CODE,';
      V_PARENT_CN_NAME     := 'MANUFACTURE_OBJECT_CN_NAME  AS PARENT_CN_NAME,'; 
      V_GROUP_LEVEL       := '''LV1''';
    ELSIF LEV_NUM = 5 THEN
      -- LV1往上分别按视角收敛3个不同层级
      --制造层级
      V_LV1_PROD_RND_TEAM := '';
      V_CHILD_LEVEL       := '''LV1''';
      V_GROUP_TOTAL       := 'MANUFACTURE_OBJECT_CODE AS GROUP_CODE,
                              MANUFACTURE_OBJECT_CN_NAME AS GROUP_CN_NAME ,
                              ''MANUFACTURE_OBJECT'' AS GROUP_LEVEL,';
      V_PARENT_CODE       := 'SHIPPING_OBJECT_CODE AS PARENT_CODE,';
      V_PARENT_CN_NAME    := 'SHIPPING_OBJECT_CN_NAME AS PARENT_CN_NAME,';
      V_GROUP_LEVEL       := '''MANUFACTURE_OBJECT''';
    ELSIF LEV_NUM = 6 THEN
      -- 发货层级
      V_IN_MANUFACTURE_TOTAL  := '';
      V_CHILD_LEVEL := '''MANUFACTURE_OBJECT''';
      V_MADE_TOTAL   := 'T1.SHIPPING_OBJECT_CODE AS MADE_CODE,T1.SHIPPING_OBJECT_CN_NAME AS MADE_CN_NAME,';
      V_GROUP_TOTAL := 'SHIPPING_OBJECT_CODE AS GROUP_CODE,
                        SHIPPING_OBJECT_CN_NAME AS GROUP_CN_NAME, 
                        ''SHIPPING_OBJECT'' AS GROUP_LEVEL,';
      V_PARENT_CODE := 'LV0_PROD_RND_TEAM_CODE    AS PARENT_CODE,';
      V_PARENT_CN_NAME := 'LV0_PROD_RD_TEAM_CN_NAME    AS PARENT_CN_NAME,';
      V_GROUP_LEVEL := '''SHIPPING_OBJECT''';
      V_SHIPPING_TOTAL        := 'T1.SHIPPING_OBJECT_CODE,T1.SHIPPING_OBJECT_CN_NAME,';
      V_MANUFACTURE_TOTAL        := '';
        V_JOIN_PARA   := ' AND T1.SHIPPING_OBJECT_CODE   =  T2.SHIPPING_OBJECT_CODE';
    ELSIF LEV_NUM = 7 THEN
      -- ICT层级
      V_CHILD_LEVEL := '''SHIPPING_OBJECT''';
      V_MADE_TOTAL   := 'T1.LV0_PROD_RND_TEAM_CODE AS MADE_CODE,T1.LV0_PROD_RD_TEAM_CN_NAME AS MADE_CN_NAME,';
      V_GROUP_TOTAL := 'LV0_PROD_RND_TEAM_CODE AS GROUP_CODE,
                        LV0_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME, 
                       ''LV0'' AS GROUP_LEVEL,';
      V_PARENT_CODE := 'LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_PARENT_CN_NAME := 'LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      V_GROUP_LEVEL := '''LV0''';
      V_SHIPPING_TOTAL        := '';
      V_IN_MANUFACTURE_TOTAL = '';
      V_IN_SHIPPING_TOTAL = '';
      V_JOIN_PARA   := '';
    END IF;                                    
            
V_SQL := '        
INSERT INTO GROUP_AMP_TMP(
                 VIEW_FLAG,
                 PERIOD_YEAR ,
                 MADE_CODE ,
                 MADE_CN_NAME ,
                 GROUP_CODE ,
                 GROUP_CN_NAME ,
                 GROUP_LEVEL ,
                 PARENT_CODE ,
                 PARENT_CN_NAME,
                 LV0_PROD_RND_TEAM_CODE ,
                 LV0_PROD_RD_TEAM_CN_NAME ,
                 '||V_LV1_PROD_RND_TEAM 
                 ||V_LV2_PROD_RND_TEAM
                 ||V_LV3_PROD_RND_TEAM
                 ||V_LV4_PROD_RND_TEAM
                 ||V_IN_MANUFACTURE_TOTAL
                 ||V_IN_SHIPPING_TOTAL||'
                 ANNUAL_AMP ,
                 CALIBER_FLAG ,
                 OVERSEA_FLAG ,
                 LV0_PROD_LIST_CODE ,
                 LV0_PROD_LIST_CN_NAME )    

-- 子层级数据分视角按层级进行计算（关联单年权重表和涨跌幅临时表）
    WITH BY_YEAR_TMP AS(
         SELECT T2.VIEW_FLAG,
                T1.PERIOD_YEAR,
                T1.MADE_CODE ,
                T1.MADE_CN_NAME ,
                T1.GROUP_CODE ,
                T1.GROUP_CN_NAME ,
                T1.GROUP_LEVEL ,
                T1.PARENT_CODE ,
                T1.PARENT_CN_NAME,
                T1.LV0_PROD_RND_TEAM_CODE ,
                T1.LV0_PROD_RD_TEAM_CN_NAME ,
                '||V_LV1_PROD_RND_TEAM 
                 ||V_LV2_PROD_RND_TEAM
                 ||V_LV3_PROD_RND_TEAM
                 ||V_LV4_PROD_RND_TEAM
                 ||V_MANUFACTURE_TOTAL
                 ||V_SHIPPING_TOTAL||'
                T1.ANNUAL_AMP * T2.WEIGHT_RATE AS ANNUAL_AMP_WEIGHT,
                T1.PARENT_CODE,
                T1.CALIBER_FLAG,
                T1.OVERSEA_FLAG,
                T1.LV0_PROD_LIST_CODE,
                T1.LV0_PROD_LIST_CN_NAME
             FROM GROUP_AMP_TMP T1
             INNER JOIN '||V_FROM_WEIGHT_TABLE||' T2
             ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
             AND T1.GROUP_CODE =T2.GROUP_CODE
             AND NVL(T1.PARENT_CODE,''SNULL'') = NVL(T2.PARENT_CODE,''SNULL'')
             '||V_REL_VIEW_FLAG || V_REL_MADE_CODE||V_JOIN_PARA||'
             AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
             AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
             AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
             AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
             WHERE T2.VERSION_ID = '||V_VERSION_ID||'
             AND T1.GROUP_LEVEL = '||V_CHILD_LEVEL
             ||V_SQL_CONDITION||'
            )
 -- 子层级数据分视角往上卷积（按层级卷积上表计算结果）
        SELECT T1.VIEW_FLAG,
                T1.PERIOD_YEAR,
                '||V_MADE_TOTAL
                 ||V_GROUP_TOTAL
                 ||V_PARENT_CODE
                 ||V_PARENT_CN_NAME
                 ||'LV0_PROD_RND_TEAM_CODE ,
                 LV0_PROD_RD_TEAM_CN_NAME ,
                '||V_LV1_PROD_RND_TEAM 
                 ||V_LV2_PROD_RND_TEAM
                 ||V_LV3_PROD_RND_TEAM
                 ||V_LV4_PROD_RND_TEAM
                 ||V_MANUFACTURE_TOTAL
                 ||V_SHIPPING_TOTAL||'
                NVL(SUM(T1.ANNUAL_AMP_WEIGHT),0) AS ANNUAL_AMP,
                T1.CALIBER_FLAG,
                T1.OVERSEA_FLAG,
                T1.LV0_PROD_LIST_CODE,
                T1.LV0_PROD_LIST_CN_NAME
           FROM BY_YEAR_TMP T1
           GROUP BY T1.PERIOD_YEAR,
                    LV0_PROD_RND_TEAM_CODE ,
                    LV0_PROD_RD_TEAM_CN_NAME ,
                    '||V_LV1_PROD_RND_TEAM 
                     ||V_LV2_PROD_RND_TEAM
                     ||V_LV3_PROD_RND_TEAM
                     ||V_LV4_PROD_RND_TEAM
                     ||V_MANUFACTURE_TOTAL
                     ||V_SHIPPING_TOTAL||'
                     T1.VIEW_FLAG,
                     T1.CALIBER_FLAG,
                     T1.OVERSEA_FLAG,
                     T1.LV0_PROD_LIST_CODE,
                     T1.LV0_PROD_LIST_CN_NAME'
                      ;
                      
    DBMS_OUTPUT.PUT_LINE(V_SQL);      
    EXECUTE IMMEDIATE V_SQL; 
    DBMS_OUTPUT.PUT_LINE('涨跌幅数据计算成功');  
    
    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入产业标识为：'||F_INDUSTRY_FLAG||'，版本号为：'||V_VERSION_ID ||' 的第'||LEV_NUM||'次循环，数据插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');       
     
  -- 年度涨跌幅主逻辑SQL
     V_SQL := '
       INSERT INTO '||V_TO_AMP_TABLE||'(
               VERSION_ID,
               PERIOD_YEAR,
               MADE_CODE,
               MADE_CN_NAME,
               SHIPPING_OBJECT_CODE,
               SHIPPING_OBJECT_CN_NAME,
               MANUFACTURE_OBJECT_CODE,
               MANUFACTURE_OBJECT_CN_NAME,
               GROUP_CODE,
               GROUP_CN_NAME,
               GROUP_LEVEL,
               ANNUAL_AMP,
               PARENT_CODE,
               PARENT_CN_NAME,
               CREATED_BY,
               CREATION_DATE,
               LAST_UPDATED_BY,
               LAST_UPDATE_DATE,
               DEL_FLAG,
               VIEW_FLAG,
               CALIBER_FLAG,
               OVERSEA_FLAG,
               LV0_PROD_LIST_CODE,
               LV0_PROD_LIST_CN_NAME
              )
         SELECT '||V_VERSION_ID||' AS VERSION_ID,
            T1.PERIOD_YEAR,
            T1.MADE_CODE,
            T1.MADE_CN_NAME,
            T1.SHIPPING_OBJECT_CODE,
            T1.SHIPPING_OBJECT_CN_NAME,
            T1.MANUFACTURE_OBJECT_CODE,
            T1.MANUFACTURE_OBJECT_CN_NAME,
            T1.GROUP_CODE,
            T1.GROUP_CN_NAME,
            T1.GROUP_LEVEL,
            T1.ANNUAL_AMP,
            T1.PARENT_CODE,
            T1.PARENT_CN_NAME,
            -1 AS CREATED_BY,
            CURRENT_TIMESTAMP AS CREATION_DATE,
            -1 AS LAST_UPDATED_BY,
            CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
            ''N'' AS DEL_FLAG,
            T1.VIEW_FLAG,
            T1.CALIBER_FLAG,
            T1.OVERSEA_FLAG,
            T1.LV0_PROD_LIST_CODE,
            T1.LV0_PROD_LIST_CN_NAME
        FROM GROUP_AMP_TMP T1
        WHERE GROUP_LEVEL IN ( '||V_GROUP_LEVEL||' )';    
    DBMS_OUTPUT.PUT_LINE(V_SQL);      
    EXECUTE IMMEDIATE V_SQL; 
            
  --写入日志
   V_STEP_NUM:=V_STEP_NUM+1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '插入产业标识为：'||F_INDUSTRY_FLAG||'，版本号为：'||V_VERSION_ID ||' 的层级为：'||V_GROUP_LEVEL||'数据插入结果表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
    DBMS_OUTPUT.PUT_LINE('插入版本号为：'||V_VERSION_ID ||' 的层级为：'||V_GROUP_LEVEL||'数据插入结果表');    


 ------------------------------------------------------状态码计算逻辑--------------------------------------------------------------------    
    -- 状态码逻辑数据计算
 V_SQL := '
INSERT INTO '||V_TO_STATUS_TABLE||' (
    PERIOD_YEAR,
    VERSION_ID,
    MADE_CODE,
    MADE_CN_NAME,
    SHIPPING_OBJECT_CODE,
    SHIPPING_OBJECT_CN_NAME,
    MANUFACTURE_OBJECT_CODE,
    MANUFACTURE_OBJECT_CN_NAME,
    GROUP_CODE,
    GROUP_CN_NAME,
    GROUP_LEVEL,
    VIEW_FLAG,
    CALIBER_FLAG,
    OVERSEA_FLAG,
    LV0_PROD_LIST_CODE,
    LV0_PROD_LIST_CN_NAME,
    STATUS_CODE,
    PARENT_CODE,
    PARENT_CN_NAME,
    CREATED_BY,
    CREATION_DATE,
    LAST_UPDATED_BY,
    LAST_UPDATE_DATE,
    DEL_FLAG
    ) WITH ITEM_STATUS_TMP AS (
    SELECT
        T1.PERIOD_YEAR,
       '||V_MADE_TOTAL
        ||V_IN_SHIPPING_TOTAL
        ||V_IN_MANUFACTURE_TOTAL
        ||V_GROUP_TOTAL
        ||V_PARENT_CODE
        ||V_PARENT_CN_NAME||'
        T1.VIEW_FLAG,
        T1.CALIBER_FLAG,
        T1.OVERSEA_FLAG,
        T1.LV0_PROD_LIST_CODE,
        SUM ( CASE WHEN T1.STATUS_CODE IN ( 3, 5 ) THEN 1 ELSE 0 END ) AS STATUS_NORMAL,-- 值大于1，则赋3
        SUM ( DECODE( T1.STATUS_CODE, 1, 0, 1 ) ) AS STATUS_1,-- 值=0，说明子级都为1，赋1
        SUM ( DECODE( T1.STATUS_CODE, 4, 0, 1 ) ) AS STATUS_4 -- 值=0，说明子级都为4，赋2
    FROM
        MID_STATUS_TMP T1 
    GROUP BY
        '||V_LV1_PROD_RND_TEAM 
        ||V_LV2_PROD_RND_TEAM
        ||V_LV3_PROD_RND_TEAM
        ||V_LV4_PROD_RND_TEAM
        ||V_IN_MANUFACTURE_TOTAL
        ||V_IN_SHIPPING_TOTAL||'
        T1.VIEW_FLAG,
        T1.CALIBER_FLAG,
        T1.OVERSEA_FLAG,
        T1.LV0_PROD_LIST_CODE,
        T1.LV0_PROD_RND_TEAM_CODE,
        T1.LV0_PROD_RD_TEAM_CN_NAME,
        T1.PERIOD_YEAR 
    ) 
    
SELECT
    T1.PERIOD_YEAR,
    '||V_VERSION_ID||' AS VERSION_ID,
    T1.MADE_CODE,
    T1.MADE_CN_NAME,
    T1.SHIPPING_OBJECT_CODE,
    T1.SHIPPING_OBJECT_CN_NAME,
    T1.MANUFACTURE_OBJECT_CODE,
    T1.MANUFACTURE_OBJECT_CN_NAME,
    T1.GROUP_CODE,
    T1.GROUP_CN_NAME,
    T1.GROUP_LEVEL,
    T1.VIEW_FLAG,
    T1.CALIBER_FLAG,
    T1.OVERSEA_FLAG,
    T1.LV0_PROD_LIST_CODE,
    T1.LV0_PROD_LIST_CN_NAME,
    CASE
        WHEN T2.STATUS_NORMAL > 0 THEN
        3 
        WHEN T2.STATUS_1 = 0 THEN
        1 
        WHEN T2.STATUS_4 = 0 THEN
        2 ELSE 4 
    END AS STATUS_CODE,
    T1.PARENT_CODE,
    T1.PARENT_CN_NAME,
    - 1 AS CREATED_BY,
    CURRENT_TIMESTAMP AS CREATION_DATE,
    - 1 AS LAST_UPDATED_BY,
    CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
    ''N'' AS DEL_FLAG
FROM
    '||V_TO_AMP_TABLE||' T1
    LEFT JOIN ITEM_STATUS_TMP T2
    ON T1.PERIOD_YEAR = T2.PERIOD_YEAR 
    AND NVL ( T1.GROUP_CODE, ''SNULL'' ) = NVL ( T2.GROUP_CODE, ''SNULL'' ) 
    AND NVL ( T1.PARENT_CODE, ''SNULL'' ) = NVL ( T2.PARENT_CODE, ''SNULL'' ) 
    AND NVL ( T1.MADE_CODE, ''SNULL'' ) = NVL ( T2.MADE_CODE, ''SNULL'' )'
    ||V_JOIN_PARA||'
    AND NVL ( T1.VIEW_FLAG, ''SNULL'' ) = NVL ( T2.VIEW_FLAG, ''SNULL'' ) 
    AND NVL ( T1.CALIBER_FLAG, ''SNULL'' ) = NVL ( T2.CALIBER_FLAG, ''SNULL'' ) 
    AND NVL ( T1.OVERSEA_FLAG, ''SNULL'' ) = NVL ( T2.OVERSEA_FLAG, ''SNULL'' ) 
    AND NVL ( T1.LV0_PROD_LIST_CODE, ''SNULL'' ) = NVL ( T2.LV0_PROD_LIST_CODE, ''SNULL'' ) 
WHERE
    T1.GROUP_LEVEL IN ( '||V_GROUP_LEVEL||' ) 
    AND T1.VERSION_ID = '||V_VERSION_ID;
    
      DBMS_OUTPUT.PUT_LINE(V_SQL);      
    EXECUTE IMMEDIATE V_SQL; 
    DBMS_OUTPUT.PUT_LINE('状态码数据计算成功'); 
     

 --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '产业标识为：'||F_INDUSTRY_FLAG||'，层级为：'||V_GROUP_LEVEL||' 的状态码数据插入结果表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');                     
        
  END LOOP;   -- 结束循环    
  
 V_SQL := '
      ANALYSE '||V_TO_AMP_TABLE||';
      ANALYSE '||V_TO_STATUS_TABLE||';';
    EXECUTE IMMEDIATE V_SQL; 
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集统计信息完成!');
 
  RETURN 'SUCCESS';
 
 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

END$$
/

