-- Name: f_dm_foc_data_primary_encrypt_t_quarter_merge; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_data_primary_encrypt_t_quarter_merge(OUT x_result_status text)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
创建时间：2023-8-15
创建人  ：李志勇 00808731 
背景描述：发货时点DATA加解密按照季度集成后,然后调用该函数将所有季度表数据合并到目标表
参数描述：参数一(p_period)：传入会计期（年月）,改成全量新增，所以不需要会计期参数
		  参数二(x_result_status):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_foc_data_primary_encrypt_t_quarter_merge();

*/

declare
	v_sp_name varchar(100) := 'fin_dm_opt_foi.f_dm_foc_data_primary_encrypt_t_quarter_merge';
	v_tbl_name varchar(100) := 'fin_dm_opt_foi.dm_foc_data_primary_encrypt_t';     ----发货明细DATA加解密后的表
	v_dml_row_count  number default 0 ;
	V_FROM_TABLE varchar(200) := ' fin_dm_opt_foi.dm_foc_data_primary_encrypt_t_';
	V_SQL TEXT;
	V_SQL_TAIL TEXT; --tt_202101_202103  V_SQL_TAIL用于标识202103
	V_PERIOD_ID BIGINT;
	V_PERIOD_ID_TAIL  BIGINT;
	V_NUM BIGINT := -31;  --用于标识 202101
	V_STEP_MUM BIGINT := 0;


begin
	x_result_status := 'SUCCESS';       --1表示成功
	

	 --写日志,开始
	insert into fin_dm_opt_foi.dm_foi_log_t
		(log_id,
		 version_id,
		 sp_name,
		 para_list,
		 step_num,
		 cal_log_desc,
		 formula_sql_txt,
		 dml_row_count	,
		 result_status,
		 errbuf,
		 created_by,
		 creation_date)
	values
		(fin_dm_opt_foi.dm_foi_log_s.nextval,
		 null,
		 v_sp_name,
		 '',
		 1,                                             --第一步
		 '发货时点DATA加解密季度表合并到'||v_tbl_name||'：开始运行',
		 null,
		 v_dml_row_count,
		 x_result_status,
		 null,
		 1,
		 current_timestamp);


		---支持重跑，清除目标表要插入会计期的数据
-- 		delete from fin_dm_opt_foi.DWL_PROD_BOM_ITEM_REV_DETAIL_TEMP 
-- 		where period_id in (select distinct period_id from fin_dm_opt_foi.DWL_PROD_BOM_ITEM_SHIP_DTL_I_TEMP);    ---temp表的日期 
			
		FOR NUM_FLAG IN 0 .. 9 LOOP
			V_SQL := 'SELECT TO_CHAR(ADD_MONTHS(CURRENT_DATE,'||V_NUM||'),''YYYYMM'')';  --ADD_MONTHS 增加
			V_SQL_TAIL := 'SELECT TO_CHAR(ADD_MONTHS(CURRENT_DATE,'||V_NUM + 2||'),''YYYYMM'')';  --ADD_MONTHS 增加
			EXECUTE IMMEDIATE V_SQL INTO V_PERIOD_ID; 
			EXECUTE IMMEDIATE V_SQL_TAIL INTO V_PERIOD_ID_TAIL; 
			
			
		---插入目标表数据
--		V_SQL := '
--		insert into fin_dm_opt_foi.DM_FOC_DATA_PRIMARY_ENCRYPT_T         
--		(
--			primary_id ,
--			period_id,
--			rmb_cost_amt,
--			creation_date ,
--			last_update_date
--		)
--		select 
--			primary_id ,
--			period_id,
--			rmb_cost_amt,
--			creation_date ,
--			last_update_date	
--	   from '||V_FROM_TABLE||V_PERIOD_ID||'_'||V_PERIOD_ID_TAIL;

		---插入目标表数据 -全部插入，仅表字段完全一致时使用
		V_SQL := '
		insert into fin_dm_opt_foi.DM_FOC_DATA_PRIMARY_ENCRYPT_T 
		select *	
	   from '||V_FROM_TABLE||V_PERIOD_ID||'_'||V_PERIOD_ID_TAIL;
 
		EXECUTE IMMEDIATE V_SQL;
		DBMS_OUTPUT.PUT_LINE(V_SQL);
		
		V_STEP_MUM := V_STEP_MUM + 1;
	PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
	(F_SP_NAME => V_SP_NAME,
		F_STEP_NUM =>  V_STEP_MUM,
		F_CAL_LOG_DESC => '将表'||V_FROM_TABLE||V_PERIOD_ID||'_'||V_PERIOD_ID_TAIL||'的数据插入表中',
		F_DML_ROW_COUNT => SQL%ROWCOUNT,
		F_ERRBUF => 'SUCCESS');  
		
		V_NUM := V_NUM +3 ;
		
	
	END LOOP;

	 -- 写结束日志
	insert into fin_dm_opt_foi.dm_foi_log_t
		(log_id,
		version_id,
		sp_name,
		para_list,
		step_num,
		cal_log_desc,
		formula_sql_txt,
		dml_row_count,
		result_status,
		errbuf,
		created_by,
		creation_date)
	values
		(
		fin_dm_opt_foi.dm_foi_log_s.nextval,
		null,
		v_sp_name,
		'',
		2,                                             --最后一步
		'发货时点DATA加解密季度表合并到'||v_tbl_name||'：结束运行',
		null,
		v_dml_row_count,
		'1',
		null,
		1,
		current_timestamp);
		
		return 'SUCCESS';

exception
  	when others then

	PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
	(F_SP_NAME => V_SP_NAME, 
	F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
	F_RESULT_STATUS => X_RESULT_STATUS, 
	F_ERRBUF => SQLSTATE||':'||SQLERRM
	);
	  
	x_result_status := '2001';       --fail表示失败
	
    --收集统计信息
    analyse fin_dm_opt_foi.dm_foc_data_primary_encrypt_t;	

end;
$$
/

