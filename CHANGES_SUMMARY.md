# SQL文件拆分总结

## 概述
根据要求，将原始的 `f_dm_foc_annual_amp_t_dms.sql` 文件中的 `F_INDUSTRY_FLAG = 'I'` 条件剥离出来，形成两个独立的SQL文件。

## 文件变更

### 1. 新建文件：`f_dm_foc_annual_amp_t_dms_ict.sql`
- **功能**：专门处理ICT产业项目的分视角年度涨跌幅计算
- **函数名**：`fin_dm_opt_foi.f_dm_foc_annual_amp_t_dms_ict`
- **参数变更**：移除了 `f_industry_flag` 参数，因为该文件专门处理ICT场景
- **主要特点**：
  - 硬编码了ICT相关的表名和配置
  - 移除了产业类型判断逻辑
  - 保留了完整的ICT业务逻辑

### 2. 修改文件：`f_dm_foc_annual_amp_t_dms.sql`
- **功能**：现在只处理数字能源(E)和IAS产业项目
- **主要变更**：
  - 移除了所有 `F_INDUSTRY_FLAG = 'I'` 相关的条件判断
  - 更新了注释，说明现在只支持数字能源和IAS
  - 保持了原有的函数签名和参数

## 具体变更内容

### ICT专用文件的关键配置
```sql
-- ICT产业项目配置
V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T';
V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_DMS_VIEW_ANNL_COST_T_DMS';
V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_DMS_ANNUAL_WEIGHT_T';
V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_ANNUAL_AMP_T';
V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_GROUP_AMP_T_DMS';
V_TMP_TABLE := 'DMS_DECRYPT_AVG_TMP';
```

### 移除的ICT逻辑
1. 产业类型判断中的ICT分支
2. 循环逻辑中ICT特定的父层级处理
3. ICT特定的GROUP层级逻辑

## 使用方式

### ICT场景
```sql
SELECT FIN_DM_OPT_FOI.F_DM_FOC_ANNUAL_AMP_T_DMS_ICT(
    f_cost_type => 'P',
    f_dimension_type => 'D',
    f_view_flag => NULL,
    f_keystr => NULL,
    f_version_id => NULL
);
```

### 数字能源/IAS场景
```sql
SELECT FIN_DM_OPT_FOI.F_DM_FOC_ANNUAL_AMP_T_DMS(
    f_industry_flag => 'E',  -- 或 'IAS'
    f_cost_type => 'P',
    f_dimension_type => 'D',
    f_view_flag => NULL,
    f_keystr => NULL,
    f_version_id => NULL
);
```

## NVL条件优化 (2024年更新)

### 背景
将所有关联条件中的NVL语句优化为更标准的NULL处理方式，提高SQL执行效率。

### 变更内容
将形如：
```sql
AND NVL(T1.LV0_PROD_RND_TEAM_CODE,'SNULL0') = NVL(T2.LV0_PROD_RND_TEAM_CODE,'SNULL0')
```

转换为：
```sql
AND ((T1.LV0_PROD_RND_TEAM_CODE = T2.LV0_PROD_RND_TEAM_CODE)
OR (T1.LV0_PROD_RND_TEAM_CODE IS NULL AND T2.LV0_PROD_RND_TEAM_CODE IS NULL))
```

### 涉及的字段
- LV0_PROD_RND_TEAM_CODE
- LV1_PROD_RND_TEAM_CODE
- LV2_PROD_RND_TEAM_CODE
- LV3_PROD_RND_TEAM_CODE
- LV4_PROD_RND_TEAM_CODE (仅IAS)
- DIMENSION_CODE
- DIMENSION_SUBCATEGORY_CODE
- DIMENSION_SUB_DETAIL_CODE
- SPART_CODE
- COA_CODE (仅数字能源)
- PARENT_CODE

### 优势
- 避免了NVL函数的性能开销
- 更符合标准SQL语法
- 提高了查询执行效率
- 更清晰地表达NULL值处理逻辑

## WITH语句改造为临时表 (2024年更新)

### 背景
将WITH语句的缓存表改成CREATE TEMPORARY TABLE + INSERT的方式，提高SQL执行的稳定性和可维护性。

### 改造内容

#### ICT文件改造完成
1. **BY_YEAR_AVG_TMP临时表**：
   - 用于存储按年份行转列的年均本数据
   - 包含LAST_THREE_YEAR_AVG、LAST_TWO_YEAR_AVG、LAST_YEAR_AVG、YEAR_AVG字段

2. **YEAR_ANNUAL_AMP_TMP临时表**：
   - 用于存储年度涨跌幅数据
   - 通过三个年份的UNION ALL计算涨跌幅

3. **BY_YEAR_TMP临时表**：
   - 用于循环中的子层级数据计算
   - 关联单年权重表和涨跌幅临时表

#### 改造优势
- **稳定性提升**：避免复杂WITH语句可能导致的执行计划问题
- **可维护性**：每个步骤独立，便于调试和优化
- **性能优化**：临时表可以创建索引，提高查询效率
- **内存管理**：更好的内存使用控制

#### 临时表命名规范
- ICT文件：`*_ICT`后缀
- 主文件：`*_MAIN`后缀（待完成）

### 待完成工作
- 原文件（数字能源/IAS）的WITH语句改造

## 验证
- ICT文件已完成WITH语句改造并通过语法检查
- 保持了原有的业务逻辑完整性
- ICT文件移除了不必要的产业类型判断，提高了执行效率
- 所有NVL条件已成功转换为标准NULL处理方式
