-- Name: f_dm_fol_air_actual_perform_income_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fol_air_actual_perform_income_info_t(p_version_id integer DEFAULT NULL::integer, p_refresh_type character varying DEFAULT NULL::character varying, p_version_code character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建人  ：zwx1275798
创建时间：2024年8月27日
背景描述：物流空运实际履行收益表         
参数描述： p_version_id   逻辑：1、自动调度，取航线量汇总表的最大版本ID；2、刷新（页面的刷新价格表）：取java传版本ID；
           p_version_code 逻辑：1、自动调度，取价格补录头表的最大版本code；2、刷新（页面的刷新价格表）：取java传版本code；
           p_refresh_type 逻辑：1、自动调度，直接赋值为 4_AUTO；2、刷新（页面的刷新价格表）：取java传的值（1_刷新价格表 ）
		   x_success_flag:返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_fol_air_actual_perform_income_info_t()
变更记录：202503 zwx1275798 代码优化(代码由2738行缩减至2097行)：
                                         1、将所有with as 临时表修改为temporary会话临时表
                                         2、temporary会话临时表的表结构创建语句删除，将insert temporary语句修改为创建temporary表的逻辑语句
										 3、将各层级成本、货量、收益，需多次计算的逻辑进行整合，精简逻辑，去除冗余代码
*/


declare
	v_sp_name varchar(200) := 'fin_dm_opt_foi.f_dm_fol_air_actual_perform_income_info_t('||p_version_id||','''||p_refresh_type||''')';
	v_tbl_name varchar(200) := 'fin_dm_opt_foi.dm_fol_air_actual_perform_income_info_t';
	v_dml_row_count  int default 0 ;
	v_max_version_id int;
	v_price_version_code varchar(30);

begin

  set enable_force_vector_engine to on;
	x_success_flag := '1';        --1表示成功

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '物流空运实际履行收益表 '||v_tbl_name||'：开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	  
    --从 物流空运实际履行收益表 中，删除版本信息表中执行失败的 version_id 数据
        delete from fin_dm_opt_foi.dm_fol_air_actual_perform_income_info_t t1 
		where t1.version_id in (select distinct t2.version_id from fin_dm_opt_foi.dm_fol_air_version_info_t t2 where  t2.step='2001');
		
  -- 如果是传version_id调函数取JAVA传入的p_version_id，如果是自动调度的则取航线量汇总表的最大版本ID
        if p_version_id is not null then 
        select  p_version_id into v_max_version_id ;
        else 
        select max(version_id) as max_version_id into v_max_version_id       
		from fin_dm_opt_foi.dm_fol_air_route_info_sum_t;	
        end if 
        ;		

        -- 如果p_version_code为空，则取 物流空运价格补录表头表中 的最大版本编码，如果p_version_code不为空，则取传入的p_version_code   
		if (p_version_code is null or p_version_code = '') then
        select max(version_code) as max_version_code into v_price_version_code 
		from fin_dm_opt_foi.apd_fol_air_route_price_heaer_t 
		where upper(status)='FINAL';
		else 
		select  p_version_code into v_price_version_code ;
		end if
          ; 

   -- 清理已经写入版本信息表的数据
         delete from fin_dm_opt_foi.dm_fol_air_version_info_t 
		 where version_id = v_max_version_id 
		 and step = 2 
		 and source_en_name = 'f_dm_fol_air_actual_perform_income_info_t'
		 and refresh_type = nvl(p_refresh_type,'4_AUTO');

    -- 成功数据写入到版本信息表
    insert into fin_dm_opt_foi.dm_fol_air_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
		 , transport_mode       -- 运输方式（精品空运、精品海运）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )    
    select v_max_version_id   as version_id
         , v_price_version_code as version_code
         , 2 as step
         , 'f_dm_fol_air_actual_perform_income_info_t' as source_en_name
         , '物流空运实际履行收益表函数' as source_cn_name
         ,  nvl(p_refresh_type,'4_AUTO') as refresh_type
		 , '精品空运' as transport_mode
         , 'version_code 是物流航线价格补录表的版本编码' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
    ;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => ' 版本ID：'||v_max_version_id||'，价格补录表的版本编码：'||v_price_version_code||'，写入到版本信息表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
  
         --从 航线量汇总表 取出 version_id 为最大版本
        drop table if exists air_income_route_tmp;
		create temporary table air_income_route_tmp
		      as
		select  version_id
		       ,year
	           ,period_id
			   ,active_period
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name
               ,dest_port_name
	           ,dest_country_name
			   ,price_id 
			   ,supplier_short_name
			   ,currency
			   ,price
			   ,container_qty      
			   ,Huawei_group    
			   ,service_level   
			   ,is_high_quality              	   
		from  fin_dm_opt_foi.dm_fol_air_route_info_sum_t
		where 1=1
		  and version_id = v_max_version_id			
			   ;
			   			   
			    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => 'air_income_route_tmp 从 航线量汇总表 取出 version_id 为最大版本'||v_max_version_id||',数据量为'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
		
			   -- 航线层级当日货量
			 drop table if exists air_income_qty_tmp;
		     create temporary table air_income_qty_tmp
		      as
		    select  version_id
		       ,year
	           ,period_id
			   ,active_period
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name
               ,dest_port_name
	           ,dest_country_name
			   ,currency
			   ,sum(container_qty) as container_qty       
			   ,Huawei_group    
			   ,service_level   
			   ,is_high_quality
		from  air_income_route_tmp
		where container_qty is not null
		group by  version_id
		       , year
	           , period_id
			   , active_period
			   , transport_mode
			   , region_cn_name
			   , route
			   ,source_port_name
               ,dest_port_name
	           ,dest_country_name
			   , currency      
			   , Huawei_group    
			   , service_level   
			   , is_high_quality 
			   ;
			   
			    v_dml_row_count := sql%rowcount;	-- 收集数据量

          -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 4,
        p_log_cal_log_desc => 'air_income_qty_tmp 航线层级月度货量'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
	   
	       -- 取初始使用比例补录表的定稿版本
            drop table if exists price_ratio_info_tmp;
		     create temporary table price_ratio_info_tmp
		      as
            select price_id
				  ,region_cn_name          -- 区域
				  ,source_port_name
				  ,dest_port_name
                  ,source_port_name||'-'||dest_port_name as route -- 目的港
                  ,supplier_short_name     -- LST（即供应商）
                  ,to_char(begin_date,'yyyymmdd')as begin_date  -- 开始时间
                  ,to_char(end_date,'yyyymmdd')  as end_date    -- 结束时间
                  ,container_qty           -- 货量
				  ,Huawei_group         -- 华为分组
            from fin_dm_opt_foi.apd_fol_air_price_ratio_info_t
            where upper(status) = 'FINAL'  -- 只取终稿的
               ;
			   
			-- 从20200101至数据最大会计期的所有年月日
			drop table if exists time_tmp;
		     create temporary table time_tmp
		      as
            select to_char(generate_series,'YYYYMMDD')  as active_period
            from generate_series('2020-01-01':: timestamp ,CURRENT_DATE::timestamp, '1 day')
            where to_char(generate_series,'YYYYMM') <= (select max(period_id)  from air_income_route_tmp)
              ;
			   
			-- 计算供应商有效期的天数
			drop table if exists days_tmp;
		     create temporary table days_tmp
		      as
			   select t1.price_id
				    , t1.region_cn_name          -- 区域
				    , t1.source_port_name
					, t1.dest_port_name
                    , t1.source_port_name||'-'||t1.dest_port_name as route -- 目的港
                    , t1.supplier_short_name     -- LST（即供应商）
                    , t2.active_period        -- 日粒度有效期
					, t1.Huawei_group         -- 华为分组
					, count(t2.active_period) over (partition by t1.price_id
					                                            ,t1.region_cn_name
															   ,t1.source_port_name
															   ,t1.dest_port_name
															   ,t1.supplier_short_name
															   ,t1.Huawei_group) as cn
				from price_ratio_info_tmp t1
				left join time_tmp t2
			    on t2.active_period >=t1.begin_date
			   and t2.active_period <=t1.end_date
				;		   
			   
			   -- 将初始比例转化为日颗粒度，用有效期内的货量除以天数，得到日均货量
			   drop table if exists day_avg_tmp;
		     create temporary table day_avg_tmp
		      as
			 select      t1.price_id
				       , t1.region_cn_name          -- 区域
				       , t1.source_port_name
					   , t1.dest_port_name
                       , t1.source_port_name||'-'||t1.dest_port_name as route -- 目的港
                       , t1.supplier_short_name     -- LST（即供应商）
                       , t2.active_period           --日颗粒度的有效期
                       , t1.container_qty / t2.cn  as container_qty  -- 日均货量
					   , t1.Huawei_group            -- 华为分组
			from price_ratio_info_tmp t1
			left join days_tmp t2
			 on t2.active_period >=t1.begin_date
			and t2.active_period <=t1.end_date
			and t1.price_id = t2.price_id
			and t1.region_cn_name = t2.region_cn_name
			and t1.source_port_name = t2.source_port_name
			and t1.dest_port_name = t2.dest_port_name
			and t1.supplier_short_name = t2.supplier_short_name
			and t1.Huawei_group = t2.Huawei_group
			  ;
			  
			  --计算航线下所有供应商的日货量
			  drop table if exists route_day_qty;
		     create temporary table route_day_qty
		      as
		    select region_cn_name          -- 区域
				 , source_port_name
				 , dest_port_name
                 , route -- 目的港
                 , active_period
                 , sum(container_qty)  as container_qty -- 货量
				 , Huawei_group         -- 华为分组
              from day_avg_tmp
			  group by region_cn_name          -- 区域
			     , source_port_name
			     , dest_port_name
                 , route -- 目的港
                 , active_period
			     , Huawei_group         -- 华为分组
			       ;
				   
			 -- 计算供应商下区分价格ID的日粒度的定标比例：日均货量/航线下所有供应商的日货量
		     drop table if exists ratio_day_tmp;
		     create temporary table ratio_day_tmp
		      as
			  select     t1.price_id
				       , t1.region_cn_name          -- 区域
				       , t1.source_port_name
					   , t1.dest_port_name
                       , t1.route -- 目的港
                       , t1.supplier_short_name     -- LST（即供应商）
                       , t1.active_period           --日颗粒度的有效期
                       , t1.container_qty / t2.container_qty  as init_ratio  -- 定标比例
					   , t1.Huawei_group            -- 华为分组
			from day_avg_tmp t1
			left join route_day_qty t2
			 on t1.active_period =t2.active_period
			and t1.region_cn_name = t2.region_cn_name
			and t1.source_port_name = t2.source_port_name
			and t1.dest_port_name = t2.dest_port_name
			and t1.Huawei_group = t2.Huawei_group
			 ;
			 
			  -- 对于货代/精品的分别计算时，需要折算对应的定标比例，先将所有供应商的比例加起来（区分精品）
			  drop table if exists price_ratio_info_tmp1;
		     create temporary table price_ratio_info_tmp1
		      as
		   select  t1.version_id
		       ,t1.year
	           ,t1.period_id
			   ,t1.active_period
			   ,t1.transport_mode
			   ,t1.region_cn_name
			   ,t1.route
			   ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name
			   ,t1.currency  
               ,sum(t2.init_ratio)  as	all_init_ratio			   
			   ,t1.Huawei_group    
			   ,t1.service_level   
			   ,t1.is_high_quality 	   
		from   air_income_route_tmp t1
		left join ratio_day_tmp t2
		  on t1.price_id = t2.price_id
		 and t1.region_cn_name = t2.region_cn_name
		 and t1.route = t2.route
		 and t1.source_port_name = t2.source_port_name
		 and t1.dest_port_name = t2.dest_port_name
		 and t1.supplier_short_name = t2.supplier_short_name
		 and t1.Huawei_group = t2.Huawei_group 
		 and t1.active_period = t2.active_period 
		 where 1=1
		 and t1.transport_mode = '精品空运'
		 group by t1.version_id
		       ,t1.year
	           ,t1.period_id
			   ,t1.active_period
			   ,t1.transport_mode
			   ,t1.region_cn_name
			   ,t1.route
			   ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name
			   ,t1.currency 		   
			   ,t1.Huawei_group    
			   ,t1.service_level   
			   ,t1.is_high_quality 
		        ;
			   
		-- 航线层级当日的加权定标价：卷积到航线
		   drop table if exists air_iratio_price_tmp;
		   create temporary table air_iratio_price_tmp
		      as
		   -- 计算当日的加权定标价 posted_income_amt1 作为后面ALL（精品+货代）的值
            select  t1.version_id
		       ,t1.year
	           ,t1.period_id
			   ,t1.active_period
			   ,t1.transport_mode
			   ,t1.region_cn_name
			   ,t1.route
			   ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name
			   ,t1.currency  
               ,round(sum(t1.price*t2.init_ratio),10) as posted_income_amt1  -- 加权定标价(用于ALL（精品+货代）计算的值)	
               ,null as posted_income_amt2			   
			   ,t1.Huawei_group    
			   ,t1.service_level   
			   ,t1.is_high_quality 	   
		from   air_income_route_tmp t1
		left join ratio_day_tmp t2
		  on t1.price_id = t2.price_id
		 and t1.region_cn_name = t2.region_cn_name
		 and t1.route = t2.route
		 and t1.source_port_name = t2.source_port_name
		 and t1.dest_port_name = t2.dest_port_name
		 and t1.supplier_short_name = t2.supplier_short_name
		 and t1.Huawei_group = t2.Huawei_group
		 and t1.active_period = t2.active_period
		 where 1=1
		 and t1.transport_mode = '精品空运'
		 group by t1.version_id
		       ,t1.year
	           ,t1.period_id
			   ,t1.active_period
			   ,t1.transport_mode
			   ,t1.region_cn_name
			   ,t1.route
			   ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name
			   ,t1.currency      
			   ,t1.Huawei_group    
			   ,t1.service_level   
			   ,t1.is_high_quality 
			   union all
	 -- 计算当日的加权定标价 posted_income_amt2 作为 货代/精品的分别计算的值：单个供应商的定标比例*所有供应商的定标比例*价格
			   select  t1.version_id
		       ,t1.year
	           ,t1.period_id
			   ,t1.active_period
			   ,t1.transport_mode
			   ,t1.region_cn_name
			   ,t1.route
			   ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name
			   ,t1.currency
               ,null as posted_income_amt1			   
               ,round(sum(t1.price*(t2.init_ratio/t3.all_init_ratio)),10)as posted_income_amt2  -- 加权定标价(用于货代/精品的分别计算时的值)				   
			   ,t1.Huawei_group    
			   ,t1.service_level   
			   ,t1.is_high_quality 	   
		from   air_income_route_tmp t1
		left join ratio_day_tmp t2
		  on t1.price_id = t2.price_id
		 and t1.region_cn_name = t2.region_cn_name
		 and t1.route = t2.route
		 and t1.source_port_name = t2.source_port_name
		 and t1.dest_port_name = t2.dest_port_name
		 and t1.supplier_short_name = t2.supplier_short_name
		 and t1.Huawei_group = t2.Huawei_group
		 and t1.active_period = t2.active_period
		 left join price_ratio_info_tmp1 t3
		  on t1.region_cn_name = t3.region_cn_name
		 and t1.route = t3.route
		 and t1.source_port_name = t3.source_port_name
		 and t1.dest_port_name = t3.dest_port_name
		 and t1.Huawei_group = t3.Huawei_group
		 and t1.active_period = t3.active_period     
		 where 1=1
		   and t1.transport_mode = '精品空运'
		 group by t1.version_id
		       ,t1.year
	           ,t1.period_id
			   ,t1.active_period
			   ,t1.transport_mode
			   ,t1.region_cn_name
			   ,t1.route
			   ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name
			   ,t1.currency      
			   ,t1.Huawei_group    
			   ,t1.service_level   
			   ,t1.is_high_quality   
		 ;
		 
		   v_dml_row_count := sql%rowcount;	-- 收集数据量
		 
		 -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 5,
        p_log_cal_log_desc => 'air_iratio_price_tmp 航线层级月度的加权定标价'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
			 
		 -- 航线层级的月度定标成本
		  drop table if exists air_month_income_cost_tmp;
		   create temporary table air_month_income_cost_tmp
		      as		
			-- 区分精品货代
		    select t1.version_id
               ,t1.year				
		       ,t1.period_id          
		       ,t1.transport_mode     
		       ,t1.region_cn_name     
		       ,t1.route              
		       ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name 			     
		       ,t1.currency 
               ,null as hw_cost_amt   
			   ,null as tac_cost_amt			   
       		   ,round(sum(t1.posted_income_amt2*t2.container_qty),10) as  posted_cost_amt
               ,null as income_tac_amt			   
			   ,null as income_ratio_amt
			   ,t1.Huawei_group    
               ,t1.service_level   
			   ,t1.is_high_quality 
			from air_iratio_price_tmp t1
            left join air_income_qty_tmp t2
			  on t1.version_id = t2.version_id
		     and t1.active_period  = t2.active_period
		     and t1.transport_mode = t2.transport_mode
		     and t1.region_cn_name = t2.region_cn_name
		     and t1.route=t2.route
		     and t1.currency = t2.currency 
		     and t1.Huawei_group = t2.Huawei_group
		     and t1.service_level = t2.service_level
		     and t1.is_high_quality = t2.is_high_quality	
             where t2.container_qty is not null		
               and t2.transport_mode = '精品空运'			 
			 group by t1.version_id
               ,t1.year				
		       ,t1.period_id          
		       ,t1.transport_mode     
		       ,t1.region_cn_name     
		       ,t1.route              
		       ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name 			     
		       ,t1.currency 
			   ,t1.Huawei_group    
               ,t1.service_level   
			   ,t1.is_high_quality
			 union all
			 -- 不区分精品、货代
			 select t1.version_id
               ,t1.year				
		       ,t1.period_id          
		       ,t1.transport_mode     
		       ,t1.region_cn_name     
		       ,t1.route              
		       ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name			     
		       ,t1.currency 
               ,null as hw_cost_amt   
			   ,null as tac_cost_amt			   
       		   ,round(sum(t1.posted_income_amt1*t2.container_qty),10) as  posted_cost_amt   
			   ,null as income_tac_amt	
			   ,null as income_ratio_amt
			   ,t1.Huawei_group    
               ,t1.service_level   
			   ,'ALL' as is_high_quality 
			from air_iratio_price_tmp t1
            left join air_income_qty_tmp t2
			  on t1.version_id = t2.version_id
		     and t1.active_period  = t2.active_period
		     and t1.transport_mode = t2.transport_mode
		     and t1.region_cn_name = t2.region_cn_name
		     and t1.route=t2.route
		     and t1.currency = t2.currency 
		     and t1.Huawei_group = t2.Huawei_group
		     and t1.service_level = t2.service_level
		     and t1.is_high_quality = t2.is_high_quality	
             where t2.container_qty is not null		
             and t2.transport_mode = '精品空运'				 
			 group by t1.version_id
               ,t1.year				
		       ,t1.period_id          
		       ,t1.transport_mode     
		       ,t1.region_cn_name     
		       ,t1.route              
		       ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name 			     
		       ,t1.currency 
			   ,t1.Huawei_group    
               ,t1.service_level   
		     ; 
			 
			   v_dml_row_count := sql%rowcount;	-- 收集数据量
			 
			 -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 6,
        p_log_cal_log_desc => 'air_month_income_cost_tmp 航线层级的月度定标成本'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;	
	
	           -- 计算华为成本和TAC成本：卷积到航线层级【卷积到月（当日的价*量）】
			   drop table if exists hw_tac_cost_tmp;
		   create temporary table hw_tac_cost_tmp
		      as
			 select  version_id
		       ,year
	           ,period_id
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name
               ,dest_port_name
	           ,dest_country_name
			   ,currency
			   ,sum(price*container_qty) as amt    
			   ,Huawei_group    
			   ,service_level   
			   ,is_high_quality            
		  from  air_income_route_tmp
		  where container_qty is not null
		  group by version_id
		       ,year
	           ,period_id
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name
               ,dest_port_name
	           ,dest_country_name
			   ,currency    
			   ,Huawei_group    
			   ,service_level   
			   ,is_high_quality  
                  ;
			 
			 -- 将华为成本和TAC成本行专列
			  insert into air_month_income_cost_tmp(
                version_id
               ,year				
		       ,period_id          
		       ,transport_mode     
		       ,region_cn_name     
		       ,route              
		       ,source_port_name
               ,dest_port_name
	           ,dest_country_name			     
		       ,currency  			   
       		   ,hw_cost_amt   
			   ,tac_cost_amt
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
                )					
				-- 区分精品、货代			   
              select version_id
		       ,year
	           ,period_id
			   ,'精品空运' as transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name
               ,dest_port_name
	           ,dest_country_name
			   ,currency
			   ,sum(case when transport_mode = '精品空运'
			         then amt 
					 else null 
					 end) as hw_cost_amt
			    ,sum(case when transport_mode = 'TAC'
			         then amt
                     else null					 
					 end) as tac_cost_amt
			   ,Huawei_group    
			   ,service_level   
			   ,is_high_quality      
              from 	hw_tac_cost_tmp
			  group by version_id
		       ,year
	           ,period_id
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name
               ,dest_port_name
	           ,dest_country_name
			   ,currency
			   ,Huawei_group    
			   ,service_level   
			   ,is_high_quality
			   union all
			   -- 将华为成本和TAC成本行专列, 不区分精品、货代（ALL）	
			   select version_id
		       ,year
	           ,period_id
			   ,'精品空运' as transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name
               ,dest_port_name
	           ,dest_country_name
			   ,currency 
			   ,sum(case when transport_mode = '精品空运'
			         then amt
                     else null					 
					 end) as hw_cost_amt
			    ,sum(case when transport_mode = 'TAC'
			         then amt
                     else null					 
					 end) as tac_cost_amt
			   ,Huawei_group    
			   ,service_level   
			   ,'ALL' as is_high_quality      
              from 	hw_tac_cost_tmp
               group by version_id
		       ,year
	           ,period_id
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name
               ,dest_port_name
	           ,dest_country_name
			   ,currency    
			   ,Huawei_group    
			   ,service_level   
			   		 ;		

           v_dml_row_count := sql%rowcount;	-- 收集数据量
		   
                -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 7,
        p_log_cal_log_desc => 'air_month_income_cost_tmp 航线层级月度的华为成本和TAC成本'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;					 
					 
				-- 取出华为成本	
				drop table if exists hw_cost_tmp;
		   create temporary table hw_cost_tmp
		      as
        select version_id
               ,year				
		       ,period_id          
		       ,transport_mode     
		       ,region_cn_name     
		       ,route              
		       ,source_port_name
               ,dest_port_name
	           ,dest_country_name			     
		       ,currency    		   
       		   ,hw_cost_amt   
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
		from air_month_income_cost_tmp
		where hw_cost_amt is not null
                ;
				
				-- 取出定标成本	
				drop table if exists posted_cost_tmp;
		   create temporary table posted_cost_tmp
		      as
        select version_id
               ,year				
		       ,period_id          
		       ,transport_mode     
		       ,region_cn_name     
		       ,route              
		       ,source_port_name
               ,dest_port_name
	           ,dest_country_name 			     
		       ,currency    		   
       		   ,posted_cost_amt   
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
	    from air_month_income_cost_tmp
		where posted_cost_amt is not null
               ;
				
		     -- 计算航线层级的月度比定标价收益= 定标成本 - 华为成本	 
    insert into air_month_income_cost_tmp(
                version_id
               ,year				
		       ,period_id          
		       ,transport_mode     
		       ,region_cn_name     
		       ,route              
		       ,source_port_name
               ,dest_port_name
	           ,dest_country_name			     
		       ,currency    		   
			   ,income_ratio_amt
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
                )
		select  t1.version_id
               ,t1.year				
		       ,t1.period_id          
		       ,t1.transport_mode     
		       ,t1.region_cn_name     
		       ,t1.route              
		       ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name			     
		       ,t1.currency   		   
			   ,sum(t1.posted_cost_amt - t2.hw_cost_amt) as income_ratio_amt
			   ,t1.Huawei_group    
               ,t1.service_level   
			   ,t1.is_high_quality 
		   from posted_cost_tmp  t1
		   left join  hw_cost_tmp t2
		      on t1.version_id = t2.version_id
		     and t1.period_id  = t2.period_id
		     and t1.transport_mode = t2.transport_mode
		     and t1.region_cn_name = t2.region_cn_name
		     and t1.route=t2.route
		     and t1.currency = t2.currency 
		     and t1.Huawei_group = t2.Huawei_group
		     and t1.service_level = t2.service_level
		     and t1.is_high_quality = t2.is_high_quality
		   group by t1.version_id
               ,t1.year				
		       ,t1.period_id          
		       ,t1.transport_mode     
		       ,t1.region_cn_name     
		       ,t1.route              
		       ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name			     
		       ,t1.currency 
			   ,t1.Huawei_group    
               ,t1.service_level   
			   ,t1.is_high_quality
		   ;
		   
		     v_dml_row_count := sql%rowcount;	-- 收集数据量
		   
		   -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 8,
        p_log_cal_log_desc => 'air_month_income_cost_tmp 航线层级的月度比定标价收益'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;					
	           
			 -- 取出 TAC成本	
				drop table if exists tac_cost_tmp;
		   create temporary table tac_cost_tmp
		      as 
        select version_id
               ,year				
		       ,period_id          
		       ,transport_mode     
		       ,region_cn_name     
		       ,route              
		       ,source_port_name
               ,dest_port_name
	           ,dest_country_name 			     
		       ,currency    		   
       		   ,tac_cost_amt   
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
	    from air_month_income_cost_tmp
		where tac_cost_amt is not null
              ;			
		   
		   -- 计算航线层级的月度比市场价收益 = TAC成本 – 华为成本	 
     insert into air_month_income_cost_tmp(
                version_id
               ,year				
		       ,period_id          
		       ,transport_mode     
		       ,region_cn_name     
		       ,route              
		       ,source_port_name
               ,dest_port_name
	           ,dest_country_name 			     
		       ,currency    		   
			   ,income_tac_amt
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
                )					
		select  t1.version_id
               ,t1.year				
		       ,t1.period_id          
		       ,t1.transport_mode     
		       ,t1.region_cn_name     
		       ,t1.route              
		       ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name 			     
		       ,t1.currency 		   
			   ,sum(t1.tac_cost_amt - t2.hw_cost_amt)  income_tac_amt
			   ,t1.Huawei_group    
               ,t1.service_level   
			   ,t1.is_high_quality 
		   from  tac_cost_tmp t1
		    left join  hw_cost_tmp t2
		      on t1.version_id = t2.version_id
		     and t1.period_id  = t2.period_id
		     and t1.transport_mode = t2.transport_mode
		     and t1.region_cn_name = t2.region_cn_name
		     and t1.route=t2.route
		     and t1.currency = t2.currency 
		     and t1.Huawei_group = t2.Huawei_group
		     and t1.service_level = t2.service_level
		     and t1.is_high_quality = t2.is_high_quality
		   group by t1.version_id
               ,t1.year				
		       ,t1.period_id          
		       ,t1.transport_mode     
		       ,t1.region_cn_name     
		       ,t1.route              
		       ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name 			     
		       ,t1.currency   
			   ,t1.Huawei_group    
               ,t1.service_level   
			   ,t1.is_high_quality 
		   ;
		   
		     v_dml_row_count := sql%rowcount;	-- 收集数据量
		   
		   -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 9,
        p_log_cal_log_desc => 'air_month_income_cost_tmp 航线层级的月度比市场价收益'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;				
	     
		 -- 收敛航线层级月度成本和收益
		 drop table if exists route_cost_tmp;
		   create temporary table route_cost_tmp
		      as 
        select version_id
               ,year				
		       ,period_id          
		       ,transport_mode     
		       ,region_cn_name     
		       ,route              
		       ,source_port_name
               ,dest_port_name
	           ,dest_country_name			     
		       ,currency    		   
       		   ,sum(hw_cost_amt) as hw_cost_amt
               ,sum(tac_cost_amt) as tac_cost_amt
               ,sum(posted_cost_amt) as posted_cost_amt
               ,sum(income_tac_amt) as income_tac_amt
               ,sum(income_ratio_amt) as income_ratio_amt		   
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
		from air_month_income_cost_tmp
		group by version_id
               ,year				
		       ,period_id          
		       ,transport_mode     
		       ,region_cn_name     
		       ,route              
		       ,source_port_name
               ,dest_port_name
	           ,dest_country_name			     
		       ,currency    		          		   		   
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
              ;
			  
			  -- 航线层级月度货量
			  drop table if exists route_qty_tmp;
		   create temporary table route_qty_tmp
		      as 
                select  version_id
		       ,year
	           ,period_id
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name
               ,dest_port_name
	           ,dest_country_name
			   ,currency
			   ,sum(container_qty) as container_qty       
			   ,Huawei_group    
			   ,service_level   
			   ,is_high_quality
		from  air_income_route_tmp
		where container_qty is not null
		  and transport_mode = '精品空运'
		group by  version_id
		       , year
	           , period_id
			   , transport_mode
			   , region_cn_name
			   , route
			   ,source_port_name
               ,dest_port_name
	           ,dest_country_name
			   , currency      
			   , Huawei_group    
			   , service_level   
			   , is_high_quality 
			   union all
			    select  version_id
		       ,year
	           ,period_id
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name
               ,dest_port_name
	           ,dest_country_name
			   ,currency
			   ,sum(container_qty) as container_qty       
			   ,Huawei_group    
			   ,service_level   
			   ,'ALL' as is_high_quality
		from  air_income_route_tmp
		where container_qty is not null
		  and transport_mode = '精品空运'
		group by  version_id
		       , year
	           , period_id
			   , transport_mode
			   , region_cn_name
			   , route
			   ,source_port_name
               ,dest_port_name
	           ,dest_country_name
			   , currency      
			   , Huawei_group    
			   , service_level   
                ;			   
		   
		  -- 计算航线层级月度收益率
		  drop table if exists air_income_cost_tmp;
		   create temporary table air_income_cost_tmp
		      as 
			    -- 航线层级月度比市场价收益率
			   select t1.version_id       
	           ,t1.period_id        
	           ,t1.year             
               ,'M' as target_type      
               ,'03' as level_code       
               ,'航线' as level_desc       
               ,t1.transport_mode   
               ,t1.region_cn_name   
               ,t1.route            
               ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name
               ,t1.currency         
               ,sum(t2.container_qty)    as container_qty    -- 航线层级月度货量 
			   ,sum(t1.hw_cost_amt)      as hw_cost_amt      -- 华为成本金额 
               ,sum(t1.tac_cost_amt)     as  tac_cost_amt    -- TAC成本金额 
               ,null as posted_cost_amt  -- 定标成本金额 
               ,sum(t1.income_tac_amt)   as income_amt       -- 比市场价收益
               ,sum(t1.income_tac_amt/t1.tac_cost_amt) as income_ratio    -- 比市场价收益率
			   ,'1' as price_comparison_type  --价格比较类型（1、比市场价结果   2、比定标价结果）
               ,t1.Huawei_group     
               ,t1.service_level    
               ,t1.is_high_quality  
           from route_cost_tmp t1
		   left join route_qty_tmp  t2
		     on t1.version_id = t2.version_id
		    and t1.period_id  = t2.period_id
		    and t1.transport_mode = t2.transport_mode
		    and t1.region_cn_name = t2.region_cn_name
		    and t1.route=t2.route
		    and t1.currency = t2.currency
		    and t1.year = t2.year		 
		    and t1.Huawei_group = t2.Huawei_group
		    and t1.service_level = t2.service_level
		    and t1.is_high_quality = t2.is_high_quality
			group by t1.version_id       
	           ,t1.period_id        
	           ,t1.year
			   ,t1.transport_mode   
               ,t1.region_cn_name   
               ,t1.route            
               ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name
               ,t1.currency 
			   ,t1.Huawei_group     
               ,t1.service_level    
               ,t1.is_high_quality
			   union all
			   	-- 航线层级月度比定标价收益率
			   select t1.version_id       
	           ,t1.period_id        
	           ,t1.year             
               ,'M' as target_type      
               ,'03' as level_code       
               ,'航线' as level_desc       
               ,t1.transport_mode   
               ,t1.region_cn_name   
               ,t1.route            
               ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name
               ,t1.currency         
               ,sum(t2.container_qty)    as container_qty    -- 航线层级月度货量 
               ,sum(t1.hw_cost_amt)     as  hw_cost_amt    -- 华为成本金额 
               ,null as tac_cost_amt      -- TAC成本金额
               ,sum(t1.posted_cost_amt)  as posted_cost_amt  -- 定标成本金额 
               ,sum(t1.income_ratio_amt) as income_amt       -- 比定标价收益
               ,sum(t1.income_ratio_amt/t1.posted_cost_amt) as income_ratio    -- 比定标价收益率
			   ,'2' as price_comparison_type  --价格比较类型（1、比市场价结果   2、比定标价结果）
               ,t1.Huawei_group     
               ,t1.service_level    
               ,t1.is_high_quality  
           from route_cost_tmp t1
		   left join route_qty_tmp  t2
		     on t1.version_id = t2.version_id
		    and t1.period_id  = t2.period_id
		    and t1.transport_mode = t2.transport_mode
		    and t1.region_cn_name = t2.region_cn_name
		    and t1.route=t2.route
		    and t1.currency = t2.currency
		    and t1.year = t2.year		 
		    and t1.Huawei_group = t2.Huawei_group
		    and t1.service_level = t2.service_level
		    and t1.is_high_quality = t2.is_high_quality
			group by t1.version_id       
	           ,t1.period_id        
	           ,t1.year
			   ,t1.transport_mode   
               ,t1.region_cn_name   
               ,t1.route            
               ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name
               ,t1.currency 
			   ,t1.Huawei_group     
               ,t1.service_level    
               ,t1.is_high_quality
                 ;
				 				 
				   v_dml_row_count := sql%rowcount;	-- 收集数据量
				   -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 10,
        p_log_cal_log_desc => 'air_income_cost_tmp 航线层级月度收益率'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;				
				   	
				  -- 收敛区域层级月度成本和收益
		 drop table if exists region_cost_tmp;
		   create temporary table region_cost_tmp
		      as 
        select version_id
               ,year				
		       ,period_id          
		       ,transport_mode     
		       ,region_cn_name    	     
		       ,currency    		   
       		   ,sum(hw_cost_amt) as hw_cost_amt
               ,sum(tac_cost_amt) as tac_cost_amt
               ,sum(posted_cost_amt) as posted_cost_amt
               ,sum(income_tac_amt) as income_tac_amt
               ,sum(income_ratio_amt) as income_ratio_amt		   
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
		from air_month_income_cost_tmp
		group by version_id
               ,year				
		       ,period_id          
		       ,transport_mode     
		       ,region_cn_name     		      		     
		       ,currency    		          		   		   
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
              ;
			  
			  -- 区域层级月度货量
			   drop table if exists region_qty_tmp;
		   create temporary table region_qty_tmp
		      as 
                select  version_id
		       ,year
	           ,period_id
			   ,transport_mode
			   ,region_cn_name
			   ,currency
			   ,sum(container_qty) as container_qty       
			   ,Huawei_group    
			   ,service_level   
			   ,is_high_quality
		from  route_qty_tmp
		group by  version_id
		       , year
	           , period_id
			   , transport_mode
			   , region_cn_name
			   , currency      
			   , Huawei_group    
			   , service_level   
			   , is_high_quality 
			     ;
			  
				 -- 区域层级月度收益率
       insert into air_income_cost_tmp(
	             version_id       
	           ,period_id        
	           ,year             
               ,target_type      
               ,level_code       
               ,level_desc       
               ,transport_mode   
               ,region_cn_name   
               ,currency         
               ,container_qty   
               ,hw_cost_amt 			   
               ,tac_cost_amt          
               ,posted_cost_amt  
               ,income_amt       
               ,income_ratio   
               ,price_comparison_type			   
               ,Huawei_group     
               ,service_level    
               ,is_high_quality  
			   )		
               -- 区域层级月度比市场价收益率			   
			   select t1.version_id       
	           ,t1.period_id        
	           ,t1.year             
               ,'M' as target_type      
               ,'02' as level_code       
               ,'区域' as level_desc       
               ,t1.transport_mode   
               ,t1.region_cn_name   
               ,t1.currency         
               ,sum(t2.container_qty)    as container_qty    -- 航线层级月度货量 
			   ,sum(t1.hw_cost_amt)      as hw_cost_amt      -- 华为成本金额 
               ,sum(t1.tac_cost_amt)     as  tac_cost_amt    -- TAC成本金额 
               ,null as posted_cost_amt  -- 定标成本金额 
               ,sum(t1.income_tac_amt)   as income_amt       -- 比市场价收益
               ,sum(t1.income_tac_amt/t1.tac_cost_amt) as income_ratio    -- 比市场价收益率
			   ,'1' as price_comparison_type  --价格比较类型（1、比市场价结果   2、比定标价结果）
               ,t1.Huawei_group     
               ,t1.service_level    
               ,t1.is_high_quality  
           from region_cost_tmp t1
		   left join region_qty_tmp  t2
		     on t1.version_id = t2.version_id
		    and t1.period_id  = t2.period_id
		    and t1.transport_mode = t2.transport_mode
		    and t1.region_cn_name = t2.region_cn_name
		    and t1.currency = t2.currency
		    and t1.year = t2.year		 
		    and t1.Huawei_group = t2.Huawei_group
		    and t1.service_level = t2.service_level
		    and t1.is_high_quality = t2.is_high_quality
			group by t1.version_id       
	           ,t1.period_id        
	           ,t1.year
			   ,t1.transport_mode   
               ,t1.region_cn_name   
               ,t1.currency 
			   ,t1.Huawei_group     
               ,t1.service_level    
               ,t1.is_high_quality
			   union all
			    -- 区域层级月度比定标价收益率
			   select t1.version_id       
	           ,t1.period_id        
	           ,t1.year             
               ,'M' as target_type      
               ,'02' as level_code       
               ,'区域' as level_desc       
               ,t1.transport_mode   
               ,t1.region_cn_name   
               ,t1.currency         
               ,sum(t2.container_qty)    as container_qty    -- 航线层级月度货量 
               ,sum(t1.hw_cost_amt)     as  hw_cost_amt    -- 华为成本金额 
               ,null as tac_cost_amt      -- TAC成本金额
               ,sum(t1.posted_cost_amt)  as posted_cost_amt  -- 定标成本金额 
               ,sum(t1.income_ratio_amt) as income_amt       -- 比定标价收益
               ,sum(t1.income_ratio_amt/t1.posted_cost_amt) as income_ratio    -- 比定标价收益率
			   ,'2' as price_comparison_type  --价格比较类型（1、比市场价结果   2、比定标价结果）
               ,t1.Huawei_group     
               ,t1.service_level    
               ,t1.is_high_quality  
           from region_cost_tmp t1
		   left join region_qty_tmp  t2
		     on t1.version_id = t2.version_id
		    and t1.period_id  = t2.period_id
		    and t1.transport_mode = t2.transport_mode
		    and t1.region_cn_name = t2.region_cn_name
		    and t1.currency = t2.currency
		    and t1.year = t2.year		 
		    and t1.Huawei_group = t2.Huawei_group
		    and t1.service_level = t2.service_level
		    and t1.is_high_quality = t2.is_high_quality
			group by t1.version_id       
	           ,t1.period_id        
	           ,t1.year
			   ,t1.transport_mode   
               ,t1.region_cn_name   
               ,t1.currency 
			   ,t1.Huawei_group     
               ,t1.service_level    
               ,t1.is_high_quality
                 ;				 
				 
				   v_dml_row_count := sql%rowcount;	-- 收集数据量
				 -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 11,
        p_log_cal_log_desc => 'air_income_cost_tmp 区域层级月度比市场价收益率'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;				
	
	     -- 收敛 运输方式层级 月度成本和收益
		 drop table if exists transport_cost_tmp;
		   create temporary table transport_cost_tmp
		      as 
        select version_id
               ,year				
		       ,period_id          
		       ,transport_mode          
		       ,currency    		   
       		   ,sum(hw_cost_amt) as hw_cost_amt
               ,sum(tac_cost_amt) as tac_cost_amt
               ,sum(posted_cost_amt) as posted_cost_amt
               ,sum(income_tac_amt) as income_tac_amt
               ,sum(income_ratio_amt) as income_ratio_amt		   
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
		from air_month_income_cost_tmp
		group by version_id
               ,year				
		       ,period_id          
		       ,transport_mode         		      		     
		       ,currency    		          		   		   
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
              ;
			  
			  -- 运输方式层级月度货量
			   drop table if exists transport_qty_tmp;
		   create temporary table transport_qty_tmp
		      as 
                select  version_id
		       ,year
	           ,period_id
			   ,transport_mode
			   ,currency
			   ,sum(container_qty) as container_qty       
			   ,Huawei_group    
			   ,service_level   
			   ,is_high_quality
		from  region_qty_tmp
		group by  version_id
		       , year
	           , period_id
			   , transport_mode
			   , currency      
			   , Huawei_group    
			   , service_level   
			   , is_high_quality 
			     ;
				 
				  -- 运输方式层级月度收益率
       insert into air_income_cost_tmp(
	             version_id       
	           ,period_id        
	           ,year             
               ,target_type      
               ,level_code       
               ,level_desc       
               ,transport_mode    
               ,currency         
               ,container_qty   
               ,hw_cost_amt 			   
               ,tac_cost_amt          
               ,posted_cost_amt  
               ,income_amt       
               ,income_ratio   
               ,price_comparison_type			   
               ,Huawei_group     
               ,service_level    
               ,is_high_quality  
			   )	
             -- 运输方式层级月度比市场价收益率			   
			   select t1.version_id       
	           ,t1.period_id        
	           ,t1.year             
               ,'M' as target_type      
               ,'01' as level_code       
               ,'运输方式' as level_desc       
               ,t1.transport_mode    
               ,t1.currency         
               ,sum(t2.container_qty)    as container_qty    -- 航线层级月度货量 
			   ,sum(t1.hw_cost_amt)      as hw_cost_amt      -- 华为成本金额 
               ,sum(t1.tac_cost_amt)     as  tac_cost_amt    -- TAC成本金额 
               ,null as posted_cost_amt  -- 定标成本金额 
               ,sum(t1.income_tac_amt)   as income_amt       -- 比市场价收益
               ,sum(t1.income_tac_amt/t1.tac_cost_amt) as income_ratio    -- 比市场价收益率
			   ,'1' as price_comparison_type  --价格比较类型（1、比市场价结果   2、比定标价结果）
               ,t1.Huawei_group     
               ,t1.service_level    
               ,t1.is_high_quality  
           from transport_cost_tmp t1
		   left join transport_qty_tmp  t2
		     on t1.version_id = t2.version_id
		    and t1.period_id  = t2.period_id
		    and t1.transport_mode = t2.transport_mode
		    and t1.currency = t2.currency
		    and t1.year = t2.year		 
		    and t1.Huawei_group = t2.Huawei_group
		    and t1.service_level = t2.service_level
		    and t1.is_high_quality = t2.is_high_quality
			group by t1.version_id       
	           ,t1.period_id        
	           ,t1.year
			   ,t1.transport_mode   
               ,t1.currency 
			   ,t1.Huawei_group     
               ,t1.service_level    
               ,t1.is_high_quality
			   union all
			    -- 运输方式层级月度比定标价收益率
			    select t1.version_id       
	           ,t1.period_id        
	           ,t1.year             
               ,'M' as target_type      
               ,'01' as level_code       
               ,'运输方式' as level_desc       
               ,t1.transport_mode   
               ,t1.currency         
               ,sum(t2.container_qty)    as container_qty    -- 航线层级月度货量 
               ,sum(t1.hw_cost_amt)     as  hw_cost_amt    -- 华为成本金额 
               ,null as tac_cost_amt      -- TAC成本金额
               ,sum(t1.posted_cost_amt)  as posted_cost_amt  -- 定标成本金额 
               ,sum(t1.income_ratio_amt) as income_amt       -- 比定标价收益
               ,sum(t1.income_ratio_amt/t1.posted_cost_amt) as income_ratio    -- 比定标价收益率
			   ,'2' as price_comparison_type  --价格比较类型（1、比市场价结果   2、比定标价结果）
               ,t1.Huawei_group     
               ,t1.service_level    
               ,t1.is_high_quality  
           from transport_cost_tmp t1
		   left join transport_qty_tmp  t2
		     on t1.version_id = t2.version_id
		    and t1.period_id  = t2.period_id
		    and t1.transport_mode = t2.transport_mode
		    and t1.currency = t2.currency
		    and t1.year = t2.year		 
		    and t1.Huawei_group = t2.Huawei_group
		    and t1.service_level = t2.service_level
		    and t1.is_high_quality = t2.is_high_quality
			group by t1.version_id       
	           ,t1.period_id        
	           ,t1.year
			   ,t1.transport_mode    
               ,t1.currency 
			   ,t1.Huawei_group     
               ,t1.service_level    
               ,t1.is_high_quality
                 ;             
				 
				   v_dml_row_count := sql%rowcount;	-- 收集数据量
				 
				 -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 12,
        p_log_cal_log_desc => 'air_income_cost_tmp 运输方式层级月度比市场价收益率'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;				
		 
				 -- 将所有层级的月度成本、收益、收益率入到临时表
				 drop table if exists air_actual_perform_income_info_tmp;
		   create temporary table air_actual_perform_income_info_tmp
		      as
				select version_id             
	           ,year
	           ,period_id  			   
               ,target_type           
               ,transport_mode   
               ,region_cn_name   
               ,route            
               ,source_port_name
               ,dest_port_name
	           ,dest_country_name
			   ,level_code       
               ,level_desc  
               ,currency         
               ,container_qty       
               ,hw_cost_amt
               ,tac_cost_amt  			   
               ,posted_cost_amt  
               ,income_amt       
               ,income_ratio   
               ,price_comparison_type			   
               ,Huawei_group     
               ,service_level    
               ,is_high_quality  			   
			   from air_income_cost_tmp
			   ;
			   
			     v_dml_row_count := sql%rowcount;	-- 收集数据量
			   
			   -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 16,
        p_log_cal_log_desc => 'air_actual_perform_income_info_tmp 所有层级的月度成本、收益、收益率入到临时表'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;				
			   
			     -- 将所有层级月度指标按YTD汇总
				 drop table if exists ytd_cost_tmp;
		   create temporary table ytd_cost_tmp
		      as 
		 select version_id
               ,year
               ,period_id
               ,'YTD' as target_type
               ,transport_mode
			   ,region_cn_name
               ,route
               ,source_port_name
               ,dest_port_name
	           ,dest_country_name
               ,level_code
               ,level_desc
               ,currency
               ,sum(container_qty) over(partition by  version_id
                                                     ,year
                                                     ,target_type
                                                     ,transport_mode
													 ,region_cn_name
                                                     ,route
                                                     ,source_port_name
                                                     ,dest_port_name
	                                                 ,dest_country_name
                                                     ,level_code
                                                     ,level_desc
                                                     ,currency 
                                                     ,price_comparison_type
                                                     ,Huawei_group
                                                     ,service_level
                                                     ,is_high_quality
													 order by period_id) as container_qty													 
               ,sum(hw_cost_amt)   over(partition by  version_id
                                                     ,year
                                                     ,target_type
                                                     ,transport_mode
													 ,region_cn_name
                                                     ,route
                                                     ,source_port_name
                                                     ,dest_port_name
	                                                 ,dest_country_name
                                                     ,level_code
                                                     ,level_desc
                                                     ,currency 
                                                     ,price_comparison_type
                                                     ,Huawei_group
                                                     ,service_level
                                                     ,is_high_quality
													 order by period_id) as hw_cost_amt
               ,sum(tac_cost_amt)  over(partition by  version_id
                                                     ,year
                                                     ,target_type
                                                     ,transport_mode
													 ,region_cn_name
                                                     ,route
                                                     ,source_port_name
                                                     ,dest_port_name
	                                                 ,dest_country_name
                                                     ,level_code
                                                     ,level_desc
                                                     ,currency 
                                                     ,price_comparison_type
                                                     ,Huawei_group
                                                     ,service_level
                                                     ,is_high_quality
													 order by period_id) as tac_cost_amt			   
               ,sum(posted_cost_amt) over(partition by  version_id
                                                     ,year
                                                     ,target_type
                                                     ,transport_mode
													 ,region_cn_name
                                                     ,route
                                                     ,source_port_name
                                                     ,dest_port_name
	                                                 ,dest_country_name
                                                     ,level_code
                                                     ,level_desc
                                                     ,currency 
                                                     ,price_comparison_type
                                                     ,Huawei_group
                                                     ,service_level
                                                     ,is_high_quality
													 order by period_id) as posted_cost_amt
               ,sum(income_amt)    over(partition by  version_id
                                                     ,year
                                                     ,target_type
                                                     ,transport_mode
													 ,region_cn_name
                                                     ,route
                                                     ,source_port_name
                                                     ,dest_port_name
	                                                 ,dest_country_name
                                                     ,level_code
                                                     ,level_desc
                                                     ,currency 
                                                     ,price_comparison_type
                                                     ,Huawei_group
                                                     ,service_level
                                                     ,is_high_quality
													 order by period_id) as income_amt   
			   ,price_comparison_type
               ,Huawei_group
               ,service_level
               ,is_high_quality              
			 from air_income_cost_tmp
			        ;
			  			   
			   -- 计算所有层级月度YTD的收益率
	insert into air_actual_perform_income_info_tmp(
				version_id
               ,year
               ,period_id
               ,target_type
               ,transport_mode
               ,region_cn_name
               ,route
               ,source_port_name
               ,dest_port_name
	           ,dest_country_name
               ,level_code
               ,level_desc
               ,currency
               ,container_qty
               ,hw_cost_amt
               ,tac_cost_amt
               ,posted_cost_amt
               ,income_amt
               ,income_ratio
			   ,price_comparison_type
               ,Huawei_group
               ,service_level
               ,is_high_quality               
			    )
			 select version_id
               ,year
               ,period_id
               ,target_type
               ,transport_mode
               ,region_cn_name
               ,route
               ,source_port_name
               ,dest_port_name
	           ,dest_country_name
               ,level_code
               ,level_desc
               ,currency
               ,container_qty
               ,sum(hw_cost_amt) as hw_cost_amt
               ,sum(tac_cost_amt) as tac_cost_amt
               ,sum(posted_cost_amt) as posted_cost_amt
               ,sum(income_amt) as income_amt
               ,sum(case when price_comparison_type = '1' 
			         then income_amt/tac_cost_amt 
			         when price_comparison_type = '2' 
					 then income_amt/posted_cost_amt 
					 end) as income_ratio
			   ,price_comparison_type
               ,Huawei_group
               ,service_level
               ,is_high_quality       
			 from ytd_cost_tmp
			 group by version_id
               ,year
               ,period_id
               ,target_type
               ,transport_mode
               ,region_cn_name
               ,route
               ,source_port_name
               ,dest_port_name
	           ,dest_country_name
               ,level_code
               ,level_desc
               ,currency
               ,container_qty              
			   ,price_comparison_type
               ,Huawei_group
               ,service_level
               ,is_high_quality      
			    ;
								
				  v_dml_row_count := sql%rowcount;	-- 收集数据量
				 -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 17,
        p_log_cal_log_desc => 'air_actual_perform_income_info_tmp 将所有层级月度指标按YTD汇总'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;		
				
				-- 将所有层级月度指标按年汇总
				drop table if exists year_cost_tmp;
		   create temporary table year_cost_tmp
		      as 
		   select version_id
               ,year
               ,(year||'00')::int as period_id
               ,'Y' as target_type
               ,transport_mode
			   ,region_cn_name
               ,route
               ,source_port_name
               ,dest_port_name
	           ,dest_country_name
               ,level_code
               ,level_desc
               ,currency
               ,sum(container_qty)   as  container_qty      
               ,sum(hw_cost_amt)     as hw_cost_amt
               ,sum(tac_cost_amt)    as  tac_cost_amt 			   
               ,sum(posted_cost_amt) as  posted_cost_amt 
               ,sum(income_amt)      as income_amt    
			   ,price_comparison_type
               ,Huawei_group
               ,service_level
               ,is_high_quality               
			  from air_income_cost_tmp
              group by version_id
               ,year
               ,transport_mode
			   ,region_cn_name
               ,route
               ,source_port_name
               ,dest_port_name
	           ,dest_country_name
               ,level_code
               ,level_desc
               ,currency
			   ,price_comparison_type
               ,Huawei_group
               ,service_level
               ,is_high_quality
			       ;
				   
				   -- 计算所有层级年度收益率
	insert into air_actual_perform_income_info_tmp(
				version_id
               ,year
               ,period_id
               ,target_type
               ,transport_mode
               ,region_cn_name
               ,route
               ,source_port_name
               ,dest_port_name
	           ,dest_country_name
               ,level_code
               ,level_desc
               ,currency
               ,container_qty
               ,hw_cost_amt
               ,tac_cost_amt
               ,posted_cost_amt
               ,income_amt
               ,income_ratio
			   ,price_comparison_type
               ,Huawei_group
               ,service_level
               ,is_high_quality              
			    )				
			    select version_id
               ,year
               ,period_id
               ,target_type
               ,transport_mode
               ,region_cn_name
               ,route
               ,source_port_name
               ,dest_port_name
	           ,dest_country_name
               ,level_code
               ,level_desc
               ,currency
               ,container_qty
               ,sum(hw_cost_amt) as hw_cost_amt
               ,sum(tac_cost_amt) as tac_cost_amt
               ,sum(posted_cost_amt) as posted_cost_amt
               ,sum(income_amt) as income_amt
               ,sum(case when price_comparison_type = '1' 
			         then income_amt/tac_cost_amt
			         when price_comparison_type = '2' 
					 then income_amt/posted_cost_amt
					 end) as income_ratio
			   ,price_comparison_type
               ,Huawei_group
               ,service_level
               ,is_high_quality       
			 from year_cost_tmp
			 group by version_id
               ,year
               ,period_id
               ,target_type
               ,transport_mode
               ,region_cn_name
               ,route
               ,source_port_name
               ,dest_port_name
	           ,dest_country_name
               ,level_code
               ,level_desc
               ,currency
               ,container_qty              
			   ,price_comparison_type
               ,Huawei_group
               ,service_level
               ,is_high_quality      
			   ;
			 
              v_dml_row_count := sql%rowcount;	-- 收集数据量
				 -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 18,
        p_log_cal_log_desc => 'air_actual_perform_income_info_tmp 将所有层级月度指标按年汇总'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;		
	
				-- 从202021至数据最大会计期的所有会计期和年份
				drop table if exists period_tmp;
		   create temporary table period_tmp
		      as 
              select to_char(generate_series,'YYYYMM')  as period_id
			        ,to_char(generate_series,'YYYY')    as year
              from generate_series('2020-01-01':: timestamp ,CURRENT_DATE::timestamp, '1 month')
              where to_char(generate_series,'YYYYMM') <= (select max(period_id)  from air_income_route_tmp)
             ;
	
	          -- 所有航线补齐所有时间
			  drop table if exists apd_time_route_tmp;
		   create temporary table apd_time_route_tmp
		      as 
		select distinct       
			    t1.period_id
			   ,t1.year
               ,t2.target_type
               ,t2.transport_mode
               ,t2.region_cn_name
               ,t2.route
               ,t2.source_port_name
               ,t2.dest_port_name
	           ,t2.dest_country_name
               ,t2.level_code
               ,t2.level_desc
               ,t2.currency               
			   ,t2.price_comparison_type
               ,t2.Huawei_group
               ,t2.service_level
               ,t2.is_high_quality      	
        from period_tmp  t1
		left join air_actual_perform_income_info_tmp t2
		on 1=1
		where t2.target_type in ('YTD','M')
		union all
		select distinct       
			    t1.year||'00' as period_id
			   ,t1.year
               ,t2.target_type
               ,t2.transport_mode
               ,t2.region_cn_name
               ,t2.route
               ,t2.source_port_name
               ,t2.dest_port_name
	           ,t2.dest_country_name
               ,t2.level_code
               ,t2.level_desc
               ,t2.currency               
			   ,t2.price_comparison_type
               ,t2.Huawei_group
               ,t2.service_level
               ,t2.is_high_quality      	
        from period_tmp  t1
		left join air_actual_perform_income_info_tmp t2
		on 1=1
		where t2.target_type ='Y'
		        ;
			  
		delete from fin_dm_opt_foi.dm_fol_air_actual_perform_income_info_t where version_id = v_max_version_id;
	
	    -- 将缺失月份数据补齐入到结果表
	   insert into fin_dm_opt_foi.dm_fol_air_actual_perform_income_info_t(
				version_id
               ,year
               ,period_id
               ,target_type
               ,transport_mode
               ,region_cn_name
               ,route
               ,source_port_name
               ,dest_port_name
	           ,dest_country_name
               ,level_code
               ,level_desc
               ,currency
               ,container_qty
               ,hw_cost_amt
               ,tac_cost_amt
               ,posted_cost_amt
               ,income_amt
               ,income_ratio
			   ,price_comparison_type
               ,Huawei_group
               ,service_level
               ,is_high_quality
               ,remark
               ,created_by
               ,creation_date
               ,last_updated_by
               ,last_update_date
               ,del_flag			   
			    )
		select  v_max_version_id as version_id
               ,t1.year
               ,t1.period_id
               ,t1.target_type
               ,t1.transport_mode
               ,t1.region_cn_name
               ,t1.route
               ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name
               ,t1.level_code
               ,t1.level_desc
               ,t1.currency
               ,t2.container_qty
               ,t2.hw_cost_amt
               ,t2.tac_cost_amt
               ,t2.posted_cost_amt
               ,t2.income_amt
               ,t2.income_ratio
			   ,t1.price_comparison_type
               ,t1.Huawei_group
               ,t1.service_level
               ,t1.is_high_quality 
               , '' as remark
  	           , -1 as created_by
  	           , current_timestamp as creation_date
  	           , -1 as last_updated_by
  	           , current_timestamp as last_update_date
  	           , 'N' as del_flag			   
		   from apd_time_route_tmp t1
		   left join air_actual_perform_income_info_tmp t2
		     on 1=1
			and t1.period_id            = t2.period_id
			and t1.year                 = t2.year
            and t1.target_type          = t2.target_type
            and t1.transport_mode       = t2.transport_mode
            and nvl(t1.region_cn_name,'SNULL') = nvl(t2.region_cn_name,'SNULL')
            and nvl(t1.route,'SNULL') = nvl(t2.route,'SNULL')
            and nvl(t1.source_port_name,'SNULL') = nvl(t2.source_port_name,'SNULL')
            and nvl(t1.dest_port_name,'SNULL') = nvl(t2.dest_port_name,'SNULL')
	        and nvl(t1.dest_country_name,'SNULL') = nvl(t2.dest_country_name,'SNULL')
            and t1.level_code = t2.level_code
            and t1.level_desc = t2.level_desc
            and t1.currency = t2.currency      
			and t1.price_comparison_type = t2.price_comparison_type
            and t1.Huawei_group =t2.Huawei_group
            and t1.service_level = t2.service_level
            and t1.is_high_quality = t2.is_high_quality			
		   ;
		   		   
		     v_dml_row_count := sql%rowcount;	-- 收集数据量
		    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 19,
        p_log_cal_log_desc => 'dm_fol_air_actual_perform_income_info_t 将缺失月份数据补齐入到结果表'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;		

             -- 成功数据写入到版本信息表，需要更新对应的“执行中”数据状态
    update fin_dm_opt_foi.dm_fol_air_version_info_t set step = 1
     where version_id = v_max_version_id
       and source_en_name = 'f_dm_fol_air_actual_perform_income_info_t'
       and refresh_type = nvl(p_refresh_type,'4_AUTO')
       and upper(del_flag) = 'N'
    ;
	
	  --收集统计信息
  analyse fin_dm_opt_foi.dm_fol_air_actual_perform_income_info_t;
  analyse fin_dm_opt_foi.dm_fol_air_version_info_t;
	
	 exception
  	when others then

      perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		    p_log_row_count => null,
		    p_log_errbuf => sqlstate  --错误编码
      ) ;
	x_success_flag := '2001';	         --2001表示失败

  -- 失败信息写入到版本信息表
  insert into fin_dm_opt_foi.dm_fol_air_version_info_t(
         version_id           -- 版本ID（自动生成）
       , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
       , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
       , source_en_name       -- 来源英文描述（可以是表名、函数名等）
       , source_cn_name       -- 来源中文描述
       , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
	   , transport_mode       -- 运输方式（精品空运、精品海运）
       , remark               -- 备注
       , created_by           -- 创建人
       , creation_date        -- 创建时间
       , last_updated_by      -- 修改人
       , last_update_date     -- 修改时间
       , del_flag             -- 是否删除
  )
	select v_max_version_id   as version_id
       , v_price_version_code as version_code
       , 2001 as step
       , 'f_dm_fol_air_actual_perform_income_info_t' as source_en_name
       , '物流空运实际履行收益表'           as source_cn_name
       , nvl(p_refresh_type,'4_AUTO') as refresh_type
	   , '精品空运' as transport_mode
       , 'version_code 是物流空运价格补录表的' as remark
	   , -1 as created_by
	   , current_timestamp as creation_date
	   , -1 as last_updated_by
	   , current_timestamp as last_update_date
	   , 'N' as del_flag
  ;

end;
$$
/

