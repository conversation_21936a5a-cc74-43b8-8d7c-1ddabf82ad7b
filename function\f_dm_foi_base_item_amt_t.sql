-- Name: f_dm_foi_base_item_amt_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_base_item_amt_t(f_caliber_flag character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
创建时间：2023/1/30
创建人  ：罗若文
最后修改时间: 2023/1/30
最后修改人: 唐钦
背景描述：实际数金额卷积至ITEM层
参数描述：x_result_status ：是否成功
事例：SELECT FIN_DM_OPT_FOI.f_dm_foi_base_item_amt_t()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOI_BASE_ITEM_AMT_T'; --存储过程名称
  V_VERSION_ID BIGINT ; --版本号ID
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_FROM_TABLE VARCHAR(200);
  V_TO_TABLE     VARCHAR(200);
  V_SQL   TEXT;
  -- 202407版本新增
  V_SQL_CONDITION VARCHAR(300);
  V_CALIBER VARCHAR(100);
  V_IN_CALIBER VARCHAR(200);
  V_VERSION_TABLE VARCHAR(200);
  
BEGIN
  X_RESULT_STATUS = '1';

  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  -- 根据不同入参值对变量进行不同定义
  IF F_CALIBER_FLAG = 'E' THEN
    V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T';
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T';
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_BASE_ITEM_AMT_T';
  ELSE 
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_HIS_ORD_REC_REPORT_T';
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_BASE_ITEM_AMT_T';
    V_CALIBER := 'CALIBER_FLAG,';
    V_IN_CALIBER := ''''||F_CALIBER_FLAG||''' AS CALIBER_FLAG,';
    V_SQL_CONDITION := 'AND CALIBER_FLAG = '''||F_CALIBER_FLAG||'''';
    IF F_CALIBER_FLAG = 'IAS' THEN   -- IAS
       V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_PLAN_VERSION_T';
    ELSIF F_CALIBER_FLAG = 'EAST_CHINA_PQC' THEN   -- 华东采购
       V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ECPQC_PLAN_VERSION_T';
    END IF;
  END IF;
   
  --清空目标表数据:
  IF F_CALIBER_FLAG IN ('I','E') THEN
  EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_TO_TABLE;
  ELSE 
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE CALIBER_FLAG = '''||F_CALIBER_FLAG||'''';
  END IF;
  
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空 '||V_TO_TABLE||'数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 

  --版本号赋值.
  IF F_VERSION_ID IS NULL THEN
     V_SQL := '   
         SELECT VERSION_ID
            FROM '||V_VERSION_TABLE||'
            WHERE
             DEL_FLAG = ''N''
             AND STATUS = 1
             AND UPPER(DATA_TYPE) = ''CATEGORY''
             AND UPPER(VERSION_TYPE) IN (''AUTO'',''FINAL'')
             ORDER BY LAST_UPDATE_DATE DESC
             LIMIT 1';
   EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF; 

  --往目标表里插数
  V_SQL := '
  INSERT INTO '||V_TO_TABLE||'
    (    VERSION_ID,
        YEAR,
        PERIOD_ID,
        ITEM_CODE,
        ITEM_NAME,
        RECEIVE_QTY,
        RECEIVE_AMT_USD,
        RECEIVE_AMT_CNY,
        CATEGORY_CODE,
        CATEGORY_NAME,
        L4_CEG_CODE,
        L4_CEG_SHORT_CN_NAME,
        L4_CEG_CN_NAME,
        L3_CEG_CODE,
        L3_CEG_SHORT_CN_NAME,
        L3_CEG_CN_NAME,
        L2_CEG_CODE,
        L2_CEG_CN_NAME,
        '||V_CALIBER||'
        CREATED_BY,
        CREATION_DATE,
        LAST_UPDATED_BY,
        LAST_UPDATE_DATE,
        DEL_FLAG
     )
     
     SELECT '||V_VERSION_ID||' AS VERSION_ID,
        T.YEAR,
        T.PERIOD_ID,
        T.ITEM_CODE,
        T.ITEM_NAME,
        T.RECEIVE_QTY,
        T.RECEIVE_AMT_USD,
        T.RECEIVE_AMT_CNY,
        T.CATEGORY_CODE,
        T.CATEGORY_NAME,
        T.L4_CEG_CODE,
        T.L4_CEG_SHORT_CN_NAME,
        T.L4_CEG_CN_NAME,
        T.L3_CEG_CODE,
        T.L3_CEG_SHORT_CN_NAME,
        T.L3_CEG_CN_NAME,
        T.L2_CEG_CODE,
        T.L2_CEG_CN_NAME,
        '||V_IN_CALIBER||'
        -1 AS CREATED_BY,
        CURRENT_TIMESTAMP AS CREATION_DATE,
        -1 AS  LAST_UPDATED_BY,
        CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
        ''N'' AS DEL_FLAG
        FROM 
     (
        SELECT 
        YEAR,
        PERIOD_ID,
        ITEM_CODE,
        ITEM_NAME,
        SUM(RECEIVE_QTY) RECEIVE_QTY,
        SUM(RECEIVE_AMT_USD) RECEIVE_AMT_USD,
        SUM(RECEIVE_AMT_CNY) RECEIVE_AMT_CNY,
        CATEGORY_CODE,
        CATEGORY_NAME,
        L4_CEG_CODE,
        L4_CEG_SHORT_CN_NAME,
        L4_CEG_CN_NAME,
        L3_CEG_CODE,
        L3_CEG_SHORT_CN_NAME,
        L3_CEG_CN_NAME,
        L2_CEG_CODE,
        L2_CEG_CN_NAME
        FROM '||V_FROM_TABLE||'
        WHERE DEL_FLAG = ''N''
        '||V_SQL_CONDITION||'
        GROUP BY YEAR,
        PERIOD_ID,
        ITEM_CODE,
        ITEM_NAME,
        CATEGORY_CODE,
        CATEGORY_NAME,
        L4_CEG_CODE,
        L4_CEG_SHORT_CN_NAME,
        L4_CEG_CN_NAME,
        L3_CEG_CODE,
        L3_CEG_SHORT_CN_NAME,
        L3_CEG_CN_NAME,
        '||V_CALIBER||'
        L2_CEG_CODE,
        L2_CEG_CN_NAME
        )T';
    EXECUTE IMMEDIATE V_SQL;
        
  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插数到：'||V_TO_TABLE||'表, 版本号='||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集 '||V_TO_TABLE||'表统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

