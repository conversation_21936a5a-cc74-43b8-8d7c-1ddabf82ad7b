-- Name: f_dm_fom_ems_po_price_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_ems_po_price_t(f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$


/*
最近更新时间 : 2024年11月18日14点29分
更新时间: 2024年4月15日17点59分
修改人: 黄心蕊
修改内容: 202405版本修改 终端BG数据加入计算
创建时间：2023/12/05
创建人  ：许灿烽
背景描述： 制造EMS费用明细表
参数描述:x_result_status :是否成功
	参数一(f_version_id)：前端传入的版本号
	参数二(x_result_status)：运行状态返回值, 1 为成功，0 为失败
来源表:FIN_DM_OPT_FOI.DM_RC_SBG_MF_INV_EXP_MES_I  --制造EMS费用明细(EMS的Item的加工费用和量)  FIN_DM_OPT_FOI.APD_FOM_MANUFACTURE_DIM_T B 
目标表:FIN_DM_OPT_FOI.DM_FOM_EMS_PO_PRICE_T --制造EMS费用明细表
事例：fin_dm_opt_foi.f_dm_fom_ems_po_price_t()
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOM_EMS_PO_PRICE_T'; --存储过程名称
  V_TO_TABLE VARCHAR(50) := 'FIN_DM_OPT_FOI.DM_FOM_EMS_PO_PRICE_T'; -- 目标表
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT ; --版本号ID
  V_VERSION_NAME VARCHAR2(50) ; --版本中文名称
  V_DIM_VERSION_ID BIGINT ; --维表要取的版本号
BEGIN
  X_RESULT_STATUS = '1';
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

--取刷新数据的版本号,如果前端传值就用前端的,没有再赋值
IF F_VERSION_ID IS NULL THEN
--查询年度版本号
  SELECT VERSION_ID,VERSION INTO V_VERSION_ID,V_VERSION_NAME
    FROM
        FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T 
    WHERE
        SUBSTR(VERSION,1,6) = TO_CHAR(CURRENT_DATE, 'YYYYMM')
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'  AND VERSION_TYPE='AUTO';
ELSE V_VERSION_ID := F_VERSION_ID;
	
	SELECT VERSION INTO V_VERSION_NAME
	FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T
	WHERE VERSION_ID = V_VERSION_ID;
END IF;

-- 查询制造量纲维表的最新版本号
  SELECT MAX(T.VERSION_ID) 
    INTO V_DIM_VERSION_ID 
    FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'DIM_DMS'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '制造量纲维表使用的版本号:'||V_DIM_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
	 


TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOM_EMS_PO_PRICE_T;
INSERT INTO  FIN_DM_OPT_FOI.DM_FOM_EMS_PO_PRICE_T 
SELECT 
V_VERSION_ID AS VERSION_ID    --版本ID
,V_VERSION_NAME AS VERSION_NAME    --版本名称
,SUBSTR(A.PERIOD_ID,1,4) AS PERIOD_YEAR    --会计年
,A.PERIOD_ID,    --会计期
/*
,B.LV0_CODE    --重量级团队LV0编码
,B.LV0_CN_NAME    --重量级团队LV0中文名称
,B.LV1_CODE    --重量级团队LV1编码
,B.LV1_CN_NAME    --重量级团队LV1中文名称
,B.BUSSINESS_OBJECT AS BUSSINESS_OBJECT_CODE    --经营对象编码
,B.BUSSINESS_OBJECT AS BUSSINESS_OBJECT_CN_NAME    --经营对象名称
*/
B.APD_MANUFACTURE_PROD_LV0 AS LV0_CODE, --LV0中文名称
B.APD_MANUFACTURE_PROD_LV0 AS LV0_CN_NAME , --LV0中文名称
B.APD_MANUFACTURE_PROD_LV1 AS LV1_CODE , --LV1中文名称
B.APD_MANUFACTURE_PROD_LV1 AS LV1_CN_NAME , --LV1中文名称
B.APD_OPERATE_OBJECT AS BUSSINESS_OBJECT_CODE , --经营对象编码
B.APD_OPERATE_OBJECT AS BUSSINESS_OBJECT_CN_NAME , --经营对象名称
 CASE WHEN B.APD_MANUFACTURE_PROD_LV0 = 'ICT' AND B.APD_MANUFACTURE_PROD_LV1 = '云核心网' THEN ''
       WHEN B.APD_MANUFACTURE_PROD_LV0 = '海思光电' AND B.APD_MANUFACTURE_PROD_LV1 = '海思光电' THEN '' ELSE B.APD_SHIPMENT_OBJECT END AS SHIPPING_OBJECT_CODE     --发货对象编码
,CASE WHEN B.APD_MANUFACTURE_PROD_LV0 = 'ICT' AND B.APD_MANUFACTURE_PROD_LV1 = '云核心网' THEN ''
       WHEN B.APD_MANUFACTURE_PROD_LV0 = '海思光电' AND B.APD_MANUFACTURE_PROD_LV1 = '海思光电' THEN '' ELSE B.APD_SHIPMENT_OBJECT END AS SHIPPING_OBJECT_CN_NAME    --发货对象名称
,CASE WHEN B.APD_MANUFACTURE_PROD_LV0 = 'ICT' AND B.APD_MANUFACTURE_PROD_LV1 = '云核心网' THEN ''
       WHEN B.APD_MANUFACTURE_PROD_LV0 = '海思光电' AND B.APD_MANUFACTURE_PROD_LV1 = '海思光电' THEN '' ELSE B.APD_MANUFACTURE_OBJECT END AS MANUFACTURE_OBJECT_CODE     --制造对象编码
,CASE WHEN B.APD_MANUFACTURE_PROD_LV0 = 'ICT' AND B.APD_MANUFACTURE_PROD_LV1 = '云核心网' THEN ''
       WHEN B.APD_MANUFACTURE_PROD_LV0 = '海思光电' AND B.APD_MANUFACTURE_PROD_LV1 = '海思光电' THEN '' ELSE B.APD_MANUFACTURE_OBJECT END AS MANUFACTURE_OBJECT_CN_NAME    --制造对象名称
,A.ITEM AS ITEM_CODE    --子项ITEM编码
,NULL AS ITEM_CN_NAME    --子项ITEM中文	202411版本 ITEM中文名不全且不展示,字段置空
,-1 AS CREATED_BY    --创建人
,CURRENT_TIMESTAMP AS CREATION_DATE    --创建日期
,-1 AS LAST_UPDATED_BY    --最后更新人
,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE    --最后更新日期
,'N' AS DEL_FLAG    --删除标识(未删除：N，已删除：Y)
,SUM(A.RMB_EMS_AMT) AS RMB_EMS_AMT    --EMS吸收金额(自制加密，EMS金额不加密)
,SUM(A.TRANSACTION_QUANTITY) AS TRANSACTION_QUANTITY    --交易数量
FROM(
SELECT 
 CASE LENGTH(MATERIAL_CODE) WHEN 7 THEN CONCAT('0',MATERIAL_CODE)
     WHEN 8 THEN MATERIAL_CODE
     WHEN 9  THEN IF(LEFT(MATERIAL_CODE,1)='W',SUBSTR(MATERIAL_CODE,2,8), MATERIAL_CODE)
     WHEN 10 THEN IF(LEFT(MATERIAL_CODE,1)='W',SUBSTR(MATERIAL_CODE,2,8), MATERIAL_CODE)
     WHEN 11 THEN IF(LEFT(MATERIAL_CODE,1)='W',SUBSTR(MATERIAL_CODE,2,10), MATERIAL_CODE)
     WHEN 12 THEN IF(LEFT(MATERIAL_CODE,1)='W',SUBSTR(MATERIAL_CODE,2,10), MATERIAL_CODE)
     WHEN 13 THEN IF(LEFT(MATERIAL_CODE,1)='W',SUBSTR(MATERIAL_CODE,2,12), MATERIAL_CODE)
     WHEN 14 THEN IF(LEFT(MATERIAL_CODE,1)='W',SUBSTR(MATERIAL_CODE,2,12), MATERIAL_CODE)
     WHEN 15 THEN IF(LEFT(MATERIAL_CODE,1)='W',SUBSTR(MATERIAL_CODE,2,14), MATERIAL_CODE)
     WHEN 16 THEN IF(LEFT(MATERIAL_CODE,1)='W',SUBSTR(MATERIAL_CODE,2,14), MATERIAL_CODE) END AS ITEM  --需要做逻辑处理
,PERIOD_ID   --交易日期
,PURCHASE_UNIT_PRICE*QUANTITY AS RMB_EMS_AMT        --EMS吸收金额(自制加密，EMS金额不加密)  PURCHASE_UNIT_PRICE 为单价
,QUANTITY AS TRANSACTION_QUANTITY  --交易数量（PCS）
FROM FIN_DM_OPT_FOI.DM_RC_SBG_MF_INV_EXP_MES_I  --制造EMS费用明细(EMS的Item的加工费用和量) 
WHERE PURCHASE_UNIT_PRICE > 0 --剔除小于等于0的数据
AND MATERIAL_CODE NOT LIKE '%C'   --C结尾这类编码是维修任务令，把C结尾的物料编码去掉
)A
/*
INNER JOIN FIN_DM_OPT_FOI.APD_FOM_MANUFACTURE_DIM_T B 
ON(A.ITEM = B.ITEM_CODE)
WHERE B.VERSION_ID = V_DIM_VERSION_ID   --只取一个版本的数据
AND B.DIM_TREE_FLAG = 1  --只取在维度树的数据
--AND B.LV0_CN_NAME <> '终端BG'   --LV0是终端BG，就把数据删掉
--202405版本修改 终端BG数据加入计算
*/
--202411版本 
   INNER JOIN FIN_DM_OPT_FOI.APD_INV_ITEM_MANUFACTURE_T B
    ON (A.ITEM = B.ITEM_CODE)
GROUP BY 
 SUBSTR(A.PERIOD_ID,1,4)     --会计年
,A.PERIOD_ID    --会计期
/*
,B.LV0_CODE    --重量级团队LV0编码
,B.LV0_CN_NAME    --重量级团队LV0中文名称
,B.LV1_CODE    --重量级团队LV1编码
,B.LV1_CN_NAME    --重量级团队LV1中文名称
,B.BUSSINESS_OBJECT     --经营对象编码
,B.SHIPPING_OBJECT      --发货对象编码
,B.MANUFACTURE_OBJECT      --制造对象编码
*/
--202411版本 维表更换
,B.APD_MANUFACTURE_PROD_LV0
,B.APD_MANUFACTURE_PROD_LV1
,B.APD_OPERATE_OBJECT
,B.APD_SHIPMENT_OBJECT
,B.APD_MANUFACTURE_OBJECT	
,A.ITEM     --子项ITEM编码
;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '清空制造EMS费用明细表,并重新插入新的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
-- 202411版本 经营对象 发货对象 制造对象中，任意层级为空，则继承上级后赋值‘未定义’
--制造对象依赖发货对象，发货对象依赖经营对象，即UPDATE存在先后顺序，不可更改

--替换经营对象空值字段
UPDATE FIN_DM_OPT_FOI.DM_FOM_EMS_PO_PRICE_T  SET BUSSINESS_OBJECT_CODE = LV1_CN_NAME||'_未定义经营对象' WHERE 
(CASE 
  WHEN LV0_CN_NAME = 'ICT' AND LV1_CN_NAME = '云核心网' THEN 1=0
  WHEN LV0_CN_NAME = '海思光电' AND LV1_CN_NAME = '海思光电' THEN 1=0
  ELSE BUSSINESS_OBJECT_CODE IS NULL
  END );
UPDATE FIN_DM_OPT_FOI.DM_FOM_EMS_PO_PRICE_T  SET BUSSINESS_OBJECT_CN_NAME = LV1_CN_NAME||'_未定义经营对象' WHERE
(CASE 
  WHEN LV0_CN_NAME = 'ICT' AND LV1_CN_NAME = '云核心网' THEN 1=0
  WHEN LV0_CN_NAME = '海思光电' AND LV1_CN_NAME = '海思光电' THEN 1=0
  ELSE BUSSINESS_OBJECT_CN_NAME IS NULL
  END );
  
--替换发货对象空值字段
UPDATE FIN_DM_OPT_FOI.DM_FOM_EMS_PO_PRICE_T  SET SHIPPING_OBJECT_CODE = BUSSINESS_OBJECT_CN_NAME||'_未定义发货对象' WHERE
(CASE 
  WHEN LV0_CN_NAME = 'ICT' AND LV1_CN_NAME = '云核心网' THEN 1=0
  WHEN LV0_CN_NAME = '海思光电' AND LV1_CN_NAME = '海思光电' THEN 1=0
  ELSE SHIPPING_OBJECT_CODE IS NULL
  END );
UPDATE FIN_DM_OPT_FOI.DM_FOM_EMS_PO_PRICE_T  SET SHIPPING_OBJECT_CN_NAME = BUSSINESS_OBJECT_CN_NAME||'_未定义发货对象' WHERE
(CASE 
  WHEN LV0_CN_NAME = 'ICT' AND LV1_CN_NAME = '云核心网' THEN 1=0
  WHEN LV0_CN_NAME = '海思光电' AND LV1_CN_NAME = '海思光电' THEN 1=0
  ELSE SHIPPING_OBJECT_CN_NAME IS NULL
  END );
  
--替换制造对象空值字段
UPDATE FIN_DM_OPT_FOI.DM_FOM_EMS_PO_PRICE_T  SET MANUFACTURE_OBJECT_CODE = SHIPPING_OBJECT_CODE||'_未定义制造对象' WHERE 
(CASE 
  WHEN LV0_CN_NAME = 'ICT' AND LV1_CN_NAME = '云核心网' THEN 1=0
  WHEN LV0_CN_NAME = '海思光电' AND LV1_CN_NAME = '海思光电' THEN 1=0
  ELSE MANUFACTURE_OBJECT_CODE IS NULL
  END );
UPDATE FIN_DM_OPT_FOI.DM_FOM_EMS_PO_PRICE_T  SET MANUFACTURE_OBJECT_CN_NAME = SHIPPING_OBJECT_CN_NAME||'_未定义制造对象' WHERE
(CASE 
  WHEN LV0_CN_NAME = 'ICT' AND LV1_CN_NAME = '云核心网' THEN 1=0
  WHEN LV0_CN_NAME = '海思光电' AND LV1_CN_NAME = '海思光电' THEN 1=0
  ELSE MANUFACTURE_OBJECT_CN_NAME IS NULL
  END );

  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => 'EMS费用明细表空值维度字段重置完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  

  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END
$$
/

