-- Name: f_dm_foc_repl_base_related; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_repl_base_related(f_caliber_flag character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
最后修改人: 罗若文
背景描述：1.Binding组基础数据关联维度
		  2.BINDING组人民币当前成本筛选
		  3.其余度量卷积
参数描述：X_RESULT_STATUS ：是否成功
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_REPL_BASE_RELATED()
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_REPL_BASE_RELATED'; --存储过程名称
  V_VERSION_ID BIGINT ; --新版本号ID, 取自 FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S
  V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYY-MM'); --新的版本中文名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 存放当前是否已有版本号标识
  V_DIM_VERSION_ID BIGINT ; --最新的品类-专家团映射维版本号
  V_SQL TEXT;
  V_FROM_TABLE VARCHAR(100);
  V_TO_TABLE VARCHAR(100);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
   
	--判断收入时点发货时点
	IF F_CALIBER_FLAG = 'C' THEN 
		V_FROM_TABLE  :=  'FIN_DM_OPT_FOI.DM_PS_PCA_REP_SAVE_LV3_SUM_W_F' ;
		V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_SHIP_RAW_DATA_T';

	ELSIF F_CALIBER_FLAG = 'R' THEN 
		V_FROM_TABLE  :=  'FIN_DM_OPT_FOI.DM_PS_PCA_REP_SAVE_SUM_W_F' ;
		V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_INCOME_RAW_DATA_T'; 
	 
	ELSE 
		RETURN '时点错误';

	END  IF ;
	
	

  --清空目标表数据:
V_SQL := 'TRUNCATE '||V_TO_TABLE ;

EXECUTE IMMEDIATE V_SQL;
  

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除'||V_TO_TABLE||'表的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   

-- 查询该月版本是否已存在，若存在，沿用，否则新建 
SELECT COUNT(1) INTO V_CURRENT_FLAG
FROM
	FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T 
WHERE
	VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
	AND DEL_FLAG = 'N'
	AND STATUS = 1
	AND UPPER(DATA_TYPE) = 'CATEGORY';
	
 -- FLAG 不等于0，说明已有版本号，沿用        
IF V_CURRENT_FLAG <> 0 THEN 
	SELECT VERSION_ID INTO V_VERSION_ID
	FROM
		FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T 
	WHERE
		VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
		AND DEL_FLAG = 'N'
		AND STATUS = 1
		AND UPPER(DATA_TYPE) = 'CATEGORY';
ELSE
	--新版本号赋值
	SELECT NEXTVAL('FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S')
	INTO V_VERSION_ID
	FROM DUAL;
     
  --往版本信息表记录本次TOP品类版本号, 版本号为V_VERSION_ID, 依赖的品类专家维V_DIM_VERSION_ID
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   IS_RUNNING)
   VALUES
   (V_VERSION_ID,V_DIM_VERSION_ID,V_VERSION_NAME,1,'AUTO','CATEGORY',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N','Y');

END IF;
   


  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '往版本信息表记录TOP品类版本号信息, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

   
   

   
   
  --往目标表里插数
V_SQL := '
		--拿到替代关系描述不为空的维度
	WITH RAW_DIMENSION AS (
		SELECT DISTINCT PERIOD_ID ,
				LV3_PROD_LIST_CODE ,
				REPLACEMENT_GROUP_ID ,
				REPLACEMENT_DESCRIPTION
		FROM '||V_FROM_TABLE||'
		WHERE SUBSTR(PERIOD_ID,0,4) >= YEAR(CURRENT_TIMESTAMP) - 3
		AND REPLACEMENT_DESCRIPTION IS NOT NULL
		)
	,

	
	 EFFECT_REP AS (
		SELECT  DISTINCT
				LV3_PROD_LIST_CODE,
				REPLACEMENT_GROUP_ID 
		FROM '||V_FROM_TABLE||'
		WHERE SUBSTR(PERIOD_ID,0,4) = YEAR(CURRENT_TIMESTAMP)
	)
		,
		--拿到前三年的数,并关联出有值的替代关系描述即有效的替代id
	RAW_DATA AS (
		SELECT 	 
			DISTINCT
			T1.PERIOD_ID ,
			T1.LV3_PROD_LIST_CODE ,
			T1.REPLACEMENT_GROUP_ID ,
			T1.BINDING_CONFIG_ID ,
			T2.REPLACEMENT_DESCRIPTION ,
			T1.REPLACING_LY_SHIP_QTY ,
			T1.REPLACING_CY_SHIP_QTY ,
			T1.RMB_AAA_REP_BASELINE_COST_AMT ,
			T1.RMB_AAA_BINDING_CUR_COST_AMT 
			FROM '||V_FROM_TABLE||' T1	
		JOIN  RAW_DIMENSION T2
		ON T1.PERIOD_ID = T2.PERIOD_ID
		AND T1.LV3_PROD_LIST_CODE = T2.LV3_PROD_LIST_CODE
		AND T1.REPLACEMENT_GROUP_ID = T2.REPLACEMENT_GROUP_ID
		AND SUBSTR(T1.PERIOD_ID,0,4) >= YEAR(CURRENT_TIMESTAMP) - 3
		
		WHERE 1 = 1
		
		--EXISTS ( SELECT 1 FROM EFFECT_REP T3 
		--				WHERE T1.REPLACEMENT_GROUP_ID = T3.REPLACEMENT_GROUP_ID
		--				AND T1.LV3_PROD_LIST_CODE = T3.LV3_PROD_LIST_CODE
		--)
		AND  (CASE WHEN '''||F_CALIBER_FLAG||''' = ''C'' THEN REPORT_DATA_TYPE_CODE = ''101''
					WHEN '''||F_CALIBER_FLAG||''' = ''R'' THEN REPORT_DATA_TYPE_CODE IN (''501'')
					END )
			
			),

		ROWNUMBER AS (
		SELECT 	 
			PERIOD_ID ,
			LV3_PROD_LIST_CODE AS LV3_PROD_RND_TEAM_CODE,
			REPLACEMENT_GROUP_ID ,
			BINDING_CONFIG_ID ,
			REPLACEMENT_DESCRIPTION ,
			REPLACING_LY_SHIP_QTY ,
			REPLACING_CY_SHIP_QTY ,
			RMB_AAA_REP_BASELINE_COST_AMT ,
			RMB_AAA_BINDING_CUR_COST_AMT ,
			ROW_NUMBER() OVER(PARTITION BY PERIOD_ID,LV3_PROD_LIST_CODE,REPLACEMENT_GROUP_ID ORDER BY  RMB_AAA_BINDING_CUR_COST_AMT DESC) AS RANK  --按BINDING本年金额排序，后续只取1的值
			FROM RAW_DATA
			),
		COUNTDATA AS (
			SELECT 
				PERIOD_ID ,
				LV3_PROD_RND_TEAM_CODE ,
				REPLACEMENT_GROUP_ID ,
				BINDING_CONFIG_ID ,
				REPLACEMENT_DESCRIPTION ,
				SUM(REPLACING_LY_SHIP_QTY) AS REPLACING_LY_SHIP_QTY,
				SUM(REPLACING_CY_SHIP_QTY) AS REPLACING_CY_SHIP_QTY,
				AVG(RMB_AAA_REP_BASELINE_COST_AMT) AS RMB_AAA_REP_BASELINE_COST_AMT,
				--SUM(CASE WHEN RANK = 1 THEN RMB_AAA_BINDING_CUR_COST_AMT 
					--ELSE 0 END )AS RMB_AAA_BINDING_CUR_COST_AMT
				AVG(RMB_AAA_BINDING_CUR_COST_AMT)	AS  RMB_AAA_BINDING_CUR_COST_AMT
			FROM 	ROWNUMBER
			GROUP BY
				PERIOD_ID ,
				LV3_PROD_RND_TEAM_CODE ,
				REPLACEMENT_GROUP_ID ,
				BINDING_CONFIG_ID ,
				REPLACEMENT_DESCRIPTION 
				
			
			)
		INSERT INTO '||V_TO_TABLE||' (
				VERSION_ID,
				PERIOD_ID,
				LV0_PROD_RD_TEAM_CN_NAME ,
				LV1_PROD_RD_TEAM_CN_NAME ,
				LV2_PROD_RD_TEAM_CN_NAME ,
				LV3_PROD_RD_TEAM_CN_NAME ,
				LV0_PROD_RND_TEAM_CODE ,
				LV1_PROD_RND_TEAM_CODE ,
				LV2_PROD_RND_TEAM_CODE ,
				LV3_PROD_RND_TEAM_CODE ,
				REPLACEMENT_GROUP_ID ,
				BINDING_CONFIG_ID ,
				REPLACEMENT_DESCRIPTION ,
				REPLACING_LY_SHIP_QTY,
				REPLACING_CY_SHIP_QTY,
				RMB_AAA_REP_BASELINE_COST_AMT,
				RMB_AAA_BINDING_CUR_COST_AMT,
				CREATED_BY ,
				CREATION_DATE ,
				LAST_UPDATED_BY ,
				LAST_UPDATE_DATE ,
				DEL_FLAG 
				)
			SELECT 	
				DISTINCT '||V_VERSION_ID||',
				T1.PERIOD_ID ,
				T2.LV0_PROD_RD_TEAM_CN_NAME ,
				T2.LV1_PROD_RD_TEAM_CN_NAME ,
				T2.LV2_PROD_RD_TEAM_CN_NAME ,
				T2.LV3_PROD_RD_TEAM_CN_NAME ,
				T2.LV0_PROD_RND_TEAM_CODE ,
				T2.LV1_PROD_RND_TEAM_CODE ,
				T2.LV2_PROD_RND_TEAM_CODE ,
				T1.LV3_PROD_RND_TEAM_CODE ,
				T1.REPLACEMENT_GROUP_ID ,
				T1.BINDING_CONFIG_ID ,
				T1.REPLACEMENT_DESCRIPTION ,
				T1.REPLACING_LY_SHIP_QTY,
				T1.REPLACING_CY_SHIP_QTY,
				T1.RMB_AAA_REP_BASELINE_COST_AMT,
				T1.RMB_AAA_BINDING_CUR_COST_AMT,
				-1 AS CREATED_BY,
				CURRENT_TIMESTAMP AS CREATION_DATE,
				-1 AS LAST_UPDATED_BY,
				CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
				''N'' AS DEL_FLAG
				FROM COUNTDATA T1
				LEFT JOIN (SELECT DISTINCT LV0_PROD_RD_TEAM_CN_NAME ,
				LV1_PROD_RD_TEAM_CN_NAME ,
				LV2_PROD_RD_TEAM_CN_NAME ,
				LV3_PROD_RD_TEAM_CN_NAME ,
				LV0_PROD_RND_TEAM_CODE ,
				LV1_PROD_RND_TEAM_CODE ,
				LV2_PROD_RND_TEAM_CODE ,
				LV3_PROD_RND_TEAM_CODE
				FROM DMDIM.DM_DIM_PRODUCT_D WHERE SCD_ACTIVE_IND = 1)   T2 --产品标准维维表
				ON T1.LV3_PROD_RND_TEAM_CODE = T2.LV3_PROD_RND_TEAM_CODE
				WHERE T2.LV0_PROD_RND_TEAM_CODE = ''104364''
				';
	DBMS_OUTPUT.PUT_LINE(V_SQL);
	  EXECUTE IMMEDIATE V_SQL;
 
  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插数到 '||V_TO_TABLE||'表, 新版本号='||V_VERSION_ID,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  
  --3.收集统计信息
  V_SQL := 'ANALYZE '||V_TO_TABLE;
   EXECUTE IMMEDIATE V_SQL;
  
  
  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集 '||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END
$$
/

