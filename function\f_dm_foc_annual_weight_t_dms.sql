-- Name: f_dm_foc_annual_weight_t_dms; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_annual_weight_t_dms(f_industry_flag character varying, f_lev_num bigint, f_dimension_type character varying, f_keystr character varying DEFAULT NULL::character varying, f_view_flag bigint DEFAULT NULL::bigint, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$


/*
  修改时间：2024年5月6日16点11分
  修改人：TWX1139790
  背景描述：分视角权重表(年度分析-一览表)
  参数描述：x_success_flag ：是否成功
  事例    ：select opt_fcst.F_DM_FOC_ANNUAL_WEIGHT_T()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_ANNUAL_WEIGHT_T_DMS'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_KEYSTR  varchar2(50) := F_KEYSTR;  -- 密钥入参
  V_LEVEL_NUM INT := F_LEV_NUM;          -- 数字自增代表不同层级，用于循环
  V_END_NUM INT;
  V_SQL        TEXT;   --SQL逻辑
  V_FROM_TABLE VARCHAR(200);
  V_FROM1_TABLE VARCHAR(200);
  V_SPART_CODE VARCHAR(200);
  V_SPART_NAME VARCHAR(200);
  V_IN_SPART VARCHAR(200);
  V_SQL_REL_SPART TEXT;
  V_TO_TABLE VARCHAR(200);
  V_SEQUENCE VARCHAR(200);
  V_INTO_LV3_PROD VARCHAR(200);  -- LV3层级主要用于INSERT逻辑的语句
  V_LV3_PROD VARCHAR(500);  -- LV3重量级团队层级的LV3字段的数据
  V_LV2_PROD VARCHAR(500);  -- LV2重量级团队层级的LV3字段的数据
  V_LV1_PROD VARCHAR(500);  -- LV1重量级团队层级的LV3字段的数据
  V_PARENT_AMT TEXT;   -- 父层级金额
  V_GROUP_LEVEL TEXT;
  V_SQL_CEG_PARENT TEXT;  -- 专家团层级父级CODE
  V_SQL_PROD_RND_TEAM_CODE TEXT; --重量级团队CODE_CASE
  V_SQL_PROD_RND_TEAM_CN_NAME TEXT; --重量级团队NAME_CASE
  V_SQL_GROUP TEXT;
  V_DMS_CODE VARCHAR(500);  -- 所有量纲层级CODE
  V_DMS_NAME VARCHAR(500);  -- 所有量纲层级中文名称
  V_SQL_DMS_CODE TEXT;      -- 量纲层级CODE需要的SQL逻辑
  V_SQL_DMS_NAME TEXT;      -- 量纲层级中文名称需要的SQL逻辑
  V_SQL_DMS_PARENT TEXT;    -- 量纲层级的父层级CODE
  V_SQL_REL_DMS TEXT;       -- 量纲层级关联条件
  V_SQL_REL_LV3_PROD_CODE TEXT;   -- LV3层级关联条件
  V_IN_DMS TEXT;
  V_SQL_CONDITION TEXT;     -- 关联条件逻辑
  V_SQL_PARENT TEXT;  -- 不同层级的父级CODE
  V_GRANULARITY_TYPE VARCHAR(200);  -- 颗粒度类型逻辑
  V_BEGIN_NUM INT;   -- 不同成本类型，循环开始的值不一致
  V_SQL_DMS_PARENT_NAME TEXT;
  V_SQL_CEG_PARENT_NAME TEXT;
  V_SQL_AMT VARCHAR(500);   -- 除ITEM层级以外的金额不需要加解密
  V_SQL_VIEW VARCHAR(500);
  
-- 5月版本
  V_DIFF_COLUMN_CODE VARCHAR(200);
  V_DIFF_COLUMN_NAME VARCHAR(200);
  V_DIFF_COLUMN_CODE_BAK VARCHAR(200);
  V_DIFF_COLUMN_NAME_BAK VARCHAR(200);
  V_IN_DIFF_COLUMN_BAK VARCHAR(200);
  V_IN_DIFF_COLUMN VARCHAR(200);
  V_REL_DIFF_COLUMN TEXT;
  V_VERSION_TABLE VARCHAR(100);
  V_SQL_PARA VARCHAR(500);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

 -- 判断产业项目为ICT还是数字能源
  IF F_INDUSTRY_FLAG = 'I' THEN   -- ICT  
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T';
    -- 判断入参是要走通用颗粒度的逻辑还是盈利颗粒度的逻辑，处理来源表和目标表的变量
    IF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
            V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T_DMS';   -- 来源表（ITEM层级取数）
            V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_CATE_T_DMS';  -- 来源表2（其余层级取数）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_ANNUAL_WEIGHT_T'; --目标表 
    ELSE
    RETURN '入参错误，不为：D';
    END IF;
  ELSIF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源 
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T';
    -- 判断入参是要走通用颗粒度的逻辑还是盈利颗粒度的逻辑，处理来源表和目标表的变量
    IF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
            V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MID_MONTH_ITEM_T_DMS';   -- 来源表（ITEM层级取数）
            V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MID_MONTH_CATE_T_DMS';  -- 来源表2（其余层级取数）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_ANNUAL_WEIGHT_T'; --目标表 
    ELSE
    RETURN '入参错误，不为：D';
    END IF;
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 数字能源 
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T';
    -- 判断入参是要走通用颗粒度的逻辑还是盈利颗粒度的逻辑，处理来源表和目标表的变量
    IF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
            V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MID_MONTH_ITEM_T_DMS';   -- 来源表（ITEM层级取数）
            V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MID_MONTH_CATE_T_DMS';  -- 来源表2（其余层级取数）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_ANNUAL_WEIGHT_T'; --目标表 
    ELSE
    RETURN '入参错误，不为：D';
    END IF;
  END IF;
   
  --版本号赋值
  V_SQL := '
    SELECT VERSION_ID 
    FROM
        '||V_VERSION_TABLE||'
    WHERE
        DEL_FLAG = ''N''
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = ''CATEGORY''
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1';
    EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
    
  -- 判断变量赋值/删除目标表数据:
  IF V_LEVEL_NUM = 1 THEN
     V_END_NUM := 1;
     V_SQL_VIEW := ' AND VIEW_FLAG = '||F_VIEW_FLAG;
     V_SQL_AMT := 'T1.RMB_COST_AMT AS COST_AMT,';
     EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' AND GROUP_LEVEL = ''ITEM'' AND VIEW_FLAG = '||F_VIEW_FLAG;
  ELSIF V_LEVEL_NUM = 2 THEN
     V_FROM_TABLE := V_FROM1_TABLE;
     V_SQL_AMT := 'T1.RMB_COST_AMT AS COST_AMT,';
   IF F_INDUSTRY_FLAG = 'I' THEN   -- ICT 
      V_END_NUM := 12;
   ELSIF F_INDUSTRY_FLAG IN ('E','IAS') THEN   -- 202405版本新增数字能源/202407版本新增IAS
      V_END_NUM := 13;
   END IF;
     EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' AND GROUP_LEVEL <> ''ITEM''';
  END IF;

  --1.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除'||V_TO_TABLE||'表版本号为：'||V_VERSION_ID||',LEVEL_NUM为：'||V_LEVEL_NUM||'的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  --创建年度发货额临时表
    DROP TABLE IF EXISTS DMS_SUM_COST_TMP;
    CREATE TEMPORARY TABLE DMS_SUM_COST_TMP (
        PERIOD_YEAR BIGINT,
        LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV0_PROD_RD_TEAM_CN_NAME VARCHAR(200),
        LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200),
        LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200),
        LV3_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200),
        LV4_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200),
        DIMENSION_CODE VARCHAR(500),
        DIMENSION_CN_NAME VARCHAR(2000),
        DIMENSION_SUBCATEGORY_CODE VARCHAR(500),
        DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(2000),
        DIMENSION_SUB_DETAIL_CODE VARCHAR(500),
        DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(2000),
        PROD_RND_TEAM_CODE    VARCHAR(50),
        PROD_RD_TEAM_CN_NAME VARCHAR(200),
        DMS_CODE VARCHAR(500),
        DMS_CN_NAME VARCHAR(2000),
        SPART_CODE VARCHAR(200),
        SPART_CN_NAME VARCHAR(200),
        COA_CODE VARCHAR(200),
        COA_CN_NAME VARCHAR(200),
        GROUP_CODE CHARACTER VARYING(50),
        GROUP_CN_NAME CHARACTER VARYING(2000),
        GROUP_LEVEL CHARACTER VARYING(50),
        COST_AMT NUMERIC,
        PARENT_CODE VARCHAR(50),
        PARENT_CN_NAME VARCHAR(2000),
        VIEW_FLAG VARCHAR(2),
        CALIBER_FLAG VARCHAR(2),
        OVERSEA_FLAG VARCHAR(2),
        LV0_PROD_LIST_CODE VARCHAR(50),
        LV0_PROD_LIST_CN_NAME VARCHAR(200)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(PARENT_CODE,GROUP_CODE);

    --2.写入日志
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_NUM,
     F_CAL_LOG_DESC => '年度发货额临时表创建完成',
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS');  
    
    --重置变量入参
    -- 量纲颗粒度：量纲层级相关变量
        V_INTO_LV3_PROD := 'LV3_PROD_RND_TEAM_CODE,
                            LV3_PROD_RD_TEAM_CN_NAME, ';        
        V_SQL_REL_LV3_PROD_CODE := '
           AND NVL(T1.LV3_PROD_RND_TEAM_CODE,''SNULL'') = NVL(T2.LV3_PROD_RND_TEAM_CODE,''SNULL'')  ';  -- LV3层级关联条件
        V_DMS_CODE := 'DMS_CODE,
                       DIMENSION_CODE,
                       DIMENSION_SUBCATEGORY_CODE,
                       DIMENSION_SUB_DETAIL_CODE,
                       ';
        V_DMS_NAME:= 'DMS_CN_NAME,
                      DIMENSION_CN_NAME,
                      DIMENSION_SUBCATEGORY_CN_NAME,
                      DIMENSION_SUB_DETAIL_CN_NAME,';
        V_SQL_DMS_CODE := 'CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.DIMENSION_CODE 
                                WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_SUBCATEGORY_CODE
                           ELSE T1.DIMENSION_SUB_DETAIL_CODE
                           END AS DMS_CODE,
                           T1.DIMENSION_CODE,
                           T1.DIMENSION_SUBCATEGORY_CODE,
                           T1.DIMENSION_SUB_DETAIL_CODE,';  
        V_SQL_DMS_NAME := 'CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.DIMENSION_CN_NAME 
                                WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_SUBCATEGORY_CN_NAME
                           ELSE T1.DIMENSION_SUB_DETAIL_CN_NAME
                           END AS DMS_CN_NAME,
                           T1.DIMENSION_CN_NAME,
                           T1.DIMENSION_SUBCATEGORY_CN_NAME,
                           T1.DIMENSION_SUB_DETAIL_CN_NAME,';
        V_SQL_REL_DMS := '
               AND NVL(T1.DIMENSION_CODE,''SNULL'') = NVL(T2.DIMENSION_CODE,''SNULL'')
               AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''SNULL1'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''SNULL1'') 
               AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''SNULL2'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''SNULL2'') ';   --量纲层级关联条件
        V_SQL_REL_SPART := '
              AND NVL(T1.SPART_CODE,''SNULL9'') = NVL(T2.SPART_CODE,''SNULL9'') ';
        
    -- 量纲颗粒度的维度时，不需要盈利颗粒度字段
    IF F_DIMENSION_TYPE = 'D' THEN
                V_GRANULARITY_TYPE := ' ''D'' AS GRANULARITY_TYPE,';
                V_SQL_PROD_RND_TEAM_CODE := '                 
                            CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RND_TEAM_CODE
                                 WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RND_TEAM_CODE
                            ELSE T1.LV3_PROD_RND_TEAM_CODE
                            END AS PROD_RND_TEAM_CODE,';    
                V_SQL_PROD_RND_TEAM_CN_NAME:='
                            CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                                 WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                            ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                            END AS PROD_RD_TEAM_CN_NAME,';
                V_SQL_CEG_PARENT := '
                                     CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.DIMENSION_CODE 
                                          WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_SUBCATEGORY_CODE
                                          WHEN T1.VIEW_FLAG IN (''9'',''10'',''11'',''12'') THEN T1.SPART_CODE
                                     ELSE T1.DIMENSION_SUB_DETAIL_CODE
                                     END AS PARENT_CODE,';   -- 专家团层级的父层级CODE(202405新增COA层级)
                V_SQL_CEG_PARENT_NAME := '
                                     CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.DIMENSION_CN_NAME 
                                          WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_SUBCATEGORY_CN_NAME
                                          WHEN T1.VIEW_FLAG IN (''9'',''10'',''11'',''12'') THEN T1.SPART_CN_NAME
                                     ELSE T1.DIMENSION_SUB_DETAIL_CN_NAME
                                     END AS PARENT_CN_NAME,';   -- 专家团层级的父层级中文名称(202405新增COA层级)
                V_IN_DMS := '
                            T1.DMS_CODE,
                            T1.DIMENSION_CODE,
                            T1.DIMENSION_SUBCATEGORY_CODE,
                            T1.DIMENSION_SUB_DETAIL_CODE,
                            T1.DMS_CN_NAME,
                            T1.DIMENSION_CN_NAME,
                            T1.DIMENSION_SUBCATEGORY_CN_NAME,
                            T1.DIMENSION_SUB_DETAIL_CN_NAME,';
                V_SPART_CODE := 'SPART_CODE,';
                V_SPART_NAME := 'SPART_CN_NAME,';
                V_IN_SPART := 'T1.SPART_CODE,
                               T1.SPART_CN_NAME, '; 

             IF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源
                V_SQL_DMS_PARENT := '
                                 CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RND_TEAM_CODE
                                      WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RND_TEAM_CODE
                                      WHEN T1.VIEW_FLAG IN (''12'') THEN T1.COA_CODE    -- 202405月版本新增视角12
                                 ELSE T1.LV3_PROD_RND_TEAM_CODE
                                 END AS PARENT_CODE,';   -- 量纲层级的父层级CODE
                V_SQL_DMS_PARENT_NAME := '
                                 CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                                      WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                                      WHEN T1.VIEW_FLAG IN (''12'') THEN T1.COA_CN_NAME    -- 202405月版本新增视角12
                                 ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                                 END AS PARENT_CN_NAME,';   -- 量纲层级的父层级CODE
             ELSIF F_INDUSTRY_FLAG = 'I' THEN   -- ICT
                V_SQL_DMS_PARENT := '
                                 CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RND_TEAM_CODE
                                      WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RND_TEAM_CODE
                                 ELSE T1.LV3_PROD_RND_TEAM_CODE
                                 END AS PARENT_CODE,';   -- 量纲层级的父层级CODE
                V_SQL_DMS_PARENT_NAME := '
                                 CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                                      WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                                 ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                                 END AS PARENT_CN_NAME,';   -- 量纲层级的父层级CODE
             ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407新增IAS
                V_SQL_PARA := '
                                 CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RND_TEAM_CODE
                                      WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RND_TEAM_CODE
                                      WHEN T1.VIEW_FLAG IN (''12'') THEN T1.LV4_PROD_RND_TEAM_CODE    -- 202405月版本新增视角12
                                 ELSE T1.LV3_PROD_RND_TEAM_CODE
                                 ';
                V_SQL_DMS_PARENT := V_SQL_PARA||'END AS PARENT_CODE,';   -- 量纲层级的父层级CODE
                V_SQL_PROD_RND_TEAM_CODE := V_SQL_PARA||'END AS PROD_RND_TEAM_CODE,';    
                V_SQL_PARA := '
                                 CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                                      WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                                      WHEN T1.VIEW_FLAG IN (''12'') THEN T1.LV4_PROD_RD_TEAM_CN_NAME    -- 202405月版本新增视角12
                                 ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                                 ';   -- 重定义
                V_SQL_DMS_PARENT_NAME := V_SQL_PARA||'END AS PARENT_CN_NAME,';   -- 量纲层级的父层级CODE
                V_SQL_PROD_RND_TEAM_CN_NAME := V_SQL_PARA||'END AS PROD_RD_TEAM_CN_NAME,';
             END IF;
  -- 当产业项目标识为：E时，加COA层级变量/IAS时，加LV4层级变量
         IF F_INDUSTRY_FLAG = 'E' THEN 
            V_DIFF_COLUMN_CODE := 'COA_CODE,';
            V_DIFF_COLUMN_NAME := 'COA_CN_NAME,';
            V_IN_DIFF_COLUMN := 'T1.COA_CODE,T1.COA_CN_NAME,';
            V_REL_DIFF_COLUMN := ' AND NVL(T1.COA_CODE,''S2'') = NVL(T2.COA_CODE,''S2'') ';
            V_DIFF_COLUMN_CODE_BAK := V_DIFF_COLUMN_CODE;
            V_DIFF_COLUMN_NAME_BAK := V_DIFF_COLUMN_NAME;
            V_IN_DIFF_COLUMN_BAK := V_IN_DIFF_COLUMN;    -- 备份变量值
         ELSIF F_INDUSTRY_FLAG = 'IAS' THEN 
            V_DIFF_COLUMN_CODE := 'LV4_PROD_RND_TEAM_CODE,';
            V_DIFF_COLUMN_NAME := 'LV4_PROD_RD_TEAM_CN_NAME,';
            V_IN_DIFF_COLUMN := 'T1.LV4_PROD_RND_TEAM_CODE,T1.LV4_PROD_RD_TEAM_CN_NAME,';
            V_REL_DIFF_COLUMN := ' AND NVL(T1.LV4_PROD_RND_TEAM_CODE,''S2'') = NVL(T2.LV4_PROD_RND_TEAM_CODE,''S2'') ';
         ELSE NULL;
         END IF;
    ELSE
      NULL;
    END IF;   
   
    FOR LEVEL_NUM IN V_LEVEL_NUM .. V_END_NUM LOOP
    -- 重置公用变量
       V_LV1_PROD := 'T1.LV1_PROD_RND_TEAM_CODE , 
                      T1.LV1_PROD_RD_TEAM_CN_NAME ,';
       V_LV2_PROD := 'T1.LV2_PROD_RND_TEAM_CODE , 
                      T1.LV2_PROD_RD_TEAM_CN_NAME ,';
       V_LV3_PROD := 'T1.LV3_PROD_RND_TEAM_CODE , 
                      T1.LV3_PROD_RD_TEAM_CN_NAME ,';
       V_SQL_CONDITION := '';   -- 筛选条件SQL

    -- ITEM层级
       IF LEVEL_NUM = 1 THEN
           V_SQL_GROUP := ' T1.ITEM_CODE AS GROUP_CODE,
                            T1.ITEM_CN_NAME AS GROUP_CN_NAME,  
                            ''ITEM'' AS GROUP_LEVEL,';
           V_SQL_PARENT := 'T1.CATEGORY_CODE AS PARENT_CODE,
                            T1.CATEGORY_CN_NAME AS PARENT_CN_NAME,';
           V_SQL_CONDITION := ' AND T1.ONLY_ITEM_FLAG = ''N''
                                AND REVIEW_ITEM_FLAG = 0   -- 不属于底层数据审视范围的数据';   -- 筛选条件SQL
    -- 品类层级 
       ELSIF LEVEL_NUM = 2 THEN
           V_SQL_GROUP := ' T1.CATEGORY_CODE AS GROUP_CODE,
                            T1.CATEGORY_CN_NAME AS GROUP_CN_NAME,  
                            ''CATEGORY'' AS GROUP_LEVEL,';
           V_SQL_PARENT := 'T1.L4_CEG_CODE AS PARENT_CODE,
                            T1.L4_CEG_SHORT_CN_NAME AS PARENT_CN_NAME,';
    -- 模块层级 
       ELSIF LEVEL_NUM = 3 THEN
           V_SQL_GROUP := ' T1.L4_CEG_CODE AS GROUP_CODE,
                            T1.L4_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,  
                            ''MODL'' AS GROUP_LEVEL,';
           V_SQL_PARENT := 'T1.L3_CEG_CODE AS PARENT_CODE,
                            T1.L3_CEG_SHORT_CN_NAME AS PARENT_CN_NAME,';  
    -- 专家团层级 
       ELSIF LEVEL_NUM = 4 THEN
           V_SQL_GROUP := ' T1.L3_CEG_CODE AS GROUP_CODE,
                            T1.L3_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,  
                            ''CEG'' AS GROUP_LEVEL,';
           V_SQL_PARENT := V_SQL_CEG_PARENT||V_SQL_CEG_PARENT_NAME;  -- 不同颗粒度下在专家团层级往上收敛时的粒度逻辑不一致
    -- 通用颗粒度：LV3、盈利颗粒度：L2、量纲颗粒度：SUB_DETAIL (量纲子类明细)
       ELSIF LEVEL_NUM = 5 THEN
         IF F_DIMENSION_TYPE = 'D' THEN   -- 量纲颗粒度
               V_IN_SPART := 'NULL AS SPART_CODE,
                              NULL AS SPART_CN_NAME,';
               V_SQL_GROUP := ' T1.DIMENSION_SUB_DETAIL_CODE AS GROUP_CODE, 
                                T1.DIMENSION_SUB_DETAIL_CN_NAME AS GROUP_CN_NAME, 
                                ''SUB_DETAIL'' AS GROUP_LEVEL,';
               V_SQL_PARENT := 'T1.DIMENSION_SUBCATEGORY_CODE AS PARENT_CODE,
                                T1.DIMENSION_SUBCATEGORY_CN_NAME AS PARENT_CN_NAME,';  
               V_SQL_CONDITION := ' AND T1.VIEW_FLAG IN (''2'',''5'',''8'',''9'',''10'',''11'',''12'') ';  -- 只取量纲视角3/6/9的数据
         END IF;
    -- 通用颗粒度：LV2、盈利颗粒度：L1、量纲颗粒度：SUBCATEGORY (量纲子类)
       ELSIF LEVEL_NUM = 6 THEN
         IF F_DIMENSION_TYPE = 'D' THEN   -- 量纲颗粒度
               V_SQL_GROUP := ' T1.DIMENSION_SUBCATEGORY_CODE AS GROUP_CODE, 
                                T1.DIMENSION_SUBCATEGORY_CN_NAME AS GROUP_CN_NAME, 
                                ''SUBCATEGORY'' AS GROUP_LEVEL,';
               V_SQL_PARENT := 'T1.DIMENSION_CODE AS PARENT_CODE,
                                T1.DIMENSION_CN_NAME AS PARENT_CN_NAME,';  
               V_SQL_DMS_CODE := 'T1.DIMENSION_SUBCATEGORY_CODE AS DMS_CODE,
                                  T1.DIMENSION_CODE,
                                  T1.DIMENSION_SUBCATEGORY_CODE,
                                  NULL AS DIMENSION_SUB_DETAIL_CODE,';  
               V_SQL_DMS_NAME := 'T1.DIMENSION_SUBCATEGORY_CN_NAME AS DMS_CN_NAME,
                                  T1.DIMENSION_CN_NAME,
                                  T1.DIMENSION_SUBCATEGORY_CN_NAME,
                                  NULL AS DIMENSION_SUB_DETAIL_CN_NAME,';
               V_SQL_CONDITION := ' AND T1.VIEW_FLAG IN (''1'',''2'',''4'',''5'',''7'',''8'',''9'',''10'',''11'',''12'') ';  -- 只取量纲视角2/3/5/6/8/9的数据
         END IF;
    -- 通用颗粒度：LV1、盈利颗粒度：LV2、量纲颗粒度：DIMENSION (量纲)
       ELSIF LEVEL_NUM = 7 THEN
         IF F_DIMENSION_TYPE = 'D' THEN   -- 量纲颗粒度
               V_SQL_GROUP := ' T1.DIMENSION_CODE AS GROUP_CODE, 
                                T1.DIMENSION_CN_NAME AS GROUP_CN_NAME, 
                                ''DIMENSION'' AS GROUP_LEVEL,';
               V_SQL_PARENT := V_SQL_DMS_PARENT||V_SQL_DMS_PARENT_NAME;  
               V_SQL_DMS_CODE := 'T1.DIMENSION_CODE AS DMS_CODE,
                                  T1.DIMENSION_CODE,
                                  NULL AS DIMENSION_SUBCATEGORY_CODE,
                                  NULL AS DIMENSION_SUB_DETAIL_CODE,';  
               V_SQL_DMS_NAME := 'T1.DIMENSION_CN_NAME AS DMS_CN_NAME,
                                  T1.DIMENSION_CN_NAME,
                                  NULL AS DIMENSION_SUBCATEGORY_CN_NAME,
                                  NULL AS DIMENSION_SUB_DETAIL_CN_NAME,';
         END IF;
    -- 通用颗粒度：ICT、盈利颗粒度：LV1、量纲颗粒度：LV3
       ELSIF LEVEL_NUM = 8 THEN
            IF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源
               V_IN_DIFF_COLUMN := 'NULL AS COA_CODE,
                                    NULL AS COA_CN_NAME, '; 
            ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407-IAS
               V_IN_DIFF_COLUMN := 'NULL AS LV4_PROD_RND_TEAM_CODE,
                                    NULL AS LV4_PROD_RD_TEAM_CN_NAME, '; 
            END IF;
         IF F_DIMENSION_TYPE = 'D' THEN   -- 量纲颗粒度
               V_SQL_GROUP := ' T1.LV3_PROD_RND_TEAM_CODE AS GROUP_CODE, 
                                T1.LV3_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME, 
                                ''LV3'' AS GROUP_LEVEL,';
               V_SQL_PARENT := 'T1.LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                T1.LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';  
               V_SQL_DMS_CODE := 'NULL AS DMS_CODE,
                                  NULL AS DIMENSION_CODE,
                                  NULL AS DIMENSION_SUBCATEGORY_CODE,
                                  NULL AS DIMENSION_SUB_DETAIL_CODE,';  
               V_SQL_DMS_NAME := 'NULL AS DMS_CN_NAME,
                                  NULL AS DIMENSION_CN_NAME,
                                  NULL AS DIMENSION_SUBCATEGORY_CN_NAME,
                                  NULL AS DIMENSION_SUB_DETAIL_CN_NAME,';
               V_SQL_CONDITION := ' AND T1.VIEW_FLAG IN (''6'',''7'',''8'',''11'',''12'') ';  -- 只取量纲视角7/8/9的数据 
               V_SQL_PROD_RND_TEAM_CODE := 'T1.LV3_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,'; 
               V_SQL_PROD_RND_TEAM_CN_NAME := 'T1.LV3_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,';
         END IF;
    -- 盈利颗粒度：ICT、量纲颗粒度：LV2
       ELSIF LEVEL_NUM = 9 THEN
         IF F_DIMENSION_TYPE = 'D' THEN   -- 量纲颗粒度
               V_LV3_PROD := 'NULL AS LV3_PROD_RND_TEAM_CODE , 
                              NULL AS LV3_PROD_RD_TEAM_CN_NAME ,';
               V_SQL_GROUP := ' T1.LV2_PROD_RND_TEAM_CODE AS GROUP_CODE, 
                                T1.LV2_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME, 
                                ''LV2'' AS GROUP_LEVEL,';
               V_SQL_PARENT := 'T1.LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                T1.LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';  
               V_SQL_CONDITION := ' AND T1.VIEW_FLAG IN (''3'',''4'',''5'',''6'',''7'',''8'',''10'',''11'',''12'') ';  -- 只取量纲视角4/5/6/7/8/9的数据 
               V_SQL_PROD_RND_TEAM_CODE := 'T1.LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,'; 
               V_SQL_PROD_RND_TEAM_CN_NAME := 'T1.LV2_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,';
               V_SQL_DMS_CODE := 'NULL AS DMS_CODE,
                                  NULL AS DIMENSION_CODE,
                                  NULL AS DIMENSION_SUBCATEGORY_CODE,
                                  NULL AS DIMENSION_SUB_DETAIL_CODE,';  
               V_SQL_DMS_NAME := 'NULL AS DMS_CN_NAME,
                                  NULL AS DIMENSION_CN_NAME,
                                  NULL AS DIMENSION_SUBCATEGORY_CN_NAME,
                                  NULL AS DIMENSION_SUB_DETAIL_CN_NAME,';
         END IF;
    -- 量纲颗粒度：LV1
       ELSIF LEVEL_NUM = 10 THEN
           V_LV3_PROD := 'NULL AS LV3_PROD_RND_TEAM_CODE , 
                          NULL AS LV3_PROD_RD_TEAM_CN_NAME ,';
           V_LV2_PROD := 'NULL AS LV2_PROD_RND_TEAM_CODE , 
                          NULL AS LV2_PROD_RD_TEAM_CN_NAME ,';
           V_SQL_GROUP := ' T1.LV1_PROD_RND_TEAM_CODE AS GROUP_CODE, 
                            T1.LV1_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME, 
                            ''LV1'' AS GROUP_LEVEL,';
           V_SQL_PARENT := 'T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                            T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,   ';  
           V_SQL_PROD_RND_TEAM_CODE := 'T1.LV1_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,'; 
           V_SQL_PROD_RND_TEAM_CN_NAME := 'T1.LV1_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,';
           V_SQL_DMS_CODE := 'NULL AS DMS_CODE,
                              NULL AS DIMENSION_CODE,
                              NULL AS DIMENSION_SUBCATEGORY_CODE,
                              NULL AS DIMENSION_SUB_DETAIL_CODE,';  
           V_SQL_DMS_NAME := 'NULL AS DMS_CN_NAME,
                              NULL AS DIMENSION_CN_NAME,
                              NULL AS DIMENSION_SUBCATEGORY_CN_NAME,
                              NULL AS DIMENSION_SUB_DETAIL_CN_NAME,';
    -- 量纲颗粒度：LV0
       ELSIF LEVEL_NUM = 11 THEN
           V_LV3_PROD := 'NULL AS LV3_PROD_RND_TEAM_CODE , 
                          NULL AS LV3_PROD_RD_TEAM_CN_NAME ,';
           V_LV2_PROD := 'NULL AS LV2_PROD_RND_TEAM_CODE , 
                          NULL AS LV2_PROD_RD_TEAM_CN_NAME ,';
           V_LV1_PROD := 'NULL AS LV1_PROD_RND_TEAM_CODE , 
                          NULL AS LV1_PROD_RD_TEAM_CN_NAME ,';
           V_SQL_GROUP := ' T1.LV0_PROD_RND_TEAM_CODE AS GROUP_CODE, 
                            T1.LV0_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME, 
                            ''LV0'' AS GROUP_LEVEL,';
           V_SQL_PARENT := 'T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                            T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';  
           V_SQL_PROD_RND_TEAM_CODE := 'T1.LV0_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,'; 
           V_SQL_PROD_RND_TEAM_CN_NAME := 'T1.LV0_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,';
           V_SQL_DMS_CODE := 'NULL AS DMS_CODE,
                              NULL AS DIMENSION_CODE,
                              NULL AS DIMENSION_SUBCATEGORY_CODE,
                              NULL AS DIMENSION_SUB_DETAIL_CODE,';  
           V_SQL_DMS_NAME := 'NULL AS DMS_CN_NAME,
                              NULL AS DIMENSION_CN_NAME,
                              NULL AS DIMENSION_SUBCATEGORY_CN_NAME,
                              NULL AS DIMENSION_SUB_DETAIL_CN_NAME,';
                                  
    -- 量纲颗粒度：SPART(1月版本新增)
       ELSIF LEVEL_NUM = 12 THEN
           V_LV3_PROD := 'T1.LV3_PROD_RND_TEAM_CODE, 
                          T1.LV3_PROD_RD_TEAM_CN_NAME,';
           V_LV2_PROD := 'T1.LV2_PROD_RND_TEAM_CODE, 
                          T1.LV2_PROD_RD_TEAM_CN_NAME,';
           V_LV1_PROD := 'T1.LV1_PROD_RND_TEAM_CODE , 
                          T1.LV1_PROD_RD_TEAM_CN_NAME ,';
           V_SQL_GROUP := ' T1.SPART_CODE AS GROUP_CODE, 
                            T1.SPART_CN_NAME AS GROUP_CN_NAME, 
                            ''SPART'' AS GROUP_LEVEL,';
           V_SQL_PARENT := 'T1.DIMENSION_SUB_DETAIL_CODE AS PARENT_CODE,
                            T1.DIMENSION_SUB_DETAIL_CN_NAME AS PARENT_CN_NAME,';  
           V_SQL_DMS_CODE := 'T1.DIMENSION_SUB_DETAIL_CODE AS DMS_CODE,
                              T1.DIMENSION_CODE,
                              T1.DIMENSION_SUBCATEGORY_CODE,
                              T1.DIMENSION_SUB_DETAIL_CODE,';  
           V_SQL_DMS_NAME := 'T1.DIMENSION_SUB_DETAIL_CN_NAME AS DMS_CN_NAME,
                              T1.DIMENSION_CN_NAME,
                              T1.DIMENSION_SUBCATEGORY_CN_NAME,
                              T1.DIMENSION_SUB_DETAIL_CN_NAME,';
           V_SQL_PROD_RND_TEAM_CODE := 'CASE WHEN T1.VIEW_FLAG IN (''11'',''12'') THEN T1.LV3_PROD_RND_TEAM_CODE
                                             WHEN T1.VIEW_FLAG = ''10'' THEN T1.LV2_PROD_RND_TEAM_CODE
                                             WHEN T1.VIEW_FLAG = ''9'' THEN T1.LV1_PROD_RND_TEAM_CODE
                                        END AS PROD_RND_TEAM_CODE ,'; 
           V_SQL_PROD_RND_TEAM_CN_NAME := 'CASE WHEN T1.VIEW_FLAG IN (''11'',''12'') THEN T1.LV3_PROD_RD_TEAM_CN_NAME   -- 202405月版本新增视角12
                                                WHEN T1.VIEW_FLAG = ''10'' THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                                                WHEN T1.VIEW_FLAG = ''9'' THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                                           END AS PROD_RD_TEAM_CN_NAME,';
           V_SQL_CONDITION := ' AND T1.VIEW_FLAG IN (''9'',''10'',''11'',''12'') ';  -- 202405月版本新增视角12
           V_IN_SPART := 'T1.SPART_CODE,
                          T1.SPART_CN_NAME, '; 
        IF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源
           V_IN_DIFF_COLUMN := 'T1.COA_CODE,
                                T1.COA_CN_NAME,';
        ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- IAS
           V_IN_DIFF_COLUMN := 'T1.LV4_PROD_RND_TEAM_CODE,
                                T1.LV4_PROD_RD_TEAM_CN_NAME,';
           V_SQL_PROD_RND_TEAM_CODE := 'CASE WHEN T1.VIEW_FLAG = ''12'' THEN T1.LV4_PROD_RND_TEAM_CODE
                                             WHEN T1.VIEW_FLAG = ''11'' THEN T1.LV3_PROD_RND_TEAM_CODE
                                             WHEN T1.VIEW_FLAG = ''10'' THEN T1.LV2_PROD_RND_TEAM_CODE
                                             WHEN T1.VIEW_FLAG = ''9'' THEN T1.LV1_PROD_RND_TEAM_CODE
                                        END AS PROD_RND_TEAM_CODE ,'; 
           V_SQL_PROD_RND_TEAM_CN_NAME := 'CASE WHEN T1.VIEW_FLAG = ''12''  THEN T1.LV4_PROD_RD_TEAM_CN_NAME   
                                                WHEN T1.VIEW_FLAG = ''11''  THEN T1.LV3_PROD_RD_TEAM_CN_NAME   
                                                WHEN T1.VIEW_FLAG = ''10'' THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                                                WHEN T1.VIEW_FLAG = ''9'' THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                                           END AS PROD_RD_TEAM_CN_NAME,';
        END IF;
    -- 量纲颗粒度：COA(202405月版本新增)/LV4(202407月版本新增)
       ELSIF LEVEL_NUM = 13 THEN
           V_SQL_PARENT := 'T1.LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,
                            T1.LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';  
           V_SQL_DMS_CODE := 'NULL AS DMS_CODE,
                              NULL AS DIMENSION_CODE,
                              NULL AS DIMENSION_SUBCATEGORY_CODE,
                              NULL AS DIMENSION_SUB_DETAIL_CODE,';  
           V_SQL_DMS_NAME := 'NULL AS DMS_CN_NAME,
                              NULL AS DIMENSION_CN_NAME,
                              NULL AS DIMENSION_SUBCATEGORY_CN_NAME,
                              NULL AS DIMENSION_SUB_DETAIL_CN_NAME,';
           V_SQL_CONDITION := ' AND T1.VIEW_FLAG IN (''12'') ';  -- 202405月版本新增视角12
           V_IN_SPART := 'NULL AS SPART_CODE,
                          NULL AS SPART_CN_NAME, '; 
        IF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源
           V_SQL_GROUP := ' T1.COA_CODE AS GROUP_CODE, 
                            T1.COA_CN_NAME AS GROUP_CN_NAME, 
                            ''COA'' AS GROUP_LEVEL,';
        ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- IAS
           V_SQL_GROUP := ' T1.LV4_PROD_RND_TEAM_CODE AS GROUP_CODE, 
                            T1.LV4_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME, 
                            ''LV4'' AS GROUP_LEVEL,';
        END IF;
       ELSE NULL;
       END IF;

    V_SQL := '
     INSERT INTO DMS_SUM_COST_TMP(
                 PERIOD_YEAR,
                 LV0_PROD_RND_TEAM_CODE , 
                 LV0_PROD_RD_TEAM_CN_NAME ,
                 LV1_PROD_RND_TEAM_CODE , 
                 LV1_PROD_RD_TEAM_CN_NAME ,
                 LV2_PROD_RND_TEAM_CODE , 
                 LV2_PROD_RD_TEAM_CN_NAME ,
                 '||V_INTO_LV3_PROD||'
                 PROD_RND_TEAM_CODE ,
                 PROD_RD_TEAM_CN_NAME ,
                 '||V_DMS_CODE
                 ||V_DMS_NAME
                 ||V_SPART_CODE
                 ||V_SPART_NAME
                 ||V_DIFF_COLUMN_CODE
                 ||V_DIFF_COLUMN_NAME
                 ||'GROUP_CODE,
                 GROUP_CN_NAME,
                 GROUP_LEVEL,
                 COST_AMT,
                 PARENT_CODE,
                 PARENT_CN_NAME,
                 VIEW_FLAG,
                 CALIBER_FLAG,
                 OVERSEA_FLAG,
                 LV0_PROD_LIST_CODE,
                 LV0_PROD_LIST_CN_NAME
            )       
  -- 分视角下各层级CODE数据按年汇总

           SELECT T1.PERIOD_YEAR,
                  T1.LV0_PROD_RND_TEAM_CODE , 
                  T1.LV0_PROD_RD_TEAM_CN_NAME ,
                  '||V_LV1_PROD
                  ||V_LV2_PROD
                  ||V_LV3_PROD
                  ||V_SQL_PROD_RND_TEAM_CODE
                  ||V_SQL_PROD_RND_TEAM_CN_NAME
                  ||V_SQL_DMS_CODE
                  ||V_SQL_DMS_NAME
                  ||V_IN_SPART
                  ||V_IN_DIFF_COLUMN
                  ||V_SQL_GROUP
                  ||V_SQL_AMT
                  ||V_SQL_PARENT||'
                  T1.VIEW_FLAG,
                  T1.CALIBER_FLAG,
                  T1.OVERSEA_FLAG,
                  T1.LV0_PROD_LIST_CODE,
                  T1.LV0_PROD_LIST_CN_NAME
              FROM '||V_FROM_TABLE||' T1
              WHERE 1 = 1
              '||V_SQL_CONDITION||'
              '||V_SQL_VIEW;   -- 筛选条件SQL
                                      
            DBMS_OUTPUT.PUT_LINE(V_SQL);
            EXECUTE IMMEDIATE V_SQL;      
                    
   -- 写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '第'||LEVEL_NUM||'次循环，'||V_SQL_GROUP||' 对应卷积后的数据插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');                 
      DBMS_OUTPUT.PUT_LINE('第'||LEVEL_NUM||'次循环');
      END LOOP;   -- 结束循环     
      
    -- 重置变量值
    IF V_LEVEL_NUM = 1 THEN
       V_BEGIN_NUM := 1;
    ELSIF V_LEVEL_NUM = 2 THEN
          V_BEGIN_NUM := 1;
    -- 202405版本新增：不同产业标识，对应循环次数不一致
      IF F_INDUSTRY_FLAG = 'I' THEN -- ICT
          V_END_NUM := 5;
      ELSIF F_INDUSTRY_FLAG = 'E' THEN --数字能源
          V_END_NUM := 6;
      END IF;
    END IF;

  IF F_DIMENSION_TYPE = 'D' THEN
    V_IN_SPART := 'T1.SPART_CODE,
                   T1.SPART_CN_NAME, ';  -- 变量重定义
  END IF;
                                
    -- 分为3类不同层级，进行循环：1：（'ITEM','CATEGORY','MODL','CEG'）、2：（'L1','L2','DIMENSION','SUBCATEGORY'）、3：('LV3','LV2','LV1','LV0'）、4：（'SUB_DETAIL'）
       FOR GRO_NUM IN V_BEGIN_NUM .. V_END_NUM LOOP
            IF GRO_NUM = 1 THEN
                V_PARENT_AMT := '
                SUM(SS.RMB_COST_AMT) OVER(PARTITION BY SS.PERIOD_YEAR,SS.LV0_PROD_RND_TEAM_CODE,SS.LV1_PROD_RND_TEAM_CODE,SS.LV2_PROD_RND_TEAM_CODE,SS.GROUP_LEVEL,SS.PARENT_CODE,'||V_INTO_LV3_PROD||V_DMS_CODE||V_SPART_CODE||'SS.VIEW_FLAG,SS.CALIBER_FLAG,SS.OVERSEA_FLAG,SS.LV0_PROD_LIST_CODE) AS PARENT_AMT, ';
                V_GROUP_LEVEL := ' 
                WHERE GROUP_LEVEL IN (''ITEM'',''CATEGORY'',''MODL'',''CEG'') ';  
            ELSIF GRO_NUM = 2 THEN
                V_PARENT_AMT := '
                SUM(SS.RMB_COST_AMT) OVER(PARTITION BY SS.PERIOD_YEAR,SS.LV0_PROD_RND_TEAM_CODE,SS.LV1_PROD_RND_TEAM_CODE,SS.LV2_PROD_RND_TEAM_CODE,SS.GROUP_LEVEL,SS.PARENT_CODE,'||V_INTO_LV3_PROD||'SS.VIEW_FLAG,SS.CALIBER_FLAG,SS.OVERSEA_FLAG,SS.LV0_PROD_LIST_CODE) AS PARENT_AMT, ';
                V_GROUP_LEVEL := ' 
                WHERE GROUP_LEVEL IN (''L1'',''L2'',''DIMENSION'',''SUBCATEGORY'') ';   
            ELSIF GRO_NUM = 3 THEN
                V_PARENT_AMT := '
                SUM(SS.RMB_COST_AMT) OVER(PARTITION BY SS.PERIOD_YEAR,SS.GROUP_LEVEL,SS.PARENT_CODE,SS.VIEW_FLAG,SS.CALIBER_FLAG,SS.OVERSEA_FLAG,SS.LV0_PROD_LIST_CODE) AS PARENT_AMT, ';
                V_GROUP_LEVEL := ' 
                WHERE GROUP_LEVEL IN (''LV4'',''LV3'',''LV2'',''LV1'',''LV0'') ';     -- 202407新增LV4层级
            ELSIF GRO_NUM = 4 THEN
                V_PARENT_AMT := '
                SUM(SS.RMB_COST_AMT) OVER(PARTITION BY SS.PERIOD_YEAR,SS.LV0_PROD_RND_TEAM_CODE,SS.LV1_PROD_RND_TEAM_CODE,SS.LV2_PROD_RND_TEAM_CODE,SS.DIMENSION_SUBCATEGORY_CODE,SS.DIMENSION_CODE,SS.GROUP_LEVEL,SS.PARENT_CODE,'||V_INTO_LV3_PROD||'SS.VIEW_FLAG,SS.CALIBER_FLAG,SS.OVERSEA_FLAG,SS.LV0_PROD_LIST_CODE) AS PARENT_AMT, ';
                V_GROUP_LEVEL := ' 
                WHERE GROUP_LEVEL IN (''SUB_DETAIL'') ';   
            ELSIF GRO_NUM = 5 THEN
                V_PARENT_AMT := '
                SUM(SS.RMB_COST_AMT) OVER(PARTITION BY SS.PERIOD_YEAR,SS.LV0_PROD_RND_TEAM_CODE,SS.LV1_PROD_RND_TEAM_CODE,SS.LV2_PROD_RND_TEAM_CODE,SS.GROUP_LEVEL,SS.PARENT_CODE,'||V_INTO_LV3_PROD||V_DMS_CODE||'SS.VIEW_FLAG,SS.CALIBER_FLAG,SS.OVERSEA_FLAG,SS.LV0_PROD_LIST_CODE) AS PARENT_AMT, ';
                V_GROUP_LEVEL := ' 
                WHERE GROUP_LEVEL IN (''SPART'') ';  
            ELSIF GRO_NUM = 6 THEN  -- 202405新增COA层级
                V_PARENT_AMT := '
                SUM(SS.RMB_COST_AMT) OVER(PARTITION BY SS.PERIOD_YEAR,SS.LV0_PROD_RND_TEAM_CODE,SS.LV1_PROD_RND_TEAM_CODE,SS.LV2_PROD_RND_TEAM_CODE,SS.GROUP_LEVEL,SS.PARENT_CODE,'||V_INTO_LV3_PROD||'SS.VIEW_FLAG,SS.CALIBER_FLAG,SS.OVERSEA_FLAG,SS.LV0_PROD_LIST_CODE) AS PARENT_AMT, ';
                V_GROUP_LEVEL := ' 
                WHERE GROUP_LEVEL IN (''COA'') ';  
            END IF;        
                                
  -- 插入各视角、各层级的权重数据到分视角权重表(年度分析-一览表)
    V_SQL := '
    INSERT INTO '||V_TO_TABLE||'(
             VERSION_ID,
             PERIOD_YEAR,
             PERIOD_YEAR_TYPE,
             PROD_RND_TEAM_CODE,
             PROD_RND_TEAM_CN_NAME,
             '||V_DMS_CODE
             ||V_DMS_NAME
             ||V_SPART_CODE
             ||V_SPART_NAME
             ||V_DIFF_COLUMN_CODE_BAK
             ||V_DIFF_COLUMN_NAME_BAK
             ||'GROUP_CODE,
             GROUP_CN_NAME,
             GROUP_LEVEL,
             WEIGHT_RATE,
             ABSOLUTE_WEIGHT, 
             RMB_COST_AMT,
             ABSOLUTE_PARENT_AMT,
             PARENT_CODE,
             PARENT_CN_NAME,
             CREATED_BY,
             CREATION_DATE,
             LAST_UPDATED_BY,
             LAST_UPDATE_DATE,
             DEL_FLAG,
             VIEW_FLAG,
             APPEND_FLAG,
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME
        )    
    -- 生成连续年份, 三年前的年份+当年（若当年为1月时，当年年份不含）   
  WITH PERIOD_YEAR_TMP AS(     
           SELECT CAST(GENERATE_SERIES(YEAR(CURRENT_TIMESTAMP)-3,
                              YEAR(CURRENT_TIMESTAMP),
                              1) AS BIGINT) AS PERIOD_YEAR FROM DUAL
                   )    
   , DECRYPT_COST_TMP AS(
            SELECT T1.PERIOD_YEAR,
                    T1.LV0_PROD_RND_TEAM_CODE , 
                    T1.LV0_PROD_RD_TEAM_CN_NAME ,
                    T1.LV1_PROD_RND_TEAM_CODE , 
                    T1.LV1_PROD_RD_TEAM_CN_NAME ,
                    T1.LV2_PROD_RND_TEAM_CODE , 
                    T1.LV2_PROD_RD_TEAM_CN_NAME ,
                    '||V_INTO_LV3_PROD||'
                    T1.PROD_RND_TEAM_CODE ,
                    T1.PROD_RD_TEAM_CN_NAME ,
                    '||V_DMS_CODE
                    ||V_DMS_NAME
                    ||V_SPART_CODE
                    ||V_SPART_NAME
                    ||V_DIFF_COLUMN_CODE
                    ||V_DIFF_COLUMN_NAME
                    ||'T1.GROUP_CODE,
                    T1.GROUP_CN_NAME,
                    T1.GROUP_LEVEL,
                    SUM(T1.COST_AMT) AS RMB_COST_AMT,
                    T1.PARENT_CODE,
                    T1.PARENT_CN_NAME,
                    T1.VIEW_FLAG,
                    T1.CALIBER_FLAG,
                    T1.OVERSEA_FLAG,
                    T1.LV0_PROD_LIST_CODE,
                    T1.LV0_PROD_LIST_CN_NAME
                FROM DMS_SUM_COST_TMP T1
                '||V_GROUP_LEVEL||'
                AND COST_AMT <> 0     -- 剔除金额为0的数据
                GROUP BY T1.PERIOD_YEAR,
                         T1.LV0_PROD_RND_TEAM_CODE , 
                         T1.LV0_PROD_RD_TEAM_CN_NAME ,
                         T1.LV1_PROD_RND_TEAM_CODE , 
                         T1.LV1_PROD_RD_TEAM_CN_NAME ,
                         T1.LV2_PROD_RND_TEAM_CODE , 
                         T1.LV2_PROD_RD_TEAM_CN_NAME ,
                         '||V_INTO_LV3_PROD||'
                         T1.PROD_RND_TEAM_CODE ,
                         T1.PROD_RD_TEAM_CN_NAME ,
                         '||V_DMS_CODE
                         ||V_DMS_NAME
                         ||V_SPART_CODE
                         ||V_SPART_NAME
                         ||V_DIFF_COLUMN_CODE
                         ||V_DIFF_COLUMN_NAME
                         ||'GROUP_CODE,
                         GROUP_CN_NAME,
                         GROUP_LEVEL,
                         PARENT_CODE,
                         PARENT_CN_NAME,
                         VIEW_FLAG,
                         CALIBER_FLAG,
                         OVERSEA_FLAG,
                         LV0_PROD_LIST_CODE,
                         LV0_PROD_LIST_CN_NAME)
    -- 生成连续年的发散维
 , CONTIN_DIM_TMP AS(
       SELECT DISTINCT T2.PERIOD_YEAR,
              T1.LV0_PROD_RND_TEAM_CODE , 
              T1.LV0_PROD_RD_TEAM_CN_NAME ,
              T1.LV1_PROD_RND_TEAM_CODE , 
              T1.LV1_PROD_RD_TEAM_CN_NAME ,
              T1.LV2_PROD_RND_TEAM_CODE , 
              T1.LV2_PROD_RD_TEAM_CN_NAME ,
              '||V_INTO_LV3_PROD||'
              T1.PROD_RND_TEAM_CODE ,
              T1.PROD_RD_TEAM_CN_NAME ,
              '||V_IN_DMS
              ||V_IN_SPART
              ||V_IN_DIFF_COLUMN
              ||'T1.GROUP_CODE,
              T1.GROUP_CN_NAME,
              T1.GROUP_LEVEL,
              T1.PARENT_CODE,
              T1.PARENT_CN_NAME,
              T1.VIEW_FLAG,
              T1.CALIBER_FLAG,
              T1.OVERSEA_FLAG,
              T1.LV0_PROD_LIST_CODE,
              T1.LV0_PROD_LIST_CN_NAME
           FROM DECRYPT_COST_TMP T1,PERIOD_YEAR_TMP T2
           '||V_GROUP_LEVEL||'
  )
    -- 各视角下各层级的权重逻辑计算
                    SELECT '
                           ||V_VERSION_ID||' AS VERSION_ID,
                           T1.PERIOD_YEAR,
                           T1.PERIOD_YEAR AS PERIOD_YEAR_TYPE, 
                           T1.PROD_RND_TEAM_CODE ,
                           T1.PROD_RD_TEAM_CN_NAME ,
                           '||V_IN_DMS
                           ||V_IN_SPART
                           ||V_IN_DIFF_COLUMN_BAK
                           ||'T1.GROUP_CODE,
                           T1.GROUP_CN_NAME,
                           T1.GROUP_LEVEL,
                           DECODE(T2.RMB_COST_AMT, NULL, 0, T2.RMB_COST_AMT / NULLIF(T2.PARENT_AMT,0)) AS WEIGHT_RATE,  -- 相对权重值，原始有数据，则计算，维度补齐的情况，则赋0
                           DECODE(T2.RMB_COST_AMT, NULL, 0, T2.RMB_COST_AMT / NULLIF(T2.ABSOLUTE_PARENT_AMT,0)) AS ABSOLUTE_WEIGHT,  -- 绝对权重值，原始有数据，则计算，维度补齐的情况，则赋0
                           DECODE(T1.GROUP_LEVEL, ''ITEM'', NULL, T2.RMB_COST_AMT) AS RMB_COST_AMT,
                           T2.ABSOLUTE_PARENT_AMT,
                           T1.PARENT_CODE,
                           T1.PARENT_CN_NAME,
                           -1 AS CREATED_BY,
                           CURRENT_TIMESTAMP AS CREATION_DATE,
                           -1 AS LAST_UPDATED_BY,
                           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                           ''N'' AS DEL_FLAG,
                           T1.VIEW_FLAG,
                           DECODE(T2.RMB_COST_AMT, NULL, ''Y'',''N'') AS APPEND_FLAG,  --补齐标识：Y为补齐，N为原始
                           T1.CALIBER_FLAG,
                           T1.OVERSEA_FLAG,
                           T1.LV0_PROD_LIST_CODE,
                           T1.LV0_PROD_LIST_CN_NAME
                        FROM CONTIN_DIM_TMP T1
                        LEFT JOIN(
                                    SELECT SS.PERIOD_YEAR,
                                           SS.LV0_PROD_RND_TEAM_CODE , 
                                           SS.LV1_PROD_RND_TEAM_CODE , 
                                           SS.LV2_PROD_RND_TEAM_CODE , 
                                           '||V_INTO_LV3_PROD
                                           ||V_DMS_CODE
                                           ||V_SPART_CODE
                                           ||V_DIFF_COLUMN_CODE
                                           ||'SS.GROUP_CODE,
                                           SS.GROUP_LEVEL,
                                           SS.RMB_COST_AMT,
                                           '||V_PARENT_AMT||'
                                           SUM(SS.RMB_COST_AMT) OVER(PARTITION BY SS.PERIOD_YEAR,SS.VIEW_FLAG,SS.CALIBER_FLAG,SS.OVERSEA_FLAG,SS.LV0_PROD_LIST_CODE,SS.GROUP_LEVEL) AS ABSOLUTE_PARENT_AMT,
                                           SS.PARENT_CODE,
                                           SS.VIEW_FLAG,
                                           SS.CALIBER_FLAG,
                                           SS.OVERSEA_FLAG,
                                           SS.LV0_PROD_LIST_CODE
                                   FROM DECRYPT_COST_TMP SS
                                   '||V_GROUP_LEVEL||'        
                                   ) T2
                           ON NVL(T1.LV0_PROD_RND_TEAM_CODE,''SNULL'') = NVL(T2.LV0_PROD_RND_TEAM_CODE,''SNULL'')
                           AND NVL(T1.LV1_PROD_RND_TEAM_CODE,''SNULL'') = NVL(T2.LV1_PROD_RND_TEAM_CODE,''SNULL'')
                           AND NVL(T1.LV2_PROD_RND_TEAM_CODE,''SNULL'') = NVL(T2.LV2_PROD_RND_TEAM_CODE,''SNULL'')
                           '||V_SQL_REL_LV3_PROD_CODE
                           ||V_SQL_REL_DMS
                           ||V_SQL_REL_SPART
                           ||V_REL_DIFF_COLUMN||' 
                           AND T1.GROUP_CODE = T2.GROUP_CODE
                           AND T1.GROUP_LEVEL =T2.GROUP_LEVEL
                           AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
                           AND T1.VIEW_FLAG = T2.VIEW_FLAG
                           AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
                           AND T1.PARENT_CODE = T2.PARENT_CODE
                           AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
                           AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
                           WHERE T1.PERIOD_YEAR >= YEAR(CURRENT_TIMESTAMP)-2
                                            ';
            DBMS_OUTPUT.PUT_LINE(V_SQL);
            EXECUTE IMMEDIATE V_SQL;   
                                            
    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '第'||GRO_NUM||'次循环，插入版本号为：'||V_VERSION_ID||' 的权重数据到'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');    
    DBMS_OUTPUT.PUT_LINE('第'||GRO_NUM||'次循环');
    END LOOP;
     
  IF F_LEV_NUM = 2 THEN
 -- 将计算绝对权重分母值的数据插入数据表，方便汇总组合计算时取用
   DELETE FROM FIN_DM_OPT_FOI.DM_FOC_ABSOLUTE_AMT_T
      WHERE GRANULARITY_TYPE = F_DIMENSION_TYPE AND VERSION_ID = V_VERSION_ID AND COST_TYPE = 'P' AND INDUSTRY_TYPE = F_INDUSTRY_FLAG;
 
 V_SQL := '
   INSERT INTO FIN_DM_OPT_FOI.DM_FOC_ABSOLUTE_AMT_T(
        VERSION_ID,
        PERIOD_YEAR,
        VIEW_FLAG,
        CALIBER_FLAG,
        OVERSEA_FLAG,
        LV0_PROD_LIST_CODE,
        GRANULARITY_TYPE,
        ABSOLUTE_PARENT_AMT,
        COST_TYPE,
        CREATED_BY,
        CREATION_DATE,
        LAST_UPDATED_BY,
        LAST_UPDATE_DATE,
        DEL_FLAG,
        INDUSTRY_TYPE)
 WITH DIS_VIEW_AMT_TMP AS(
     SELECT DISTINCT VERSION_ID,
            PERIOD_YEAR,
            VIEW_FLAG,
            CALIBER_FLAG,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            ABSOLUTE_PARENT_AMT,
            DECODE(APPEND_FLAG, ''N'',1,2) AS APD_FLAG   -- 补齐标识为：N的时候，给1，为：Y时给2
          FROM '||V_TO_TABLE||'
          WHERE VERSION_ID = '||V_VERSION_ID||'
      )
     SELECT VERSION_ID,
            PERIOD_YEAR,
            VIEW_FLAG,
            CALIBER_FLAG,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            '||V_GRANULARITY_TYPE||'
            ABSOLUTE_PARENT_AMT,    
            ''P'' AS COST_TYPE, 
            -1 AS CREATED_BY,
            CURRENT_TIMESTAMP AS CREATION_DATE,
            -1 AS LAST_UPDATED_BY,
            CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
            ''N'' AS DEL_FLAG,
            '''||F_INDUSTRY_FLAG||''' AS INDUSTRY_TYPE
         FROM (
           SELECT VERSION_ID,
                  PERIOD_YEAR,
                  VIEW_FLAG,
                  CALIBER_FLAG,
                  OVERSEA_FLAG,
                  LV0_PROD_LIST_CODE,
                  ABSOLUTE_PARENT_AMT,
                  APD_FLAG,
                  DENSE_RANK() OVER(PARTITION BY PERIOD_YEAR,VIEW_FLAG,CALIBER_FLAG,OVERSEA_FLAG,LV0_PROD_LIST_CODE ORDER BY APD_FLAG) AS RNK_FLAG 
                        -- 根据APD_FLAG排序，当出现2条数据时，补齐标识为：N的数据排1，否则为：Y的数据排1
               FROM DIS_VIEW_AMT_TMP
              ) T
          WHERE RNK_FLAG = 1'
          ;

            EXECUTE IMMEDIATE V_SQL;   
          
    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除表：DM_FOC_ABSOLUTE_AMT_T中颗粒度为：'||F_DIMENSION_TYPE||' 的数据，并重新插入成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');            
    
  ELSE NULL;
  END IF;
  
    --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
 
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END
$$
/

