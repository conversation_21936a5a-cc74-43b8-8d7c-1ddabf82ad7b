-- Name: f_dm_foc_dms_item_append_combine; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_dms_item_append_combine(f_industry_flag character varying, f_caliber_flag character varying, f_oversea_flag character varying, f_lv0_prod_list_code character varying, f_view_flag character varying, f_dimension_type character varying, f_keystr character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*

背景描述：合并实际数预测数补齐结果;预测数补齐结果插入新表 9-11 
参数描述:f_dimension_type : 维度类型(D:量纲颗粒度), x_result_status :是否成功
来源表:
目标表:FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_202401(量纲颗粒度)
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_DMS_ITEM_APPEND_COMBINE()
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_DMS_ITEM_APPEND_COMBINE'; --存储过程名称
  V_STEP_MUM   BIGINT := 0;   --步骤号
  V_VERSION_ID BIGINT; --版本号, 取自 FIN_DM_OPT_FOI.DM_FOC_BASE_DETAIL_ITEM_T表补齐实际数的版本号
  V_FCST_PERIOD BIGINT := CAST(TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMM') AS BIGINT);  --当前系统预测月

  -- 7月版本需求新增
  V_SQL  TEXT; -- SQL逻辑
  V_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_LV4_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_L1_NAME VARCHAR(200);
  V_L2_NAME VARCHAR(200);
  V_IN_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_LV4_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_L1_NAME VARCHAR(200);
  V_IN_L2_NAME VARCHAR(200);

  V_FROM_TABLE VARCHAR(500); -- 来源表
  V_TO_TABLE1 VARCHAR(500); -- 目标表1,加密预测结果插入原结果表
  V_TO_TABLE2 VARCHAR(500); -- 目标表2,实际预测结果合并
  
   -- 9月版本需求新增
  V_DIMENSION_CODE VARCHAR(100);
  V_DIMENSION_CN_NAME VARCHAR(100);
  V_DIMENSION_EN_NAME VARCHAR(100);
  V_DIMENSION_SUBCATEGORY_CODE VARCHAR(100);
  V_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(100);
  V_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(100);
  V_DIMENSION_SUB_DETAIL_CODE VARCHAR(100);
  V_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(100);
  V_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(100);
  V_IN_DIMENSION_CODE VARCHAR(100);
  V_IN_DIMENSION_CN_NAME VARCHAR(100);
  V_IN_DIMENSION_EN_NAME VARCHAR(100);
  V_IN_DIMENSION_SUBCATEGORY_CODE VARCHAR(100);
  V_IN_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(100);
  V_IN_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(100);
  V_IN_DIMENSION_SUB_DETAIL_CODE VARCHAR(100);
  V_IN_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(100);
  V_IN_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(100);

  --202401月版本新增spart层
  V_SPART_CODE VARCHAR(200);
  V_SPART_CN_NAME VARCHAR(200);
  V_IN_SPART_CODE VARCHAR(200);
  V_IN_SPART_CN_NAME VARCHAR(200);
  V_INSERT_SPART_CODE VARCHAR(200);
  V_INSERT_SPART_CN_NAME VARCHAR(200);
  
  --202405月版本新增COA层
  V_COA_CODE VARCHAR(100);
  V_COA_CN_NAME VARCHAR(100);
  V_IN_COA_CODE VARCHAR(100);
  V_IN_COA_CN_NAME VARCHAR(100);
  V_INSERT_COA_CODE VARCHAR(200);
  V_INSERT_COA_CN_NAME VARCHAR(200);
  V_VERSION_TABLE VARCHAR(100);

BEGIN
  X_RESULT_STATUS = '1';

  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行,颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG);

  --对入参合法性判断
  IF F_DIMENSION_TYPE NOT IN ('D') THEN
     DBMS_OUTPUT.PUT_LINE ('入参有误，请重新输入！');
     RETURN '0';
  END IF;

  IF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'I' THEN -- 量纲颗粒度
     V_TO_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_FCST_T';--目标表1  --传入加密后的预测数据
     V_TO_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS'; --目标表2 --合并未加密的实际预测数据
	 
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'E' THEN -- 量纲颗粒度
     V_TO_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_FCST_T';--目标表1  --传入加密后的预测数据
     V_TO_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS'; --目标表2 --合并未加密的实际预测数据

  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 量纲颗粒度
     V_TO_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_FCST_T';--目标表1  --传入加密后的预测数据
     V_TO_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS'; --目标表2 --合并未加密的实际预测数据	 
	 
  ELSE
    NULL;
  END IF;

--判断版本表
  IF F_INDUSTRY_FLAG = 'I' THEN 
	--新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
	
  ELSIF F_INDUSTRY_FLAG = 'E' THEN 
	 
	 --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_ENERGY_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 

  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN 
	 
	 --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_IAS_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
		
  ELSE 
     NULL ;
	 
  END IF;

  --1.删除目标表1中预测数据
  V_SQL := 'DELETE FROM '||V_TO_TABLE1||' T WHERE T.SCENARIO_FLAG = ''Y'' AND T.VIEW_FLAG = '''||f_view_flag||''' AND CALIBER_FLAG = '''||f_caliber_flag||''' AND OVERSEA_FLAG = '''||f_oversea_flag||''' AND LV0_PROD_LIST_CODE = '''||f_lv0_prod_list_code||'''';
  EXECUTE IMMEDIATE V_SQL;
  
    --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除'||V_TO_TABLE1||'预测数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  
 --1.删除目标表2中实际预测数据
  V_SQL := 'DELETE FROM '||V_TO_TABLE2||' T WHERE  T.VIEW_FLAG = '''||f_view_flag||''' AND CALIBER_FLAG = '''||f_caliber_flag||''' AND OVERSEA_FLAG = '''||f_oversea_flag||''' AND LV0_PROD_LIST_CODE = '''||f_lv0_prod_list_code||'''';
  EXECUTE IMMEDIATE V_SQL;
  
  --1.写入日志
   V_STEP_MUM := V_STEP_MUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除'||V_TO_TABLE2||'实际预测数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');



  IF F_INDUSTRY_FLAG = 'I' AND F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_09CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_09CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_09CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_09RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_09RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_09RO'; --R/O目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_10CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_10CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_10CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_10RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_10RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_10RO'; --R/O目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_11CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_11CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_11CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_11RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_11RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_11RO'; --R/O目标表
	
	--增加对ICT 12视角的判断
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '12'  THEN
    RETURN 'SUCCESS'; 
	
	ELSIF F_INDUSTRY_FLAG = 'E' AND F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_09CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_09CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_09CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_09RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_09RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_09RO'; --R/O目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_10CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_10CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_10CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_10RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_10RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_10RO'; --R/O目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_11CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_11CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_11CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_11RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_11RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_11RO'; --R/O目标表
    ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_12CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_12CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_12CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_12RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_12RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_12RO'; --R/O目标表	
	 
	
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_09CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_09CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_09CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_09RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_09RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_09RO'; --R/O目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_10CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_10CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_10CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_10RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_10RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_10RO'; --R/O目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_11CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_11CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_11CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_11RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_11RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_11RO'; --R/O目标表
    ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_12CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_12CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_12CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_12RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_12RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_12RO'; --R/O目标表
	
	
   END IF;
   
    --7月版本需求新增
    --重置变量入参
    V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
	V_LV4_PROD_RND_TEAM_CODE := 'LV4_PROD_RND_TEAM_CODE,';
    V_LV4_PROD_RD_TEAM_CN_NAME := 'LV4_PROD_RD_TEAM_CN_NAME,';
    V_L1_NAME := 'L1_NAME,';
    V_L2_NAME := 'L2_NAME,';
    V_IN_LV3_PROD_RND_TEAM_CODE := 'T1.LV3_PROD_RND_TEAM_CODE,';
    V_IN_LV3_PROD_RD_TEAM_CN_NAME :='T1.LV3_PROD_RD_TEAM_CN_NAME,';
	V_IN_LV4_PROD_RND_TEAM_CODE := 'T1.LV4_PROD_RND_TEAM_CODE,';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME :='T1.LV4_PROD_RD_TEAM_CN_NAME,';
    V_IN_L1_NAME := 'T1.L1_NAME,';
    V_IN_L2_NAME := 'T1.L2_NAME,';

    --9月版本需求新增量纲
    V_DIMENSION_CODE := 'DIMENSION_CODE,';
    V_DIMENSION_CN_NAME := 'DIMENSION_CN_NAME,';
    V_DIMENSION_EN_NAME := 'DIMENSION_EN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_EN_NAME := 'DIMENSION_SUBCATEGORY_EN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME := 'DIMENSION_SUB_DETAIL_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_EN_NAME := 'DIMENSION_SUB_DETAIL_EN_NAME,';
    V_IN_DIMENSION_CODE := 'T1.DIMENSION_CODE,';
    V_IN_DIMENSION_CN_NAME := 'T1.DIMENSION_CN_NAME,';
    V_IN_DIMENSION_EN_NAME := 'T1.DIMENSION_EN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_CODE := 'T1.DIMENSION_SUBCATEGORY_CODE,';
    V_IN_DIMENSION_SUBCATEGORY_CN_NAME := 'T1.DIMENSION_SUBCATEGORY_CN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_EN_NAME := 'T1.DIMENSION_SUBCATEGORY_EN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_CODE := 'T1.DIMENSION_SUB_DETAIL_CODE,';
    V_IN_DIMENSION_SUB_DETAIL_CN_NAME := 'T1.DIMENSION_SUB_DETAIL_CN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_EN_NAME := 'T1.DIMENSION_SUB_DETAIL_EN_NAME,';

	--202401月版本新增spart层
	V_SPART_CODE := 'SPART_CODE,';
	V_SPART_CN_NAME  := 'SPART_CN_NAME,';
	V_IN_SPART_CODE  := 'T1.SPART_CODE,';
	V_IN_SPART_CN_NAME := 'T1.SPART_CN_NAME,';
	V_INSERT_SPART_CODE  := ' AND NVL(T1.SPART_CODE,2) = NVL(T2.SPART_CODE,2)';
	
	
	--202405月版本新增coa层
	V_COA_CODE := 'COA_CODE,';
	V_COA_CN_NAME  := 'COA_CN_NAME,';
	V_IN_COA_CODE  := 'T1.COA_CODE,';
	V_IN_COA_CN_NAME := 'T1.COA_CN_NAME,';
	V_INSERT_COA_CODE  := ' AND NVL(T1.COA_CODE,2) = NVL(T2.COA_CODE,2)';

    --通用颗粒度的维度时，不需要L1、L2字段和所有量纲字段
    IF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG IN ('E','I') THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';


       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
	   V_SPART_CODE := '';
	   V_SPART_CN_NAME  := '';
	   V_IN_SPART_CODE  := '';
	   V_IN_SPART_CN_NAME := '';
	   V_INSERT_SPART_CODE := '';
	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';
	   
	   V_LV4_PROD_RND_TEAM_CODE := '';
       V_LV4_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV4_PROD_RND_TEAM_CODE := '';
       V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';

	   
	ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG IN ('IAS') THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';


       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
	   V_SPART_CODE := '';
	   V_SPART_CN_NAME  := '';
	   V_IN_SPART_CODE  := '';
	   V_IN_SPART_CN_NAME := '';
	   V_INSERT_SPART_CODE := '';
	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';
	   
	   
    --盈利颗粒度的维度时，不需要LV3字段和所有量纲字段
  ELSIF F_DIMENSION_TYPE = 'P' THEN
       V_LV3_PROD_RND_TEAM_CODE := '';
       V_LV3_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV3_PROD_RND_TEAM_CODE := '';
       V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';

       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_SPART_CODE := '';
	   V_SPART_CN_NAME  := '';
	   V_IN_SPART_CODE  := '';
	   V_IN_SPART_CN_NAME := '';
	   V_INSERT_SPART_CODE := '';
	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';
	   
	   V_LV4_PROD_RND_TEAM_CODE := '';
       V_LV4_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV4_PROD_RND_TEAM_CODE := '';
       V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
   
    --量纲颗粒度的维度时，不需要L1、L2字段
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'I' THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';

	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';
	   
	   V_LV4_PROD_RND_TEAM_CODE := '';
       V_LV4_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV4_PROD_RND_TEAM_CODE := '';
       V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';

   ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'E' THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
	   
	   
	   V_LV4_PROD_RND_TEAM_CODE := '';
       V_LV4_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV4_PROD_RND_TEAM_CODE := '';
       V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';

	   
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'IAS' THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';

	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';
	      
	   
	   
    ELSE
      NULL;
    END IF;

  --2.加密预测数数据插入原表
  V_SQL :=
  'INSERT INTO '||V_TO_TABLE1||'
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE,
     LV2_PROD_RD_TEAM_CN_NAME,'||
     V_LV3_PROD_RND_TEAM_CODE ||
     V_LV3_PROD_RD_TEAM_CN_NAME ||
	 V_LV4_PROD_RND_TEAM_CODE ||
     V_LV4_PROD_RD_TEAM_CN_NAME ||
     V_L1_NAME ||
     V_L2_NAME ||
     V_DIMENSION_CODE ||
     V_DIMENSION_CN_NAME ||
     V_DIMENSION_EN_NAME||
     V_DIMENSION_SUBCATEGORY_CODE ||
     V_DIMENSION_SUBCATEGORY_CN_NAME ||
     V_DIMENSION_SUBCATEGORY_EN_NAME||
     V_DIMENSION_SUB_DETAIL_CODE ||
     V_DIMENSION_SUB_DETAIL_CN_NAME ||
     V_DIMENSION_SUB_DETAIL_EN_NAME ||
	 V_SPART_CODE||
	 V_SPART_CN_NAME||
	 V_COA_CODE||
	 V_COA_CN_NAME||'
     L3_CEG_CODE,
     L3_CEG_CN_NAME,
     L3_CEG_SHORT_CN_NAME,
     L4_CEG_CODE,
     L4_CEG_CN_NAME,
     L4_CEG_SHORT_CN_NAME,
     CATEGORY_CODE,
     CATEGORY_CN_NAME,
     ITEM_CODE,
     ITEM_CN_NAME,
     SHIP_QUANTITY,
     RMB_COST_AMT,
     RMB_AVG_AMT,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     APPEND_FLAG,
     SCENARIO_FLAG,
     VIEW_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME,
     LV0_PROD_LIST_EN_NAME)
  SELECT '||V_VERSION_ID||' AS VERSION_ID,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.LV0_PROD_RND_TEAM_CODE,
           T1.LV0_PROD_RD_TEAM_CN_NAME,
           T1.LV1_PROD_RND_TEAM_CODE,
           T1.LV1_PROD_RD_TEAM_CN_NAME,
           T1.LV2_PROD_RND_TEAM_CODE,
           T1.LV2_PROD_RD_TEAM_CN_NAME,'||
           V_IN_LV3_PROD_RND_TEAM_CODE ||
           V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
		   V_IN_LV4_PROD_RND_TEAM_CODE ||
           V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
           V_IN_L1_NAME ||
           V_IN_L2_NAME ||
           V_IN_DIMENSION_CODE ||
           V_IN_DIMENSION_CN_NAME ||
           V_IN_DIMENSION_EN_NAME||
           V_IN_DIMENSION_SUBCATEGORY_CODE ||
           V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
           V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
           V_IN_DIMENSION_SUB_DETAIL_CODE ||
           V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
           V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
		   V_IN_SPART_CODE||
		   V_IN_SPART_CN_NAME||
		   V_IN_COA_CODE||
		   V_IN_COA_CN_NAME||'
           T1.L3_CEG_CODE,
           T1.L3_CEG_CN_NAME,
           T1.L3_CEG_SHORT_CN_NAME,
           T1.L4_CEG_CODE,
           T1.L4_CEG_CN_NAME,
           T1.L4_CEG_SHORT_CN_NAME,
           T1.CATEGORY_CODE,
           T1.CATEGORY_CN_NAME,
           T1.ITEM_CODE,
           T1.ITEM_CN_NAME,
		   T1.SHIP_QUANTITY,
		   T1.RMB_COST_AMT,
           GS_ENCRYPT(T1.RMB_AVG_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256'') AS RMB_AVG_AMT,
           T1.CREATED_BY,
           T1.CREATION_DATE,
           T1.LAST_UPDATED_BY,
           T1.LAST_UPDATE_DATE,
           T1.DEL_FLAG,
           T1.APPEND_FLAG,
           T1.SCENARIO_FLAG,
           T1.VIEW_FLAG,
           T1.CALIBER_FLAG,
           T1.OVERSEA_FLAG,
           T1.LV0_PROD_LIST_CODE,
           T1.LV0_PROD_LIST_CN_NAME,
           T1.LV0_PROD_LIST_EN_NAME
        FROM '||V_FROM_TABLE||' T1
       WHERE T1.SCENARIO_FLAG = ''Y'' 
	   AND T1.APPEND_FLAG = ''N''
	   AND T1.VIEW_FLAG = '''||f_view_flag||''' 
			AND T1.OVERSEA_FLAG = '''||f_oversea_flag||''' AND T1.LV0_PROD_LIST_CODE = '''||f_lv0_prod_list_code||'''
			AND T1.CALIBER_FLAG = '''||f_caliber_flag||''''
			;
		
		EXECUTE IMMEDIATE V_SQL;
	  --2.写入日志
	V_STEP_MUM := V_STEP_MUM + 1;
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	(F_SP_NAME => V_SP_NAME,
	F_STEP_NUM => V_STEP_MUM,
	F_CAL_LOG_DESC => '插入预测数据到'||V_TO_TABLE1||'表, 版本号='||V_VERSION_ID,
	F_DML_ROW_COUNT => SQL%ROWCOUNT,
	F_RESULT_STATUS => X_RESULT_STATUS,
	F_ERRBUF => 'SUCCESS');

	--3.收集统计信息
	EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE1;

	--3.日志结束
	V_STEP_MUM := V_STEP_MUM + 1;
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	(F_SP_NAME => V_SP_NAME,
	F_STEP_NUM => V_STEP_MUM,
	F_CAL_LOG_DESC => V_SP_NAME||'第一步插入预测数据到新表运行结束, 收集'||V_TO_TABLE1||'统计信息完成!');
   
   
   
  --3.实际预测数数据合并
  V_SQL :=
  'INSERT INTO '||V_TO_TABLE2||'
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE,
     LV2_PROD_RD_TEAM_CN_NAME,'||
     V_LV3_PROD_RND_TEAM_CODE ||
     V_LV3_PROD_RD_TEAM_CN_NAME ||
	 V_LV4_PROD_RND_TEAM_CODE ||
     V_LV4_PROD_RD_TEAM_CN_NAME ||
     V_L1_NAME ||
     V_L2_NAME ||
     V_DIMENSION_CODE ||
     V_DIMENSION_CN_NAME ||
     V_DIMENSION_EN_NAME||
     V_DIMENSION_SUBCATEGORY_CODE ||
     V_DIMENSION_SUBCATEGORY_CN_NAME ||
     V_DIMENSION_SUBCATEGORY_EN_NAME||
     V_DIMENSION_SUB_DETAIL_CODE ||
     V_DIMENSION_SUB_DETAIL_CN_NAME ||
     V_DIMENSION_SUB_DETAIL_EN_NAME ||
	 V_SPART_CODE||
	 V_SPART_CN_NAME||
	 V_COA_CODE||
	 V_COA_CN_NAME||'
     L3_CEG_CODE,
     L3_CEG_CN_NAME,
     L3_CEG_SHORT_CN_NAME,
     L4_CEG_CODE,
     L4_CEG_CN_NAME,
     L4_CEG_SHORT_CN_NAME,
     CATEGORY_CODE,
     CATEGORY_CN_NAME,
     ITEM_CODE,
     ITEM_CN_NAME,
     SHIP_QUANTITY,
     RMB_COST_AMT,
     RMB_AVG_AMT,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     APPEND_FLAG,
     SCENARIO_FLAG,
     VIEW_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME,
     LV0_PROD_LIST_EN_NAME)
  SELECT '||V_VERSION_ID||' AS VERSION_ID,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.LV0_PROD_RND_TEAM_CODE,
           T1.LV0_PROD_RD_TEAM_CN_NAME,
           T1.LV1_PROD_RND_TEAM_CODE,
           T1.LV1_PROD_RD_TEAM_CN_NAME,
           T1.LV2_PROD_RND_TEAM_CODE,
           T1.LV2_PROD_RD_TEAM_CN_NAME,'||
           V_IN_LV3_PROD_RND_TEAM_CODE ||
           V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
		   V_IN_LV4_PROD_RND_TEAM_CODE ||
           V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
           V_IN_L1_NAME ||
           V_IN_L2_NAME ||
           V_IN_DIMENSION_CODE ||
           V_IN_DIMENSION_CN_NAME ||
           V_IN_DIMENSION_EN_NAME||
           V_IN_DIMENSION_SUBCATEGORY_CODE ||
           V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
           V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
           V_IN_DIMENSION_SUB_DETAIL_CODE ||
           V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
           V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
		   V_IN_SPART_CODE||
		   V_IN_SPART_CN_NAME||
		   V_IN_COA_CODE||
		   V_IN_COA_CN_NAME||'
           T1.L3_CEG_CODE,
           T1.L3_CEG_CN_NAME,
           T1.L3_CEG_SHORT_CN_NAME,
           T1.L4_CEG_CODE,
           T1.L4_CEG_CN_NAME,
           T1.L4_CEG_SHORT_CN_NAME,
           T1.CATEGORY_CODE,
           T1.CATEGORY_CN_NAME,
           T1.ITEM_CODE,
           T1.ITEM_CN_NAME,
		   T1.SHIP_QUANTITY,
		   T1.RMB_COST_AMT,
           T1.RMB_AVG_AMT,
           T1.CREATED_BY,
           T1.CREATION_DATE,
           T1.LAST_UPDATED_BY,
           T1.LAST_UPDATE_DATE,
           T1.DEL_FLAG,
           T1.APPEND_FLAG,
           T1.SCENARIO_FLAG,
           T1.VIEW_FLAG,
           T1.CALIBER_FLAG,
           T1.OVERSEA_FLAG,
           T1.LV0_PROD_LIST_CODE,
           T1.LV0_PROD_LIST_CN_NAME,
           T1.LV0_PROD_LIST_EN_NAME
        FROM '||V_FROM_TABLE||' T1
       WHERE  T1.VIEW_FLAG = '''||f_view_flag||''' 
			AND T1.OVERSEA_FLAG = '''||f_oversea_flag||''' AND T1.LV0_PROD_LIST_CODE = '''||f_lv0_prod_list_code||'''
			AND T1.CALIBER_FLAG = '''||f_caliber_flag||''''
			;
		
		EXECUTE IMMEDIATE V_SQL;
		
	  --2.写入日志
	V_STEP_MUM := V_STEP_MUM + 1;
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	(F_SP_NAME => V_SP_NAME,
	F_STEP_NUM => V_STEP_MUM,
	F_CAL_LOG_DESC => '插入预测数据到'||V_TO_TABLE1||'表, 版本号='||V_VERSION_ID,
	F_DML_ROW_COUNT => SQL%ROWCOUNT,
	F_RESULT_STATUS => X_RESULT_STATUS,
	F_ERRBUF => 'SUCCESS');





	--3.收集统计信息
	EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE1;

	--3.日志结束
	V_STEP_MUM := V_STEP_MUM + 1;
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	(F_SP_NAME => V_SP_NAME,
	F_STEP_NUM => V_STEP_MUM,
	F_CAL_LOG_DESC => V_SP_NAME||'第一步插入预测数据到新表运行结束, 收集'||V_TO_TABLE1||'统计信息完成!');
   



  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';

  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败',
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END

$$
/

