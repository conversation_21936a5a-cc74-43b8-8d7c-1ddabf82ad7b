-- Name: f_dm_fol_air_actual_perform_alloc_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fol_air_actual_perform_alloc_info_t(p_version_id integer DEFAULT NULL::integer, p_refresh_type character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建人  ：zwx1275798
创建时间：2024年8月27日
背景描述：物流空运实际履行份额表         
参数描述： p_version_id 逻辑：1、自动调度，航线量汇总表最大版本ID；2、刷新：取java传版本ID；
          p_refresh_type 逻辑：1、自动调度，直接赋值为 4_AUTO；2、刷新：取JAVA传入的刷新类型
		  x_success_flag:返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_fol_air_actual_perform_alloc_info_t()
变更记录：202503 zwx1275798 代码优化(代码由872行缩减至744行)：
                                         1、将所有with as 临时表修改为temporary会话临时表
                                         2、将所有temporary会话临时表的表结构创建语句删除，将insert temporary语句修改为创建temporary表的逻辑语句
		 


*/

declare
	v_sp_name varchar(200) := 'fin_dm_opt_foi.f_dm_fol_air_actual_perform_alloc_info_t('||p_version_id||','''||p_refresh_type||''')';
	v_tbl_name varchar(200) := 'fin_dm_opt_foi.dm_fol_air_actual_perform_alloc_info_t';
	v_dml_row_count  int default 0 ;
	v_max_version_id int;

begin

  set enable_force_vector_engine to on;
	x_success_flag := '1';        --1表示成功

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '物流空运实际履行份额表 '||v_tbl_name||'：开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
  ) ;       		
  
   --从 物流空运实际履行份额表 中，删除版本信息表中执行失败的 version_id 数据
        delete from fin_dm_opt_foi.dm_fol_air_actual_perform_alloc_info_t t1
              where t1.version_id in (select distinct t2.version_id from fin_dm_opt_foi.dm_fol_air_version_info_t t2 where  t2.step='2001');

    -- 如果是传version_id调函数取JAVA传入的p_version_id，如果是自动调度的则取航线量汇总表的最大版本ID
        if p_version_id is not null then
        select  p_version_id into v_max_version_id ;
        else
        select max(version_id) as max_version_id into v_max_version_id
		from fin_dm_opt_foi.dm_fol_air_route_info_sum_t;
        end if
        ;

     -- 清理已经写入版本信息表的数据
         delete from fin_dm_opt_foi.dm_fol_air_version_info_t 
		 where version_id = v_max_version_id 
		 and step = 2 
		 and source_en_name = 'f_dm_fol_air_actual_perform_alloc_info_t'
		 and refresh_type = nvl(p_refresh_type,'4_AUTO');
    
    --  将执行步骤：2  执行中 插入版本信息表中的
    insert into fin_dm_opt_foi.dm_fol_air_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
		 , transport_mode       -- 运输方式（精品空运、精品海运）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select v_max_version_id   as version_id
         , '' as version_code
         , 2 as step  -- 执行中
         , 'f_dm_fol_air_actual_perform_alloc_info_t' as source_en_name
         , '物流空运实际履行份额函数'           as source_cn_name
         , nvl(p_refresh_type,'4_AUTO') as refresh_type
		 , '精品空运' as transport_mode
         , '没用到价格，故无价格表的版本code' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
    ;
    
    v_dml_row_count := sql%rowcount;	-- 收集数据量
  
    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '版本ID：'||v_max_version_id||'，写入到版本信息表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
  
      --从 航线量表 取出 version_id 为最大版本，仅精品空运	  
	    drop table if exists air_alloc_qty_tmp;
		create temporary table air_alloc_qty_tmp
		      as
          -- 区分是否精品				
		select  version_id
		       ,year
	           ,period_id
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name     
               ,dest_port_name       
	           ,dest_country_name
			   ,supplier_short_name
			   ,sum(container_qty) as container_qty      
			   ,Huawei_group    
			   ,service_level   
			   ,is_high_quality 
		from  fin_dm_opt_foi.dm_fol_air_route_info_sum_t
		where 1=1
		  and transport_mode = '精品空运'
		  and version_id = v_max_version_id
		  and container_qty is not null		
		group by version_id
		       ,year
		       ,period_id
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name     
               ,dest_port_name       
	           ,dest_country_name
			   ,supplier_short_name  
			   ,Huawei_group    
			   ,service_level   
			   ,is_high_quality 	
          union	all
         -- 不区分是否精品
		 select  version_id
		       ,year
	           ,period_id
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name     
               ,dest_port_name       
	           ,dest_country_name
			   ,supplier_short_name
			   ,sum(container_qty) as container_qty      
			   ,Huawei_group    
			   ,service_level   
			   ,'ALL' as is_high_quality 
		from  fin_dm_opt_foi.dm_fol_air_route_info_sum_t
		where 1=1
		  and transport_mode = '精品空运'
		  and version_id = v_max_version_id
		  and container_qty is not null		
		group by version_id
		       ,year
		       ,period_id
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name     
               ,dest_port_name       
	           ,dest_country_name
			   ,supplier_short_name   
			   ,Huawei_group    
			   ,service_level   
			   ;
    
	         v_dml_row_count := sql%rowcount;	-- 收集数据量
  
            -- 写入日志
            perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
                p_log_version_id => null,                 --版本
                p_log_sp_name => v_sp_name,    --sp名称
                p_log_para_list => '',--参数
                p_log_step_num  => 3,
                p_log_cal_log_desc => 'air_alloc_qty_tmp 从 航线量表 取出 version_id 为最大版本'||v_dml_row_count,--日志描述
                p_log_formula_sql_txt => null,--错误信息
                p_log_row_count => v_dml_row_count,
                p_log_errbuf => null  --错误编码
            ) ;
	
	          -- 计算航线层级下的供应商当年累加YTD的值
			    drop table if exists air_alloc_ytd_tmp;
		        create temporary table air_alloc_ytd_tmp
		           as
	    	    select  t1.version_id
               ,t1.year				
		       ,t1.period_id          
		       ,t1.transport_mode     
		       ,t1.region_cn_name     
		       ,t1.route              
		       ,t1.source_port_name     
               ,t1.dest_port_name       
	           ,t1.dest_country_name  			   
		       ,t1.supplier_short_name                              
			   ,t1.Huawei_group    
               ,t1.service_level   
			   ,t1.is_high_quality 
               ,sum(t1.container_qty) over(partition by t1.version_id,t1.year,t1.transport_mode,t1.region_cn_name,t1.route,
                                                   t1.dest_country_name,t1.supplier_short_name,t1.Huawei_group,t1.service_level,t1.is_high_quality
                                          order by t1.period_id
                                     ) as container_qty -- 柜型量
                from air_alloc_qty_tmp t1
				;
				
				
				v_dml_row_count := sql%rowcount;	-- 收集数据量
				
				-- 写入日志
            perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
                p_log_version_id => null,                 --版本
                p_log_sp_name => v_sp_name,    --sp名称
                p_log_para_list => '',--参数
                p_log_step_num  => 4,
                p_log_cal_log_desc => 'air_alloc_ytd_tmp 计算航线层级下的供应商当年累加YTD的值'||v_dml_row_count,--日志描述
                p_log_formula_sql_txt => null,--错误信息
                p_log_row_count => v_dml_row_count,
                p_log_errbuf => null  --错误编码
            ) ;
			
			   -- 创建连续月份临时表
			    drop table if exists time_tmp;
		        create temporary table time_tmp
		           as
				 select distinct t2.version_id
               ,t2.year				         
		       ,t2.transport_mode     
		       ,t2.region_cn_name     
		       ,t2.route              
		       ,t2.source_port_name     
               ,t2.dest_port_name       
	           ,t2.dest_country_name  			   
		       ,t2.supplier_short_name                                 
			   ,t2.Huawei_group    
               ,t2.service_level   
			   ,t2.is_high_quality 
               ,(t2.year||lpad(t1.month,2,'0'))::int as apd_period_id
               from air_alloc_ytd_tmp t2
               left join (select unnest(array[1,2,3,4,5,6,7,8,9,10,11,12]) as month) t1
                 on 1=1
				 ;
				 
				-- 将连续月份临时表与航线层级下的供应商当年累加YTD的临时表关联，count会计期数据量为0的就是缺失的月份
				 drop table if exists time_tmp2;
		        create temporary table time_tmp2
		           as
				select t1.version_id
                     ,t1.year				         
		             ,t1.transport_mode     
		             ,t1.region_cn_name     
		             ,t1.route              
		             ,t1.source_port_name     
                     ,t1.dest_port_name       
	                 ,t1.dest_country_name 			   
		             ,t1.supplier_short_name                             
			         ,t1.Huawei_group    
                     ,t1.service_level   
			         ,t1.is_high_quality 
					 ,t1.apd_period_id
					 ,t2.period_id 
			         ,t2.container_qty 
                , count(t2.period_id) over(partition by t1.year, t1.route, t1.supplier_short_name,t1.is_high_quality,t1.Huawei_group,t1.service_level order by t1.apd_period_id) as cn
                from time_tmp t1
                left join air_alloc_ytd_tmp t2
                  on t1.version_id = t2.version_id
                 and t1.year = t2.year
                 and t1.transport_mode = t2.transport_mode
                 and t1.region_cn_name = t2.region_cn_name
                 and t1.route = t2.route
                 and t1.supplier_short_name = t2.supplier_short_name
                 and t1.apd_period_id = t2.period_id
				 and t1.Huawei_group = t2.Huawei_group
		         and t1.service_level = t2.service_level
		         and t1.is_high_quality = t2.is_high_quality
                   ;				 
				   				   
				-- 将航线层级下的供应商当年累加YTD的值为空的月份补齐
				drop table if exists air_apd_alloc_ytd_tmp;
		        create temporary table air_apd_alloc_ytd_tmp
		           as		
                select version_id
                   ,year	
                   ,apd_period_id as period_id				   
		           ,transport_mode     
		           ,region_cn_name     
		           ,route              
		           ,source_port_name     
                   ,dest_port_name       
	               ,dest_country_name 			   
		           ,supplier_short_name                                 
			       ,Huawei_group    
                   ,service_level   
			       ,is_high_quality                   
                   , max(container_qty) over(partition by year, transport_mode, region_cn_name, route, supplier_short_name,Huawei_group,service_level,is_high_quality, cn) as container_qty
			  from time_tmp2
             where cn <> 0
               and apd_period_id <= (select max(period_id) as max_period_id  from air_alloc_qty_tmp)
			   ;			   
			   
			   v_dml_row_count := sql%rowcount;	-- 收集数据量
				
				-- 写入日志
            perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
                p_log_version_id => null,                 --版本
                p_log_sp_name => v_sp_name,    --sp名称
                p_log_para_list => '',--参数
                p_log_step_num  => 5,
                p_log_cal_log_desc => 'air_apd_alloc_ytd_tmp 将航线层级下的供应商当年累加YTD的值为空的月份补齐'||v_dml_row_count,--日志描述
                p_log_formula_sql_txt => null,--错误信息
                p_log_row_count => v_dml_row_count,
                p_log_errbuf => null  --错误编码
            ) ;
			
			-- 航线层级的YTD汇总
			   drop table if exists air_route_ytd_tmp;
		       create temporary table air_route_ytd_tmp
		         as	
			   select version_id
               ,year				
		       ,period_id          
		       ,transport_mode     
		       ,region_cn_name     
		       ,route
               ,source_port_name     
               ,dest_port_name       
	           ,dest_country_name			   
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
			   ,sum(container_qty) as container_qty
			   from air_apd_alloc_ytd_tmp 
			   group by version_id
               ,year				
		       ,period_id          
		       ,transport_mode     
		       ,region_cn_name 
               ,route
               ,source_port_name     
               ,dest_port_name       
	           ,dest_country_name			   
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
			   ;
			   
			   delete from fin_dm_opt_foi.dm_fol_air_actual_perform_alloc_info_t  where version_id = v_max_version_id;
			   
			 -- 航线层级的份额占比：航线下供应商YTD/航线YTD
			   insert into fin_dm_opt_foi.dm_fol_air_actual_perform_alloc_info_t(
			    version_id
               ,year
               ,period_id
               ,target_type
               ,transport_mode
               ,region_cn_name
               ,route
               ,source_port_name     
               ,dest_port_name       
	           ,dest_country_name
               ,supplier_short_name
               ,level_code
               ,level_desc
               ,percent
               ,Huawei_group
               ,service_level
               ,is_high_quality
               ,remark
               ,created_by
               ,creation_date
               ,last_updated_by
               ,last_update_date
               ,del_flag
			   )
			   select t1.version_id
               ,t1.year
               ,t1.period_id
               ,'YTD' as target_type
               ,t1.transport_mode
               ,t1.region_cn_name
               ,t1.route
               ,t1.source_port_name     
               ,t1.dest_port_name       
	           ,t1.dest_country_name
               ,t1.supplier_short_name
               ,'03' as level_code         
               ,'航线' as level_desc
               ,t1.container_qty/t2.container_qty as percent
               ,t1.Huawei_group
               ,t1.service_level
               ,t1.is_high_quality
               , '' as remark
  	           , -1 as created_by
  	           , current_timestamp as creation_date
  	           , -1 as last_updated_by
  	           , current_timestamp as last_update_date
  	           , 'N' as del_flag
			   from air_apd_alloc_ytd_tmp t1
			   left join air_route_ytd_tmp  t2
			   on  t1.version_id = t2.version_id
			   and t1.year = t2.year
			   and t1.period_id = t2.period_id
			   and t1.transport_mode = t2.transport_mode		 
		       and t1.region_cn_name = t2.region_cn_name
		       and t1.route = t2.route
		       and t1.Huawei_group = t2.Huawei_group
		       and t1.service_level = t2.service_level
		       and t1.is_high_quality = t2.is_high_quality
			   ;
			   
			    v_dml_row_count := sql%rowcount;	-- 收集数据量
				
				-- 写入日志
            perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
                p_log_version_id => null,                 --版本
                p_log_sp_name => v_sp_name,    --sp名称
                p_log_para_list => '',--参数
                p_log_step_num  => 6,
                p_log_cal_log_desc => 'dm_fol_air_actual_perform_alloc_info_t 航线层级的份额占比'||v_dml_row_count,--日志描述
                p_log_formula_sql_txt => null,--错误信息
                p_log_row_count => v_dml_row_count,
                p_log_errbuf => null  --错误编码
            ) ;			
			
			   -- 区域下供应商YTD汇总
			   drop table if exists air_region_supplier_ytd_tmp;
		       create temporary table air_region_supplier_ytd_tmp
		         as	
				 select version_id
               ,year				
		       ,period_id          
		       ,transport_mode     
		       ,region_cn_name     			   
		       ,supplier_short_name                                
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
			   ,sum(container_qty) as container_qty
			   from air_apd_alloc_ytd_tmp
			   group by version_id
               ,year				
		       ,period_id          
		       ,transport_mode     
		       ,region_cn_name     			   
		       ,supplier_short_name                                
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality
			   ;
			   
			   -- 区域层级的YTD汇总
			   drop table if exists air_region_ytd_tmp;
		       create temporary table air_region_ytd_tmp
		         as	
			   select version_id
               ,year				
		       ,period_id          
		       ,transport_mode 
               ,region_cn_name			                               
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
			   ,sum(container_qty) as container_qty
			   from air_apd_alloc_ytd_tmp
			   group by version_id
               ,year				
		       ,period_id          
		       ,transport_mode     
		       ,region_cn_name 		                          
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
                  ;			   				 
			   
			   -- 区域层级的份额占比：区域下供应商YTD/区域YTD
			   insert into fin_dm_opt_foi.dm_fol_air_actual_perform_alloc_info_t(
			    version_id
               ,year
               ,period_id
               ,target_type
               ,transport_mode
               ,region_cn_name
               ,supplier_short_name
               ,level_code
               ,level_desc
               ,percent
               ,Huawei_group
               ,service_level
               ,is_high_quality
               ,remark
               ,created_by
               ,creation_date
               ,last_updated_by
               ,last_update_date
               ,del_flag
			   )			  	   
			   select t1.version_id
               ,t1.year
               ,t1.period_id
               ,'YTD' as target_type
               ,t1.transport_mode
               ,t1.region_cn_name               
               ,t1.supplier_short_name
               ,'02' as level_code         
               ,'区域' as level_desc
               ,t1.container_qty/t2.container_qty as percent
               ,t1.Huawei_group
               ,t1.service_level
               ,t1.is_high_quality
               , '' as remark
  	           , -1 as created_by
  	           , current_timestamp as creation_date
  	           , -1 as last_updated_by
  	           , current_timestamp as last_update_date
  	           , 'N' as del_flag
			   from air_region_supplier_ytd_tmp t1
			   left join air_region_ytd_tmp  t2
			   on  t1.version_id = t2.version_id
			   and t1.year = t2.year
			   and t1.period_id = t2.period_id
			   and t1.transport_mode = t2.transport_mode		 
		       and t1.region_cn_name = t2.region_cn_name
		       and t1.Huawei_group = t2.Huawei_group
		       and t1.service_level = t2.service_level
		       and t1.is_high_quality = t2.is_high_quality
			   ;
			   
			    v_dml_row_count := sql%rowcount;	-- 收集数据量
				
				-- 写入日志
            perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
                p_log_version_id => null,                 --版本
                p_log_sp_name => v_sp_name,    --sp名称
                p_log_para_list => '',--参数
                p_log_step_num  => 7,
                p_log_cal_log_desc => 'dm_fol_air_actual_perform_alloc_info_t 区域层级的份额占比'||v_dml_row_count,--日志描述
                p_log_formula_sql_txt => null,--错误信息
                p_log_row_count => v_dml_row_count,
                p_log_errbuf => null  --错误编码
            ) ;
			
			 -- 运输层级下供应商的YTD汇总
			   drop table if exists air_transport_supplier_ytd_tmp;
		       create temporary table air_transport_supplier_ytd_tmp
		         as	
			   select version_id
               ,year				
		       ,period_id          
		       ,transport_mode         			   
		       ,supplier_short_name                                 
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
			   ,sum(container_qty) as container_qty
			   from air_apd_alloc_ytd_tmp
			   group by version_id
               ,year				
		       ,period_id          
		       ,transport_mode        			   
		       ,supplier_short_name                               
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
			   ;
			   
			   -- 运输层级YTD汇总
			   drop table if exists air_transport_ytd_tmp;
		       create temporary table air_transport_ytd_tmp
		         as	
		       select version_id
               ,year				
		       ,period_id          
		       ,transport_mode                                                 
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
			   ,sum(container_qty) as container_qty
			   from air_apd_alloc_ytd_tmp
			   group by version_id
               ,year				
		       ,period_id          
		       ,transport_mode     	                        
			   ,Huawei_group    
               ,service_level   
			   ,is_high_quality 
			   ;
			   
			   -- 运输层级的份额占比：运输方式下供应商YTD/运输方式YTD
			   insert into fin_dm_opt_foi.dm_fol_air_actual_perform_alloc_info_t(
			    version_id
               ,year
               ,period_id
               ,target_type
               ,transport_mode
               ,supplier_short_name
               ,level_code
               ,level_desc
               ,percent
               ,Huawei_group
               ,service_level
               ,is_high_quality
               ,remark
               ,created_by
               ,creation_date
               ,last_updated_by
               ,last_update_date
               ,del_flag
			   )   
			   select t1.version_id
               ,t1.year
               ,t1.period_id
               ,'YTD' as target_type
               ,t1.transport_mode               
               ,t1.supplier_short_name
			   ,'01' as level_code         
               ,'运输方式' as level_desc
               ,t1.container_qty/t2.container_qty as percent
               ,t1.Huawei_group
               ,t1.service_level
               ,t1.is_high_quality
               , '' as remark
  	           , -1 as created_by
  	           , current_timestamp as creation_date
  	           , -1 as last_updated_by
  	           , current_timestamp as last_update_date
  	           , 'N' as del_flag
			   from air_transport_supplier_ytd_tmp t1
			   left join air_transport_ytd_tmp  t2
			   on  t1.version_id = t2.version_id
			   and t1.year = t2.year
			   and t1.period_id = t2.period_id
			   and t1.transport_mode = t2.transport_mode		 
		       and t1.Huawei_group = t2.Huawei_group
		       and t1.service_level = t2.service_level
		       and t1.is_high_quality = t2.is_high_quality
			   ;			   
			   
			   v_dml_row_count := sql%rowcount;	-- 收集数据量
				
				-- 写入日志
            perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
                p_log_version_id => null,                 --版本
                p_log_sp_name => v_sp_name,    --sp名称
                p_log_para_list => '',--参数
                p_log_step_num  => 8,
                p_log_cal_log_desc => 'dm_fol_air_actual_perform_alloc_info_t 运输层级的份额占比'||v_dml_row_count,--日志描述
                p_log_formula_sql_txt => null,--错误信息
                p_log_row_count => v_dml_row_count,
                p_log_errbuf => null  --错误编码
            ) ;
			   			   
			    -- 清理已经写入版本信息表的数据
         delete from fin_dm_opt_foi.dm_fol_air_version_info_t 
		 where version_id = v_max_version_id 
		 and step = 1 
		 and source_en_name = 'f_dm_fol_air_actual_perform_alloc_info_t'
		 and refresh_type = nvl(p_refresh_type,'4_AUTO');
				
			 --将版本信息表中的执行步骤改为：1 成功
		update fin_dm_opt_foi.dm_fol_air_version_info_t 
		set step = 1 
		where source_en_name = 'f_dm_fol_air_actual_perform_alloc_info_t' 
		  and version_id = v_max_version_id
		  and refresh_type = nvl(p_refresh_type,'4_AUTO');
    
    v_dml_row_count := sql%rowcount;	-- 收集数据量
  
    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 4,
        p_log_cal_log_desc => '版本信息表中的step已更新为完成，运行结束！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;

          --收集统计信息
        analyse fin_dm_opt_foi.dm_fol_air_actual_perform_alloc_info_t;
        analyse fin_dm_opt_foi.dm_fol_air_version_info_t;

  exception
  	when others then

      perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		    p_log_row_count => null,
		    p_log_errbuf => sqlstate  --错误编码
      ) ;
	x_success_flag := '2001';	         --2001表示失败

  -- 失败信息写入到版本信息表
  insert into fin_dm_opt_foi.dm_fol_air_version_info_t(
         version_id           -- 版本ID（自动生成）
       , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
       , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
       , source_en_name       -- 来源英文描述（可以是表名、函数名等）
       , source_cn_name       -- 来源中文描述
       , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
	   , transport_mode       -- 运输方式（精品空运、精品海运）
       , remark               -- 备注
       , created_by           -- 创建人
       , creation_date        -- 创建时间
       , last_updated_by      -- 修改人
       , last_update_date     -- 修改时间
       , del_flag             -- 是否删除
  )
  select v_max_version_id as version_id
       , '' as version_code
       , 2001 as step
       , 'f_dm_fol_air_actual_perform_alloc_info_t' as source_en_name
       , '物流空运实际履行份额函数'           as source_cn_name
       , nvl(p_refresh_type,'4_AUTO') as refresh_type
	   , '精品空运' as transport_mode
       , '实际履行份额不涉及价格，故无价格补录表的版本code' as remark
	     , -1 as created_by
	     , current_timestamp as creation_date
	     , -1 as last_updated_by
	     , current_timestamp as last_update_date
	     , 'N' as del_flag
    ;

end;
$$
/

