-- Name: f_dm_foi_cal_log_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_cal_log_t(f_version_id numeric DEFAULT NULL::numeric, f_sp_name character varying DEFAULT NULL::character varying, f_para_list character varying DEFAULT NULL::character varying, f_step_num numeric DEFAULT NULL::numeric, f_cal_log_desc character varying DEFAULT NULL::character varying, f_formula_sql_txt text DEFAULT NULL::text, f_dml_row_count numeric DEFAULT (0)::numeric, f_result_status character varying DEFAULT (1)::character varying, f_errbuf character varying DEFAULT 'SUCCESS'::character varying)
 RETURNS void
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
DECLARE
BEGIN
  INSERT INTO FIN_DM_OPT_FOI.DM_FOI_LOG_T
    (LOG_ID,
     VERSION_ID,
     SP_NAME,
     PARA_LIST,
     STEP_NUM,
     CAL_LOG_DESC,
     FORMULA_SQL_TXT,
     DML_ROW_COUNT,
     RESULT_STATUS,
     ERRBUF,
     CREATED_BY,
     CREATION_DATE)
  VALUES
    (FIN_DM_OPT_FOI.DM_FOI_LOG_S.NEXTVAL,
     F_VERSION_ID,
     F_SP_NAME,
     F_PARA_LIST,
     F_STEP_NUM,
     F_CAL_LOG_DESC,
     F_FORMULA_SQL_TXT,
     F_DML_ROW_COUNT,
     F_RESULT_STATUS,
     F_ERRBUF,
     - 1,
     CLOCK_TIMESTAMP());
EXCEPTION
  WHEN OTHERS THEN
  RAISE NOTICE 'FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T函数报错,请检查!';
  INSERT INTO FIN_DM_OPT_FOI.DM_FOI_LOG_T
    (LOG_ID,
     VERSION_ID,
     SP_NAME,
     PARA_LIST,
     STEP_NUM,
     CAL_LOG_DESC,
     FORMULA_SQL_TXT,
     DML_ROW_COUNT,
     RESULT_STATUS,
     ERRBUF,
     CREATED_BY,
     CREATION_DATE)
  VALUES
    (FIN_DM_OPT_FOI.DM_FOI_LOG_S.NEXTVAL,
     NULL,
     NULL,
     NULL,
     NULL,
     '日志程序错误',
     NULL,
     0,
     '0',
     SQLERRM,
     - 1,
     CLOCK_TIMESTAMP());
  END$$
/

