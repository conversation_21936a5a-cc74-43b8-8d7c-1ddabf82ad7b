-- Name: f_dm_fom_sync_dim_made_bak20240816; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_sync_dim_made_bak20240816(f_industry_flag character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
创建时间:2023/1/20
创建人  :唐钦
背景描述:f_dm_fom_sync_dim_made 制造单领域量纲维表，同步至产业成本量纲维表
参数描述:x_result_status :是否成功
来源表:   FIN_DM_OPT_FOI.APD_FOM_MANUFACTURE_DIM_T 
目标表:   FIN_DM_OPT_FOI.APD_FOC_MANUFACTURE_DIM_T
事例:FIN_DM_OPT_FOI.F_DM_FOM_SYNC_DIM_MADE()
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOM_SYNC_DIM_MADE'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_FOM_CURR_VERSION BIGINT; -- 制造单领域量纲维表当前的版本号ID
  V_LAST_FLAG  BIGINT; -- 制造单领域量纲维表上次版本号ID
  V_FOM_LAST_VERSION BIGINT; -- 制造单领域量纲维表上次的版本号ID
  V_FOC_VERSION_ID BIGINT; -- 产业成本指数当前版本号ID
  V_FOC_ADD_VERSION BIGINT; -- 产业成本指数新增的版本号ID
  V_FOC_VERSION_NAME VARCHAR2(200);  -- 产业成本指数新增的版本号名称
  V_VERSION_NUM BIGINT;
  V_VERSION_NUM1 VARCHAR(50);
  V_T1_TABLE VARCHAR2(200);
  V_T2_TABLE VARCHAR2(200);
  V_SQL TEXT;
  
 -- 202405版本新增
  V_FOC_VERSION_TABLE VARCHAR(200);
  V_FOC_VERSION_SEQUENCE VARCHAR(200);
  V_TO_TABLE VARCHAR(200);
  V_DIFF_CONDITION VARCHAR(50);
  
BEGIN
  X_RESULT_STATUS = '1';
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
 -- 判断产业项目为ICT还是数字能源
  IF F_INDUSTRY_FLAG = 'I' THEN   -- ICT  
     V_DIFF_CONDITION := '''SYNC_FOM''';
     V_FOC_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T';
     V_FOC_VERSION_SEQUENCE := '''FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S''';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.APD_FOC_MANUFACTURE_DIM_T';
  ELSIF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源 
     V_DIFF_CONDITION := '''SYNC_FOM_ENERGY''';
     V_FOC_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T';
     V_FOC_VERSION_SEQUENCE := '''FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_S''';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.APD_FOC_ENERGY_MANUFACTURE_DIM_T';
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407版本新增IAS 
     V_DIFF_CONDITION := '''SYNC_FOM_IAS''';
     V_FOC_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T';
     V_FOC_VERSION_SEQUENCE := '''FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_S''';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.APD_FOC_IAS_MANUFACTURE_DIM_T';
  END IF;

--取到对应的3个版本号的值
--制造单领域量纲维表 当前版本号ID
  SELECT T.VERSION_ID       
    INTO V_FOM_CURR_VERSION  
    FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T T
   WHERE UPPER(DATA_TYPE) = 'DIM_DMS'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1
   ORDER BY LAST_UPDATE_DATE DESC 
   LIMIT 1;

  V_SQL := '
  SELECT COUNT(1) 
     FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T 
     WHERE ENABLE_FLAG = ''Y''
     AND DEL_FLAG = ''N''
     AND UPPER(PARA_NAME) = '||V_DIFF_CONDITION;
  EXECUTE IMMEDIATE V_SQL INTO V_LAST_FLAG;    -- 制造量纲维表上次同步数据的版本号ID

--产业成本指数量纲维表当前版本号ID
  V_SQL := '
    SELECT VERSION_ID       -- 产业成本指数当前版本号ID(同步结果表版本信息)
     FROM '||V_FOC_VERSION_TABLE||' 
     WHERE DEL_FLAG = ''N''
     AND UPPER(DATA_TYPE) = ''DIMENSION_MADE''
     AND STATUS = ''1''
     ORDER BY LAST_UPDATE_DATE DESC
     LIMIT 1';      
  EXECUTE IMMEDIATE V_SQL INTO V_FOC_VERSION_ID;

 -- 判断除了初始化的维表数据之外，是否有对制造单领域的制造量纲维表做修改
  IF V_LAST_FLAG <> 0 THEN   -- 制造量纲维表只有初始化数据
  V_SQL := '
    SELECT VALUE  
        FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T 
        WHERE ENABLE_FLAG = ''Y''
        AND DEL_FLAG = ''N''
        AND UPPER(PARA_NAME) = '||V_DIFF_CONDITION;
  EXECUTE IMMEDIATE V_SQL INTO V_FOM_LAST_VERSION;    -- 制造量纲维表上次同步数据的版本号ID
  ELSE V_FOM_LAST_VERSION := V_FOC_VERSION_ID;
  END IF;

 -- 如果制造量纲维表未进行最新同步，直接返回，否则继续执行同步逻辑
  IF V_FOM_CURR_VERSION = V_FOM_LAST_VERSION THEN
    RETURN '制造量纲维表未修改，维表最新版本号数据与上次同步时的数据一致';
  ELSE

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '制造量纲维表版本号：'||V_FOM_CURR_VERSION,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   END IF;

-- 产业成本指数新版本号赋值
  V_SQL := '
  SELECT NEXTVAL('||V_FOC_VERSION_SEQUENCE||')
    FROM DUAL';
  EXECUTE IMMEDIATE V_SQL INTO V_FOC_ADD_VERSION;

-- 判断同步维表当天，是否有对产业映射维表做修改，在版本名称后缀做次数区分
  V_SQL := '
  SELECT COUNT(1) 
      FROM '||V_FOC_VERSION_TABLE||' 
      WHERE DEL_FLAG = ''N'' AND UPPER(DATA_TYPE) = ''DIMENSION_MADE''
      AND STATUS = ''1'' AND SUBSTR(VERSION,1,8) = REPLACE(CURRENT_DATE,''-'',NULL)';
    EXECUTE IMMEDIATE V_SQL INTO V_VERSION_NUM;
      
  IF V_VERSION_NUM > 0 THEN
  V_SQL :='
  SELECT MAX(SUBSTR(VERSION,10,3)) 
      FROM '||V_FOC_VERSION_TABLE||' 
      WHERE DEL_FLAG = ''N'' AND UPPER(DATA_TYPE) = ''DIMENSION_MADE''
      AND STATUS = ''1'' AND SUBSTR(VERSION,1,8) = REPLACE(CURRENT_DATE,''-'',NULL)';
    EXECUTE IMMEDIATE V_SQL INTO V_VERSION_NUM;
  ELSE V_VERSION_NUM := '';
  END IF;    
  
  IF V_VERSION_NUM IS NULL THEN  
     V_VERSION_NUM := '1'; 
     V_VERSION_NUM1 := '-00';
  ELSE V_VERSION_NUM := V_VERSION_NUM + 1;
    IF V_VERSION_NUM >= 10 THEN 
       V_VERSION_NUM1 := '-0';
    ELSE V_VERSION_NUM1 := '-00';
    END IF;
  END IF;  
-- 拼接版本名称
  V_FOC_VERSION_NAME := REPLACE(CURRENT_DATE,'-','')||V_VERSION_NUM1||V_VERSION_NUM;    

  V_SQL := '
  INSERT INTO '||V_FOC_VERSION_TABLE||'
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   IS_RUNNING)
   VALUES
   ('||V_FOC_ADD_VERSION||','||V_FOC_VERSION_ID||','''||V_FOC_VERSION_NAME||''',1,''ADJUST'',''DIMENSION_MADE'',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,''N'',''N'')';
  EXECUTE IMMEDIATE V_SQL;
   
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '刷新最新生成的制造量纲维表版本号：'||V_FOC_ADD_VERSION,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  V_SQL := '
  INSERT INTO '||V_TO_TABLE||'(
         ITEM_CODE,
         CN_DESC,
         LV0_ORG_CN,
         BUSSINESS_OBJECT,
         SHIPPING_OBJECT,
         MANUFACTURE_OBJECT,
         MANUFACTURE_BU,
         VERSION_ID 
  )
  SELECT ITEM_CODE,
         CN_DESC,
         LV0_ORG_CN,
         BUSSINESS_OBJECT,
         SHIPPING_OBJECT,
         MANUFACTURE_OBJECT,
         MANUFACTURE_BU,
         '||V_FOC_ADD_VERSION||' AS VERSION_ID   -- 存入新的版本号里
      FROM '||V_TO_TABLE||'
     WHERE VERSION_ID = '||V_FOC_VERSION_ID;
    EXECUTE IMMEDIATE V_SQL;
   
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '把上版本：'||V_FOC_VERSION_ID||'，产业-制造量纲维表数据，插入产业维表，并作为新版本：'||V_FOC_ADD_VERSION||'，的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  --创建临时表
    DROP TABLE IF EXISTS DIM_ADD_DEL_TMP;
    CREATE TEMPORARY TABLE DIM_ADD_DEL_TMP (
         ITEM_CODE VARCHAR(50),
         CN_DESC VARCHAR(500),
         LV0_ORG_CN VARCHAR(500),
         BUSSINESS_OBJECT VARCHAR(500),
         SHIPPING_OBJECT VARCHAR(500),
         MANUFACTURE_OBJECT VARCHAR(500),
         MANUFACTURE_BU VARCHAR(500),
         MODFIY_STATUS VARCHAR(50)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(ITEM_CODE);

  IF V_LAST_FLAG <> 0 THEN   -- 不是只有初始化数据
       V_T1_TABLE := 'FIN_DM_OPT_FOI.APD_FOM_MANUFACTURE_DIM_T';
       V_T2_TABLE := 'FIN_DM_OPT_FOI.APD_FOM_MANUFACTURE_DIM_T';
  ELSE 
    V_T1_TABLE := 'FIN_DM_OPT_FOI.APD_FOM_MANUFACTURE_DIM_T';
    V_T2_TABLE := V_TO_TABLE;
  END IF;

-- 把有新增/删除的数据放到临时表
  V_SQL := '
  INSERT INTO DIM_ADD_DEL_TMP(
         ITEM_CODE,
         CN_DESC,
         LV0_ORG_CN,
         BUSSINESS_OBJECT,
         SHIPPING_OBJECT,
         MANUFACTURE_OBJECT,
         MANUFACTURE_BU,
         MODFIY_STATUS)
  SELECT ITEM_CODE,
         CN_DESC,
         LV0_ORG_CN,
         BUSSINESS_OBJECT,
         SHIPPING_OBJECT,
         MANUFACTURE_OBJECT,
         MANUFACTURE_BU,
         ''ADD'' AS MODFIY_STATUS
     FROM '||V_T1_TABLE||' T1
     WHERE VERSION_ID = '||V_FOM_CURR_VERSION||'
     AND NOT EXISTS (SELECT 1 FROM '||V_T2_TABLE||' T2
                        WHERE NVL(T1.ITEM_CODE,''S1'') = NVL(T2.ITEM_CODE,''S1'')
                           AND  NVL(T1.CN_DESC,''S2'') = NVL(T2.CN_DESC,''S2'')        
                           AND  NVL(T1.LV0_ORG_CN        ,''S3'')    = NVL(T2.LV0_ORG_CN        ,''S3'')
                           AND  NVL(T1.BUSSINESS_OBJECT  ,''S4'')    = NVL(T2.BUSSINESS_OBJECT  ,''S4'')
                           AND  NVL(T1.SHIPPING_OBJECT   ,''S5'')    = NVL(T2.SHIPPING_OBJECT   ,''S5'')
                           AND  NVL(T1.MANUFACTURE_OBJECT,''S6'')    = NVL(T2.MANUFACTURE_OBJECT,''S6'')
                           AND  NVL(T1.MANUFACTURE_BU    ,''S7'')    = NVL(T2.MANUFACTURE_BU    ,''S7'')    
                           AND T2.VERSION_ID = '||V_FOM_LAST_VERSION||')
  UNION ALL 
 SELECT ITEM_CODE,
         CN_DESC,
         LV0_ORG_CN,
         BUSSINESS_OBJECT,
         SHIPPING_OBJECT,
         MANUFACTURE_OBJECT,
         MANUFACTURE_BU,
         ''DEL'' AS MODFIY_STATUS
     FROM '||V_T2_TABLE||' T1
     WHERE VERSION_ID = '||V_FOM_LAST_VERSION||'
     AND NOT EXISTS (SELECT 1 FROM '||V_T1_TABLE||' T2
                        WHERE NVL(T1.ITEM_CODE,''S1'') = NVL(T2.ITEM_CODE,''S1'')
                           AND  NVL(T1.CN_DESC,''S2'') = NVL(T2.CN_DESC,''S2'')        
                           AND  NVL(T1.LV0_ORG_CN        ,''S3'')    = NVL(T2.LV0_ORG_CN        ,''S3'')
                           AND  NVL(T1.BUSSINESS_OBJECT  ,''S4'')    = NVL(T2.BUSSINESS_OBJECT  ,''S4'')
                           AND  NVL(T1.SHIPPING_OBJECT   ,''S5'')    = NVL(T2.SHIPPING_OBJECT   ,''S5'')
                           AND  NVL(T1.MANUFACTURE_OBJECT,''S6'')    = NVL(T2.MANUFACTURE_OBJECT,''S6'')
                           AND  NVL(T1.MANUFACTURE_BU    ,''S7'')    = NVL(T2.MANUFACTURE_BU    ,''S7'') 
                           AND T2.VERSION_ID = '||V_FOM_CURR_VERSION||')';
    EXECUTE IMMEDIATE V_SQL;
  
  -- 先对产业成本的维表做删除操作
  V_SQL := '
  DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_FOC_ADD_VERSION||' 
        AND ITEM_CODE IN ( SELECT ITEM_CODE FROM DIM_ADD_DEL_TMP WHERE MODFIY_STATUS = ''DEL'' )';   
  EXECUTE IMMEDIATE V_SQL;
       
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '把新版本：'||V_FOC_ADD_VERSION||'，的数据，在制造单领域已删除的数据删掉',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  V_SQL := '
  INSERT INTO '||V_TO_TABLE||'(
         ITEM_CODE,
         CN_DESC,
         LV0_ORG_CN,
         BUSSINESS_OBJECT,
         SHIPPING_OBJECT,
         MANUFACTURE_OBJECT,
         MANUFACTURE_BU,
         VERSION_ID 
  )
  SELECT ITEM_CODE,
         CN_DESC,
         LV0_ORG_CN,
         BUSSINESS_OBJECT,
         SHIPPING_OBJECT,
         MANUFACTURE_OBJECT,
         MANUFACTURE_BU,
         '||V_FOC_ADD_VERSION||' AS VERSION_ID   -- 存入新的版本号里
      FROM DIM_ADD_DEL_TMP T2
     WHERE MODFIY_STATUS = ''ADD''
     AND NOT EXISTS (SELECT 1 FROM '||V_TO_TABLE||' T3 
                                   WHERE T2.ITEM_CODE = T3.ITEM_CODE
                                   AND T3.VERSION_ID = '||V_FOC_ADD_VERSION||' )';
  EXECUTE IMMEDIATE V_SQL;
   
    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '把新版本：'||V_FOC_ADD_VERSION||'，的数据，在制造单领域新增的数据做新增',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 删除变量参数表上次保存的制造量纲维表同步版本号数据    
EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T WHERE PARA_NAME = '||V_DIFF_CONDITION||' AND ENABLE_FLAG = ''Y'''; 
  
-- 将制造版本号本次取得的最新版本号插入到变量参数表
  V_SQL := '
   INSERT INTO FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T (
          ID,
          PARA_NAME,
          PARA_DESCRIPTION,
          VALUE,
          CREATED_BY,
          CREATION_DATE,
          LAST_UPDATED_BY,
          LAST_UPDATE_DATE,
          DEL_FLAG,
          ENABLE_FLAG)
   VALUES( FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_S.NEXTVAL,
          '||V_DIFF_CONDITION||',
          ''制造量纲维表最新同步版本号'',
          '||V_FOM_CURR_VERSION||',
          -1,
          CURRENT_TIMESTAMP,
          -1,
          CURRENT_TIMESTAMP,
          ''N'',
          ''Y'')';
  EXECUTE IMMEDIATE V_SQL;
        
 -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将制造量纲维表本次取得的最新版本号：'||V_FOM_CURR_VERSION||'插入到变量参数表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END$$
/

