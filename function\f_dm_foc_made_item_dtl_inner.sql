-- Name: f_dm_foc_made_item_dtl_inner; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_made_item_dtl_inner(f_industry_flag character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
最后修改人: 罗若文
背景描述：1. 贴源层的实际发货明细表关联维表带出重量级团队,采购专家团字段
          2. 将单ITEM的品类数据条目打上标识
		  3. 增加数字能源适配
参数描述：x_result_status ：是否成功
事例：SELECT FIN_DM_OPT_FOI.f_dm_foc_made_item_dtl_inner()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_MADE_ITEM_DTL_INNER'; --存储过程名称
  V_VERSION_ID BIGINT ; --新版本号ID, 取自 FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S
  V_VERSION_NAME VARCHAR2(100) := TO_CHAR(CURRENT_TIMESTAMP,'YYYY-MM'); --新的版本中文名称
  V_DIM_VERSION_ID BIGINT ; --最新的品类-专家团映射维版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 存放当前是否已有版本号标识
  V_CURRENT_DIM_FLAG BIGINT;   -- 存放量纲的版本标识
  V_SQL TEXT;
  V_FROM_TABLE VARCHAR(100);
  V_TO_TABLE VARCHAR(100);
  V_DIM_TABLE VARCHAR(100);
  V_LV0_PARA VARCHAR(20);
  V_LV1_PARA VARCHAR(50);
  V_DIM_PARA VARCHAR(100);
  V_INSERT_COA_PARA VARCHAR(100);
  V_SELECT_COA_PARA VARCHAR(100);
  V_INVENTORY_ORG_CODE VARCHAR(10);
  V_INSERT_LV4_PARA VARCHAR(100);
  V_SELECT_LV4_PARA_T VARCHAR(100);
  V_SELECT_LV4_PARA_CP VARCHAR(100);
  V_SELECT_LV4_PARA_D  VARCHAR(100);
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
   
   
   --对制造量纲维表进行判断
   SELECT COUNT(DISTINCT VERSION_ID)  INTO  V_CURRENT_DIM_FLAG 
		FROM FIN_DM_OPT_FOI.DM_FOC_INV_ITEM_MANUFACTURE_T
			WHERE VERSION_ID = TO_CHAR(CURRENT_TIMESTAMP,'YYYYMM');
   
   IF V_CURRENT_DIM_FLAG = 0 THEN 
		DELETE  FROM DM_FOC_INV_ITEM_MANUFACTURE_T WHERE VERSION_ID =  TO_CHAR(CURRENT_TIMESTAMP,'YYYYMM');
		
		INSERT INTO DM_FOC_INV_ITEM_MANUFACTURE_T 
				SELECT TO_CHAR(CURRENT_TIMESTAMP,'YYYYMM') AS VERSION_ID ,
						*
						FROM APD_INV_ITEM_MANUFACTURE_T
							WHERE 1 = 1;
	
	END IF;
   
   
   
   
   
     --判断入参为ICT还是数字能源
   IF f_industry_flag = 'I' THEN 
	  V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_BOM_ITEM_SHIP_DTL_T';
	  V_LV0_PARA := '104364'; --IRB
	  V_LV1_PARA := '';
	  V_INSERT_COA_PARA := ' PROD_CODE, ';
	  V_SELECT_COA_PARA := ' T.PROD_CODE, ';
	  V_DIM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_INV_ITEM_MANUFACTURE_T';
	  V_DIM_PARA := '';
	  V_INVENTORY_ORG_CODE := 'H80';
	  V_INSERT_LV4_PARA := '';
	  V_SELECT_LV4_PARA_T := '';
	  V_SELECT_LV4_PARA_CP := '';
	  V_SELECT_LV4_PARA_D := '';
   --判断入参为ICT还是数字能源
   ELSIF f_industry_flag =  'E' THEN 
	  V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_BOM_ITEM_SHIP_DTL_T';
	  V_LV0_PARA := '153462'; --数字能源
	  V_LV1_PARA := '';
	  V_INSERT_COA_PARA := ' PROD_CODE, PROD_CN_NAME ,';
	  V_SELECT_COA_PARA := ' T.PROD_CODE, T.PROD_CN_NAME ,';
	  V_DIM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_INV_ITEM_MANUFACTURE_T';
	  V_DIM_PARA := ' AND M.APD_MANUFACTURE_PROD_LV0 IN (''数字能源'', ''数字能源TMT'') ';
	  V_INVENTORY_ORG_CODE := 'NY1';
	  V_INSERT_LV4_PARA := '';
	  V_SELECT_LV4_PARA_T := '';
	  V_SELECT_LV4_PARA_CP := '';
	  V_SELECT_LV4_PARA_D := '';
	--判断入参为ICT还是数字能源还是IAS  
	ELSIF f_industry_flag =  'IAS' THEN  
	  V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_BOM_ITEM_SHIP_DTL_T';
	  V_LV0_PARA :=  '104210' ; --IAS
	  V_LV1_PARA := 'AND CP.LV1_PROD_RND_TEAM_CODE = ''144349'' ';
	  V_INSERT_COA_PARA := ' PROD_CODE, PROD_CN_NAME ,';
	  V_SELECT_COA_PARA := ' T.PROD_CODE, T.PROD_CN_NAME ,';
	  V_DIM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_INV_ITEM_MANUFACTURE_T';
	  V_DIM_PARA := ' AND M.APD_MANUFACTURE_PROD_LV0 IN (''IAS'') AND M.APD_MANUFACTURE_PROD_LV1 IN (''IAS'') '; 
	  V_INVENTORY_ORG_CODE := 'H80';	  
	  V_INSERT_LV4_PARA := 'LV4_PROD_RND_TEAM_CODE, LV4_PROD_RD_TEAM_CN_NAME, ';
	  V_SELECT_LV4_PARA_T := 'T.LV4_PROD_RND_TEAM_CODE, T.LV4_PROD_RD_TEAM_CN_NAME, ';
	  V_SELECT_LV4_PARA_CP := 'CP.LV4_PROD_RND_TEAM_CODE, CP.LV4_PROD_RD_TEAM_CN_NAME, ';
	  V_SELECT_LV4_PARA_D := 'D.LV4_PROD_RND_TEAM_CODE, D.LV4_PROD_RD_TEAM_CN_NAME, ';
   ELSE	
		RETURN '入参有误';
   
   END IF;
   

  --清空目标表数据:
	V_SQL := 'TRUNCATE TABLE '||V_TO_TABLE ;
	EXECUTE IMMEDIATE V_SQL;
   
   

  
   --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清除'||V_TO_TABLE||'表的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
 IF F_INDUSTRY_FLAG = 'I' THEN 
  -- 查询该月版本是否已存在，若存在，沿用，否则新建 
  SELECT COUNT(1) INTO V_CURRENT_FLAG
    FROM
        FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T 
    WHERE
        VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY';
    -- FLAG 不等于0，说明已有版本号，沿用        
  IF V_CURRENT_FLAG <> 0 THEN 
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
        FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T 
    WHERE
        VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY';
  ELSE
  --新版本号赋值
  SELECT NEXTVAL('FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S')
    INTO V_VERSION_ID
    FROM DUAL;
     
  --往版本信息表记录本次TOP品类版本号, 版本号为V_VERSION_ID, 依赖的品类专家维V_DIM_VERSION_ID
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   IS_RUNNING)
   VALUES
   (V_VERSION_ID,V_DIM_VERSION_ID,V_VERSION_NAME,1,'AUTO','CATEGORY',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N','Y');
   END IF;
   
  --查询品类专家团映射关系的最新版本号; 
  SELECT MAX(T.VERSION_ID) 
    INTO V_DIM_VERSION_ID 
    FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'DIMENSION_MADE'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1;
	 
	 --如果是数字能源
 ELSIF F_INDUSTRY_FLAG = 'E' THEN 
     SELECT COUNT(1) INTO V_CURRENT_FLAG
    FROM
        FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T 
    WHERE
        VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY';
    -- FLAG 不等于0，说明已有版本号，沿用        
  IF V_CURRENT_FLAG <> 0 THEN 
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
        FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T 
    WHERE
        VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY';
  ELSE
  --新版本号赋值
  SELECT NEXTVAL('FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_S')
    INTO V_VERSION_ID
    FROM DUAL;
     
  --往版本信息表记录本次TOP品类版本号, 版本号为V_VERSION_ID, 依赖的品类专家维V_DIM_VERSION_ID
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   IS_RUNNING)
   VALUES
   (V_VERSION_ID,V_DIM_VERSION_ID,V_VERSION_NAME,1,'AUTO','CATEGORY',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N','Y');
   END IF;
   
  --查询品类专家团映射关系的最新版本号; 
  SELECT MAX(T.VERSION_ID) 
    INTO V_DIM_VERSION_ID 
    FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'DIMENSION_MADE'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1;
 
 
 
 	 --如果是数字能源
 ELSIF F_INDUSTRY_FLAG = 'IAS' THEN 
     SELECT COUNT(1) INTO V_CURRENT_FLAG
    FROM
        FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T 
    WHERE
        VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY';
    -- FLAG 不等于0，说明已有版本号，沿用        
  IF V_CURRENT_FLAG <> 0 THEN 
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
        FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T 
    WHERE
        VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY';
  ELSE
  --新版本号赋值
  SELECT NEXTVAL('FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_S')
    INTO V_VERSION_ID
    FROM DUAL;
     
  --往版本信息表记录本次TOP品类版本号, 版本号为V_VERSION_ID, 依赖的品类专家维V_DIM_VERSION_ID
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   IS_RUNNING)
   VALUES
   (V_VERSION_ID,V_DIM_VERSION_ID,V_VERSION_NAME,1,'AUTO','CATEGORY',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N','Y');
   END IF;
   
  --查询品类专家团映射关系的最新版本号; 
  SELECT MAX(T.VERSION_ID) 
    INTO V_DIM_VERSION_ID 
    FROM FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'DIMENSION_MADE'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1;
 
 
 
 ELSE
  NULL;
END IF;
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '往版本信息表记录TOP品类版本号信息, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
 
 IF F_INDUSTRY_FLAG = 'IAS' THEN 
 
  --往目标表里插数
	V_SQL := 'INSERT INTO '||V_TO_TABLE||'
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE,
     LV2_PROD_RD_TEAM_CN_NAME,
     LV3_PROD_RND_TEAM_CODE,
     LV3_PROD_RD_TEAM_CN_NAME,
	 '||V_INSERT_LV4_PARA||' --2407版本新增
     LV0_PROD_LIST_CODE,--销售维度BG代码(9月需求调整)
     LV0_PROD_LIST_CN_NAME,--销售维度BG中文名称(9月需求调整)
     LV0_PROD_LIST_EN_NAME,--销售维度BG英文名称(9月需求调整)
     DIMENSION_CODE,  --量纲编码(9月需求调整)
     DIMENSION_CN_NAME,  --量纲中文名称(9月需求调整)
     DIMENSION_EN_NAME,  --量纲英文名称(9月需求调整)
     DIMENSION_SUBCATEGORY_CODE,  --量纲子类编码(9月需求调整)
     DIMENSION_SUBCATEGORY_CN_NAME,  --量纲子类中文名称(9月需求调整)
     DIMENSION_SUBCATEGORY_EN_NAME,  --量纲子类英文名称(9月需求调整)
     DIMENSION_SUB_DETAIL_CODE,  --量纲子类明细编码(9月需求调整)
     DIMENSION_SUB_DETAIL_CN_NAME,  --量纲子类明细中文名称(9月需求调整)
     DIMENSION_SUB_DETAIL_EN_NAME,  --量纲子类明细英文名称(9月需求调整)
	 SHIPPING_OBJECT_CODE,	 -- 发货对象  11月需求调整
	 SHIPPING_OBJECT_CN_NAME, --发货对象名称 11月需求调整
	 MANUFACTURE_OBJECT_CODE, -- 制造对象  11月需求调整
	 MANUFACTURE_OBJECT_CN_NAME, --制造对象名称 11月需求调整
     ITEM_CODE,
     ITEM_CN_NAME,
     SHIP_QUANTITY,
     RMB_AVG_AMT, --本层制造成本均本成本
	 rmb_cost_amt,
	 '||V_INSERT_COA_PARA||' --24年五月版本
     CN_DESCRIPTION,
     PARENTPARTNUMBER,
     PARENT_SHIP_QUANTITY,
     NON_SALE_FLAG,
     OVERSEA_FLAG,--是否海外标识:Y是(海外O),N否(国内I)(9月需求调整)
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     PRIMARY_ID,
     L1_NAME,
     L2_NAME
     )
SELECT     T.VERSION_ID,
           T.PERIOD_YEAR,
           T.PERIOD_ID, --年月
           T.LV0_PROD_RND_TEAM_CODE, --重量级团队LV0代码
           T.LV0_PROD_RD_TEAM_CN_NAME, --重量级团队LV0中文名称
           T.LV1_PROD_RND_TEAM_CODE, --重量级团队LV1代码
           T.LV1_PROD_RD_TEAM_CN_NAME, --重量级团队LV1中文名称
           T.LV2_PROD_RND_TEAM_CODE, --重量级团队LV2代码
           T.LV2_PROD_RD_TEAM_CN_NAME, --重量级团队LV2中文名称
           T.LV3_PROD_RND_TEAM_CODE, --重量级团队LV3代码
           T.LV3_PROD_RD_TEAM_CN_NAME, --重量级团队LV3中文名称
		   '||V_SELECT_LV4_PARA_T||' --24年7月版本
           T.LV0_PROD_LIST_CODE,--销售维度BG代码(9月需求调整)
           T.LV0_PROD_LIST_CN_NAME,--销售维度BG中文名称(9月需求调整)
           T.LV0_PROD_LIST_EN_NAME,--销售维度BG英文名称(9月需求调整)
           T.DIMENSION_CODE,  --量纲编码(9月需求调整)
           T.DIMENSION_CN_NAME,  --量纲中文名称(9月需求调整)
           T.DIMENSION_EN_NAME,  --量纲英文名称(9月需求调整)
           T.DIMENSION_SUBCATEGORY_CODE,  --量纲子类编码(9月需求调整)
           T.DIMENSION_SUBCATEGORY_CN_NAME,  --量纲子类中文名称(9月需求调整)
           T.DIMENSION_SUBCATEGORY_EN_NAME,  --量纲子类英文名称(9月需求调整)
           T.DIMENSION_SUB_DETAIL_CODE,  --量纲子类明细编码(9月需求调整)
           T.DIMENSION_SUB_DETAIL_CN_NAME,  --量纲子类明细中文名称(9月需求调整)
           T.DIMENSION_SUB_DETAIL_EN_NAME,  --量纲子类明细英文名称(9月需求调整)
		   --T.OPERATION_OBJECT,   -- 经营对象  11月需求调整
		   T.SHIPPING_OBJECT_CODE,	 -- 发货对象  11月需求调整
		   T.SHIPPING_OBJECT_CN_NAME, --发货对象名称 11月需求调整
		   T.MANUFACTURE_OBJECT_CODE, -- 制造对象  11月需求调整
		   T.MANUFACTURE_OBJECT_CN_NAME, --制造对象名称 11月需求调整
           T.ITEM_CODE, --子项ITEM编码
           T.ITEM_CN_NAME, --子项ITEM名称
           T.SHIP_QUANTITY, --子项ITEM发货量
		   T.RMB_AVG_AMT, --本层制造成本均本成本
		   T.rmb_cost_amt,
		   '||V_SELECT_COA_PARA||' --24年五月版本
           T.CN_DESCRIPTION, --产品名称
           T.PARENTPARTNUMBER, --父项ITEM编码
           T.PARENT_SHIP_QUANTITY, --父项编码发货数量(缺少父项ITEM名称字段匹配规则)
           T.NON_SALE_FLAG, --非销售标识
           T.OVERSEA_FLAG,--是否海外标识:Y是(海外O),N否(国内I)(9月需求调整)
           T.CREATED_BY,
           T.CREATION_DATE,
           T.LAST_UPDATED_BY,
           T.LAST_UPDATE_DATE,
           T.DEL_FLAG,
           T.PRIMARY_ID,
           T.L1_NAME, --盈利颗粒度L1层级
           CASE WHEN T.FLAG = 0 THEN NVL(L2S.L2_NAME,''其他'')
                ELSE NVL(T.L2_NAME,''其他'')
                END AS L2_NAME --盈利颗粒度L2层级
FROM 
(
    SELECT 
			'||V_VERSION_ID||' AS VERSION_ID,
           CAST(SUBSTR(TO_CHAR(D.PERIOD_ID), 1, 4) AS BIGINT) AS PERIOD_YEAR,
           D.PERIOD_ID, --年月
           D.LV0_PROD_RND_TEAM_CODE, --重量级团队LV0代码(9月需求调整)
           D.LV0_PROD_RD_TEAM_CN_NAME, --重量级团队LV0中文名称(9月需求调整)
           D.LV1_PROD_RND_TEAM_CODE, --重量级团队LV1代码(9月需求调整)
           D.LV1_PROD_RD_TEAM_CN_NAME, --重量级团队LV1中文名称(9月需求调整)
           D.LV2_PROD_RND_TEAM_CODE, --重量级团队LV2代码(9月需求调整)
           D.LV2_PROD_RD_TEAM_CN_NAME, --重量级团队LV2中文名称(9月需求调整)
           D.LV3_PROD_RND_TEAM_CODE, --重量级团队LV3代码(9月需求调整)
           D.LV3_PROD_RD_TEAM_CN_NAME, --重量级团队LV3中文名称(9月需求调整)
		   '||V_SELECT_LV4_PARA_D||'
           CASE WHEN D.LV0_PROD_LIST_CODE IN ( ''PDCG901159'', ''DCG817296'', ''PDCG901160'' ) THEN
                     D.LV0_PROD_LIST_CODE ELSE ''其他'' 
              END AS LV0_PROD_LIST_CODE,--销售维度BG代码(9月需求调整)
           CASE WHEN D.LV0_PROD_LIST_CODE IN ( ''PDCG901159'', ''PDCG817296'', ''PDCG901160'' ) THEN
                     D.LV0_PROD_LIST_CN_NAME ELSE ''其他'' 
              END AS LV0_PROD_LIST_CN_NAME,--销售维度BG中文名称(9月需求调整)
           CASE WHEN D.LV0_PROD_LIST_CODE IN ( ''PDCG901159'', ''PDCG817296'', ''PDCG901160'' ) THEN
                     D.LV0_PROD_LIST_EN_NAME ELSE ''OTHER'' 
              END AS LV0_PROD_LIST_EN_NAME,--销售维度BG英文名称(9月需求调整)
           PD.PRODUCT_DIMENSION_CODE AS DIMENSION_CODE,  --量纲编码(9月需求调整)
           PD.PRODUCT_DIMENSION_CN_NAME AS DIMENSION_CN_NAME,  --量纲中文名称(9月需求调整)
           PD.PRODUCT_DIMENSION_EN_NAME AS DIMENSION_EN_NAME,  --量纲英文名称(9月需求调整)
           PD.DIMENSION_SUBCATEGORY_CODE,  --量纲子类编码(9月需求调整)
           PD.DIMENSION_SUBCATEGORY_CN_NAME,  --量纲子类中文名称(9月需求调整)
           PD.DIMENSION_SUBCATEGORY_EN_NAME,  --量纲子类英文名称(9月需求调整)
           PD.DIMENSION_SUB_DETAIL_CODE,  --量纲子类明细编码(9月需求调整)
           PD.DIMENSION_SUB_DETAIL_CN_NAME,  --量纲子类明细中文名称(9月需求调整)
           PD.DIMENSION_SUB_DETAIL_EN_NAME,  --量纲子类明细英文名称(9月需求调整)
		   M.APD_SHIPMENT_OBJECT AS SHIPPING_OBJECT_CODE,	 --发货对象  11月需求调整
		   M.APD_SHIPMENT_OBJECT AS SHIPPING_OBJECT_CN_NAME, --发货对象名称 11月需求调整(暂无CODE，均用名称代替)
		   M.APD_MANUFACTURE_OBJECT AS MANUFACTURE_OBJECT_CODE, --制造对象  11月需求调整
		   M.APD_MANUFACTURE_OBJECT AS MANUFACTURE_OBJECT_CN_NAME,  --制造对象名称  11月需求调整(暂无CODE，均用名称代替)
           D.ITEM_CODE, --子项ITEM编码
           NULL AS ITEM_CN_NAME, --子项ITEM名称
           D.SHIP_QUANTITY, --子项ITEM发货量
           D.RMB_AVG_AMT, --本层制造成本均本成本
		   D.rmb_cost_amt, --制造成本
           D.PROD_CODE, --产品编码
		   D.PROD_CN_NAME, --产品名称
           D.CN_DESCRIPTION, --产品名称
           D.PARENTPARTNUMBER, --父项ITEM编码
           D.PARENT_SHIP_QUANTITY, --父项编码发货数量(缺少父项ITEM名称字段匹配规则)
           D.NON_SALE_FLAG, --非销售标识
           DECODE(RC.OVERSEA_FLAG,''Y'',''O'',''N'',''I'') AS OVERSEA_FLAG,--是否海外标识:Y是(海外O),N否(国内I)(9月需求调整)
           ''-1'' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           ''-1'' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           D.PRIMARY_ID,
           COALESCE(L1.L1_NAME, L1B.L1_NAME, CFG.L1_NAME, CFG2.L1_NAME, ''其他'') AS L1_NAME, --盈利颗粒度L1层级
           L2C.L2_NAME AS L2_NAME, --COA维表盈利颗粒度L2层级
           COUNT(L2C.L2_NAME) OVER(PARTITION BY COALESCE(L1.L1_NAME, L1B.L1_NAME, CFG.L1_NAME, CFG2.L1_NAME)) AS FLAG --标识COA维表L2_NAME为空的数据为0
		   FROM
		   (SELECT P.PERIOD_ID,
			   P.ITEM_CODE,
			   CASE WHEN N.Original_ITEM_CODE IS NOT NULL THEN N.FINAL_ITEM_CODE
			   ELSE P.ITEM_CODE END AS OBJECT_ITEM_CODE,
               P.ITEM_CN_NAME,
               P.SHIP_QUANTITY, --子项ITEM发货量
               P.PARENTPARTNUMBER, --父项ITEM编码
               P.PARENT_SHIP_QUANTITY, --父项编码发货数量(缺少父项ITEM名称字段匹配规则)
               P.NON_SALE_FLAG, --非销售标识
			   IM.RMB_AVG_AMT,  --制造成本均本
               NVL((P.SHIP_QUANTITY * IM.RMB_AVG_AMT),0) as rmb_cost_amt, --制造成本
               P.PROD_KEY,
               P.MAIN_DIMENSION_KEY,
               P.MODEL_NUM,
               P.RECOGNISE_TYPE_ID,
               P.IS_RESALE_FLAG,
               P.PRIMARY_ID,
               P.GEO_PC_KEY,
			   CP.PROD_CODE, --产品编码
		   CP.PROD_CN_NAME, --产品名称
		   CP.CN_DESCRIPTION, --产品名称
		   CP.LV0_PROD_RND_TEAM_CODE, --重量级团队LV0代码(9月需求调整)
           CP.LV0_PROD_RD_TEAM_CN_NAME, --重量级团队LV0中文名称(9月需求调整)
           CP.LV1_PROD_RND_TEAM_CODE, --重量级团队LV1代码(9月需求调整)
           CP.LV1_PROD_RD_TEAM_CN_NAME, --重量级团队LV1中文名称(9月需求调整)
           CP.LV2_PROD_RND_TEAM_CODE, --重量级团队LV2代码(9月需求调整)
           CP.LV2_PROD_RD_TEAM_CN_NAME, --重量级团队LV2中文名称(9月需求调整)
           CP.LV3_PROD_RND_TEAM_CODE, --重量级团队LV3代码(9月需求调整)
           CP.LV3_PROD_RD_TEAM_CN_NAME, --重量级团队LV3中文名称(9月需求调整)
		   '||V_SELECT_LV4_PARA_CP||'
			CP.LV0_PROD_LIST_CODE	,
			CP.LV0_PROD_LIST_CN_NAME,
			CP.LV0_PROD_LIST_EN_NAME		
          FROM FIN_DM_OPT_FOI.DWL_PROD_BOM_ITEM_SHIP_DIM_I P  
		INNER JOIN DMDIM.DM_DIM_PRODUCT_D CP --重量级团队匹配维表(9月需求调整)
        ON P.PROD_KEY = CP.PROD_KEY
       AND CP.LV0_PROD_RND_TEAM_CODE = '''||V_LV0_PARA||''' 
		'||V_LV1_PARA||'
       -- AND CP.PROD_POV_ID = 4 --(9月需求调整)
       AND P.SHIP_QUANTITY >= 0 -- 数量非负数
	  --AND P.MODEL_NUM IN (''AI'', ''PH'', ''FG'') -- 11月版本调整  模板属性取AI、PH、FG件
       AND P.RECOGNISE_TYPE_ID = 4 --取供应中心发区域（4）
       --AND P.IS_RESALE_FLAG = ''N'' --去除转售重复数据(9月需求调整)
		  
         INNER JOIN (SELECT DISTINCT IM.PERIOD_ID,
                                    IM.COST_TYPE_NAME,
                                    IM.MATERIAL_ID,
                                    DM.MATERIAL_CODE,
                                    IM.INVENTORY_ORG_ID,
                                    IO.INVENTORY_ORG_CODE,
                                    (NVL(IM.TL_RESOURCE,0) +
                                    NVL(IM.TL_OUTSIDE_PROCESSING,0) +
                                    NVL(IM.TL_OVERHEAD,0)) AS RMB_AVG_AMT
                      FROM FIN_DM_OPT_FOI.DWL_PROD_INV_MATERIAL_COST_I IM
                      LEFT JOIN DWRDIM.DWR_DIM_INVENTORY_ORG_D IO
                        ON IM.INVENTORY_ORG_ID = IO.INVENTORY_ORG_ID
                      JOIN DWRDIM.DWR_DIM_MATERIAL_D DM
                        ON IM.MATERIAL_ID = DM.MATERIAL_ID
                       WHERE (NVL(IM.TL_RESOURCE,33333) + NVL(IM.TL_OUTSIDE_PROCESSING,33333) + NVL(IM.TL_OVERHEAD,33333)) <> 99999  -- 11月版本调整  物料标准成本卷积历史
                       AND IO.INVENTORY_ORG_CODE = '''||V_INVENTORY_ORG_CODE||''' --获取其INVENTORY_ORG_CODE(库存组织代码)作为筛选字段=NY1  --202405版本修改 原为H80
                       AND IM.COST_TYPE_NAME = ''Group''
                       AND DM.SCD_ACTIVE_IND = 1
					   --AND IM.BASED_ON_ROLLUP_FLAG = 1   --202405版本新增
					   ) IM
            ON P.ITEM_CODE = IM.MATERIAL_CODE
           AND P.PERIOD_ID = IM.PERIOD_ID
		   AND CASE WHEN MONTH(CURRENT_TIMESTAMP) = 1 THEN CAST(SUBSTR(TO_CHAR(P.PERIOD_ID),1,4) AS BIGINT) >= YEAR(CURRENT_TIMESTAMP) -4 
				WHEN MONTH(CURRENT_TIMESTAMP) != 1 THEN CAST(SUBSTR(TO_CHAR(P.PERIOD_ID),1,4) AS BIGINT) >= YEAR(CURRENT_TIMESTAMP) -3 
				END
		   LEFT JOIN (SELECT * FROM DM_FOC_IAS_SHIP_RECURSIVE_RESULT WHERE STEP != 0 )N
		ON P.ITEM_CODE = N.ORIGINAL_ITEM_CODE 
		AND P.PERIOD_ID = N.PERIOD_ID
		AND CP.LV0_PROD_RND_TEAM_CODE = N.LV0_PROD_RND_TEAM_CODE
		AND CP.LV1_PROD_RND_TEAM_CODE = N.LV1_PROD_RND_TEAM_CODE
		AND CP.LV2_PROD_RND_TEAM_CODE = N.LV2_PROD_RND_TEAM_CODE
		AND CP.LV3_PROD_RND_TEAM_CODE = N.LV3_PROD_RND_TEAM_CODE
		AND CP.LV4_PROD_RND_TEAM_CODE = N.LV4_PROD_RND_TEAM_CODE
		AND CP.LV0_PROD_LIST_CODE     = N.LV0_PROD_LIST_CODE
		AND CP.PROD_CODE = N.PROD_CODE
		AND P.PARENTPARTNUMBER =  N.SPART_CODE
 
		   ) D
		
	 INNER JOIN '||V_DIM_TABLE||' M -- 1月版本调整  制造量纲维度表
      ON D.OBJECT_ITEM_CODE = M.ITEM_CODE
	  AND M.VERSION_ID = TO_CHAR(CURRENT_TIMESTAMP,''YYYYMM'')
	  '||V_DIM_PARA||'
	  AND M.APD_SHIPMENT_OBJECT IS NOT NULL
	  AND M.APD_MANUFACTURE_OBJECT IS NOT NULL
     
     LEFT JOIN (SELECT * FROM DMDIM.DM_DIM_PRODUCTDIMENSION_D WHERE SCD_ACTIVE_IND = 1 )PD  --量纲维表(9月需求调整)
        ON D.MAIN_DIMENSION_KEY = PD.DIMENSION_KEY
      LEFT JOIN FIN_DM_OPT_FOP.APD_FOP_ICT_FCST_HOLISTIC_VIEW_T L1 --ICT业务预测全景图,7月版本增加盈利颗粒度L1、L2层级
        ON D.LV1_PROD_RND_TEAM_CODE = L1.LV1_CODE
       AND D.LV2_PROD_RND_TEAM_CODE = L1.LV2_CODE
       AND D.LV3_PROD_RND_TEAM_CODE = L1.LV3_CODE
       AND L1.LV3_CODE IS NOT NULL -- 先LV3_CODE有值的进行关联
       AND L1.DEL_FLAG = ''N'' AND UPPER(L1.STATUS) = ''SUBMIT'' 
      LEFT JOIN FIN_DM_OPT_FOP.APD_FOP_ICT_FCST_HOLISTIC_VIEW_T L1B --ICT业务预测全景图,7月版本增加盈利颗粒度L1、L2层级
        ON D.LV1_PROD_RND_TEAM_CODE = L1B.LV1_CODE
       AND D.LV2_PROD_RND_TEAM_CODE = L1B.LV2_CODE
       AND L1B.LV2_CODE IS NOT NULL
       AND L1B.LV3_CODE IS NULL -- LV3_CODE无值时关联到LV2_CODE取值即可
       AND L1B.DEL_FLAG = ''N'' AND UPPER(L1B.STATUS) = ''SUBMIT'' 
      LEFT JOIN FIN_DM_OPT_FOI.DM_FOC_PROD_L1_CFG_T CFG -- 其他场景:重量级团队和L1_NAME关系的配置表
        ON D.LV1_PROD_RD_TEAM_CN_NAME = CFG.LV1_PROD_RD_TEAM_CN_NAME
       AND D.LV2_PROD_RD_TEAM_CN_NAME = CFG.LV2_PROD_RD_TEAM_CN_NAME
       AND D.LV3_PROD_RD_TEAM_CN_NAME = CFG.LV3_PROD_RD_TEAM_CN_NAME
       AND CFG.LV3_PROD_RD_TEAM_CN_NAME IS NOT NULL -- 先LV3_PROD_RD_TEAM_CN_NAME有值的进行关联
      LEFT JOIN FIN_DM_OPT_FOI.DM_FOC_PROD_L1_CFG_T CFG2 -- 其他场景:重量级团队和L1_NAME关系的配置表
        ON D.LV1_PROD_RD_TEAM_CN_NAME = CFG2.LV1_PROD_RD_TEAM_CN_NAME
       AND D.LV2_PROD_RD_TEAM_CN_NAME = CFG2.LV2_PROD_RD_TEAM_CN_NAME
       AND CFG2.LV2_PROD_RD_TEAM_CN_NAME IS NOT NULL
       AND CFG2.LV3_PROD_RD_TEAM_CN_NAME IS NULL -- LV3_PROD_RD_TEAM_CN_NAME无值时关联到LV2取值即可
      LEFT JOIN FIN_DM_OPT_FOP.APD_FOP_COA_L1_T L2C --COA维表
       ON COALESCE(L1.L1_NAME, L1B.L1_NAME, CFG.L1_NAME, CFG2.L1_NAME) = L2C.L1_NAME
      AND D.PROD_CODE = L2C.COA_CODE
      AND L2C.DEL_FLAG = ''N'' AND UPPER(L2C.STATUS) = ''SUBMIT'' 
      LEFT JOIN DWRDIM.DWR_DIM_REGION_RC_D RC --(9月需求调整)国内海外区分通过主表关联COA区域维表，取OVERSEA_FLAG字段作为国内/海外标签
       ON D.GEO_PC_KEY = RC.GEO_PC_KEY
      
      ) T
      LEFT JOIN (SELECT DISTINCT ITEM_CODE,L1_NAME,L2_NAME FROM FIN_DM_OPT_FOP.DM_DIM_FOP_SPART_PROFITING_RELATION_T --Spart与L1、L2、L3关系表
                  WHERE DEL_FLAG = ''N''
                    AND UPPER(STATUS) = ''SUBMIT''  -- 状态（SAVE 保存、SUBMIT 提交）
                    AND PERIOD_ID = (SELECT MAX(PERIOD_ID) FROM FIN_DM_OPT_FOP.DM_DIM_FOP_SPART_PROFITING_RELATION_T)  --取最大的年月
                ) L2S
       ON T.L1_NAME = L2S.L1_NAME
      AND T.PARENTPARTNUMBER = L2S.ITEM_CODE'
      ;
	  DBMS_OUTPUT.PUT_LINE (V_SQL);
	  EXECUTE IMMEDIATE V_SQL;
  
  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插数到 '||V_TO_TABLE||'表, 新版本号='||V_VERSION_ID||', 所依赖的品类-专家团的版本号='||V_DIM_VERSION_ID,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  
  --3.收集统计信息
  V_SQL := 'ANALYZE '||V_TO_TABLE;
   EXECUTE IMMEDIATE V_SQL;
  
  
  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集 '||V_TO_TABLE||'统计信息完成!');
   
   ELSIF F_INDUSTRY_FLAG != 'IAS' THEN 
   
   --往目标表里插数
	V_SQL := 'INSERT INTO '||V_TO_TABLE||'
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE,
     LV2_PROD_RD_TEAM_CN_NAME,
     LV3_PROD_RND_TEAM_CODE,
     LV3_PROD_RD_TEAM_CN_NAME,
	 '||V_INSERT_LV4_PARA||' --2407版本新增
     LV0_PROD_LIST_CODE,--销售维度BG代码(9月需求调整)
     LV0_PROD_LIST_CN_NAME,--销售维度BG中文名称(9月需求调整)
     LV0_PROD_LIST_EN_NAME,--销售维度BG英文名称(9月需求调整)
     DIMENSION_CODE,  --量纲编码(9月需求调整)
     DIMENSION_CN_NAME,  --量纲中文名称(9月需求调整)
     DIMENSION_EN_NAME,  --量纲英文名称(9月需求调整)
     DIMENSION_SUBCATEGORY_CODE,  --量纲子类编码(9月需求调整)
     DIMENSION_SUBCATEGORY_CN_NAME,  --量纲子类中文名称(9月需求调整)
     DIMENSION_SUBCATEGORY_EN_NAME,  --量纲子类英文名称(9月需求调整)
     DIMENSION_SUB_DETAIL_CODE,  --量纲子类明细编码(9月需求调整)
     DIMENSION_SUB_DETAIL_CN_NAME,  --量纲子类明细中文名称(9月需求调整)
     DIMENSION_SUB_DETAIL_EN_NAME,  --量纲子类明细英文名称(9月需求调整)
	 SHIPPING_OBJECT_CODE,	 -- 发货对象  11月需求调整
	 SHIPPING_OBJECT_CN_NAME, --发货对象名称 11月需求调整
	 MANUFACTURE_OBJECT_CODE, -- 制造对象  11月需求调整
	 MANUFACTURE_OBJECT_CN_NAME, --制造对象名称 11月需求调整
     ITEM_CODE,
     ITEM_CN_NAME,
     SHIP_QUANTITY,
     RMB_AVG_AMT, --本层制造成本均本成本
	 rmb_cost_amt,
	 '||V_INSERT_COA_PARA||' --24年五月版本
     CN_DESCRIPTION,
     PARENTPARTNUMBER,
     PARENT_SHIP_QUANTITY,
     NON_SALE_FLAG,
     OVERSEA_FLAG,--是否海外标识:Y是(海外O),N否(国内I)(9月需求调整)
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     PRIMARY_ID,
     L1_NAME,
     L2_NAME
     )
SELECT     T.VERSION_ID,
           T.PERIOD_YEAR,
           T.PERIOD_ID, --年月
           T.LV0_PROD_RND_TEAM_CODE, --重量级团队LV0代码
           T.LV0_PROD_RD_TEAM_CN_NAME, --重量级团队LV0中文名称
           T.LV1_PROD_RND_TEAM_CODE, --重量级团队LV1代码
           T.LV1_PROD_RD_TEAM_CN_NAME, --重量级团队LV1中文名称
           T.LV2_PROD_RND_TEAM_CODE, --重量级团队LV2代码
           T.LV2_PROD_RD_TEAM_CN_NAME, --重量级团队LV2中文名称
           T.LV3_PROD_RND_TEAM_CODE, --重量级团队LV3代码
           T.LV3_PROD_RD_TEAM_CN_NAME, --重量级团队LV3中文名称
		   '||V_SELECT_LV4_PARA_T||' --24年7月版本
           T.LV0_PROD_LIST_CODE,--销售维度BG代码(9月需求调整)
           T.LV0_PROD_LIST_CN_NAME,--销售维度BG中文名称(9月需求调整)
           T.LV0_PROD_LIST_EN_NAME,--销售维度BG英文名称(9月需求调整)
           T.DIMENSION_CODE,  --量纲编码(9月需求调整)
           T.DIMENSION_CN_NAME,  --量纲中文名称(9月需求调整)
           T.DIMENSION_EN_NAME,  --量纲英文名称(9月需求调整)
           T.DIMENSION_SUBCATEGORY_CODE,  --量纲子类编码(9月需求调整)
           T.DIMENSION_SUBCATEGORY_CN_NAME,  --量纲子类中文名称(9月需求调整)
           T.DIMENSION_SUBCATEGORY_EN_NAME,  --量纲子类英文名称(9月需求调整)
           T.DIMENSION_SUB_DETAIL_CODE,  --量纲子类明细编码(9月需求调整)
           T.DIMENSION_SUB_DETAIL_CN_NAME,  --量纲子类明细中文名称(9月需求调整)
           T.DIMENSION_SUB_DETAIL_EN_NAME,  --量纲子类明细英文名称(9月需求调整)
		   --T.OPERATION_OBJECT,   -- 经营对象  11月需求调整
		   T.SHIPPING_OBJECT_CODE,	 -- 发货对象  11月需求调整
		   T.SHIPPING_OBJECT_CN_NAME, --发货对象名称 11月需求调整
		   T.MANUFACTURE_OBJECT_CODE, -- 制造对象  11月需求调整
		   T.MANUFACTURE_OBJECT_CN_NAME, --制造对象名称 11月需求调整
           T.ITEM_CODE, --子项ITEM编码
           T.ITEM_CN_NAME, --子项ITEM名称
           T.SHIP_QUANTITY, --子项ITEM发货量
		   T.RMB_AVG_AMT, --本层制造成本均本成本
		   T.rmb_cost_amt,
		   '||V_SELECT_COA_PARA||' --24年五月版本
           T.CN_DESCRIPTION, --产品名称
           T.PARENTPARTNUMBER, --父项ITEM编码
           T.PARENT_SHIP_QUANTITY, --父项编码发货数量(缺少父项ITEM名称字段匹配规则)
           T.NON_SALE_FLAG, --非销售标识
           T.OVERSEA_FLAG,--是否海外标识:Y是(海外O),N否(国内I)(9月需求调整)
           T.CREATED_BY,
           T.CREATION_DATE,
           T.LAST_UPDATED_BY,
           T.LAST_UPDATE_DATE,
           T.DEL_FLAG,
           T.PRIMARY_ID,
           T.L1_NAME, --盈利颗粒度L1层级
           CASE WHEN T.FLAG = 0 THEN NVL(L2S.L2_NAME,''其他'')
                ELSE NVL(T.L2_NAME,''其他'')
                END AS L2_NAME --盈利颗粒度L2层级
FROM 
(
    SELECT 
			'||V_VERSION_ID||' AS VERSION_ID,
           CAST(SUBSTR(TO_CHAR(D.PERIOD_ID), 1, 4) AS BIGINT) AS PERIOD_YEAR,
           D.PERIOD_ID, --年月
           CP.LV0_PROD_RND_TEAM_CODE, --重量级团队LV0代码(9月需求调整)
           CP.LV0_PROD_RD_TEAM_CN_NAME, --重量级团队LV0中文名称(9月需求调整)
           CP.LV1_PROD_RND_TEAM_CODE, --重量级团队LV1代码(9月需求调整)
           CP.LV1_PROD_RD_TEAM_CN_NAME, --重量级团队LV1中文名称(9月需求调整)
           CP.LV2_PROD_RND_TEAM_CODE, --重量级团队LV2代码(9月需求调整)
           CP.LV2_PROD_RD_TEAM_CN_NAME, --重量级团队LV2中文名称(9月需求调整)
           CP.LV3_PROD_RND_TEAM_CODE, --重量级团队LV3代码(9月需求调整)
           CP.LV3_PROD_RD_TEAM_CN_NAME, --重量级团队LV3中文名称(9月需求调整)
		   '||V_SELECT_LV4_PARA_CP||'
           CASE WHEN CP.LV0_PROD_LIST_CODE IN ( ''PDCG901159'', ''DCG817296'', ''PDCG901160'' ) THEN
                     CP.LV0_PROD_LIST_CODE ELSE ''其他'' 
              END AS LV0_PROD_LIST_CODE,--销售维度BG代码(9月需求调整)
           CASE WHEN CP.LV0_PROD_LIST_CODE IN ( ''PDCG901159'', ''PDCG817296'', ''PDCG901160'' ) THEN
                     CP.LV0_PROD_LIST_CN_NAME ELSE ''其他'' 
              END AS LV0_PROD_LIST_CN_NAME,--销售维度BG中文名称(9月需求调整)
           CASE WHEN CP.LV0_PROD_LIST_CODE IN ( ''PDCG901159'', ''PDCG817296'', ''PDCG901160'' ) THEN
                     CP.LV0_PROD_LIST_EN_NAME ELSE ''OTHER'' 
              END AS LV0_PROD_LIST_EN_NAME,--销售维度BG英文名称(9月需求调整)
           PD.PRODUCT_DIMENSION_CODE AS DIMENSION_CODE,  --量纲编码(9月需求调整)
           PD.PRODUCT_DIMENSION_CN_NAME AS DIMENSION_CN_NAME,  --量纲中文名称(9月需求调整)
           PD.PRODUCT_DIMENSION_EN_NAME AS DIMENSION_EN_NAME,  --量纲英文名称(9月需求调整)
           PD.DIMENSION_SUBCATEGORY_CODE,  --量纲子类编码(9月需求调整)
           PD.DIMENSION_SUBCATEGORY_CN_NAME,  --量纲子类中文名称(9月需求调整)
           PD.DIMENSION_SUBCATEGORY_EN_NAME,  --量纲子类英文名称(9月需求调整)
           PD.DIMENSION_SUB_DETAIL_CODE,  --量纲子类明细编码(9月需求调整)
           PD.DIMENSION_SUB_DETAIL_CN_NAME,  --量纲子类明细中文名称(9月需求调整)
           PD.DIMENSION_SUB_DETAIL_EN_NAME,  --量纲子类明细英文名称(9月需求调整)
		   M.APD_SHIPMENT_OBJECT AS SHIPPING_OBJECT_CODE,	 --发货对象  11月需求调整
		   M.APD_SHIPMENT_OBJECT AS SHIPPING_OBJECT_CN_NAME, --发货对象名称 11月需求调整(暂无CODE，均用名称代替)
		   M.APD_MANUFACTURE_OBJECT AS MANUFACTURE_OBJECT_CODE, --制造对象  11月需求调整
		   M.APD_MANUFACTURE_OBJECT AS MANUFACTURE_OBJECT_CN_NAME,  --制造对象名称  11月需求调整(暂无CODE，均用名称代替)
           M.ITEM_CODE, --子项ITEM编码
           NULL AS ITEM_CN_NAME, --子项ITEM名称
           D.SHIP_QUANTITY, --子项ITEM发货量
           D.RMB_AVG_AMT, --本层制造成本均本成本
		   D.rmb_cost_amt, --制造成本
           CP.PROD_CODE, --产品编码
		   CP.PROD_CN_NAME, --产品名称
           CP.CN_DESCRIPTION, --产品名称
           D.PARENTPARTNUMBER, --父项ITEM编码
           D.PARENT_SHIP_QUANTITY, --父项编码发货数量(缺少父项ITEM名称字段匹配规则)
           D.NON_SALE_FLAG, --非销售标识
           DECODE(RC.OVERSEA_FLAG,''Y'',''O'',''N'',''I'') AS OVERSEA_FLAG,--是否海外标识:Y是(海外O),N否(国内I)(9月需求调整)
           ''-1'' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           ''-1'' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           D.PRIMARY_ID,
           COALESCE(L1.L1_NAME, L1B.L1_NAME, CFG.L1_NAME, CFG2.L1_NAME, ''其他'') AS L1_NAME, --盈利颗粒度L1层级
           L2C.L2_NAME AS L2_NAME, --COA维表盈利颗粒度L2层级
           COUNT(L2C.L2_NAME) OVER(PARTITION BY COALESCE(L1.L1_NAME, L1B.L1_NAME, CFG.L1_NAME, CFG2.L1_NAME)) AS FLAG --标识COA维表L2_NAME为空的数据为0
		   FROM
		   (SELECT P.PERIOD_ID,
               P.ITEM_CODE,
               P.ITEM_CN_NAME,
               P.SHIP_QUANTITY, --子项ITEM发货量
               P.PARENTPARTNUMBER, --父项ITEM编码
               P.PARENT_SHIP_QUANTITY, --父项编码发货数量(缺少父项ITEM名称字段匹配规则)
               P.NON_SALE_FLAG, --非销售标识
			   IM.RMB_AVG_AMT,  --制造成本均本
               NVL((P.SHIP_QUANTITY * IM.RMB_AVG_AMT),0) as rmb_cost_amt, --制造成本
               P.PROD_KEY,
               P.MAIN_DIMENSION_KEY,
               P.MODEL_NUM,
               P.RECOGNISE_TYPE_ID,
               P.IS_RESALE_FLAG,
               P.PRIMARY_ID,
               P.GEO_PC_KEY
          FROM FIN_DM_OPT_FOI.DWL_PROD_BOM_ITEM_SHIP_DIM_I P
         INNER JOIN (SELECT DISTINCT IM.PERIOD_ID,
                                    IM.COST_TYPE_NAME,
                                    IM.MATERIAL_ID,
                                    DM.MATERIAL_CODE,
                                    IM.INVENTORY_ORG_ID,
                                    IO.INVENTORY_ORG_CODE,
                                    (NVL(IM.TL_RESOURCE,0) +
                                    NVL(IM.TL_OUTSIDE_PROCESSING,0) +
                                    NVL(IM.TL_OVERHEAD,0)) AS RMB_AVG_AMT
                      FROM FIN_DM_OPT_FOI.DWL_PROD_INV_MATERIAL_COST_I IM
                      LEFT JOIN DWRDIM.DWR_DIM_INVENTORY_ORG_D IO
                        ON IM.INVENTORY_ORG_ID = IO.INVENTORY_ORG_ID
                      JOIN DWRDIM.DWR_DIM_MATERIAL_D DM
                        ON IM.MATERIAL_ID = DM.MATERIAL_ID
                       WHERE (NVL(IM.TL_RESOURCE,33333) + NVL(IM.TL_OUTSIDE_PROCESSING,33333) + NVL(IM.TL_OVERHEAD,33333)) <> 99999  -- 11月版本调整  物料标准成本卷积历史
                       AND IO.INVENTORY_ORG_CODE = '''||V_INVENTORY_ORG_CODE||''' --获取其INVENTORY_ORG_CODE(库存组织代码)作为筛选字段=NY1  --202405版本修改 原为H80
                       AND IM.COST_TYPE_NAME = ''Group''
                       AND DM.SCD_ACTIVE_IND = 1
					   --AND IM.BASED_ON_ROLLUP_FLAG = 1   --202405版本新增
					   ) IM
            ON P.ITEM_CODE = IM.MATERIAL_CODE
           AND P.PERIOD_ID = IM.PERIOD_ID
		  AND CASE WHEN MONTH(CURRENT_TIMESTAMP) = 1 THEN CAST(SUBSTR(TO_CHAR(P.PERIOD_ID),1,4) AS BIGINT) >= YEAR(CURRENT_TIMESTAMP) -4 
				WHEN MONTH(CURRENT_TIMESTAMP) != 1 THEN CAST(SUBSTR(TO_CHAR(P.PERIOD_ID),1,4) AS BIGINT) >= YEAR(CURRENT_TIMESTAMP) -3 
				END 
		   ) D
		
	 LEFT JOIN '||V_DIM_TABLE||' M -- 1月版本调整  制造量纲维度表
      ON D.ITEM_CODE = M.ITEM_CODE
	  AND M.VERSION_ID = TO_CHAR(CURRENT_TIMESTAMP,''YYYYMM'')
	  '||V_DIM_PARA||'
	  --AND M.APD_SHIPMENT_OBJECT IS NOT NULL
	  --AND M.APD_MANUFACTURE_OBJECT IS NOT NULL
     INNER JOIN DMDIM.DM_DIM_PRODUCT_D CP --重量级团队匹配维表(9月需求调整)
        ON D.PROD_KEY = CP.PROD_KEY
       AND CP.LV0_PROD_RND_TEAM_CODE = '''||V_LV0_PARA||''' 
		'||V_LV1_PARA||'
       -- AND CP.PROD_POV_ID = 4 --(9月需求调整)
       AND D.SHIP_QUANTITY >= 0 -- 数量非负数
	  --AND D.MODEL_NUM IN (''AI'', ''PH'', ''FG'') -- 11月版本调整  模板属性取AI、PH、FG件
       AND D.RECOGNISE_TYPE_ID = 4 --取供应中心发区域（4）
       --AND D.IS_RESALE_FLAG = ''N'' --去除转售重复数据(9月需求调整)
     LEFT JOIN DM_FOC_DIM_PRODUCTDIMENSION_D PD  --量纲维表(9月需求调整)
        ON D.MAIN_DIMENSION_KEY = PD.DIMENSION_KEY
      LEFT JOIN FIN_DM_OPT_FOP.APD_FOP_ICT_FCST_HOLISTIC_VIEW_T L1 --ICT业务预测全景图,7月版本增加盈利颗粒度L1、L2层级
        ON CP.LV1_PROD_RND_TEAM_CODE = L1.LV1_CODE
       AND CP.LV2_PROD_RND_TEAM_CODE = L1.LV2_CODE
       AND CP.LV3_PROD_RND_TEAM_CODE = L1.LV3_CODE
       AND L1.LV3_CODE IS NOT NULL -- 先LV3_CODE有值的进行关联
       AND L1.DEL_FLAG = ''N'' AND UPPER(L1.STATUS) = ''SUBMIT'' 
      LEFT JOIN FIN_DM_OPT_FOP.APD_FOP_ICT_FCST_HOLISTIC_VIEW_T L1B --ICT业务预测全景图,7月版本增加盈利颗粒度L1、L2层级
        ON CP.LV1_PROD_RND_TEAM_CODE = L1B.LV1_CODE
       AND CP.LV2_PROD_RND_TEAM_CODE = L1B.LV2_CODE
       AND L1B.LV2_CODE IS NOT NULL
       AND L1B.LV3_CODE IS NULL -- LV3_CODE无值时关联到LV2_CODE取值即可
       AND L1B.DEL_FLAG = ''N'' AND UPPER(L1B.STATUS) = ''SUBMIT'' 
      LEFT JOIN FIN_DM_OPT_FOI.DM_FOC_PROD_L1_CFG_T CFG -- 其他场景:重量级团队和L1_NAME关系的配置表
        ON CP.LV1_PROD_RD_TEAM_CN_NAME = CFG.LV1_PROD_RD_TEAM_CN_NAME
       AND CP.LV2_PROD_RD_TEAM_CN_NAME = CFG.LV2_PROD_RD_TEAM_CN_NAME
       AND CP.LV3_PROD_RD_TEAM_CN_NAME = CFG.LV3_PROD_RD_TEAM_CN_NAME
       AND CFG.LV3_PROD_RD_TEAM_CN_NAME IS NOT NULL -- 先LV3_PROD_RD_TEAM_CN_NAME有值的进行关联
      LEFT JOIN FIN_DM_OPT_FOI.DM_FOC_PROD_L1_CFG_T CFG2 -- 其他场景:重量级团队和L1_NAME关系的配置表
        ON CP.LV1_PROD_RD_TEAM_CN_NAME = CFG2.LV1_PROD_RD_TEAM_CN_NAME
       AND CP.LV2_PROD_RD_TEAM_CN_NAME = CFG2.LV2_PROD_RD_TEAM_CN_NAME
       AND CFG2.LV2_PROD_RD_TEAM_CN_NAME IS NOT NULL
       AND CFG2.LV3_PROD_RD_TEAM_CN_NAME IS NULL -- LV3_PROD_RD_TEAM_CN_NAME无值时关联到LV2取值即可
      LEFT JOIN FIN_DM_OPT_FOP.APD_FOP_COA_L1_T L2C --COA维表
       ON COALESCE(L1.L1_NAME, L1B.L1_NAME, CFG.L1_NAME, CFG2.L1_NAME) = L2C.L1_NAME
      AND CP.PROD_CODE = L2C.COA_CODE
      AND L2C.DEL_FLAG = ''N'' AND UPPER(L2C.STATUS) = ''SUBMIT'' 
      LEFT JOIN DWRDIM.DWR_DIM_REGION_RC_D RC --(9月需求调整)国内海外区分通过主表关联COA区域维表，取OVERSEA_FLAG字段作为国内/海外标签
       ON D.GEO_PC_KEY = RC.GEO_PC_KEY
      
      ) T
      LEFT JOIN (SELECT DISTINCT ITEM_CODE,L1_NAME,L2_NAME FROM FIN_DM_OPT_FOP.DM_DIM_FOP_SPART_PROFITING_RELATION_T --Spart与L1、L2、L3关系表
                  WHERE DEL_FLAG = ''N''
                    AND UPPER(STATUS) = ''SUBMIT''  -- 状态（SAVE 保存、SUBMIT 提交）
                    AND PERIOD_ID = (SELECT MAX(PERIOD_ID) FROM FIN_DM_OPT_FOP.DM_DIM_FOP_SPART_PROFITING_RELATION_T)  --取最大的年月
                ) L2S
       ON T.L1_NAME = L2S.L1_NAME
      AND T.PARENTPARTNUMBER = L2S.ITEM_CODE'
      ;
	  DBMS_OUTPUT.PUT_LINE (V_SQL);
	  EXECUTE IMMEDIATE V_SQL;
  
  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插数到 '||V_TO_TABLE||'表, 新版本号='||V_VERSION_ID||', 所依赖的品类-专家团的版本号='||V_DIM_VERSION_ID,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  
  --3.收集统计信息
  V_SQL := 'ANALYZE '||V_TO_TABLE;
   EXECUTE IMMEDIATE V_SQL;
  
  
  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集 '||V_TO_TABLE||'统计信息完成!');
   
   END IF;
   
     RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
 
END$$
/

