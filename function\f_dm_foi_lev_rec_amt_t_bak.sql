-- Name: f_dm_foi_lev_rec_amt_t_bak; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_lev_rec_amt_t_bak(f_cate_version bigint, f_item_version bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$ 
/***************************************************************************************************************************************************************
修改时间：2023-08-09
修改人：黄心蕊 hwx1187045
创建时间：2022-10-22
创建人  ：songhui swx1182801
背景描述：到货总金额数据表,然后调用该函数的版本将相对应的数据生成导入到目标表中
参数描述：参数一(f_cate_version)：top品类清单表最新版本号
					参数二(f_item_version)：导入通用版本号（规格品清单版本号）
					参数三(x_success_flag)  ：运行状态返回值-成功或者失败
事例    ：select FIN_DM_OPT_FOI.f_dm_foi_lev_rec_amt_t(-1,-1)	--一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                  VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOI_LEV_REC_AMT_T';
  V_VERSION                  BIGINT; --执行版本号
  V_STEP_NUM                 BIGINT := 0; --函数步骤数及异常定点
  V_PART1_PUBLIC             TEXT := NULL; -- 函数逻辑公共部分
  V_EXECUTE_SQL              TEXT := NULL; -- 执行SQL
  V_CATEGORY                 TEXT := NULL; --品类部分字段
  V_LV4                      TEXT := NULL; --模块部分字段
  V_LV3                      TEXT := NULL; --专家团部分字段
  V_LV2                      TEXT := NULL; --ICT部分字段
  V_L3_CEG_SHORT_CN_NAME     TEXT := NULL; --专家团短名字段
  V_L4_CEG_SHORT_CN_NAME     TEXT := NULL; --模块短名字段
  V_CONTINUITY_TYPE          TEXT := NULL; --含连续性影响字段
  V_SQL_GROUP_LEVEL          TEXT := NULL; --卷积层级
  V_SQL_GEOUP_CODE           TEXT := NULL; --查询层级CODE
  V_SQL_GROUP_CN_NAME        TEXT := NULL; --查询层级NAME
  V_SQL_PARENT_CODE          TEXT := NULL; --上层级CODE
  V_SQL_ITEM                 TEXT := NULL; --查询ITEM部分字段
  V_SQL_CATEGORY             TEXT := NULL; --查询品类部分字段
  V_SQL_LV4                  TEXT := NULL; --查询模块部分字段
  V_SQL_LV3                  TEXT := NULL; --查询专家团部分字段
  V_SQL_LV2                  TEXT := NULL; --查询ICT部分字段
  V_SQL_L3_CEG_SHORT_CN_NAME TEXT := NULL; --查询专家团短名字段
  V_SQL_L4_CEG_SHORT_CN_NAME TEXT := NULL; --查询模块短名字段
  V_SQL_CONTINUITY_TYPE      TEXT := NULL; --查询含连续性影响字段
  V_GROUP_CATEGORY           TEXT := NULL; --品类卷积字段
  V_GROUP_CONTINUITY_TYPE    TEXT := NULL; --含连续性影响字段卷积字段
  V_JOIN_CONTINUITY_TYPE     TEXT := NULL; --含连续性影响字段关联字段
  V_FILTER1                  TEXT := NULL; --筛选条件
  V_CHILD_LEVEL              TEXT := NULL; --子层级值
  V_SQL1                     TEXT := NULL; --公共部分逻辑1
  V_SQL2                     TEXT := NULL; --公共部分逻辑2
  V_SQL3                     TEXT := NULL; --逻辑3
  
BEGIN
  X_SUCCESS_FLAG := '1';

  -- 将查询到的数据放到变量中的公共sql
  V_PART1_PUBLIC := '
                    SELECT VALUE 
                        FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T
                        WHERE ENABLE_FLAG = ''Y''
                        AND UPPER(PARA_NAME) = ''$PARA_NAME$'';
                      ';

  -- 无论是季调还是月调，都通过sql从变量参数表（dm_foi_plan_var_para_t）表中取出对应的规格品版本号
  IF f_cate_version IN (-1, 0) AND f_item_version IN (-1, 0) THEN
    V_EXECUTE_SQL := REPLACE(V_PART1_PUBLIC,
                             '$PARA_NAME$',
                             'VERSION_ID-ITEM'); -- 规格品版本号
    EXECUTE V_EXECUTE_SQL
      INTO V_VERSION;
  
    -- 业务在前台配置页面调整规格品数据时，Java传参，优先取规格品版本号，若无，则取TOP品类版本号作为生成本次数据的版本号   
  ELSE
    V_VERSION := NVL(F_ITEM_VERSION, F_CATE_VERSION);
  END IF;
  
  --0.日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  DROP TABLE IF EXISTS DM_LEV_AMT_TEMP;
  CREATE TEMPORARY TABLE DM_LEV_AMT_TEMP(
   YEAR	INT,
   PERIOD_ID	INT,
   GROUP_CODE	VARCHAR(50),
   GROUP_CN_NAME	VARCHAR(2000),
   GROUP_LEVEL	VARCHAR(50),
   RECEIVE_QTY	BIGINT,
   RECEIVE_AMT_USD	NUMERIC,
   RECEIVE_AMT_CNY	NUMERIC,
   AVG_PRICE_CNY	NUMERIC,
   ITEM_CODE	VARCHAR(50),
   ITEM_NAME	VARCHAR(2000),
   CATEGORY_CODE	VARCHAR(50),
   CATEGORY_NAME	VARCHAR(200),
   L4_CEG_CODE	VARCHAR(200),
   L4_CEG_SHORT_CN_NAME	VARCHAR(200),
   L3_CEG_CODE	VARCHAR(200),
   L3_CEG_SHORT_CN_NAME	VARCHAR(200),
   L2_CEG_CODE	VARCHAR(200),
   L2_CEG_CN_NAME	VARCHAR(200),
   TOP_FLAG	VARCHAR(3),
   L4_CEG_CN_NAME	VARCHAR(255),
   L3_CEG_CN_NAME	VARCHAR(255),
   PARENT_CODE	VARCHAR(50),
   APPEND_FLAG	VARCHAR(2),
   CONTINUITY_TYPE	VARCHAR(50)
  )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY REPLICATION;
   
  --供应商层级层级插数
  V_STEP_NUM := V_STEP_NUM + 1;
  INSERT INTO DM_LEV_AMT_TEMP
    (YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     RECEIVE_QTY,
     RECEIVE_AMT_USD,
     RECEIVE_AMT_CNY,
     AVG_PRICE_CNY,
     ITEM_CODE,
     ITEM_NAME,
     CATEGORY_CODE,
     CATEGORY_NAME,
     L2_CEG_CN_NAME,
     L3_CEG_SHORT_CN_NAME,
     L4_CEG_SHORT_CN_NAME,
     L2_CEG_CODE,
     L3_CEG_CODE,
     L4_CEG_CODE,
     L4_CEG_CN_NAME,
     L3_CEG_CN_NAME,
     TOP_FLAG,
     PARENT_CODE,
	 APPEND_FLAG)
    SELECT T1.YEAR,
           T1.PERIOD_ID,
           T1.SUPPLIER_CODE AS GROUP_CODE,
           T1.SUPPLIER_CN_NAME AS GROUP_CN_NAME,
           'SUPPLIER' AS GROUP_LEVEL,
           T1.RECEIVE_QTY,
           T1.RECEIVE_AMT_USD,
           T1.RECEIVE_AMT_CNY,
           CASE
             WHEN NVL(T1.RECEIVE_QTY, 0) = 0 THEN 0
             ELSE T1.RECEIVE_AMT_CNY / T1.RECEIVE_QTY
           END AS AVG_PRICE_CNY, --到货均价(CNY)
           T1.ITEM_CODE,
           T1.ITEM_NAME,
           T1.CATEGORY_CODE,
           T1.CATEGORY_NAME,
           T1.L2_CEG_CN_NAME,
           T1.L3_CEG_SHORT_CN_NAME,
           T1.L4_CEG_SHORT_CN_NAME,
           T1.L2_CEG_CODE,
           T1.L3_CEG_CODE,
           T1.L4_CEG_CODE,
           T1.L4_CEG_CN_NAME,
           T1.L3_CEG_CN_NAME,
           CASE
             WHEN T2.VERSION_ID = V_VERSION THEN 'Y'
             ELSE 'N'
           END AS TOP_FLAG,
           T1.ITEM_CODE AS PARENT_CODE,
		   'N' AS APPEND_FLAG
      FROM FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_REPORT_T T1
      LEFT JOIN (SELECT ITEM_CODE, ITEM_NAME, VERSION_ID
                   FROM FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_INFO_T
                  WHERE VERSION_ID = V_VERSION) T2 --202309版本加入非规格品供应商均价及热力图计算
        ON (T1.ITEM_CODE = T2.ITEM_CODE AND T1.ITEM_NAME = T2.ITEM_NAME)
     WHERE UPPER(T1.APPEND_FLAG) = 'N';

	  				
 --写入日志
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '供应商层级收敛完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');
   
V_STEP_NUM:=V_STEP_NUM+1;
  /*ITEM补齐数与预测数插数*/
WITH  TOP_ITEM AS
   (--规格品部分
	SELECT ITEM_CODE, ITEM_NAME,CATEGORY_CODE, VERSION_ID
      FROM FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_INFO_T
     WHERE VERSION_ID = V_VERSION),
  
 ITEM_AMT AS
   (--实际数补齐部分数
    SELECT YEAR,
           PERIOD_ID,
           ITEM_CODE,
           ITEM_NAME,
           CATEGORY_CODE,
           '' AS RECEIVE_QTY,
           '' AS RECEIVE_AMT_USD,
           '' AS RECEIVE_AMT_CNY,
           AVG_PRICE_CNY,
           APPEND_FLAG
      FROM FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_APPEND_T
     WHERE APPEND_FLAG = 'Y'
    UNION ALL
    --预测数及预测数补齐部分数
	SELECT YEAR,
           PERIOD_ID,
           ITEM_CODE,
           ITEM_NAME,
		   CATEGORY_CODE,
           RECEIVE_QTY,
           RECEIVE_AMT_USD,
           RECEIVE_AMT_CNY,
           CASE
             WHEN AVG_PRICE_CNY IS NULL AND RECEIVE_QTY <> 0 THEN
              RECEIVE_AMT_CNY / RECEIVE_QTY
             ELSE
              AVG_PRICE_CNY
           END AS AVG_PRICE_CNY,
           APPEND_FLAG
      FROM FIN_DM_OPT_FOI.DM_FOI_ITEM_FCST_SUM_T)
  
  INSERT INTO DM_LEV_AMT_TEMP(
  YEAR,
  PERIOD_ID,
  GROUP_CODE,
  GROUP_CN_NAME,
  GROUP_LEVEL,
  RECEIVE_QTY,
  RECEIVE_AMT_USD,
  RECEIVE_AMT_CNY,
  AVG_PRICE_CNY,
  CATEGORY_CODE,
  CATEGORY_NAME,
  L2_CEG_CN_NAME,
  L3_CEG_SHORT_CN_NAME,
  L4_CEG_SHORT_CN_NAME,
  L2_CEG_CODE,
  L3_CEG_CODE,
  L4_CEG_CODE,
  L4_CEG_CN_NAME,
  L3_CEG_CN_NAME,
  TOP_FLAG,
  PARENT_CODE,
  APPEND_FLAG)
  SELECT T1.YEAR,
         T1.PERIOD_ID,
         T1.ITEM_CODE AS GROUP_CODE,
         T1.ITEM_NAME AS GROUP_CN_NAME,
         'ITEM' AS GROUP_LEVEL,
         T1.RECEIVE_QTY,
         T1.RECEIVE_AMT_USD,
         T1.RECEIVE_AMT_CNY,
         T1.AVG_PRICE_CNY,
         T2.CATEGORY_CODE,
         T2.CATEGORY_NAME,
         T2.L2_CEG_CN_NAME,
         T2.L3_CEG_SHORT_CN_NAME,
         T2.L4_CEG_SHORT_CN_NAME,
         T2.L2_CEG_CODE,
         T2.L3_CEG_CODE,
         T2.L4_CEG_CODE,
         T2.L4_CEG_CN_NAME,
         T2.L3_CEG_CN_NAME,
         CASE
           WHEN T3.VERSION_ID = V_VERSION THEN 'Y'
           ELSE 'N'
         END AS TOP_FLAG,
         T2.CATEGORY_CODE AS PARENT_CODE,
         T1.APPEND_FLAG
    FROM ITEM_AMT T1
	LEFT JOIN FIN_DM_OPT_FOI.DM_DIM_FOI_ITEM_CATG_MODL_CEG_T T2 
	  ON T1.CATEGORY_CODE=T2.CATEGORY_CODE AND T1.ITEM_CODE=T2.ITEM_CODE
    LEFT JOIN TOP_ITEM T3
      ON (T1.ITEM_CODE = T3.ITEM_CODE AND T1.CATEGORY_CODE = T3.CATEGORY_CODE);

 --写入日志
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => 'ITEM层级补齐数及预测数插数完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');
  
 --层级收敛
  V_STEP_NUM:= V_STEP_NUM + 1;
  
  /*字段值定义*/
  V_CATEGORY                 := 'CATEGORY_CODE , CATEGORY_NAME ,';
  V_LV4                      := 'L4_CEG_CODE , L4_CEG_CN_NAME , ';
  V_LV3                      := 'L3_CEG_CODE , L3_CEG_CN_NAME , ';
  V_LV2                      := 'L2_CEG_CODE , L2_CEG_CN_NAME , ';
  V_L3_CEG_SHORT_CN_NAME     := 'L3_CEG_SHORT_CN_NAME , ';
  V_L4_CEG_SHORT_CN_NAME     := 'L4_CEG_SHORT_CN_NAME , ';
  V_CONTINUITY_TYPE          := '';
  
  V_SQL_GROUP_LEVEL          := '''ITEM''';
  V_SQL_GEOUP_CODE           := 'T1.ITEM_CODE AS GROUP_CODE,';
  V_SQL_GROUP_CN_NAME        := 'T1.ITEM_NAME AS GROUP_CN_NAME,';
  V_SQL_PARENT_CODE          := 'T1.CATEGORY_CODE AS PARENT_CODE ,';
  V_SQL_ITEM                 := 'T1.ITEM_CODE , T1.ITEM_NAME ,';
  V_SQL_CATEGORY             := 'T1.CATEGORY_CODE , T1.CATEGORY_NAME ,';
  V_SQL_LV4                  := 'T1.L4_CEG_CODE , T1.L4_CEG_CN_NAME , ';
  V_SQL_LV3                  := 'T1.L3_CEG_CODE , T1.L3_CEG_CN_NAME , ';
  V_SQL_LV2                  := 'T1.L2_CEG_CODE , T1.L2_CEG_CN_NAME , ';
  V_SQL_L3_CEG_SHORT_CN_NAME := 'T1.L3_CEG_SHORT_CN_NAME , ';
  V_SQL_L4_CEG_SHORT_CN_NAME := 'T1.L4_CEG_SHORT_CN_NAME , ';
  V_SQL_CONTINUITY_TYPE      := '';

  /*卷积字段定义*/
  V_GROUP_CATEGORY := 'T1.CATEGORY_CODE , T1.CATEGORY_NAME ,';
  V_GROUP_CONTINUITY_TYPE:='';

  /*条件定义*/
  V_JOIN_CONTINUITY_TYPE := '';
  V_CHILD_LEVEL          := '''SUPPLIER''';

  V_SQL1 := '
  INSERT INTO DM_LEV_AMT_TEMP
  (YEAR,
   PERIOD_ID,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   RECEIVE_QTY,
   RECEIVE_AMT_USD,
   RECEIVE_AMT_CNY,
   AVG_PRICE_CNY,
   '||V_CATEGORY
   ||V_LV2
   ||V_LV3
   ||V_LV4
   ||V_L3_CEG_SHORT_CN_NAME
   ||V_L4_CEG_SHORT_CN_NAME
   ||V_CONTINUITY_TYPE||'
   PARENT_CODE,
   APPEND_FLAG,
   TOP_FLAG
   ) ' ;
   
  V_SQL2 := '
    SELECT T1.YEAR,
         T1.PERIOD_ID,
         '||V_SQL_GEOUP_CODE 
		 ||V_SQL_GROUP_CN_NAME 
		 ||V_SQL_GROUP_LEVEL||' AS GROUP_LEVEL,
         SUM(T1.RECEIVE_QTY) AS RECEIVE_QTY,
         SUM(T1.RECEIVE_AMT_USD) AS RECEIVE_AMT_USD,
         SUM(T1.RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY,
         CASE
           WHEN NVL(T1.SUM(RECEIVE_QTY), 0) = 0 THEN 0
           ELSE SUM(T1.RECEIVE_AMT_CNY) / SUM(T1.RECEIVE_QTY)
         END AS AVG_PRICE_CNY, 
         '||V_SQL_CATEGORY
		 ||V_SQL_LV2
		 ||V_SQL_LV3
		 ||V_SQL_LV4
		 ||V_SQL_L3_CEG_SHORT_CN_NAME
		 ||V_SQL_L4_CEG_SHORT_CN_NAME
		 ||V_SQL_CONTINUITY_TYPE
		 ||V_SQL_PARENT_CODE||'
		 ''N'' AS APPEND_FLAG,
		 T1.TOP_FLAG
    FROM DM_LEV_AMT_TEMP T1 '||V_JOIN_CONTINUITY_TYPE||'
   WHERE T1.GROUP_LEVEL = '||V_CHILD_LEVEL||'
	 AND T1.APPEND_FLAG = ''N'' 
   GROUP BY '||V_GROUP_CATEGORY
			||V_SQL_LV2
			||V_SQL_LV3
			||V_SQL_LV4
			||V_SQL_ITEM
			||V_SQL_L3_CEG_SHORT_CN_NAME
			||V_SQL_L4_CEG_SHORT_CN_NAME
			||V_GROUP_CONTINUITY_TYPE||'
			T1.YEAR,
			T1.PERIOD_ID,
			T1.TOP_FLAG';
			
  V_SQL3:='';
  
   --写入日志
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '变量定义完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');
  
  
  /*非补齐实际数卷积*/
  FOR LEVEL_FLAG IN 1 .. 5 LOOP
  
  /*ITEM卷积*/
  IF LEVEL_FLAG = 1 THEN
  NULL;
  
  /*品类卷积*/
  ELSIF LEVEL_FLAG = 2 THEN
  /*字段值定义*/
  V_CATEGORY                 := '';
  V_SQL_GROUP_LEVEL          := '''CATEGORY''';
  V_SQL_GEOUP_CODE           := 'T1.CATEGORY_CODE AS GROUP_CODE,';
  V_SQL_GROUP_CN_NAME        := 'T1.CATEGORY_NAME AS GROUP_CN_NAME,';
  V_SQL_PARENT_CODE          := 'T1.L4_CEG_CODE AS PARENT_CODE ,';
  V_SQL_ITEM                 := '';
  V_SQL_CATEGORY             := '';

  /*条件定义*/
  V_FILTER1              := '';
  V_CHILD_LEVEL          := '''ITEM''';
  
  /*LV4卷积*/
  ELSIF LEVEL_FLAG = 3 THEN
  /*字段值定义*/
  V_L4_CEG_SHORT_CN_NAME     := '';
  V_SQL_GROUP_LEVEL          := '''LV4''';
  V_SQL_GEOUP_CODE           := 'T1.L4_CEG_CODE AS GROUP_CODE,';
  V_SQL_GROUP_CN_NAME        := 'T1.L4_CEG_CN_NAME AS GROUP_CN_NAME,';
  V_SQL_PARENT_CODE          := 'T1.L3_CEG_CODE AS PARENT_CODE ,';
  V_SQL_L4_CEG_SHORT_CN_NAME := '';

  /*卷积字段定义*/
  V_GROUP_CATEGORY := '';

  /*条件定义*/
  V_CHILD_LEVEL          := '''CATEGORY''';
  
  /*LV3卷积*/
  ELSIF LEVEL_FLAG = 4 THEN
  /*字段值定义*/
  V_LV4                      := '';
  V_L3_CEG_SHORT_CN_NAME     := '';
  V_CONTINUITY_TYPE          := 'CONTINUITY_TYPE,';
  
  V_SQL_GROUP_LEVEL          := '''LV3''';
  V_SQL_GEOUP_CODE           := 'T1.L3_CEG_CODE AS GROUP_CODE,';
  V_SQL_GROUP_CN_NAME        := 'T1.L3_CEG_CN_NAME AS GROUP_CN_NAME,';
  V_SQL_PARENT_CODE          := 'T1.L2_CEG_CODE AS PARENT_CODE ,';
  V_SQL_LV4                  := '';
  V_SQL_L3_CEG_SHORT_CN_NAME := '';
  V_SQL_CONTINUITY_TYPE      := 'T2.CONTINUITY_TYPE , ';
  V_GROUP_CONTINUITY_TYPE	 := 'T2.CONTINUITY_TYPE , ';

  /*条件定义*/
  V_CHILD_LEVEL          := '''LV4''';
  V_JOIN_CONTINUITY_TYPE := '
						  LEFT JOIN (SELECT VALUE, PARA_NAME AS CONTINUITY_TYPE
									   FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T
									  WHERE ENABLE_FLAG = ''Y''
										AND PARA_NAME = ''不含连续性影响'') T2
							ON T1.L3_CEG_CN_NAME = T2.VALUE ';
							
  /*LV2卷积*/
  ELSIF LEVEL_FLAG = 5 THEN
  /*字段值定义*/
  V_LV3 := '';

  V_SQL_GROUP_LEVEL       := '''LV2''';
  V_SQL_GEOUP_CODE        := 'T1.L2_CEG_CODE AS GROUP_CODE,';
  V_SQL_GROUP_CN_NAME     := 'T1.L2_CEG_CN_NAME AS GROUP_CN_NAME,';
  V_SQL_PARENT_CODE       := ''''' AS PARENT_CODE ,';
  V_SQL_LV3               := '';
  V_SQL_CONTINUITY_TYPE   := 'T1.CONTINUITY_TYPE , ';
  V_GROUP_CONTINUITY_TYPE := 'T1.CONTINUITY_TYPE , ';

  /*条件定义*/
  V_CHILD_LEVEL := '''LV3''';
  V_JOIN_CONTINUITY_TYPE:='';
  V_FILTER1     := ' AND T1.CONTINUITY_TYPE IS NOT NULL';
  
  V_SQL3:='
  UNION ALL
      SELECT T1.YEAR,
         T1.PERIOD_ID,
         '||V_SQL_GEOUP_CODE
		 ||V_SQL_GROUP_CN_NAME
		 ||V_SQL_GROUP_LEVEL||' AS GROUP_LEVEL,
         SUM(T1.RECEIVE_QTY) AS RECEIVE_QTY,
         SUM(T1.RECEIVE_AMT_USD) AS RECEIVE_AMT_USD,
         SUM(T1.RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY,
         CASE
           WHEN NVL(SUM(T1.RECEIVE_QTY), 0) = 0 THEN 0
           ELSE SUM(T1.RECEIVE_AMT_CNY) / SUM(T1.RECEIVE_QTY)
         END AS AVG_PRICE_CNY, 
         '||V_SQL_LV2
		 ||V_SQL_CONTINUITY_TYPE
		 ||V_SQL_PARENT_CODE||'
		 ''N'' AS APPEND_FLAG,
		 T1.TOP_FLAG
    FROM DM_LEV_AMT_TEMP T1 '||V_JOIN_CONTINUITY_TYPE||'
   WHERE T1.GROUP_LEVEL = '||V_CHILD_LEVEL|| V_FILTER1 ||' 
   GROUP BY '||V_SQL_LV2
			||V_GROUP_CONTINUITY_TYPE||'
			T1.YEAR,
			T1.PERIOD_ID,
			T1.TOP_FLAG';
			
  V_SQL_CONTINUITY_TYPE   := '''含连续性影响'' AS CONTINUITY_TYPE ,';
  V_GROUP_CONTINUITY_TYPE := '';
  V_FILTER1               := '';
  
  
  END IF;
  
    V_SQL1 := '
  INSERT INTO DM_LEV_AMT_TEMP
  (YEAR,
   PERIOD_ID,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   RECEIVE_QTY,
   RECEIVE_AMT_USD,
   RECEIVE_AMT_CNY,
   AVG_PRICE_CNY,
   '||V_CATEGORY
   ||V_LV2
   ||V_LV3
   ||V_LV4
   ||V_L3_CEG_SHORT_CN_NAME
   ||V_L4_CEG_SHORT_CN_NAME
   ||V_CONTINUITY_TYPE||'
   PARENT_CODE,
   APPEND_FLAG,
   TOP_FLAG
   ) ' ;
   
  V_SQL2 := '
    SELECT T1.YEAR,
         T1.PERIOD_ID,
         '||V_SQL_GEOUP_CODE 
		 ||V_SQL_GROUP_CN_NAME 
		 ||V_SQL_GROUP_LEVEL||' AS GROUP_LEVEL,
         SUM(T1.RECEIVE_QTY) AS RECEIVE_QTY,
         SUM(T1.RECEIVE_AMT_USD) AS RECEIVE_AMT_USD,
         SUM(T1.RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY,
         CASE
           WHEN NVL(SUM(T1.RECEIVE_QTY), 0) = 0 THEN 0
           ELSE SUM(T1.RECEIVE_AMT_CNY) / SUM(T1.RECEIVE_QTY)
         END AS AVG_PRICE_CNY, 
         '||V_SQL_CATEGORY
		 ||V_SQL_LV2
		 ||V_SQL_LV3
		 ||V_SQL_LV4
		 ||V_SQL_L3_CEG_SHORT_CN_NAME
		 ||V_SQL_L4_CEG_SHORT_CN_NAME
		 ||V_SQL_CONTINUITY_TYPE
		 ||V_SQL_PARENT_CODE||'
		 ''N'' AS APPEND_FLAG,
		 T1.TOP_FLAG
    FROM DM_LEV_AMT_TEMP T1 '||V_JOIN_CONTINUITY_TYPE||'
   WHERE T1.GROUP_LEVEL = '||V_CHILD_LEVEL|| V_FILTER1 ||' 
   GROUP BY '||V_GROUP_CATEGORY
			||V_SQL_LV2
			||V_SQL_LV3
			||V_SQL_LV4
			||V_SQL_ITEM
			||V_SQL_L3_CEG_SHORT_CN_NAME
			||V_SQL_L4_CEG_SHORT_CN_NAME
			||V_GROUP_CONTINUITY_TYPE||'
			T1.YEAR,
			T1.PERIOD_ID,
			T1.TOP_FLAG';

  V_EXECUTE_SQL := V_SQL1|| V_SQL2||V_SQL3;
  EXECUTE V_EXECUTE_SQL;

V_STEP_NUM:=V_STEP_NUM+1;
 --写入日志
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '第'||LEVEL_FLAG||'次循环完成，'||V_SQL_GROUP_LEVEL||'层级金额收敛完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');
   
END LOOP;

V_STEP_NUM :=V_STEP_NUM+1;
DELETE FROM FIN_DM_OPT_FOI.DM_FOI_LEV_REC_AMT_T WHERE VERSION_ID = V_VERSION;

 --写入日志
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '金额表同版本'||V_VERSION||'数据删除完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');

  V_STEP_NUM:=V_STEP_NUM+1;
  INSERT INTO FIN_DM_OPT_FOI.DM_FOI_LEV_REC_AMT_T
    (ID,
     YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     RECEIVE_QTY,
     RECEIVE_AMT_USD,
     RECEIVE_AMT_CNY,
     AVG_PRICE_CNY,
     ITEM_CODE,
     ITEM_NAME,
     CATEGORY_CODE,
     CATEGORY_NAME,
     L4_CEG_CODE,
     L4_CEG_SHORT_CN_NAME,
     L3_CEG_CODE,
     L3_CEG_SHORT_CN_NAME,
     L2_CEG_CODE,
     L2_CEG_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     TOP_FLAG,
     VERSION_ID,
     L4_CEG_CN_NAME,
     L3_CEG_CN_NAME,
     PARENT_CODE,
     APPEND_FLAG,
     CONTINUITY_TYPE)
    SELECT FIN_DM_OPT_FOI.DM_FOI_LEV_REC_AMT_S.NEXTVAL AS ID,
           YEAR,
           PERIOD_ID,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           RECEIVE_QTY,
           RECEIVE_AMT_USD,
           RECEIVE_AMT_CNY,
           AVG_PRICE_CNY,
           ITEM_CODE,
           ITEM_NAME,
           CATEGORY_CODE,
           CATEGORY_NAME,
           L4_CEG_CODE,
           L4_CEG_SHORT_CN_NAME,
           L3_CEG_CODE,
           L3_CEG_SHORT_CN_NAME,
           L2_CEG_CODE,
           L2_CEG_CN_NAME,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG,
           TOP_FLAG,
           V_VERSION AS VERSION_ID,
           L4_CEG_CN_NAME,
           L3_CEG_CN_NAME,
           PARENT_CODE,
           APPEND_FLAG,
           CONTINUITY_TYPE
      FROM DM_LEV_AMT_TEMP;
	  
 --写入日志
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '金额表同版本'||V_VERSION||'数据删除完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');
   
  --收集信息
  V_STEP_NUM:=V_STEP_NUM+1;
  ANALYSE FIN_DM_OPT_FOI.DM_FOI_LEV_REC_AMT_T;

  --日志结束
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集FIN_DM_OPT_FOI.DM_FOI_LEV_REC_AMT_T统计信息完成!');
 
RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := 0;
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
	 
END;
$$
/

