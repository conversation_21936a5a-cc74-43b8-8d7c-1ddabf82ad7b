-- Name: f_dm_fom_month_weight_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_month_weight_t(f_caliber_flag character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
最后更新时间: 2024年4月12日16点44分
更新人 : 黄心蕊
更新内容 : 202405版本修改 指数权重时间范围前滚12个月 保留原逻辑权重作为权重饼图权重使用
创建时间：2023-12-08
创建人  ：黄心蕊 HWX1187045
背景描述：月度分析-权重表数据初始化
参数描述：参数一(F_CALIBER_FLAG)：'E'为EMS，'M'为自制
		  参数二(F_VERSION_ID)：运行版本号
		  参数三(X_SUCCESS_FLAG)：运行状态返回值 ‘1’为成功，‘0’为失败
来源表：FIN_DM_OPT_FOI.DM_FOM_MONTH_MID_WEIGHT_T 月度分析权重中间表
		FIN_DM_OPT_FOI.DM_FOM_TOP_MANUFACTURE_DTL_T 月度分析制造对象金额表
目标表：FIN_DM_OPT_FOI.DM_FOM_MONTH_WEIGHT_T 月度分析权重表
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOM_MONTH_WEIGHT_T('E',''); --EMS一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOM_MONTH_WEIGHT_T('M',''); --自制一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME      VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOM_MONTH_WEIGHT_T';
  V_VERSION      BIGINT; --版本号
  V_STEP_NUM     INT := 0; --函数步骤号
  V_CALIBER_FLAG VARCHAR(2) := F_CALIBER_FLAG;
  V_PERIOD_YEAR  VARCHAR(100) := YEAR(CURRENT_DATE) - 1 || '-' ||
                                 YEAR(CURRENT_DATE);

BEGIN 

X_SUCCESS_FLAG := '1';

--日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  V_STEP_NUM := V_STEP_NUM + 1;
  --版本号入参判断，当入参为空，取规格品清单最新版本号
  IF F_ITEM_VERSION IS NULL THEN
	SELECT VERSION_ID
	  INTO V_VERSION
	  FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T
	 WHERE UPPER(VERSION_TYPE) IN ('AUTO', 'FINAL')
	   AND UPPER(DATA_TYPE) = 'MONTH'
	   AND STATUS = 1
	 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
    --入参不为空，则以入参为版本号
  ELSE
    V_VERSION := F_ITEM_VERSION;
  END IF;
  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
  
DELETE FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_WEIGHT_T WHERE VERSION_ID = V_VERSION AND CALIBER_FLAG = V_CALIBER_FLAG;

  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '删除'''||V_CALIBER_FLAG||''' 口径数据成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

  V_STEP_NUM := V_STEP_NUM + 1;
--1.建临时表，存入分层编码金额
DROP TABLE IF EXISTS DM_LEV_TOP_AMT_TEMP;
CREATE TEMPORARY TABLE DM_LEV_TOP_AMT_TEMP(
	PERIOD_ID INT, --202405版本修改 方案修改 加入会计期
	LV0_CODE CHARACTER VARYING(50),
	LV0_CN_NAME CHARACTER VARYING(200),
	LV1_CODE CHARACTER VARYING(50),
	LV1_CN_NAME CHARACTER VARYING(200),
	BUSSINESS_OBJECT_CODE CHARACTER VARYING(200),
	BUSSINESS_OBJECT_CN_NAME CHARACTER VARYING(200),
	SHIPPING_OBJECT_CODE CHARACTER VARYING(200),
	SHIPPING_OBJECT_CN_NAME CHARACTER VARYING(200),
	MANUFACTURE_OBJECT_CODE  CHARACTER VARYING(200),
	MANUFACTURE_OBJECT_CN_NAME CHARACTER VARYING(200),
	GROUP_CODE 	  	CHARACTER VARYING(200),
	GROUP_CN_NAME CHARACTER VARYING(200),
	GROUP_LEVEL   CHARACTER VARYING(50),
	PARENT_CODE 		CHARACTER VARYING(200),
	PARENT_CN_NAME  CHARACTER VARYING(200),
	CALIBER_FLAG CHARACTER VARYING(2),
	RMB_COST_AMT  NUMERIC
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY ROUNDROBIN;
  
--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '金额临时表创建成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
  V_STEP_NUM := V_STEP_NUM + 1;
  --2.分层金额卷积  
  INSERT INTO DM_LEV_TOP_AMT_TEMP
    (LV0_CODE,
     LV0_CN_NAME,
     LV1_CODE,
     LV1_CN_NAME,
     BUSSINESS_OBJECT_CODE,
     BUSSINESS_OBJECT_CN_NAME,
     SHIPPING_OBJECT_CODE,
     SHIPPING_OBJECT_CN_NAME,
	 MANUFACTURE_OBJECT_CODE,
	 MANUFACTURE_OBJECT_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     RMB_COST_AMT,
     CALIBER_FLAG,
     PARENT_CODE,
     PARENT_CN_NAME,
	 PERIOD_ID) --202405版本修改 方案修改 加入会计期
  --制造对象层级金额插数
    SELECT LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           BUSSINESS_OBJECT_CODE,
           BUSSINESS_OBJECT_CN_NAME,
           SHIPPING_OBJECT_CODE,
           SHIPPING_OBJECT_CN_NAME,
		   MANUFACTURE_OBJECT_CODE,
		   MANUFACTURE_OBJECT_CN_NAME,
           MANUFACTURE_OBJECT_CODE AS GROUP_CODE,
           MANUFACTURE_OBJECT_CN_NAME AS GROUP_CN_NAME,
           'MANUFACTURE_OBJECT' AS GROUP_LEVEL,
           SUM(RMB_COST_AMT),
           V_CALIBER_FLAG AS CALIBER_FLAG,
           SHIPPING_OBJECT_CODE AS PARENT_CODE,
           SHIPPING_OBJECT_CN_NAME AS PARENT_CN_NAME,
		   PERIOD_ID --202405版本修改 方案修改 加入会计期
      FROM FIN_DM_OPT_FOI.DM_FOM_TOP_MANUFACTURE_DTL_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG
	   AND GROUP_LEVEL = 'MANUFACTURE_OBJECT' --海思与云核心网的经营对象层级金额不参与计算
	   AND PERIOD_YEAR IN (YEAR(CURRENT_DATE), YEAR(CURRENT_DATE) - 1)
	 GROUP BY LV0_CODE,
	          LV0_CN_NAME,
	          LV1_CODE,
	          LV1_CN_NAME,
	          BUSSINESS_OBJECT_CODE,
	          BUSSINESS_OBJECT_CN_NAME,
	          SHIPPING_OBJECT_CODE,
	          SHIPPING_OBJECT_CN_NAME,
	          MANUFACTURE_OBJECT_CODE,
	          MANUFACTURE_OBJECT_CN_NAME,
			  PERIOD_ID --202405版本修改 方案修改 加入会计期
			  
UNION ALL
		   
  --发货对象层级金额卷积
    SELECT LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           BUSSINESS_OBJECT_CODE,
           BUSSINESS_OBJECT_CN_NAME,
           SHIPPING_OBJECT_CODE,
           SHIPPING_OBJECT_CN_NAME,
		   '',
		   '',
           SHIPPING_OBJECT_CODE AS GROUP_CODE,
           SHIPPING_OBJECT_CN_NAME AS GROUP_CN_NAME,
           'SHIPPING_OBJECT' AS GROUP_LEVEL,
           SUM(RMB_COST_AMT),
           V_CALIBER_FLAG AS CALIBER_FLAG,
           BUSSINESS_OBJECT_CODE AS PARENT_CODE,
           BUSSINESS_OBJECT_CN_NAME AS PARENT_CN_NAME,
		   PERIOD_ID --202405版本修改 方案修改 加入会计期
      FROM FIN_DM_OPT_FOI.DM_FOM_TOP_MANUFACTURE_DTL_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG
	   AND GROUP_LEVEL = 'MANUFACTURE_OBJECT'  --海思与云核心网的经营对象层级金额不参与计算
	   AND PERIOD_YEAR IN (YEAR(CURRENT_DATE), YEAR(CURRENT_DATE) - 1)
     GROUP BY LV0_CODE,
              LV0_CN_NAME,
              LV1_CODE,
              LV1_CN_NAME,
              BUSSINESS_OBJECT_CODE,
              BUSSINESS_OBJECT_CN_NAME,
              SHIPPING_OBJECT_CODE,
              SHIPPING_OBJECT_CN_NAME,
              CALIBER_FLAG,
			  PERIOD_ID --202405版本修改 方案修改 加入会计期
    
    UNION ALL
    --经营对象层级金额卷积
    SELECT LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           BUSSINESS_OBJECT_CODE,
           BUSSINESS_OBJECT_CN_NAME,
           '',
           '',
		   '',
		   '',
           BUSSINESS_OBJECT_CODE AS GROUP_CODE,
           BUSSINESS_OBJECT_CN_NAME AS GROUP_CN_NAME,
           'BUSSINESS_OBJECT' AS GROUP_LEVEL,
           SUM(RMB_COST_AMT),
           V_CALIBER_FLAG AS CALIBER_FLAG,
           LV1_CODE AS PARENT_CODE,
           LV1_CN_NAME AS PARENT_CN_NAME,
		   PERIOD_ID --202405版本修改 方案修改 加入会计期
      FROM FIN_DM_OPT_FOI.DM_FOM_TOP_MANUFACTURE_DTL_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG
	   AND PERIOD_YEAR IN (YEAR(CURRENT_DATE), YEAR(CURRENT_DATE) - 1)
     GROUP BY LV0_CODE,
              LV0_CN_NAME,
              LV1_CODE,
              LV1_CN_NAME,
              BUSSINESS_OBJECT_CODE,
              BUSSINESS_OBJECT_CN_NAME,
              CALIBER_FLAG,
			  PERIOD_ID --202405版本修改 方案修改 加入会计期
    
    UNION ALL
    --LV1层级金额卷积
    SELECT LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           '',
           '',
           '',
           '',
		   '',
		   '',
           LV1_CODE AS GROUP_CODE,
           LV1_CN_NAME AS GROUP_CN_NAME,
           'LV1' AS GROUP_LEVEL,
           SUM(RMB_COST_AMT),
           V_CALIBER_FLAG AS CALIBER_FLAG,
           LV0_CODE AS PARENT_CODE,
           LV0_CN_NAME AS PARENT_CN_NAME,
		   PERIOD_ID --202405版本修改 方案修改 加入会计期
      FROM FIN_DM_OPT_FOI.DM_FOM_TOP_MANUFACTURE_DTL_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG
	   AND PERIOD_YEAR IN (YEAR(CURRENT_DATE), YEAR(CURRENT_DATE) - 1)
     GROUP BY LV0_CODE, LV0_CN_NAME, LV1_CODE, LV1_CN_NAME, CALIBER_FLAG,PERIOD_ID --202405版本修改 方案修改 加入会计期
    
    UNION ALL
    --LV0层级金额卷积
    SELECT LV0_CODE,
           LV0_CN_NAME,
           '',
           '',
           '',
           '',
           '',
           '',
		   '',
		   '',
           LV0_CODE AS GROUP_CODE,
           LV0_CN_NAME AS GROUP_CN_NAME,
           'LV0' AS GROUP_LEVEL,
           SUM(RMB_COST_AMT),
           V_CALIBER_FLAG AS CALIBER_FLAG,
           '' AS PARENT_CODE,
           '' AS PARENT_CN_NAME,
		   PERIOD_ID --202405版本修改 方案修改 加入会计期
      FROM FIN_DM_OPT_FOI.DM_FOM_TOP_MANUFACTURE_DTL_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG
	   AND PERIOD_YEAR IN (YEAR(CURRENT_DATE), YEAR(CURRENT_DATE) - 1)
     GROUP BY LV0_CODE, LV0_CN_NAME, CALIBER_FLAG,PERIOD_ID --202405版本修改 方案修改 加入会计期
	 ;
			 
			
--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '金额表插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 

  
  V_STEP_NUM:= V_STEP_NUM+1;
--3.权重计算
--3.1 权重饼图权重计算
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_MONTH_WEIGHT_T
    (VERSION_ID,
     PERIOD_YEAR,
     LV0_CODE,
     LV0_CN_NAME,
     LV1_CODE,
     LV1_CN_NAME,
     BUSSINESS_OBJECT_CODE,
     BUSSINESS_OBJECT_CN_NAME,
     SHIPPING_OBJECT_CODE,
     SHIPPING_OBJECT_CN_NAME,
     MANUFACTURE_OBJECT_CODE,
     MANUFACTURE_OBJECT_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     WEIGHT_RATE,
     PARENT_CODE,
     PARENT_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     CALIBER_FLAG,
	 USE_TYPE) --202405版本修改 新增使用类型字段，用于区分是指数权重还是权重饼图权重
	 
 --计算制造对象，发货对象，经营对象，LV1与LV0的权重，LV0权重为1 
  SELECT V_VERSION AS VERSION_ID,
         V_PERIOD_YEAR AS PERIOD_YEAR,
         LV0_CODE,
         LV0_CN_NAME,
         LV1_CODE,
         LV1_CN_NAME,
         BUSSINESS_OBJECT_CODE,
         BUSSINESS_OBJECT_CN_NAME,
         SHIPPING_OBJECT_CODE,
         SHIPPING_OBJECT_CN_NAME,
         MANUFACTURE_OBJECT_CODE,
         MANUFACTURE_OBJECT_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         CASE
           WHEN GROUP_LEVEL = 'MANUFACTURE_OBJECT' THEN
            SUM (RMB_COST_AMT) / NULLIF(SUM(SUM (RMB_COST_AMT)) OVER(PARTITION BY LV0_CODE,LV1_CODE,BUSSINESS_OBJECT_CODE,SHIPPING_OBJECT_CODE,GROUP_LEVEL),0)
           WHEN GROUP_LEVEL = 'SHIPPING_OBJECT' THEN
            SUM (RMB_COST_AMT) / NULLIF(SUM(SUM (RMB_COST_AMT)) OVER(PARTITION BY LV0_CODE,LV1_CODE,BUSSINESS_OBJECT_CODE,GROUP_LEVEL),0)
           WHEN GROUP_LEVEL = 'BUSSINESS_OBJECT' THEN
            SUM (RMB_COST_AMT) / NULLIF(SUM(SUM (RMB_COST_AMT)) OVER(PARTITION BY LV0_CODE, LV1_CODE,GROUP_LEVEL),0)
           WHEN GROUP_LEVEL = 'LV1' THEN
            SUM (RMB_COST_AMT) / NULLIF(SUM(SUM (RMB_COST_AMT)) OVER(PARTITION BY LV0_CODE,GROUP_LEVEL), 0)
           WHEN GROUP_LEVEL = 'LV0' THEN
            SUM (RMB_COST_AMT) / NULLIF(SUM(SUM (RMB_COST_AMT)) OVER(PARTITION BY LV0_CODE,GROUP_LEVEL), 0)
         END AS WEIGHT_RATE,
         PARENT_CODE,
         PARENT_CN_NAME,
         '-1' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         '-1' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG ,
		 V_CALIBER_FLAG AS CALIBER_FLAG,
		 'W' AS USE_TYPE --202405版本修改 新增使用类型字段 W为饼图权重类型
      FROM DM_LEV_TOP_AMT_TEMP	--202405版本修改 不筛选会计期,T-1与T(YTD)为饼图权重会计期范围
	 GROUP BY LV0_CODE,
	          LV0_CN_NAME,
	          LV1_CODE,
	          LV1_CN_NAME,
	          BUSSINESS_OBJECT_CODE,
	          BUSSINESS_OBJECT_CN_NAME,
	          SHIPPING_OBJECT_CODE,
	          SHIPPING_OBJECT_CN_NAME,
	          MANUFACTURE_OBJECT_CODE,
	          MANUFACTURE_OBJECT_CN_NAME,
	          GROUP_CODE,
	          GROUP_CN_NAME,
	          GROUP_LEVEL,
			  PARENT_CODE,
	          PARENT_CN_NAME
	 
    UNION ALL
 --存入ITEM层级权重
    SELECT V_VERSION AS VERSION_ID,
           V_PERIOD_YEAR AS PERIOD_YEAR,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           BUSSINESS_OBJECT_CODE,
           BUSSINESS_OBJECT_CN_NAME,
           SHIPPING_OBJECT_CODE,
           SHIPPING_OBJECT_CN_NAME,
           MANUFACTURE_OBJECT_CODE,
           MANUFACTURE_OBJECT_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           'ITEM' AS GROUP_LEVEL,
           WEIGHT_RATE,
           PARENT_CODE,
           PARENT_CN_NAME,
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG ,
		   V_CALIBER_FLAG AS CALIBER_FLAG,
		   USE_TYPE --202405版本修改 ITEM权重字段直取 已包含两个类型权重
      FROM DM_FOM_MONTH_MID_WEIGHT_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG;
   
--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '权重饼图权重计算完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
  V_STEP_NUM:= V_STEP_NUM+1;
--3.权重计算
--3.2 指数权重计算
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_MONTH_WEIGHT_T
    (VERSION_ID,
     PERIOD_YEAR,
     LV0_CODE,
     LV0_CN_NAME,
     LV1_CODE,
     LV1_CN_NAME,
     BUSSINESS_OBJECT_CODE,
     BUSSINESS_OBJECT_CN_NAME,
     SHIPPING_OBJECT_CODE,
     SHIPPING_OBJECT_CN_NAME,
     MANUFACTURE_OBJECT_CODE,
     MANUFACTURE_OBJECT_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     WEIGHT_RATE,
     PARENT_CODE,
     PARENT_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     CALIBER_FLAG,
	 USE_TYPE) --202405版本修改 新增使用类型字段，用于区分是指数权重还是权重饼图权重
	 
 --计算制造对象，发货对象，经营对象，LV1与LV0的权重，LV0权重为1 
  SELECT V_VERSION AS VERSION_ID,
         V_PERIOD_YEAR AS PERIOD_YEAR,
         LV0_CODE,
         LV0_CN_NAME,
         LV1_CODE,
         LV1_CN_NAME,
         BUSSINESS_OBJECT_CODE,
         BUSSINESS_OBJECT_CN_NAME,
         SHIPPING_OBJECT_CODE,
         SHIPPING_OBJECT_CN_NAME,
         MANUFACTURE_OBJECT_CODE,
         MANUFACTURE_OBJECT_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         CASE
           WHEN GROUP_LEVEL = 'MANUFACTURE_OBJECT' THEN
            SUM (RMB_COST_AMT) / NULLIF(SUM(SUM (RMB_COST_AMT)) OVER(PARTITION BY LV0_CODE,LV1_CODE,BUSSINESS_OBJECT_CODE,SHIPPING_OBJECT_CODE,GROUP_LEVEL),0)
           WHEN GROUP_LEVEL = 'SHIPPING_OBJECT' THEN
            SUM (RMB_COST_AMT) / NULLIF(SUM(SUM (RMB_COST_AMT)) OVER(PARTITION BY LV0_CODE,LV1_CODE,BUSSINESS_OBJECT_CODE,GROUP_LEVEL),0)
           WHEN GROUP_LEVEL = 'BUSSINESS_OBJECT' THEN
            SUM (RMB_COST_AMT) / NULLIF(SUM(SUM (RMB_COST_AMT)) OVER(PARTITION BY LV0_CODE, LV1_CODE,GROUP_LEVEL),0)
           WHEN GROUP_LEVEL = 'LV1' THEN
            SUM (RMB_COST_AMT) / NULLIF(SUM(SUM (RMB_COST_AMT)) OVER(PARTITION BY LV0_CODE,GROUP_LEVEL), 0)
           WHEN GROUP_LEVEL = 'LV0' THEN
            SUM (RMB_COST_AMT) / NULLIF(SUM(SUM (RMB_COST_AMT)) OVER(PARTITION BY LV0_CODE,GROUP_LEVEL), 0)
         END AS WEIGHT_RATE,
         PARENT_CODE,
         PARENT_CN_NAME,
         '-1' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         '-1' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG ,
		 V_CALIBER_FLAG AS CALIBER_FLAG,
		 'I' AS USE_TYPE --202405版本修改 新增使用类型字段 I为指数权重类型
      FROM DM_LEV_TOP_AMT_TEMP
	 WHERE PERIOD_ID >= CAST(TO_CHAR(ADD_MONTHS(NOW(),-12),'YYYYMM') AS BIGINT ) --202405版本修改 时间范围前滚12个月 (仅指数权重)
	 GROUP BY LV0_CODE,
	          LV0_CN_NAME,
	          LV1_CODE,
	          LV1_CN_NAME,
	          BUSSINESS_OBJECT_CODE,
	          BUSSINESS_OBJECT_CN_NAME,
	          SHIPPING_OBJECT_CODE,
	          SHIPPING_OBJECT_CN_NAME,
	          MANUFACTURE_OBJECT_CODE,
	          MANUFACTURE_OBJECT_CN_NAME,
	          GROUP_CODE,
	          GROUP_CN_NAME,
	          GROUP_LEVEL,
			  PARENT_CODE,
	          PARENT_CN_NAME;
	 
--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '指数权重计算完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
  
 ANALYZE FIN_DM_OPT_FOI.DM_FOM_MONTH_WEIGHT_T;

RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END

$$
/

