-- Name: f_dm_fcst_ict_repl_pre_base_cus_result; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_ict_repl_pre_base_cus_result(f_cost_type character varying, f_granularity_type character varying, f_ytd_flag character varying, f_keystr text DEFAULT NULL::text, f_version_id integer DEFAULT NULL::integer, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间:2024年10月16日16点21分
创建人: 黄心蕊
描述: ICT经管-编码替代页面-单选
参数描述: 参数一: F_COST_TYPE 成本类型:PSP PSP成本; STD: 标准成本
		参数二: F_GRANULARITY_TYPE 目录树类型:	IRB:重量级团队目录; INDUS:产业目录;  PROD:销售目录 指数类型为降成本指数时本入参为空
		参数三: F_YTD_FLAG 是否月累计标签 'MON' 月度 , 'YTD' 月累计
		参数四: F_KEYSTR	密钥
		参数五: F_VERSION_ID 版本号 
		参数六: X_RESULT_STATUS 运行状态返回值 ‘1’为成功，‘0’为失败

--------来源表
----月度
--替代关系指数表 (取SPART层级)
PSP DM_FCST_ICT_PSP_PROD_MON_REPL_COST_IDX_T
STD DM_FCST_ICT_STD_PROD_MON_REPL_COST_IDX_T

--同基指数表 (取SPART层级)
PSP DM_FCST_ICT_PSP_PROD_REPL_SAME_COST_IDX_T
STD DM_FCST_ICT_STD_PROD_REPL_SAME_COST_IDX_T

--金额
PSP DM_FCST_ICT_PSP_PROD_MON_REPL_COST_CV_T
STD DM_FCST_ICT_STD_PROD_MON_REPL_COST_CV_T

----月累计
--替代关系指数表 (取SPART层级)
PSP DM_FCST_ICT_PSP_PROD_YTD_REPL_COST_IDX_T
STD DM_FCST_ICT_STD_PROD_YTD_REPL_COST_IDX_T

--金额
PSP DM_FCST_ICT_PSP_PROD_YTD_REPL_COST_CV_T
STD DM_FCST_ICT_STD_PROD_YTD_REPL_COST_CV_T

--同基指数表 (取SPART层级)
PSP DM_FCST_ICT_PSP_PROD_REPL_SAME_YTD_COST_IDX_T
STD DM_FCST_ICT_STD_PROD_REPL_SAME_YTD_COST_IDX_T

--------目标表
----月度
--金额表
PSP DM_FCST_ICT_PSP_BASE_CUS_MON_REPL_INFO_IDX_T
STD DM_FCST_ICT_STD_BASE_CUS_MON_REPL_INFO_IDX_T

--权重表
PSP DM_FCST_ICT_PSP_BASE_CUS_MON_REPL_WEIGHT_IDX_T
STD DM_FCST_ICT_STD_BASE_CUS_MON_REPL_WEIGHT_IDX_T

--指数表
PSP DM_FCST_ICT_PSP_BASE_CUS_MON_REPL_COST_IDX_T
STD DM_FCST_ICT_STD_BASE_CUS_MON_REPL_COST_IDX_T

----月累计
--金额表
PSP DM_FCST_ICT_PSP_BASE_CUS_YTD_REPL_INFO_IDX_T
STD DM_FCST_ICT_STD_BASE_CUS_YTD_REPL_INFO_IDX_T

--指数表
PSP DM_FCST_ICT_PSP_BASE_CUS_YTD_REPL_COST_IDX_T
STD DM_FCST_ICT_STD_BASE_CUS_YTD_REPL_COST_IDX_T

SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_REPL_PRE_BASE_CUS_RESULT('PSP','PROD','MON','','');
SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_REPL_PRE_BASE_CUS_RESULT('PSP','PROD','YTD','','');
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                 VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FCST_ICT_REPL_PRE_BASE_CUS_RESULT';
  V_VERSION                 VARCHAR(10);
  V_EXCEPTION_FLAG          INT;
  V_YEAR                    VARCHAR(50);
  V_FROM_REPL_IDX_TABLE     VARCHAR(200);
  V_FROM_SAME_IDX_TABLE     VARCHAR(200);
  V_FROM_SAME_MON_IDX_TABLE VARCHAR(200);
  V_FROM_SAME_YTD_IDX_TABLE VARCHAR(200);
  V_FROM_REPL_MON_IDX_TABLE VARCHAR(200);
  V_FROM_REPL_YTD_IDX_TABLE VARCHAR(200);
  V_FROM_AMT_TABLE          VARCHAR(100);
  V_FROM_PBI_DIM_TABLE		VARCHAR(200);
  V_TO_IDX_TABLE            VARCHAR(200);
  V_TO_WEIGHT_TABLE         VARCHAR(200);
  V_TO_AMT_TABLE            VARCHAR(200);
  V_TO_MON_IDX_TABLE        VARCHAR(200);
  V_TO_YTD_IDX_TABLE        VARCHAR(200);
  V_TO_MON_AMT_TABLE        VARCHAR(200);
  V_TO_YTD_AMT_TABLE        VARCHAR(200);
  V_BASE_PERIOD_ID          INT := TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY')-1||'01'; --基期会计期
									
  V_SQL                     TEXT;
  
BEGIN 

  X_RESULT_STATUS := '1';

 --写入日志
 V_EXCEPTION_FLAG	:= 0;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 0,
  F_CAL_LOG_DESC => '开始执行',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
--取版本号
--月度版本号取值
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;
  
    
  V_FROM_SAME_MON_IDX_TABLE	:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_REPL_SAME_COST_IDX_T';
  V_FROM_SAME_YTD_IDX_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_REPL_SAME_YTD_COST_IDX_T';
  V_FROM_REPL_MON_IDX_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_MON_REPL_COST_IDX_T';
  V_FROM_REPL_YTD_IDX_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_YTD_REPL_COST_IDX_T';
  V_FROM_PBI_DIM_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_DIM_INFO_T';
  V_TO_MON_IDX_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_MON_REPL_COST_IDX_T';
  V_TO_YTD_IDX_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_YTD_REPL_COST_IDX_T';
  V_TO_WEIGHT_TABLE        	:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_MON_REPL_WEIGHT_IDX_T';
  V_TO_MON_AMT_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_MON_REPL_INFO_IDX_T';
  V_TO_YTD_AMT_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_YTD_REPL_INFO_IDX_T';
  V_YEAR 					:= TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY')-1 || '-' || TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY');
  
  IF F_YTD_FLAG = 'MON' THEN
    --月度表
    V_FROM_REPL_IDX_TABLE := V_FROM_REPL_MON_IDX_TABLE;
    V_FROM_SAME_IDX_TABLE := V_FROM_SAME_MON_IDX_TABLE;
    V_FROM_AMT_TABLE      := 'DM_BASE_MON_AMT_TEMP';
    V_TO_IDX_TABLE        := V_TO_MON_IDX_TABLE;
    V_TO_AMT_TABLE        := V_TO_MON_AMT_TABLE;
  ELSIF F_YTD_FLAG = 'YTD' THEN
    --月累计表
    V_FROM_REPL_IDX_TABLE := V_FROM_REPL_YTD_IDX_TABLE;
    V_FROM_SAME_IDX_TABLE := V_FROM_SAME_YTD_IDX_TABLE;
    V_FROM_AMT_TABLE      := 'DM_BASE_YTD_AMT_TEMP';
    V_TO_IDX_TABLE        := V_TO_YTD_IDX_TABLE;
    V_TO_AMT_TABLE        := V_TO_YTD_AMT_TABLE;
  END IF;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '取得本次执行数据成本类型：'||F_COST_TYPE||'，目录树：'||F_GRANULARITY_TYPE||'，版本号：'||V_VERSION,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 


--取金额-用于计算虚化权重
--建临时表
DROP TABLE IF EXISTS DM_BASE_MON_AMT_TEMP;
CREATE TEMPORARY TABLE DM_BASE_MON_AMT_TEMP(
 PROD_LIST_CODE			VARCHAR(50),
 PROD_LIST_CN_NAME		VARCHAR(200),
 PERIOD_YEAR			INT,
 PERIOD_ID				INT,
 GROUP_CODE				VARCHAR(50)  ,
 GROUP_CN_NAME			VARCHAR(200) ,
 PARENT_CODE			VARCHAR(50)  ,
 PARENT_CN_NAME			VARCHAR(200) ,
 RMB_COST_AMT			NUMERIC,
 SHIPMENT_QTY			NUMERIC,
 REPLACE_RELATION_NAME	VARCHAR(200),
 REPLACE_RELATION_TYPE	VARCHAR(50),
 RELATION_TYPE			VARCHAR(50),
 CODE_TYPE				VARCHAR(50),
 REGION_CODE			VARCHAR(50)  ,
 REGION_CN_NAME			VARCHAR(200) ,
 REPOFFICE_CODE			VARCHAR(50)  ,
 REPOFFICE_CN_NAME		VARCHAR(200) ,
 BG_CODE				VARCHAR(50)  ,
 BG_CN_NAME         	VARCHAR(200) ,
 OVERSEA_FLAG			VARCHAR(10),    
 SOFTWARE_MARK			VARCHAR(20)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(GROUP_CODE,PROD_LIST_CODE);

--判断取金额
  IF F_COST_TYPE = 'PSP' THEN
  --PSP金额直取
    INSERT INTO DM_BASE_MON_AMT_TEMP
      (PROD_LIST_CODE,
       PROD_LIST_CN_NAME,
       PERIOD_YEAR,
       PERIOD_ID,
       GROUP_CODE,
       GROUP_CN_NAME,
       PARENT_CODE,
       PARENT_CN_NAME,
       RMB_COST_AMT,
	   SHIPMENT_QTY,
       REPLACE_RELATION_NAME,
       REPLACE_RELATION_TYPE,
       RELATION_TYPE,
       CODE_TYPE,
       REGION_CODE,
       REGION_CN_NAME,
       REPOFFICE_CODE,
       REPOFFICE_CN_NAME,
       BG_CODE,
       BG_CN_NAME,
       OVERSEA_FLAG,
       SOFTWARE_MARK)
      SELECT PROD_LIST_CODE,
             PROD_LIST_CN_NAME,
             PERIOD_YEAR,
             PERIOD_ID,
             GROUP_CODE,
             GROUP_CN_NAME,
             PARENT_CODE,
             PARENT_CN_NAME,
             RMB_COST_AMT,
			 SHIPMENT_QTY,
             REPLACE_RELATION_NAME,
             REPLACE_RELATION_TYPE,
             RELATION_TYPE,
             CODE_TYPE,
             REGION_CODE,
             REGION_CN_NAME,
             REPOFFICE_CODE,
             REPOFFICE_CN_NAME,
             BG_CODE,
             BG_CN_NAME,
             OVERSEA_FLAG,
             SOFTWARE_MARK
        FROM FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_PROD_MON_REPL_COST_CV_T
       WHERE VERSION_ID = V_VERSION
         AND BASE_PERIOD_ID = V_BASE_PERIOD_ID
         AND GROUP_LEVEL = 'SPART';
		 
  ELSIF F_COST_TYPE = 'STD' THEN
  --STD金额解密落表
    INSERT INTO DM_BASE_MON_AMT_TEMP
      (PROD_LIST_CODE,
       PROD_LIST_CN_NAME,
       PERIOD_YEAR,
       PERIOD_ID,
       GROUP_CODE,
       GROUP_CN_NAME,
       PARENT_CODE,
       PARENT_CN_NAME,
       RMB_COST_AMT,
	   SHIPMENT_QTY,
       REPLACE_RELATION_NAME,
       REPLACE_RELATION_TYPE,
       RELATION_TYPE,
       CODE_TYPE,
       REGION_CODE,
       REGION_CN_NAME,
       REPOFFICE_CODE,
       REPOFFICE_CN_NAME,
       BG_CODE,
       BG_CN_NAME,
       OVERSEA_FLAG,
       SOFTWARE_MARK)
    SELECT PROD_LIST_CODE,
           PROD_LIST_CN_NAME,
           PERIOD_YEAR,
           PERIOD_ID,
           GROUP_CODE,
           GROUP_CN_NAME,
           PARENT_CODE,
           PARENT_CN_NAME,
           TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,
                                F_KEYSTR,
                                'aes128',
                                'cbc',
                                'sha256')) AS RMB_COST_AMT,
           SHIPMENT_QTY,
		   REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           RELATION_TYPE,
           CODE_TYPE,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
           SOFTWARE_MARK
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_STD_PROD_MON_REPL_COST_CV_T
     WHERE VERSION_ID = V_VERSION
       AND BASE_PERIOD_ID = V_BASE_PERIOD_ID
       AND GROUP_LEVEL = 'SPART';

  END IF;
  
   --写入日志
 V_EXCEPTION_FLAG	:= 2;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 2,
  F_CAL_LOG_DESC => '基础金额解密插数完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
--权重计算
--权重表建表
DROP TABLE IF EXISTS DM_REPL_WEIGHT_TEMP;
CREATE TEMPORARY TABLE DM_REPL_WEIGHT_TEMP(
 LV0_CODE	VARCHAR(50),
 LV1_CODE	VARCHAR(50),
 LV2_CODE	VARCHAR(50),
 LV3_CODE	VARCHAR(50),
 LV4_CODE	VARCHAR(50),
 LV0_NAME	VARCHAR(200),
 LV1_NAME	VARCHAR(200),
 LV2_NAME	VARCHAR(200),
 LV3_NAME	VARCHAR(200),
 LV4_NAME	VARCHAR(200),
 PROD_LIST_CODE				VARCHAR(50),
 PROD_LIST_CN_NAME           VARCHAR(200),
 GROUP_CODE                  VARCHAR(50)  ,
 GROUP_CN_NAME               VARCHAR(200) ,
 WEIGHT_RATE                 NUMERIC  ,
 PARENT_LEVEL                VARCHAR(200) ,
 PARENT_CN_NAME              VARCHAR(200),
 REPLACE_RELATION_NAME       VARCHAR(200),
 REPLACE_RELATION_TYPE       VARCHAR(50),
 RELATION_TYPE               VARCHAR(50),
 CODE_TYPE                   VARCHAR(50),
 REGION_CODE                 VARCHAR(50)  ,
 REGION_CN_NAME              VARCHAR(200) ,
 REPOFFICE_CODE              VARCHAR(50)  ,
 REPOFFICE_CN_NAME           VARCHAR(200) ,
 BG_CODE                     VARCHAR(50)  ,
 BG_CN_NAME                  VARCHAR(200) ,
 OVERSEA_FLAG                VARCHAR(10),    
 SOFTWARE_MARK               VARCHAR(20)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(GROUP_CODE,PROD_LIST_CODE);

V_SQL:= '
--两年金额收敛              
  WITH BASE_YEARS_AMT AS
   (SELECT T2.LV0_PROD_LIST_CODE AS LV0_CODE,
           T2.LV0_PROD_LIST_CN_NAME AS LV0_NAME,
           T2.LV1_PROD_LIST_CODE AS LV1_CODE,
           T2.LV1_PROD_LIST_CN_NAME AS LV1_NAME,
           T2.LV2_PROD_LIST_CODE AS LV2_CODE,
           T2.LV2_PROD_LIST_CN_NAME AS LV2_NAME,
           T2.LV3_PROD_LIST_CODE AS LV3_CODE,
           T2.LV3_PROD_LIST_CN_NAME AS LV3_NAME,
           T2.LV4_PROD_LIST_CODE AS LV4_CODE,
           T2.LV4_PROD_LIST_CN_NAME AS LV4_NAME,
		   T1.GROUP_CODE,
           T1.GROUP_CN_NAME,
           SUM(T1.RMB_COST_AMT) AS YEARS_AMT,
           T1.PARENT_CODE,
           T1.PARENT_CN_NAME,
           T1.REPLACE_RELATION_NAME,
           T1.REPLACE_RELATION_TYPE,
           T1.RELATION_TYPE,
           T1.CODE_TYPE,
           T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
           T1.SOFTWARE_MARK
      FROM DM_BASE_MON_AMT_TEMP T1
      LEFT JOIN (SELECT DISTINCT LV0_PROD_LIST_CODE,
                                LV0_PROD_LIST_CN_NAME,
                                LV1_PROD_LIST_CODE,
                                LV1_PROD_LIST_CN_NAME,
                                LV2_PROD_LIST_CODE,
                                LV2_PROD_LIST_CN_NAME,
                                LV3_PROD_LIST_CODE,
                                LV3_PROD_LIST_CN_NAME,
                                LV4_PROD_LIST_CODE,
                                LV4_PROD_LIST_CN_NAME
                  FROM '||V_FROM_PBI_DIM_TABLE||'
                 WHERE GROUP_LEVEL = ''LV4'') T2
        ON T1.PROD_LIST_CODE = T2.LV4_PROD_LIST_CODE
     WHERE PERIOD_YEAR IN (TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),''YYYY''), TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),''YYYY'')-1)
     GROUP BY T2.LV0_PROD_LIST_CODE,
	          T2.LV0_PROD_LIST_CN_NAME,
	          T2.LV1_PROD_LIST_CODE,
	          T2.LV1_PROD_LIST_CN_NAME,
	          T2.LV2_PROD_LIST_CODE,
	          T2.LV2_PROD_LIST_CN_NAME,
	          T2.LV3_PROD_LIST_CODE,
	          T2.LV3_PROD_LIST_CN_NAME,
	          T2.LV4_PROD_LIST_CODE,
	          T2.LV4_PROD_LIST_CN_NAME,
              T1.GROUP_CODE,
              T1.GROUP_CN_NAME,
              T1.PARENT_CODE,
              T1.PARENT_CN_NAME,
              T1.REPLACE_RELATION_NAME,
              T1.REPLACE_RELATION_TYPE,
              T1.RELATION_TYPE,
              T1.CODE_TYPE,
              T1.REGION_CODE,
              T1.REGION_CN_NAME,
              T1.REPOFFICE_CODE,
              T1.REPOFFICE_CN_NAME,
              T1.BG_CODE,
              T1.BG_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.SOFTWARE_MARK)
  INSERT INTO DM_REPL_WEIGHT_TEMP
    (LV0_CODE,
	 LV1_CODE,
	 LV2_CODE,
	 LV3_CODE,
	 LV4_CODE,
	 LV0_NAME,
	 LV1_NAME,
	 LV2_NAME,
	 LV3_NAME,
	 LV4_NAME,
	 GROUP_CODE,
     GROUP_CN_NAME,
     WEIGHT_RATE,
     PARENT_LEVEL,
     PARENT_CN_NAME,
     REPLACE_RELATION_NAME,
     REPLACE_RELATION_TYPE,
     RELATION_TYPE,
     CODE_TYPE,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     BG_CODE,
     BG_CN_NAME,
     OVERSEA_FLAG,
     SOFTWARE_MARK)
  --单选替代关系类型(一对一,一对多,多对一)
  SELECT LV0_CODE,
		 LV1_CODE,
		 LV2_CODE,
		 LV3_CODE,
		 LV4_CODE,
		 LV0_NAME,
		 LV1_NAME,
		 LV2_NAME,
		 LV3_NAME,
		 LV4_NAME,
		 GROUP_CODE,
         GROUP_CN_NAME,
         YEARS_AMT / SUM(YEARS_AMT) OVER(PARTITION BY REPLACE_RELATION_TYPE, CODE_TYPE,LV0_CODE,
													REGION_CODE, REPOFFICE_CODE, BG_CODE, OVERSEA_FLAG, SOFTWARE_MARK),
         ''LV0_REPLACE'' AS PARENT_LEVEL,
         LV0_CODE||REPLACE_RELATION_TYPE AS PARENT_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         CODE_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         SOFTWARE_MARK
    FROM BASE_YEARS_AMT
  --单选关系类型(收编,替换)
  UNION ALL
  SELECT LV0_CODE,
		 LV1_CODE,
		 LV2_CODE,
		 LV3_CODE,
		 LV4_CODE,
		 LV0_NAME,
		 LV1_NAME,
		 LV2_NAME,
		 LV3_NAME,
		 LV4_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         YEARS_AMT / SUM(YEARS_AMT) OVER(PARTITION BY RELATION_TYPE, CODE_TYPE,LV0_CODE,
													REGION_CODE, REPOFFICE_CODE, BG_CODE, OVERSEA_FLAG, SOFTWARE_MARK),
         ''LV0_RELATION'' AS PARENT_LEVEL,
         LV0_CODE||RELATION_TYPE AS PARENT_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         CODE_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         SOFTWARE_MARK
    FROM BASE_YEARS_AMT
  -- 混选替代关系类型及关系类型(一对一&收编,一对一&替换 / 一对多&收编,一对多&替换 / 多对多&收编,多对多&替换)
  UNION ALL
  SELECT LV0_CODE,
		 LV1_CODE,
		 LV2_CODE,
		 LV3_CODE,
		 LV4_CODE,
		 LV0_NAME,
		 LV1_NAME,
		 LV2_NAME,
		 LV3_NAME,
		 LV4_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         YEARS_AMT / SUM(YEARS_AMT) OVER(PARTITION BY RELATION_TYPE, REPLACE_RELATION_TYPE, CODE_TYPE, LV0_CODE,
													REGION_CODE, REPOFFICE_CODE, BG_CODE, OVERSEA_FLAG, SOFTWARE_MARK),
         ''LV0_REPLACE_RELATION'' AS PARENT_LEVEL,
         LV0_CODE||REPLACE_RELATION_TYPE||RELATION_TYPE AS PARENT_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         CODE_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         SOFTWARE_MARK
    FROM BASE_YEARS_AMT

  ----LV1
  --单选LV1+替代关系类型(一对一,一对多,多对一)
  UNION ALL
  SELECT LV0_CODE,
		 LV1_CODE,
		 LV2_CODE,
		 LV3_CODE,
		 LV4_CODE,
		 LV0_NAME,
		 LV1_NAME,
		 LV2_NAME,
		 LV3_NAME,
		 LV4_NAME,
		 GROUP_CODE,
         GROUP_CN_NAME,
         YEARS_AMT / SUM(YEARS_AMT) OVER(PARTITION BY REPLACE_RELATION_TYPE, CODE_TYPE,LV1_CODE,
													REGION_CODE, REPOFFICE_CODE, BG_CODE, OVERSEA_FLAG, SOFTWARE_MARK),
         ''LV1_REPLACE'' AS PARENT_LEVEL,
         LV1_CODE||REPLACE_RELATION_TYPE AS PARENT_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         CODE_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         SOFTWARE_MARK
    FROM BASE_YEARS_AMT
  --单选LV1+关系类型(收编,替换)
  UNION ALL
  SELECT LV0_CODE,
		 LV1_CODE,
		 LV2_CODE,
		 LV3_CODE,
		 LV4_CODE,
		 LV0_NAME,
		 LV1_NAME,
		 LV2_NAME,
		 LV3_NAME,
		 LV4_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         YEARS_AMT / SUM(YEARS_AMT) OVER(PARTITION BY RELATION_TYPE, CODE_TYPE,LV1_CODE,
													REGION_CODE, REPOFFICE_CODE, BG_CODE, OVERSEA_FLAG, SOFTWARE_MARK),
         ''LV1_RELATION'' AS PARENT_LEVEL,
         LV1_CODE||RELATION_TYPE AS PARENT_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         CODE_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         SOFTWARE_MARK
    FROM BASE_YEARS_AMT
  -- 混选LV1+替代关系类型及关系类型(一对一&收编,一对一&替换 / 一对多&收编,一对多&替换 / 多对多&收编,多对多&替换)
  UNION ALL
  SELECT LV0_CODE,
		 LV1_CODE,
		 LV2_CODE,
		 LV3_CODE,
		 LV4_CODE,
		 LV0_NAME,
		 LV1_NAME,
		 LV2_NAME,
		 LV3_NAME,
		 LV4_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         YEARS_AMT / SUM(YEARS_AMT) OVER(PARTITION BY RELATION_TYPE, REPLACE_RELATION_TYPE, CODE_TYPE, LV1_CODE,
													REGION_CODE, REPOFFICE_CODE, BG_CODE, OVERSEA_FLAG, SOFTWARE_MARK),
         ''LV1_REPLACE_RELATION'' AS PARENT_LEVEL,
         LV1_CODE||REPLACE_RELATION_TYPE||RELATION_TYPE AS PARENT_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         CODE_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         SOFTWARE_MARK
    FROM BASE_YEARS_AMT
	
  ----LV2	
  --单选LV2+替代关系类型(一对一,一对多,多对一)
  UNION ALL
  SELECT LV0_CODE,
		 LV1_CODE,
		 LV2_CODE,
		 LV3_CODE,
		 LV4_CODE,
		 LV0_NAME,
		 LV1_NAME,
		 LV2_NAME,
		 LV3_NAME,
		 LV4_NAME,
		 GROUP_CODE,
         GROUP_CN_NAME,
         YEARS_AMT / SUM(YEARS_AMT) OVER(PARTITION BY REPLACE_RELATION_TYPE, CODE_TYPE,LV2_CODE,
													REGION_CODE, REPOFFICE_CODE, BG_CODE, OVERSEA_FLAG, SOFTWARE_MARK),
         ''LV2_REPLACE'' AS PARENT_LEVEL,
         LV2_CODE||REPLACE_RELATION_TYPE AS PARENT_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         CODE_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         SOFTWARE_MARK
    FROM BASE_YEARS_AMT
  --单选LV2+关系类型(收编,替换)
  UNION ALL
  SELECT LV0_CODE,
		 LV1_CODE,
		 LV2_CODE,
		 LV3_CODE,
		 LV4_CODE,
		 LV0_NAME,
		 LV1_NAME,
		 LV2_NAME,
		 LV3_NAME,
		 LV4_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         YEARS_AMT / SUM(YEARS_AMT) OVER(PARTITION BY RELATION_TYPE, CODE_TYPE,LV2_CODE,
													REGION_CODE, REPOFFICE_CODE, BG_CODE, OVERSEA_FLAG, SOFTWARE_MARK),
         ''LV2_RELATION'' AS PARENT_LEVEL,
         LV2_CODE||RELATION_TYPE AS PARENT_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         CODE_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         SOFTWARE_MARK
    FROM BASE_YEARS_AMT
  -- 混选LV2+替代关系类型及关系类型(一对一&收编,一对一&替换 / 一对多&收编,一对多&替换 / 多对多&收编,多对多&替换)
  UNION ALL
  SELECT LV0_CODE,
		 LV1_CODE,
		 LV2_CODE,
		 LV3_CODE,
		 LV4_CODE,
		 LV0_NAME,
		 LV1_NAME,
		 LV2_NAME,
		 LV3_NAME,
		 LV4_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         YEARS_AMT / SUM(YEARS_AMT) OVER(PARTITION BY RELATION_TYPE, REPLACE_RELATION_TYPE, CODE_TYPE, LV2_CODE,
													REGION_CODE, REPOFFICE_CODE, BG_CODE, OVERSEA_FLAG, SOFTWARE_MARK),
         ''LV2_REPLACE_RELATION'' AS PARENT_LEVEL,
         LV2_CODE||REPLACE_RELATION_TYPE||RELATION_TYPE AS PARENT_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         CODE_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         SOFTWARE_MARK
    FROM BASE_YEARS_AMT
	
  ----LV3
  --单选LV3+替代关系类型(一对一,一对多,多对一)
  UNION ALL
  SELECT LV0_CODE,
		 LV1_CODE,
		 LV2_CODE,
		 LV3_CODE,
		 LV4_CODE,
		 LV0_NAME,
		 LV1_NAME,
		 LV2_NAME,
		 LV3_NAME,
		 LV4_NAME,
		 GROUP_CODE,
         GROUP_CN_NAME,
         YEARS_AMT / SUM(YEARS_AMT) OVER(PARTITION BY REPLACE_RELATION_TYPE, CODE_TYPE,LV3_CODE,
													REGION_CODE, REPOFFICE_CODE, BG_CODE, OVERSEA_FLAG, SOFTWARE_MARK),
         ''LV3_REPLACE'' AS PARENT_LEVEL,
         LV3_CODE||REPLACE_RELATION_TYPE AS PARENT_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         CODE_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         SOFTWARE_MARK
    FROM BASE_YEARS_AMT
  --单选LV3+关系类型(收编,替换)
  UNION ALL
  SELECT LV0_CODE,
		 LV1_CODE,
		 LV2_CODE,
		 LV3_CODE,
		 LV4_CODE,
		 LV0_NAME,
		 LV1_NAME,
		 LV2_NAME,
		 LV3_NAME,
		 LV4_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         YEARS_AMT / SUM(YEARS_AMT) OVER(PARTITION BY RELATION_TYPE, CODE_TYPE,LV3_CODE,
													REGION_CODE, REPOFFICE_CODE, BG_CODE, OVERSEA_FLAG, SOFTWARE_MARK),
         ''LV3_RELATION'' AS PARENT_LEVEL,
         LV3_CODE||RELATION_TYPE AS PARENT_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         CODE_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         SOFTWARE_MARK
    FROM BASE_YEARS_AMT
  -- 混选LV3+替代关系类型及关系类型(一对一&收编,一对一&替换 / 一对多&收编,一对多&替换 / 多对多&收编,多对多&替换)
  UNION ALL
  SELECT LV0_CODE,
		 LV1_CODE,
		 LV2_CODE,
		 LV3_CODE,
		 LV4_CODE,
		 LV0_NAME,
		 LV1_NAME,
		 LV2_NAME,
		 LV3_NAME,
		 LV4_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         YEARS_AMT / SUM(YEARS_AMT) OVER(PARTITION BY RELATION_TYPE, REPLACE_RELATION_TYPE, CODE_TYPE, LV3_CODE,
													REGION_CODE, REPOFFICE_CODE, BG_CODE, OVERSEA_FLAG, SOFTWARE_MARK),
         ''LV3_REPLACE_RELATION'' AS PARENT_LEVEL,
         LV3_CODE||REPLACE_RELATION_TYPE||RELATION_TYPE AS PARENT_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         CODE_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         SOFTWARE_MARK
    FROM BASE_YEARS_AMT
	
  ----LV4
  --单选LV4+替代关系类型(一对一,一对多,多对一)
  UNION ALL
  SELECT LV0_CODE,
		 LV1_CODE,
		 LV2_CODE,
		 LV3_CODE,
		 LV4_CODE,
		 LV0_NAME,
		 LV1_NAME,
		 LV2_NAME,
		 LV3_NAME,
		 LV4_NAME,
		 GROUP_CODE,
         GROUP_CN_NAME,
         YEARS_AMT / SUM(YEARS_AMT) OVER(PARTITION BY REPLACE_RELATION_TYPE, CODE_TYPE,LV4_CODE,
													REGION_CODE, REPOFFICE_CODE, BG_CODE, OVERSEA_FLAG, SOFTWARE_MARK),
         ''LV4_REPLACE'' AS PARENT_LEVEL,
         LV4_CODE||REPLACE_RELATION_TYPE AS PARENT_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         CODE_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         SOFTWARE_MARK
    FROM BASE_YEARS_AMT
  --单选LV4+关系类型(收编,替换)
  UNION ALL
  SELECT LV0_CODE,
		 LV1_CODE,
		 LV2_CODE,
		 LV3_CODE,
		 LV4_CODE,
		 LV0_NAME,
		 LV1_NAME,
		 LV2_NAME,
		 LV3_NAME,
		 LV4_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         YEARS_AMT / SUM(YEARS_AMT) OVER(PARTITION BY RELATION_TYPE, CODE_TYPE,LV4_CODE,
													REGION_CODE, REPOFFICE_CODE, BG_CODE, OVERSEA_FLAG, SOFTWARE_MARK),
         ''LV4_RELATION'' AS PARENT_LEVEL,
         LV4_CODE||RELATION_TYPE AS PARENT_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         CODE_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         SOFTWARE_MARK
    FROM BASE_YEARS_AMT
  -- 混选LV4+替代关系类型及关系类型(一对一&收编,一对一&替换 / 一对多&收编,一对多&替换 / 多对多&收编,多对多&替换)
  UNION ALL
  SELECT LV0_CODE,
		 LV1_CODE,
		 LV2_CODE,
		 LV3_CODE,
		 LV4_CODE,
		 LV0_NAME,
		 LV1_NAME,
		 LV2_NAME,
		 LV3_NAME,
		 LV4_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         YEARS_AMT / SUM(YEARS_AMT) OVER(PARTITION BY RELATION_TYPE, REPLACE_RELATION_TYPE, CODE_TYPE, LV4_CODE,
													REGION_CODE, REPOFFICE_CODE, BG_CODE, OVERSEA_FLAG, SOFTWARE_MARK),
         ''LV4_REPLACE_RELATION'' AS PARENT_LEVEL,
         LV4_CODE||REPLACE_RELATION_TYPE||RELATION_TYPE AS PARENT_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         CODE_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         SOFTWARE_MARK
    FROM BASE_YEARS_AMT;';
	
EXECUTE V_SQL;
	
 --写入日志
 V_EXCEPTION_FLAG	:= 3;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 3,
  F_CAL_LOG_DESC => '权重计算完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  
  V_SQL:='
  
  DELETE FROM '||V_TO_WEIGHT_TABLE||' WHERE VERSION_ID = '||V_VERSION||' AND GROUP_LEVEL_TYPE = ''PRE_CUS'' ;
  
  INSERT INTO '||V_TO_WEIGHT_TABLE||'
    (VERSION_ID,
     CUSTOM_ID,
     CUSTOM_CN_NAME,
     PERIOD_YEAR,
     PERIOD_ID,
     BASE_PERIOD_ID,
     PBI_DIM_CODE,
     PBI_DIM_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     GROUP_LEVEL_TYPE,
     WEIGHT_RATE,
     REPLACE_RELATION_NAME,
     REPLACE_RELATION_TYPE,
     RELATION_TYPE,
     PARENT_CODE,
     PARENT_CN_NAME,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     BG_CODE,
     BG_CN_NAME,
     OVERSEA_FLAG,
	 SOFTWARE_MARK,
     GRANULARITY_TYPE,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           YEAR(NOW()) AS PERIOD_YEAR,
           '''' AS PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           LV4_CODE AS PBI_DIM_CODE,
           LV4_NAME AS PBI_DIM_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           ''SPART'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           WEIGHT_RATE,
           REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           RELATION_TYPE,
           PARENT_CN_NAME AS PARENT_CODE,
           PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_REPL_WEIGHT_TEMP;
	  ';
  
  EXECUTE V_SQL;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 4;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 4,
  F_CAL_LOG_DESC => '权重结果表插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
 
IF F_YTD_FLAG = 'YTD' THEN 
	--取YTD金额-用于计算虚化金额
--建临时表
DROP TABLE IF EXISTS DM_BASE_YTD_AMT_TEMP;
CREATE TEMPORARY TABLE DM_BASE_YTD_AMT_TEMP(
 PROD_LIST_CODE			VARCHAR(50),
 PROD_LIST_CN_NAME		VARCHAR(200),
 PERIOD_YEAR			INT,
 PERIOD_ID				INT,
 GROUP_CODE				VARCHAR(50)  ,
 GROUP_CN_NAME			VARCHAR(200) ,
 PARENT_CODE			VARCHAR(50)  ,
 PARENT_CN_NAME			VARCHAR(200) ,
 RMB_COST_AMT			NUMERIC,
 SHIPMENT_QTY			NUMERIC,
 REPLACE_RELATION_NAME	VARCHAR(200),
 REPLACE_RELATION_TYPE	VARCHAR(50),
 RELATION_TYPE			VARCHAR(50),
 CODE_TYPE				VARCHAR(50),
 REGION_CODE			VARCHAR(50)  ,
 REGION_CN_NAME			VARCHAR(200) ,
 REPOFFICE_CODE			VARCHAR(50)  ,
 REPOFFICE_CN_NAME		VARCHAR(200) ,
 BG_CODE				VARCHAR(50)  ,
 BG_CN_NAME         	VARCHAR(200) ,
 OVERSEA_FLAG			VARCHAR(10),    
 SOFTWARE_MARK			VARCHAR(20)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(GROUP_CODE,PROD_LIST_CODE);

--判断取金额
  IF F_COST_TYPE = 'PSP' THEN
  --PSP金额直取
    INSERT INTO DM_BASE_YTD_AMT_TEMP
      (PROD_LIST_CODE,
       PROD_LIST_CN_NAME,
       PERIOD_YEAR,
       PERIOD_ID,
       GROUP_CODE,
       GROUP_CN_NAME,
       PARENT_CODE,
       PARENT_CN_NAME,
       RMB_COST_AMT,
	   SHIPMENT_QTY,
       REPLACE_RELATION_NAME,
       REPLACE_RELATION_TYPE,
       RELATION_TYPE,
       CODE_TYPE,
       REGION_CODE,
       REGION_CN_NAME,
       REPOFFICE_CODE,
       REPOFFICE_CN_NAME,
       BG_CODE,
       BG_CN_NAME,
       OVERSEA_FLAG,
       SOFTWARE_MARK)
      SELECT PROD_LIST_CODE,
             PROD_LIST_CN_NAME,
             PERIOD_YEAR,
             PERIOD_ID,
             GROUP_CODE,
             GROUP_CN_NAME,
             PARENT_CODE,
             PARENT_CN_NAME,
             RMB_COST_AMT,
			 SHIPMENT_QTY,
             REPLACE_RELATION_NAME,
             REPLACE_RELATION_TYPE,
             RELATION_TYPE,
             CODE_TYPE,
             REGION_CODE,
             REGION_CN_NAME,
             REPOFFICE_CODE,
             REPOFFICE_CN_NAME,
             BG_CODE,
             BG_CN_NAME,
             OVERSEA_FLAG,
             SOFTWARE_MARK
        FROM FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_PROD_YTD_REPL_COST_CV_T
       WHERE VERSION_ID = V_VERSION
         AND BASE_PERIOD_ID = V_BASE_PERIOD_ID
         AND GROUP_LEVEL = 'SPART';
		 
  ELSIF F_COST_TYPE = 'STD' THEN
  --STD金额解密落表
    INSERT INTO DM_BASE_YTD_AMT_TEMP
      (PROD_LIST_CODE,
       PROD_LIST_CN_NAME,
       PERIOD_YEAR,
       PERIOD_ID,
       GROUP_CODE,
       GROUP_CN_NAME,
       PARENT_CODE,
       PARENT_CN_NAME,
       RMB_COST_AMT,
	   SHIPMENT_QTY,
       REPLACE_RELATION_NAME,
       REPLACE_RELATION_TYPE,
       RELATION_TYPE,
       CODE_TYPE,
       REGION_CODE,
       REGION_CN_NAME,
       REPOFFICE_CODE,
       REPOFFICE_CN_NAME,
       BG_CODE,
       BG_CN_NAME,
       OVERSEA_FLAG,
       SOFTWARE_MARK)
    SELECT PROD_LIST_CODE,
           PROD_LIST_CN_NAME,
           PERIOD_YEAR,
           PERIOD_ID,
           GROUP_CODE,
           GROUP_CN_NAME,
           PARENT_CODE,
           PARENT_CN_NAME,
           TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,
                                F_KEYSTR,
                                'aes128',
                                'cbc',
                                'sha256')) AS RMB_COST_AMT,
           SHIPMENT_QTY,
		   REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           RELATION_TYPE,
           CODE_TYPE,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
           SOFTWARE_MARK
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_STD_PROD_YTD_REPL_COST_CV_T
     WHERE VERSION_ID = V_VERSION
       AND BASE_PERIOD_ID = V_BASE_PERIOD_ID
       AND GROUP_LEVEL = 'SPART';

  END IF;
  
  END IF;
  
   --写入日志
 V_EXCEPTION_FLAG	:= 4.5;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 4.5,
  F_CAL_LOG_DESC => '基础金额解密插数完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
--金额表落表
  V_SQL:= '
  
  DELETE FROM '||V_TO_AMT_TABLE||' WHERE VERSION_ID = '||V_VERSION||' AND GROUP_LEVEL_TYPE = ''PRE_CUS'' ;
  
  INSERT INTO '||V_TO_AMT_TABLE||'
    (VERSION_ID,
     CUSTOM_ID,
     CUSTOM_CN_NAME,
     PERIOD_YEAR,
     PERIOD_ID,
     BASE_PERIOD_ID,
     PBI_DIM_CODE,
     PBI_DIM_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     GROUP_LEVEL_TYPE,
     PART_QTY,
     RMB_COST_AMT,
     REPLACE_RELATION_NAME,
     REPLACE_RELATION_TYPE,
     RELATION_TYPE,
     CODE_TYPE,
     PARENT_CODE,
     PARENT_CN_NAME,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     BG_CODE,
     BG_CN_NAME,
     OVERSEA_FLAG,
	 SOFTWARE_MARK,
     GRANULARITY_TYPE,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG)
	 WITH BASE_AMT AS (
	SELECT T1.PERIOD_YEAR,
           T1.PERIOD_ID,
		   T2.LV0_PROD_LIST_CODE AS LV0_CODE,
		   T2.LV0_PROD_LIST_CN_NAME AS LV0_NAME,
		   T2.LV1_PROD_LIST_CODE AS LV1_CODE,
		   T2.LV1_PROD_LIST_CN_NAME AS LV1_NAME,
		   T2.LV2_PROD_LIST_CODE AS LV2_CODE,
		   T2.LV2_PROD_LIST_CN_NAME AS LV2_NAME,
		   T2.LV3_PROD_LIST_CODE AS LV3_CODE,
		   T2.LV3_PROD_LIST_CN_NAME AS LV3_NAME,
		   T2.LV4_PROD_LIST_CODE AS LV4_CODE,
		   T2.LV4_PROD_LIST_CN_NAME AS LV4_NAME,
		   T1.GROUP_CODE,
           T1.GROUP_CN_NAME,
           T1.RMB_COST_AMT,
		   T1.SHIPMENT_QTY,
           T1.REPLACE_RELATION_NAME,
           T1.REPLACE_RELATION_TYPE,
           T1.RELATION_TYPE,
           T1.CODE_TYPE,
           T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
           T1.SOFTWARE_MARK
	  FROM '||V_FROM_AMT_TABLE||' T1
      LEFT JOIN (SELECT DISTINCT LV0_PROD_LIST_CODE,
                                LV0_PROD_LIST_CN_NAME,
                                LV1_PROD_LIST_CODE,
                                LV1_PROD_LIST_CN_NAME,
                                LV2_PROD_LIST_CODE,
                                LV2_PROD_LIST_CN_NAME,
                                LV3_PROD_LIST_CODE,
                                LV3_PROD_LIST_CN_NAME,
                                LV4_PROD_LIST_CODE,
                                LV4_PROD_LIST_CN_NAME
                  FROM '||V_FROM_PBI_DIM_TABLE||'
                 WHERE GROUP_LEVEL = ''LV4'') T2
        ON T1.PROD_LIST_CODE = T2.LV4_PROD_LIST_CODE
	 )
	 
--单选LV0+替代关系类型(一对一,一对多,多对一)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           PERIOD_YEAR,
           PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           LV0_CODE AS PBI_DIM_CODE,
           LV0_NAME AS PBI_DIM_CN_NAME,
           REPLACE_RELATION_TYPE AS GROUP_CODE,
           REPLACE_RELATION_TYPE AS GROUP_CN_NAME,
           ''LV0_REPLACE'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(SHIPMENT_QTY) AS PART_QTY,
           SUM(RMB_COST_AMT) AS RMB_COST_AMT,
           '''' AS REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           '''' AS RELATION_TYPE,
           CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM BASE_AMT
	 GROUP BY PERIOD_YEAR,
	          PERIOD_ID,
			  REPLACE_RELATION_TYPE,
			  LV0_CODE,
			  LV0_NAME,
			  CODE_TYPE,
			  REGION_CODE,
	          REGION_CN_NAME,
	          REPOFFICE_CODE,
	          REPOFFICE_CN_NAME,
	          BG_CODE,
	          BG_CN_NAME,
	          OVERSEA_FLAG,
			  SOFTWARE_MARK
			  
  --单选LV0+关系类型(收编,替换)
  UNION ALL 
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           PERIOD_YEAR,
           PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           LV0_CODE AS PBI_DIM_CODE,
           LV0_NAME AS PBI_DIM_CN_NAME,
           RELATION_TYPE AS GROUP_CODE,
           RELATION_TYPE AS GROUP_CN_NAME,
           ''LV0_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(SHIPMENT_QTY) AS PART_QTY,
           SUM(RMB_COST_AMT) AS RMB_COST_AMT,
           '''' AS REPLACE_RELATION_NAME,
           '''' AS REPLACE_RELATION_TYPE,
           RELATION_TYPE,
           CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM BASE_AMT
	 GROUP BY PERIOD_YEAR,
	          PERIOD_ID,
			  LV0_CODE,
			  LV0_NAME,
			  RELATION_TYPE,
			  CODE_TYPE,
			  REGION_CODE,
	          REGION_CN_NAME,
	          REPOFFICE_CODE,
	          REPOFFICE_CN_NAME,
	          BG_CODE,
	          BG_CN_NAME,
	          OVERSEA_FLAG,
			  SOFTWARE_MARK
			  
  -- 混选LV0+替代关系类型及关系类型(一对一&收编,一对一&替换 / 一对多&收编,一对多&替换 / 多对多&收编,多对多&替换)
  UNION ALL 
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           PERIOD_YEAR,
           PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           LV0_CODE AS PBI_DIM_CODE,
           LV0_NAME AS PBI_DIM_CN_NAME,
           REPLACE_RELATION_TYPE||RELATION_TYPE AS GROUP_CODE,
           REPLACE_RELATION_TYPE||RELATION_TYPE AS GROUP_CN_NAME,
           ''LV0_REPLACE_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(SHIPMENT_QTY) AS PART_QTY,
           SUM(RMB_COST_AMT) AS RMB_COST_AMT,
           '''' AS REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           RELATION_TYPE,
           CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM BASE_AMT
	 GROUP BY PERIOD_YEAR,
	          PERIOD_ID,
			  LV0_CODE,
			  LV0_NAME,
			  RELATION_TYPE,
			  REPLACE_RELATION_TYPE,
			  CODE_TYPE,
			  REGION_CODE,
	          REGION_CN_NAME,
	          REPOFFICE_CODE,
	          REPOFFICE_CN_NAME,
	          BG_CODE,
	          BG_CN_NAME,
	          OVERSEA_FLAG,
			  SOFTWARE_MARK
				  
----LV1			  
--单选LV1+替代关系类型(一对一,一对多,多对一)
  UNION ALL
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           PERIOD_YEAR,
           PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           LV1_CODE AS PBI_DIM_CODE,
           LV1_NAME AS PBI_DIM_CN_NAME,
           REPLACE_RELATION_TYPE AS GROUP_CODE,
           REPLACE_RELATION_TYPE AS GROUP_CN_NAME,
           ''LV1_REPLACE'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(SHIPMENT_QTY) AS PART_QTY,
           SUM(RMB_COST_AMT) AS RMB_COST_AMT,
           '''' AS REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           '''' AS RELATION_TYPE,
           CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM BASE_AMT
	 GROUP BY PERIOD_YEAR,
	          PERIOD_ID,
			  REPLACE_RELATION_TYPE,
			  LV1_CODE,
			  LV1_NAME,
			  CODE_TYPE,
			  REGION_CODE,
	          REGION_CN_NAME,
	          REPOFFICE_CODE,
	          REPOFFICE_CN_NAME,
	          BG_CODE,
	          BG_CN_NAME,
	          OVERSEA_FLAG,
			  SOFTWARE_MARK
			  
  --单选LV1+关系类型(收编,替换)
  UNION ALL 
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           PERIOD_YEAR,
           PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           LV1_CODE AS PBI_DIM_CODE,
           LV1_NAME AS PBI_DIM_CN_NAME,
           RELATION_TYPE AS GROUP_CODE,
           RELATION_TYPE AS GROUP_CN_NAME,
           ''LV1_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(SHIPMENT_QTY) AS PART_QTY,
           SUM(RMB_COST_AMT) AS RMB_COST_AMT,
           '''' AS REPLACE_RELATION_NAME,
           '''' AS REPLACE_RELATION_TYPE,
           RELATION_TYPE,
           CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM BASE_AMT
	 GROUP BY PERIOD_YEAR,
	          PERIOD_ID,
			  LV1_CODE,
			  LV1_NAME,
			  RELATION_TYPE,
			  CODE_TYPE,
			  REGION_CODE,
	          REGION_CN_NAME,
	          REPOFFICE_CODE,
	          REPOFFICE_CN_NAME,
	          BG_CODE,
	          BG_CN_NAME,
	          OVERSEA_FLAG,
			  SOFTWARE_MARK
			  
  -- 混选LV1+替代关系类型及关系类型(一对一&收编,一对一&替换 / 一对多&收编,一对多&替换 / 多对多&收编,多对多&替换)
  UNION ALL 
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           PERIOD_YEAR,
           PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           LV1_CODE AS PBI_DIM_CODE,
           LV1_NAME AS PBI_DIM_CN_NAME,
           REPLACE_RELATION_TYPE||RELATION_TYPE AS GROUP_CODE,
           REPLACE_RELATION_TYPE||RELATION_TYPE AS GROUP_CN_NAME,
           ''LV1_REPLACE_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(SHIPMENT_QTY) AS PART_QTY,
           SUM(RMB_COST_AMT) AS RMB_COST_AMT,
           '''' AS REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           RELATION_TYPE,
           CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM BASE_AMT
	 GROUP BY PERIOD_YEAR,
	          PERIOD_ID,
			  LV1_CODE,
			  LV1_NAME,
			  RELATION_TYPE,
			  REPLACE_RELATION_TYPE,
			  CODE_TYPE,
			  REGION_CODE,
	          REGION_CN_NAME,
	          REPOFFICE_CODE,
	          REPOFFICE_CN_NAME,
	          BG_CODE,
	          BG_CN_NAME,
	          OVERSEA_FLAG,
			  SOFTWARE_MARK
			  
----LV2		  
--单选LV2+替代关系类型(一对一,一对多,多对一)
  UNION ALL
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           PERIOD_YEAR,
           PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           LV2_CODE AS PBI_DIM_CODE,
           LV2_NAME AS PBI_DIM_CN_NAME,
           REPLACE_RELATION_TYPE AS GROUP_CODE,
           REPLACE_RELATION_TYPE AS GROUP_CN_NAME,
           ''LV2_REPLACE'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(SHIPMENT_QTY) AS PART_QTY,
           SUM(RMB_COST_AMT) AS RMB_COST_AMT,
           '''' AS REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           '''' AS RELATION_TYPE,
           CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM BASE_AMT
	 GROUP BY PERIOD_YEAR,
	          PERIOD_ID,
			  REPLACE_RELATION_TYPE,
			  LV2_CODE,
			  LV2_NAME,
			  CODE_TYPE,
			  REGION_CODE,
	          REGION_CN_NAME,
	          REPOFFICE_CODE,
	          REPOFFICE_CN_NAME,
	          BG_CODE,
	          BG_CN_NAME,
	          OVERSEA_FLAG,
			  SOFTWARE_MARK
			  
  --单选LV2+关系类型(收编,替换)
  UNION ALL 
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           PERIOD_YEAR,
           PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           LV2_CODE AS PBI_DIM_CODE,
           LV2_NAME AS PBI_DIM_CN_NAME,
           RELATION_TYPE AS GROUP_CODE,
           RELATION_TYPE AS GROUP_CN_NAME,
           ''LV2_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(SHIPMENT_QTY) AS PART_QTY,
           SUM(RMB_COST_AMT) AS RMB_COST_AMT,
           '''' AS REPLACE_RELATION_NAME,
           '''' AS REPLACE_RELATION_TYPE,
           RELATION_TYPE,
           CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM BASE_AMT
	 GROUP BY PERIOD_YEAR,
	          PERIOD_ID,
			  LV2_CODE,
			  LV2_NAME,
			  RELATION_TYPE,
			  CODE_TYPE,
			  REGION_CODE,
	          REGION_CN_NAME,
	          REPOFFICE_CODE,
	          REPOFFICE_CN_NAME,
	          BG_CODE,
	          BG_CN_NAME,
	          OVERSEA_FLAG,
			  SOFTWARE_MARK
			  
  -- 混选LV2+替代关系类型及关系类型(一对一&收编,一对一&替换 / 一对多&收编,一对多&替换 / 多对多&收编,多对多&替换)
  UNION ALL 
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           PERIOD_YEAR,
           PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           LV2_CODE AS PBI_DIM_CODE,
           LV2_NAME AS PBI_DIM_CN_NAME,
           REPLACE_RELATION_TYPE||RELATION_TYPE AS GROUP_CODE,
           REPLACE_RELATION_TYPE||RELATION_TYPE AS GROUP_CN_NAME,
           ''LV2_REPLACE_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(SHIPMENT_QTY) AS PART_QTY,
           SUM(RMB_COST_AMT) AS RMB_COST_AMT,
           '''' AS REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           RELATION_TYPE,
           CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM BASE_AMT
	 GROUP BY PERIOD_YEAR,
	          PERIOD_ID,
			  LV2_CODE,
			  LV2_NAME,
			  RELATION_TYPE,
			  REPLACE_RELATION_TYPE,
			  CODE_TYPE,
			  REGION_CODE,
	          REGION_CN_NAME,
	          REPOFFICE_CODE,
	          REPOFFICE_CN_NAME,
	          BG_CODE,
	          BG_CN_NAME,
	          OVERSEA_FLAG,
			  SOFTWARE_MARK
			  
----LV3		  
--单选LV3+替代关系类型(一对一,一对多,多对一)
  UNION ALL
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           PERIOD_YEAR,
           PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           LV3_CODE AS PBI_DIM_CODE,
           LV3_NAME AS PBI_DIM_CN_NAME,
           REPLACE_RELATION_TYPE AS GROUP_CODE,
           REPLACE_RELATION_TYPE AS GROUP_CN_NAME,
           ''LV3_REPLACE'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(SHIPMENT_QTY) AS PART_QTY,
           SUM(RMB_COST_AMT) AS RMB_COST_AMT,
           '''' AS REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           '''' AS RELATION_TYPE,
           CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM BASE_AMT
	 GROUP BY PERIOD_YEAR,
	          PERIOD_ID,
			  REPLACE_RELATION_TYPE,
			  LV3_CODE,
			  LV3_NAME,
			  CODE_TYPE,
			  REGION_CODE,
	          REGION_CN_NAME,
	          REPOFFICE_CODE,
	          REPOFFICE_CN_NAME,
	          BG_CODE,
	          BG_CN_NAME,
	          OVERSEA_FLAG,
			  SOFTWARE_MARK
			  
  --单选LV3+关系类型(收编,替换)
  UNION ALL 
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           PERIOD_YEAR,
           PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           LV3_CODE AS PBI_DIM_CODE,
           LV3_NAME AS PBI_DIM_CN_NAME,
           RELATION_TYPE AS GROUP_CODE,
           RELATION_TYPE AS GROUP_CN_NAME,
           ''LV3_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(SHIPMENT_QTY) AS PART_QTY,
           SUM(RMB_COST_AMT) AS RMB_COST_AMT,
           '''' AS REPLACE_RELATION_NAME,
           '''' AS REPLACE_RELATION_TYPE,
           RELATION_TYPE,
           CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM BASE_AMT
	 GROUP BY PERIOD_YEAR,
	          PERIOD_ID,
			  LV3_CODE,
			  LV3_NAME,
			  RELATION_TYPE,
			  CODE_TYPE,
			  REGION_CODE,
	          REGION_CN_NAME,
	          REPOFFICE_CODE,
	          REPOFFICE_CN_NAME,
	          BG_CODE,
	          BG_CN_NAME,
	          OVERSEA_FLAG,
			  SOFTWARE_MARK
			  
  -- 混选LV3+替代关系类型及关系类型(一对一&收编,一对一&替换 / 一对多&收编,一对多&替换 / 多对多&收编,多对多&替换)
  UNION ALL 
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           PERIOD_YEAR,
           PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           LV3_CODE AS PBI_DIM_CODE,
           LV3_NAME AS PBI_DIM_CN_NAME,
           REPLACE_RELATION_TYPE||RELATION_TYPE AS GROUP_CODE,
           REPLACE_RELATION_TYPE||RELATION_TYPE AS GROUP_CN_NAME,
           ''LV3_REPLACE_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(SHIPMENT_QTY) AS PART_QTY,
           SUM(RMB_COST_AMT) AS RMB_COST_AMT,
           '''' AS REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           RELATION_TYPE,
           CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM BASE_AMT
	 GROUP BY PERIOD_YEAR,
	          PERIOD_ID,
			  LV3_CODE,
			  LV3_NAME,
			  RELATION_TYPE,
			  REPLACE_RELATION_TYPE,
			  CODE_TYPE,
			  REGION_CODE,
	          REGION_CN_NAME,
	          REPOFFICE_CODE,
	          REPOFFICE_CN_NAME,
	          BG_CODE,
	          BG_CN_NAME,
	          OVERSEA_FLAG,
			  SOFTWARE_MARK
			  
----LV4		  
--单选LV4+替代关系类型(一对一,一对多,多对一)
  UNION ALL
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           PERIOD_YEAR,
           PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           LV4_CODE AS PBI_DIM_CODE,
           LV4_NAME AS PBI_DIM_CN_NAME,
           REPLACE_RELATION_TYPE AS GROUP_CODE,
           REPLACE_RELATION_TYPE AS GROUP_CN_NAME,
           ''LV4_REPLACE'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(SHIPMENT_QTY) AS PART_QTY,
           SUM(RMB_COST_AMT) AS RMB_COST_AMT,
           '''' AS REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           '''' AS RELATION_TYPE,
           CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM BASE_AMT
	 GROUP BY PERIOD_YEAR,
	          PERIOD_ID,
			  REPLACE_RELATION_TYPE,
			  LV4_CODE,
			  LV4_NAME,
			  CODE_TYPE,
			  REGION_CODE,
	          REGION_CN_NAME,
	          REPOFFICE_CODE,
	          REPOFFICE_CN_NAME,
	          BG_CODE,
	          BG_CN_NAME,
	          OVERSEA_FLAG,
			  SOFTWARE_MARK
			  
  --单选LV4+关系类型(收编,替换)
  UNION ALL 
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           PERIOD_YEAR,
           PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           LV4_CODE AS PBI_DIM_CODE,
           LV4_NAME AS PBI_DIM_CN_NAME,
           RELATION_TYPE AS GROUP_CODE,
           RELATION_TYPE AS GROUP_CN_NAME,
           ''LV4_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(SHIPMENT_QTY) AS PART_QTY,
           SUM(RMB_COST_AMT) AS RMB_COST_AMT,
           '''' AS REPLACE_RELATION_NAME,
           '''' AS REPLACE_RELATION_TYPE,
           RELATION_TYPE,
           CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM BASE_AMT
	 GROUP BY PERIOD_YEAR,
	          PERIOD_ID,
			  LV4_CODE,
			  LV4_NAME,
			  RELATION_TYPE,
			  CODE_TYPE,
			  REGION_CODE,
	          REGION_CN_NAME,
	          REPOFFICE_CODE,
	          REPOFFICE_CN_NAME,
	          BG_CODE,
	          BG_CN_NAME,
	          OVERSEA_FLAG,
			  SOFTWARE_MARK
			  
  -- 混选LV4+替代关系类型及关系类型(一对一&收编,一对一&替换 / 一对多&收编,一对多&替换 / 多对多&收编,多对多&替换)
  UNION ALL 
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           PERIOD_YEAR,
           PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           LV4_CODE AS PBI_DIM_CODE,
           LV4_NAME AS PBI_DIM_CN_NAME,
           REPLACE_RELATION_TYPE||RELATION_TYPE AS GROUP_CODE,
           REPLACE_RELATION_TYPE||RELATION_TYPE AS GROUP_CN_NAME,
           ''LV4_REPLACE_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(SHIPMENT_QTY) AS PART_QTY,
           SUM(RMB_COST_AMT) AS RMB_COST_AMT,
           '''' AS REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           RELATION_TYPE,
           CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM BASE_AMT
	 GROUP BY PERIOD_YEAR,
	          PERIOD_ID,
			  LV4_CODE,
			  LV4_NAME,
			  RELATION_TYPE,
			  REPLACE_RELATION_TYPE,
			  CODE_TYPE,
			  REGION_CODE,
	          REGION_CN_NAME,
	          REPOFFICE_CODE,
	          REPOFFICE_CN_NAME,
	          BG_CODE,
	          BG_CN_NAME,
	          OVERSEA_FLAG,
			  SOFTWARE_MARK;
			  ';
			  
  EXECUTE V_SQL;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 5;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 5,
  F_CAL_LOG_DESC => '金额结果表插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
	
--替代关系指数落表
--建表
DROP TABLE IF EXISTS DM_BASE_INDEX_TEMP;
CREATE TEMPORARY TABLE DM_BASE_INDEX_TEMP(
PERIOD_YEAR				INT,
PERIOD_ID				INT,
PROD_LIST_CODE			VARCHAR(50),
PROD_LIST_CN_NAME		VARCHAR(200),
GROUP_CODE				VARCHAR(50),
GROUP_CN_NAME			VARCHAR(200),
GROUP_LEVEL				VARCHAR(50)  ,
COST_INDEX				NUMERIC      ,
PARENT_CODE				VARCHAR(500) ,
PARENT_CN_NAME			VARCHAR(200) ,
REPLACE_RELATION_NAME	VARCHAR(200) ,
REPLACE_RELATION_TYPE	VARCHAR(50)  ,
RELATION_TYPE			VARCHAR(50)  ,
CODE_TYPE				VARCHAR(50)  ,
REGION_CODE				VARCHAR(50)  ,
REGION_CN_NAME			VARCHAR(200) ,
REPOFFICE_CODE			VARCHAR(50)  ,
REPOFFICE_CN_NAME		VARCHAR(200) ,
BG_CODE					VARCHAR(50)  ,
BG_CN_NAME				VARCHAR(200) ,
OVERSEA_FLAG			VARCHAR(10)  ,
SOFTWARE_MARK			VARCHAR(20)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(GROUP_CODE,PROD_LIST_CODE);

--插数
V_SQL:= '
  INSERT INTO DM_BASE_INDEX_TEMP
    (PERIOD_YEAR,
     PERIOD_ID,
     PROD_LIST_CODE,
     PROD_LIST_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_INDEX,
     PARENT_CODE,
     PARENT_CN_NAME,
     REPLACE_RELATION_NAME,
     REPLACE_RELATION_TYPE,
     RELATION_TYPE,
     CODE_TYPE,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     BG_CODE,
     BG_CN_NAME,
     OVERSEA_FLAG,
	 SOFTWARE_MARK)
    SELECT PERIOD_YEAR,
           PERIOD_ID,
           PROD_LIST_CODE,
           PROD_LIST_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           PARENT_CODE,
           PARENT_CN_NAME,
           REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           RELATION_TYPE,
           CODE_TYPE,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK
      FROM '||V_FROM_REPL_IDX_TABLE||'
     WHERE GROUP_LEVEL = ''SPART''
       AND VERSION_ID = '||V_VERSION||'
       AND BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'
    UNION ALL
    SELECT PERIOD_YEAR,
           PERIOD_ID,
           PROD_LIST_CODE,
           PROD_LIST_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           PARENT_CODE,
           PARENT_CN_NAME,
           REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           RELATION_TYPE,
           ''SAME'' AS CODE_TYPE,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK
      FROM '||V_FROM_SAME_IDX_TABLE||'
     WHERE GROUP_LEVEL = ''SPART''
       AND VERSION_ID = '||V_VERSION||'
       AND BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||';
	   ';
  EXECUTE V_SQL;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 6;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 6,
  F_CAL_LOG_DESC => '基础指数表插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
	   
	
  --虚化指数计算
  V_SQL:= '
  
  DELETE FROM '||V_TO_IDX_TABLE||' WHERE VERSION_ID = '||V_VERSION||' AND GROUP_LEVEL_TYPE = ''PRE_CUS'' ;
  
  INSERT INTO '||V_TO_IDX_TABLE||'
    (VERSION_ID,
     CUSTOM_ID,
     CUSTOM_CN_NAME,
     PERIOD_YEAR,
     PERIOD_ID,
     BASE_PERIOD_ID,
     PBI_DIM_CODE,
     PBI_DIM_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     GROUP_LEVEL_TYPE,
     COST_INDEX,
     REPLACE_RELATION_NAME,
     REPLACE_RELATION_TYPE,
     RELATION_TYPE,
     CODE_TYPE,
     PARENT_CODE,
     PARENT_CN_NAME,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     BG_CODE,
     BG_CN_NAME,
     OVERSEA_FLAG,
	 SOFTWARE_MARK,
     GRANULARITY_TYPE,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG)
  --单选LV0+替代关系类型(一对一,一对多,多对多)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           T2.LV0_CODE AS PBI_DIM_CODE,
           T2.LV0_NAME AS PBI_DIM_CN_NAME,
           T1.REPLACE_RELATION_TYPE AS GROUP_CODE,
           T1.REPLACE_RELATION_TYPE AS GROUP_CN_NAME,
           ''LV0_REPLACE'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           '''' AS REPLACE_RELATION_NAME,
           T1.REPLACE_RELATION_TYPE,
           '''' AS RELATION_TYPE,
           T1.CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
           T1.SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_BASE_INDEX_TEMP T1
      LEFT JOIN DM_REPL_WEIGHT_TEMP T2
        ON T1.PROD_LIST_CODE = T2.LV4_CODE
       --AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T2.PARENT_LEVEL = ''LV0_REPLACE''
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND DECODE(T1.CODE_TYPE, ''SAME'', ''NEW'', T1.CODE_TYPE) = T2.CODE_TYPE
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
     WHERE T2.PARENT_LEVEL = ''LV0_REPLACE''
     GROUP BY T1.PERIOD_YEAR,
              T1.PERIOD_ID,
			  T2.LV0_CODE,
			  T2.LV0_NAME,
              T1.REPLACE_RELATION_TYPE,
              T1.CODE_TYPE,
              T1.REGION_CODE,
              T1.REGION_CN_NAME,
              T1.REPOFFICE_CODE,
              T1.REPOFFICE_CN_NAME,
              T1.BG_CODE,
              T1.BG_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.SOFTWARE_MARK
			  
  UNION ALL
  --单选LV0+关系类型(收编,替换)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           T2.LV0_CODE AS PBI_DIM_CODE,
           T2.LV0_NAME AS PBI_DIM_CN_NAME,
           T1.RELATION_TYPE AS GROUP_CODE,
           T1.RELATION_TYPE AS GROUP_CN_NAME,
           ''LV0_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           '''' AS REPLACE_RELATION_NAME,
           '''' AS REPLACE_RELATION_TYPE,
           T1.RELATION_TYPE,
           T1.CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
           T1.SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_BASE_INDEX_TEMP T1
      LEFT JOIN DM_REPL_WEIGHT_TEMP T2
        ON T1.PROD_LIST_CODE = T2.LV4_CODE
       --AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T2.PARENT_LEVEL = ''LV0_RELATION''
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND DECODE(T1.CODE_TYPE, ''SAME'', ''NEW'', T1.CODE_TYPE) = T2.CODE_TYPE
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
     WHERE T2.PARENT_LEVEL = ''LV0_RELATION''
     GROUP BY T1.PERIOD_YEAR,
              T1.PERIOD_ID,
              T1.RELATION_TYPE,
			  T2.LV0_CODE,
			  T2.LV0_NAME,
              T1.CODE_TYPE,
              T1.REGION_CODE,
              T1.REGION_CN_NAME,
              T1.REPOFFICE_CODE,
              T1.REPOFFICE_CN_NAME,
              T1.BG_CODE,
              T1.BG_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.SOFTWARE_MARK
  UNION ALL
  -- 混选LV0+替代关系类型及关系类型(一对一&收编,一对一&替换 / 一对多&收编,一对多&替换 / 多对多&收编,多对多&替换)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           T2.LV0_CODE AS PBI_DIM_CODE,
           T2.LV0_NAME AS PBI_DIM_CN_NAME,
           T1.REPLACE_RELATION_TYPE||T1.RELATION_TYPE AS GROUP_CODE,
           T1.REPLACE_RELATION_TYPE||T1.RELATION_TYPE AS GROUP_CN_NAME,
           ''LV0_REPLACE_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           '''' AS REPLACE_RELATION_NAME,
           T1.REPLACE_RELATION_TYPE,
           T1.RELATION_TYPE,
           T1.CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
           T1.SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_BASE_INDEX_TEMP T1
      LEFT JOIN DM_REPL_WEIGHT_TEMP T2
        ON T1.PROD_LIST_CODE = T2.LV4_CODE
       --AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T2.PARENT_LEVEL = ''LV0_REPLACE_RELATION''
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND DECODE(T1.CODE_TYPE, ''SAME'', ''NEW'', T1.CODE_TYPE) = T2.CODE_TYPE
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
     WHERE T2.PARENT_LEVEL = ''LV0_REPLACE_RELATION''
     GROUP BY T1.PERIOD_YEAR,
              T1.PERIOD_ID,
			  T2.LV0_CODE,
			  T2.LV0_NAME,
              T1.REPLACE_RELATION_TYPE,
			  T1.RELATION_TYPE,
              T1.CODE_TYPE,
              T1.REGION_CODE,
              T1.REGION_CN_NAME,
              T1.REPOFFICE_CODE,
              T1.REPOFFICE_CN_NAME,
              T1.BG_CODE,
              T1.BG_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.SOFTWARE_MARK
	
  --LV1
  UNION ALL 
  --单选LV1+替代关系类型(一对一,一对多,多对多)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           T2.LV1_CODE AS PBI_DIM_CODE,
           T2.LV1_NAME AS PBI_DIM_CN_NAME,
           T1.REPLACE_RELATION_TYPE AS GROUP_CODE,
           T1.REPLACE_RELATION_TYPE AS GROUP_CN_NAME,
           ''LV1_REPLACE'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           '''' AS REPLACE_RELATION_NAME,
           T1.REPLACE_RELATION_TYPE,
           '''' AS RELATION_TYPE,
           T1.CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
           T1.SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_BASE_INDEX_TEMP T1
      LEFT JOIN DM_REPL_WEIGHT_TEMP T2
        ON T1.PROD_LIST_CODE = T2.LV4_CODE
       --AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T2.PARENT_LEVEL = ''LV1_REPLACE''
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND DECODE(T1.CODE_TYPE, ''SAME'', ''NEW'', T1.CODE_TYPE) = T2.CODE_TYPE
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
     WHERE T2.PARENT_LEVEL = ''LV1_REPLACE''
     GROUP BY T1.PERIOD_YEAR,
              T1.PERIOD_ID,
			  T2.LV1_CODE,
			  T2.LV1_NAME,
              T1.REPLACE_RELATION_TYPE,
              T1.CODE_TYPE,
              T1.REGION_CODE,
              T1.REGION_CN_NAME,
              T1.REPOFFICE_CODE,
              T1.REPOFFICE_CN_NAME,
              T1.BG_CODE,
              T1.BG_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.SOFTWARE_MARK
			  
  UNION ALL
  --单选LV1+关系类型(收编,替换)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           T2.LV1_CODE AS PBI_DIM_CODE,
           T2.LV1_NAME AS PBI_DIM_CN_NAME,
           T1.RELATION_TYPE AS GROUP_CODE,
           T1.RELATION_TYPE AS GROUP_CN_NAME,
           ''LV1_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           '''' AS REPLACE_RELATION_NAME,
           '''' AS REPLACE_RELATION_TYPE,
           T1.RELATION_TYPE,
           T1.CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
           T1.SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_BASE_INDEX_TEMP T1
      LEFT JOIN DM_REPL_WEIGHT_TEMP T2
        ON T1.PROD_LIST_CODE = T2.LV4_CODE
       --AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T2.PARENT_LEVEL = ''LV1_RELATION''
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND DECODE(T1.CODE_TYPE, ''SAME'', ''NEW'', T1.CODE_TYPE) = T2.CODE_TYPE
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
     WHERE T2.PARENT_LEVEL = ''LV1_RELATION''
     GROUP BY T1.PERIOD_YEAR,
              T1.PERIOD_ID,
              T1.RELATION_TYPE,
			  T2.LV1_CODE,
			  T2.LV1_NAME,
              T1.CODE_TYPE,
              T1.REGION_CODE,
              T1.REGION_CN_NAME,
              T1.REPOFFICE_CODE,
              T1.REPOFFICE_CN_NAME,
              T1.BG_CODE,
              T1.BG_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.SOFTWARE_MARK
  UNION ALL
  -- 混选LV1+替代关系类型及关系类型(一对一&收编,一对一&替换 / 一对多&收编,一对多&替换 / 多对多&收编,多对多&替换)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           T2.LV1_CODE AS PBI_DIM_CODE,
           T2.LV1_NAME AS PBI_DIM_CN_NAME,
           T1.REPLACE_RELATION_TYPE||T1.RELATION_TYPE AS GROUP_CODE,
           T1.REPLACE_RELATION_TYPE||T1.RELATION_TYPE AS GROUP_CN_NAME,
           ''LV1_REPLACE_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           '''' AS REPLACE_RELATION_NAME,
           T1.REPLACE_RELATION_TYPE,
           T1.RELATION_TYPE,
           T1.CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
           T1.SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_BASE_INDEX_TEMP T1
      LEFT JOIN DM_REPL_WEIGHT_TEMP T2
        ON T1.PROD_LIST_CODE = T2.LV4_CODE
       --AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T2.PARENT_LEVEL = ''LV1_REPLACE_RELATION''
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND DECODE(T1.CODE_TYPE, ''SAME'', ''NEW'', T1.CODE_TYPE) = T2.CODE_TYPE
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
     WHERE T2.PARENT_LEVEL = ''LV1_REPLACE_RELATION''
     GROUP BY T1.PERIOD_YEAR,
              T1.PERIOD_ID,
			  T2.LV1_CODE,
			  T2.LV1_NAME,
              T1.REPLACE_RELATION_TYPE,
			  T1.RELATION_TYPE,
              T1.CODE_TYPE,
              T1.REGION_CODE,
              T1.REGION_CN_NAME,
              T1.REPOFFICE_CODE,
              T1.REPOFFICE_CN_NAME,
              T1.BG_CODE,
              T1.BG_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.SOFTWARE_MARK
			  
  --LV2
  UNION ALL 
  --单选LV2+替代关系类型(一对一,一对多,多对多)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           T2.LV2_CODE AS PBI_DIM_CODE,
           T2.LV2_NAME AS PBI_DIM_CN_NAME,
           T1.REPLACE_RELATION_TYPE AS GROUP_CODE,
           T1.REPLACE_RELATION_TYPE AS GROUP_CN_NAME,
           ''LV2_REPLACE'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           '''' AS REPLACE_RELATION_NAME,
           T1.REPLACE_RELATION_TYPE,
           '''' AS RELATION_TYPE,
           T1.CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
           T1.SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_BASE_INDEX_TEMP T1
      LEFT JOIN DM_REPL_WEIGHT_TEMP T2
        ON T1.PROD_LIST_CODE = T2.LV4_CODE
       --AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T2.PARENT_LEVEL = ''LV2_REPLACE''
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND DECODE(T1.CODE_TYPE, ''SAME'', ''NEW'', T1.CODE_TYPE) = T2.CODE_TYPE
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
     WHERE T2.PARENT_LEVEL = ''LV2_REPLACE''
     GROUP BY T1.PERIOD_YEAR,
              T1.PERIOD_ID,
			  T2.LV2_CODE,
			  T2.LV2_NAME,
              T1.REPLACE_RELATION_TYPE,
              T1.CODE_TYPE,
              T1.REGION_CODE,
              T1.REGION_CN_NAME,
              T1.REPOFFICE_CODE,
              T1.REPOFFICE_CN_NAME,
              T1.BG_CODE,
              T1.BG_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.SOFTWARE_MARK
			  
  UNION ALL
  --单选LV2+关系类型(收编,替换)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           T2.LV2_CODE AS PBI_DIM_CODE,
           T2.LV2_NAME AS PBI_DIM_CN_NAME,
           T1.RELATION_TYPE AS GROUP_CODE,
           T1.RELATION_TYPE AS GROUP_CN_NAME,
           ''LV2_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           '''' AS REPLACE_RELATION_NAME,
           '''' AS REPLACE_RELATION_TYPE,
           T1.RELATION_TYPE,
           T1.CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
           T1.SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_BASE_INDEX_TEMP T1
      LEFT JOIN DM_REPL_WEIGHT_TEMP T2
        ON T1.PROD_LIST_CODE = T2.LV4_CODE
       --AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T2.PARENT_LEVEL = ''LV2_RELATION''
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND DECODE(T1.CODE_TYPE, ''SAME'', ''NEW'', T1.CODE_TYPE) = T2.CODE_TYPE
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
     WHERE T2.PARENT_LEVEL = ''LV2_RELATION''
     GROUP BY T1.PERIOD_YEAR,
              T1.PERIOD_ID,
              T1.RELATION_TYPE,
			  T2.LV2_CODE,
			  T2.LV2_NAME,
              T1.CODE_TYPE,
              T1.REGION_CODE,
              T1.REGION_CN_NAME,
              T1.REPOFFICE_CODE,
              T1.REPOFFICE_CN_NAME,
              T1.BG_CODE,
              T1.BG_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.SOFTWARE_MARK
  UNION ALL
  -- 混选LV2+替代关系类型及关系类型(一对一&收编,一对一&替换 / 一对多&收编,一对多&替换 / 多对多&收编,多对多&替换)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           T2.LV2_CODE AS PBI_DIM_CODE,
           T2.LV2_NAME AS PBI_DIM_CN_NAME,
           T1.REPLACE_RELATION_TYPE||T1.RELATION_TYPE AS GROUP_CODE,
           T1.REPLACE_RELATION_TYPE||T1.RELATION_TYPE AS GROUP_CN_NAME,
           ''LV2_REPLACE_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           '''' AS REPLACE_RELATION_NAME,
           T1.REPLACE_RELATION_TYPE,
           T1.RELATION_TYPE,
           T1.CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
           T1.SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_BASE_INDEX_TEMP T1
      LEFT JOIN DM_REPL_WEIGHT_TEMP T2
        ON T1.PROD_LIST_CODE = T2.LV4_CODE
       --AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T2.PARENT_LEVEL = ''LV2_REPLACE_RELATION''
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND DECODE(T1.CODE_TYPE, ''SAME'', ''NEW'', T1.CODE_TYPE) = T2.CODE_TYPE
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
     WHERE T2.PARENT_LEVEL = ''LV2_REPLACE_RELATION''
     GROUP BY T1.PERIOD_YEAR,
              T1.PERIOD_ID,
			  T2.LV2_CODE,
			  T2.LV2_NAME,
              T1.REPLACE_RELATION_TYPE,
			  T1.RELATION_TYPE,
              T1.CODE_TYPE,
              T1.REGION_CODE,
              T1.REGION_CN_NAME,
              T1.REPOFFICE_CODE,
              T1.REPOFFICE_CN_NAME,
              T1.BG_CODE,
              T1.BG_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.SOFTWARE_MARK
			  
  --LV3
  UNION ALL 
  --单选LV3+替代关系类型(一对一,一对多,多对多)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           T2.LV3_CODE AS PBI_DIM_CODE,
           T2.LV3_NAME AS PBI_DIM_CN_NAME,
           T1.REPLACE_RELATION_TYPE AS GROUP_CODE,
           T1.REPLACE_RELATION_TYPE AS GROUP_CN_NAME,
           ''LV3_REPLACE'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           '''' AS REPLACE_RELATION_NAME,
           T1.REPLACE_RELATION_TYPE,
           '''' AS RELATION_TYPE,
           T1.CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
           T1.SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_BASE_INDEX_TEMP T1
      LEFT JOIN DM_REPL_WEIGHT_TEMP T2
        ON T1.PROD_LIST_CODE = T2.LV4_CODE
       --AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T2.PARENT_LEVEL = ''LV3_REPLACE''
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND DECODE(T1.CODE_TYPE, ''SAME'', ''NEW'', T1.CODE_TYPE) = T2.CODE_TYPE
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
     WHERE T2.PARENT_LEVEL = ''LV3_REPLACE''
     GROUP BY T1.PERIOD_YEAR,
              T1.PERIOD_ID,
			  T2.LV3_CODE,
			  T2.LV3_NAME,
              T1.REPLACE_RELATION_TYPE,
              T1.CODE_TYPE,
              T1.REGION_CODE,
              T1.REGION_CN_NAME,
              T1.REPOFFICE_CODE,
              T1.REPOFFICE_CN_NAME,
              T1.BG_CODE,
              T1.BG_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.SOFTWARE_MARK
			  
  UNION ALL
  --单选LV3+关系类型(收编,替换)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           T2.LV3_CODE AS PBI_DIM_CODE,
           T2.LV3_NAME AS PBI_DIM_CN_NAME,
           T1.RELATION_TYPE AS GROUP_CODE,
           T1.RELATION_TYPE AS GROUP_CN_NAME,
           ''LV3_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           '''' AS REPLACE_RELATION_NAME,
           '''' AS REPLACE_RELATION_TYPE,
           T1.RELATION_TYPE,
           T1.CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
           T1.SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_BASE_INDEX_TEMP T1
      LEFT JOIN DM_REPL_WEIGHT_TEMP T2
        ON T1.PROD_LIST_CODE = T2.LV4_CODE
       --AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T2.PARENT_LEVEL = ''LV3_RELATION''
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND DECODE(T1.CODE_TYPE, ''SAME'', ''NEW'', T1.CODE_TYPE) = T2.CODE_TYPE
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
     WHERE T2.PARENT_LEVEL = ''LV3_RELATION''
     GROUP BY T1.PERIOD_YEAR,
              T1.PERIOD_ID,
              T1.RELATION_TYPE,
			  T2.LV3_CODE,
			  T2.LV3_NAME,
              T1.CODE_TYPE,
              T1.REGION_CODE,
              T1.REGION_CN_NAME,
              T1.REPOFFICE_CODE,
              T1.REPOFFICE_CN_NAME,
              T1.BG_CODE,
              T1.BG_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.SOFTWARE_MARK
  UNION ALL
  -- 混选LV3+替代关系类型及关系类型(一对一&收编,一对一&替换 / 一对多&收编,一对多&替换 / 多对多&收编,多对多&替换)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           T2.LV3_CODE AS PBI_DIM_CODE,
           T2.LV3_NAME AS PBI_DIM_CN_NAME,
           T1.REPLACE_RELATION_TYPE||T1.RELATION_TYPE AS GROUP_CODE,
           T1.REPLACE_RELATION_TYPE||T1.RELATION_TYPE AS GROUP_CN_NAME,
           ''LV3_REPLACE_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           '''' AS REPLACE_RELATION_NAME,
           T1.REPLACE_RELATION_TYPE,
           T1.RELATION_TYPE,
           T1.CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
           T1.SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_BASE_INDEX_TEMP T1
      LEFT JOIN DM_REPL_WEIGHT_TEMP T2
        ON T1.PROD_LIST_CODE = T2.LV4_CODE
       --AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T2.PARENT_LEVEL = ''LV3_REPLACE_RELATION''
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND DECODE(T1.CODE_TYPE, ''SAME'', ''NEW'', T1.CODE_TYPE) = T2.CODE_TYPE
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
     WHERE T2.PARENT_LEVEL = ''LV3_REPLACE_RELATION''
     GROUP BY T1.PERIOD_YEAR,
              T1.PERIOD_ID,
			  T2.LV3_CODE,
			  T2.LV3_NAME,
              T1.REPLACE_RELATION_TYPE,
			  T1.RELATION_TYPE,
              T1.CODE_TYPE,
              T1.REGION_CODE,
              T1.REGION_CN_NAME,
              T1.REPOFFICE_CODE,
              T1.REPOFFICE_CN_NAME,
              T1.BG_CODE,
              T1.BG_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.SOFTWARE_MARK
			  
  --LV4
  UNION ALL 
  --单选LV4+替代关系类型(一对一,一对多,多对多)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           T2.LV4_CODE AS PBI_DIM_CODE,
           T2.LV4_NAME AS PBI_DIM_CN_NAME,
           T1.REPLACE_RELATION_TYPE AS GROUP_CODE,
           T1.REPLACE_RELATION_TYPE AS GROUP_CN_NAME,
           ''LV4_REPLACE'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           '''' AS REPLACE_RELATION_NAME,
           T1.REPLACE_RELATION_TYPE,
           '''' AS RELATION_TYPE,
           T1.CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
           T1.SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_BASE_INDEX_TEMP T1
      LEFT JOIN DM_REPL_WEIGHT_TEMP T2
        ON T1.PROD_LIST_CODE = T2.LV4_CODE
       --AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T2.PARENT_LEVEL = ''LV4_REPLACE''
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND DECODE(T1.CODE_TYPE, ''SAME'', ''NEW'', T1.CODE_TYPE) = T2.CODE_TYPE
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
     WHERE T2.PARENT_LEVEL = ''LV4_REPLACE''
     GROUP BY T1.PERIOD_YEAR,
              T1.PERIOD_ID,
			  T2.LV4_CODE,
			  T2.LV4_NAME,
              T1.REPLACE_RELATION_TYPE,
              T1.CODE_TYPE,
              T1.REGION_CODE,
              T1.REGION_CN_NAME,
              T1.REPOFFICE_CODE,
              T1.REPOFFICE_CN_NAME,
              T1.BG_CODE,
              T1.BG_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.SOFTWARE_MARK
			  
  UNION ALL
  --单选LV4+关系类型(收编,替换)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           T2.LV4_CODE AS PBI_DIM_CODE,
           T2.LV4_NAME AS PBI_DIM_CN_NAME,
           T1.RELATION_TYPE AS GROUP_CODE,
           T1.RELATION_TYPE AS GROUP_CN_NAME,
           ''LV4_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           '''' AS REPLACE_RELATION_NAME,
           '''' AS REPLACE_RELATION_TYPE,
           T1.RELATION_TYPE,
           T1.CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
           T1.SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_BASE_INDEX_TEMP T1
      LEFT JOIN DM_REPL_WEIGHT_TEMP T2
        ON T1.PROD_LIST_CODE = T2.LV4_CODE
       --AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T2.PARENT_LEVEL = ''LV4_RELATION''
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND DECODE(T1.CODE_TYPE, ''SAME'', ''NEW'', T1.CODE_TYPE) = T2.CODE_TYPE
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
     WHERE T2.PARENT_LEVEL = ''LV4_RELATION''
     GROUP BY T1.PERIOD_YEAR,
              T1.PERIOD_ID,
              T1.RELATION_TYPE,
			  T2.LV4_CODE,
			  T2.LV4_NAME,
              T1.CODE_TYPE,
              T1.REGION_CODE,
              T1.REGION_CN_NAME,
              T1.REPOFFICE_CODE,
              T1.REPOFFICE_CN_NAME,
              T1.BG_CODE,
              T1.BG_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.SOFTWARE_MARK
  UNION ALL
  -- 混选LV4+替代关系类型及关系类型(一对一&收编,一对一&替换 / 一对多&收编,一对多&替换 / 多对多&收编,多对多&替换)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''' AS CUSTOM_ID,
           '''' AS CUSTOM_CN_NAME,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           T2.LV4_CODE AS PBI_DIM_CODE,
           T2.LV4_NAME AS PBI_DIM_CN_NAME,
           T1.REPLACE_RELATION_TYPE||T1.RELATION_TYPE AS GROUP_CODE,
           T1.REPLACE_RELATION_TYPE||T1.RELATION_TYPE AS GROUP_CN_NAME,
           ''LV4_REPLACE_RELATION'' AS GROUP_LEVEL,
           ''PRE_CUS'' AS GROUP_LEVEL_TYPE,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           '''' AS REPLACE_RELATION_NAME,
           T1.REPLACE_RELATION_TYPE,
           T1.RELATION_TYPE,
           T1.CODE_TYPE,
           '''' AS PARENT_CODE,
           '''' AS PARENT_CN_NAME,
           T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
           T1.SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_BASE_INDEX_TEMP T1
      LEFT JOIN DM_REPL_WEIGHT_TEMP T2
        ON T1.PROD_LIST_CODE = T2.LV4_CODE
       --AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T2.PARENT_LEVEL = ''LV4_REPLACE_RELATION''
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND DECODE(T1.CODE_TYPE, ''SAME'', ''NEW'', T1.CODE_TYPE) = T2.CODE_TYPE
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
     WHERE T2.PARENT_LEVEL = ''LV4_REPLACE_RELATION''
     GROUP BY T1.PERIOD_YEAR,
              T1.PERIOD_ID,
			  T2.LV4_CODE,
			  T2.LV4_NAME,
              T1.REPLACE_RELATION_TYPE,
			  T1.RELATION_TYPE,
              T1.CODE_TYPE,
              T1.REGION_CODE,
              T1.REGION_CN_NAME,
              T1.REPOFFICE_CODE,
              T1.REPOFFICE_CN_NAME,
              T1.BG_CODE,
              T1.BG_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.SOFTWARE_MARK;
  
  ';
  EXECUTE V_SQL;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 7;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 7,
  F_CAL_LOG_DESC => '指数结果表插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
     PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_STEP_NUM => V_EXCEPTION_FLAG,
    F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

 END; 
$$
/

