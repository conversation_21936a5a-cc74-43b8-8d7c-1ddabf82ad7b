-- Name: f_dm_fcst_ict_cost_gap_detail_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_ict_cost_gap_detail_t(f_granularity_type character varying, f_view_flag character varying, f_period_year integer, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

  /*
  创建人  ：tangqin
  背景描述：成本差异明细
  参数描述：x_result_status ：是否成功
  事例    ：select FIN_DM_OPT_FOI.f_dm_fcst_ict_cost_gap_detail_t('IRB')
  事例    ：select FIN_DM_OPT_FOI.f_dm_fcst_ict_cost_gap_detail_t('INDUS')
  事例    ：select FIN_DM_OPT_FOI.f_dm_fcst_ict_cost_gap_detail_t('PROD')
  */


DECLARE

    V_SP_NAME          VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FCST_ICT_COST_GAP_DETAIL_T';
    V_VERSION          VARCHAR(10);
    V_STEP_NUM         BIGINT       := 0; --步骤号
    V_FROM_TABLE1      VARCHAR(200);
    V_FROM_TABLE2      VARCHAR(200);
	V_DIMENSION_TABLE1      VARCHAR(200);
    V_DIMENSION_TABLE2      VARCHAR(200);
    V_TO_TABLE         VARCHAR(200);
    V_GRANULARITY_TYPE VARCHAR(200) := f_granularity_type;
    V_SQL              TEXT;
	V_RAW_PARA		   TEXT;
    V_PROD_PARA        TEXT;
    V_INSERT_JOIN_STD  TEXT;
    V_INSERT_psp_only  TEXT;
    V_INSERT_STD       TEXT;
    V_INSERT_ALL_TMP   TEXT;
    V_INSERT_STD_T5     TEXT;
    V_INSERT_STD_T6     TEXT;
    V_COST_TYPE         TEXT := 'PSP_STD';
    V_PROD_last         TEXT ;

	
      -- 游标指针
/*  v_cursor SYS_REFCURSOR;

    --游标定义
   CURSOR C_IRB IS
    SELECT DISTINCT PERIOD_ID
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_IRB_MON_COST_AMT_T order by period_id ;

     CURSOR C_INDUS IS
    SELECT DISTINCT PERIOD_ID
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_INDUS_MON_COST_AMT_T order by period_id ;

     CURSOR C_PROD IS
    SELECT DISTINCT PERIOD_ID
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_PROD_MON_COST_AMT_T order by period_id ;*/


BEGIN
    x_result_status = 'SUCCESS';

    --0.开始日志
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
            ( F_SP_NAME => V_SP_NAME ,
              F_STEP_NUM => V_STEP_NUM ,
              F_CAL_LOG_DESC => V_SP_NAME || '开始执行' );

    /*取版本号-每次只更新最新一笔的版本号*/
    SELECT
        VERSION_ID
    INTO V_VERSION
    FROM
        FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T --版本表
    WHERE
          DEL_FLAG = 'N'
      AND STATUS = 1
      AND UPPER( DATA_TYPE ) = 'MONTH'
    ORDER BY
        LAST_UPDATE_DATE DESC
    LIMIT 1;

    /*成本类型判断*/

	
    IF UPPER( f_granularity_type ) = 'IRB' THEN
        V_FROM_TABLE1 := '  FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_IRB_MON_COST_AMT_T ';
        V_FROM_TABLE2 := '  FIN_DM_OPT_FOI.DM_FCST_ICT_STD_IRB_MON_COST_AMT_T ';
        V_TO_TABLE := '  FIN_DM_OPT_FOI.DM_FCST_ICT_IRB_COST_GAP_DETAIL_T ';
    ELSEIF UPPER( f_granularity_type ) = 'INDUS' THEN
        V_FROM_TABLE1 := '  FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_INDUS_MON_COST_AMT_T ';
        V_FROM_TABLE2 := '  FIN_DM_OPT_FOI.DM_FCST_ICT_STD_INDUS_MON_COST_AMT_T ';
        V_TO_TABLE := '  FIN_DM_OPT_FOI.DM_FCST_ICT_INDUS_COST_GAP_DETAIL_T ';
    ELSEIF UPPER( f_granularity_type ) = 'PROD' THEN
        V_FROM_TABLE1 := ' FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_PROD_MON_COST_AMT_T ';
        V_FROM_TABLE2 := ' FIN_DM_OPT_FOI.DM_FCST_ICT_STD_PROD_MON_COST_AMT_T ';
        V_TO_TABLE := ' FIN_DM_OPT_FOI.DM_FCST_ICT_PROD_COST_GAP_DETAIL_T ';
    END IF;

	
	
    --判断PBI维度选择PBI字段
    IF upper(F_GRANULARITY_TYPE) = 'IRB' THEN
		V_RAW_PARA := ' PROD_RND_TEAM_CODE ,PROD_RD_TEAM_CN_NAME,';
        V_PROD_PARA := ' T1.PROD_RND_TEAM_CODE ,T1.PROD_RD_TEAM_CN_NAME, ';
        V_INSERT_JOIN_STD := '  AND  NVL(T2.PROD_RND_TEAM_CODE,1) =  NVL(T1.PROD_RND_TEAM_CODE,1)
                                 AND  NVL(T2.PROD_RD_TEAM_CN_NAME,1)  =  NVL(T1.PROD_RD_TEAM_CN_NAME,1) ';
        V_INSERT_PSP_ONLY := ' PSP_ONLY.PROD_RND_TEAM_CODE,
                               PSP_ONLY.PROD_RD_TEAM_CN_NAME,';
        V_INSERT_STD := '  T3.PROD_RND_TEAM_CODE,
                           T3.PROD_RD_TEAM_CN_NAME,';
        V_INSERT_ALL_TMP := '  T4.PROD_RND_TEAM_CODE,
                                T4.PROD_RD_TEAM_CN_NAME,';
        V_INSERT_STD_T5 := ' AND  NVL(t4.prod_rnd_team_code,1)  =  NVL(T5.prod_rnd_team_code,1) ';
        V_INSERT_STD_T6 := ' AND  NVL(t4.prod_rnd_team_code ,1) =  NVL(T6.prod_rnd_team_code,1) ';
        V_PROD_last := ' T1.PROD_RND_TEAM_CODE  as PROD_RND_TEAM_CODE ,T1.PROD_RD_TEAM_CN_NAME as PROD_RD_TEAM_CN_NAME, ';


    ELSIF upper(F_GRANULARITY_TYPE) = 'INDUS' THEN
		V_RAW_PARA := ' INDUSTRY_CATG_CODE ,INDUSTRY_CATG_CN_NAME,';
        V_PROD_PARA := ' T1.INDUSTRY_CATG_CODE ,T1.INDUSTRY_CATG_CN_NAME, ';
        V_INSERT_JOIN_STD := '  AND  NVL(T2.INDUSTRY_CATG_CODE ,1) =  NVL(T1.INDUSTRY_CATG_CODE,1)
                                AND  NVL(T2.INDUSTRY_CATG_CN_NAME ,1) =  NVL(T1.INDUSTRY_CATG_CN_NAME,1) ';
        V_INSERT_PSP_ONLY := ' PSP_ONLY.INDUSTRY_CATG_CODE,
                               PSP_ONLY.INDUSTRY_CATG_CN_NAME,';
        V_INSERT_STD := '  T3.INDUSTRY_CATG_CODE,
                           T3.INDUSTRY_CATG_CN_NAME,';
        V_INSERT_ALL_TMP := '  T4.INDUSTRY_CATG_CODE,
                              T4.INDUSTRY_CATG_CN_NAME,';
        V_INSERT_STD_T5 := '  AND  NVL(t4.INDUSTRY_CATG_CODE,1)  =  NVL(T5.INDUSTRY_CATG_CODE,1) ';
        V_INSERT_STD_T6 := '  AND  NVL(t4.INDUSTRY_CATG_CODE,1)  =  NVL(T6.INDUSTRY_CATG_CODE,1) ';
                V_PROD_last := ' T1.INDUSTRY_CATG_CODE  as PROD_RND_TEAM_CODE ,T1.INDUSTRY_CATG_CN_NAME as PROD_RD_TEAM_CN_NAME, ';



    ELSIF upper(F_GRANULARITY_TYPE) = 'PROD' THEN
		V_RAW_PARA := ' PROD_LIST_CODE ,PROD_LIST_CN_NAME,';
        V_PROD_PARA := ' T1.PROD_LIST_CODE ,T1.PROD_LIST_CN_NAME, ';
        V_INSERT_JOIN_STD := '  AND  NVL(T2.PROD_LIST_CODE,1) =  NVL(T1.PROD_LIST_CODE,1)
                                AND  NVL(T2.PROD_LIST_CN_NAME ,1) =  NVL(T1.PROD_LIST_CN_NAME,1) ';
        V_INSERT_PSP_ONLY := ' PSP_ONLY.PROD_LIST_CODE,
                               PSP_ONLY.PROD_LIST_CN_NAME,';
        V_INSERT_STD := '  T3.PROD_LIST_CODE,
                           T3.PROD_LIST_CN_NAME,';
        V_INSERT_ALL_TMP := '  T4.PROD_LIST_CODE,
                               T4.PROD_LIST_CN_NAME,';
        V_INSERT_STD_T5 := '  AND  NVL(t4.PROD_LIST_CODE,1) =  NVL(T5.PROD_LIST_CODE,1) ';
        V_INSERT_STD_T6 := '  AND  NVL(t4.PROD_LIST_CODE ,1) =  NVL(T6.PROD_LIST_CODE,1) ';
                        V_PROD_last := ' T1.PROD_LIST_CODE  as PROD_RND_TEAM_CODE ,T1.PROD_LIST_CN_NAME as PROD_RD_TEAM_CN_NAME, ';


    ELSE
        NULL ;
    END IF;


    V_SQL := 'DELETE FROM   ' ||V_TO_TABLE ||' WHERE PERIOD_YEAR = '||F_PERIOD_YEAR||' AND VIEW_FLAG = '''||F_VIEW_FLAG||''';';

     EXECUTE IMMEDIATE V_SQL;



        --1、删除最新版本数据
    V_STEP_NUM := V_STEP_NUM +1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
            ( F_SP_NAME => V_SP_NAME ,
              F_STEP_NUM => V_STEP_NUM ,
              F_CAL_LOG_DESC =>  '删除目标表数据' );



			  
			  
	
--创建临时表
            DROP TABLE IF EXISTS all_tmp;
            CREATE TEMPORARY TABLE all_tmp (
				VERSION_ID BIGINT,
                PERIOD_ID NUMERIC,
				PERIOD_YEAR NUMERIC,
				REGION_CODE CHARACTER VARYING(50),
				REGION_CN_NAME CHARACTER VARYING(200),
				REGION_EN_NAME CHARACTER VARYING(200),
				REPOFFICE_CODE CHARACTER VARYING(50),
				REPOFFICE_CN_NAME CHARACTER VARYING(200),
				REPOFFICE_EN_NAME CHARACTER VARYING(200),
				BG_CODE CHARACTER VARYING(50),
				BG_CN_NAME CHARACTER VARYING(200),
				PROD_LIST_CODE CHARACTER VARYING(50),
				PROD_LIST_CN_NAME CHARACTER VARYING(200),
				PROD_RND_TEAM_CODE CHARACTER VARYING(50),
				PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
				INDUSTRY_CATG_CODE CHARACTER VARYING(50),
				INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
				DIMENSION_CODE CHARACTER VARYING(500),
				DIMENSION_CN_NAME CHARACTER VARYING(200),
				DIMENSION_SUBCATEGORY_CODE CHARACTER VARYING(500),
				DIMENSION_SUBCATEGORY_CN_NAME CHARACTER VARYING(200),
				DIMENSION_SUB_DETAIL_CODE CHARACTER VARYING(500),
				DIMENSION_SUB_DETAIL_CN_NAME CHARACTER VARYING(200),
				GROUP_CODE CHARACTER VARYING(188),
				GROUP_CN_NAME CHARACTER VARYING(188),
				GROUP_LEVEL CHARACTER VARYING(50),
				PARENT_CODE CHARACTER VARYING(188),
				PARENT_CN_NAME CHARACTER VARYING(188),
				PARENT_LEVEL CHARACTER VARYING(50),
				del_flag CHARACTER VARYING(10),
				OVERSEA_FLAG CHARACTER VARYING(1),
				MAIN_FLAG VARCHAR(1),
				CODE_ATTRIBUTES VARCHAR(50),
				VIEW_FLAG VARCHAR(10),
				ONLY_SPART_FLAG VARCHAR(1),
				SOFTWARE_MARK VARCHAR(50)
            )
            ON COMMIT PRESERVE ROWS
            DISTRIBUTE BY HASH(PERIOD_ID,GROUP_CODE);
	
				RAISE notice'临时表创建完成';  
			  
		V_SQL := ' 
		INSERT INTO ALL_TMP (
			version_id,
             period_year,
             period_id, '
             || V_RAW_PARA || '

             dimension_code,
             dimension_cn_name,
             dimension_subcategory_code,
             dimension_subcategory_cn_name,
             dimension_sub_detail_code,
             dimension_sub_detail_cn_name,
             group_code,
             group_cn_name,
             group_level,
             parent_code,
             parent_cn_name,
             main_flag,
             view_flag,
             region_code,
             region_cn_name,
             repoffice_code,
             repoffice_cn_name,
             bg_code,
             bg_cn_name,
             oversea_flag,
             del_flag,
             code_attributes,
			 SOFTWARE_MARK
		
		)
		
		
		
		
		WITH PSP_only        AS (
                            SELECT
                                t1.version_id,
                                t1.period_year,
                                t1.period_id, '
                                ||V_PROD_PARA||
                                't1.dimension_code,
                                t1.dimension_cn_name,
                                t1.dimension_subcategory_code,
                                t1.dimension_subcategory_cn_name,
                                t1.dimension_sub_detail_code,
                                t1.dimension_sub_detail_cn_name,
                                t1.group_code,
                                t1.group_cn_name,
                                t1.group_level,
                                t1.parent_code,
                                t1.parent_cn_name,
                                t1.main_flag,
                                t1.view_flag,
                                t1.region_code,
                                t1.region_cn_name,
                                t1.repoffice_code,
                                t1.repoffice_cn_name,
                                t1.bg_code,
                                t1.bg_cn_name,
                                t1.oversea_flag,
                                t1.del_flag,
                                t1.code_attributes,
								t1.SOFTWARE_MARK
                            FROM'
                                ||V_FROM_TABLE1|| ' T1
                            WHERE 
								 T1.SOFTWARE_MARK = ''HARDWARE''
								AND  T1.VIEW_FLAG = '''||F_VIEW_FLAG||'''
								AND  T1.PERIOD_YEAR = '||F_PERIOD_YEAR||'
                                AND  NOT EXISTS (
                                            SELECT
                                                   1
                                               FROM '
                                                   ||V_FROM_TABLE2 ||' T2
                                               WHERE
                                                     T2.version_id = T1.version_id
                                                 AND T2.period_year = T1.period_year
                                                 AND T2.period_id = T1.period_id  '
                                                 ||V_INSERT_JOIN_STD||
                                                 ' AND NVL(T2.dimension_code,1) = NVL(T1.dimension_code,1)
                                                 AND NVL(T2.dimension_subcategory_code,1) = NVL(T1.dimension_subcategory_code,1)
                                                 AND NVL(T2.dimension_sub_detail_code,1) = NVL(T1.dimension_sub_detail_code,1)
                                                 AND NVL(T2.group_code,1) = NVL(T1.group_code,1)
                                                 AND T2.group_level = T1.group_level
                                                 AND NVL(T2.parent_code,1) = NVL(T1.parent_code,1)
                                                 AND T2.main_flag = T1.main_flag
                                                 AND T2.view_flag = T1.view_flag
                                                 AND NVL(T2.region_code,1) = NVL(T1.region_code,1)
                                                 AND NVL(T2.repoffice_code ,1)= NVL(T1.repoffice_code,1)
                                                 AND NVL(T2.bg_code ,1)= NVL(T1.bg_code,1)
                                                 AND T2.oversea_flag = T1.oversea_flag
                                                 AND T2.del_flag = T1.del_flag
                                                 AND NVL( T2.code_attributes , 1 ) = nvl( T1.code_attributes , 1 )
												 AND T2.SOFTWARE_MARK = T1.SOFTWARE_MARK

                                                 AND t1.version_id = ' ||V_VERSION ||'
                                           )
							
                            )

         SELECT
             PSP_only.version_id,
             PSP_only.period_year,
             PSP_only.period_id, '
             || V_INSERT_PSP_ONLY || '

             PSP_only.dimension_code,
             PSP_only.dimension_cn_name,
             PSP_only.dimension_subcategory_code,
             PSP_only.dimension_subcategory_cn_name,
             PSP_only.dimension_sub_detail_code,
             PSP_only.dimension_sub_detail_cn_name,
             PSP_only.group_code,
             PSP_only.group_cn_name,
             PSP_only.group_level,
             PSP_only.parent_code,
             PSP_only.parent_cn_name,
             PSP_only.main_flag,
             PSP_only.view_flag,
             PSP_only.region_code,
             PSP_only.region_cn_name,
             PSP_only.repoffice_code,
             PSP_only.repoffice_cn_name,
             PSP_only.bg_code,
             PSP_only.bg_cn_name,
             PSP_only.oversea_flag,
             PSP_only.del_flag,
             PSP_only.code_attributes,
			 PSP_only.SOFTWARE_MARK
			 
         FROM
             PSP_only
         UNION ALL
         SELECT
             T3.version_id,
             T3.period_year,
             T3.period_id, '
             || V_INSERT_STD || '

             T3.dimension_code,
             T3.dimension_cn_name,
             T3.dimension_subcategory_code,
             T3.dimension_subcategory_cn_name,
             T3.dimension_sub_detail_code,
             T3.dimension_sub_detail_cn_name,
             T3.group_code,
             T3.group_cn_name,
             T3.group_level,
             T3.parent_code,
             T3.parent_cn_name,
             T3.main_flag,
             T3.view_flag,
             T3.region_code,
             T3.region_cn_name,
             T3.repoffice_code,
             T3.repoffice_cn_name,
             T3.bg_code,
             T3.bg_cn_name,
             T3.oversea_flag,
             T3.del_flag,
             T3.code_attributes,
			 T3.SOFTWARE_MARK
         FROM  '
            || V_FROM_TABLE2 || ' T3
         WHERE
             t3.version_id = ' || V_VERSION  ||'
			 AND T3.VIEW_FLAG = '''||F_VIEW_FLAG||'''
			 AND  T3.PERIOD_YEAR = '||F_PERIOD_YEAR||''
    
			 ;
		
		DBMS_OUTPUT.PUT_LINE(V_SQL);

		
		EXECUTE IMMEDIATE V_SQL; 
		
		RAISE notice'维度组合完成';


        RAISE notice'1111111111111111111';
    V_SQL := '
             INSERT
        INTO ' || V_TO_TABLE||' (
        version_id ,
        --base_period_id ,
        period_year ,
        period_id ,
        granularity_type ,
        prod_rnd_team_code ,
        prod_rd_team_cn_name ,
        dimension_code ,
        dimension_cn_name ,
        dimension_subcategory_code ,
        dimension_subcategory_cn_name ,
        dimension_sub_detail_code ,
        dimension_sub_detail_cn_name ,
        group_code ,
        group_cn_name ,
        cost_type ,
        group_level ,
        psp_rmb_cost_amt ,
        std_rmb_cost_amt ,
        gap_psp_std ,
        ratio_psp_std ,
        parent_code ,
        parent_cn_name ,
        main_flag ,
        view_flag ,
        region_code ,
        region_cn_name ,
        repoffice_code ,
        repoffice_cn_name ,
        bg_code ,
        bg_cn_name ,
        oversea_flag ,
        created_by ,
        creation_date ,
        last_updated_by ,
        last_update_date ,
        del_flag ,
        code_attributes,
		software_mark
    )

   
--计算psp-标准 ，psp/标准
     with    psp_std_gap_tmp AS (
         SELECT
             T4.version_id,
             T4.period_year,
             T4.period_id,'
             ||V_INSERT_ALL_TMP||'
--              T4.prod_rnd_team_code,
--              T4.prod_rd_team_cn_name,
             T4.dimension_code,
             T4.dimension_cn_name,
             T4.dimension_subcategory_code,
             T4.dimension_subcategory_cn_name,
             T4.dimension_sub_detail_code,
             T4.dimension_sub_detail_cn_name,
             T4.group_code,
             T4.group_cn_name,
             T4.group_level,
             T4.parent_code,
             T4.parent_cn_name,
             T4.main_flag,
             T4.view_flag,
             T4.region_code,
             T4.region_cn_name,
             T4.repoffice_code,
             T4.repoffice_cn_name,
             T4.bg_code,
             T4.bg_cn_name,
             T4.oversea_flag,
             T4.del_flag,
             T4.code_attributes,
			 t4.software_mark,
             NVL( t5.rmb_cost_amt , 0 )                                 AS psp_rmb_cost_amt,
             NVL( t6.rmb_cost_amt , 0 )                                 AS std_rmb_cost_amt,
             NVL( t5.rmb_cost_amt , 0 ) - NVL( t6.rmb_cost_amt , 0 )    AS GAP_PSP_STD,
             NVL( t5.rmb_cost_amt , 0 ) / NULLIF( t6.rmb_cost_amt , 0 ) AS RATIO_PSP_STD
         FROM
             ALL_TMP t4
                  LEFT JOIN ' ||V_FROM_TABLE1 ||' t5
                               ON
                                           t4.version_id = T5.version_id
                                       AND t4.period_year = T5.period_year
                                       AND t4.period_id = T5.period_id '
                                               ||V_INSERT_STD_T5|| '
                                       AND NVL( t4.dimension_code , 1 ) = NVL( T5.dimension_code , 1 )
                                       AND NVL( t4.dimension_subcategory_code , 2 ) =
                                           NVL( T5.dimension_subcategory_code , 2 )
                                       AND NVL( t4.dimension_sub_detail_code , 3 ) =
                                           NVL( T5.dimension_sub_detail_code , 3 )
                                       AND NVL(t4.group_code,1) = NVL(T5.group_code,1)
                                       AND t4.group_level = T5.group_level
                                       AND NVL(t4.parent_code,1) = NVL(T5.parent_code,1)
                                       AND t4.main_flag = T5.main_flag
                                       AND t4.view_flag = T5.view_flag
                                       AND NVL(t4.region_code,1) = NVL(T5.region_code,1)
                                       AND NVL(t4.repoffice_code ,1)= NVL(T5.repoffice_code,1)
                                       AND NVL(t4.bg_code ,1)= NVL(T5.bg_code,1)
                                       AND t4.oversea_flag = T5.oversea_flag
                                       AND t4.del_flag = T5.del_flag
                                       AND NVL( t4.code_attributes , 1 ) = nvl( T5.code_attributes , 1 )
									   AND T4.SOFTWARE_MARK = T5.SOFTWARE_MARK
                     LEFT JOIN '||V_FROM_TABLE2 || '  t6
                               ON
                                           t4.version_id = T6.version_id
                                       AND t4.period_year = T6.period_year
                                       AND t4.period_id = T6.period_id '
                                   ||V_INSERT_STD_T6|| '
                                       AND NVL( t4.dimension_code , 1 ) = NVL(T6.dimension_code, 1 )
                                       AND NVL( t4.dimension_subcategory_code , 2 ) =
                                           NVL( T6.dimension_subcategory_code , 2 )
                                       AND NVL( t4.dimension_sub_detail_code , 3 ) =
                                           NVL( T6.dimension_sub_detail_code , 3 )
                                       AND NVL( t4.group_code , 1 )= NVL( T6.group_code, 1 )
                                       AND t4.group_level = T6.group_level
                                       AND NVL( t4.parent_code , 1 )= NVL( T6.parent_code, 1 )
                                       AND t4.main_flag = T6.main_flag
                                       AND t4.view_flag = T6.view_flag
                                       AND NVL( t4.region_code , 1 )= NVL( T6.region_code, 1 )
                                       AND NVL( t4.repoffice_code, 1 ) = NVL( T6.repoffice_code, 1 )
                                       AND NVL( t4.bg_code , 1 )= NVL( T6.bg_code, 1 )
                                       AND t4.oversea_flag = T6.oversea_flag
                                       AND t4.del_flag = T6.del_flag
                                       AND nvl( t4.code_attributes , 1 ) = NVL( T6.code_attributes , 1 )
									   AND T4.SOFTWARE_MARK = T6.SOFTWARE_MARK
             )

    SELECT
        t1.version_id,
        --t.--base_period_id ,
        t1.period_year,
        t1.period_id, '''
        ||V_GRANULARITY_TYPE|| ''' AS granularity_type, '
        ||V_PROD_last|| '
        t1.dimension_code,
        t1.dimension_cn_name,
        t1.dimension_subcategory_code,
        t1.dimension_subcategory_cn_name,
        t1.dimension_sub_detail_code,
        t1.dimension_sub_detail_cn_name,
        t1.group_code,
        t1.group_cn_name, '''
        ||V_COST_TYPE  ||'''     AS cost_type,
        t1.group_level,
        t1.psp_rmb_cost_amt,
        t1.std_rmb_cost_amt,
        t1.gap_psp_std,
        t1.ratio_psp_std,
        t1.parent_code,
        t1.parent_cn_name,
        t1.main_flag,
        t1.view_flag,
        t1.region_code,
        t1.region_cn_name,
        t1.repoffice_code,
        t1.repoffice_cn_name,
        t1.bg_code,
        t1.bg_cn_name,
        t1.oversea_flag,
        -1                 AS CREATED_BY,
        CURRENT_TIMESTAMP  AS CREATION_DATE,
        -1                 AS last_updated_by,
        CURRENT_TIMESTAMP  AS last_update_date,
        t1.del_flag,
        t1.code_attributes,
		t1.software_mark
    FROM
        psp_std_gap_tmp T1;  ';
    -- dbms_output.PUT_LINE(V_SQL);
--     RAISE NOTICE '--------------------------';
--      RAISE NOTICE '%',V_SQL;

   EXECUTE IMMEDIATE V_SQL;
   RAISE notice'计算完成';

    --2 执行结束
    V_STEP_NUM = V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
            ( F_SP_NAME => V_SP_NAME ,
              F_STEP_NUM => V_STEP_NUM ,
              F_CAL_LOG_DESC => '会计期数据 '||V_GRANULARITY_TYPE || '类型数据插入目标表结束：' || V_TO_TABLE,
              F_DML_ROW_COUNT => SQL % ROWCOUNT ,
              F_RESULT_STATUS => X_RESULT_STATUS ,
              F_ERRBUF => 'SUCCESS' );


    --收集统计信息
    --EXECUTE IMMEDIATE ' ANALYZE ' || V_TO_TABLE;

    --日志结束
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
            ( F_SP_NAME => V_SP_NAME ,
              F_STEP_NUM => V_STEP_NUM ,
              F_CAL_LOG_DESC => V_SP_NAME || '运行结束, 收集' || V_TO_TABLE || '统计信息完成!' );

    RETURN 'SUCCESS';

EXCEPTION
    WHEN OTHERS THEN
        X_RESULT_STATUS := 'FAILED';

        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
                ( F_SP_NAME => V_SP_NAME ,
                  F_CAL_LOG_DESC => V_SP_NAME || '运行失败' ,
                  F_RESULT_STATUS => X_RESULT_STATUS ,
                  F_ERRBUF => SQLSTATE || ':' || SQLERRM
                );

END;

$$
/

