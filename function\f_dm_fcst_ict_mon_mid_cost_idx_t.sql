-- Name: f_dm_fcst_ict_mon_mid_cost_idx_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_ict_mon_mid_cost_idx_t(f_cost_type character varying, f_granularity_type character varying, f_page_type character varying, f_keystr text, f_version_id integer, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/***************************************************************************************************************************************************************
--2024年9月24日14点32分 新增软硬件标识
------月度指数中间表 (计算SPART层级+量纲子类明细层级)
--------------------来源表
----------分子
----月均本表
取数方式:取全量
--PSP
重量级团队目录		DM_FCST_ICT_PSP_IRB_BASE_DETAIL_SPART_T		--PSP IRB
产业目录：			DM_FCST_ICT_PSP_INDUS_BASE_DETAIL_SPART_T   --PSP INDUS
销售目录：			DM_FCST_ICT_PSP_PROD_BASE_DETAIL_SPART_T    --PSP PROD
重量级团队目录		DM_FCST_ICT_STD_IRB_BASE_DETAIL_SPART_T     --STD IRB
产业目录：			DM_FCST_ICT_STD_INDUS_BASE_DETAIL_SPART_T   --STD INDUS
销售目录：			DM_FCST_ICT_STD_PROD_BASE_DETAIL_SPART_T    --STD PROD

--------------------目标表
--月度指数中间表
重量级团队目录		DM_FCST_ICT_PSP_IRB_MON_MID_COST_IDX_T		--PSP IRB
产业目录			DM_FCST_ICT_PSP_INDUS_MON_MID_COST_IDX_T    --PSP INDUS
销售目录			DM_FCST_ICT_PSP_PROD_MON_MID_COST_IDX_T     --PSP PROD
重量级团队目录		DM_FCST_ICT_STD_IRB_MON_MID_COST_IDX_T      --STD IRB
产业目录			DM_FCST_ICT_STD_INDUS_MON_MID_COST_IDX_T    --STD INDUS
销售目录			DM_FCST_ICT_STD_PROD_MON_MID_COST_IDX_T     --STD PROD

--事例    ：
  SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_MID_COST_IDX_T('PSP','IRB','MONTH','',''); 		--PSP成本 重量级团队目录一个版本数据
  SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_MID_COST_IDX_T('PSP','IRB','REPLACE','',''); 		--PSP成本 重量级团队目录一个版本数据
  SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_MID_COST_IDX_T('PSP','INDUS','',''); 	--PSP成本 产业目录一个版本数据
  SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_MID_COST_IDX_T('PSP','PROD','',''); 	--PSP成本 销售目录一个版本数据
  SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_MID_COST_IDX_T('STD''IRB','密钥',''); 	--标准成本 重量级团队目录一个版本数据
  SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_MID_COST_IDX_T('STD''INDUS','密钥',''); --标准成本 产业目录一个版本数据
  SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_MID_COST_IDX_T('STD''PROD','密钥','');  --标准成本 销售目录一个版本数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME             VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_MID_COST_IDX_T';
  V_VERSION             VARCHAR(10);
  V_EXCEPTION_FLAG      INT;
  V_FROM_TABLE          VARCHAR(200);
  V_TO_TABLE            VARCHAR(200);
  V_TO_REPL_TABLE       VARCHAR(200);
  V_FROM_YTD_TABLE		VARCHAR(200);
  V_TO_YTD_TABLE	    VARCHAR(200);
  V_TO_REPL_YTD_TABLE	  VARCHAR(200);
  V_FROM_MONTH_TABLE    VARCHAR(200);
  V_TO_MONTH_TABLE	    VARCHAR(200);
  V_TO_YTD_RESULT_TABLE VARCHAR(200);
  V_BASE_PERIOD_ID		INT ; 
  V_OTHER_DIM_PART      TEXT;
  V_JOIN_REPL_OTHER_DIM_PART TEXT;
  V_SQL_OTHER_DIM_PART  TEXT;
  V_JOIN_OTHER_DIM_PART TEXT;
  V_DIMENSION_PART      TEXT;
  V_SQL_DIMENSION_PART  TEXT;
  V_JOIN_DIMENSION_CODE TEXT;
  V_RMB_AVG_AMT         TEXT;
  V_PBI_PART            TEXT;
  V_REPL_PBI_PART       TEXT;
  V_REPL_PARENT_PBI_PART TEXT;
  V_SQL_PBI_PART        TEXT;
  V_JOIN_PB1_PART       TEXT;
  V_SQL                 TEXT;
  V_PROD_PART                 TEXT;
  V_SQL_PROD_PART                 TEXT;

BEGIN


--判断基期
IF     MONTH(CURRENT_DATE)   = 1 then                                                                                                                                  
	V_BASE_PERIOD_ID := TO_NUMBER((YEAR(CURRENT_DATE) - 2) || '01'); --基期会计期,如果当前是1月则取两年前的1月为基期
ELSIF  MONTH(CURRENT_DATE)   != 1 then 
	V_BASE_PERIOD_ID := TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '01'); --基期会计期,如果当前不是1月则取去年前的1月为基期

END IF;	
	
RAISE NOTICE '取版本号';
--版本表
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;

  raise notice'1111111111111111111';

  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '取得本次执行数据成本类型：'||F_COST_TYPE||'，目录树：'||F_GRANULARITY_TYPE||'，以及版本号：'||V_VERSION ,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');


----变量赋值
--表变量赋值

  V_FROM_YTD_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_SUM_DETAIL_SPART_T'; --原分年月度累计关联TOP
  V_TO_YTD_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_YTD_MID_COST_IDX_T';
  V_FROM_MONTH_TABLE	:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_BASE_DETAIL_SPART_T';
  V_TO_MONTH_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_MON_MID_COST_IDX_T';
  
  
  
  V_TO_YTD_RESULT_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_YTD_COST_IDX_T';
  V_TO_REPL_YTD_TABLE   := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_REPL_SUM_DETAIL_SPART_T';  --原不分年月度累计关联top

  raise notice'222222222222222222';

  V_OTHER_DIM_PART:='
			REGION_CODE,
			REGION_CN_NAME,
			REPOFFICE_CODE,
			REPOFFICE_CN_NAME,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			';

	V_JOIN_REPL_OTHER_DIM_PART :='
			AND T1.REGION_CODE       = T2.REGION_CODE
			AND T1.REGION_CN_NAME    = T2.REGION_CN_NAME
			AND T1.REPOFFICE_CODE    = T2.REPOFFICE_CODE
			AND T1.REPOFFICE_CN_NAME = T2.REPOFFICE_CN_NAME
			AND T1.BG_CODE           = T2.BG_CODE
			AND T1.BG_CN_NAME        = T2.BG_CN_NAME
			AND T1.OVERSEA_FLAG      = T2.OVERSEA_FLAG
			';

  V_SQL_OTHER_DIM_PART:='
			T1.REGION_CODE,
			T1.REGION_CN_NAME,
			T1.REPOFFICE_CODE,
			T1.REPOFFICE_CN_NAME,
			T1.BG_CODE,
			T1.BG_CN_NAME,
			T1.OVERSEA_FLAG,
			';
  V_JOIN_OTHER_DIM_PART:='
		AND T1.REGION_CODE = T2.REGION_CODE
		AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
		AND T1.BG_CODE = T2.BG_CODE
		AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
		';

  V_DIMENSION_PART:='
					DIMENSION_CODE,
					DIMENSION_CN_NAME,
					DIMENSION_SUBCATEGORY_CODE,
					DIMENSION_SUBCATEGORY_CN_NAME,
					DIMENSION_SUB_DETAIL_CODE,
					DIMENSION_SUB_DETAIL_CN_NAME,
					';
  V_SQL_DIMENSION_PART:='
					T1.DIMENSION_CODE,
					T1.DIMENSION_CN_NAME,
					T1.DIMENSION_SUBCATEGORY_CODE,
					T1.DIMENSION_SUBCATEGORY_CN_NAME,
					T1.DIMENSION_SUB_DETAIL_CODE,
					T1.DIMENSION_SUB_DETAIL_CN_NAME,
					';
  V_JOIN_DIMENSION_CODE:='
					AND NVL(T1.DIMENSION_CODE,''DC'') = NVL(T2.DIMENSION_CODE,''DC'')
					AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''DSC'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''DSC'')
					AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''DSDC'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''DSDC'')
					';

    V_SQL_PROD_PART  := '
      LV4_CODE AS PROD_CODE,
	  LV4_CN_NAME AS PROD_CN_NAME,
      ';

--条件判断(加密不加密)

--页面判断(月度页面/编码替代页面)
  IF F_PAGE_TYPE = 'MONTH' THEN
    V_FROM_TABLE	:= V_FROM_MONTH_TABLE;
	V_TO_TABLE		:= V_TO_MONTH_TABLE;
	
	
  ELSIF F_PAGE_TYPE = 'REPLACE' THEN
    V_FROM_TABLE	:= V_FROM_YTD_TABLE; --10版本月度累计使用分年累计
	V_TO_TABLE		:= V_TO_YTD_TABLE;
	
  END IF;



--是否解密判断
  IF F_COST_TYPE = 'PSP' THEN
    V_RMB_AVG_AMT           := 'RMB_AVG_AMT,';
  ELSIF F_COST_TYPE = 'STD' THEN
    V_RMB_AVG_AMT			:= '
							   TO_NUMBER(GS_DECRYPT(RMB_AVG_AMT,
									 '''||F_KEYSTR||''',
									 ''aes128'',
									 ''cbc'',
									 ''sha256'')) AS RMB_AVG_AMT,
							';
  END IF;
--不同目录维度字段判断
  IF F_GRANULARITY_TYPE = 'IRB' THEN

    V_PBI_PART := '
      LV0_PROD_RND_TEAM_CODE,
      LV1_PROD_RND_TEAM_CODE,
      LV2_PROD_RND_TEAM_CODE,
      LV3_PROD_RND_TEAM_CODE,
      LV4_PROD_RND_TEAM_CODE,
      LV0_PROD_RD_TEAM_CN_NAME,
      LV1_PROD_RD_TEAM_CN_NAME,
      LV2_PROD_RD_TEAM_CN_NAME,
      LV3_PROD_RD_TEAM_CN_NAME,
      LV4_PROD_RD_TEAM_CN_NAME,
      ';

    V_SQL_PBI_PART  := '
      T1.LV0_PROD_RND_TEAM_CODE,
      T1.LV1_PROD_RND_TEAM_CODE,
      T1.LV2_PROD_RND_TEAM_CODE,
      T1.LV3_PROD_RND_TEAM_CODE,
      T1.LV4_PROD_RND_TEAM_CODE,
      T1.LV0_PROD_RD_TEAM_CN_NAME,
      T1.LV1_PROD_RD_TEAM_CN_NAME,
      T1.LV2_PROD_RD_TEAM_CN_NAME,
      T1.LV3_PROD_RD_TEAM_CN_NAME,
      T1.LV4_PROD_RD_TEAM_CN_NAME,
      ';

	V_PROD_PART:='
		PROD_RND_TEAM_CODE,
		PROD_RD_TEAM_CN_NAME,
	';
	
	V_REPL_PBI_PART := 'LV4_PROD_RND_TEAM_CODE AS PBI_CODE,LV4_PROD_RD_TEAM_CN_NAME AS PBI_CN_NAME,';
	
	V_REPL_PARENT_PBI_PART := 'LV4_PROD_RND_TEAM_CODE AS PARENT_CODE,LV4_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
	

  ELSIF F_GRANULARITY_TYPE = 'INDUS' THEN
    V_PBI_PART := '
      LV0_INDUSTRY_CATG_CODE    ,
      LV1_INDUSTRY_CATG_CODE      ,
      LV2_INDUSTRY_CATG_CODE      ,
      LV3_INDUSTRY_CATG_CODE      ,
      LV4_INDUSTRY_CATG_CODE      ,
      LV0_INDUSTRY_CATG_CN_NAME,
      LV1_INDUSTRY_CATG_CN_NAME,
      LV2_INDUSTRY_CATG_CN_NAME,
      LV3_INDUSTRY_CATG_CN_NAME,
      LV4_INDUSTRY_CATG_CN_NAME,
      ';

    V_SQL_PBI_PART := '
      T1.LV0_INDUSTRY_CATG_CODE,
      T1.LV1_INDUSTRY_CATG_CODE,
      T1.LV2_INDUSTRY_CATG_CODE,
      T1.LV3_INDUSTRY_CATG_CODE,
      T1.LV4_INDUSTRY_CATG_CODE,
      T1.LV0_INDUSTRY_CATG_CN_NAME,
      T1.LV1_INDUSTRY_CATG_CN_NAME,
      T1.LV2_INDUSTRY_CATG_CN_NAME,
      T1.LV3_INDUSTRY_CATG_CN_NAME,
      T1.LV4_INDUSTRY_CATG_CN_NAME,
      ';

	V_PROD_PART:='
		INDUSTRY_CATG_CODE,
		INDUSTRY_CATG_CN_NAME,
	';
	
	V_REPL_PBI_PART := 'LV4_INDUSTRY_CATG_CODE AS PBI_CODE,LV4_INDUSTRY_CATG_CN_NAME AS PBI_CN_NAME,';
	
	V_REPL_PARENT_PBI_PART := 'LV4_INDUSTRY_CATG_CODE AS PARENT_CODE,LV4_INDUSTRY_CATG_CN_NAME AS PARENT_CN_NAME,';

  ELSIF F_GRANULARITY_TYPE = 'PROD' THEN
    V_PBI_PART      := '
      LV0_PROD_LIST_CODE,
      LV1_PROD_LIST_CODE,
      LV2_PROD_LIST_CODE,
      LV3_PROD_LIST_CODE,
      LV4_PROD_LIST_CODE,
      LV0_PROD_LIST_CN_NAME,
      LV1_PROD_LIST_CN_NAME,
      LV2_PROD_LIST_CN_NAME,
      LV3_PROD_LIST_CN_NAME,
      LV4_PROD_LIST_CN_NAME,
      ';
    V_SQL_PBI_PART  := '
      T1.LV0_PROD_LIST_CODE,
      T1.LV1_PROD_LIST_CODE,
      T1.LV2_PROD_LIST_CODE,
      T1.LV3_PROD_LIST_CODE,
      T1.LV4_PROD_LIST_CODE,
      T1.LV0_PROD_LIST_CN_NAME,
      T1.LV1_PROD_LIST_CN_NAME,
      T1.LV2_PROD_LIST_CN_NAME,
      T1.LV3_PROD_LIST_CN_NAME,
      T1.LV4_PROD_LIST_CN_NAME,
      ';

	V_PROD_PART:='
		PROD_LIST_CODE,
		PROD_LIST_CN_NAME,
	';
	
	V_REPL_PBI_PART := 'LV4_PROD_LIST_CODE AS PBI_CODE,LV4_PROD_LIST_CN_NAME AS PBI_CN_NAME,';
	
	V_REPL_PARENT_PBI_PART := 'LV4_PROD_LIST_CODE AS PARENT_CODE,LV4_PROD_LIST_CN_NAME AS PARENT_CN_NAME,';

  END IF;

RAISE NOTICE'月均本(STD解密)落表';
----月均本(STD解密)落表
--建临时中间表
DROP TABLE IF EXISTS DM_MONTH_AVG_TEMP;
CREATE TEMPORARY TABLE DM_MONTH_AVG_TEMP(
 LV0_CODE		VARCHAR(50),
 LV1_CODE   	VARCHAR(50),
 LV2_CODE   	VARCHAR(50),
 LV3_CODE   	VARCHAR(50),
 LV4_CODE   	VARCHAR(50),
 LV0_CN_NAME	VARCHAR(200),
 LV1_CN_NAME   	VARCHAR(200),
 LV2_CN_NAME   	VARCHAR(200),
 LV3_CN_NAME   	VARCHAR(200),
 LV4_CN_NAME   	VARCHAR(200),
 DIMENSION_CODE	VARCHAR(50),
 DIMENSION_CN_NAME	VARCHAR(200),
 DIMENSION_SUBCATEGORY_CODE	VARCHAR(50),
 DIMENSION_SUBCATEGORY_CN_NAME	VARCHAR(200),
 DIMENSION_SUB_DETAIL_CODE	VARCHAR(50),
 DIMENSION_SUB_DETAIL_CN_NAME	VARCHAR(200),
 VIEW_FLAG			VARCHAR(20),
 PERIOD_YEAR		INT,
 PERIOD_ID			INT,
 SPART_CODE			VARCHAR(50)  ,
 SPART_CN_NAME		VARCHAR(200) ,
 RMB_AVG_AMT		NUMERIC,
 REGION_CODE		VARCHAR(50)  ,
 REGION_CN_NAME		VARCHAR(200) ,
 REPOFFICE_CODE		VARCHAR(50)  ,
 REPOFFICE_CN_NAME	VARCHAR(200) ,
 BG_CODE			VARCHAR(50)  ,
 BG_CN_NAME         VARCHAR(200) ,
 OVERSEA_FLAG		VARCHAR(10),
 MAIN_FLAG			VARCHAR(1),
 CODE_ATTRIBUTES    VARCHAR(20),     --是否主力编码
 SOFTWARE_MARK		VARCHAR(20)		--202410版本 新增软硬件标识
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE,OVERSEA_FLAG);

--落表(金额字段赋值)
V_SQL:='
  INSERT INTO DM_MONTH_AVG_TEMP
    (PERIOD_YEAR,
	 PERIOD_ID,
     LV0_CODE,
	 LV1_CODE,
	 LV2_CODE,
	 LV3_CODE,
	 LV4_CODE,
	 LV0_CN_NAME,
	 LV1_CN_NAME,
	 LV2_CN_NAME,
	 LV3_CN_NAME,
	 LV4_CN_NAME,
	 '||V_DIMENSION_PART||'
	 SPART_CODE,
     SPART_CN_NAME,
	 RMB_AVG_AMT,
	 '||V_OTHER_DIM_PART||'
	 MAIN_FLAG,
     CODE_ATTRIBUTES,
	 SOFTWARE_MARK,		--202410版本 新增软硬件标识
	 VIEW_FLAG)
    SELECT PERIOD_YEAR,
           PERIOD_ID,
		   '||V_PBI_PART||V_DIMENSION_PART||'
		   SPART_CODE,
           SPART_CN_NAME,
           '||V_RMB_AVG_AMT||'
           '||V_OTHER_DIM_PART||'
		   MAIN_FLAG,
           CODE_ATTRIBUTES,
		   SOFTWARE_MARK,		--202410版本 新增软硬件标识
		   VIEW_FLAG
      FROM '||V_FROM_TABLE||'
';
DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE V_SQL;

raise notice'333333333333';

 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 2,
  F_CAL_LOG_DESC => '落表(金额字段赋值)',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');

RAISE NOTICE'指数计算';
----建临时中间表
--取基期均本，相除 得到全量SPART或量纲子类明细指数
V_SQL:='

TRUNCATE TABLE '||V_TO_TABLE||';

    WITH BASE_DB AS
     (SELECT T1.PERIOD_ID,
             T1.PERIOD_YEAR,
             T1.LV0_CODE,
			 T1.LV1_CODE,
			 T1.LV2_CODE,
			 T1.LV3_CODE,
			 T1.LV4_CODE,
			 T1.LV0_CN_NAME,
			 T1.LV1_CN_NAME,
			 T1.LV2_CN_NAME,
			 T1.LV3_CN_NAME,
			 T1.LV4_CN_NAME,
			 '||V_SQL_DIMENSION_PART||'
			 T1.SPART_CODE,
             T1.SPART_CN_NAME,
			 DECODE(T1.VIEW_FLAG,''PROD_SPART'',T1.SPART_CODE,T1.DIMENSION_SUB_DETAIL_CODE) AS GROUP_CODE,
			 DECODE(T1.VIEW_FLAG,''PROD_SPART'',T1.SPART_CN_NAME,T1.DIMENSION_SUB_DETAIL_CN_NAME) AS GROUP_CN_NAME,
			 DECODE(T1.VIEW_FLAG,''PROD_SPART'',''SPART'',''SUB_DETAIL'') AS GROUP_LEVEL,
             T1.RMB_AVG_AMT / NULLIF(T2.RMB_AVG_AMT, 0) * 100 AS COST_INDEX,
			 DECODE(T1.VIEW_FLAG,''PROD_SPART'',T1.LV4_CODE,T1.DIMENSION_SUBCATEGORY_CODE) AS PARENT_CODE,
			 DECODE(T1.VIEW_FLAG,''PROD_SPART'',T1.LV4_CN_NAME,T1.DIMENSION_SUBCATEGORY_CN_NAME) AS PARENT_CN_NAME,
             '||V_SQL_OTHER_DIM_PART||'
			 T1.MAIN_FLAG,
             T1.CODE_ATTRIBUTES,
			 T1.SOFTWARE_MARK,		--202410版本 新增软硬件标识
             T1.VIEW_FLAG
        FROM DM_MONTH_AVG_TEMP T1
        LEFT JOIN (SELECT PERIOD_ID,
                         PERIOD_YEAR,
                         LV0_CODE,
						 LV1_CODE,
						 LV2_CODE,
						 LV3_CODE,
						 LV4_CODE,
						 '||V_DIMENSION_PART||'
						 SPART_CODE,
                         SPART_CN_NAME,
                         RMB_AVG_AMT,
                         VIEW_FLAG,
                         '||V_OTHER_DIM_PART||'
						 MAIN_FLAG,
                         CODE_ATTRIBUTES,
						 SOFTWARE_MARK		--202410版本 新增软硬件标识
                    FROM DM_MONTH_AVG_TEMP
                   WHERE PERIOD_ID = '||V_BASE_PERIOD_ID||') T2
          ON NVL(T1.SPART_CODE,''SC'') = NVL(T2.SPART_CODE,''SC'')
         AND T1.VIEW_FLAG = T2.VIEW_FLAG
		 AND T1.LV0_CODE = T2.LV0_CODE
		 AND T1.LV1_CODE = T2.LV1_CODE
		 AND T1.LV2_CODE = T2.LV2_CODE
		 AND T1.LV3_CODE = T2.LV3_CODE
		 AND T1.LV4_CODE = T2.LV4_CODE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK 		--202410版本 新增软硬件标识
		 AND NVL(T1.MAIN_FLAG,''MF'') = NVL(T2.MAIN_FLAG,''MF'')
         AND NVL(T1.CODE_ATTRIBUTES,''CA'') = NVL(T2.CODE_ATTRIBUTES,''CA'')
       '||V_JOIN_OTHER_DIM_PART||V_JOIN_DIMENSION_CODE||')

  INSERT INTO '||V_TO_TABLE||'
    (VERSION_ID,
     BASE_PERIOD_ID,
	 '||V_PROD_PART||'
	 PERIOD_ID,
     PERIOD_YEAR,
	 LV0_CODE,
	 LV1_CODE,
	 LV2_CODE,
	 LV3_CODE,
	 LV4_CODE,
	 LV0_CN_NAME,
	 LV1_CN_NAME,
	 LV2_CN_NAME,
	 LV3_CN_NAME,
	 LV4_CN_NAME,
     '||V_DIMENSION_PART||'
	 SPART_CODE,
     SPART_CN_NAME,
	 GROUP_CODE,
	 GROUP_CN_NAME,
	 GROUP_LEVEL,
     COST_INDEX,
	 PARENT_CODE,
	 PARENT_CN_NAME,
     VIEW_FLAG,
	 SOFTWARE_MARK,		--202410版本 新增软硬件标识
     '||V_OTHER_DIM_PART||'
	 MAIN_FLAG,
     CODE_ATTRIBUTES,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG)

    --普通全量数据
    SELECT '||V_VERSION||' AS VERSION_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
		   '||V_SQL_PROD_PART||'
		   PERIOD_ID,
           PERIOD_YEAR,
		   LV0_CODE,
		   LV1_CODE,
		   LV2_CODE,
		   LV3_CODE,
		   LV4_CODE,
		   LV0_CN_NAME,
		   LV1_CN_NAME,
		   LV2_CN_NAME,
		   LV3_CN_NAME,
		   LV4_CN_NAME,
           '||V_DIMENSION_PART||'
		   SPART_CODE,
           SPART_CN_NAME,
		   GROUP_CODE,
		   GROUP_CN_NAME,
		   GROUP_LEVEL,
           COST_INDEX,
		   PARENT_CODE,
		   PARENT_CN_NAME,
           VIEW_FLAG,
		   SOFTWARE_MARK,		--202410版本 新增软硬件标识
           '||V_OTHER_DIM_PART||'
		   ''N''AS MAIN_FLAG,
           '''' AS CODE_ATTRIBUTES,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N''AS DEL_FLAG
      FROM BASE_DB
    UNION ALL
    --主力编码数据
    SELECT '||V_VERSION||' AS VERSION_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
		   '||V_SQL_PROD_PART||'
		   PERIOD_ID,
           PERIOD_YEAR,
		   LV0_CODE,
		   LV1_CODE,
		   LV2_CODE,
		   LV3_CODE,
		   LV4_CODE,
		   LV0_CN_NAME,
		   LV1_CN_NAME,
		   LV2_CN_NAME,
		   LV3_CN_NAME,
		   LV4_CN_NAME,
           '||V_DIMENSION_PART||'
		   SPART_CODE,
           SPART_CN_NAME,
		   GROUP_CODE,
		   GROUP_CN_NAME,
		   GROUP_LEVEL,
           COST_INDEX,
		   PARENT_CODE,
		   PARENT_CN_NAME,
           VIEW_FLAG,
		   SOFTWARE_MARK,		--202410版本 新增软硬件标识
           '||V_OTHER_DIM_PART||'
		   ''Y''AS MAIN_FLAG,
           CODE_ATTRIBUTES,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
          ''N''AS DEL_FLAG
      FROM BASE_DB
     WHERE MAIN_FLAG =''Y''
       AND VIEW_FLAG =''PROD_SPART''
    UNION ALL
    --主力编码-编码属性‘全选’
    SELECT '||V_VERSION||' AS VERSION_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
		   '||V_SQL_PROD_PART||'
		   PERIOD_ID,
           PERIOD_YEAR,
		   LV0_CODE,
		   LV1_CODE,
		   LV2_CODE,
		   LV3_CODE,
		   LV4_CODE,
		   LV0_CN_NAME,
		   LV1_CN_NAME,
		   LV2_CN_NAME,
		   LV3_CN_NAME,
		   LV4_CN_NAME,
		   '||V_DIMENSION_PART||'
		   SPART_CODE,
           SPART_CN_NAME,
		   GROUP_CODE,
		   GROUP_CN_NAME,
		   GROUP_LEVEL,
           COST_INDEX,
		   PARENT_CODE,
		   PARENT_CN_NAME,
           VIEW_FLAG,
		   SOFTWARE_MARK,		--202410版本 新增软硬件标识
           '||V_OTHER_DIM_PART||'
		   ''Y''AS MAIN_FLAG,
		   ''全选''AS CODE_ATTRIBUTES,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
          ''N''AS DEL_FLAG
      FROM BASE_DB
     WHERE MAIN_FLAG =''Y''
       AND VIEW_FLAG =''PROD_SPART''
	   ';
	 DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE V_SQL;


PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 3,
  F_CAL_LOG_DESC => '指数计算',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');



RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';

    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_EXCEPTION_FLAG,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败',
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => SQLSTATE||':'||SQLERRM

    );


 END; 
$$
/

