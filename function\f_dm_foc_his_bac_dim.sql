-- Name: f_dm_foc_his_bac_dim; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_his_bac_dim(f_industry_flag character varying, f_cost_type character varying, f_dimension_type character varying, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/***************************************************************************************************************************************************************
最近更新时间: 2024年4月15日18点13分
修改人: 黄心蕊 hwx1187045
修改内容: 加入参，实现维表刷新按场景调用
创建时间：2024-03-25
创建人：  黄心蕊 hwx1187045
背景描述：产业采购-产业制造-产业总成本 维表备份及刷新函数
参数描述：参数一(F_COST_TYPE): 'T'为总成本 , 'P'为采购 , 'M'为制造
		  参数二(F_DIMENSION_TYPE): 'U'为通用 , 'P'为盈利 , 'D'为量纲
		  参数三(X_RESULT_STATUS): 运行状态返回值 ‘1’为成功，‘0’为失败
事例    ：--总成本
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_HIS_BAC_DIM('T','U');--总成本通用维表刷新 
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_HIS_BAC_DIM('T','P');--总成本盈利维表刷新 
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_HIS_BAC_DIM('T','D');--总成本量纲维表刷新 
		  --采购
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_HIS_BAC_DIM('P','U');--采购通用维表刷新
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_HIS_BAC_DIM('P','P');--采购盈利维表刷新
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_HIS_BAC_DIM('P','D');--采购量纲维表刷新
		  --制造
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_HIS_BAC_DIM('M','U');--制造通用维表刷新 
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_HIS_BAC_DIM('M','U');--制造盈利维表刷新 
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_HIS_BAC_DIM('M','U');--制造量纲维表刷新 
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME        VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_HIS_BAC_DIM';
  V_STEP_NUM       INT := 0;
  V_VERSION_NAME   INT := TO_NUMBER(TO_CHAR(CURRENT_DATE, 'YYYYMM'));
  V_VERSION        INT DEFAULT NULL;
  V_TABLE_MID_PART VARCHAR(20);
  V_SQL            TEXT;

BEGIN

  X_RESULT_STATUS:='1';
  
  IF F_INDUSTRY_FLAG = 'I' THEN
  
    V_TABLE_MID_PART := '';
  
    --日志开始
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	(F_SP_NAME      => V_SP_NAME,
	F_STEP_NUM     => 0,
	F_CAL_LOG_DESC => V_SP_NAME||'开始执行，ICT表开始刷新,成本类型为:'||F_COST_TYPE||'，颗粒度为:'||F_DIMENSION_TYPE
	);
  
  ELSIF F_INDUSTRY_FLAG = 'E' THEN
  
    V_TABLE_MID_PART := 'ENERGY_';
  
    --日志开始
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	(F_SP_NAME      => V_SP_NAME,
	F_STEP_NUM     => 0,
	F_CAL_LOG_DESC => V_SP_NAME||'开始执行，数字能源表开始刷新,成本类型为:'||F_COST_TYPE||'，颗粒度为:'||F_DIMENSION_TYPE
	);
	
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN
  
    V_TABLE_MID_PART := 'IAS_';
  
    --日志开始
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	(F_SP_NAME      => V_SP_NAME,
	F_STEP_NUM     => 0,
	F_CAL_LOG_DESC => V_SP_NAME||'开始执行，IAS表开始刷新,成本类型为:'||F_COST_TYPE||'，颗粒度为:'||F_DIMENSION_TYPE
	);
  
  END IF;
   
  
IF F_COST_TYPE = 'P' AND F_DIMENSION_TYPE = 'U' THEN 
 --采购通用历史表插数
 V_STEP_NUM:= V_STEP_NUM+1;	
 
 V_SQL :='
 
SELECT DISTINCT VERSION_ID FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MID_VIEW_INFO_D LIMIT 1;';

EXECUTE V_SQL INTO V_VERSION;

 V_SQL :='
 
DELETE FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'HIS_VIEW_INFO_D WHERE VERSION_NAME = '||V_VERSION_NAME||'
													AND VERSION_ID = '||V_VERSION||';
													
INSERT INTO FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'HIS_VIEW_INFO_D
  (VERSION_NAME,
   GROUP_LEVEL,
   LV0_PROD_RND_TEAM_CODE,
   LV0_PROD_RD_TEAM_CN_NAME,
   LV1_PROD_RND_TEAM_CODE,
   LV1_PROD_RD_TEAM_CN_NAME,
   LV2_PROD_RND_TEAM_CODE,
   LV2_PROD_RD_TEAM_CN_NAME,
   L3_CEG_CODE,
   L3_CEG_CN_NAME,
   L3_CEG_SHORT_CN_NAME,
   CATEGORY_CODE,
   CATEGORY_CN_NAME,
   ITEM_CODE,
   ITEM_CN_NAME,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   VIEW_FLAG,
   VERSION_ID,
   LV3_PROD_RND_TEAM_CODE,
   LV3_PROD_RD_TEAM_CN_NAME,
   CALIBER_FLAG,
   OVERSEA_FLAG,
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME,
   LV0_PROD_LIST_EN_NAME,
   L4_CEG_CODE,
   L4_CEG_CN_NAME,
   L4_CEG_SHORT_CN_NAME)
  SELECT '||V_VERSION_NAME||' AS VERSION_NAME,
         GROUP_LEVEL,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         L3_CEG_CODE,
         L3_CEG_CN_NAME,
         L3_CEG_SHORT_CN_NAME,
         CATEGORY_CODE,
         CATEGORY_CN_NAME,
         ITEM_CODE,
         ITEM_CN_NAME,
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         VIEW_FLAG,
         VERSION_ID,
         LV3_PROD_RND_TEAM_CODE,
         LV3_PROD_RD_TEAM_CN_NAME,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV0_PROD_LIST_EN_NAME,
         L4_CEG_CODE,
         L4_CEG_CN_NAME,
         L4_CEG_SHORT_CN_NAME
    FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'VIEW_INFO_D;';
	
EXECUTE V_SQL;
	
	--插入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '采购通用本版本'||V_VERSION||'数据备份完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');
 
V_SQL := '
 --修改表名，替换最新数据
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MID_VIEW_INFO_D	RENAME TO DM_FOC_'||V_TABLE_MID_PART||'VIEW_INFO_D_BAC ;
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'VIEW_INFO_D		RENAME TO DM_FOC_'||V_TABLE_MID_PART||'MID_VIEW_INFO_D ;	
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'VIEW_INFO_D_BAC	RENAME TO DM_FOC_'||V_TABLE_MID_PART||'VIEW_INFO_D ;';

EXECUTE V_SQL;

	--插入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '采购通用维表替换完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');
  
ELSIF F_COST_TYPE = 'P' AND F_DIMENSION_TYPE = 'P' THEN 
 --采购盈利历史表插数
V_STEP_NUM:= V_STEP_NUM+1;	

V_SQL := '

SELECT DISTINCT VERSION_ID FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'PFT_MID_VIEW_INFO_D LIMIT 1;';

EXECUTE V_SQL INTO V_VERSION;
 
V_SQL := '
 
DELETE FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'PFT_HIS_VIEW_INFO_D WHERE VERSION_NAME = '||V_VERSION_NAME||'
														AND VERSION_ID = '||V_VERSION||';
													
INSERT INTO FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'PFT_HIS_VIEW_INFO_D  
  (VERSION_NAME,
   GROUP_LEVEL,
   LV0_PROD_RND_TEAM_CODE,
   LV0_PROD_RD_TEAM_CN_NAME,
   LV1_PROD_RND_TEAM_CODE,
   LV1_PROD_RD_TEAM_CN_NAME,
   LV2_PROD_RND_TEAM_CODE,
   LV2_PROD_RD_TEAM_CN_NAME,
   L3_CEG_CODE,
   L3_CEG_CN_NAME,
   L3_CEG_SHORT_CN_NAME,
   CATEGORY_CODE,
   CATEGORY_CN_NAME,
   ITEM_CODE,
   ITEM_CN_NAME,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   VIEW_FLAG,
   VERSION_ID,
   L1_NAME,
   L2_NAME,
   CALIBER_FLAG,
   OVERSEA_FLAG,
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME,
   LV0_PROD_LIST_EN_NAME,
   L4_CEG_CODE,
   L4_CEG_CN_NAME,
   L4_CEG_SHORT_CN_NAME)
  SELECT '||V_VERSION_NAME||' AS VERSION_NAME,
         GROUP_LEVEL,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         L3_CEG_CODE,
         L3_CEG_CN_NAME,
         L3_CEG_SHORT_CN_NAME,
         CATEGORY_CODE,
         CATEGORY_CN_NAME,
         ITEM_CODE,
         ITEM_CN_NAME,
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         VIEW_FLAG,
         VERSION_ID,
         L1_NAME,
         L2_NAME,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV0_PROD_LIST_EN_NAME,
         L4_CEG_CODE,
         L4_CEG_CN_NAME,
         L4_CEG_SHORT_CN_NAME
    FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'PFT_VIEW_INFO_D;';

EXECUTE V_SQL;
	
	--插入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '采购盈利本版本 = '||V_VERSION||' 数据备份完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');
  
 V_STEP_NUM:=V_STEP_NUM+1;
 V_SQL:= '
 --维表替换 
  ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'PFT_MID_VIEW_INFO_D	RENAME TO DM_FOC_'||V_TABLE_MID_PART||'PFT_VIEW_INFO_D_BAC ; 	
  ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'PFT_VIEW_INFO_D		RENAME TO DM_FOC_'||V_TABLE_MID_PART||'PFT_MID_VIEW_INFO_D ;
  ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'PFT_VIEW_INFO_D_BAC	RENAME TO DM_FOC_'||V_TABLE_MID_PART||'PFT_VIEW_INFO_D ;';

EXECUTE V_SQL;
  
	
	--插入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '采购盈利维表替换完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');
  
  
ELSIF F_COST_TYPE = 'P' AND F_DIMENSION_TYPE = 'D' THEN 
 --采购量纲历史表插数
V_STEP_NUM:= V_STEP_NUM+1;	

 V_SQL:= '
SELECT DISTINCT VERSION_ID FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'DMS_MID_VIEW_INFO_D LIMIT 1;';

EXECUTE V_SQL INTO V_VERSION;
 
 V_SQL:= '
 
DELETE FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'DMS_HIS_VIEW_INFO_D WHERE VERSION_NAME = '||V_VERSION_NAME||'
														AND VERSION_ID = '||V_VERSION||';
													
INSERT INTO FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'DMS_HIS_VIEW_INFO_D
  (VERSION_NAME,
   VERSION_ID,
   GROUP_LEVEL,
   LV0_PROD_RND_TEAM_CODE,
   LV0_PROD_RD_TEAM_CN_NAME,
   LV1_PROD_RND_TEAM_CODE,
   LV1_PROD_RD_TEAM_CN_NAME,
   LV2_PROD_RND_TEAM_CODE,
   LV2_PROD_RD_TEAM_CN_NAME,
   L3_CEG_CODE,
   L3_CEG_CN_NAME,
   L3_CEG_SHORT_CN_NAME,
   CATEGORY_CODE,
   CATEGORY_CN_NAME,
   ITEM_CODE,
   ITEM_CN_NAME,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   VIEW_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG,
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME,
   LV0_PROD_LIST_EN_NAME,
   LV3_PROD_RND_TEAM_CODE,
   LV3_PROD_RD_TEAM_CN_NAME,
   DIMENSION_CODE,
   DIMENSION_CN_NAME,
   DIMENSION_EN_NAME,
   DIMENSION_SUBCATEGORY_CODE,
   DIMENSION_SUBCATEGORY_CN_NAME,
   DIMENSION_SUBCATEGORY_EN_NAME,
   DIMENSION_SUB_DETAIL_CODE,
   DIMENSION_SUB_DETAIL_CN_NAME,
   DIMENSION_SUB_DETAIL_EN_NAME,
   L4_CEG_CODE,
   L4_CEG_CN_NAME,
   L4_CEG_SHORT_CN_NAME,
   SPART_CODE,
   SPART_CN_NAME)
  SELECT '||V_VERSION_NAME||' AS VERSION_NAME,
         VERSION_ID,
         GROUP_LEVEL,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         L3_CEG_CODE,
         L3_CEG_CN_NAME,
         L3_CEG_SHORT_CN_NAME,
         CATEGORY_CODE,
         CATEGORY_CN_NAME,
         ITEM_CODE,
         ITEM_CN_NAME,
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         VIEW_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV0_PROD_LIST_EN_NAME,
         LV3_PROD_RND_TEAM_CODE,
         LV3_PROD_RD_TEAM_CN_NAME,
         DIMENSION_CODE,
         DIMENSION_CN_NAME,
         DIMENSION_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUBCATEGORY_EN_NAME,
         DIMENSION_SUB_DETAIL_CODE,
         DIMENSION_SUB_DETAIL_CN_NAME,
         DIMENSION_SUB_DETAIL_EN_NAME,
         L4_CEG_CODE,
         L4_CEG_CN_NAME,
         L4_CEG_SHORT_CN_NAME,
         SPART_CODE,
         SPART_CN_NAME
    FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'DMS_VIEW_INFO_D;';

EXECUTE V_SQL;
	
	--插入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '采购量纲本版本 = '||V_VERSION||' 数据备份完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');
  
 V_STEP_NUM:=V_STEP_NUM+1;
 V_SQL:='
 --维表替换 
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'DMS_MID_VIEW_INFO_D	RENAME TO DM_FOC_'||V_TABLE_MID_PART||'DMS_VIEW_INFO_D_BAC ; 
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'DMS_VIEW_INFO_D		RENAME TO DM_FOC_'||V_TABLE_MID_PART||'DMS_MID_VIEW_INFO_D ;
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'DMS_VIEW_INFO_D_BAC	RENAME TO DM_FOC_'||V_TABLE_MID_PART||'DMS_VIEW_INFO_D ;';

EXECUTE V_SQL;
  
	--插入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '采购量纲维表替换完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');
  
  
ELSIF F_COST_TYPE = 'M' AND F_DIMENSION_TYPE = 'U' THEN 
 --制造通用历史表插数
V_STEP_NUM:= V_STEP_NUM+1;	
 V_SQL:='
SELECT DISTINCT VERSION_ID FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_MID_VIEW_INFO_D LIMIT 1;';

EXECUTE V_SQL INTO V_VERSION;
 
 V_SQL:='
 
DELETE FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_HIS_VIEW_INFO_D WHERE VERSION_NAME = '||V_VERSION_NAME||'
														 AND VERSION_ID = '||V_VERSION||';
													
INSERT INTO FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_HIS_VIEW_INFO_D
  (VERSION_NAME,
   VERSION_ID,
   GROUP_LEVEL,
   LV0_PROD_RND_TEAM_CODE,
   LV0_PROD_RD_TEAM_CN_NAME,
   LV1_PROD_RND_TEAM_CODE,
   LV1_PROD_RD_TEAM_CN_NAME,
   LV2_PROD_RND_TEAM_CODE,
   LV2_PROD_RD_TEAM_CN_NAME,
   SHIPPING_OBJECT_CODE,
   SHIPPING_OBJECT_CN_NAME,
   MANUFACTURE_OBJECT_CODE,
   MANUFACTURE_OBJECT_CN_NAME,
   ITEM_CODE,
   ITEM_CN_NAME,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   VIEW_FLAG,
   CALIBER_FLAG,
   LV3_PROD_RND_TEAM_CODE,
   LV3_PROD_RD_TEAM_CN_NAME,
   OVERSEA_FLAG,
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME,
   LV0_PROD_LIST_EN_NAME)
  SELECT '||V_VERSION_NAME||' AS VERSION_NAME,
         VERSION_ID,
         GROUP_LEVEL,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         SHIPPING_OBJECT_CODE,
         SHIPPING_OBJECT_CN_NAME,
         MANUFACTURE_OBJECT_CODE,
         MANUFACTURE_OBJECT_CN_NAME,
         ITEM_CODE,
         ITEM_CN_NAME,
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         VIEW_FLAG,
         CALIBER_FLAG,
         LV3_PROD_RND_TEAM_CODE,
         LV3_PROD_RD_TEAM_CN_NAME,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV0_PROD_LIST_EN_NAME
    FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_VIEW_INFO_D;';

EXECUTE V_SQL;
	
	--插入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '制造通用本版本 = '||V_VERSION||' 数据备份完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');
  
 V_STEP_NUM:=V_STEP_NUM+1;
 V_SQL:='
 --维表替换 
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_MID_VIEW_INFO_D	RENAME TO DM_FOC_'||V_TABLE_MID_PART||'MADE_VIEW_INFO_D_BAC ;     
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_VIEW_INFO_D		RENAME TO  DM_FOC_'||V_TABLE_MID_PART||'MADE_MID_VIEW_INFO_D ;
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_VIEW_INFO_D_BAC	RENAME TO DM_FOC_'||V_TABLE_MID_PART||'MADE_VIEW_INFO_D ;';

EXECUTE V_SQL;
  
	--插入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '制造通用维表替换完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');
  
ELSIF F_COST_TYPE = 'M' AND F_DIMENSION_TYPE = 'P' THEN 
 --制造盈利历史表插数
V_STEP_NUM:= V_STEP_NUM+1;	
 V_SQL:='
SELECT DISTINCT VERSION_ID FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_PFT_MID_VIEW_INFO_D LIMIT 1;';

EXECUTE V_SQL INTO V_VERSION;
 
V_SQL:='
DELETE FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_PFT_HIS_VIEW_INFO_D WHERE VERSION_NAME = '||V_VERSION_NAME||'
															 AND VERSION_ID = '||V_VERSION||';										

INSERT INTO FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_PFT_HIS_VIEW_INFO_D
  (VERSION_NAME,
   VERSION_ID,
   GROUP_LEVEL,
   LV0_PROD_RND_TEAM_CODE,
   LV0_PROD_RD_TEAM_CN_NAME,
   LV1_PROD_RND_TEAM_CODE,
   LV1_PROD_RD_TEAM_CN_NAME,
   LV2_PROD_RND_TEAM_CODE,
   LV2_PROD_RD_TEAM_CN_NAME,
   SHIPPING_OBJECT_CODE,
   SHIPPING_OBJECT_CN_NAME,
   MANUFACTURE_OBJECT_CODE,
   MANUFACTURE_OBJECT_CN_NAME,
   ITEM_CODE,
   ITEM_CN_NAME,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   VIEW_FLAG,
   CALIBER_FLAG,
   L1_NAME,
   L2_NAME,
   OVERSEA_FLAG,
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME,
   LV0_PROD_LIST_EN_NAME)
  SELECT '||V_VERSION_NAME||' AS VERSION_NAME,
         VERSION_ID,
         GROUP_LEVEL,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         SHIPPING_OBJECT_CODE,
         SHIPPING_OBJECT_CN_NAME,
         MANUFACTURE_OBJECT_CODE,
         MANUFACTURE_OBJECT_CN_NAME,
         ITEM_CODE,
         ITEM_CN_NAME,
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         VIEW_FLAG,
         CALIBER_FLAG,
         L1_NAME,
         L2_NAME,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME, 
         LV0_PROD_LIST_EN_NAME
    FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_PFT_VIEW_INFO_D;  ';

EXECUTE V_SQL;
	
	
	--插入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '制造盈利本版本 = '||V_VERSION||' 数据备份完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');
  
 V_STEP_NUM:=V_STEP_NUM+1;
  V_SQL:='
 --维表替换 
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_PFT_MID_VIEW_INFO_D	RENAME TO DM_FOC_'||V_TABLE_MID_PART||'MADE_PFT_VIEW_INFO_D_BAC ; 
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_PFT_VIEW_INFO_D      RENAME TO  DM_FOC_'||V_TABLE_MID_PART||'MADE_PFT_MID_VIEW_INFO_D   ;
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_PFT_VIEW_INFO_D_BAC  RENAME TO DM_FOC_'||V_TABLE_MID_PART||'MADE_PFT_VIEW_INFO_D    ;';

EXECUTE V_SQL;
  
	--插入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '制造盈利维表替换完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');
  
  
ELSIF F_COST_TYPE = 'M' AND F_DIMENSION_TYPE = 'D' THEN 
 --采购量纲历史表插数
V_STEP_NUM:= V_STEP_NUM+1;	
  V_SQL:='
SELECT DISTINCT VERSION_ID FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_DMS_MID_VIEW_INFO_D LIMIT 1;';

EXECUTE V_SQL INTO V_VERSION;
 
 V_SQL:='
 
DELETE FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_DMS_HIS_VIEW_INFO_D WHERE VERSION_NAME = '||V_VERSION_NAME||'
															 AND VERSION_ID = '||V_VERSION||';
													
--制造量纲
INSERT INTO FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_DMS_HIS_VIEW_INFO_D
  (VERSION_NAME,
   VERSION_ID,
   GROUP_LEVEL,
   LV0_PROD_RND_TEAM_CODE,
   LV0_PROD_RD_TEAM_CN_NAME,
   LV1_PROD_RND_TEAM_CODE,
   LV1_PROD_RD_TEAM_CN_NAME,
   LV2_PROD_RND_TEAM_CODE,
   LV2_PROD_RD_TEAM_CN_NAME,
   SHIPPING_OBJECT_CODE,
   SHIPPING_OBJECT_CN_NAME,
   MANUFACTURE_OBJECT_CODE,
   MANUFACTURE_OBJECT_CN_NAME,
   ITEM_CODE,
   ITEM_CN_NAME,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   VIEW_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG,
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME,
   LV0_PROD_LIST_EN_NAME,
   LV3_PROD_RND_TEAM_CODE,
   LV3_PROD_RD_TEAM_CN_NAME,
   DIMENSION_CODE,
   DIMENSION_CN_NAME,
   DIMENSION_EN_NAME,
   DIMENSION_SUBCATEGORY_CODE,
   DIMENSION_SUBCATEGORY_CN_NAME,
   DIMENSION_SUBCATEGORY_EN_NAME,
   DIMENSION_SUB_DETAIL_CODE,
   DIMENSION_SUB_DETAIL_CN_NAME,
   DIMENSION_SUB_DETAIL_EN_NAME,
   SPART_CODE,
   SPART_CN_NAME)
  SELECT '||V_VERSION_NAME||' AS VERSION_NAME,
         VERSION_ID,
         GROUP_LEVEL,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         SHIPPING_OBJECT_CODE,
         SHIPPING_OBJECT_CN_NAME,
         MANUFACTURE_OBJECT_CODE,
         MANUFACTURE_OBJECT_CN_NAME,
         ITEM_CODE,
         ITEM_CN_NAME,
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         VIEW_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV0_PROD_LIST_EN_NAME,
         LV3_PROD_RND_TEAM_CODE,
         LV3_PROD_RD_TEAM_CN_NAME,
         DIMENSION_CODE,
         DIMENSION_CN_NAME,
         DIMENSION_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUBCATEGORY_EN_NAME,
         DIMENSION_SUB_DETAIL_CODE,
         DIMENSION_SUB_DETAIL_CN_NAME,
         DIMENSION_SUB_DETAIL_EN_NAME,
         SPART_CODE,
         SPART_CN_NAME
    FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_DMS_VIEW_INFO_D;';

EXECUTE V_SQL;
	
	--插入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '制造量纲本版本 = '||V_VERSION||' 数据备份完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');
  
 V_STEP_NUM:=V_STEP_NUM+1;
   V_SQL:='
 --维表替换
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_DMS_MID_VIEW_INFO_D	RENAME TO DM_FOC_'||V_TABLE_MID_PART||'MADE_DMS_VIEW_INFO_D_BAC ; 
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_DMS_VIEW_INFO_D      RENAME TO  DM_FOC_'||V_TABLE_MID_PART||'MADE_DMS_MID_VIEW_INFO_D   ;
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_DMS_VIEW_INFO_D_BAC  RENAME TO DM_FOC_'||V_TABLE_MID_PART||'MADE_DMS_VIEW_INFO_D    ;';

EXECUTE V_SQL;
  
	--插入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '制造量纲维表替换完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');
  
  
ELSIF F_COST_TYPE = 'T' AND F_DIMENSION_TYPE = 'U' THEN 
 --总成本通用历史表插数
V_STEP_NUM:= V_STEP_NUM+1;	
 V_SQL:='
SELECT DISTINCT VERSION_ID FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_MID_VIEW_INFO_D LIMIT 1;';

EXECUTE V_SQL INTO V_VERSION;

 V_SQL:='
 
DELETE FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_HIS_VIEW_INFO_D WHERE VERSION_NAME = '||V_VERSION_NAME||'
														  AND VERSION_ID = '||V_VERSION||';
														 											 
INSERT INTO FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_HIS_VIEW_INFO_D
  (VERSION_NAME,
   VERSION_ID,
   GROUP_LEVEL,
   LV0_PROD_RND_TEAM_CODE,
   LV0_PROD_RD_TEAM_CN_NAME,
   LV1_PROD_RND_TEAM_CODE,
   LV1_PROD_RD_TEAM_CN_NAME,
   LV2_PROD_RND_TEAM_CODE,
   LV2_PROD_RD_TEAM_CN_NAME,
   LV3_PROD_RND_TEAM_CODE,
   LV3_PROD_RD_TEAM_CN_NAME,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   VIEW_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG,
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME,
   LV0_PROD_LIST_EN_NAME,
   PAGE_FLAG)
  SELECT '||V_VERSION_NAME||' AS VERSION_NAME,
         VERSION_ID,
         GROUP_LEVEL,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV3_PROD_RND_TEAM_CODE,
         LV3_PROD_RD_TEAM_CN_NAME,
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         VIEW_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV0_PROD_LIST_EN_NAME,
         PAGE_FLAG
    FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_VIEW_INFO_D;';

EXECUTE V_SQL;
  
		
		
	--插入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '总成本通用本版本 = '||V_VERSION||' 数据备份完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');
  
 V_STEP_NUM:=V_STEP_NUM+1;
    V_SQL:='
 --维表替换
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_MID_VIEW_INFO_D		RENAME TO DM_FOC_'||V_TABLE_MID_PART||'TOTAL_VIEW_INFO_D_BAC ;    
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_VIEW_INFO_D         RENAME TO  DM_FOC_'||V_TABLE_MID_PART||'TOTAL_MID_VIEW_INFO_D      ;
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_VIEW_INFO_D_BAC     RENAME TO DM_FOC_'||V_TABLE_MID_PART||'TOTAL_VIEW_INFO_D       ;';

EXECUTE V_SQL;
  
	--插入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '总成本通用维表替换完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');
  
  
ELSIF F_COST_TYPE = 'T' AND F_DIMENSION_TYPE = 'P' THEN 
 --总成本盈利历史表插数
V_STEP_NUM:= V_STEP_NUM+1;	
 V_SQL:='
SELECT DISTINCT VERSION_ID FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_PFT_MID_VIEW_INFO_D LIMIT 1;';

EXECUTE V_SQL INTO V_VERSION;
 
 V_SQL:='
DELETE FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_HIS_VIEW_INFO_D WHERE VERSION_NAME = '||V_VERSION_NAME||'
														 AND VERSION_ID = '||V_VERSION||';
	
INSERT INTO FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_PFT_HIS_VIEW_INFO_D
  (VERSION_NAME,
   VERSION_ID,
   GROUP_LEVEL,
   LV0_PROD_RND_TEAM_CODE,
   LV0_PROD_RD_TEAM_CN_NAME,
   LV1_PROD_RND_TEAM_CODE,
   LV1_PROD_RD_TEAM_CN_NAME,
   LV2_PROD_RND_TEAM_CODE,
   LV2_PROD_RD_TEAM_CN_NAME,
   L1_NAME,
   L2_NAME,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   VIEW_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG,
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME,
   LV0_PROD_LIST_EN_NAME,
   PAGE_FLAG)
  SELECT '||V_VERSION_NAME||' AS VERSION_NAME,
         VERSION_ID,
         GROUP_LEVEL,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         L1_NAME,
         L2_NAME,
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         VIEW_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV0_PROD_LIST_EN_NAME,
         PAGE_FLAG
    FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_PFT_VIEW_INFO_D;';

EXECUTE V_SQL;
		
	--插入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '总成本盈利本版本 = '||V_VERSION||' 数据备份完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');
  
 V_STEP_NUM:=V_STEP_NUM+1;
    V_SQL:='
 --维表替换
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_PFT_MID_VIEW_INFO_D	RENAME TO DM_FOC_'||V_TABLE_MID_PART||'TOTAL_PFT_VIEW_INFO_D_BAC ;
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_PFT_VIEW_INFO_D     RENAME TO  DM_FOC_'||V_TABLE_MID_PART||'TOTAL_PFT_MID_VIEW_INFO_D  ;
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_PFT_VIEW_INFO_D_BAC RENAME TO DM_FOC_'||V_TABLE_MID_PART||'TOTAL_PFT_VIEW_INFO_D   ;';

EXECUTE V_SQL;
  
	--插入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '总成本盈利维表替换完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');
  
ELSIF F_COST_TYPE = 'T' AND F_DIMENSION_TYPE = 'D' THEN 
 --总成本量纲历史表插数
V_STEP_NUM:= V_STEP_NUM+1;	
 V_SQL:='
SELECT DISTINCT VERSION_ID FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_DMS_MID_VIEW_INFO_D LIMIT 1;
  ';

EXECUTE V_SQL INTO V_VERSION;
 
  V_SQL:='
DELETE FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_DMS_HIS_VIEW_INFO_D WHERE VERSION_NAME = '||V_VERSION_NAME||'
															  AND VERSION_ID = '||V_VERSION||';
	
INSERT INTO FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_DMS_HIS_VIEW_INFO_D
  (VERSION_NAME,
	VERSION_ID,
   GROUP_LEVEL,
   LV0_PROD_RND_TEAM_CODE,
   LV0_PROD_RD_TEAM_CN_NAME,
   LV1_PROD_RND_TEAM_CODE,
   LV1_PROD_RD_TEAM_CN_NAME,
   LV2_PROD_RND_TEAM_CODE,
   LV2_PROD_RD_TEAM_CN_NAME,
   DIMENSION_CODE,
   DIMENSION_CN_NAME,
   DIMENSION_SUBCATEGORY_CODE,
   DIMENSION_SUBCATEGORY_CN_NAME,
   DIMENSION_SUB_DETAIL_CODE,
   DIMENSION_SUB_DETAIL_CN_NAME,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   VIEW_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG,
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME,
   LV0_PROD_LIST_EN_NAME,
   LV3_PROD_RND_TEAM_CODE,
   LV3_PROD_RD_TEAM_CN_NAME,
   DIMENSION_EN_NAME,
   DIMENSION_SUBCATEGORY_EN_NAME,
   DIMENSION_SUB_DETAIL_EN_NAME,
   PAGE_FLAG,
   SPART_CODE,
   SPART_CN_NAME)
  SELECT  '||V_VERSION_NAME||' AS VERSION_NAME,
		 VERSION_ID,
         GROUP_LEVEL,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         DIMENSION_CODE,
         DIMENSION_CN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUB_DETAIL_CODE,
         DIMENSION_SUB_DETAIL_CN_NAME,
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         VIEW_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV0_PROD_LIST_EN_NAME,
         LV3_PROD_RND_TEAM_CODE,
         LV3_PROD_RD_TEAM_CN_NAME,
         DIMENSION_EN_NAME,
         DIMENSION_SUBCATEGORY_EN_NAME,
         DIMENSION_SUB_DETAIL_EN_NAME,
         PAGE_FLAG,
         SPART_CODE,
         SPART_CN_NAME
    FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_DMS_VIEW_INFO_D;';

EXECUTE V_SQL;
		
	--插入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '总成本量纲本版本 = '||V_VERSION||' 数据备份完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');
  
  
 V_STEP_NUM:=V_STEP_NUM+1;
  V_SQL:='
 --维表替换
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_DMS_MID_VIEW_INFO_D	RENAME TO DM_FOC_'||V_TABLE_MID_PART||'TOTAL_DMS_VIEW_INFO_D_BAC ;
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_DMS_VIEW_INFO_D     RENAME TO DM_FOC_'||V_TABLE_MID_PART||'TOTAL_DMS_MID_VIEW_INFO_D  ;
ALTER TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_DMS_VIEW_INFO_D_BAC RENAME TO DM_FOC_'||V_TABLE_MID_PART||'TOTAL_DMS_VIEW_INFO_D   ;';

EXECUTE V_SQL;
  
	--插入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '总成本量纲维表替换完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');  
  
 END IF ;

  
  
  RETURN 'SUCCESS';

  
 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
 	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

 END; 

$$
/

