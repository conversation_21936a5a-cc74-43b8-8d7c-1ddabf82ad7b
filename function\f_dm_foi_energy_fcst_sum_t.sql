-- Name: f_dm_foi_energy_fcst_sum_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_energy_fcst_sum_t(f_caliber_flag character varying, f_version_id bigint DEFAULT NULL::integer, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$                
  /*
  创建时间：2024-02-02
  创建人  ：罗若文
  修改是按：2024年4月12日17点48分
  修改人：唐钦
  背景描述：数字能源预测数补齐
  参数描述：x_result_status ：是否成功
  事例    ：select FIN_DM_OPT_FOI.F_DM_FOI_ENERGY_FCST_SUM_T()
  
  */
  
 
--预估价表：FOI_DWK_GRP_PROCOST_PRICE_REL_I
--计划量表：DWK_PRO_PP_PLAN_DP_DETAIL_I
  
  DECLARE 
  V_SP_NAME VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOI_ENERGY_FCST_SUM_T';
  V_DML_ROW_COUNT    NUMBER DEFAULT 0;
  V_PERIOD_BEGIN     BIGINT := CAST(TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMM') AS BIGINT);  -- 开始月份（当月） 
  V_PERIOD_END       BIGINT := CAST((TO_CHAR(CURRENT_TIMESTAMP, 'YYYY') || 12) AS BIGINT);  -- 结束月份（当年12月）
  V_MAX_PURCH_PERIOD BIGINT;
  V_MAX_PURCH_PERIOD1 BIGINT;
  V_MAX_PURCH_PERIOD2 BIGINT;
  V_MAX_UNIT_PERIOD  BIGINT;
  V_STEP_NUM         BIGINT := 0;
  V_FROM_TABLE    VARCHAR(100);
  V_TO_TABLE      VARCHAR(100);
  V_CEG_TABLE       VARCHAR(100);
  V_PLAN_TABLE    VARCHAR(100);
  V_ACTUAL_TABLE  VARCHAR(100);
  V_CURRENT_FLAG  INT;
  V_VERSION_ID    INT;
  V_ITEM_CODE     VARCHAR(30);
  V_PERIOD_ID      VARCHAR(30);
  V_PURCH_ARRIVE  VARCHAR(30);
  V_ID_PARA               VARCHAR(20);
  V_ID              VARCHAR(100);
  V_VERSION_PARA1  VARCHAR(30);
  V_VERSION_PARA2   VARCHAR(20);
  V_SQL1          TEXT;
  V_SQL2          TEXT;
  V_SQL3          TEXT;
  V_EXCUTE_SQL      TEXT;
  
BEGIN
  x_result_status := '1';
  

  --0.日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => v_sp_name,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => v_sp_name || '开始执行');
   
    --判断入参是ICT还是数字能源
  IF f_caliber_flag = 'I' THEN 
    V_FROM_TABLE := 'DM_FOI_VIEW_INFO_T';
    V_TO_TABLE := 'DM_FOI_FCST_SUM_T';
    V_PLAN_TABLE := 'FOI_DWK_GRP_ICT_PURCH_EXPEND_I';
    V_ACTUAL_TABLE := 'DM_FOI_ITEM_SUP_AVG_T';
    V_ID_PARA := 'ID ,';
    V_ID := 'FIN_DM_OPT_FOI.DM_FOI_ITEM_FCST_SUM_S.NEXTVAL ,';
    V_VERSION_PARA1 := '';
    V_ITEM_CODE := 'SRC_ITEM_CODE';
    V_PERIOD_ID := 'SRC_PERIOD_ID';
    V_PURCH_ARRIVE := 'PURCH_ARRIVE_MON';
    
  SELECT CAST(MAX(SRC_PERIOD_ID) AS BIGINT)
    INTO V_MAX_PURCH_PERIOD
    FROM FIN_DM_OPT_FOI.FOI_DWK_GRP_ICT_PURCH_EXPEND_I; -- 取计划量表最大会计期
  SELECT CAST(MAX(PERIOD_ID) AS BIGINT)
    INTO V_MAX_UNIT_PERIOD
    FROM FIN_DM_OPT_FOI.FOI_DWK_GRP_PROCOST_PRICE_REL_I; -- 取预估价表最大会计期

    --判断入参是ICT还是数字能源
  ELSIF f_caliber_flag = 'E' THEN 
    V_FROM_TABLE := 'DM_FOI_ENERGY_VIEW_INFO_T';
    V_TO_TABLE := 'DM_FOI_ENERGY_FCST_SUM_T';
    V_PLAN_TABLE := 'DWK_PRO_PP_PLAN_DP_DETAIL_I';
    V_ACTUAL_TABLE := 'DM_FOI_ENERGY_ITEM_SUP_AVG_T';
    V_ID_PARA := '';
    V_ID := '';
    V_VERSION_PARA1 := 'VERSION_ID ,';
    V_VERSION_PARA2 := '';
    V_ITEM_CODE := 'ITEM_CODE';
    V_PERIOD_ID := 'DATA_BATCH';
    V_PURCH_ARRIVE := 'PURCH_ARRIVE_MONTH';
    
    SELECT CAST(MAX(SRC_PERIOD_ID) AS BIGINT)
    INTO V_MAX_PURCH_PERIOD1
    FROM FIN_DM_OPT_FOI.FOI_DWK_GRP_ICT_PURCH_EXPEND_I; -- 取计划量表最大会计期
    
    SELECT CAST(MAX(DATA_BATCH) AS BIGINT)
    INTO V_MAX_PURCH_PERIOD2
    FROM FIN_DM_OPT_FOI.DWK_PRO_PP_PLAN_DP_DETAIL_I; -- 取计划量表最大会计期
    
  SELECT CAST(MAX(PERIOD_ID) AS BIGINT)
    INTO V_MAX_UNIT_PERIOD
    FROM FIN_DM_OPT_FOI.FOI_DWK_GRP_PROCOST_PRICE_REL_I; -- 取预估价表最大会计期
    
  --判断入参版本号
  IF f_version_id IS  NULL  THEN 
      SELECT COUNT(1) INTO V_CURRENT_FLAG
    FROM
        FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T 
    WHERE
        VERSION = TO_CHAR(CURRENT_DATE, 'YYYYMM')||'-ITEM-Auto'
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ITEM';
        
    -- FLAG 不等于0，说明已有版本号，沿用        
        IF V_CURRENT_FLAG <> 0 THEN 
            SELECT VERSION_ID INTO V_VERSION_ID
            FROM
                FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T 
                WHERE
                    VERSION = TO_CHAR(CURRENT_DATE, 'YYYYMM')||'-ITEM-Auto'
                    AND DEL_FLAG = 'N'
                    AND STATUS = 1
                    AND UPPER(DATA_TYPE) = 'ITEM';
            
        ELSE 
            RETURN '没有找到版本号';
        END IF;
  ELSE
            V_VERSION_ID = f_version_id;
        END IF;
  
    --最后插入用
      V_VERSION_PARA2 := V_VERSION_ID ||',' ;  
ELSE 
      RETURN '输入的入参有误';
    END IF;
   

  -- 支持重跑，清除目标表的预测数据
  V_EXCUTE_SQL := 'TRUNCATE TABLE  '||V_TO_TABLE ;

  EXECUTE IMMEDIATE V_EXCUTE_SQL;
  
  --1.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 1,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'表的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => x_result_status,
   F_ERRBUF => 'SUCCESS');

  -- 取补齐的上一个月的历史数数据
V_SQL1:= ' WITH OPT_DIM_HIS_TMP AS
    (
     SELECT PERIOD_ID,                                  -- 会计期
            ITEM_CODE,                                  -- ITEM编码        
            ITEM_NAME,                                                            -- ITEM中文名称
            CATEGORY_CODE,                                                        -- 品类编码
            CATEGORY_NAME,                                                        -- 品类中文名称
            L4_CEG_CODE,                                                          -- 模块编码
            L4_CEG_SHORT_CN_NAME,                                                 -- 模块（GROUP LV4简称）
            L4_CEG_CN_NAME,                                                       -- 模块中文名称
            L3_CEG_CODE,                                                          -- 专家团编码
            L3_CEG_SHORT_CN_NAME,                                                 -- 专家团（GROUP LV3简称）
            L3_CEG_CN_NAME,                                                       -- 专家团中文名称
            L2_CEG_CODE,                                                          -- 采购组织编码
            L2_CEG_CN_NAME,                                                       -- 采购组织中文名称
            AVG_RECEIVE_AMT                               -- 均价
         FROM '||V_ACTUAL_TABLE||'       
         WHERE PERIOD_ID = CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP, -1),''YYYYMM'') AS BIGINT)
         AND GROUP_LEVEL = ''ITEM''
    )'; 
              
  -- 取出满足取数逻辑的计划量数据
  IF F_CALIBER_FLAG = 'I' THEN 
 V_SQL2 := ' ,
  FIN_DM_OPT_FOI_ITEM_QTY_TMP AS
   (SELECT I.'||V_PURCH_ARRIVE||' AS PURCH_ARRIVE_MONTH, -- 采购到货月份
           I.'||V_ITEM_CODE|| ' AS ITEM_CODE, -- ITEM编码
           I.ITEM_SUBTYPE_CODE, -- 品类编码
           --I.ITEM_SUBTYPE_CN_NAME, -- 品类中文名称
           SUM(I.PURCH_QTY) AS PURCH_QTY -- 采购数量卷积至ITEM层级
      FROM '||V_PLAN_TABLE||' I -- 数字能源采购计划量接口表
     INNER JOIN OPT_DIM_HIS_TMP DIM
        ON I.'||V_ITEM_CODE||' = DIM.ITEM_CODE
     WHERE I.'||V_PERIOD_ID||' = '||V_MAX_PURCH_PERIOD||'     -- 取最大会计期
       AND I.'||V_PURCH_ARRIVE||' BETWEEN '||V_PERIOD_BEGIN||' AND '||V_PERIOD_END||' -- 取规定月份的数据
       AND  I.'||V_ITEM_CODE||' IS NOT NULL
       AND I.ITEM_SUBTYPE_CODE IS NOT NULL
       AND I.DEL_FLAG = ''N''
     GROUP BY I.'||V_PURCH_ARRIVE||',
              I.'||V_ITEM_CODE||',
              I.ITEM_SUBTYPE_CODE
              --I.ITEM_SUBTYPE_CN_NAME
              ) ';
    ELSIF F_CALIBER_FLAG = 'E' THEN
V_SQL2:= ' ,
    FIN_DM_OPT_FOI_ITEM_QTY_TMP AS
   (SELECT I.PURCH_ARRIVE_MON AS PURCH_ARRIVE_MONTH, -- 采购到货月份
           I.SRC_ITEM_CODE as ITEM_CODE, -- ITEM编码
           I.ITEM_SUBTYPE_CODE, -- 品类编码
           SUM(I.PURCH_QTY) AS PURCH_QTY -- 采购数量卷积至ITEM层级
      FROM FIN_DM_OPT_FOI.FOI_DWK_GRP_ICT_PURCH_EXPEND_I I -- ICT采购计划量接口表
     INNER JOIN OPT_DIM_HIS_TMP DIM
        ON I.SRC_ITEM_CODE = DIM.ITEM_CODE
     WHERE I.SRC_PERIOD_ID = '||V_MAX_PURCH_PERIOD1||' -- 取最大会计期
       AND I.PURCH_ARRIVE_MON BETWEEN '||V_PERIOD_BEGIN||' AND '||V_PERIOD_END||' -- 取规定月份的数据
       AND I.SRC_ITEM_CODE IS NOT NULL
       AND I.ITEM_SUBTYPE_CODE IS NOT NULL
       AND I.DEL_FLAG = ''N''
       AND DIM.L3_CEG_CODE IN (''12251'',''12252'',''12253'',''12254'',''12256'',''12257'',''12274'',''16349'',''19382'',''16449'',''14029'',''19462'',''19484'') --集团代采专家团
     GROUP BY I.PURCH_ARRIVE_MON,
              I.SRC_ITEM_CODE,
              I.ITEM_SUBTYPE_CODE
              UNION ALL
    SELECT I.'||V_PURCH_ARRIVE||' AS PURCH_ARRIVE_MONTH, -- 采购到货月份
           I.'||V_ITEM_CODE|| ' AS ITEM_CODE, -- ITEM编码
           I.ITEM_SUBTYPE_CODE, -- 品类编码
           SUM(I.PURCH_QTY) AS PURCH_QTY -- 采购数量卷积至ITEM层级
      FROM '||V_PLAN_TABLE||' I -- 数字能源采购计划量接口表
     INNER JOIN OPT_DIM_HIS_TMP DIM
        ON I.'||V_ITEM_CODE||' = DIM.ITEM_CODE
     WHERE I.'||V_PERIOD_ID||' = '||V_MAX_PURCH_PERIOD2||'     -- 取最大会计期
       AND I.'||V_PURCH_ARRIVE||' BETWEEN '||V_PERIOD_BEGIN||' AND '||V_PERIOD_END||' -- 取规定月份的数据
       AND  I.'||V_ITEM_CODE||' IS NOT NULL
       AND I.ITEM_SUBTYPE_CODE IS NOT NULL
       AND I.DEL_FLAG = ''N''
       AND DIM.L3_CEG_CODE IN (''50050'',''50051'',''50053'',''50873'',''50874'',''15809'',''12321'',''13289'') --集团代采专家团
     GROUP BY I.'||V_PURCH_ARRIVE||',
              I.'||V_ITEM_CODE||',
              I.ITEM_SUBTYPE_CODE          
              ) ';
    ELSE NULL;
END IF;

  -- 取出满足取数逻辑的所有item的预估价数据
V_SQL3 := ' ,
  FIN_DM_OPT_FOI_ITEM_UNIT_TMP AS
   (SELECT I.FCST_PERIOD_ID, 
           I.ITEM_CODE, 
           I.RMB_UNIT_PRICE, 
           I.CURRENCY_CODE
      FROM FIN_DM_OPT_FOI.FOI_DWK_GRP_PROCOST_PRICE_REL_I I -- 采购预估价接口表
     INNER JOIN OPT_DIM_HIS_TMP DIM
        ON I.ITEM_CODE = DIM.ITEM_CODE
     WHERE I.FCST_PERIOD_ID BETWEEN '||V_PERIOD_BEGIN||' AND '||V_PERIOD_END||'
       AND I.PERIOD_ID = '||V_MAX_UNIT_PERIOD||' -- 取预估价表的最大会计期 
       AND I.ITEM_CODE IS NOT NULL
    )
  -- 取出所有满足item_code能与维度关联表的item_code匹配的计划量和单价,并根据有量无价的补齐规则对数据进行补齐
  ,
  OPT_ITEM_QTY_UNIT_TMP AS
       ( -- 预测数均本数据
        SELECT CAST (NVL(T1.PURCH_ARRIVE_MONTH,T2.FCST_PERIOD_ID) AS BIGINT) AS PERIOD_ID,   -- 采购到货月份
               NVL(T1.ITEM_CODE,T2.ITEM_CODE) AS ITEM_CODE,                 -- ITEM编码
               T3.ITEM_NAME,                                                            -- ITEM中文名称
               T3.CATEGORY_CODE,                                                        -- 品类编码
               T3.CATEGORY_NAME,                                                        -- 品类中文名称
               T3.L4_CEG_CODE,                                                          -- 模块编码
               T3.L4_CEG_SHORT_CN_NAME,                                                 -- 模块（GROUP LV4简称）
               T3.L4_CEG_CN_NAME,                                                       -- 模块中文名称
               T3.L3_CEG_CODE,                                                          -- 专家团编码
               T3.L3_CEG_SHORT_CN_NAME,                                                 -- 专家团（GROUP LV3简称）
               T3.L3_CEG_CN_NAME,                                                       -- 专家团中文名称
               T3.L2_CEG_CODE,                                                          -- 采购组织编码
               T3.L2_CEG_CN_NAME,                                                       -- 采购组织中文名称
               NVL(T1.PURCH_QTY,0) AS PURCH_QTY,                            -- 当计划量有数据的时候，取计划量数据，否则取0
               T2.RMB_UNIT_PRICE,
               DECODE(T2.RMB_UNIT_PRICE, NULL, 0, 1) AS NULL_FLAG           --空标识, 用于sum开窗累计
           FROM FIN_DM_OPT_FOI_ITEM_QTY_TMP T1                                                 
           FULL JOIN FIN_DM_OPT_FOI_ITEM_UNIT_TMP T2                                          --  计划量数据和预估价数据全外关联       
           ON T1.ITEM_CODE = T2.ITEM_CODE
           AND T1.PURCH_ARRIVE_MONTH = T2.FCST_PERIOD_ID
           INNER JOIN OPT_DIM_HIS_TMP T3
           ON T3.ITEM_CODE = NVL(T1.ITEM_CODE ,T2.ITEM_CODE)
        UNION ALL
         -- 补齐后上个月实际数均本数据
        SELECT PERIOD_ID,
               ITEM_CODE,
               ITEM_NAME,                                                            -- ITEM中文名称
               CATEGORY_CODE,                                                        -- 品类编码
               CATEGORY_NAME,                                                        -- 品类中文名称
               L4_CEG_CODE,                                                          -- 模块编码
               L4_CEG_SHORT_CN_NAME,                                                 -- 模块（GROUP LV4简称）
               L4_CEG_CN_NAME,                                                       -- 模块中文名称
               L3_CEG_CODE,                                                          -- 专家团编码
               L3_CEG_SHORT_CN_NAME,                                                 -- 专家团（GROUP LV3简称）
               L3_CEG_CN_NAME,                                                       -- 专家团中文名称
               L2_CEG_CODE,                                                          -- 采购组织编码
               L2_CEG_CN_NAME,                                                       -- 采购组织中文名称
               NULL AS PURCH_QTY, 
               AVG_RECEIVE_AMT AS RMB_UNIT_PRICE,
               DECODE(AVG_RECEIVE_AMT, NULL, 0, 1) AS NULL_FLAG           --空标识, 用于sum开窗累计
           FROM OPT_DIM_HIS_TMP
        )

  INSERT INTO '||V_TO_TABLE||'(
    '||V_ID_PARA|| V_VERSION_PARA1||' 
    YEAR, -- 年份
    PERIOD_ID, -- 会计期（YYYYMM）
    ITEM_CODE, -- ITEM编码
    ITEM_NAME, -- ITEM中文名称
    CATEGORY_CODE, -- 品类编码
    CATEGORY_NAME, -- 品类中文名称
    L4_CEG_CODE, -- 模块编码
    L4_CEG_SHORT_CN_NAME, -- 模块（GROUP LV4简称）
    L4_CEG_CN_NAME, -- 模块中文名称
    L3_CEG_CODE, -- 专家团编码
    L3_CEG_SHORT_CN_NAME, -- 专家团（GROUP LV3简称）
    L3_CEG_CN_NAME, -- 专家团中文名称
    L2_CEG_CODE, -- 生产采购编码
    L2_CEG_CN_NAME, -- 生产采购中文名称
    RECEIVE_QTY, -- 到货总数量
    RECEIVE_AMT_USD, -- 到货总金额(USD)
    RECEIVE_AMT_CNY, -- 到货总金额(CNY)         
    CREATED_BY, -- 创建人
    CREATION_DATE, -- 创建时间
    LAST_UPDATED_BY, -- 修改人
    LAST_UPDATE_DATE, -- 修改时间
    DEL_FLAG, -- 删除标识(未删除：N，已删除：Y)
    APPEND_FLAG, -- 是否补录(Y:是补录数据、N：真实数据)
    AVG_PRICE_CNY, -- 到货均价(CNY)
    AVG_PRICE_USD -- 到货均价(USD)
    ) 
  -- 取出全品类下所有ITEM，有价有量，有量无价的总数量和总金额，以及有价无量的均价
  SELECT 
  '||V_ID||V_VERSION_PARA2||'
         SUBSTR(T1.PERIOD_ID, 1, 4) AS YEAR,
         T1.PERIOD_ID, -- 会计期
         T1.ITEM_CODE, -- ITEM编码
         T1.ITEM_NAME, -- ITEM中文名称
         T1.CATEGORY_CODE, -- 品类编码
         T1.CATEGORY_NAME, -- 品类中文名称
         T1.L4_CEG_CODE,
         T1.L4_CEG_SHORT_CN_NAME,
         T1.L4_CEG_CN_NAME,
         T1.L3_CEG_CODE,
         T1.L3_CEG_SHORT_CN_NAME,
         T1.L3_CEG_CN_NAME,
         T1.L2_CEG_CODE,
         T1.L2_CEG_CN_NAME,
         T1.PURCH_QTY AS RECEIVE_QTY, -- 采购数量 
         '''' AS RECEIVE_AMT_USD, -- 预测总额（USD）
         NVL(T1.PURCH_QTY * T1.RMB_UNIT_PRICE,0) AS RECEIVE_AMT_CNY, -- 预测总额（RMB）
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         CASE
           WHEN T1.PURCH_QTY = 0 AND T1.RMB_UNIT_PRICE > 0 THEN
            ''Y''
           ELSE
            ''N''
         END AS APPEND_FLAG,
         FIRST_VALUE(T1.RMB_UNIT_PRICE) OVER(PARTITION BY T1.ITEM_CODE,T1.AVG_AMT_FLAG ORDER BY T1.PERIOD_ID) AS AVG_PRICE_CNY, -- 人民币单价
         '''' AS AVG_PRICE_USD -- 美元单价
    FROM (
            SELECT PERIOD_ID,
               ITEM_CODE,
               ITEM_NAME,                                                    
               CATEGORY_CODE,                                                
               CATEGORY_NAME,                                                
               L4_CEG_CODE,                                                  
               L4_CEG_SHORT_CN_NAME,                                         
               L4_CEG_CN_NAME,                                               
               L3_CEG_CODE,                                                  
               L3_CEG_SHORT_CN_NAME,                                         
               L3_CEG_CN_NAME,                                               
               L2_CEG_CODE,                                                  
               L2_CEG_CN_NAME,                                               
               PURCH_QTY, 
               RMB_UNIT_PRICE,
               SUM(NULL_FLAG) OVER(PARTITION BY ITEM_CODE ORDER BY PERIOD_ID) AS AVG_AMT_FLAG
           FROM OPT_ITEM_QTY_UNIT_TMP
         ) T1 
         WHERE T1.PERIOD_ID >= CAST(TO_CHAR(CURRENT_TIMESTAMP,''YYYYMM'') AS BIGINT)';

    V_EXCUTE_SQL := V_SQL1||V_SQL2||V_SQL3;

    EXECUTE IMMEDIATE V_EXCUTE_SQL;

  --2.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC  => '插入预测数到'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => x_result_status,
   F_ERRBUF => 'SUCCESS');
   
-- 收集信息
 V_EXCUTE_SQL := 'ANALYSE '||V_TO_TABLE ;
    EXECUTE IMMEDIATE V_EXCUTE_SQL;
    
  --3.日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME      => V_SP_NAME,
   F_STEP_NUM     => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME || '运行结束');
   
 return 'SUCCESS';

    
    EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := 0;
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

