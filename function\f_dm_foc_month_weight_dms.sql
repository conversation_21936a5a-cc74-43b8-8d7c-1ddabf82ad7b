-- Name: f_dm_foc_month_weight_dms; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_month_weight_dms(f_industry_flag character varying, f_dimension_type character varying, f_keystr character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/***************************************************************************************************************************************************************
最近修改时间: 2024年6月13日17点15分
修改人   ：黄心蕊 hwx1187045
修改内容： 202407版本 新增IAS部分,及通用颗粒度和量纲颗粒度IAS新增LV4层级
修改时间：2024年4月16日10点36分
修改人	：黄心蕊 hwx1187045
修改内容： 202405版本 新增数字能源部分,及量纲增加COA层级
创建时间：2023-03-27
创建人  ：黄心蕊 hwx1187045
修改时间：2023-12-18
修改人  ：黄心蕊 hwx1187045
修改时间：2024-03-06
修改人  ：黄心蕊 hwx1187045
修改描述： 202403版本 新增PARENT_CN_NAME
背景描述：月度分析-权重表数据初始化
修改时间：2024-03-27
修改人   ：黄心蕊 hwx1187045
修改内容： 20240327 修改版本号取数逻辑
参数描述：参数一(f_keystr)：绝密数据解密密钥串
          参数二(f_item_version)：通用版本号
          参数三(F_DIMENSION_TYPE)：维度类型（U：通用/P：盈利颗粒度/D：量纲颗粒度）
          参数四(X_RESULT_STATUS)：运行状态返回值 ‘1’为成功，‘0’为失败
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_MONTH_WEIGHT('U','密钥串'); --通用颗粒度一个版本的数据
          SELECT FIN_DM_OPT_FOI.F_DM_FOC_MONTH_WEIGHT('F','密钥串'); --盈利颗粒度一个版本的数据
          SELECT FIN_DM_OPT_FOI.F_DM_FOC_MONTH_WEIGHT('D','密钥串'); --量纲颗粒度一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                      VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_MONTH_WEIGHT_DMS';	--DMS修改
  V_VERSION                      BIGINT;
  V_STEP_NUM                     BIGINT := 0; --函数步骤号
  V_EXCEPTION_FLAG               VARCHAR(20) := '0'; --异常定点
  V_KEYSTR                       VARCHAR(100) := F_KEYSTR;
  V_PERIOD_YEAR                  VARCHAR(20) := YEAR(CURRENT_DATE) - 1 || '-' ||
                                                YEAR(CURRENT_DATE);
  V_DIMENSION_TYPE               VARCHAR(2) := F_DIMENSION_TYPE;
  V_BASE_AMT_TEMP                TEXT := ''; --金额临时表
  V_LV3_PROD_RND_TEAM_CODE       TEXT := ''; --LV3CODE字段名
  V_LV3_PROD_RD_TEAM_CN_NAME     TEXT := ''; --LV3NAME字段名
  V_PROD_LV3_CODE                TEXT := ''; --除LV3外，其他重量级团队LV3CODE查询字段
  V_L1_NAME                      TEXT := ''; --盈利颗粒度L1名称字段
  V_L2_NAME                      TEXT := ''; --盈利颗粒度L2名称字段
  V_TOP_ITEM_INFO_T              TEXT := ''; --规格品维取数表
  V_MONTH_ITEM_AMT_T             TEXT := ''; --金额取数表
  V_PROD_PROFITS_NAME            TEXT := ''; --重量级团队盈利颗粒度字段
  V_PROD_L1_NAME                 TEXT := ''; --重量级团队l1字段
  V_PROD_L2_NAME                 TEXT := ''; --重量级团队l2字段
  V_PROFITS_NAME                 TEXT := ''; --盈利颗粒度维字段
  V_SQL_LV3_PROD_RND_TEAM_CODE   TEXT := ''; --LV3CODE查询字段
  V_SQL_LV3_PROD_RD_TEAM_CN_NAME TEXT := ''; --LV3NAME查询字段
  V_SQL_L1_NAME                  TEXT := ''; --盈利颗粒度L1名称查询字段
  V_SQL_L2_NAME                  TEXT := ''; --盈利颗粒度L2名称查询字段
  V_SQL_PROFITS_NAME             TEXT := ''; --盈利颗粒度维判断逻辑
  V_SQL_PROD_RND_TEAM_CODE       TEXT := ''; --重量级团队CODE判断逻辑
  V_SQL_PROD_RND_TEAM_CN_NAME    TEXT := ''; --重量级团队NAME判断逻辑
  V_JION_LV3_PROD_RND_TEAM_CODE  TEXT := ''; --重量级团队CODE关联逻辑
  V_JOIN_L1_NAME                 TEXT := ''; --盈利颗粒度L1关联逻辑
  V_JOIN_L2_NAME                 TEXT := ''; --盈利颗粒度L2关联逻辑
  V_CEG_PARENT_CODE              TEXT := ''; --专家团上层级取数逻辑
  V_SQL_NEW                      TEXT := ''; --7月新增层级卷积逻辑
  V_SQL_NEW_F                    TEXT := ''; --7月新增层级卷积逻辑
  V_LV2_VIEW                     TEXT := ''; --LV2视角逻辑
  V_LV1_VIEW                     TEXT := ''; --LV1视角逻辑
  V_LV0_VIEW                     TEXT := ''; --LV0视角逻辑  
  V_SEQUENCE                     TEXT := ''; --序列
  V_DECRYP_YEAR_AMT_TEMP         TEXT := ''; --解密临时表
  V_SQL_WEIGHT                   TEXT := ''; --权重计算逻辑
  V_MID_TABLE                    TEXT := ''; --中间表
  V_TARGET_TABLE                 TEXT := ''; --结果表
  V_SQL                          TEXT := ''; --执行语句
  V_SQL_TEST                     TEXT := ''; --执行语句测试

  V_DIMENSION_CODE                TEXT := ''; --量纲颗粒度
  V_DIMENSION_CN_NAME             TEXT := '';
  V_DIMENSION_EN_NAME             TEXT := '';
  V_DIMENSION_SUBCATEGORY_CODE    TEXT := ''; --量纲子类
  V_DIMENSION_SUBCATEGORY_CN_NAME TEXT := '';
  V_DIMENSION_SUBCATEGORY_EN_NAME TEXT := '';
  V_DIMENSION_SUB_DETAIL_CODE     TEXT := ''; --量纲子类明细
  V_DIMENSION_SUB_DETAIL_CN_NAME  TEXT := '';
  V_DIMENSION_SUB_DETAIL_EN_NAME  TEXT := '';
  V_DMS_CODE                      TEXT := ''; --量纲粒度
  V_DMS_CN_NAME                   TEXT := ''; --量纲粒度

  V_JOIN_DIMENSION_CODE             TEXT := ''; --量纲关联条件
  V_JOIN_DIMENSION_SUBCATEGORY_CODE TEXT := ''; --量纲子类关联条件
  V_JOIN_DIMENSION_SUB_DETAIL_CODE  TEXT := ''; --子类明细关联条件

  V_SQL_DIMENSION_CODE                TEXT := ''; --量纲查询表字段
  V_SQL_DIMENSION_CN_NAME             TEXT := ''; --量纲查询表字段
  V_SQL_DIMENSION_EN_NAME             TEXT := ''; --量纲查询表字段
  V_SQL_DIMENSION_SUBCATEGORY_CODE    TEXT := ''; --量纲子类查询表字段
  V_SQL_DIMENSION_SUBCATEGORY_CN_NAME TEXT := ''; --量纲子类查询表字段
  V_SQL_DIMENSION_SUBCATEGORY_EN_NAME TEXT := ''; --量纲子类查询表字段
  V_SQL_DIMENSION_SUB_DETAIL_CODE     TEXT := ''; --子类明细查询表字段
  V_SQL_DIMENSION_SUB_DETAIL_CN_NAME  TEXT := ''; --子类明细查询表字段
  V_SQL_DIMENSION_SUB_DETAIL_EN_NAME  TEXT := ''; --子类明细查询表字段
  V_SQL_DMS_CODE                      TEXT := '';
  V_SQL_DMS_CN_NAME                   TEXT := '';
  V_SQL_L2_NEW                        TEXT := '';
  V_SQL_L1_NEW                        TEXT := '';
  V_SQL_L0_NEW                        TEXT := '';

  V_PROD_DMS_CODE                      TEXT := '';
  V_PROD_DMS_CN_NAME                   TEXT := '';
  V_PROD_DIMENSION_CODE                TEXT := '';
  V_PROD_DIMENSION_CN_NAME             TEXT := '';
  V_PROD_DIMENSION_SUBCATEGORY_CODE    TEXT := '';
  V_PROD_DIMENSION_SUBCATEGORY_CN_NAME TEXT := '';
  V_PROD_DIMENSION_SUB_DETAIL_CODE     TEXT := '';
  V_PROD_DIMENSION_SUB_DETAIL_CN_NAME  TEXT := '';

  --202401版本新增SPART层级
  V_SPART_CODE         TEXT := ''; --量纲颗粒度
  V_SPART_CN_NAME      TEXT := '';
  V_JOIN_SPART_CODE    TEXT := ''; --量纲关联条件
  V_SQL_SPART_CODE     TEXT := ''; --量纲查询表字段
  V_SQL_SPART_CN_NAME  TEXT := ''; --量纲查询表字段
  V_PROD_SPART_CODE    TEXT := '';
  V_PROD_SPART_CN_NAME TEXT := '';

  --202405版本 数字能源新增COA层级
  V_COA_PART         TEXT := '';
  V_SQL_COA_PART     TEXT := '';
  V_PROD_COA_PART    TEXT := '';
  V_JOIN_COA_CODE    TEXT := '';
  V_COA_SQL_NEW      TEXT := '';
  V_COA_SQL_NEW_F    TEXT := '';
  V_DIMENSION_PARENT TEXT := '';
  V_TO_REV_TABLE     TEXT := '';

  --202407版本 IAS新增LV4层级
  V_LV4_PART          TEXT := '';	--LV4字段
  V_SQL_LV4_PART      TEXT := '';	--LV4字段
  V_JOIN_LV4_CODE     TEXT := '';	--LV4关联
  V_LV4_SQL_NEW       TEXT := '';	--新增LV4层级金额计算逻辑
  V_LV4_SQL_NEW_F     TEXT := '';	--新增LV4层级下拉框多选场景下金额计算逻辑
  V_PROD_LV4_CODE     TEXT := '';	--LV3及以上层级时 LV4层级字段置空
  
  V_REV_VIEW_FLAG     TEXT := '';	--反向视角对正向视角取值视角 ICT与数字能源取VIEW3 IAS取VIEW7
  V_LV4_REV_VIEW6_SQL TEXT := '';	--反向视角6，LV4计算逻辑
  V_LV4_REV_VIEW5_SQL TEXT := '';	--反向视角5，LV4计算逻辑
  V_LV4_REV_VIEW4_SQL TEXT := '';	--反向视角4，LV4计算逻辑
  V_REV_ITEM_PARENT   TEXT := '';	--反向视角时ITEM父级 ICT与数字能源为LV3 IAS为LV4
  
BEGIN 

X_RESULT_STATUS := '1';

--日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

V_EXCEPTION_FLAG := '1';  
--版本号入参判断，当入参为空，取TOP规格品清单最新版本号
IF F_ITEM_VERSION IS NULL THEN
/*
SELECT VERSION_ID INTO V_VERSION
  FROM FIN_DM_OPT_FOI.DM_FOC_TOP_ITEM_INFO_T
   ORDER BY LAST_UPDATE_DATE DESC
 LIMIT 1
;*/
	IF F_INDUSTRY_FLAG = 'I' THEN
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
	 --20240327 修改版本号取数逻辑
	ELSIF F_INDUSTRY_FLAG = 'E' THEN	--202405版本 新增数字能源部分
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
		 
	ELSIF F_INDUSTRY_FLAG = 'IAS' THEN	--202407版本 新增IAS部分
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
	END IF ;

--入参不为空，则以入参为版本号
ELSE V_VERSION := F_ITEM_VERSION;
END IF;
  
  --写入日志
  V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行数据产业项目标识：'||F_INDUSTRY_FLAG||'，颗粒度：'||V_DIMENSION_TYPE||'，以及版本号：'||V_VERSION ,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

  IF V_DIMENSION_TYPE = 'U' THEN
    /*通用颗粒度部分定义*/
  
    V_SQL_PROD_RND_TEAM_CODE       := '         
         CASE T2.VIEW_FLAG
           WHEN ''0'' THEN T2.LV0_PROD_RND_TEAM_CODE
           WHEN ''1'' THEN T2.LV1_PROD_RND_TEAM_CODE
           WHEN ''2'' THEN T2.LV2_PROD_RND_TEAM_CODE
           ELSE T2.LV3_PROD_RND_TEAM_CODE
         END AS PROD_RND_TEAM_CODE,';
    V_SQL_PROD_RND_TEAM_CN_NAME    := '
         CASE T2.VIEW_FLAG
           WHEN ''0'' THEN T2.LV0_PROD_RD_TEAM_CN_NAME
           WHEN ''1'' THEN T2.LV1_PROD_RD_TEAM_CN_NAME
           WHEN ''2'' THEN T2.LV2_PROD_RD_TEAM_CN_NAME
           ELSE T2.LV3_PROD_RD_TEAM_CN_NAME
         END AS PROD_RND_TEAM_CN_NAME,';
  
    /*表定义*/
	  IF F_INDUSTRY_FLAG = 'I' THEN 
	  /*表定义*/
		V_DECRYP_YEAR_AMT_TEMP    := 'DM_DECRYP_UNI_YEAR_AMT_TEMP';
		V_BASE_AMT_TEMP            := 'DM_YEARS_UNI_AMT_TEMP';
		V_TOP_ITEM_INFO_T          := 'FIN_DM_OPT_FOI.DM_FOC_TOP_ITEM_INFO_T';
		V_MONTH_ITEM_AMT_T         := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T';
		V_MID_TABLE                := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_WEIGHT_T';
		V_TARGET_TABLE             := 'FIN_DM_OPT_FOI.DM_FOC_MONTH_WEIGHT_T';
		V_TO_REV_TABLE				:='FIN_DM_OPT_FOI.DM_FOC_REV_MONTH_WEIGHT_T';
		
		/*反向视角部分定义*/
		V_REV_VIEW_FLAG				:= 3;	--ICT反向视角从视角3取数
		V_REV_ITEM_PARENT			:= 'LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,
										LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,
		';									--ICT反向视角中,ITEM父级为LV3
		
	  ELSIF F_INDUSTRY_FLAG = 'E' THEN  --202405版本 新增数字能源部分来源数据
	  /*表定义*/
		V_DECRYP_YEAR_AMT_TEMP     := 'DM_ENERGY_DECRYP_UNI_YEAR_AMT_TEMP';
		V_BASE_AMT_TEMP            := 'DM_ENERGY_YEARS_UNI_AMT_TEMP';
		V_TOP_ITEM_INFO_T          := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOP_ITEM_INFO_T';
		V_MONTH_ITEM_AMT_T         := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_T';
		V_MID_TABLE                := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_WEIGHT_T';
		V_TARGET_TABLE             := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MONTH_WEIGHT_T';
		V_TO_REV_TABLE				:='FIN_DM_OPT_FOI.DM_FOC_ENERGY_REV_MONTH_WEIGHT_T';
		
		/*反向视角部分定义*/
		V_REV_VIEW_FLAG				:= 3;	--数字能源反向视角从视角3取数
		V_REV_ITEM_PARENT			:= 'LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,
										LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,
		';									--数字能源反向视角中,ITEM父级为LV3
		
	  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN  --202407版本 新增IAS数据
	  /*表定义*/
		V_DECRYP_YEAR_AMT_TEMP     := 'DM_IAS_DECRYP_UNI_YEAR_AMT_TEMP';
		V_BASE_AMT_TEMP            := 'DM_IAS_YEARS_UNI_AMT_TEMP';
		V_TOP_ITEM_INFO_T          := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOP_ITEM_INFO_T';
		V_MONTH_ITEM_AMT_T         := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_T';
		V_MID_TABLE                := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_WEIGHT_T';
		V_TARGET_TABLE             := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MONTH_WEIGHT_T';
		V_TO_REV_TABLE				:='FIN_DM_OPT_FOI.DM_FOC_IAS_REV_MONTH_WEIGHT_T';
		
		/*字段定义*/
		V_LV4_PART			:= 'LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME,';		--202407版本 IAS新增LV4层级
		V_SQL_LV4_PART		:= 'T2.LV4_PROD_RND_TEAM_CODE,T2.LV4_PROD_RD_TEAM_CN_NAME,';
		V_JOIN_LV4_CODE		:= 'AND NVL(T1.LV4_PROD_RND_TEAM_CODE,4) = NVL(T2.LV4_PROD_RND_TEAM_CODE,4)';
		
		V_SQL_PROD_RND_TEAM_CODE       := '         
			 CASE T2.VIEW_FLAG
			   WHEN ''0'' THEN T2.LV0_PROD_RND_TEAM_CODE
			   WHEN ''1'' THEN T2.LV1_PROD_RND_TEAM_CODE
			   WHEN ''2'' THEN T2.LV2_PROD_RND_TEAM_CODE
			   WHEN ''7'' THEN T2.LV4_PROD_RND_TEAM_CODE		--202407版本 IAS新增视角7 IAS新增LV4层级
			 ELSE T2.LV3_PROD_RND_TEAM_CODE
			 END AS PROD_RND_TEAM_CODE,';
		V_SQL_PROD_RND_TEAM_CN_NAME    := '
		   CASE T2.VIEW_FLAG
			   WHEN ''0'' THEN T2.LV0_PROD_RD_TEAM_CN_NAME
			   WHEN ''1'' THEN T2.LV1_PROD_RD_TEAM_CN_NAME
			   WHEN ''2'' THEN T2.LV2_PROD_RD_TEAM_CN_NAME
			   WHEN ''7'' THEN T2.LV4_PROD_RD_TEAM_CN_NAME		--202407版本 IAS新增视角7 IAS新增LV4层级
		   ELSE T2.LV3_PROD_RD_TEAM_CN_NAME
		   END AS PROD_RND_TEAM_CN_NAME,';
		   
		V_PROD_LV4_CODE                := ' '''' AS LV4_PROD_RND_TEAM_CODE,
											'''' AS LV4_PROD_RD_TEAM_CN_NAME,
											'; --202407版本 IAS新增LV4层级
		
		/*特殊逻辑定义*/
		V_LV4_SQL_NEW := '
			  --LV4层级收敛
			  SELECT VIEW_FLAG,
					 LV4_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
					 LV4_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
					 LV0_PROD_RND_TEAM_CODE,
					 LV1_PROD_RND_TEAM_CODE,
					 LV2_PROD_RND_TEAM_CODE,
					 LV3_PROD_RND_TEAM_CODE,
					 LV4_PROD_RND_TEAM_CODE,	--202407版本 IAS新增LV4层级
					 LV4_PROD_RD_TEAM_CN_NAME,
					 LV4_PROD_RND_TEAM_CODE AS GROUP_CODE,
					 LV4_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
					 ''LV4'' AS GROUP_LEVEL,
					 SUM(YEARS_AMT) AS YEARS_AMT,
					 LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,
					 LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
					 CALIBER_FLAG,
					 OVERSEA_FLAG,
					 LV0_PROD_LIST_CODE,
					 LV0_PROD_LIST_CN_NAME
				FROM ' || V_BASE_AMT_TEMP || '
			   WHERE VIEW_FLAG = 7 	--202407版本 IAS新增视角7
			   AND  GROUP_LEVEL=''ITEM''
			   GROUP BY VIEW_FLAG,
						LV2_PROD_RND_TEAM_CODE,
						LV0_PROD_RND_TEAM_CODE,
						LV1_PROD_RND_TEAM_CODE,
						LV3_PROD_RND_TEAM_CODE,
						LV3_PROD_RD_TEAM_CN_NAME,
						LV4_PROD_RND_TEAM_CODE,
						LV4_PROD_RD_TEAM_CN_NAME,	--202407版本 IAS新增LV4层级
						CALIBER_FLAG,
						OVERSEA_FLAG,
						LV0_PROD_LIST_CODE,
						LV0_PROD_LIST_CN_NAME
			  UNION ALL
		';
		
		V_LV4_SQL_NEW_F		 := '
		  --LV4层级收敛
		  SELECT VIEW_FLAG,
				 LV4_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
				 LV4_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
				 LV0_PROD_RND_TEAM_CODE,
				 LV1_PROD_RND_TEAM_CODE,
				 LV2_PROD_RND_TEAM_CODE,
				 LV3_PROD_RND_TEAM_CODE,
				 LV4_PROD_RND_TEAM_CODE,	--202407版本 IAS新增LV4层级
				 LV4_PROD_RD_TEAM_CN_NAME,
				 LV4_PROD_RND_TEAM_CODE AS GROUP_CODE,	--202407版本 IAS新增LV4层级
				 LV4_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
				 ''F_LV4'' AS GROUP_LEVEL,
				 SUM(YEARS_AMT) AS YEARS_AMT,
				 LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,
				 LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
				 CALIBER_FLAG,
				 OVERSEA_FLAG,
				 LV0_PROD_LIST_CODE,
				 LV0_PROD_LIST_CN_NAME ,
				 GROUP_CODE,GROUP_CN_NAME
			FROM ' || V_BASE_AMT_TEMP || '
		   WHERE VIEW_FLAG = 7 --202407版本 IAS新增视角7
		   AND  GROUP_LEVEL=''ITEM''
		   GROUP BY VIEW_FLAG,
					LV2_PROD_RND_TEAM_CODE,
					LV0_PROD_RND_TEAM_CODE,
					LV1_PROD_RND_TEAM_CODE,
					LV3_PROD_RND_TEAM_CODE,
					LV3_PROD_RD_TEAM_CN_NAME,
					LV4_PROD_RND_TEAM_CODE ,
					LV4_PROD_RD_TEAM_CN_NAME ,
					CALIBER_FLAG,
					OVERSEA_FLAG,
					LV0_PROD_LIST_CODE,
					LV0_PROD_LIST_CN_NAME ,
					GROUP_CODE,GROUP_CN_NAME 
		  UNION ALL
		';
		
		
		/*202407版本 IAS反向视角变量定义*/
		V_REV_VIEW_FLAG				:= 7;	--IAS反向视角从视角7取数
		V_REV_ITEM_PARENT			:= 'LV4_PROD_RND_TEAM_CODE AS PARENT_CODE,
										LV4_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,
		';	
		V_LV4_REV_VIEW6_SQL := '
			--LV4
			SELECT 6,
				   TOP_CATEGORY_CODE,
				   TOP_CATEGORY_CN_NAME,
				   LV4_PROD_RND_TEAM_CODE AS GROUP_CODE,
				   LV4_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
				   ''LV4'' AS GROUP_LEVEL,
				   SUM(YEARS_AMT) AS YEARS_AMT,
				   LV3_PROD_RND_TEAM_CODE AS PARENT_CODE, 
				   LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,  
				   TOP_L3_CEG_CODE,
				   TOP_L3_CEG_SHORT_CN_NAME,
				   TOP_L4_CEG_CODE,
				   TOP_L4_CEG_SHORT_CN_NAME,   
				   TOP_CATEGORY_CODE,
				   TOP_CATEGORY_CN_NAME,
				   CALIBER_FLAG,
				   OVERSEA_FLAG,
				   LV0_PROD_LIST_CODE,
				   LV0_PROD_LIST_CN_NAME
			  FROM '||V_BASE_AMT_TEMP||'
			  WHERE VIEW_FLAG = 7	
				AND GROUP_LEVEL=''ITEM''
			 GROUP BY LV4_PROD_RND_TEAM_CODE,
					  LV4_PROD_RD_TEAM_CN_NAME,
					  LV3_PROD_RND_TEAM_CODE,
					  LV3_PROD_RD_TEAM_CN_NAME,
					  TOP_L3_CEG_CODE,
					  TOP_L3_CEG_SHORT_CN_NAME,
					  TOP_L4_CEG_CODE,
					  TOP_L4_CEG_SHORT_CN_NAME,   
					  TOP_CATEGORY_CODE,
					  TOP_CATEGORY_CN_NAME,
					  CALIBER_FLAG,
					  OVERSEA_FLAG,
					  LV0_PROD_LIST_CODE,
					  LV0_PROD_LIST_CN_NAME
					  
			UNION ALL

		';


		V_LV4_REV_VIEW5_SQL := '
			--LV4
			SELECT 5,
				   TOP_L4_CEG_CODE,
				   TOP_L4_CEG_SHORT_CN_NAME,
				   LV4_PROD_RND_TEAM_CODE AS GROUP_CODE,
				   LV4_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
				   ''LV4'' AS GROUP_LEVEL,
				   SUM(YEARS_AMT) AS YEARS_AMT,
				   LV3_PROD_RND_TEAM_CODE AS PARENT_CODE, 
				   LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, 
				   TOP_L3_CEG_CODE,
				   TOP_L3_CEG_SHORT_CN_NAME,
				   TOP_L4_CEG_CODE,
				   TOP_L4_CEG_SHORT_CN_NAME,   
				   CALIBER_FLAG,
				   OVERSEA_FLAG,
				   LV0_PROD_LIST_CODE,
				   LV0_PROD_LIST_CN_NAME
			  FROM '||V_BASE_AMT_TEMP||'
			  WHERE VIEW_FLAG = 7
				AND GROUP_LEVEL=''ITEM''
			 GROUP BY LV4_PROD_RND_TEAM_CODE,
					  LV4_PROD_RD_TEAM_CN_NAME,
					  LV3_PROD_RND_TEAM_CODE,
					  LV3_PROD_RD_TEAM_CN_NAME,
					  TOP_L3_CEG_CODE,
					  TOP_L3_CEG_SHORT_CN_NAME,
					  TOP_L4_CEG_CODE,
					  TOP_L4_CEG_SHORT_CN_NAME,   
					  CALIBER_FLAG,
					  OVERSEA_FLAG,
					  LV0_PROD_LIST_CODE,
					  LV0_PROD_LIST_CN_NAME
					  
			UNION ALL

		';
				  
		V_LV4_REV_VIEW4_SQL :='
			--LV4
			SELECT 4,
				   TOP_L3_CEG_CODE,
				   TOP_L3_CEG_SHORT_CN_NAME,
				   LV4_PROD_RND_TEAM_CODE AS GROUP_CODE,
				   LV4_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
				   ''LV4'' AS GROUP_LEVEL,
				   SUM(YEARS_AMT) AS YEARS_AMT,
				   LV3_PROD_RND_TEAM_CODE AS PARENT_CODE, 
				   LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,
				   TOP_L3_CEG_CODE,
				   TOP_L3_CEG_SHORT_CN_NAME, 
				   CALIBER_FLAG,
				   OVERSEA_FLAG,
				   LV0_PROD_LIST_CODE,
				   LV0_PROD_LIST_CN_NAME
			  FROM '||V_BASE_AMT_TEMP||'
			  WHERE VIEW_FLAG = 7
				AND GROUP_LEVEL=''ITEM''
			 GROUP BY LV3_PROD_RND_TEAM_CODE,
					  LV3_PROD_RD_TEAM_CN_NAME,
					  LV4_PROD_RND_TEAM_CODE,
					  LV4_PROD_RD_TEAM_CN_NAME,
					  TOP_L3_CEG_CODE,
					  TOP_L3_CEG_SHORT_CN_NAME, 
					  CALIBER_FLAG,
					  OVERSEA_FLAG,
					  LV0_PROD_LIST_CODE,
					  LV0_PROD_LIST_CN_NAME
					  
			UNION ALL 
		';
		
	  END IF;
  
    /*字段值定义*/
    V_LV3_PROD_RND_TEAM_CODE       := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME     := 'LV3_PROD_RD_TEAM_CN_NAME,';
    V_SQL_LV3_PROD_RND_TEAM_CODE   := 'T2.LV3_PROD_RND_TEAM_CODE,';
    V_SQL_LV3_PROD_RD_TEAM_CN_NAME := 'T2.LV3_PROD_RD_TEAM_CN_NAME,';
    V_CEG_PARENT_CODE              := 'PROD_RND_TEAM_CODE AS PARENT_CODE,
										PROD_RND_TEAM_CN_NAME AS PARENT_CN_NAME ,';   
								--202403版本 新增PARENT_CN_NAME
    
    V_PROD_LV3_CODE                := ' '''' AS LV3_PROD_RND_TEAM_CODE,'; 
    V_SQL_WEIGHT                   := '
         CASE
           WHEN GROUP_LEVEL IN (''ITEM'',''CATEGORY'',''MODL'',''CEG'') THEN
            YEARS_AMT / NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY VIEW_FLAG,CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,GROUP_LEVEL,
                  LV0_PROD_RND_TEAM_CODE,
                  LV1_PROD_RND_TEAM_CODE,
                  LV2_PROD_RND_TEAM_CODE,
                  PROD_RND_TEAM_CODE,
                  PARENT_CODE),0)
           ELSE
            YEARS_AMT / NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY VIEW_FLAG, CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,GROUP_LEVEL,
                  PARENT_CODE), 0)
         END AS WEIGHT_RATE, ';
  
    /*条件定义*/
    V_JION_LV3_PROD_RND_TEAM_CODE := 'AND NVL(T1.LV3_PROD_RND_TEAM_CODE,3) = NVL(T2.LV3_PROD_RND_TEAM_CODE,3)';
    V_LV2_VIEW                    := ' WHERE VIEW_FLAG IN(2,3,7)';	--202407版本 IAS新增视角7
    V_LV1_VIEW                    := ' WHERE VIEW_FLAG IN(1,2,3,7)';
    V_LV0_VIEW                    := ' WHERE VIEW_FLAG IN(0,1,2,3,7)';
    /*新增逻辑定义*/
    V_SQL_NEW := V_LV4_SQL_NEW||	--202407版本 IAS新增LV4层级
	'	
      --LV3层级收敛
      SELECT VIEW_FLAG,
             LV3_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
             LV3_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
             LV0_PROD_RND_TEAM_CODE,
             LV1_PROD_RND_TEAM_CODE,
             LV2_PROD_RND_TEAM_CODE,
             LV3_PROD_RND_TEAM_CODE,
			 '||V_PROD_LV4_CODE||'	--202407版本 IAS新增LV4层级
             LV3_PROD_RND_TEAM_CODE AS GROUP_CODE,
             LV3_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
             ''LV3'' AS GROUP_LEVEL,
             SUM(YEARS_AMT) AS YEARS_AMT,
             LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
			 LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME
        FROM ' || V_BASE_AMT_TEMP || '
       WHERE VIEW_FLAG IN (3,7) /*7月新增视角*/
							--202407版本 IAS新增视角7
       AND  GROUP_LEVEL=''ITEM''
       GROUP BY VIEW_FLAG,
                LV2_PROD_RND_TEAM_CODE,
                LV0_PROD_RND_TEAM_CODE,
                LV1_PROD_RND_TEAM_CODE,
                LV3_PROD_RND_TEAM_CODE,
                LV3_PROD_RD_TEAM_CN_NAME,
				LV2_PROD_RD_TEAM_CN_NAME, --202403版本 新增PARENT_CN_NAME
                CALIBER_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME
      UNION ALL
';

    V_SQL_NEW_F := V_LV4_SQL_NEW_F ||	--202407版本 IAS新增LV4层级
	'
      --LV3层级收敛
      SELECT VIEW_FLAG,
             LV3_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
             LV3_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
             LV0_PROD_RND_TEAM_CODE,
             LV1_PROD_RND_TEAM_CODE,
             LV2_PROD_RND_TEAM_CODE,
             LV3_PROD_RND_TEAM_CODE,
			 '||V_PROD_LV4_CODE||'	--202407版本 IAS新增LV4层级
             LV3_PROD_RND_TEAM_CODE AS GROUP_CODE,
             LV3_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
             ''F_LV3'' AS GROUP_LEVEL,
             SUM(YEARS_AMT) AS YEARS_AMT,
             LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
			 LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME ,
             GROUP_CODE,GROUP_CN_NAME
        FROM ' || V_BASE_AMT_TEMP || '
       WHERE VIEW_FLAG IN (3,7) /*7月新增视角*/
							--202407版本 IAS新增视角7
       AND  GROUP_LEVEL=''ITEM''
       GROUP BY VIEW_FLAG,
                LV2_PROD_RND_TEAM_CODE,
                LV0_PROD_RND_TEAM_CODE,
                LV1_PROD_RND_TEAM_CODE,
                LV3_PROD_RND_TEAM_CODE,
                LV3_PROD_RD_TEAM_CN_NAME,
				LV2_PROD_RD_TEAM_CN_NAME, --202403版本 新增PARENT_CN_NAME
                CALIBER_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME ,
                GROUP_CODE,GROUP_CN_NAME 
      UNION ALL
';

  ELSIF V_DIMENSION_TYPE = 'P' THEN
    /*盈利颗粒度部分定义*/
  
    /*表定义*/
  
	  IF F_INDUSTRY_FLAG = 'I' THEN 
	  /*表定义*/
		V_DECRYP_YEAR_AMT_TEMP     := 'DM_DECRYP_PFT_YEAR_AMT_TEMP';
		V_BASE_AMT_TEMP            := 'DM_YEARS_PFT_AMT_TEMP';
		V_TOP_ITEM_INFO_T          := 'FIN_DM_OPT_FOI.DM_FOC_PFT_TOP_ITEM_INFO_T';
		V_MONTH_ITEM_AMT_T         := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_ITEM_T';
		V_MID_TABLE                := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_WEIGHT_T';
		V_TARGET_TABLE             := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MONTH_WEIGHT_T';
		
	  ELSIF F_INDUSTRY_FLAG = 'E' THEN  --202405版本 新增数字能源部分来源数据
	  /*表定义*/
		V_DECRYP_YEAR_AMT_TEMP     := 'DM_ENERGY_PFT_DECRYP_UNI_YEAR_AMT_TEMP';
		V_BASE_AMT_TEMP            := 'DM_ENERGY_PFT_YEARS_UNI_AMT_TEMP';
		V_TOP_ITEM_INFO_T          := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_TOP_ITEM_INFO_T';
		V_MONTH_ITEM_AMT_T         := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_MID_MONTH_ITEM_T';
		V_MID_TABLE                := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_MID_MONTH_WEIGHT_T';
		V_TARGET_TABLE             := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_MONTH_WEIGHT_T';
		
	  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN  --202407版本 新增IAS数据
	  /*表定义*/
		V_DECRYP_YEAR_AMT_TEMP     := 'DM_IAS_PFT_DECRYP_UNI_YEAR_AMT_TEMP';
		V_BASE_AMT_TEMP            := 'DM_IAS_PFT_YEARS_UNI_AMT_TEMP';
		V_TOP_ITEM_INFO_T          := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_TOP_ITEM_INFO_T';
		V_MONTH_ITEM_AMT_T         := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_MID_MONTH_ITEM_T';
		V_MID_TABLE                := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_MID_MONTH_WEIGHT_T';
		V_TARGET_TABLE             := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_MONTH_WEIGHT_T';
		
	  END IF;
  
    /*字段值定义*/
    V_L1_NAME                   := 'L1_NAME,';
    V_L2_NAME                   := 'L2_NAME,';
    V_PROFITS_NAME              := 'PROFITS_NAME,';
    V_PROD_PROFITS_NAME         := ' '''' AS PROFITS_NAME,';
    V_PROD_L1_NAME              := ' '''' AS L1_NAME,';
    V_PROD_L2_NAME              := ' '''' AS L2_NAME,';
    V_SQL_L1_NAME               := 'T2.L1_NAME,';
    V_SQL_L2_NAME               := 'T2.L2_NAME,';
    --V_SEQUENCE                  := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MONTH_WEIGHT_S.NEXTVAL';
    V_SQL_PROFITS_NAME          := '
       CASE T2.VIEW_FLAG
         WHEN ''3'' THEN T2.L1_NAME
         WHEN ''4'' THEN T2.L2_NAME
         ELSE ''''
       END AS PROFITS_NAME,';
    V_SQL_PROD_RND_TEAM_CODE    := '         
       CASE T2.VIEW_FLAG
         WHEN ''0'' THEN T2.LV0_PROD_RND_TEAM_CODE
         WHEN ''1'' THEN T2.LV1_PROD_RND_TEAM_CODE
         ELSE T2.LV2_PROD_RND_TEAM_CODE
       END AS PROD_RND_TEAM_CODE,';
    V_SQL_PROD_RND_TEAM_CN_NAME := '
       CASE T2.VIEW_FLAG
         WHEN ''0'' THEN T2.LV0_PROD_RD_TEAM_CN_NAME
         WHEN ''1'' THEN T2.LV1_PROD_RD_TEAM_CN_NAME
         ELSE T2.LV2_PROD_RD_TEAM_CN_NAME
       END AS PROD_RND_TEAM_CN_NAME,';
    V_CEG_PARENT_CODE           := '
       CASE
         WHEN VIEW_FLAG IN (0, 1, 2) THEN PROD_RND_TEAM_CODE
         ELSE PROFITS_NAME
       END AS PARENT_CODE,
	   CASE
         WHEN VIEW_FLAG IN (0, 1, 2) THEN PROD_RND_TEAM_CN_NAME
         ELSE PROFITS_NAME
       END AS PARENT_CN_NAME,'; --202403版本 新增PARENT_CN_NAME
    V_SQL_WEIGHT                := '
        CASE
         WHEN GROUP_LEVEL IN (''ITEM'',''CATEGORY'',''MODL'',''CEG'') THEN
          YEARS_AMT / NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,VIEW_FLAG,GROUP_LEVEL,
                                  PROD_RND_TEAM_CODE,
                                  NVL(L1_NAME,3),NVL(L2_NAME,4),
                                  PROFITS_NAME,
                                  PARENT_CODE),0)
         WHEN GROUP_LEVEL IN (''L1'',''L2'') THEN
          YEARS_AMT / NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,VIEW_FLAG,GROUP_LEVEL,
                                  PROD_RND_TEAM_CODE,
                                  PARENT_CODE),0)
         ELSE
          YEARS_AMT /
          NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,GROUP_LEVEL,VIEW_FLAG, PARENT_CODE), 0)
       END AS WEIGHT_RATE, ';
  
    /*条件定义*/
    V_JOIN_L1_NAME := ' AND NVL(T1.L1_NAME,3) = NVL(T2.L1_NAME,3)';
    V_JOIN_L2_NAME := ' AND NVL(T1.L2_NAME,4) = NVL(T2.L2_NAME,4)';
    V_LV2_VIEW     := ' WHERE VIEW_FLAG IN(2,3,4)';
    V_LV1_VIEW     := ' WHERE VIEW_FLAG IN(1,2,3,4)';
    V_LV0_VIEW     := ' WHERE VIEW_FLAG IN(0,1,2,3,4)';
    /*7月版本新增逻辑定义*/
    V_SQL_NEW := '
      --盈利颗粒度L2收敛
      SELECT VIEW_FLAG,
             LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
             LV2_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
             LV0_PROD_RND_TEAM_CODE,
             LV1_PROD_RND_TEAM_CODE,
             LV2_PROD_RND_TEAM_CODE,
             L2_NAME AS PROFITS_NAME,
             L1_NAME AS L1_NAME,
             L2_NAME AS L2_NAME,
             L2_NAME AS GROUP_CODE,
             L2_NAME AS GROUP_CN_NAME,
             ''L2'' AS GROUP_LEVEL,
             SUM(YEARS_AMT) AS YEARS_AMT,
             L1_NAME AS PARENT_CODE,
			 L1_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME
        FROM ' || V_BASE_AMT_TEMP || '
       WHERE VIEW_FLAG = 4 AND GROUP_LEVEL=''ITEM''
       GROUP BY VIEW_FLAG,
                LV2_PROD_RND_TEAM_CODE,
                LV2_PROD_RD_TEAM_CN_NAME,
                LV0_PROD_RND_TEAM_CODE,
                LV1_PROD_RND_TEAM_CODE,
                L1_NAME,
                L2_NAME,
                CALIBER_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME
      UNION ALL
      --盈利颗粒度L1收敛
      SELECT VIEW_FLAG,
             LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
             LV2_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
             LV0_PROD_RND_TEAM_CODE,
             LV1_PROD_RND_TEAM_CODE,
             LV2_PROD_RND_TEAM_CODE,
             L1_NAME AS PROFITS_NAME,
             L1_NAME AS L1_NAME,
             '''' AS L2_NAME,
             L1_NAME AS GROUP_CODE,
             L1_NAME AS GROUP_CN_NAME,
             ''L1'' AS GROUP_LEVEL,
             SUM(YEARS_AMT) AS YEARS_AMT,
             LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
			 LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME
        FROM ' || V_BASE_AMT_TEMP || '
       WHERE VIEW_FLAG IN (3, 4) AND GROUP_LEVEL=''ITEM''
       GROUP BY VIEW_FLAG,
                LV2_PROD_RND_TEAM_CODE,
                LV2_PROD_RD_TEAM_CN_NAME,
                LV0_PROD_RND_TEAM_CODE,
                LV1_PROD_RND_TEAM_CODE,
                L1_NAME,
                CALIBER_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME
      UNION ALL
';

    V_SQL_NEW_F := '
      --盈利颗粒度L2收敛
      SELECT VIEW_FLAG,
             LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
             LV2_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
             LV0_PROD_RND_TEAM_CODE,
             LV1_PROD_RND_TEAM_CODE,
             LV2_PROD_RND_TEAM_CODE,
             L2_NAME AS PROFITS_NAME,
             L1_NAME AS L1_NAME,
             L2_NAME AS L2_NAME,
             L2_NAME AS GROUP_CODE,
             L2_NAME AS GROUP_CN_NAME,
             ''F_L2'' AS GROUP_LEVEL,
             SUM(YEARS_AMT) AS YEARS_AMT,
             L1_NAME AS PARENT_CODE,
			 L1_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME ,
             GROUP_CODE,GROUP_CN_NAME 
        FROM ' || V_BASE_AMT_TEMP || '
       WHERE VIEW_FLAG = 4 AND GROUP_LEVEL=''ITEM''
       GROUP BY VIEW_FLAG,
                LV2_PROD_RND_TEAM_CODE,
                LV2_PROD_RD_TEAM_CN_NAME,
                LV0_PROD_RND_TEAM_CODE,
                LV1_PROD_RND_TEAM_CODE,
                L1_NAME,
                L2_NAME,
                CALIBER_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
				GROUP_CODE,GROUP_CN_NAME 
      UNION ALL
      --盈利颗粒度L1收敛
      SELECT VIEW_FLAG,
             LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
             LV2_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
             LV0_PROD_RND_TEAM_CODE,
             LV1_PROD_RND_TEAM_CODE,
             LV2_PROD_RND_TEAM_CODE,
             L1_NAME AS PROFITS_NAME,
             L1_NAME AS L1_NAME,
             '''' AS L2_NAME,
             L1_NAME AS GROUP_CODE,
             L1_NAME AS GROUP_CN_NAME,
             ''F_L1'' AS GROUP_LEVEL,
             SUM(YEARS_AMT) AS YEARS_AMT,
             LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
			 LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME ,
             GROUP_CODE,GROUP_CN_NAME
        FROM ' || V_BASE_AMT_TEMP || '
       WHERE VIEW_FLAG IN (3, 4) AND GROUP_LEVEL=''ITEM''
       GROUP BY VIEW_FLAG,
                LV2_PROD_RND_TEAM_CODE,
                LV2_PROD_RD_TEAM_CN_NAME,
                LV0_PROD_RND_TEAM_CODE,
                LV1_PROD_RND_TEAM_CODE,
                L1_NAME,
                CALIBER_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME ,
                GROUP_CODE,GROUP_CN_NAME
      UNION ALL
';
   ELSIF V_DIMENSION_TYPE = 'D' THEN
  /*量纲颗粒度部分定义*/

	  IF F_INDUSTRY_FLAG = 'I' THEN 
	  /*表定义*/
		V_DECRYP_YEAR_AMT_TEMP         := 'DM_DECRYP_DMS_YEAR_AMT_TEMP';
		V_BASE_AMT_TEMP                := 'DM_YEARS_DMS_AMT_TEMP';
		/*
		V_MID_TABLE                    := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_WEIGHT_T';
		V_TARGET_TABLE                 := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_WEIGHT_T';
		V_TOP_ITEM_INFO_T              := 'FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_ITEM_INFO_T';
		V_MONTH_ITEM_AMT_T             := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T';
		*/
		
		V_MID_TABLE                    := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_WEIGHT_T_DMS'; --DMS修改
		V_TARGET_TABLE                 := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_WEIGHT_T';  --DMS修改
		V_TOP_ITEM_INFO_T              := 'FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_ITEM_INFO_T';  --DMS修改
		V_MONTH_ITEM_AMT_T             := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T_DMS';  --DMS修改
		
		V_DIMENSION_PARENT:=' PROD_RND_TEAM_CODE AS PARENT_CODE,
							  PROD_RND_TEAM_CN_NAME AS PARENT_CN_NAME,
		';
		
		V_SQL_PROD_RND_TEAM_CODE       := '         
		 CASE  WHEN T2.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN T2.LV1_PROD_RND_TEAM_CODE
			   WHEN T2.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T2.LV2_PROD_RND_TEAM_CODE
			 ELSE T2.LV3_PROD_RND_TEAM_CODE
			 END AS PROD_RND_TEAM_CODE,'; --202401版本新增SPART层级 加入9，10，11三个视角
		V_SQL_PROD_RND_TEAM_CN_NAME    := '
		   CASE WHEN T2.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN T2.LV1_PROD_RD_TEAM_CN_NAME
				WHEN T2.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T2.LV2_PROD_RD_TEAM_CN_NAME
		   ELSE T2.LV3_PROD_RD_TEAM_CN_NAME
		   END AS PROD_RND_TEAM_CN_NAME,'; --202401版本新增SPART层级 加入9，10，11三个视角
		
		V_SQL_WEIGHT                := '
		CASE
		 WHEN GROUP_LEVEL IN (''ITEM'',''CATEGORY'',''MODL'',''CEG'') THEN
		  YEARS_AMT / NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,VIEW_FLAG,GROUP_LEVEL,
								  PROD_RND_TEAM_CODE,
								  NVL(DIMENSION_CODE,''D1''),NVL(DIMENSION_SUBCATEGORY_CODE,''D2''),NVL(DIMENSION_SUB_DETAIL_CODE,''D3''),
								  NVL(SPART_CODE,''D4''),
								  NVL(DMS_CODE,''DD''),
								  PARENT_CODE),0)	--采购层级通过重量级团队,量纲,量纲子类,量纲子类明细,SPART分组
			--202401版本新增SPART层级
		 WHEN GROUP_LEVEL IN (''SPART'') THEN
		  YEARS_AMT / NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,VIEW_FLAG,GROUP_LEVEL,
								  PROD_RND_TEAM_CODE,NVL(DIMENSION_CODE,''D1''),NVL(DIMENSION_SUBCATEGORY_CODE,''D2''),
								  PARENT_CODE),0)	--SPART层级通过重量级团队,量纲,量纲子类,量纲子类明细分组
		 WHEN GROUP_LEVEL IN (''SUB_DETAIL'') THEN
		  YEARS_AMT / NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,VIEW_FLAG,GROUP_LEVEL,
								  PROD_RND_TEAM_CODE,NVL(DIMENSION_CODE,''D1''),
								  PARENT_CODE),0)	--量纲子类明细层级通过重量级团队,量纲,量纲子类分组
		 WHEN GROUP_LEVEL IN (''SUBCATEGORY'',''DIMENSION'') THEN
		  YEARS_AMT / NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,VIEW_FLAG,GROUP_LEVEL,
								  PROD_RND_TEAM_CODE,
								  PARENT_CODE),0)	--量纲子类及量纲通过重量级团队及其父级分组
		 ELSE
		  YEARS_AMT /
		  NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,GROUP_LEVEL,VIEW_FLAG, PARENT_CODE), 0)
													--ELSE重量级团队层级通过其父级分组
	   END AS WEIGHT_RATE, ';
		
	  ELSIF F_INDUSTRY_FLAG = 'E' THEN  --202405版本 新增数字能源部分来源数据
	  /*表定义*/
		V_DECRYP_YEAR_AMT_TEMP     := 'DM_ENERGY_DMS_DECRYP_UNI_YEAR_AMT_TEMP';
		V_BASE_AMT_TEMP            := 'DM_ENERGY_DMS_YEARS_UNI_AMT_TEMP';
		/*
		V_TOP_ITEM_INFO_T          := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_TOP_ITEM_INFO_T';
		V_MONTH_ITEM_AMT_T         := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MID_MONTH_ITEM_T';
		V_MID_TABLE                := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MID_MONTH_WEIGHT_T';
		V_TARGET_TABLE             := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MONTH_WEIGHT_T';*/
		
		V_TOP_ITEM_INFO_T          := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_TOP_ITEM_INFO_T';
        V_MONTH_ITEM_AMT_T         := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MID_MONTH_ITEM_T_DMS';		--DMS修改
        V_MID_TABLE                := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MID_MONTH_WEIGHT_T_DMS';	--DMS修改
        V_TARGET_TABLE             := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MONTH_WEIGHT_T';
		
		V_COA_PART					:='COA_CODE,COA_CN_NAME,'; 
		V_SQL_COA_PART				:='T2.COA_CODE,T2.COA_CN_NAME,'; 
		V_PROD_COA_PART				:= ' '''' AS COA_CODE,'''' AS COA_CODE ,';
		
		V_JOIN_COA_CODE :='AND NVL(T1.COA_CODE,''D5'') = NVL(T2.COA_CODE,''D5'') ';
		
		V_DIMENSION_PARENT:='
		DECODE(VIEW_FLAG,''12'',COA_CODE,PROD_RND_TEAM_CODE) AS PARENT_CODE,
		DECODE(VIEW_FLAG,''12'',COA_CN_NAME,PROD_RND_TEAM_CN_NAME) AS PARENT_CN_NAME,
		';	--202405版本 数字能源下12视角量纲父级为COA，其他视角均为其上重量级团队
		
		V_SQL_PROD_RND_TEAM_CODE       := '         
		 CASE  WHEN T2.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN T2.LV1_PROD_RND_TEAM_CODE
			   WHEN T2.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T2.LV2_PROD_RND_TEAM_CODE
			 ELSE T2.LV3_PROD_RND_TEAM_CODE
			 END AS PROD_RND_TEAM_CODE,'; --202401版本新增SPART层级 加入9，10，11三个视角
		V_SQL_PROD_RND_TEAM_CN_NAME    := '
		   CASE WHEN T2.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN T2.LV1_PROD_RD_TEAM_CN_NAME
				WHEN T2.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T2.LV2_PROD_RD_TEAM_CN_NAME
		   ELSE T2.LV3_PROD_RD_TEAM_CN_NAME
		   END AS PROD_RND_TEAM_CN_NAME,'; --202401版本新增SPART层级 加入9，10，11三个视角
		
		--权重计算
		V_SQL_WEIGHT                := '
		CASE
		 WHEN GROUP_LEVEL IN (''ITEM'',''CATEGORY'',''MODL'',''CEG'') THEN
		  YEARS_AMT / NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,VIEW_FLAG,GROUP_LEVEL,
								  PROD_RND_TEAM_CODE,
								  NVL(DIMENSION_CODE,''D1''),NVL(DIMENSION_SUBCATEGORY_CODE,''D2''),NVL(DIMENSION_SUB_DETAIL_CODE,''D3''),
								  NVL(SPART_CODE,''D4''),NVL(COA_CODE,''D5''),
								  NVL(DMS_CODE,''DD''),
								  PARENT_CODE),0)	--采购层级通过重量级团队,量纲,量纲子类,量纲子类明细,SPART,COA分组
			--202401版本新增SPART层级
		 WHEN GROUP_LEVEL IN (''SPART'') THEN
		  YEARS_AMT / NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,VIEW_FLAG,GROUP_LEVEL,
								  PROD_RND_TEAM_CODE,NVL(DIMENSION_CODE,''D1''),NVL(DIMENSION_SUBCATEGORY_CODE,''D2''),
								  NVL(COA_CODE,''D5''),
								  PARENT_CODE),0)	--SPART层级通过重量级团队,量纲,量纲子类,量纲子类明细,COA分组
		 WHEN GROUP_LEVEL IN (''SUB_DETAIL'') THEN
		  YEARS_AMT / NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,VIEW_FLAG,GROUP_LEVEL,
								  PROD_RND_TEAM_CODE,NVL(DIMENSION_CODE,''D1''),
								  NVL(COA_CODE,''D5''),
								  PARENT_CODE),0)	--量纲子类明细层级通过重量级团队,量纲,量纲子类,COA分组
		 WHEN GROUP_LEVEL IN (''SUBCATEGORY'') THEN
		  YEARS_AMT / NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,VIEW_FLAG,GROUP_LEVEL,
								  PROD_RND_TEAM_CODE,NVL(DIMENSION_CODE,''D1''),
								  NVL(COA_CODE,''D5''),
								  PARENT_CODE),0)	--量纲子类层级通过重量级团队,量纲,COA分组
		 WHEN GROUP_LEVEL IN (''DIMENSION'',''COA'') THEN
		  YEARS_AMT / NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,VIEW_FLAG,GROUP_LEVEL,
								  PROD_RND_TEAM_CODE,
								  PARENT_CODE),0)	--量纲及COA层级通过重量级团队及其父级分组
		 ELSE
		  YEARS_AMT /
		  NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,GROUP_LEVEL,VIEW_FLAG, PARENT_CODE), 0)
													--ELSE重量级团队层级通过其父级分组
	   END AS WEIGHT_RATE, ';
	   
		--202405版本 数字能源新增COA层级
		V_COA_SQL_NEW :='
		  SELECT VIEW_FLAG,
				 PROD_RND_TEAM_CODE,
				 PROD_RND_TEAM_CN_NAME,
				 LV0_PROD_RND_TEAM_CODE,
				 LV1_PROD_RND_TEAM_CODE,
				 LV2_PROD_RND_TEAM_CODE,         
				 LV3_PROD_RND_TEAM_CODE,
				 COA_CODE AS DMS_CODE,
				 COA_CN_NAME AS DMS_CN_NAME,
				  '||V_COA_PART||'	--202405版本 数字能源新增COA层级
				 '''' ,
				 '''' ,
				 '''' ,
				 '''' ,
				 '''' ,
				 '''' ,
				 '''',
				 '''',
				 COA_CODE AS GROUP_CODE,
				 COA_CN_NAME AS GROUP_CN_NAME,
				 ''COA'' AS GROUP_LEVEL,
				 SUM(YEARS_AMT) ,
				 PROD_RND_TEAM_CODE AS PARENT_CODE,
				 PROD_RND_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
				 CALIBER_FLAG,
				 OVERSEA_FLAG,
				 LV0_PROD_LIST_CODE,
				 LV0_PROD_LIST_CN_NAME
			FROM '||V_BASE_AMT_TEMP||'
			WHERE  GROUP_LEVEL=''ITEM''
			AND VIEW_FLAG = 12
		   GROUP BY VIEW_FLAG,
					PROD_RND_TEAM_CODE,
					PROD_RND_TEAM_CN_NAME,
					LV0_PROD_RND_TEAM_CODE,
					LV1_PROD_RND_TEAM_CODE,
					LV2_PROD_RND_TEAM_CODE,         
					LV3_PROD_RND_TEAM_CODE,
					COA_CODE ,
					COA_CN_NAME,
					CALIBER_FLAG,
					SPART_CODE,
					SPART_CN_NAME,
					OVERSEA_FLAG,
					LV0_PROD_LIST_CODE,
					LV0_PROD_LIST_CN_NAME
		UNION ALL 
		';
		
		--202405版本 数字能源新增COA层级
		V_COA_SQL_NEW_F := '
			 SELECT VIEW_FLAG,
			 PROD_RND_TEAM_CODE,
			 PROD_RND_TEAM_CN_NAME,
			 LV0_PROD_RND_TEAM_CODE,
			 LV1_PROD_RND_TEAM_CODE,
			 LV2_PROD_RND_TEAM_CODE,         
			 LV3_PROD_RND_TEAM_CODE,
			 COA_CODE AS DMS_CODE,        	--202405版本 数字能源新增COA层级
			 COA_CN_NAME AS DMS_CN_NAME,
			 COA_CODE,   
			 COA_CN_NAME,
			 '''',
			 '''',
			 '''',
			 '''',
			 '''',
			 '''', 
			 '''',
			 '''',
			 COA_CODE AS GROUP_CODE,
			 COA_CN_NAME AS GROUP_CN_NAME,
			 ''F_COA'' AS GROUP_LEVEL,
			 SUM(YEARS_AMT) ,
			 PROD_RND_TEAM_CODE AS PARENT_CODE, 
			 PROD_RND_TEAM_CN_NAME AS PARENT_CN_NAME,	--202403版本 新增PARENT_CN_NAME	  
			 CALIBER_FLAG,
			 OVERSEA_FLAG,
			 LV0_PROD_LIST_CODE,
			 LV0_PROD_LIST_CN_NAME ,
			GROUP_CODE,GROUP_CN_NAME
		FROM '||V_BASE_AMT_TEMP||'
		WHERE GROUP_LEVEL=''ITEM''
		AND VIEW_FLAG = 12
	   GROUP BY VIEW_FLAG,
				 PROD_RND_TEAM_CODE,
				 PROD_RND_TEAM_CN_NAME,
				 LV0_PROD_RND_TEAM_CODE,
				 LV1_PROD_RND_TEAM_CODE,
				 LV2_PROD_RND_TEAM_CODE,
				 LV3_PROD_RND_TEAM_CODE,
				CALIBER_FLAG,
				OVERSEA_FLAG,
				COA_CODE,
				COA_CN_NAME,
				LV0_PROD_LIST_CODE,
				LV0_PROD_LIST_CN_NAME ,
				GROUP_CODE,GROUP_CN_NAME
		UNION ALL
		';

	  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN  --202407版本 新增IAS数据
	  /*表定义*/
		V_DECRYP_YEAR_AMT_TEMP     := 'DM_IAS_DMS_DECRYP_UNI_YEAR_AMT_TEMP';
		V_BASE_AMT_TEMP            := 'DM_IAS_DMS_YEARS_UNI_AMT_TEMP';
		/*
		V_TOP_ITEM_INFO_T          := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_TOP_ITEM_INFO_T';
		V_MONTH_ITEM_AMT_T         := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MID_MONTH_ITEM_T';
		V_MID_TABLE                := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MID_MONTH_WEIGHT_T';
		V_TARGET_TABLE             := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MONTH_WEIGHT_T';*/
		
		V_TOP_ITEM_INFO_T          := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_TOP_ITEM_INFO_T';
		V_MONTH_ITEM_AMT_T         := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MID_MONTH_ITEM_T_DMS';
		V_MID_TABLE                := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MID_MONTH_WEIGHT_T_DMS';
		V_TARGET_TABLE             := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MONTH_WEIGHT_T';
		
		--202407版本 IAS新增LV4层级
		V_LV4_PART				:= 'LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME,';		
		V_SQL_LV4_PART			:= 'T2.LV4_PROD_RND_TEAM_CODE,T2.LV4_PROD_RD_TEAM_CN_NAME,';
		V_JOIN_LV4_CODE			:= 'AND NVL(T1.LV4_PROD_RND_TEAM_CODE,4) = NVL(T2.LV4_PROD_RND_TEAM_CODE,4)';
		
		V_DIMENSION_PARENT		:=' PROD_RND_TEAM_CODE AS PARENT_CODE,
									PROD_RND_TEAM_CN_NAME AS PARENT_CN_NAME,
		';
		
		V_PROD_LV4_CODE                := ' '''' AS LV4_PROD_RND_TEAM_CODE,
											'''' AS LV4_PROD_RD_TEAM_CN_NAME,
										'; 
		
		V_SQL_PROD_RND_TEAM_CODE       := '         
		 CASE  WHEN T2.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T2.LV1_PROD_RND_TEAM_CODE
			   WHEN T2.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T2.LV2_PROD_RND_TEAM_CODE
			   WHEN T2.VIEW_FLAG = ''12'' THEN T2.LV4_PROD_RND_TEAM_CODE
			 ELSE T2.LV3_PROD_RND_TEAM_CODE
			 END AS PROD_RND_TEAM_CODE,';--202401版本新增SPART层级 加入9，10，11三个视角
										 --202407版本 IAS新增视角12 新增LV4重量级团队
										 
		V_SQL_PROD_RND_TEAM_CN_NAME    := '
		   CASE WHEN T2.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T2.LV1_PROD_RD_TEAM_CN_NAME
				WHEN T2.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T2.LV2_PROD_RD_TEAM_CN_NAME
				WHEN T2.VIEW_FLAG = ''12'' THEN T2.LV4_PROD_RD_TEAM_CN_NAME
		   ELSE T2.LV3_PROD_RD_TEAM_CN_NAME
		   END AS PROD_RND_TEAM_CN_NAME,';--202401版本新增SPART层级 加入9，10，11三个视角
										  --202407版本 IAS新增视角12 新增LV4重量级团队
										  
		V_SQL_WEIGHT                := '
		CASE
		 WHEN GROUP_LEVEL IN (''ITEM'',''CATEGORY'',''MODL'',''CEG'') THEN
		  YEARS_AMT / NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,VIEW_FLAG,GROUP_LEVEL,
								  PROD_RND_TEAM_CODE,
								  NVL(DIMENSION_CODE,''D1''),NVL(DIMENSION_SUBCATEGORY_CODE,''D2''),NVL(DIMENSION_SUB_DETAIL_CODE,''D3''),
								  NVL(SPART_CODE,''D4''),
								  NVL(DMS_CODE,''DD''),
								  PARENT_CODE),0)	--采购层级通过重量级团队,量纲,量纲子类,量纲子类明细,SPART分组
			--202401版本新增SPART层级
		 WHEN GROUP_LEVEL IN (''SPART'') THEN
		  YEARS_AMT / NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,VIEW_FLAG,GROUP_LEVEL,
								  PROD_RND_TEAM_CODE,NVL(DIMENSION_CODE,''D1''),NVL(DIMENSION_SUBCATEGORY_CODE,''D2''),
								  PARENT_CODE),0)	--SPART层级通过重量级团队,量纲,量纲子类,量纲子类明细分组
		 WHEN GROUP_LEVEL IN (''SUB_DETAIL'') THEN
		  YEARS_AMT / NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,VIEW_FLAG,GROUP_LEVEL,
								  PROD_RND_TEAM_CODE,NVL(DIMENSION_CODE,''D1''),
								  PARENT_CODE),0)	--量纲子类明细层级通过重量级团队,量纲,量纲子类分组
		 WHEN GROUP_LEVEL IN (''SUBCATEGORY'',''DIMENSION'') THEN
		  YEARS_AMT / NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,VIEW_FLAG,GROUP_LEVEL,
								  PROD_RND_TEAM_CODE,
								  PARENT_CODE),0)	--量纲子类及量纲通过重量级团队及其父级分组
		 ELSE
		  YEARS_AMT /
		  NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,GROUP_LEVEL,VIEW_FLAG, PARENT_CODE), 0)
													--ELSE重量级团队层级通过其父级分组
	   END AS WEIGHT_RATE, ';
	   
		--202407版本 IAS新增LV4层级
		V_LV4_SQL_NEW :='
		  SELECT VIEW_FLAG,
				 LV4_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
				 LV4_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
				 LV0_PROD_RND_TEAM_CODE,
				 LV1_PROD_RND_TEAM_CODE,
				 LV2_PROD_RND_TEAM_CODE,         
				 LV3_PROD_RND_TEAM_CODE,
				 LV4_PROD_RND_TEAM_CODE,
				 LV4_PROD_RD_TEAM_CN_NAME,
				 '''' AS DMS_CODE,
				 '''' AS DMS_CN_NAME,
				 '''' ,
				 '''' ,
				 '''' ,
				 '''' ,
				 '''' ,
				 '''' ,
				 '''',
				 '''',
				 LV4_PROD_RND_TEAM_CODE AS GROUP_CODE,
				 LV4_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
				 ''LV4'' AS GROUP_LEVEL,
				 SUM(YEARS_AMT) ,
				 LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,
				 LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, 
				 CALIBER_FLAG,
				 OVERSEA_FLAG,
				 LV0_PROD_LIST_CODE,
				 LV0_PROD_LIST_CN_NAME
			FROM '||V_BASE_AMT_TEMP||'
			WHERE GROUP_LEVEL=''ITEM''
			AND VIEW_FLAG = 12
		   GROUP BY VIEW_FLAG,
					LV0_PROD_RND_TEAM_CODE,
					LV1_PROD_RND_TEAM_CODE,
					LV2_PROD_RND_TEAM_CODE,         
					LV3_PROD_RND_TEAM_CODE,
					LV4_PROD_RND_TEAM_CODE,
					LV4_PROD_RD_TEAM_CN_NAME,
					LV3_PROD_RD_TEAM_CN_NAME,
					CALIBER_FLAG,
					OVERSEA_FLAG,
					LV0_PROD_LIST_CODE,
					LV0_PROD_LIST_CN_NAME
		UNION ALL 
		';
		
		--202407版本 IAS新增LV4层级
		V_LV4_SQL_NEW_F := '
			 SELECT VIEW_FLAG,
			 LV4_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
			 LV4_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
			 LV0_PROD_RND_TEAM_CODE,
			 LV1_PROD_RND_TEAM_CODE,
			 LV2_PROD_RND_TEAM_CODE,         
			 LV3_PROD_RND_TEAM_CODE,
			 LV4_PROD_RND_TEAM_CODE,
			 LV4_PROD_RD_TEAM_CN_NAME,
			 '''' AS DMS_CODE,        
			 '''' AS DMS_CN_NAME,
			 '''',
			 '''',
			 '''',
			 '''',
			 '''',
			 '''', 
			 '''',
			 '''',
			 LV4_PROD_RND_TEAM_CODE AS GROUP_CODE,
			 LV4_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
			 ''F_LV4'' AS GROUP_LEVEL,
			 SUM(YEARS_AMT) ,
			 LV3_PROD_RND_TEAM_CODE AS PARENT_CODE, 
			 LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,	--202403版本 新增PARENT_CN_NAME	  
			 CALIBER_FLAG,
			 OVERSEA_FLAG,
			 LV0_PROD_LIST_CODE,
			 LV0_PROD_LIST_CN_NAME ,
			GROUP_CODE,GROUP_CN_NAME
		FROM '||V_BASE_AMT_TEMP||'
		WHERE GROUP_LEVEL=''ITEM''
		AND VIEW_FLAG = 12
	   GROUP BY VIEW_FLAG,
				PROD_RND_TEAM_CODE,
				PROD_RND_TEAM_CN_NAME,
				LV0_PROD_RND_TEAM_CODE,
				LV1_PROD_RND_TEAM_CODE,
				LV2_PROD_RND_TEAM_CODE,
				LV3_PROD_RND_TEAM_CODE,
				LV4_PROD_RND_TEAM_CODE,
				LV4_PROD_RD_TEAM_CN_NAME,
				LV3_PROD_RD_TEAM_CN_NAME,
				CALIBER_FLAG,
				OVERSEA_FLAG,
				LV0_PROD_LIST_CODE,
				LV0_PROD_LIST_CN_NAME ,
				GROUP_CODE,GROUP_CN_NAME
		UNION ALL
		';
		
	  END IF;
  
  /*字段值定义*/
    V_LV3_PROD_RND_TEAM_CODE       := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME     := 'LV3_PROD_RD_TEAM_CN_NAME,';
    V_DIMENSION_CODE                := 'DIMENSION_CODE,';
    V_DIMENSION_CN_NAME             := 'DIMENSION_CN_NAME,';
    V_DIMENSION_EN_NAME             := 'DIMENSION_EN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE    := 'DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_EN_NAME := 'DIMENSION_SUBCATEGORY_EN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE     := 'DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME  := 'DIMENSION_SUB_DETAIL_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_EN_NAME    := 'DIMENSION_SUB_DETAIL_EN_NAME,';
    V_DMS_CODE                      := 'DMS_CODE,';
    V_DMS_CN_NAME                   := 'DMS_CN_NAME,'; 
	--202401版本新增SPART层级
	V_SPART_CODE	:='SPART_CODE,'; 
	V_SPART_CN_NAME :='SPART_CN_NAME,';
	
	
    V_PROD_DMS_CODE                       := ' '''' AS DMS_CODE ,';
    V_PROD_DMS_CN_NAME                    := ' '''' AS DMS_CN_NAME ,';
    V_PROD_DIMENSION_CODE                 := ' '''' AS DIMENSION_CODE ,';
    V_PROD_DIMENSION_CN_NAME              := ' '''' AS DIMENSION_CN_NAME,';
    V_PROD_DIMENSION_SUBCATEGORY_CODE     := ' '''' AS DIMENSION_SUBCATEGORY_CODE  ,';
    V_PROD_DIMENSION_SUBCATEGORY_CN_NAME  := ' '''' AS DIMENSION_SUBCATEGORY_CN_NAME ,';
    V_PROD_DIMENSION_SUB_DETAIL_CODE      := ' '''' AS DIMENSION_SUB_DETAIL_CODE ,';
    V_PROD_DIMENSION_SUB_DETAIL_CN_NAME   := ' '''' AS DIMENSION_SUB_DETAIL_CN_NAME ,';
	V_PROD_LV3_CODE                := ' '''' AS LV3_PROD_RND_TEAM_CODE,'; 
	--202401版本新增SPART层级 
	V_PROD_SPART_CODE    				:= ' '''' AS SPART_CODE,';
	V_PROD_SPART_CN_NAME 				:= ' '''' AS SPART_CN_NAME ,';
	
  
    V_SQL_LV3_PROD_RND_TEAM_CODE               := 'T2.LV3_PROD_RND_TEAM_CODE,';
    V_SQL_LV3_PROD_RD_TEAM_CN_NAME             := 'T2.LV3_PROD_RD_TEAM_CN_NAME,';  
    V_SQL_DIMENSION_CODE                := 'T2.DIMENSION_CODE,';
    V_SQL_DIMENSION_CN_NAME             := 'T2.DIMENSION_CN_NAME,';
    V_SQL_DIMENSION_EN_NAME             := 'T2.DIMENSION_EN_NAME,';
    V_SQL_DIMENSION_SUBCATEGORY_CODE    := 'T2.DIMENSION_SUBCATEGORY_CODE,';
    V_SQL_DIMENSION_SUBCATEGORY_CN_NAME := 'T2.DIMENSION_SUBCATEGORY_CN_NAME,';
    V_SQL_DIMENSION_SUBCATEGORY_EN_NAME := 'T2.DIMENSION_SUBCATEGORY_EN_NAME,';
    V_SQL_DIMENSION_SUB_DETAIL_CODE     := 'T2.DIMENSION_SUB_DETAIL_CODE,';
    V_SQL_DIMENSION_SUB_DETAIL_CN_NAME  := 'T2.DIMENSION_SUB_DETAIL_CN_NAME,';
    V_SQL_DIMENSION_SUB_DETAIL_EN_NAME    := 'T2.DIMENSION_SUB_DETAIL_EN_NAME,';
	--202401版本新增SPART层级
	V_SQL_SPART_CODE	:='T2.SPART_CODE,'; 
	V_SQL_SPART_CN_NAME :='T2.SPART_CN_NAME,';
       
    V_SQL_DMS_CODE   := '         
      CASE WHEN T2.VIEW_FLAG IN (''0'',''3'',''6'') THEN T2.DIMENSION_CODE
           WHEN T2.VIEW_FLAG IN (''1'',''4'',''7'') THEN T2.DIMENSION_SUBCATEGORY_CODE
           WHEN T2.VIEW_FLAG IN (''2'',''5'',''8'') THEN T2.DIMENSION_SUB_DETAIL_CODE
		   ELSE T2.SPART_CODE
         END AS DMS_CODE,'; --202401版本新增SPART层级 加入9，10，11三个视角
    V_SQL_DMS_CN_NAME   := '
      CASE WHEN T2.VIEW_FLAG IN (''0'',''3'',''6'') THEN T2.DIMENSION_CN_NAME
           WHEN T2.VIEW_FLAG IN (''1'',''4'',''7'') THEN T2.DIMENSION_SUBCATEGORY_CN_NAME
		   WHEN T2.VIEW_FLAG IN (''2'',''5'',''8'') THEN T2.DIMENSION_SUB_DETAIL_CN_NAME
		   ELSE T2.SPART_CN_NAME
       END AS DMS_CN_NAME,';    --202401版本新增SPART层级 加入9，10，11三个视角  
       
    V_CEG_PARENT_CODE              := 'DMS_CODE AS PARENT_CODE,
									   DMS_CN_NAME AS PARENT_CN_NAME,';  --202403版本 新增PARENT_CN_NAME
       
  /*条件定义*/
    V_JION_LV3_PROD_RND_TEAM_CODE := 'AND NVL(T1.LV3_PROD_RND_TEAM_CODE,3) = NVL(T2.LV3_PROD_RND_TEAM_CODE,3)';
    V_JOIN_DIMENSION_CODE := 'AND NVL(T1.DIMENSION_CODE,''D1'') = NVL(T2.DIMENSION_CODE,''D1'')';
    V_JOIN_DIMENSION_SUBCATEGORY_CODE := 'AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''D2'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''D2'')';
    V_JOIN_DIMENSION_SUB_DETAIL_CODE := 'AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''D3'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''D3'')';
  --202401版本新增SPART层级
	V_JOIN_SPART_CODE :='AND NVL(T1.SPART_CODE,''D4'') = NVL(T2.SPART_CODE,''D4'') ';
  
    V_LV2_VIEW     := ' WHERE VIEW_FLAG NOT IN(0,1,2,9)';
    V_LV1_VIEW     := ' WHERE 1=1';
    V_LV0_VIEW     := ' WHERE 1=1';
 
  /*版本新加入逻辑定义*/
    V_SQL_NEW := V_COA_SQL_NEW||		--202405版本 数字能源新增COA层级
				 V_LV4_SQL_NEW||		--202407版本 IAS新增LV4层级
				 '
--202401版本新增SPART层级
/*SPART层级收敛*/    
  SELECT VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,         
        '||V_LV3_PROD_RND_TEAM_CODE
		 ||V_LV4_PART||'		--202407版本 IAS新增LV4层级
          SPART_CODE AS DMS_CODE,
          SPART_CN_NAME AS DMS_CN_NAME,
		  '||V_COA_PART||' 	--202405版本 数字能源新增COA层级
         '||V_DIMENSION_CODE
         ||V_DIMENSION_CN_NAME
         ||V_DIMENSION_SUBCATEGORY_CODE
         ||V_DIMENSION_SUBCATEGORY_CN_NAME
         ||V_DIMENSION_SUB_DETAIL_CODE
         ||V_DIMENSION_SUB_DETAIL_CN_NAME||'
		 SPART_CODE,
		 SPART_CN_NAME,
         SPART_CODE AS GROUP_CODE,
         SPART_CN_NAME AS GROUP_CN_NAME,
         ''SPART'' AS GROUP_LEVEL,
         SUM(YEARS_AMT) ,
         DIMENSION_SUB_DETAIL_CODE AS PARENT_CODE,
		 DIMENSION_SUB_DETAIL_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM '||V_BASE_AMT_TEMP||'
    WHERE  GROUP_LEVEL=''ITEM''
    AND VIEW_FLAG > 8	--202405版本 数字能源新增12视角
						--202407版本 IAS新增视角12
   GROUP BY VIEW_FLAG,
             PROD_RND_TEAM_CODE,
             PROD_RND_TEAM_CN_NAME,
             LV0_PROD_RND_TEAM_CODE,
             LV1_PROD_RND_TEAM_CODE,
             LV2_PROD_RND_TEAM_CODE,     
			 '||V_COA_PART||' 	--202405版本 数字能源新增COA层级			 
            '||V_LV3_PROD_RND_TEAM_CODE
             ||V_LV4_PART			--202407版本 IAS新增LV4层级
			 ||V_DIMENSION_CODE
             ||V_DIMENSION_CN_NAME
             ||V_DIMENSION_SUBCATEGORY_CODE
             ||V_DIMENSION_SUBCATEGORY_CN_NAME
             ||V_DIMENSION_SUB_DETAIL_CODE
             ||V_DIMENSION_SUB_DETAIL_CN_NAME||' 
            CALIBER_FLAG,
			SPART_CODE,
			SPART_CN_NAME,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME
UNION ALL 
--子类明细收敛    
  SELECT VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,         
        '||V_LV3_PROD_RND_TEAM_CODE
		 ||V_LV4_PART||'			--202407版本 IAS新增LV4层级
         DIMENSION_SUB_DETAIL_CODE AS DMS_CODE     ,            
         DIMENSION_SUB_DETAIL_CN_NAME AS DMS_CN_NAME,
		 '||V_COA_PART||' 	--202405版本 数字能源新增COA层级	
         '||V_DIMENSION_CODE
         ||V_DIMENSION_CN_NAME
         ||V_DIMENSION_SUBCATEGORY_CODE
         ||V_DIMENSION_SUBCATEGORY_CN_NAME
         ||V_DIMENSION_SUB_DETAIL_CODE
         ||V_DIMENSION_SUB_DETAIL_CN_NAME||' 
		 '''' AS  SPART_CODE ,
		 '''' AS  SPART_CN_NAME ,
         DIMENSION_SUB_DETAIL_CODE AS GROUP_CODE,
         DIMENSION_SUB_DETAIL_CN_NAME AS GROUP_CN_NAME,
         ''SUB_DETAIL'' AS GROUP_LEVEL,
         SUM(YEARS_AMT) ,
         DIMENSION_SUBCATEGORY_CODE AS PARENT_CODE,
		 DIMENSION_SUBCATEGORY_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME		 
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM '||V_BASE_AMT_TEMP||'
    WHERE  GROUP_LEVEL=''ITEM''
    AND VIEW_FLAG IN (2,5,8,9,10,11,12)  --202405版本 数字能源新增12视角
										 --202407版本 IAS新增LV4层级
   GROUP BY VIEW_FLAG,
             PROD_RND_TEAM_CODE,
             PROD_RND_TEAM_CN_NAME,
             LV0_PROD_RND_TEAM_CODE,
             LV1_PROD_RND_TEAM_CODE,
             LV2_PROD_RND_TEAM_CODE,   
			 '||V_COA_PART||' 	--202405版本 数字能源新增COA层级				 
            '||V_LV3_PROD_RND_TEAM_CODE
             ||V_LV4_PART			--202407版本 IAS新增LV4层级
			 ||V_DIMENSION_CODE
             ||V_DIMENSION_CN_NAME
             ||V_DIMENSION_SUBCATEGORY_CODE
             ||V_DIMENSION_SUBCATEGORY_CN_NAME
             ||V_DIMENSION_SUB_DETAIL_CODE
             ||V_DIMENSION_SUB_DETAIL_CN_NAME||' 
            CALIBER_FLAG,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME
UNION ALL

---量纲子类收敛        
  SELECT VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,         
        '||V_LV3_PROD_RND_TEAM_CODE
		 ||V_LV4_PART			--202407版本 IAS新增LV4层级
		 ||'
         DIMENSION_SUBCATEGORY_CODE DMS_CODE,              
         DIMENSION_SUBCATEGORY_CN_NAME AS DMS_CN_NAME,
		 '||V_COA_PART||' 	--202405版本 数字能源新增COA层级	
         '||V_DIMENSION_CODE
         ||V_DIMENSION_CN_NAME
         ||V_DIMENSION_SUBCATEGORY_CODE
         ||V_DIMENSION_SUBCATEGORY_CN_NAME||' 
         '''',
         '''',
		 '''' AS  SPART_CODE ,
		 '''' AS  SPART_CN_NAME ,
         DIMENSION_SUBCATEGORY_CODE AS GROUP_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME AS GROUP_CN_NAME,
         ''SUBCATEGORY'' AS GROUP_LEVEL,
         SUM(YEARS_AMT) ,
         DIMENSION_CODE AS PARENT_CODE, 
		 DIMENSION_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM '||V_BASE_AMT_TEMP||'
    WHERE  GROUP_LEVEL=''ITEM''
    AND VIEW_FLAG NOT IN (0,3,6)
   GROUP BY VIEW_FLAG,
             PROD_RND_TEAM_CODE,
             PROD_RND_TEAM_CN_NAME,
             LV0_PROD_RND_TEAM_CODE,
             LV1_PROD_RND_TEAM_CODE,
             LV2_PROD_RND_TEAM_CODE,    
			 '||V_COA_PART||' 	--202405版本 数字能源新增COA层级				 
            '||V_LV3_PROD_RND_TEAM_CODE
             ||V_LV4_PART			--202407版本 IAS新增LV4层级
			 ||V_DIMENSION_CODE
             ||V_DIMENSION_CN_NAME
             ||V_DIMENSION_SUBCATEGORY_CODE
             ||V_DIMENSION_SUBCATEGORY_CN_NAME||' 
            CALIBER_FLAG,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME
UNION ALL            
---量纲收敛
  SELECT VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,         
        '||V_LV3_PROD_RND_TEAM_CODE
         ||V_LV4_PART			--202407版本 IAS新增LV4层级
		 ||'
		 DIMENSION_CODE DMS_CODE,                       
         DIMENSION_CN_NAME AS DMS_CN_NAME,
		 '||V_COA_PART||' 	--202405版本 数字能源新增COA层级	
         '||V_DIMENSION_CODE
         ||V_DIMENSION_CN_NAME||' 
         '''',
         '''',
         '''',
         '''',
		 '''' AS  SPART_CODE ,
		 '''' AS  SPART_CN_NAME ,
         DIMENSION_CODE AS GROUP_CODE,
         DIMENSION_CN_NAME AS GROUP_CN_NAME,
         ''DIMENSION'' AS GROUP_LEVEL,
         SUM(YEARS_AMT) ,
         /*PROD_RND_TEAM_CODE AS PARENT_CODE,
		 PROD_RND_TEAM_CN_NAME	AS	 PARENT_CN_NAME ,*/ --202403版本 新增PARENT_CN_NAME
		 '||V_DIMENSION_PARENT||'	--202405版本 新增COA层级与12视角 量纲父级在12视角为COA，其他视角为重量级团队
									--202407版本 IAS新增LV4层级 IAS新增视角12 量纲父级在12视角为LV4，依然为重量级团队
         CALIBER_FLAG,	
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM '||V_BASE_AMT_TEMP||'
    WHERE  GROUP_LEVEL=''ITEM''
   GROUP BY VIEW_FLAG,
             PROD_RND_TEAM_CODE,
             PROD_RND_TEAM_CN_NAME,
             LV0_PROD_RND_TEAM_CODE,
             LV1_PROD_RND_TEAM_CODE,
             LV2_PROD_RND_TEAM_CODE,
			 '||V_COA_PART||' 	--202405版本 数字能源新增COA层级	
            '||V_LV3_PROD_RND_TEAM_CODE
             ||V_LV4_PART			--202407版本 IAS新增LV4层级
			 ||V_DIMENSION_CODE
             ||V_DIMENSION_CN_NAME||' 
            CALIBER_FLAG,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME
UNION ALL             
--LV3层级收敛
  SELECT VIEW_FLAG,
         LV3_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
         LV3_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,         
        '||V_LV3_PROD_RND_TEAM_CODE
         ||V_PROD_LV4_CODE			--202407版本 IAS新增LV4层级
		 ||V_PROD_DMS_CODE                 
         ||V_PROD_DMS_CN_NAME||' 
		 '||V_PROD_COA_PART||' 	--202405版本 数字能源新增COA层级	
         '''',
         '''',
         '''',
         '''',
         '''',
         '''',
		 '''' AS  SPART_CODE ,
		 '''' AS  SPART_CN_NAME ,
         LV3_PROD_RND_TEAM_CODE AS GROUP_CODE,
         LV3_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
         ''LV3'' AS GROUP_LEVEL,
         SUM(YEARS_AMT) AS YEARS_AMT,
         LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
		 LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM ' || V_BASE_AMT_TEMP || '
   WHERE VIEW_FLAG IN (6,7,8,11,12) /*7月新增视角*//*202401新增视角*/ --202405版本 数字能源新增12视角
   	AND  GROUP_LEVEL=''ITEM''
   GROUP BY VIEW_FLAG,
            LV2_PROD_RND_TEAM_CODE,
			LV2_PROD_RD_TEAM_CN_NAME,
            LV0_PROD_RND_TEAM_CODE,
            LV1_PROD_RND_TEAM_CODE,
            LV3_PROD_RND_TEAM_CODE,
            LV3_PROD_RD_TEAM_CN_NAME,
            CALIBER_FLAG,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME
  UNION ALL            
            
';

 
  /*版本新加入逻辑定义*/	--202405版本 数字能源新增COA层级
    V_SQL_NEW_F := V_COA_SQL_NEW_F||		--202405版本 数字能源新增COA层级
				   V_LV4_SQL_NEW_F||		--202407版本 IAS新增LV4层级
				   '
--202401版本新增SPART层级
--SPART层级收敛    
  SELECT VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,         
        '||V_LV3_PROD_RND_TEAM_CODE
         ||V_LV4_PART			--202407版本 IAS新增LV4层级
		 ||'
		 SPART_CODE AS DMS_CODE,        
         SPART_CN_NAME AS DMS_CN_NAME,
		 '||V_COA_PART||'		--202405版本 数字能源新增COA层级
         '||V_DIMENSION_CODE
         ||V_DIMENSION_CN_NAME
         ||V_DIMENSION_SUBCATEGORY_CODE
         ||V_DIMENSION_SUBCATEGORY_CN_NAME
         ||V_DIMENSION_SUB_DETAIL_CODE
         ||V_DIMENSION_SUB_DETAIL_CN_NAME||' 
		 SPART_CODE,
		 SPART_CN_NAME,
         SPART_CODE AS GROUP_CODE,
         SPART_CN_NAME AS GROUP_CN_NAME,
         ''F_SPART'' AS GROUP_LEVEL,
         SUM(YEARS_AMT) ,
         DIMENSION_SUB_DETAIL_CODE AS PARENT_CODE, 
		 DIMENSION_SUB_DETAIL_CN_NAME AS PARENT_CN_NAME,	--202403版本 新增PARENT_CN_NAME	  
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME ,
        GROUP_CODE,GROUP_CN_NAME
    FROM '||V_BASE_AMT_TEMP||'
    WHERE GROUP_LEVEL=''ITEM''
    AND VIEW_FLAG IN (9,10,11,12) --202405版本 数字能源新增12视角
   GROUP BY VIEW_FLAG,
             PROD_RND_TEAM_CODE,
             PROD_RND_TEAM_CN_NAME,
             LV0_PROD_RND_TEAM_CODE,
             LV1_PROD_RND_TEAM_CODE,
             LV2_PROD_RND_TEAM_CODE,   
			 '||V_COA_PART||'		--202405版本 数字能源新增COA层级			 
            '||V_LV3_PROD_RND_TEAM_CODE
			 ||V_LV4_PART			--202407版本 IAS新增LV4层级
             ||V_DIMENSION_CODE
             ||V_DIMENSION_CN_NAME
             ||V_DIMENSION_SUBCATEGORY_CODE
             ||V_DIMENSION_SUBCATEGORY_CN_NAME
             ||V_DIMENSION_SUB_DETAIL_CODE
             ||V_DIMENSION_SUB_DETAIL_CN_NAME||' 
            CALIBER_FLAG,
            OVERSEA_FLAG,
			SPART_CODE,
			SPART_CN_NAME,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME ,
            GROUP_CODE,GROUP_CN_NAME
UNION ALL    
--子类明细收敛    
  SELECT VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,         
        '||V_LV3_PROD_RND_TEAM_CODE
         ||V_LV4_PART			--202407版本 IAS新增LV4层级
		 ||V_DMS_CODE                 
         ||V_DMS_CN_NAME
		 ||V_COA_PART		--202405版本 数字能源新增COA层级
         ||V_DIMENSION_CODE
         ||V_DIMENSION_CN_NAME
         ||V_DIMENSION_SUBCATEGORY_CODE
         ||V_DIMENSION_SUBCATEGORY_CN_NAME
         ||V_DIMENSION_SUB_DETAIL_CODE
         ||V_DIMENSION_SUB_DETAIL_CN_NAME||'
		 '''' AS SPART_CODE,
		 '''' AS SPART_CN_NAME,		 
         DIMENSION_SUB_DETAIL_CODE AS GROUP_CODE,
         DIMENSION_SUB_DETAIL_CN_NAME AS GROUP_CN_NAME,
         ''F_SUB_DETAIL'' AS GROUP_LEVEL,
         SUM(YEARS_AMT) ,
         DIMENSION_SUBCATEGORY_CODE AS PARENT_CODE,  
		 DIMENSION_SUBCATEGORY_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME ,
        GROUP_CODE,GROUP_CN_NAME
    FROM '||V_BASE_AMT_TEMP||'
    WHERE  GROUP_LEVEL=''ITEM''
    AND VIEW_FLAG IN (2,5,8,9,10,11,12) --202401新增视角
   GROUP BY VIEW_FLAG,
             PROD_RND_TEAM_CODE,
             PROD_RND_TEAM_CN_NAME,
             LV0_PROD_RND_TEAM_CODE,
             LV1_PROD_RND_TEAM_CODE,
             LV2_PROD_RND_TEAM_CODE,   
			 '||V_COA_PART||'		--202405版本 数字能源新增COA层级			 
            '||V_LV3_PROD_RND_TEAM_CODE
             ||V_LV4_PART			--202407版本 IAS新增LV4层级
			 ||V_DMS_CODE                 
             ||V_DMS_CN_NAME
             ||V_DIMENSION_CODE
             ||V_DIMENSION_CN_NAME
             ||V_DIMENSION_SUBCATEGORY_CODE
             ||V_DIMENSION_SUBCATEGORY_CN_NAME
             ||V_DIMENSION_SUB_DETAIL_CODE
             ||V_DIMENSION_SUB_DETAIL_CN_NAME||' 
            CALIBER_FLAG,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME ,
            GROUP_CODE,GROUP_CN_NAME
UNION ALL
---量纲子类收敛        
  SELECT VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,         
        '||V_LV3_PROD_RND_TEAM_CODE
         ||V_LV4_PART			--202407版本 IAS新增LV4层级
		 ||V_DMS_CODE                 
         ||V_DMS_CN_NAME
		 ||V_COA_PART	--202405版本 数字能源新增COA层级
         ||V_DIMENSION_CODE
         ||V_DIMENSION_CN_NAME
         ||V_DIMENSION_SUBCATEGORY_CODE
         ||V_DIMENSION_SUBCATEGORY_CN_NAME||' 
         '''',
         '''',
		 '''' AS SPART_CODE,
		 '''' AS SPART_CN_NAME,
         DIMENSION_SUBCATEGORY_CODE AS GROUP_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME AS GROUP_CN_NAME,
         ''F_SUBCATEGORY'' AS GROUP_LEVEL,
         SUM(YEARS_AMT) ,
         DIMENSION_CODE AS PARENT_CODE,  
		 DIMENSION_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME ,
         GROUP_CODE,GROUP_CN_NAME
    FROM '||V_BASE_AMT_TEMP||'
    WHERE  GROUP_LEVEL=''ITEM''
    AND VIEW_FLAG NOT IN (0,3,6)
   GROUP BY VIEW_FLAG,
             PROD_RND_TEAM_CODE,
             PROD_RND_TEAM_CN_NAME,
             LV0_PROD_RND_TEAM_CODE,
             LV1_PROD_RND_TEAM_CODE,
             LV2_PROD_RND_TEAM_CODE,   
			 '||V_COA_PART||'		--202405版本 数字能源新增COA层级			 
            '||V_LV3_PROD_RND_TEAM_CODE
             ||V_LV4_PART			--202407版本 IAS新增LV4层级
			 ||V_DMS_CODE                 
             ||V_DMS_CN_NAME
             ||V_DIMENSION_CODE
             ||V_DIMENSION_CN_NAME
             ||V_DIMENSION_SUBCATEGORY_CODE
             ||V_DIMENSION_SUBCATEGORY_CN_NAME||' 
            CALIBER_FLAG,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME ,
            GROUP_CODE,GROUP_CN_NAME
UNION ALL            
---量纲收敛
  SELECT VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,         
        '||V_LV3_PROD_RND_TEAM_CODE
         ||V_LV4_PART			--202407版本 IAS新增LV4层级
		 ||V_DMS_CODE                 
         ||V_DMS_CN_NAME
		 ||V_COA_PART		--202405版本 数字能源新增COA层级
         ||V_DIMENSION_CODE
         ||V_DIMENSION_CN_NAME||' 
         '''',
         '''',
         '''',
         '''',
		 '''' AS SPART_CODE,
		 '''' AS SPART_CN_NAME,
         DIMENSION_CODE AS GROUP_CODE,
         DIMENSION_CN_NAME AS GROUP_CN_NAME,
         ''F_DIMENSION'' AS GROUP_LEVEL,
         SUM(YEARS_AMT) ,
         '||V_DIMENSION_PARENT||'	--202405版本 新增COA层级与12视角 量纲父级在12视角为COA，其他视角为重量级团队
									--202407版本 IAS新增LV4层级 IAS新增视角12 量纲父级在12视角为LV4，依然为重量级团队
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME ,
         GROUP_CODE,GROUP_CN_NAME
    FROM '||V_BASE_AMT_TEMP||'
    WHERE  GROUP_LEVEL=''ITEM''
   GROUP BY VIEW_FLAG,
             PROD_RND_TEAM_CODE,
             PROD_RND_TEAM_CN_NAME,
             LV0_PROD_RND_TEAM_CODE,
             LV1_PROD_RND_TEAM_CODE,
             LV2_PROD_RND_TEAM_CODE,         
            '||V_LV3_PROD_RND_TEAM_CODE
			 ||V_LV4_PART			--202407版本 IAS新增LV4层级
			 ||V_COA_PART		--202405版本 数字能源新增COA层级
             ||V_DMS_CODE                 
             ||V_DMS_CN_NAME
             ||V_DIMENSION_CODE
             ||V_DIMENSION_CN_NAME||' 
            CALIBER_FLAG,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME ,
            GROUP_CODE,GROUP_CN_NAME
UNION ALL             
--LV3层级收敛
  SELECT VIEW_FLAG,
         LV3_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
         LV3_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,         
        '||V_LV3_PROD_RND_TEAM_CODE
         ||V_PROD_LV4_CODE		--202407版本 IAS新增LV4层级
		 ||V_PROD_DMS_CODE                 
         ||V_PROD_DMS_CN_NAME
		 ||V_PROD_COA_PART||'		--202405版本 数字能源新增COA层级
         '''',
         '''',
         '''',
         '''',
         '''',
         '''',
		 '''' AS SPART_CODE,
		 '''' AS SPART_CN_NAME,
         LV3_PROD_RND_TEAM_CODE AS GROUP_CODE,
         LV3_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
         ''F_LV3'' AS GROUP_LEVEL,
         SUM(YEARS_AMT) AS YEARS_AMT,
         LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
		 LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,	--202403版本 新增PARENT_CN_NAME	,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME ,
         GROUP_CODE,GROUP_CN_NAME
    FROM ' || V_BASE_AMT_TEMP || '
   WHERE VIEW_FLAG IN (6,7,8,11,12) --202401新增视角	
									--202405版本 数字能源新增12视角
									--202407版本 IAS新增视角12
   	AND  GROUP_LEVEL=''ITEM''
   GROUP BY VIEW_FLAG,
            LV2_PROD_RND_TEAM_CODE,
			LV2_PROD_RD_TEAM_CN_NAME,
            LV0_PROD_RND_TEAM_CODE,
            LV1_PROD_RND_TEAM_CODE,
            LV3_PROD_RND_TEAM_CODE,
            LV3_PROD_RD_TEAM_CN_NAME,
            CALIBER_FLAG,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME ,
           GROUP_CODE,GROUP_CN_NAME 
  UNION ALL            
            
';
  END IF;

--前置数据解密
V_SQL := '
DROP TABLE IF EXISTS '||V_DECRYP_YEAR_AMT_TEMP||';
CREATE TEMPORARY TABLE '||V_DECRYP_YEAR_AMT_TEMP||'(
  LV0_PROD_RND_TEAM_CODE  VARCHAR(50),
  LV1_PROD_RND_TEAM_CODE  VARCHAR(50),
  LV2_PROD_RND_TEAM_CODE  VARCHAR(50),
  LV3_PROD_RND_TEAM_CODE  VARCHAR(50),
  LV4_PROD_RND_TEAM_CODE	VARCHAR(50),	--202407版本 IAS新增LV4层级
  LV4_PROD_RD_TEAM_CN_NAME	VARCHAR(200),
  L1_NAME VARCHAR(200),/*7月新增盈利颗粒度维度*/
  L2_NAME VARCHAR(200),
  DIMENSION_CODE VARCHAR(50), 
  DIMENSION_SUBCATEGORY_CODE VARCHAR(50), 
  DIMENSION_SUB_DETAIL_CODE VARCHAR(50), 
  SPART_CODE 				VARCHAR(200),
  COA_CODE		VARCHAR(50), 	--202405版本 数字能源新增COA层级
  COA_CN_NAME   VARCHAR(200),
  L3_CEG_CODE VARCHAR(50),
  L4_CEG_CODE    VARCHAR(50),  
  CATEGORY_CODE VARCHAR(50),
  ITEM_CODE  VARCHAR(50),
  YEARS_AMT NUMERIC,
  VIEW_FLAG  VARCHAR(2),
  CALIBER_FLAG  VARCHAR(2), /*7月新增业务口径字段*/
  OVERSEA_FLAG  VARCHAR(2),  
  LV0_PROD_LIST_CODE VARCHAR(50)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY ROUNDROBIN;

INSERT INTO '||V_DECRYP_YEAR_AMT_TEMP||'(
         VIEW_FLAG,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,
         '||V_LV3_PROD_RND_TEAM_CODE
          ||V_LV4_PART		--202407版本 IAS新增LV4层级
		  ||V_L1_NAME
          ||V_L2_NAME
		  ||V_COA_PART		--202405版本 数字能源新增COA层级
          ||V_DIMENSION_CODE
          ||V_DIMENSION_SUBCATEGORY_CODE
          ||V_DIMENSION_SUB_DETAIL_CODE
		  ||V_SPART_CODE||' 
         L3_CEG_CODE,
         L4_CEG_CODE,
         CATEGORY_CODE,
         ITEM_CODE,
         YEARS_AMT,
         LV0_PROD_LIST_CODE,
         OVERSEA_FLAG,
         CALIBER_FLAG)
         
SELECT VIEW_FLAG,
       LV0_PROD_RND_TEAM_CODE,
       LV1_PROD_RND_TEAM_CODE,
       LV2_PROD_RND_TEAM_CODE,
       '||V_LV3_PROD_RND_TEAM_CODE
        ||V_LV4_PART		--202407版本 IAS新增LV4层级
		||V_L1_NAME
        ||V_L2_NAME
		||V_COA_PART		--202405版本 数字能源新增COA层级
        ||V_DIMENSION_CODE
        ||V_DIMENSION_SUBCATEGORY_CODE
        ||V_DIMENSION_SUB_DETAIL_CODE
		||V_SPART_CODE||' 
       L3_CEG_CODE,
       L4_CEG_CODE,
       CATEGORY_CODE,
       ITEM_CODE,
       /*TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,'''||V_KEYSTR||''',''aes128'',''cbc'', ''sha256'')) AS YEARS_AMT,
	   */
	   RMB_COST_AMT,	--DMS修改
         LV0_PROD_LIST_CODE,
         OVERSEA_FLAG,
         CALIBER_FLAG
  FROM '||V_MONTH_ITEM_AMT_T||'
 WHERE PERIOD_YEAR BETWEEN (YEAR(CURRENT_DATE) - 1) AND YEAR(CURRENT_DATE) /*权重取两年金额*/
 AND ONLY_ITEM_FLAG = ''N'' 
 AND REVIEW_ITEM_FLAG = 0
 ;
';

 
EXECUTE IMMEDIATE V_SQL;

 --写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '数据解密完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');  

V_EXCEPTION_FLAG := '2';   
V_SQL:='
DROP TABLE IF EXISTS '||V_BASE_AMT_TEMP||';
CREATE TEMPORARY TABLE '||V_BASE_AMT_TEMP||' (
    VERSION_ID BIGINT,
    LV0_PROD_RND_TEAM_CODE     VARCHAR(50),
    LV0_PROD_RD_TEAM_CN_NAME     VARCHAR(200),
    LV1_PROD_RND_TEAM_CODE     VARCHAR(50),
    LV1_PROD_RD_TEAM_CN_NAME     VARCHAR(200),
    LV2_PROD_RND_TEAM_CODE     VARCHAR(50),
    LV2_PROD_RD_TEAM_CN_NAME     VARCHAR(200),
    LV3_PROD_RND_TEAM_CODE     VARCHAR(50),/*7月新增LV3重量级团队*/
    LV3_PROD_RD_TEAM_CN_NAME     VARCHAR(200),
    LV4_PROD_RND_TEAM_CODE     VARCHAR(50),		--202407版本 IAS新增LV4层级
    LV4_PROD_RD_TEAM_CN_NAME     VARCHAR(200),
    L1_NAME    VARCHAR(200),/*7月新增盈利颗粒度维度*/
    L2_NAME    VARCHAR(200),
	COA_CODE		VARCHAR(50), 	--202405版本 数字能源新增COA层级
	COA_CN_NAME   VARCHAR(200),
    DIMENSION_CODE    VARCHAR(500), /*9月新增量纲颗粒度维度 */
    DIMENSION_CN_NAME    VARCHAR(2000) ,
    DIMENSION_EN_NAME    VARCHAR(2000) ,
    DIMENSION_SUBCATEGORY_CODE    VARCHAR(500) ,
    DIMENSION_SUBCATEGORY_CN_NAME    VARCHAR(2000) ,
    DIMENSION_SUBCATEGORY_EN_NAME    VARCHAR(2000) ,
    DIMENSION_SUB_DETAIL_CODE    VARCHAR(500) ,
    DIMENSION_SUB_DETAIL_CN_NAME    VARCHAR(2000) ,
    DIMENSION_SUB_DETAIL_EN_NAME    VARCHAR(2000) ,
	SPART_CODE    VARCHAR(200), --202401版本新增SPART层级
	SPART_CN_NAME VARCHAR(200),    
    PROFITS_NAME  VARCHAR(200),  /*7月新增盈利颗粒度字段*/
    DMS_CODE    VARCHAR(50),     /*9月新增量纲颗粒度字段*/            
    DMS_CN_NAME      VARCHAR(200),
    TOP_L3_CEG_CODE     VARCHAR(50),
    TOP_L3_CEG_SHORT_CN_NAME     VARCHAR(200),
    TOP_L4_CEG_CODE    VARCHAR(50),
    TOP_L4_CEG_SHORT_CN_NAME VARCHAR(200),    
    TOP_CATEGORY_CODE  VARCHAR(50),
    TOP_CATEGORY_CN_NAME  VARCHAR(200),
    PROD_RND_TEAM_CODE  VARCHAR(50),
    PROD_RND_TEAM_CN_NAME  VARCHAR(200),
    GROUP_CODE  VARCHAR(50),
    GROUP_CN_NAME  VARCHAR(1000),
    GROUP_LEVEL  VARCHAR(50),
    YEARS_AMT NUMERIC,
    PARENT_CODE  VARCHAR(50),
	PARENT_CN_NAME VARCHAR(200), --202403版本 新增PARENT_CN_NAME
    VIEW_FLAG  VARCHAR(2),
    CALIBER_FLAG  VARCHAR(2), /*7月新增业务口径字段*/
    OVERSEA_FLAG  VARCHAR(2),  
    LV0_PROD_LIST_CODE VARCHAR(50),
	LV0_PROD_LIST_CN_NAME VARCHAR(200)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY ROUNDROBIN;';


EXECUTE IMMEDIATE V_SQL;

 --写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '年度发货额临时表创建完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');  
  
DBMS_OUTPUT.PUT_LINE('年度发货额临时表创建完成');
/*-----------------------分视角后规格品年发货额收敛----------------*/
V_EXCEPTION_FLAG := '3';
--取规格品清单维度
V_SQL:='
--规格品两年发货额收敛
INSERT INTO '||V_BASE_AMT_TEMP||'
  (LV0_PROD_RND_TEAM_CODE,
   LV0_PROD_RD_TEAM_CN_NAME,
   LV1_PROD_RND_TEAM_CODE,
   LV1_PROD_RD_TEAM_CN_NAME,
   LV2_PROD_RND_TEAM_CODE,
   LV2_PROD_RD_TEAM_CN_NAME,
   '||V_LV3_PROD_RND_TEAM_CODE
   ||V_LV3_PROD_RD_TEAM_CN_NAME
   ||V_LV4_PART		--202407版本 IAS新增LV4层级
   ||V_L1_NAME
   ||V_L2_NAME
   ||V_COA_PART		--202405版本 数字能源新增COA层级
   ||V_DIMENSION_CODE
   ||V_DIMENSION_CN_NAME
   ||V_DIMENSION_EN_NAME
   ||V_DIMENSION_SUBCATEGORY_CODE
   ||V_DIMENSION_SUBCATEGORY_CN_NAME
   ||V_DIMENSION_SUBCATEGORY_EN_NAME
   ||V_DIMENSION_SUB_DETAIL_CODE
   ||V_DIMENSION_SUB_DETAIL_CN_NAME
   ||V_DIMENSION_SUB_DETAIL_EN_NAME
   ||V_SPART_CODE 
   ||V_SPART_CN_NAME||' 	--202401版本新增SPART层级
   TOP_L3_CEG_CODE,
   TOP_L3_CEG_SHORT_CN_NAME,
   TOP_L4_CEG_CODE,
   TOP_L4_CEG_SHORT_CN_NAME,   
   TOP_CATEGORY_CODE,
   TOP_CATEGORY_CN_NAME,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
   '||V_PROFITS_NAME||
    V_DMS_CODE||
    V_DMS_CN_NAME||'
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   YEARS_AMT,
   PARENT_CODE,
   PARENT_CN_NAME,		--202403版本 新增PARENT_CN_NAME
   VIEW_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG ,
   LV0_PROD_LIST_CODE ,
   LV0_PROD_LIST_CN_NAME)
   
WITH TOP_ITEM AS
 (SELECT VIEW_FLAG,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         '||V_LV3_PROD_RND_TEAM_CODE
		 ||V_LV3_PROD_RD_TEAM_CN_NAME
		 ||V_LV4_PART		--202407版本 IAS新增LV4层级
		 ||V_L1_NAME
		 ||V_L2_NAME
		 ||V_COA_PART	--202405版本 数字能源新增COA层级
		 ||V_DIMENSION_CODE
		 ||V_DIMENSION_CN_NAME
		 ||V_DIMENSION_EN_NAME
		 ||V_DIMENSION_SUBCATEGORY_CODE
		 ||V_DIMENSION_SUBCATEGORY_CN_NAME
		 ||V_DIMENSION_SUBCATEGORY_EN_NAME
		 ||V_DIMENSION_SUB_DETAIL_CODE
		 ||V_DIMENSION_SUB_DETAIL_CN_NAME
		 ||V_DIMENSION_SUB_DETAIL_EN_NAME
		 ||V_SPART_CODE 
		 ||V_SPART_CN_NAME||' 	--202401版本新增SPART层级		 
         TOP_L3_CEG_CODE,
         TOP_L3_CEG_SHORT_CN_NAME,
         TOP_L4_CEG_CODE,
         TOP_L4_CEG_SHORT_CN_NAME,             
         TOP_CATEGORY_CODE,
         TOP_CATEGORY_CN_NAME,
         TOP_ITEM_CODE,
         TOP_ITEM_CN_NAME,
         CALIBER_FLAG,
         OVERSEA_FLAG,  
         LV0_PROD_LIST_CODE  ,  
         LV0_PROD_LIST_CN_NAME
    FROM '||V_TOP_ITEM_INFO_T||'
   WHERE VERSION_ID ='|| V_VERSION||'
     AND IS_TOP_FLAG =''Y''/*取规格品*/
     AND DOUBLE_FLAG =''Y''), /*取全量规格品*/ 

/*解密金额卷积*/
 BASE_AMT AS (
    SELECT VIEW_FLAG,
           LV0_PROD_RND_TEAM_CODE,
           LV1_PROD_RND_TEAM_CODE,
           LV2_PROD_RND_TEAM_CODE,
           '||V_LV3_PROD_RND_TEAM_CODE
            ||V_LV4_PART		--202407版本 IAS新增LV4层级
			||V_L1_NAME
            ||V_L2_NAME
			||V_COA_PART	--202405版本 数字能源新增COA层级
            ||V_DIMENSION_CODE
            ||V_DIMENSION_SUBCATEGORY_CODE
            ||V_DIMENSION_SUB_DETAIL_CODE
			||V_SPART_CODE ||'   --202401版本新增SPART层级
           L3_CEG_CODE,
           L4_CEG_CODE,
           CATEGORY_CODE,
           ITEM_CODE,
           SUM(YEARS_AMT) AS YEARS_AMT,
           CALIBER_FLAG,
           OVERSEA_FLAG ,
           LV0_PROD_LIST_CODE 
      FROM '||V_DECRYP_YEAR_AMT_TEMP||'
     GROUP BY VIEW_FLAG,
              LV0_PROD_RND_TEAM_CODE,
              LV1_PROD_RND_TEAM_CODE,
              LV2_PROD_RND_TEAM_CODE,
			  '||V_LV3_PROD_RND_TEAM_CODE
               ||V_LV4_PART		--202407版本 IAS新增LV4层级
			   ||V_L1_NAME
               ||V_L2_NAME
			   ||V_COA_PART	--202405版本 数字能源新增COA层级
               ||V_DIMENSION_CODE
               ||V_DIMENSION_SUBCATEGORY_CODE
               ||V_DIMENSION_SUB_DETAIL_CODE
			   ||V_SPART_CODE ||'   --202401版本新增SPART层级
              L3_CEG_CODE,
              L4_CEG_CODE,
              CATEGORY_CODE,
              ITEM_CODE,
              CALIBER_FLAG,
              OVERSEA_FLAG ,
              LV0_PROD_LIST_CODE)
     

  SELECT T2.LV0_PROD_RND_TEAM_CODE,
         T2.LV0_PROD_RD_TEAM_CN_NAME,
         T2.LV1_PROD_RND_TEAM_CODE,
         T2.LV1_PROD_RD_TEAM_CN_NAME,
         T2.LV2_PROD_RND_TEAM_CODE,
         T2.LV2_PROD_RD_TEAM_CN_NAME,
         '||V_SQL_LV3_PROD_RND_TEAM_CODE
          ||V_SQL_LV3_PROD_RD_TEAM_CN_NAME
		  ||V_SQL_LV4_PART		--202407版本 IAS新增LV4层级
          ||V_SQL_L1_NAME
          ||V_SQL_L2_NAME
		  ||V_SQL_COA_PART	--202405版本 数字能源新增COA层级
          ||V_SQL_DIMENSION_CODE
          ||V_SQL_DIMENSION_CN_NAME
          ||V_SQL_DIMENSION_EN_NAME
          ||V_SQL_DIMENSION_SUBCATEGORY_CODE
          ||V_SQL_DIMENSION_SUBCATEGORY_CN_NAME
          ||V_SQL_DIMENSION_SUBCATEGORY_EN_NAME
          ||V_SQL_DIMENSION_SUB_DETAIL_CODE
          ||V_SQL_DIMENSION_SUB_DETAIL_CN_NAME
          ||V_SQL_DIMENSION_SUB_DETAIL_EN_NAME
		  ||V_SQL_SPART_CODE
		  ||V_SQL_SPART_CN_NAME||' --202401版本新增SPART层级
         T2.TOP_L3_CEG_CODE,
         T2.TOP_L3_CEG_SHORT_CN_NAME,
         T2.TOP_L4_CEG_CODE,
         T2.TOP_L4_CEG_SHORT_CN_NAME,    
         T2.TOP_CATEGORY_CODE,
         T2.TOP_CATEGORY_CN_NAME,
         '||V_SQL_PROD_RND_TEAM_CODE||
         V_SQL_PROD_RND_TEAM_CN_NAME||
         V_SQL_PROFITS_NAME||
         V_SQL_DMS_CODE||
         V_SQL_DMS_CN_NAME||' 
         T2.TOP_ITEM_CODE AS GROUP_CODE,
         T2.TOP_ITEM_CN_NAME AS GROUP_CN_NAME,
         ''ITEM'' AS GROUP_LEVEL,
         T1.YEARS_AMT,
         T2.TOP_CATEGORY_CODE AS PARENT_CODE,
		 T2.TOP_CATEGORY_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
         T2.VIEW_FLAG,
         T2.CALIBER_FLAG,
         T2.OVERSEA_FLAG ,  
         T2.LV0_PROD_LIST_CODE  ,  
         T2.LV0_PROD_LIST_CN_NAME
    FROM BASE_AMT T1
   INNER JOIN TOP_ITEM T2
      ON T1.VIEW_FLAG = T2.VIEW_FLAG
     AND T1.LV0_PROD_RND_TEAM_CODE = T2.LV0_PROD_RND_TEAM_CODE
     AND NVL(T1.LV1_PROD_RND_TEAM_CODE, 1) =
         NVL(T2.LV1_PROD_RND_TEAM_CODE, 1)
     AND NVL(T1.LV2_PROD_RND_TEAM_CODE, 2) =
         NVL(T2.LV2_PROD_RND_TEAM_CODE, 2)
         '||V_JION_LV3_PROD_RND_TEAM_CODE
          ||V_JOIN_LV4_CODE		--202407版本 IAS新增LV4层级
		  ||V_JOIN_L1_NAME
          ||V_JOIN_L2_NAME
		  ||V_JOIN_COA_CODE		--202405版本 数字能源新增COA层级
          ||V_JOIN_DIMENSION_CODE
          ||V_JOIN_DIMENSION_SUBCATEGORY_CODE
          ||V_JOIN_DIMENSION_SUB_DETAIL_CODE
		  ||V_JOIN_SPART_CODE||' --202401版本新增SPART层级
     AND T1.L3_CEG_CODE = T2.TOP_L3_CEG_CODE
     AND T1.L4_CEG_CODE = T2.TOP_L4_CEG_CODE
     AND T1.CATEGORY_CODE = T2.TOP_CATEGORY_CODE
     AND T1.ITEM_CODE = T2.TOP_ITEM_CODE
     AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
     AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG  
     AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE  
     ;';


 EXECUTE IMMEDIATE V_SQL;


--写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '规格品年度发货额收敛完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
    
 DBMS_OUTPUT.PUT_LINE('规格品年度发货额收敛完成');
/*********************************************分视角后其他层级两年发货额收敛*********************************************/

V_EXCEPTION_FLAG := '4';   
--中间表收敛插数

V_SQL:='
INSERT INTO '||V_BASE_AMT_TEMP||'
  (VIEW_FLAG,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
   LV0_PROD_RND_TEAM_CODE,
   LV1_PROD_RND_TEAM_CODE,
   LV2_PROD_RND_TEAM_CODE,
   '||V_LV3_PROD_RND_TEAM_CODE
    ||V_LV4_PART		--202407版本 IAS新增LV4层级
	||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
    ||V_DMS_CODE
    ||V_DMS_CN_NAME
	||V_COA_PART	--202405版本 数字能源新增COA层级
    ||V_DIMENSION_CODE
    ||V_DIMENSION_CN_NAME
    ||V_DIMENSION_SUBCATEGORY_CODE
    ||V_DIMENSION_SUBCATEGORY_CN_NAME
    ||V_DIMENSION_SUB_DETAIL_CODE
    ||V_DIMENSION_SUB_DETAIL_CN_NAME
	||V_SPART_CODE
	||V_SPART_CN_NAME||' --202401版本新增SPART层级
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   YEARS_AMT,
   PARENT_CODE,  
   PARENT_CN_NAME,  --202403版本 新增PARENT_CN_NAME
   CALIBER_FLAG,
   OVERSEA_FLAG, 
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME  
   )
   
--品类层级
SELECT VIEW_FLAG,
       PROD_RND_TEAM_CODE,
       PROD_RND_TEAM_CN_NAME,
       LV0_PROD_RND_TEAM_CODE,
       LV1_PROD_RND_TEAM_CODE,
       LV2_PROD_RND_TEAM_CODE,
       '||V_LV3_PROD_RND_TEAM_CODE
        ||V_LV4_PART		--202407版本 IAS新增LV4层级
		||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
         ||V_DMS_CODE                
         ||V_DMS_CN_NAME
		 ||V_COA_PART	--202405版本 数字能源新增COA层级
         ||V_DIMENSION_CODE
         ||V_DIMENSION_CN_NAME
         ||V_DIMENSION_SUBCATEGORY_CODE
         ||V_DIMENSION_SUBCATEGORY_CN_NAME
         ||V_DIMENSION_SUB_DETAIL_CODE
         ||V_DIMENSION_SUB_DETAIL_CN_NAME
		 ||V_SPART_CODE
		||V_SPART_CN_NAME||' --202401版本新增SPART层级
       TOP_CATEGORY_CODE AS GROUP_CODE,
       TOP_CATEGORY_CN_NAME AS GROUP_CN_NAME,
       ''CATEGORY'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       TOP_L4_CEG_CODE AS PARENT_CODE,
	   TOP_L4_CEG_SHORT_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME 
  FROM '||V_BASE_AMT_TEMP||'
 GROUP BY VIEW_FLAG,
          PROD_RND_TEAM_CODE,
          PROD_RND_TEAM_CN_NAME,
          TOP_L4_CEG_CODE, 
		  TOP_L4_CEG_SHORT_CN_NAME,		  
          TOP_CATEGORY_CODE,
          TOP_CATEGORY_CN_NAME,
          LV0_PROD_RND_TEAM_CODE,
          LV1_PROD_RND_TEAM_CODE,
          LV2_PROD_RND_TEAM_CODE,
          '||V_LV3_PROD_RND_TEAM_CODE
           ||V_LV4_PART		--202407版本 IAS新增LV4层级
		   ||V_PROFITS_NAME
           ||V_L1_NAME
           ||V_L2_NAME
           ||V_DMS_CODE                
           ||V_DMS_CN_NAME
		   ||V_COA_PART	--202405版本 数字能源新增COA层级
           ||V_DIMENSION_CODE
           ||V_DIMENSION_CN_NAME
           ||V_DIMENSION_SUBCATEGORY_CODE
           ||V_DIMENSION_SUBCATEGORY_CN_NAME
           ||V_DIMENSION_SUB_DETAIL_CODE
           ||V_DIMENSION_SUB_DETAIL_CN_NAME
		   ||V_SPART_CODE
		   ||V_SPART_CN_NAME||' --202401版本新增SPART层级
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME 
         
UNION ALL

--MODL层级收敛
SELECT VIEW_FLAG,
       PROD_RND_TEAM_CODE,
       PROD_RND_TEAM_CN_NAME,
       LV0_PROD_RND_TEAM_CODE,
       LV1_PROD_RND_TEAM_CODE,
       LV2_PROD_RND_TEAM_CODE,
       '||V_LV3_PROD_RND_TEAM_CODE
        ||V_LV4_PART		--202407版本 IAS新增LV4层级
		||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
        ||V_DMS_CODE                
        ||V_DMS_CN_NAME
		||V_COA_PART	--202405版本 数字能源新增COA层级
        ||V_DIMENSION_CODE
        ||V_DIMENSION_CN_NAME
        ||V_DIMENSION_SUBCATEGORY_CODE
        ||V_DIMENSION_SUBCATEGORY_CN_NAME
        ||V_DIMENSION_SUB_DETAIL_CODE
        ||V_DIMENSION_SUB_DETAIL_CN_NAME
		||V_SPART_CODE
		||V_SPART_CN_NAME||' --202401版本新增SPART层级
       TOP_L4_CEG_CODE AS GROUP_CODE,
       TOP_L4_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,
       ''MODL'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       TOP_L3_CEG_CODE AS PARENT_CODE,
	   TOP_L3_CEG_SHORT_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       CALIBER_FLAG,
        OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME 
  FROM '||V_BASE_AMT_TEMP||'
 GROUP BY VIEW_FLAG,
          PROD_RND_TEAM_CODE,
          PROD_RND_TEAM_CN_NAME,
          TOP_L3_CEG_CODE,
		  TOP_L3_CEG_SHORT_CN_NAME,
          TOP_L4_CEG_CODE,
          TOP_L4_CEG_SHORT_CN_NAME,          
          LV0_PROD_RND_TEAM_CODE,
          LV1_PROD_RND_TEAM_CODE,
          LV2_PROD_RND_TEAM_CODE,
          '||V_LV3_PROD_RND_TEAM_CODE
           ||V_LV4_PART		--202407版本 IAS新增LV4层级
		   ||V_PROFITS_NAME
           ||V_L1_NAME
           ||V_L2_NAME
           ||V_DMS_CODE                
           ||V_DMS_CN_NAME
		   ||V_COA_PART	--202405版本 数字能源新增COA层级
           ||V_DIMENSION_CODE
           ||V_DIMENSION_CN_NAME
           ||V_DIMENSION_SUBCATEGORY_CODE
           ||V_DIMENSION_SUBCATEGORY_CN_NAME
           ||V_DIMENSION_SUB_DETAIL_CODE
           ||V_DIMENSION_SUB_DETAIL_CN_NAME
		   ||V_SPART_CODE
		   ||V_SPART_CN_NAME||' --202401版本新增SPART层级
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME   
         
         
UNION ALL

--CEG专家团层级收敛
SELECT VIEW_FLAG,
       PROD_RND_TEAM_CODE,
       PROD_RND_TEAM_CN_NAME,
       LV0_PROD_RND_TEAM_CODE,
       LV1_PROD_RND_TEAM_CODE,
       LV2_PROD_RND_TEAM_CODE,
       '||V_LV3_PROD_RND_TEAM_CODE
        ||V_LV4_PART		--202407版本 IAS新增LV4层级
		||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
        ||V_DMS_CODE                
        ||V_DMS_CN_NAME
		||V_COA_PART	--202405版本 数字能源新增COA层级
        ||V_DIMENSION_CODE
        ||V_DIMENSION_CN_NAME
        ||V_DIMENSION_SUBCATEGORY_CODE
        ||V_DIMENSION_SUBCATEGORY_CN_NAME
        ||V_DIMENSION_SUB_DETAIL_CODE
        ||V_DIMENSION_SUB_DETAIL_CN_NAME
		||V_SPART_CODE
		||V_SPART_CN_NAME||' --202401版本新增SPART层级
       TOP_L3_CEG_CODE AS GROUP_CODE,
       TOP_L3_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,
       ''CEG'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       '||V_CEG_PARENT_CODE||'  --202403版本 新增PARENT_CN_NAME
        CALIBER_FLAG,
        OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME 
  FROM '||V_BASE_AMT_TEMP||'
 GROUP BY VIEW_FLAG,
          PROD_RND_TEAM_CODE,
          PROD_RND_TEAM_CN_NAME,
          TOP_L3_CEG_CODE,
          TOP_L3_CEG_SHORT_CN_NAME,
          LV0_PROD_RND_TEAM_CODE,
          LV1_PROD_RND_TEAM_CODE,
          LV2_PROD_RND_TEAM_CODE,
          '||V_LV3_PROD_RND_TEAM_CODE
           ||V_LV4_PART		--202407版本 IAS新增LV4层级
		   ||V_PROFITS_NAME
           ||V_L1_NAME
           ||V_L2_NAME
           ||V_DMS_CODE                
           ||V_DMS_CN_NAME
		   ||V_COA_PART	--202405版本 数字能源新增COA层级
           ||V_DIMENSION_CODE
           ||V_DIMENSION_CN_NAME
           ||V_DIMENSION_SUBCATEGORY_CODE
           ||V_DIMENSION_SUBCATEGORY_CN_NAME
           ||V_DIMENSION_SUB_DETAIL_CODE
           ||V_DIMENSION_SUB_DETAIL_CN_NAME
		   ||V_SPART_CODE
			||V_SPART_CN_NAME||' --202401版本新增SPART层级
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME  ;    
';

EXECUTE IMMEDIATE V_SQL;
DBMS_OUTPUT.PUT_LINE('执行第一段');
DBMS_OUTPUT.PUT_LINE(V_SQL);

V_SQL:='
INSERT INTO '||V_BASE_AMT_TEMP||'   
  (VIEW_FLAG,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
   LV0_PROD_RND_TEAM_CODE,
   LV1_PROD_RND_TEAM_CODE,
   LV2_PROD_RND_TEAM_CODE,
   '||V_LV3_PROD_RND_TEAM_CODE
    ||V_LV4_PART		--202407版本 IAS新增LV4层级
	||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
    ||V_DMS_CODE
    ||V_DMS_CN_NAME
	||V_COA_PART	--202405版本 数字能源新增COA层级
    ||V_DIMENSION_CODE
    ||V_DIMENSION_CN_NAME
    ||V_DIMENSION_SUBCATEGORY_CODE
    ||V_DIMENSION_SUBCATEGORY_CN_NAME
    ||V_DIMENSION_SUB_DETAIL_CODE
    ||V_DIMENSION_SUB_DETAIL_CN_NAME
	||V_SPART_CODE
	||V_SPART_CN_NAME||' --202401版本新增SPART层级
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   YEARS_AMT,
   PARENT_CODE,
   PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
   CALIBER_FLAG ,
   OVERSEA_FLAG ,  
   LV0_PROD_LIST_CODE  , 
   LV0_PROD_LIST_CN_NAME  
   )
  '||V_SQL_NEW||' 	--202405版本 数字能源新增COA层级
  
--LV2层级收敛
SELECT VIEW_FLAG,
       LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
       LV2_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,
         '||V_PROD_LV3_CODE
        ||V_PROD_LV4_CODE		--202407版本 IAS新增LV4层级
		||V_PROD_PROFITS_NAME||V_PROD_L1_NAME||V_PROD_L2_NAME
        ||V_PROD_DMS_CODE                 
        ||V_PROD_DMS_CN_NAME
		||V_PROD_COA_PART	--202405版本 数字能源新增COA层级
        ||V_PROD_DIMENSION_CODE
        ||V_PROD_DIMENSION_CN_NAME
        ||V_PROD_DIMENSION_SUBCATEGORY_CODE
        ||V_PROD_DIMENSION_SUBCATEGORY_CN_NAME
        ||V_PROD_DIMENSION_SUB_DETAIL_CODE
        ||V_PROD_DIMENSION_SUB_DETAIL_CN_NAME
		||V_PROD_SPART_CODE
		||V_PROD_SPART_CN_NAME||' --202401版本新增SPART层级
       LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,
       LV2_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
       ''LV2'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,
	   LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       CALIBER_FLAG,
        OVERSEA_FLAG,
        LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME 
  FROM '||V_BASE_AMT_TEMP||V_LV2_VIEW||  '
  AND GROUP_LEVEL=''ITEM''
 GROUP BY VIEW_FLAG,
          LV1_PROD_RND_TEAM_CODE,
		  LV1_PROD_RD_TEAM_CN_NAME,
          LV2_PROD_RND_TEAM_CODE, 
          LV0_PROD_RND_TEAM_CODE,
          LV2_PROD_RD_TEAM_CN_NAME,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME 
  UNION ALL
  
--LV1层级收敛
SELECT VIEW_FLAG,
       LV1_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
       LV1_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         '''' AS LV2_PROD_RND_TEAM_CODE,
         '||V_PROD_LV3_CODE
        ||V_PROD_LV4_CODE		--202407版本 IAS新增LV4层级
		||V_PROD_PROFITS_NAME||V_PROD_L1_NAME||V_PROD_L2_NAME
        ||V_PROD_DMS_CODE                 
        ||V_PROD_DMS_CN_NAME
		||V_PROD_COA_PART	--202405版本 数字能源新增COA层级
        ||V_PROD_DIMENSION_CODE
        ||V_PROD_DIMENSION_CN_NAME
        ||V_PROD_DIMENSION_SUBCATEGORY_CODE
        ||V_PROD_DIMENSION_SUBCATEGORY_CN_NAME
        ||V_PROD_DIMENSION_SUB_DETAIL_CODE
        ||V_PROD_DIMENSION_SUB_DETAIL_CN_NAME
		||V_PROD_SPART_CODE
		||V_PROD_SPART_CN_NAME||' --202401版本新增SPART层级
       LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,
       LV1_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
       ''LV1'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
	   LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME 
  FROM '||V_BASE_AMT_TEMP||V_LV1_VIEW||  '
  AND GROUP_LEVEL=''ITEM''
 GROUP BY VIEW_FLAG,
          LV1_PROD_RND_TEAM_CODE,
          LV1_PROD_RD_TEAM_CN_NAME,
          LV0_PROD_RND_TEAM_CODE,
		  LV0_PROD_RD_TEAM_CN_NAME,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME 
  UNION ALL
  
  --LV0层级收敛
SELECT VIEW_FLAG,
       LV0_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
         LV0_PROD_RND_TEAM_CODE,
         '''' AS LV1_PROD_RND_TEAM_CODE,
         '''' AS LV2_PROD_RND_TEAM_CODE,
         '||V_PROD_LV3_CODE
        ||V_PROD_LV4_CODE		--202407版本 IAS新增LV4层级
		||V_PROD_PROFITS_NAME||V_PROD_L1_NAME||V_PROD_L2_NAME
        ||V_PROD_DMS_CODE                 
        ||V_PROD_DMS_CN_NAME
		||V_PROD_COA_PART	--202405版本 数字能源新增COA层级
        ||V_PROD_DIMENSION_CODE
        ||V_PROD_DIMENSION_CN_NAME
        ||V_PROD_DIMENSION_SUBCATEGORY_CODE
        ||V_PROD_DIMENSION_SUBCATEGORY_CN_NAME
        ||V_PROD_DIMENSION_SUB_DETAIL_CODE
        ||V_PROD_DIMENSION_SUB_DETAIL_CN_NAME
		||V_PROD_SPART_CODE
		||V_PROD_SPART_CN_NAME||' --202401版本新增SPART层级
       LV0_PROD_RND_TEAM_CODE AS GROUP_CODE,
       LV0_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
       ''LV0'' AS GROUP_LEVEL,	--202405版本 ICT值统一更改为LV0
       SUM(YEARS_AMT) AS YEARS_AMT,
       NULL AS PARENT_CODE,
	   NULL AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       CALIBER_FLAG,
        OVERSEA_FLAG,
        LV0_PROD_LIST_CODE,
        LV0_PROD_LIST_CN_NAME 
  FROM '||V_BASE_AMT_TEMP||V_LV0_VIEW||  '
  AND GROUP_LEVEL=''ITEM''
 GROUP BY VIEW_FLAG, 
          LV0_PROD_RND_TEAM_CODE, 
          LV0_PROD_RD_TEAM_CN_NAME,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME ;    
';


EXECUTE IMMEDIATE V_SQL;
DBMS_OUTPUT.PUT_LINE('其他层级年度发货额收敛完成');
DBMS_OUTPUT.PUT_LINE(V_SQL);

--写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '其他层级年度发货额收敛完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  

V_EXCEPTION_FLAG := '5';   
V_SQL:='
TRUNCATE TABLE '||V_MID_TABLE||';
INSERT INTO '||V_MID_TABLE||'
  (VERSION_ID,
   PERIOD_YEAR,
   PERIOD_YEAR_TYPE,
   VIEW_FLAG,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
  '||V_PROFITS_NAME
   ||V_L1_NAME
   ||V_L2_NAME
   ||V_DMS_CODE                
   ||V_DMS_CN_NAME
   ||V_COA_PART	--202405版本 数字能源新增COA层级
   ||V_DIMENSION_CODE
   ||V_DIMENSION_CN_NAME
   ||V_DIMENSION_SUBCATEGORY_CODE
   ||V_DIMENSION_SUBCATEGORY_CN_NAME
   ||V_DIMENSION_SUB_DETAIL_CODE
   ||V_DIMENSION_SUB_DETAIL_CN_NAME
   ||V_SPART_CODE
   ||V_SPART_CN_NAME||' --202401版本新增SPART层级
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   WEIGHT_RATE,
   PARENT_CODE,
   PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG ,  
   LV0_PROD_LIST_CODE  , 
   LV0_PROD_LIST_CN_NAME)
  SELECT '||V_VERSION||' AS VERSION_ID,
         '''||V_PERIOD_YEAR||''' AS PERIOD_YEAR,
         ''S'' AS PERIOD_YEAR_TYPE,
         VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
         '||V_PROFITS_NAME
          ||V_L1_NAME
          ||V_L2_NAME
          ||V_DMS_CODE                
          ||V_DMS_CN_NAME
		  ||V_COA_PART	--202405版本 数字能源新增COA层级
          ||V_DIMENSION_CODE
          ||V_DIMENSION_CN_NAME
          ||V_DIMENSION_SUBCATEGORY_CODE
          ||V_DIMENSION_SUBCATEGORY_CN_NAME
          ||V_DIMENSION_SUB_DETAIL_CODE
          ||V_DIMENSION_SUB_DETAIL_CN_NAME
		  ||V_SPART_CODE
		  ||V_SPART_CN_NAME||' --202401版本新增SPART层级
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         '||V_SQL_WEIGHT||'
         PARENT_CODE,
		 PARENT_CN_NAME , --202403版本 新增PARENT_CN_NAME
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG ,  
         LV0_PROD_LIST_CODE  , 
         LV0_PROD_LIST_CN_NAME
    FROM '||V_BASE_AMT_TEMP||';
';


EXECUTE IMMEDIATE V_SQL;

--写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '本'||V_VERSION||'版本权重收敛完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
V_EXCEPTION_FLAG := '6';     
V_SQL := 'DELETE FROM '||V_TARGET_TABLE||' WHERE VERSION_ID = '||V_VERSION||';';
EXECUTE IMMEDIATE V_SQL;




--写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '权重表同版本数据删除完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

V_EXCEPTION_FLAG := '7';  
V_SQL:='   
INSERT INTO '||V_TARGET_TABLE||'
  (--ID,
   VERSION_ID,
   PERIOD_YEAR,
   PERIOD_YEAR_TYPE,
   VIEW_FLAG,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
   '||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
    ||V_DMS_CODE                
    ||V_DMS_CN_NAME
	||V_COA_PART	--202405版本 数字能源新增COA层级
    ||V_DIMENSION_CODE
    ||V_DIMENSION_CN_NAME
    ||V_DIMENSION_SUBCATEGORY_CODE
    ||V_DIMENSION_SUBCATEGORY_CN_NAME
    ||V_DIMENSION_SUB_DETAIL_CODE
    ||V_DIMENSION_SUB_DETAIL_CN_NAME
	||V_SPART_CODE
	||V_SPART_CN_NAME||' --202401版本新增SPART层级
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   WEIGHT_RATE,
   PARENT_CODE,
   PARENT_CN_NAME , --202403版本 新增PARENT_CN_NAME
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG ,  
   LV0_PROD_LIST_CODE  , 
   LV0_PROD_LIST_CN_NAME )
  SELECT --'||V_SEQUENCE||' AS ID,
         '||V_VERSION||' AS VERSION_ID,
         '''||V_PERIOD_YEAR||''' AS PERIOD_YEAR,
         ''S'' AS PERIOD_YEAR_TYPE,
         VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
        '||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
         ||V_DMS_CODE                
         ||V_DMS_CN_NAME
		 ||V_COA_PART	--202405版本 数字能源新增COA层级
         ||V_DIMENSION_CODE
         ||V_DIMENSION_CN_NAME
         ||V_DIMENSION_SUBCATEGORY_CODE
         ||V_DIMENSION_SUBCATEGORY_CN_NAME
         ||V_DIMENSION_SUB_DETAIL_CODE
         ||V_DIMENSION_SUB_DETAIL_CN_NAME
		 ||V_SPART_CODE
		 ||V_SPART_CN_NAME||' --202401版本新增SPART层级
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         WEIGHT_RATE,
         PARENT_CODE,
		 PARENT_CN_NAME , --202403版本 新增PARENT_CN_NAME
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG ,  
         LV0_PROD_LIST_CODE  , 
         LV0_PROD_LIST_CN_NAME 
    FROM '||V_MID_TABLE||';';
    

EXECUTE IMMEDIATE V_SQL;
/*-------------------------------------------------------------多选权重-----------------------------------------------------*/
V_SQL:='
DROP TABLE IF EXISTS  BASE_AMT_TEMP_F ;
CREATE TEMPORARY TABLE BASE_AMT_TEMP_F (
    VERSION_ID BIGINT,
    LV0_PROD_RND_TEAM_CODE     VARCHAR(50),
    LV0_PROD_RD_TEAM_CN_NAME     VARCHAR(200),
    LV1_PROD_RND_TEAM_CODE     VARCHAR(50),
    LV1_PROD_RD_TEAM_CN_NAME     VARCHAR(200),
    LV2_PROD_RND_TEAM_CODE     VARCHAR(50),
    LV2_PROD_RD_TEAM_CN_NAME     VARCHAR(200),
    LV3_PROD_RND_TEAM_CODE     VARCHAR(50),/*7月新增LV3重量级团队*/
    LV3_PROD_RD_TEAM_CN_NAME     VARCHAR(200),
    LV4_PROD_RND_TEAM_CODE     VARCHAR(50),		--202407版本 IAS新增LV4层级
    LV4_PROD_RD_TEAM_CN_NAME     VARCHAR(200),
    L1_NAME    VARCHAR(200),/*7月新增盈利颗粒度维度*/
    L2_NAME    VARCHAR(200),
	COA_CODE		VARCHAR(200),	--202405版本 数字能源新增COA层级
	COA_CN_NAME     VARCHAR(200),
    DIMENSION_CODE    VARCHAR(500), /*9月新增量纲颗粒度维度 */
    DIMENSION_CN_NAME    VARCHAR(2000) ,
    DIMENSION_EN_NAME    VARCHAR(2000) ,
    DIMENSION_SUBCATEGORY_CODE    VARCHAR(500) ,
    DIMENSION_SUBCATEGORY_CN_NAME    VARCHAR(2000) ,
    DIMENSION_SUBCATEGORY_EN_NAME    VARCHAR(2000) ,
    DIMENSION_SUB_DETAIL_CODE    VARCHAR(500) ,
    DIMENSION_SUB_DETAIL_CN_NAME    VARCHAR(2000) ,
    DIMENSION_SUB_DETAIL_EN_NAME    VARCHAR(2000) , 
	SPART_CODE VARCHAR(500) ,
	SPART_CN_NAME VARCHAR(500) , --202401版本新增SPART层级	
    PROFITS_NAME  VARCHAR(200),  /*7月新增盈利颗粒度字段*/
    DMS_CODE    VARCHAR(50),     /*9月新增量纲颗粒度字段*/            
    DMS_CN_NAME      VARCHAR(200),
    TOP_L3_CEG_CODE     VARCHAR(50),
    TOP_L3_CEG_SHORT_CN_NAME     VARCHAR(200),
    TOP_L4_CEG_CODE    VARCHAR(50),
    TOP_L4_CEG_SHORT_CN_NAME VARCHAR(200),    
    TOP_CATEGORY_CODE  VARCHAR(50),
    TOP_CATEGORY_CN_NAME  VARCHAR(200),
    PROD_RND_TEAM_CODE  VARCHAR(50),
    PROD_RND_TEAM_CN_NAME  VARCHAR(200),
    GROUP_CODE  VARCHAR(50),
    GROUP_CN_NAME  VARCHAR(1000),
    GROUP_LEVEL  VARCHAR(50),
    YEARS_AMT NUMERIC,
    PARENT_CODE  VARCHAR(50),
	PARENT_CN_NAME VARCHAR(200),  --202403版本 新增PARENT_CN_NAME
    VIEW_FLAG  VARCHAR(2),
    CALIBER_FLAG  VARCHAR(2), /*7月新增业务口径字段*/
    OVERSEA_FLAG  VARCHAR(2),  
    LV0_PROD_LIST_CODE VARCHAR(50),
	LV0_PROD_LIST_CN_NAME VARCHAR(200),
    ITEM_CODE CHARACTER VARYING(50),
    ITEM_CN_NAME CHARACTER VARYING(2000)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY ROUNDROBIN;';


EXECUTE IMMEDIATE V_SQL;

V_SQL:='
INSERT INTO  BASE_AMT_TEMP_F
  (VIEW_FLAG,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
   LV0_PROD_RND_TEAM_CODE,
   LV1_PROD_RND_TEAM_CODE,
   LV2_PROD_RND_TEAM_CODE,
   '||V_LV3_PROD_RND_TEAM_CODE
    ||V_LV4_PART		--202407版本 IAS新增LV4层级
	||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
    ||V_DMS_CODE
    ||V_DMS_CN_NAME
	||V_COA_PART		--202405版本 数字能源新增COA层级
    ||V_DIMENSION_CODE
    ||V_DIMENSION_CN_NAME
    ||V_DIMENSION_SUBCATEGORY_CODE
    ||V_DIMENSION_SUBCATEGORY_CN_NAME
    ||V_DIMENSION_SUB_DETAIL_CODE
    ||V_DIMENSION_SUB_DETAIL_CN_NAME
	||V_SPART_CODE
	||V_SPART_CN_NAME||' --202401版本新增SPART层级
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   YEARS_AMT,
   PARENT_CODE,
   PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
   CALIBER_FLAG,
   OVERSEA_FLAG, 
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME,
   ITEM_CODE,
   ITEM_CN_NAME
   )
   
--品类层级
SELECT VIEW_FLAG,
       PROD_RND_TEAM_CODE,
       PROD_RND_TEAM_CN_NAME,
       LV0_PROD_RND_TEAM_CODE,
       LV1_PROD_RND_TEAM_CODE,
       LV2_PROD_RND_TEAM_CODE,
       '||V_LV3_PROD_RND_TEAM_CODE
        ||V_LV4_PART		--202407版本 IAS新增LV4层级
		||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
        ||V_DMS_CODE                
        ||V_DMS_CN_NAME
		||V_COA_PART		--202405版本 数字能源新增COA层级
        ||V_DIMENSION_CODE
        ||V_DIMENSION_CN_NAME
        ||V_DIMENSION_SUBCATEGORY_CODE
        ||V_DIMENSION_SUBCATEGORY_CN_NAME
        ||V_DIMENSION_SUB_DETAIL_CODE
        ||V_DIMENSION_SUB_DETAIL_CN_NAME
		||V_SPART_CODE
		||V_SPART_CN_NAME||' --202401版本新增SPART层级
       TOP_CATEGORY_CODE AS GROUP_CODE,
       TOP_CATEGORY_CN_NAME AS GROUP_CN_NAME,
       ''F_CATEGORY'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       TOP_L4_CEG_CODE AS PARENT_CODE,
	   TOP_L4_CEG_SHORT_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
		 GROUP_CODE,GROUP_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
  WHERE  GROUP_LEVEL=''ITEM''
 GROUP BY VIEW_FLAG,
          PROD_RND_TEAM_CODE,
          PROD_RND_TEAM_CN_NAME,
          TOP_L4_CEG_CODE,  
		  TOP_L4_CEG_SHORT_CN_NAME	,	  
          TOP_CATEGORY_CODE,
          TOP_CATEGORY_CN_NAME,
          LV0_PROD_RND_TEAM_CODE,
          LV1_PROD_RND_TEAM_CODE,
          LV2_PROD_RND_TEAM_CODE,
          '||V_LV3_PROD_RND_TEAM_CODE
           ||V_LV4_PART		--202407版本 IAS新增LV4层级
		   ||V_PROFITS_NAME
           ||V_L1_NAME
           ||V_L2_NAME
           ||V_DMS_CODE                
           ||V_DMS_CN_NAME
		   ||V_COA_PART		--202405版本 数字能源新增COA层级
           ||V_DIMENSION_CODE
           ||V_DIMENSION_CN_NAME
           ||V_DIMENSION_SUBCATEGORY_CODE
           ||V_DIMENSION_SUBCATEGORY_CN_NAME
           ||V_DIMENSION_SUB_DETAIL_CODE
           ||V_DIMENSION_SUB_DETAIL_CN_NAME
		   ||V_SPART_CODE
			||V_SPART_CN_NAME||' --202401版本新增SPART层级
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
		 GROUP_CODE,GROUP_CN_NAME 
         
UNION ALL

--MODL层级收敛
SELECT VIEW_FLAG,
       PROD_RND_TEAM_CODE,
       PROD_RND_TEAM_CN_NAME,
       LV0_PROD_RND_TEAM_CODE,
       LV1_PROD_RND_TEAM_CODE,
       LV2_PROD_RND_TEAM_CODE,
       '||V_LV3_PROD_RND_TEAM_CODE
        ||V_LV4_PART		--202407版本 IAS新增LV4层级
		||V_PROFITS_NAME
		||V_L1_NAME
		||V_L2_NAME
        ||V_DMS_CODE                
        ||V_DMS_CN_NAME
		||V_COA_PART		--202405版本 数字能源新增COA层级
        ||V_DIMENSION_CODE
        ||V_DIMENSION_CN_NAME
        ||V_DIMENSION_SUBCATEGORY_CODE
        ||V_DIMENSION_SUBCATEGORY_CN_NAME
        ||V_DIMENSION_SUB_DETAIL_CODE
        ||V_DIMENSION_SUB_DETAIL_CN_NAME
		||V_SPART_CODE
   ||V_SPART_CN_NAME||' --202401版本新增SPART层级
       TOP_L4_CEG_CODE AS GROUP_CODE,
       TOP_L4_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,
       ''F_MODL'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       TOP_L3_CEG_CODE AS PARENT_CODE,
	   TOP_L3_CEG_SHORT_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME ,
	 GROUP_CODE,GROUP_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
   WHERE  GROUP_LEVEL=''ITEM''
 GROUP BY VIEW_FLAG,
          PROD_RND_TEAM_CODE,
          PROD_RND_TEAM_CN_NAME,
          TOP_L3_CEG_CODE,
          TOP_L4_CEG_CODE,
          TOP_L4_CEG_SHORT_CN_NAME, 
			TOP_L3_CEG_SHORT_CN_NAME,		  
          LV0_PROD_RND_TEAM_CODE,
          LV1_PROD_RND_TEAM_CODE,
          LV2_PROD_RND_TEAM_CODE,
          '||V_LV3_PROD_RND_TEAM_CODE
           ||V_LV4_PART		--202407版本 IAS新增LV4层级
		   ||V_PROFITS_NAME
           ||V_L1_NAME
           ||V_L2_NAME
           ||V_DMS_CODE                
           ||V_DMS_CN_NAME
		   ||V_COA_PART		--202405版本 数字能源新增COA层级
           ||V_DIMENSION_CODE
           ||V_DIMENSION_CN_NAME
           ||V_DIMENSION_SUBCATEGORY_CODE
           ||V_DIMENSION_SUBCATEGORY_CN_NAME
           ||V_DIMENSION_SUB_DETAIL_CODE
           ||V_DIMENSION_SUB_DETAIL_CN_NAME
		   ||V_SPART_CODE
		   ||V_SPART_CN_NAME||' --202401版本新增SPART层级
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
		 GROUP_CODE,GROUP_CN_NAME   
         
         
UNION ALL

--CEG专家团层级收敛
SELECT VIEW_FLAG,
       PROD_RND_TEAM_CODE,
       PROD_RND_TEAM_CN_NAME,
       LV0_PROD_RND_TEAM_CODE,
       LV1_PROD_RND_TEAM_CODE,
       LV2_PROD_RND_TEAM_CODE,
       '||V_LV3_PROD_RND_TEAM_CODE
        ||V_LV4_PART		--202407版本 IAS新增LV4层级
		||V_PROFITS_NAME
		||V_L1_NAME
		||V_L2_NAME
        ||V_DMS_CODE                
        ||V_DMS_CN_NAME
		||V_COA_PART		--202405版本 数字能源新增COA层级
        ||V_DIMENSION_CODE
        ||V_DIMENSION_CN_NAME
        ||V_DIMENSION_SUBCATEGORY_CODE
        ||V_DIMENSION_SUBCATEGORY_CN_NAME
        ||V_DIMENSION_SUB_DETAIL_CODE
        ||V_DIMENSION_SUB_DETAIL_CN_NAME
		||V_SPART_CODE
   ||V_SPART_CN_NAME||' --202401版本新增SPART层级
       TOP_L3_CEG_CODE AS GROUP_CODE,
       TOP_L3_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,
       ''F_CEG'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT, --202403版本 新增PARENT_CN_NAME
       '||V_CEG_PARENT_CODE||'  
        CALIBER_FLAG,
        OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
		 GROUP_CODE,GROUP_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
   WHERE  GROUP_LEVEL=''ITEM''
 GROUP BY VIEW_FLAG,
          PROD_RND_TEAM_CODE,
          PROD_RND_TEAM_CN_NAME,
          TOP_L3_CEG_CODE,
          TOP_L3_CEG_SHORT_CN_NAME,
          LV0_PROD_RND_TEAM_CODE,
          LV1_PROD_RND_TEAM_CODE,
          LV2_PROD_RND_TEAM_CODE,
          '||V_LV3_PROD_RND_TEAM_CODE
           ||V_LV4_PART		--202407版本 IAS新增LV4层级
		   ||V_PROFITS_NAME
           ||V_L1_NAME
           ||V_L2_NAME
           ||V_DMS_CODE                
           ||V_DMS_CN_NAME
		   ||V_COA_PART		--202405版本 数字能源新增COA层级
           ||V_DIMENSION_CODE
           ||V_DIMENSION_CN_NAME
           ||V_DIMENSION_SUBCATEGORY_CODE
           ||V_DIMENSION_SUBCATEGORY_CN_NAME
           ||V_DIMENSION_SUB_DETAIL_CODE
           ||V_DIMENSION_SUB_DETAIL_CN_NAME
		   ||V_SPART_CODE
			||V_SPART_CN_NAME||' --202401版本新增SPART层级
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
		 GROUP_CODE,GROUP_CN_NAME  ;    
';

EXECUTE IMMEDIATE V_SQL;
DBMS_OUTPUT.PUT_LINE('执行第一段');



V_SQL:='
INSERT INTO BASE_AMT_TEMP_F
  (VIEW_FLAG,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
   LV0_PROD_RND_TEAM_CODE,
   LV1_PROD_RND_TEAM_CODE,
   LV2_PROD_RND_TEAM_CODE,
   '||V_LV3_PROD_RND_TEAM_CODE
    ||V_LV4_PART		--202407版本 IAS新增LV4层级
	||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
    ||V_DMS_CODE
    ||V_DMS_CN_NAME
	||V_COA_PART		--202405版本 数字能源新增COA层级
    ||V_DIMENSION_CODE
    ||V_DIMENSION_CN_NAME
    ||V_DIMENSION_SUBCATEGORY_CODE
    ||V_DIMENSION_SUBCATEGORY_CN_NAME
    ||V_DIMENSION_SUB_DETAIL_CODE
    ||V_DIMENSION_SUB_DETAIL_CN_NAME
	||V_SPART_CODE
   ||V_SPART_CN_NAME||' --202401版本新增SPART层级
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   YEARS_AMT,
   PARENT_CODE,
   PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
   CALIBER_FLAG ,
   OVERSEA_FLAG ,  
   LV0_PROD_LIST_CODE  , 
   LV0_PROD_LIST_CN_NAME ,
   ITEM_CODE,
   ITEM_CN_NAME 
   )
  '||V_SQL_NEW_F||' 
  
--LV2层级收敛
SELECT VIEW_FLAG,
       LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
       LV2_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
       LV0_PROD_RND_TEAM_CODE,
       LV1_PROD_RND_TEAM_CODE,
       LV2_PROD_RND_TEAM_CODE,
	   '||V_PROD_LV3_CODE
        ||V_PROD_LV4_CODE		--202407版本 IAS新增LV4层级
		||V_PROD_PROFITS_NAME||V_PROD_L1_NAME||V_PROD_L2_NAME
        ||V_PROD_DMS_CODE                 
        ||V_PROD_DMS_CN_NAME
		||V_PROD_COA_PART		--202405版本 数字能源新增COA层级
        ||V_PROD_DIMENSION_CODE
        ||V_PROD_DIMENSION_CN_NAME
        ||V_PROD_DIMENSION_SUBCATEGORY_CODE
        ||V_PROD_DIMENSION_SUBCATEGORY_CN_NAME
        ||V_PROD_DIMENSION_SUB_DETAIL_CODE
        ||V_PROD_DIMENSION_SUB_DETAIL_CN_NAME
		||V_PROD_SPART_CODE
		||V_PROD_SPART_CN_NAME||' --202401版本新增SPART层级
       LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,
       LV2_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
       ''F_LV2'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,
	   LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       CALIBER_FLAG,
        OVERSEA_FLAG,
        LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
		 GROUP_CODE,GROUP_CN_NAME 
  FROM '||V_BASE_AMT_TEMP||V_LV2_VIEW||  '
  AND GROUP_LEVEL=''ITEM''
 GROUP BY VIEW_FLAG,
          LV1_PROD_RND_TEAM_CODE,
		  LV1_PROD_RD_TEAM_CN_NAME,
          LV2_PROD_RND_TEAM_CODE, 
          LV0_PROD_RND_TEAM_CODE,
          LV2_PROD_RD_TEAM_CN_NAME,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME ,
		 GROUP_CODE,GROUP_CN_NAME
  UNION ALL
  
--LV1层级收敛
SELECT VIEW_FLAG,
       LV1_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
       LV1_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
       LV0_PROD_RND_TEAM_CODE,
       LV1_PROD_RND_TEAM_CODE,
       '''' AS LV2_PROD_RND_TEAM_CODE,
       '||V_PROD_LV3_CODE
        ||V_PROD_LV4_CODE		--202407版本 IAS新增LV4层级
		||V_PROD_PROFITS_NAME||V_PROD_L1_NAME||V_PROD_L2_NAME
        ||V_PROD_DMS_CODE                 
        ||V_PROD_DMS_CN_NAME
		||V_PROD_COA_PART		--202405版本 数字能源新增COA层级
        ||V_PROD_DIMENSION_CODE
        ||V_PROD_DIMENSION_CN_NAME
        ||V_PROD_DIMENSION_SUBCATEGORY_CODE
        ||V_PROD_DIMENSION_SUBCATEGORY_CN_NAME
        ||V_PROD_DIMENSION_SUB_DETAIL_CODE
        ||V_PROD_DIMENSION_SUB_DETAIL_CN_NAME
		||V_PROD_SPART_CODE
		||V_PROD_SPART_CN_NAME||' --202401版本新增SPART层级
       LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,
       LV1_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
       ''F_LV1'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
	   LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME,
		 GROUP_CODE,GROUP_CN_NAME 
  FROM '||V_BASE_AMT_TEMP||V_LV1_VIEW||  '
  AND GROUP_LEVEL=''ITEM''
 GROUP BY VIEW_FLAG,
          LV1_PROD_RND_TEAM_CODE,
          LV1_PROD_RD_TEAM_CN_NAME,
          LV0_PROD_RND_TEAM_CODE,
		  LV0_PROD_RD_TEAM_CN_NAME,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME ,
		 GROUP_CODE,GROUP_CN_NAME
  UNION ALL
  
  --LV0层级收敛
SELECT VIEW_FLAG,
       LV0_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
       LV0_PROD_RND_TEAM_CODE,
       '''' AS LV1_PROD_RND_TEAM_CODE,
       '''' AS LV2_PROD_RND_TEAM_CODE,
       '||V_PROD_LV3_CODE
        ||V_PROD_LV4_CODE		--202407版本 IAS新增LV4层级
		||V_PROD_PROFITS_NAME||V_PROD_L1_NAME||V_PROD_L2_NAME
        ||V_PROD_DMS_CODE                 
        ||V_PROD_DMS_CN_NAME
		||V_PROD_COA_PART		--202405版本 数字能源新增COA层级
        ||V_PROD_DIMENSION_CODE
        ||V_PROD_DIMENSION_CN_NAME
        ||V_PROD_DIMENSION_SUBCATEGORY_CODE
        ||V_PROD_DIMENSION_SUBCATEGORY_CN_NAME
        ||V_PROD_DIMENSION_SUB_DETAIL_CODE
        ||V_PROD_DIMENSION_SUB_DETAIL_CN_NAME
		||V_PROD_SPART_CODE
		||V_PROD_SPART_CN_NAME||' --202401版本新增SPART层级
       LV0_PROD_RND_TEAM_CODE AS GROUP_CODE,
       LV0_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
       ''F_LV0'' AS GROUP_LEVEL,	--202405版本 ICT值统一更改为LV0
       SUM(YEARS_AMT) AS YEARS_AMT,
       NULL AS PARENT_CODE,
	   NULL AS PARENT_CN_NAME , --202403版本 新增PARENT_CN_NAME
       CALIBER_FLAG,
        OVERSEA_FLAG,
        LV0_PROD_LIST_CODE,
        LV0_PROD_LIST_CN_NAME,
		 GROUP_CODE,GROUP_CN_NAME
  FROM '||V_BASE_AMT_TEMP||V_LV0_VIEW||  '
  AND GROUP_LEVEL=''ITEM''
 GROUP BY VIEW_FLAG, 
          LV0_PROD_RND_TEAM_CODE, 
          LV0_PROD_RD_TEAM_CN_NAME,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME,
		 GROUP_CODE,GROUP_CN_NAME;    
';

EXECUTE IMMEDIATE V_SQL;
DBMS_OUTPUT.PUT_LINE('其他层级年度发货额收敛完成');

--写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '其他层级年度发货额收敛完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  

V_EXCEPTION_FLAG := '8';   
V_SQL:='
TRUNCATE TABLE '||V_MID_TABLE||';
INSERT INTO '||V_MID_TABLE||'
  (VERSION_ID,
   PERIOD_YEAR,
   PERIOD_YEAR_TYPE,
   VIEW_FLAG,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
  '||V_PROFITS_NAME
   ||V_L1_NAME
   ||V_L2_NAME
   ||V_DMS_CODE                
   ||V_DMS_CN_NAME
   ||V_COA_PART		--202405版本 数字能源新增COA层级
   ||V_DIMENSION_CODE
   ||V_DIMENSION_CN_NAME
   ||V_DIMENSION_SUBCATEGORY_CODE
   ||V_DIMENSION_SUBCATEGORY_CN_NAME
   ||V_DIMENSION_SUB_DETAIL_CODE
   ||V_DIMENSION_SUB_DETAIL_CN_NAME
   ||V_SPART_CODE
   ||V_SPART_CN_NAME||' --202401版本新增SPART层级
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   WEIGHT_RATE,
   PARENT_CODE,
   PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG ,  
   LV0_PROD_LIST_CODE  , 
   LV0_PROD_LIST_CN_NAME)
  SELECT '||V_VERSION||' AS VERSION_ID,
         '''||V_PERIOD_YEAR||''' AS PERIOD_YEAR,
         ''S'' AS PERIOD_YEAR_TYPE,
         VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
         '||V_PROFITS_NAME
          ||V_L1_NAME
          ||V_L2_NAME
          ||V_DMS_CODE                
          ||V_DMS_CN_NAME
		  ||V_COA_PART		--202405版本 数字能源新增COA层级
          ||V_DIMENSION_CODE
          ||V_DIMENSION_CN_NAME
          ||V_DIMENSION_SUBCATEGORY_CODE
          ||V_DIMENSION_SUBCATEGORY_CN_NAME
          ||V_DIMENSION_SUB_DETAIL_CODE
          ||V_DIMENSION_SUB_DETAIL_CN_NAME
		  ||V_SPART_CODE
			||V_SPART_CN_NAME||' --202401版本新增SPART层级
         ITEM_CODE,
         ITEM_CN_NAME,
         SUBSTR(GROUP_LEVEL, 3)||''_ITEM'' AS GROUP_LEVEL,
         YEARS_AMT/NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,VIEW_FLAG,GROUP_LEVEL,
                              PROD_RND_TEAM_CODE,
							  NVL(L1_NAME,''L1''),NVL(L2_NAME,''L2''),
                              NVL(DIMENSION_CODE,''D1''),NVL(DIMENSION_SUBCATEGORY_CODE,''D2''),NVL(DIMENSION_SUB_DETAIL_CODE,''D3''),
							  NVL(SPART_CODE,''D4''), --202401版本新增SPART层级
                              NVL(DMS_CODE,''DD''),
							  NVL(COA_CODE,''D5''),	--202405版本 数字能源新增COA层级
                              PARENT_CODE,GROUP_CODE),0), 
         GROUP_CODE AS PARENT_CODE,
		 GROUP_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG ,  
         LV0_PROD_LIST_CODE  , 
         LV0_PROD_LIST_CN_NAME
    FROM BASE_AMT_TEMP_F ;
';


EXECUTE IMMEDIATE V_SQL;

--写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '本'||V_VERSION||'版本权重收敛完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
V_EXCEPTION_FLAG := '9';     
V_SQL := 'DELETE FROM '||V_TARGET_TABLE||' WHERE  GROUP_LEVEL = ''F_ITEM'' AND VERSION_ID = '||V_VERSION||';';
EXECUTE IMMEDIATE V_SQL;

--写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '权重表同版本数据删除完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

V_EXCEPTION_FLAG := '10';  
V_SQL:='   
INSERT INTO '||V_TARGET_TABLE||'
  (--ID,
   VERSION_ID,
   PERIOD_YEAR,
   PERIOD_YEAR_TYPE,
   VIEW_FLAG,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
   '||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
    ||V_DMS_CODE                
    ||V_DMS_CN_NAME
	||V_COA_PART	--202401版本新增SPART层级
    ||V_DIMENSION_CODE
    ||V_DIMENSION_CN_NAME
    ||V_DIMENSION_SUBCATEGORY_CODE
    ||V_DIMENSION_SUBCATEGORY_CN_NAME
    ||V_DIMENSION_SUB_DETAIL_CODE
    ||V_DIMENSION_SUB_DETAIL_CN_NAME
	||V_SPART_CODE
	||V_SPART_CN_NAME||' --202401版本新增SPART层级
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   WEIGHT_RATE,
   PARENT_CODE,
   PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG ,  
   LV0_PROD_LIST_CODE  , 
   LV0_PROD_LIST_CN_NAME )
  SELECT --'||V_SEQUENCE||' AS ID,
         '||V_VERSION||' AS VERSION_ID,
         '''||V_PERIOD_YEAR||''' AS PERIOD_YEAR,
         ''S'' AS PERIOD_YEAR_TYPE,
         VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
        '||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
         ||V_DMS_CODE                
         ||V_DMS_CN_NAME
		 ||V_COA_PART	--202401版本新增SPART层级
         ||V_DIMENSION_CODE
         ||V_DIMENSION_CN_NAME
         ||V_DIMENSION_SUBCATEGORY_CODE
         ||V_DIMENSION_SUBCATEGORY_CN_NAME
         ||V_DIMENSION_SUB_DETAIL_CODE
         ||V_DIMENSION_SUB_DETAIL_CN_NAME
		 ||V_SPART_CODE
		 ||V_SPART_CN_NAME||' --202401版本新增SPART层级
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         WEIGHT_RATE,
         PARENT_CODE,
		 PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG ,  
         LV0_PROD_LIST_CODE  , 
         LV0_PROD_LIST_CN_NAME 
    FROM '||V_MID_TABLE||';';
    

EXECUTE IMMEDIATE V_SQL;

/*-----------------------------------------------------------------------插入反向视角数据，通用颗粒度---------------------------------------------------------------*/
DBMS_OUTPUT.PUT_LINE('插入反向视角数据');  
  IF V_DIMENSION_TYPE = 'U' THEN
  
 DBMS_OUTPUT.PUT_LINE('规格品年度发货额收敛完成');
/*********************************************分视角后其他层级两年发货额收敛*********************************************/

V_EXCEPTION_FLAG := '11';   
/*---------视角7，VIEW_FLAG=6，group_level = 'CEG,MODLE,CATE,LV1,LV2,LV3'----------*/
 
V_SQL:='
INSERT INTO '||V_BASE_AMT_TEMP||'
  (VIEW_FLAG,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   YEARS_AMT,
   PARENT_CODE,
   PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
   TOP_L3_CEG_CODE,
   TOP_L3_CEG_SHORT_CN_NAME,
   TOP_L4_CEG_CODE,
   TOP_L4_CEG_SHORT_CN_NAME,   
   TOP_CATEGORY_CODE,
   TOP_CATEGORY_CN_NAME,
   CALIBER_FLAG,
   OVERSEA_FLAG, 
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME  
   )
--ITEM
SELECT 6,
       TOP_CATEGORY_CODE,
       TOP_CATEGORY_CN_NAME,
       GROUP_CODE AS GROUP_CODE,
       GROUP_CN_NAME AS GROUP_CN_NAME,
       ''ITEM'' AS GROUP_LEVEL,
       YEARS_AMT,
	   '||V_REV_ITEM_PARENT||'		--202407版本 IAS新增LV4层级
       --LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,
	   --LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, 
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME,   
       TOP_CATEGORY_CODE,
       TOP_CATEGORY_CN_NAME,
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
  WHERE VIEW_FLAG = '||V_REV_VIEW_FLAG||'	--202407版本 IAS新增视角7
	AND GROUP_LEVEL=''ITEM''
	
UNION ALL

'||V_LV4_REV_VIEW6_SQL||'	--202407版本 IAS新增LV4层级

--LV3
SELECT 6,
       TOP_CATEGORY_CODE,
       TOP_CATEGORY_CN_NAME,
       LV3_PROD_RND_TEAM_CODE AS GROUP_CODE,
       LV3_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
       ''LV3'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       LV2_PROD_RND_TEAM_CODE AS PARENT_CODE, 
	   LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,  --202403版本 新增PARENT_CN_NAME
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME,   
       TOP_CATEGORY_CODE,
       TOP_CATEGORY_CN_NAME,
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
  WHERE VIEW_FLAG = '||V_REV_VIEW_FLAG||'	--202407版本 IAS新增视角7
	AND GROUP_LEVEL=''ITEM''
 GROUP BY PROD_RND_TEAM_CODE,
          PROD_RND_TEAM_CN_NAME,        
          LV3_PROD_RND_TEAM_CODE,
          LV3_PROD_RD_TEAM_CN_NAME,
          LV2_PROD_RND_TEAM_CODE,
		  LV2_PROD_RD_TEAM_CN_NAME,
          TOP_L3_CEG_CODE,
          TOP_L3_CEG_SHORT_CN_NAME,
          TOP_L4_CEG_CODE,
          TOP_L4_CEG_SHORT_CN_NAME,   
          TOP_CATEGORY_CODE,
          TOP_CATEGORY_CN_NAME,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
         
UNION ALL

--LV2
SELECT 6,
       TOP_CATEGORY_CODE,
       TOP_CATEGORY_CN_NAME,
       LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,
       LV2_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
       ''LV2'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,
	   LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,  --202403版本 新增PARENT_CN_NAME
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME,   
       TOP_CATEGORY_CODE,
       TOP_CATEGORY_CN_NAME,
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
  WHERE VIEW_FLAG = '||V_REV_VIEW_FLAG||'	--202407版本 IAS新增视角7
	AND GROUP_LEVEL=''ITEM''
 GROUP BY LV2_PROD_RND_TEAM_CODE,
          LV2_PROD_RD_TEAM_CN_NAME,
          LV1_PROD_RND_TEAM_CODE,
		  LV1_PROD_RD_TEAM_CN_NAME,
          TOP_L3_CEG_CODE,
          TOP_L3_CEG_SHORT_CN_NAME,
          TOP_L4_CEG_CODE,
          TOP_L4_CEG_SHORT_CN_NAME,   
          TOP_CATEGORY_CODE,
          TOP_CATEGORY_CN_NAME,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
         
UNION ALL

--LV1
SELECT 6,
       TOP_CATEGORY_CODE,
       TOP_CATEGORY_CN_NAME,
       LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,
       LV1_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
       ''LV1'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       TOP_CATEGORY_CODE AS PARENT_CODE,
	   TOP_CATEGORY_CN_NAME AS PARENT_CN_NAME,  --202403版本 新增PARENT_CN_NAME
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME,   
       TOP_CATEGORY_CODE,
       TOP_CATEGORY_CN_NAME,
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
  WHERE VIEW_FLAG = '||V_REV_VIEW_FLAG||'	--202407版本 IAS新增视角7
	AND GROUP_LEVEL=''ITEM''
 GROUP BY LV1_PROD_RND_TEAM_CODE,
          LV1_PROD_RD_TEAM_CN_NAME,
          TOP_CATEGORY_CODE,
          TOP_L3_CEG_CODE,
          TOP_L3_CEG_SHORT_CN_NAME,
          TOP_L4_CEG_CODE,
          TOP_L4_CEG_SHORT_CN_NAME,   
          TOP_CATEGORY_CODE,
          TOP_CATEGORY_CN_NAME,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
         
UNION ALL  

--CATEGORY
SELECT 6,
       TOP_CATEGORY_CODE,
       TOP_CATEGORY_CN_NAME,
       TOP_CATEGORY_CODE AS GROUP_CODE,
       TOP_CATEGORY_CN_NAME AS GROUP_CN_NAME,
       ''CATEGORY'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       TOP_L4_CEG_CODE AS PARENT_CODE,
	   TOP_L4_CEG_SHORT_CN_NAME AS PARENT_CN_NAME,  --202403版本 新增PARENT_CN_NAME
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME,  
       TOP_CATEGORY_CODE,
       TOP_CATEGORY_CN_NAME,  
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
  WHERE VIEW_FLAG = '||V_REV_VIEW_FLAG||'	--202407版本 IAS新增视角7
	AND GROUP_LEVEL=''ITEM''
 GROUP BY LV0_PROD_RND_TEAM_CODE,
          LV0_PROD_RD_TEAM_CN_NAME,        
          TOP_CATEGORY_CODE,
          TOP_L3_CEG_CODE,
          TOP_L3_CEG_SHORT_CN_NAME,
          TOP_L4_CEG_CODE,
          TOP_L4_CEG_SHORT_CN_NAME,   
          TOP_CATEGORY_CODE,
          TOP_CATEGORY_CN_NAME,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
         
UNION ALL 

--MODL
SELECT 6,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME, 
       TOP_L4_CEG_CODE AS GROUP_CODE,
       TOP_L4_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,
       ''MODL'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       TOP_L3_CEG_CODE AS PARENT_CODE,
	   TOP_L3_CEG_SHORT_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME,  
       '''',
       '''', 
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
  WHERE VIEW_FLAG = '||V_REV_VIEW_FLAG||'	--202407版本 IAS新增视角7
	AND GROUP_LEVEL=''ITEM''
 GROUP BY LV0_PROD_RND_TEAM_CODE,
          LV0_PROD_RD_TEAM_CN_NAME,        
          TOP_L3_CEG_CODE,
          TOP_L3_CEG_SHORT_CN_NAME,
          TOP_L4_CEG_CODE,
          TOP_L4_CEG_SHORT_CN_NAME,   
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
         
UNION ALL  

--CEG
SELECT 6,
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME, 
       TOP_L3_CEG_CODE AS GROUP_CODE,
       TOP_L3_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,
       ''CEG'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       LV0_PROD_RND_TEAM_CODE AS PARENT_CODE, 
	   LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       '''',
       '''',
       '''',
       '''',
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
  WHERE VIEW_FLAG = '||V_REV_VIEW_FLAG||'	--202407版本 IAS新增视角7
	AND GROUP_LEVEL=''ITEM''
 GROUP BY LV0_PROD_RND_TEAM_CODE,
          LV0_PROD_RD_TEAM_CN_NAME,        
          TOP_L3_CEG_CODE,
          TOP_L3_CEG_SHORT_CN_NAME, 
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
         
';

EXECUTE IMMEDIATE V_SQL;
DBMS_OUTPUT.PUT_LINE('视角6数据执行成功');



---------视角6，VIEW_FLAG=5，group_level = 'CEG,MODLE,LV1,LV2,LV3'
 
V_SQL:='
INSERT INTO '||V_BASE_AMT_TEMP||'
  (VIEW_FLAG,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   YEARS_AMT,
   PARENT_CODE,
   PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
   TOP_L3_CEG_CODE,
   TOP_L3_CEG_SHORT_CN_NAME,
   TOP_L4_CEG_CODE,
   TOP_L4_CEG_SHORT_CN_NAME,   
   CALIBER_FLAG,
   OVERSEA_FLAG, 
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME  
   )
   
--ITEM
SELECT 5,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME,
       GROUP_CODE AS GROUP_CODE,
       GROUP_CN_NAME AS GROUP_CN_NAME,
       ''ITEM'' AS GROUP_LEVEL,
       YEARS_AMT,
       '||V_REV_ITEM_PARENT||'	--202407版本 IAS新增LV4层级
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME,   
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
  WHERE VIEW_FLAG = '||V_REV_VIEW_FLAG||'	--202407版本 IAS新增视角7
	AND GROUP_LEVEL=''ITEM''
	
UNION ALL

'||V_LV4_REV_VIEW5_SQL||'	--202407版本 IAS新增LV4层级
   
--LV3
SELECT 5,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME,
       LV3_PROD_RND_TEAM_CODE AS GROUP_CODE,
       LV3_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
       ''LV3'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
	   LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME,   
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
  WHERE VIEW_FLAG = '||V_REV_VIEW_FLAG||'	--202407版本 IAS新增视角7
	AND GROUP_LEVEL=''ITEM''
 GROUP BY PROD_RND_TEAM_CODE,
          PROD_RND_TEAM_CN_NAME,        
          LV3_PROD_RND_TEAM_CODE,
          LV3_PROD_RD_TEAM_CN_NAME,
          LV2_PROD_RND_TEAM_CODE,
		  LV2_PROD_RD_TEAM_CN_NAME,
          TOP_L3_CEG_CODE,
          TOP_L3_CEG_SHORT_CN_NAME,
          TOP_L4_CEG_CODE,
          TOP_L4_CEG_SHORT_CN_NAME,   
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
         
UNION ALL

--LV2
SELECT 5,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME,
       LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,
       LV2_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
       ''LV2'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,
	   LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME,   
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
  WHERE VIEW_FLAG = '||V_REV_VIEW_FLAG||'	--202407版本 IAS新增视角7
	AND GROUP_LEVEL=''ITEM''
 GROUP BY LV2_PROD_RND_TEAM_CODE,
          LV2_PROD_RD_TEAM_CN_NAME,
          LV1_PROD_RND_TEAM_CODE,
		  LV1_PROD_RD_TEAM_CN_NAME,
          TOP_L3_CEG_CODE,
          TOP_L3_CEG_SHORT_CN_NAME,
          TOP_L4_CEG_CODE,
          TOP_L4_CEG_SHORT_CN_NAME,   
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
         
UNION ALL

--LV1
SELECT 5,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME,
       LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,
       LV1_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
       ''LV1'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       TOP_L4_CEG_CODE AS PARENT_CODE,
	   TOP_L4_CEG_SHORT_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME,   
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
  WHERE VIEW_FLAG = '||V_REV_VIEW_FLAG||'	--202407版本 IAS新增视角7
	AND GROUP_LEVEL=''ITEM''
 GROUP BY LV1_PROD_RND_TEAM_CODE,
          LV1_PROD_RD_TEAM_CN_NAME,
          TOP_L3_CEG_CODE,
          TOP_L3_CEG_SHORT_CN_NAME,
          TOP_L4_CEG_CODE,
          TOP_L4_CEG_SHORT_CN_NAME,   
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
         
UNION ALL  

--MODL
SELECT 5,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME,
       TOP_L4_CEG_CODE AS GROUP_CODE,
       TOP_L4_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,
       ''MODL'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       TOP_L3_CEG_CODE AS PARENT_CODE,
	   TOP_L3_CEG_SHORT_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME,  
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
  WHERE VIEW_FLAG = '||V_REV_VIEW_FLAG||'	--202407版本 IAS新增视角7
	AND GROUP_LEVEL=''ITEM''
 GROUP BY LV0_PROD_RND_TEAM_CODE,
          LV0_PROD_RD_TEAM_CN_NAME,        
          TOP_L3_CEG_CODE,
          TOP_L3_CEG_SHORT_CN_NAME,
          TOP_L4_CEG_CODE,
          TOP_L4_CEG_SHORT_CN_NAME,   
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
         
UNION ALL  

--CEG
SELECT 5,
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       TOP_L3_CEG_CODE AS GROUP_CODE,
       TOP_L3_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,
       ''CEG'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       LV0_PROD_RND_TEAM_CODE AS PARENT_CODE, 
	   LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       '''',
       '''',
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
  WHERE VIEW_FLAG = '||V_REV_VIEW_FLAG||'	--202407版本 IAS新增视角7
	AND GROUP_LEVEL=''ITEM''
 GROUP BY LV0_PROD_RND_TEAM_CODE,
          LV0_PROD_RD_TEAM_CN_NAME,        
          TOP_L3_CEG_CODE,
          TOP_L3_CEG_SHORT_CN_NAME, 
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
         
';

EXECUTE IMMEDIATE V_SQL;
DBMS_OUTPUT.PUT_LINE('视角5数据执行成功');


---------视角5，VIEW_FLAG=4，group_level = 'CEG,LV1,LV2,LV3'

V_SQL:='
INSERT INTO '||V_BASE_AMT_TEMP||'
  (VIEW_FLAG,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   YEARS_AMT,
   PARENT_CODE,
   PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
   TOP_L3_CEG_CODE,
   TOP_L3_CEG_SHORT_CN_NAME,  
   CALIBER_FLAG,
   OVERSEA_FLAG, 
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME  
   )
   
SELECT 4,
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       GROUP_CODE AS GROUP_CODE,
       GROUP_CN_NAME AS GROUP_CN_NAME,
       ''ITEM'' AS GROUP_LEVEL,
       YEARS_AMT,
       '||V_REV_ITEM_PARENT||'	--202407版本 IAS新增LV4层级
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME, 
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
  WHERE VIEW_FLAG = '||V_REV_VIEW_FLAG||'	--202407版本 IAS新增视角7
	AND GROUP_LEVEL=''ITEM''
	
UNION ALL

'||V_LV4_REV_VIEW4_SQL||'	--202407版本 IAS新增LV4层级

--LV3
SELECT 4,
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       LV3_PROD_RND_TEAM_CODE AS GROUP_CODE,
       LV3_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
       ''LV3'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
	   LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME, 
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
  WHERE VIEW_FLAG = '||V_REV_VIEW_FLAG||'	--202407版本 IAS新增视角7
	AND GROUP_LEVEL=''ITEM''
 GROUP BY PROD_RND_TEAM_CODE,
          PROD_RND_TEAM_CN_NAME,        
          LV3_PROD_RND_TEAM_CODE,
          LV3_PROD_RD_TEAM_CN_NAME,
          LV2_PROD_RND_TEAM_CODE,
		  LV2_PROD_RD_TEAM_CN_NAME,
          TOP_L3_CEG_CODE,
          TOP_L3_CEG_SHORT_CN_NAME, 
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
         
UNION ALL

--LV2
SELECT 4,
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,
       LV2_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
       ''LV2'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,
	   LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
  WHERE VIEW_FLAG = '||V_REV_VIEW_FLAG||'	--202407版本 IAS新增视角7
	AND GROUP_LEVEL=''ITEM''
 GROUP BY LV2_PROD_RND_TEAM_CODE,
          LV2_PROD_RD_TEAM_CN_NAME,
          LV1_PROD_RND_TEAM_CODE,
		  LV1_PROD_RD_TEAM_CN_NAME,
          TOP_L3_CEG_CODE,
          TOP_L3_CEG_SHORT_CN_NAME,  
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
         
UNION ALL

--LV1
SELECT 4,
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,
       LV1_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
       ''LV1'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       TOP_L3_CEG_CODE AS PARENT_CODE,
	   TOP_L3_CEG_SHORT_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME, 
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
  WHERE VIEW_FLAG = '||V_REV_VIEW_FLAG||'	--202407版本 IAS新增视角7
	AND GROUP_LEVEL=''ITEM''
 GROUP BY LV1_PROD_RND_TEAM_CODE,
          LV1_PROD_RD_TEAM_CN_NAME,
          TOP_L3_CEG_CODE,
          TOP_L3_CEG_SHORT_CN_NAME,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
         
UNION ALL  

--CEG
SELECT 4,
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       TOP_L3_CEG_CODE AS GROUP_CODE,
       TOP_L3_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,
       ''CEG'' AS GROUP_LEVEL,
       SUM(YEARS_AMT) AS YEARS_AMT,
       LV0_PROD_RND_TEAM_CODE AS PARENT_CODE, 
	   LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_BASE_AMT_TEMP||'
  WHERE VIEW_FLAG = '||V_REV_VIEW_FLAG||'	--202407版本 IAS新增视角7
	AND GROUP_LEVEL=''ITEM''
 GROUP BY LV0_PROD_RND_TEAM_CODE,
          LV0_PROD_RD_TEAM_CN_NAME,        
          TOP_L3_CEG_CODE,
          TOP_L3_CEG_SHORT_CN_NAME, 
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
         
';

EXECUTE IMMEDIATE V_SQL;
DBMS_OUTPUT.PUT_LINE('视角4数据执行成功');
DBMS_OUTPUT.PUT_LINE('3:-----------'||V_SQL);

 
--写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '其他层级年度发货额收敛完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  

V_EXCEPTION_FLAG := '12';   
V_SQL:='
TRUNCATE TABLE '||V_MID_TABLE||';
INSERT INTO '||V_MID_TABLE||'
  (VERSION_ID,
   PERIOD_YEAR,
   PERIOD_YEAR_TYPE,
   VIEW_FLAG,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   WEIGHT_RATE,
   PARENT_CODE,
   PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG ,  
   LV0_PROD_LIST_CODE  , 
   LV0_PROD_LIST_CN_NAME)
  SELECT '||V_VERSION||' AS VERSION_ID,
         '''||V_PERIOD_YEAR||''' AS PERIOD_YEAR,
         ''S'' AS PERIOD_YEAR_TYPE,
         VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
		 CASE WHEN GROUP_LEVEL IN (''LV1'',''LV2'',''LV3'',''ITEM'',''LV4'') THEN 
																		--202407版本 IAS新增LV4层级
          YEARS_AMT / NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY VIEW_FLAG, CALIBER_FLAG,LV0_PROD_LIST_CODE,OVERSEA_FLAG, PROD_RND_TEAM_CODE ,GROUP_LEVEL,
                  NVL(PARENT_CODE,99)), 0) 
		 ELSE  YEARS_AMT / NULLIF(SUM(YEARS_AMT) OVER(PARTITION BY VIEW_FLAG, CALIBER_FLAG,LV0_PROD_LIST_CODE,OVERSEA_FLAG,GROUP_LEVEL,
                  NVL(PARENT_CODE,99)), 0) 
		 END AS RATE,
         PARENT_CODE,
		 PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG ,  
         LV0_PROD_LIST_CODE  , 
         LV0_PROD_LIST_CN_NAME
    FROM '||V_BASE_AMT_TEMP||'
	WHERE VIEW_FLAG IN (4,5,6);
';

DBMS_OUTPUT.PUT_LINE('MID:-----------'||V_SQL);
EXECUTE IMMEDIATE V_SQL;

--写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '本'||V_VERSION||'版本权重收敛完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
V_EXCEPTION_FLAG := '13';     
V_SQL := 'DELETE FROM '||V_TO_REV_TABLE||'  WHERE VIEW_FLAG IN (4,5,6) AND VERSION_ID = '||V_VERSION||';';
EXECUTE IMMEDIATE V_SQL;	--202405版本 新增数字能源部分

--写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '权重表同版本反向视角数据删除完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

V_EXCEPTION_FLAG := '14';  
V_SQL:='   
INSERT INTO '||V_TO_REV_TABLE||'
  (VERSION_ID,
   PERIOD_YEAR,
   PERIOD_YEAR_TYPE,
   VIEW_FLAG,
   PUR_CODE,
   PUR_CN_NAME,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   WEIGHT_RATE,
   PARENT_CODE,
   PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG ,  
   LV0_PROD_LIST_CODE  , 
   LV0_PROD_LIST_CN_NAME )
  SELECT '||V_VERSION||' AS VERSION_ID,
         '''||V_PERIOD_YEAR||''' AS PERIOD_YEAR,
         ''S'' AS PERIOD_YEAR_TYPE,
         VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         WEIGHT_RATE,
         PARENT_CODE,
		 PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG ,  
         LV0_PROD_LIST_CODE  , 
         LV0_PROD_LIST_CN_NAME 
    FROM '||V_MID_TABLE||';';
    

EXECUTE IMMEDIATE V_SQL;

  END IF;
  
--写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '月度分析权重表插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_EXCEPTION_FLAG,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; 
$$
/

