-- Name: f_dm_foc_annual_weight_d_tmp; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_annual_weight_d_tmp(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2023-06-22
  创建人  ：唐钦
  背景描述：分视角权重表(年度分析-一览表)
  参数描述：x_success_flag ：是否成功
  事例    ：select opt_fcst.F_DM_FOC_ANNUAL_WEIGHT_T()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_ANNUAL_WEIGHT_D_TMP'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行'); 
   
    --版本号赋值
SELECT MAX(VERSION_ID) INTO V_VERSION_ID
 FROM  FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
 WHERE VERSION = '2023-11' AND DEL_FLAG = 'N' AND DATA_TYPE = 'CATEGORY' AND STATUS = 1;
 
-- 删除量纲颗粒度-年度权重表数据
DELETE FROM FIN_DM_OPT_FOI.DM_FOC_DMS_ANNUAL_WEIGHT_T WHERE VERSION_ID = V_VERSION_ID;

  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除:DM_FOC_DMS_ANNUAL_WEIGHT_T表版本号为：'||V_VERSION_ID||'的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
-- 插入数据到量纲颗粒度-年度权重表
INSERT INTO FIN_DM_OPT_FOI.DM_FOC_DMS_ANNUAL_WEIGHT_T(
   id,
   version_id,
   period_year,
   period_year_type,
   dimension_code,
   dimension_cn_name,
   dimension_subcategory_code,
   dimension_subcategory_cn_name,
   dimension_sub_detail_code,
   dimension_sub_detail_cn_name,
   prod_rnd_team_code, 
   prod_rnd_team_cn_name,
   dms_code,
   dms_cn_name,
   group_code,
   group_cn_name,
   group_level,
   weight_rate,
   absolute_weight,
   absolute_parent_amt,
   parent_code,
   lv0_prod_list_code,
   lv0_prod_list_cn_name,
   view_flag,
   append_flag,
   caliber_flag,
   oversea_flag,
   custom_flag,
   created_by,
   creation_date,
   last_updated_by,
   last_update_date,
   del_flag)
 SELECT ID,
        version_id,
        period_year,
        period_year_type,
        dimension_code,
        dimension_cn_name,
        dimension_subcategory_code,
        dimension_subcategory_cn_name,
        dimension_sub_detail_code,
        dimension_sub_detail_cn_name,
        prod_rnd_team_code, 
        prod_rnd_team_cn_name,
        dms_code,
        dms_cn_name,
        group_code,
        group_cn_name,
        group_level,
        weight_rate,
        absolute_weight,
        absolute_parent_amt,
        parent_code,
        lv0_prod_list_code,
        lv0_prod_list_cn_name,
        view_flag,
        append_flag,
        caliber_flag,
        oversea_flag,
        custom_flag,
        created_by,
        creation_date,
        last_updated_by,
        last_update_date,
        del_flag
 FROM FIN_DM_OPT_FOI.DM_FOC_DMS_ANNUAL_WEIGHT_T_1122_RUN
 WHERE VERSION_ID = V_VERSION_ID;
 
   V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入数据到DM_FOC_DMS_ANNUAL_WEIGHT_T表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

    --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FOC_DMS_ANNUAL_WEIGHT_T';
 
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END$$
/

