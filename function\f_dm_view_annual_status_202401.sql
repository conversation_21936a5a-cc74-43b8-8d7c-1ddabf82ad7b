-- Name: f_dm_view_annual_status_202401; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_view_annual_status_202401(f_cost_type character varying, f_dimension_type character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2023-10-23
  创建人  ：唐钦
  背景描述：年度涨跌幅状态码表
  参数描述：x_success_flag ：是否成功
  事例    ：select FIN_DM_OPT_FOI.f_dm_view_annual_status()
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_VIEW_ANNUAL_STATUS_202401'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_YEAR BIGINT := YEAR(CURRENT_TIMESTAMP);   -- 当前年份
    
-- 7月版本需求新增
  V_SQL        TEXT;   --SQL逻辑
  V_PROD_RND_TEAM TEXT;  -- 重量级团队通用层级
  V_SQL_PROD_RND_TEAM_TOTAL TEXT;
  V_LV3_PROD_RD_TEAM TEXT;  -- 通用-LV3层级（7月新增）
  V_PROFITS_NAME TEXT;                      -- 盈利颗粒度层级（7月新增）
  V_FOI_LEVEL TEXT;    -- 采购价格指数层级
  V_IN_LEVEL TEXT; 
  V_PROFITS_L1_L2   VARCHAR(200);
  V_GROUP TEXT; 
  V_TAB_PROD_RND_TEAM TEXT;
  V_TAB_LV3_PROD_RD_TEAM TEXT;
  V_TAB_PROFITS_NAME TEXT;
  V_TAB_FOI_LEVEL TEXT;
  V_FROM_TABLE_1 VARCHAR(200);
  V_FROM_TABLE_2 VARCHAR(200);
  V_TO_TABLE VARCHAR(200);
  V_TMP_TABLE VARCHAR(200);
  V_TMP2_TABLE VARCHAR(200);
  V_LAST_YEAR_FLAG VARCHAR(200);
  V_YEAR_FLAG VARCHAR(200);
  V_YEAR_APPEND VARCHAR(200);
  V_SEQUENCE VARCHAR(200);
  V_IN_PROD_RND_TEAM_CODE  TEXT; 
  V_IN_PROD_RND_TEAM_CN_NAME  TEXT; 
  V_IN_PROFITS_NAME  TEXT;
  V_REL_PROD_RND_TEAM_CODE TEXT;
  V_REL_PROD_RND_TEAM_CN_NAME TEXT;
  V_SQL_CEG_PARENT TEXT;
  V_INDEX_FLAG  TEXT;
  V_IN_INDEX_FLAG  TEXT;
  V_GROUP_CODE VARCHAR(200);
  V_SQL_PROD_RND_TEAM  VARCHAR(200);    
  V_SQL_PROFITS_NAME  VARCHAR(200);    
  V_GROUP_LEVEL  VARCHAR(200);    
  V_PARTITION_DIM  TEXT;  -- 分组中用到的维度字段
  V_SQL_PARENT TEXT;
  V_VIEW_NUM BIGINT;
  V_REL_PROD_RND_TEAM VARCHAR(500);  -- 重量级团队的关联条件
  V_REL_PROFITS_NAME VARCHAR(500);   -- 盈利层级的关联条件              
  V_REL_INDEX_FLAG TEXT;             -- 通用标识等字段的关联条件
  
  -- 202309版本新增：
  V_DMS_LEV_CODE VARCHAR(500);  -- 量纲3个层级
  V_DMS_LEV_NAME VARCHAR(500);  -- 量纲3个层级
  V_TAB_DMS_CODE VARCHAR(500);
  V_TAB_DMS_NAME VARCHAR(500);
  V_DMS_CODE VARCHAR(500);      -- 量纲颗粒度CODE
  V_DMS_NAME VARCHAR(500);      -- 量纲颗粒度名称
  V_SQL_DMS_PARENT TEXT;
  V_REL_DMS TEXT;  -- 量纲层级的关联条件
  V_SQL_DMS_LEV_CODE TEXT;
  V_SQL_DMS_LEV_NAME TEXT;
  V_SQL_DMS_CODE TEXT;
  V_SQL_DMS_NAME TEXT;
  V_SQL_DMS_CODE_TOTAL TEXT;
  V_SQL_DMS_NAME_TOTAL TEXT;
  V_GROUP_DMS_LEV_CODE TEXT;   -- 卷积时的量纲层级字段
  
  -- 202311版本新增
      V_BEGIN_NUM INT;   -- 不同成本类型，循环开始的值不一致
    V_BASE_LEVEL TEXT;    -- 基础层级（采购成本/制造成本）
    V_BASE_LEVEL_TABLE TEXT;
    V_SQL_CEG_PARENT_NAME VARCHAR(500);
    V_SQL_DMS_PARENT_NAME VARCHAR(500);
    V_IN_BASE_LEVEL VARCHAR(500);
    V_PARENT_NAME VARCHAR(200);  --采购价格指数不需要此字段，产业成本指数添加
    V_IN_PARENT_NAME VARCHAR(200);
    V_REL_BASE_LEVEL TEXT;
    
  -- 202301新增
  V_SPART_CODE VARCHAR(200);
  V_SPART_NAME VARCHAR(200);
  V_IN_SPART VARCHAR(200);
  V_REL_SPART VARCHAR(500);
  V_GROUP_SPART VARCHAR(500);
  V_IN_SPART_CODE VARCHAR(200);
    
BEGIN
  X_RESULT_STATUS = '1';
    
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
     
 -- 判断入参的成本类型是采购成本，还是制造成本的类型(11月版本需求新增)
  IF F_COST_TYPE = 'P' THEN  -- 采购成本类型
      V_BEGIN_NUM := 1;
      V_SQL_PARENT := 'T1.CATEGORY_CODE AS PARENT_CODE,
                       T1.CATEGORY_CN_NAME AS PARENT_CN_NAME,';
      V_BASE_LEVEL_TABLE := '
                  L3_CEG_CODE    VARCHAR(50),
                  L3_CEG_CN_NAME    VARCHAR(500),
                  L3_CEG_SHORT_CN_NAME    VARCHAR(500),
                  L4_CEG_CODE    VARCHAR(50),
                  L4_CEG_CN_NAME    VARCHAR(500),
                  L4_CEG_SHORT_CN_NAME    VARCHAR(500),
                  CATEGORY_CODE VARCHAR(50),
                  CATEGORY_CN_NAME VARCHAR(500),'; 
      V_BASE_LEVEL := '
                   L3_CEG_CODE,
                   L3_CEG_CN_NAME,
                   L3_CEG_SHORT_CN_NAME,
                   L4_CEG_CODE,
                   L4_CEG_CN_NAME,
                   L4_CEG_SHORT_CN_NAME,
                   CATEGORY_CODE,
                   CATEGORY_CN_NAME,';
     -- 判断入参是要走通用颗粒度的逻辑还是盈利颗粒度的逻辑，处理来源表和目标表的变量
     IF F_DIMENSION_TYPE = 'U' THEN -- 通用颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_VIEW_ANNL_COST_T';--来源表
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_ANNUAL_AMP_T';
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ANNUAL_STATUS_CODE_T';--目标表
            V_TMP_TABLE := 'ITEM_LACK_STATUS_TMP'; -- 临时表
            V_TMP2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DIM_ITEM_STATUS_T';
            V_SEQUENCE := 'FIN_DM_OPT_FOI.DM_FOC_ANNUAL_STATUS_CODE_S.NEXTVAL AS ID,';
        ELSIF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_PFT_VIEW_ANNL_COST_T';--来源表
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_PFT_ANNUAL_AMP_T';
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_ANNUAL_STATUS_CODE_T'; --目标表 
            V_TMP_TABLE := 'PFT_ITEM_LACK_STATUS_TMP'; -- 临时表
            V_TMP2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_DIM_ITEM_STATUS_T';
            V_SEQUENCE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_ANNUAL_STATUS_CODE_S.NEXTVAL AS ID,';
        ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_DMS_VIEW_ANNL_COST_T_202401';--来源表
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_DMS_ANNUAL_AMP_T_202401';
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_ANNUAL_STATUS_CODE_T_202401'; --目标表 
            V_TMP_TABLE := 'DMS_ITEM_LACK_STATUS_TMP'; -- 临时表
            V_TMP2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_DIM_ITEM_STATUS_T_202401';
            V_SEQUENCE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_ANNUAL_STATUS_CODE_S.NEXTVAL AS ID,';
            
    ELSE
    NULL;
  END IF;
  
 -- 判断入参的成本类型是采购成本，还是制造成本的类型(11月版本需求新增)
  ELSIF F_COST_TYPE = 'M' THEN  -- 制造成本类型
      V_BEGIN_NUM := 2;
      V_SQL_PARENT := 'T1.MANUFACTURE_OBJECT_CODE AS PARENT_CODE,
                       T1.MANUFACTURE_OBJECT_CN_NAME AS PARENT_CN_NAME,';
      V_BASE_LEVEL_TABLE := '
                 SHIPPING_OBJECT_CODE VARCHAR(200),
                 SHIPPING_OBJECT_CN_NAME VARCHAR(200),
                 MANUFACTURE_OBJECT_CODE VARCHAR(200),
                 MANUFACTURE_OBJECT_CN_NAME VARCHAR(200),';
      V_BASE_LEVEL := '
                 SHIPPING_OBJECT_CODE,
                 SHIPPING_OBJECT_CN_NAME,
                 MANUFACTURE_OBJECT_CODE,
                 MANUFACTURE_OBJECT_CN_NAME,';
      V_IN_BASE_LEVEL := '
                 T1.SHIPPING_OBJECT_CODE,
                 T1.SHIPPING_OBJECT_CN_NAME,
                 T1.MANUFACTURE_OBJECT_CODE,
                 T1.MANUFACTURE_OBJECT_CN_NAME,';
      V_REL_BASE_LEVEL := ' AND NVL(T1.SHIPPING_OBJECT_CODE,''SNULL'') = NVL(T2.SHIPPING_OBJECT_CODE,''SNULL'')
                             AND NVL(T1.MANUFACTURE_OBJECT_CODE,''SNULL'') = NVL(T2.MANUFACTURE_OBJECT_CODE,''SNULL'')';
     -- 判断入参是要走通用颗粒度的逻辑还是盈利颗粒度的逻辑，处理来源表和目标表的变量
     IF F_DIMENSION_TYPE = 'U' THEN -- 通用颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_MADE_VIEW_ANNL_COST_T';--来源表
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_MADE_ANNUAL_AMP_T';
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_ANNUAL_STATUS_CODE_T';--目标表
            V_TMP_TABLE := 'ITEM_LACK_STATUS_TMP'; -- 临时表
            V_TMP2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DIM_ITEM_STATUS_T';
            V_SEQUENCE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_ANNUAL_STATUS_CODE_S.NEXTVAL AS ID,';
        ELSIF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_VIEW_ANNL_COST_T';--来源表
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_ANNUAL_AMP_T';
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_ANNUAL_STATUS_CODE_T'; --目标表 
            V_TMP_TABLE := 'PFT_ITEM_LACK_STATUS_TMP'; -- 临时表
            V_TMP2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_DIM_ITEM_STATUS_T';
            V_SEQUENCE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_ANNUAL_STATUS_CODE_S.NEXTVAL AS ID,';
        ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_VIEW_ANNL_COST_T';--来源表
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_ANNUAL_AMP_T';
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_ANNUAL_STATUS_CODE_T'; --目标表 
            V_TMP_TABLE := 'DMS_ITEM_LACK_STATUS_TMP'; -- 临时表
            V_TMP2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_DIM_ITEM_STATUS_T';
            V_SEQUENCE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_ANNUAL_STATUS_CODE_S.NEXTVAL AS ID,';
    ELSE
    NULL;
    END IF;
  END IF;
   
    -- 清空中间表数据
    EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_TMP2_TABLE;
    --版本号赋值
    V_SQL := 'SELECT VERSION_ID FROM '||V_FROM_TABLE_1||' T LIMIT 1 ';
    EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
   
  --1.删除年度分析状态码表数据:
    EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;
    
     --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空版本号为：'||V_VERSION_ID||'的'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
    --重置变量入参
    -- 公用变量
    V_SQL_PROD_RND_TEAM_TOTAL := ' LV0_PROD_RND_TEAM_CODE    ,
                               LV0_PROD_RD_TEAM_CN_NAME    ,
                               LV1_PROD_RND_TEAM_CODE    ,
                               LV1_PROD_RD_TEAM_CN_NAME    ,
                               LV2_PROD_RND_TEAM_CODE    ,
                               LV2_PROD_RD_TEAM_CN_NAME ,
                               VIEW_FLAG,
                               CALIBER_FLAG,
                               OVERSEA_FLAG,          -- 202309新增：国内海外标识(I:国内/O:海外/G:全球)
                               LV0_PROD_LIST_CODE,    -- 202309新增：BG编码
                               LV0_PROD_LIST_CN_NAME, -- 202309新增：BG中文名称
                               ';        
    V_TAB_PROD_RND_TEAM = 'LV0_PROD_RND_TEAM_CODE VARCHAR(50),
                           LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
                           LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
                           LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
                           LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
                           LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
                           VIEW_FLAG VARCHAR(2),
                           CALIBER_FLAG VARCHAR(2),
                           OVERSEA_FLAG VARCHAR(2),          -- 202309新增：国内海外标识(I:国内/O:海外/G:全球)
                           LV0_PROD_LIST_CODE VARCHAR(50),    -- 202309新增：BG编码
                           LV0_PROD_LIST_CN_NAME VARCHAR(200), -- 202309新增：BG中文名称
                           ';        
    -- 通用/量纲公用变量
    V_TAB_LV3_PROD_RD_TEAM := ' LV3_PROD_RND_TEAM_CODE VARCHAR(50),
                                LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200),';        
    V_LV3_PROD_RD_TEAM := ' LV3_PROD_RND_TEAM_CODE ,
                            LV3_PROD_RD_TEAM_CN_NAME ,    ';
    -- 量纲颗粒度变量
    V_DMS_LEV_CODE := '
                   DIMENSION_CODE,                -- 量纲层级编码
                   DIMENSION_SUBCATEGORY_CODE,    -- 量纲子类编码
                   DIMENSION_SUB_DETAIL_CODE,     -- 量纲子类明细编码
                   ';
    V_DMS_LEV_NAME:= '
                  DIMENSION_CN_NAME,
                  DIMENSION_SUBCATEGORY_CN_NAME,
                  DIMENSION_SUB_DETAIL_CN_NAME,';
    V_TAB_DMS_CODE := '
                       DIMENSION_CODE VARCHAR(500),
                       DIMENSION_SUBCATEGORY_CODE VARCHAR(500),
                       DIMENSION_SUB_DETAIL_CODE VARCHAR(500),
                       SPART_CODE VARCHAR(200),
                       ';
    V_TAB_DMS_NAME:= '
                      DIMENSION_CN_NAME VARCHAR(2000),
                      DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(2000),
                      DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(2000),
                      SPART_CN_NAME VARCHAR(200),
                      ';  
    -- 盈利颗粒度变量
    V_PROFITS_L1_L2 := ' L1_NAME ,
                         L2_NAME ,';
    V_TAB_PROFITS_NAME := 'L1_NAME VARCHAR(200),
                           L2_NAME VARCHAR(200),';  
    -- 采购价格指数变量  
    V_TAB_FOI_LEVEL := 'L2_CEG_CODE VARCHAR(50),
                        L2_CEG_CN_NAME VARCHAR(200),
                        SUPPLIER_CODE VARCHAR(50),
                        SUPPLIER_CN_NAME VARCHAR(200),
                        CONTINUITY_TYPE VARCHAR(50),
                        LEVEL_TYPE VARCHAR(50),
                        GROUP_LEVEL VARCHAR(50), ';                                                
    V_FOI_LEVEL := 'L2_CEG_CODE ,
                    L2_CEG_CN_NAME ,
                    SUPPLIER_CODE,
                    SUPPLIER_CN_NAME,
                    CONTINUITY_TYPE,
                    LEVEL_TYPE,
                    GROUP_LEVEL, ';   
                                 
    --通用颗粒度的维度时，不需要L1和L2字段以及采购字段
    IF F_DIMENSION_TYPE = 'U' THEN 
          V_FOI_LEVEL := '';
          V_TAB_FOI_LEVEL := '';
          V_PROFITS_L1_L2 := '';
          V_TAB_PROFITS_NAME := '';
          V_VIEW_NUM := 0;
          V_TAB_DMS_CODE := '';
          V_TAB_DMS_NAME := '';
          V_DMS_LEV_CODE := '';
          V_DMS_LEV_NAME := '';
          
    --量纲颗粒度的维度时，不需要L1和L2字段以及采购字段
    ELSIF F_DIMENSION_TYPE = 'D' THEN 
            V_FOI_LEVEL := '';
            V_TAB_FOI_LEVEL := '';
            V_PROFITS_L1_L2 := '';
            V_TAB_PROFITS_NAME := '';
            V_VIEW_NUM := 0;
            V_DMS_CODE := ' DMS_CODE,';
            V_DMS_NAME := ' DMS_CN_NAME,';
            V_SPART_CODE := 'SPART_CODE,';
            V_SPART_NAME := 'SPART_CN_NAME,';
            V_IN_SPART := 'T1.SPART_CODE,T1.SPART_CN_NAME,';
            V_REL_SPART := ' AND NVL(T1.SPART_CODE,''S1'') = NVL(T2.SPART_CODE,''S1'') ';
            V_GROUP_SPART := V_IN_SPART;
            V_IN_SPART_CODE := 'T1.SPART_CODE,';
             
    --盈利颗粒度的维度时，不需要LV3字段以及采购字段
    ELSIF F_DIMENSION_TYPE = 'P' THEN
            V_FOI_LEVEL := '';
            V_TAB_FOI_LEVEL := '';
            V_LV3_PROD_RD_TEAM := '';
            V_TAB_LV3_PROD_RD_TEAM := '';
            V_VIEW_NUM := 0;
            V_TAB_DMS_CODE := '';
            V_TAB_DMS_NAME := '';
            V_DMS_LEV_CODE := '';
            V_DMS_LEV_NAME := '';
    ELSE
      NULL;
    END IF;
         
 -- 创建ITEM层级缺失情况临时表
    V_SQL := '
    DROP TABLE IF EXISTS ' || V_TMP_TABLE ||';
    CREATE TEMPORARY TABLE ' || V_TMP_TABLE ||' (
        PERIOD_YEAR BIGINT,
        '||V_TAB_PROD_RND_TEAM
        ||V_TAB_LV3_PROD_RD_TEAM
        ||V_TAB_DMS_CODE
        ||V_TAB_DMS_NAME
        ||V_TAB_PROFITS_NAME
        ||V_TAB_FOI_LEVEL
        ||V_BASE_LEVEL_TABLE||'
        ITEM_CODE CHARACTER VARYING(50),
        ITEM_CN_NAME CHARACTER VARYING(2000),
        LAST_THREE_YEAR_FLAG CHARACTER VARYING(50),
        LAST_THREE_APPEND_YEAR CHARACTER VARYING(50),
        LAST_TWO_YEAR_FLAG CHARACTER VARYING(50),
        LAST_TWO_APPEND_YEAR CHARACTER VARYING(50),
        LAST_YEAR_FLAG CHARACTER VARYING(50),
        LAST_APPEND_YEAR CHARACTER VARYING(50),
        CURRENT_YEAR_FLAG CHARACTER VARYING(50),
        CURRENT_APPEND_YEAR CHARACTER VARYING(50)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(ITEM_CODE)';
    EXECUTE IMMEDIATE V_SQL;
    
    --2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_MUM,
     F_CAL_LOG_DESC => 'ITEM层级缺失情况临时表创建完成',
     F_RESULT_STATUS => X_RESULT_STATUS, 
     F_ERRBUF => 'SUCCESS');   
    
    -- ITEM层级缺失数据情况插入临时表
    V_SQL := '
         INSERT INTO '||V_TMP_TABLE||'(            
                     '||V_SQL_PROD_RND_TEAM_TOTAL
                     ||V_LV3_PROD_RD_TEAM
                     ||V_DMS_LEV_CODE
                     ||V_DMS_LEV_NAME
                     ||V_SPART_CODE
                     ||V_SPART_NAME
                     ||V_PROFITS_L1_L2
                     ||V_BASE_LEVEL||'
                     ITEM_CODE,
                     ITEM_CN_NAME,
                     LAST_THREE_YEAR_FLAG,
                     LAST_THREE_APPEND_YEAR,
                     LAST_TWO_YEAR_FLAG,
                     LAST_TWO_APPEND_YEAR,
                     LAST_YEAR_FLAG,
                     LAST_APPEND_YEAR,
                     CURRENT_YEAR_FLAG,
                     CURRENT_APPEND_YEAR
                     )
          SELECT '||V_SQL_PROD_RND_TEAM_TOTAL
                  ||V_LV3_PROD_RD_TEAM
                  ||V_DMS_LEV_CODE
                  ||V_DMS_LEV_NAME
                  ||V_SPART_CODE
                  ||V_SPART_NAME
                  ||V_PROFITS_L1_L2
                  ||V_BASE_LEVEL||'
                  ITEM_CODE,
                  ITEM_CN_NAME,
                  SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-3 THEN DECODE(APPEND_FLAG,''Y'',1,0) ELSE 0 END) AS LAST_THREE_YEAR_FLAG,  -- 为当年-3年时，赋予该年数据为补齐时赋1，否则赋0
                  SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-3 THEN APPEND_YEAR ELSE 0 END) AS LAST_THREE_APPEND_YEAR,                                  -- 为当年-3年时，取到对应向前补齐数据的补齐年份，若为原始数据或是向后补齐的，则为NULL
                  SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-2 THEN DECODE(APPEND_FLAG,''Y'',1,0) ELSE 0 END) AS LAST_TWO_YEAR_FLAG,       -- 为当年-2年时，赋予该年数据为补齐时赋1，否则赋0
                  SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-2 THEN APPEND_YEAR ELSE 0 END) AS LAST_TWO_APPEND_YEAR,                                      -- 为当年-2年时，取到对应向前补齐数据的补齐年份，若为原始数据或是向后补齐的，则为NULL
                  SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-1 THEN DECODE(APPEND_FLAG,''Y'',1,0) ELSE 0 END) AS LAST_YEAR_FLAG,  
                  SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-1 THEN APPEND_YEAR ELSE 0 END) AS LAST_APPEND_YEAR, 
                  SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||' THEN DECODE(APPEND_FLAG,''Y'',1,0) ELSE 0 END) AS CURRENT_YEAR_FLAG,  
                  SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||' THEN APPEND_YEAR ELSE 0 END) AS CURRENT_APPEND_YEAR
              FROM '||V_FROM_TABLE_1||'
              GROUP BY '||V_SQL_PROD_RND_TEAM_TOTAL
                        ||V_LV3_PROD_RD_TEAM
                        ||V_DMS_LEV_CODE
                        ||V_DMS_LEV_NAME
                        ||V_SPART_CODE
                        ||V_SPART_NAME
                        ||V_PROFITS_L1_L2
                        ||V_BASE_LEVEL||'
                        ITEM_CODE,
                        ITEM_CN_NAME';
                 
     EXECUTE IMMEDIATE V_SQL; 
     DBMS_OUTPUT.PUT_LINE(V_SQL);
         
     --写入日志
   V_STEP_MUM := V_STEP_MUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_MUM,
    F_CAL_LOG_DESC => 'ITEM层级缺失数据情况插入临时表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');          
    
     -- ITEM层级状态码逻辑
     -- 对ITEM层级的年份进行循环 
        FOR YEAR_FLAG IN V_YEAR-2 .. V_YEAR LOOP                      
        
        IF YEAR_FLAG = V_YEAR-2 THEN
            V_LAST_YEAR_FLAG := 'LAST_THREE_YEAR_FLAG';
            V_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
            V_YEAR_APPEND := 'LAST_THREE_APPEND_YEAR';
        
        ELSIF YEAR_FLAG = V_YEAR-1 THEN
            V_LAST_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
            V_YEAR_FLAG := 'LAST_YEAR_FLAG';
            V_YEAR_APPEND := 'LAST_TWO_APPEND_YEAR';
            
        ELSIF YEAR_FLAG = V_YEAR THEN
            V_LAST_YEAR_FLAG := 'LAST_YEAR_FLAG';
            V_YEAR_FLAG := 'CURRENT_YEAR_FLAG';
            V_YEAR_APPEND := 'LAST_APPEND_YEAR';    
        ELSE NULL;
        END IF;
        
        V_SQL := '
        INSERT INTO '||V_TMP2_TABLE||' (
                     PERIOD_YEAR,
                     '||V_SQL_PROD_RND_TEAM_TOTAL
                     ||V_LV3_PROD_RD_TEAM
                     ||V_DMS_LEV_CODE
                     ||V_DMS_LEV_NAME
                     ||V_SPART_CODE
                     ||V_SPART_NAME
                     ||V_PROFITS_L1_L2
                     ||V_FOI_LEVEL
                     ||V_BASE_LEVEL||'
                     ITEM_CODE,
                     ITEM_CN_NAME,
                     STATUS_CODE,
                     APPEND_YEAR)
        SELECT '||YEAR_FLAG||' AS PERIOD_YEAR,
               '||V_SQL_PROD_RND_TEAM_TOTAL
               ||V_LV3_PROD_RD_TEAM
               ||V_DMS_LEV_CODE
               ||V_DMS_LEV_NAME
               ||V_SPART_CODE
               ||V_SPART_NAME
               ||V_PROFITS_L1_L2
               ||V_FOI_LEVEL
               ||V_BASE_LEVEL||'
               ITEM_CODE,
               ITEM_CN_NAME,
               CASE WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' = 1 THEN 1
                    WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 1 THEN 2
                    WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' = 0 THEN 3
                    WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 0 AND '||V_YEAR_APPEND||' = 0 THEN 4
                    WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 0 AND '||V_YEAR_APPEND||' <> 0 THEN 5
               END AS STATUS_CODE,
               '||V_YEAR_APPEND||' AS APPEND_YEAR
            FROM '||V_TMP_TABLE||' T1';
               
       EXECUTE IMMEDIATE V_SQL;  
       DBMS_OUTPUT.PUT_LINE(V_SQL);
       
        --写入日志
   V_STEP_MUM := V_STEP_MUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_MUM,
    F_CAL_LOG_DESC => 'ITEM层级全维度缺失状态码插入'||V_TMP2_TABLE||'表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');         
    END LOOP;
        
     --重置变量入参
     -- 产业/产业采购：公用变量
        V_PARENT_NAME := 'PARENT_CN_NAME,';
        V_IN_PARENT_NAME := 'T1.PARENT_CN_NAME,';
        V_PROD_RND_TEAM := 'PROD_RND_TEAM_CODE,
                            PROD_RD_TEAM_CN_NAME,';                                    
        V_GROUP := 'GROUP_CODE,
                    GROUP_CN_NAME,
                    GROUP_LEVEL,';
        V_INDEX_FLAG := 'VIEW_FLAG,
                         CALIBER_FLAG,
                         OVERSEA_FLAG,
                         LV0_PROD_LIST_CODE,
                         LV0_PROD_LIST_CN_NAME,
                         ';          
        V_IN_INDEX_FLAG := 'T1.VIEW_FLAG,
                            T1.CALIBER_FLAG,
                            T1.OVERSEA_FLAG,
                            T1.LV0_PROD_LIST_CODE,
                            T1.LV0_PROD_LIST_CN_NAME,
                            ';
        V_IN_LEVEL := ' ITEM_CODE AS GROUP_CODE,
                        ITEM_CN_NAME AS GROUP_CN_NAME,
                        ''ITEM'' AS GROUP_LEVEL, ';        
        V_SQL_PROD_RND_TEAM := ' T1.PROD_RND_TEAM_CODE,
                                 T1.PROD_RD_TEAM_CN_NAME,';     
        V_REL_PROD_RND_TEAM := ' AND NVL(T1.PROD_RND_TEAM_CODE,''SNULL'') = NVL(T2.PROD_RND_TEAM_CODE,''SNULL'') ';
        V_REL_INDEX_FLAG := ' AND NVL(T1.VIEW_FLAG,''SNULL'') = NVL(T2.VIEW_FLAG,''SNULL'')
                              AND NVL(T1.CALIBER_FLAG,''SNULL'') = NVL(T2.CALIBER_FLAG,''SNULL'') 
                              AND NVL(T1.OVERSEA_FLAG,''SNULL'') = NVL(T2.OVERSEA_FLAG,''SNULL'') 
                              AND NVL(T1.LV0_PROD_LIST_CODE,''SNULL'') = NVL(T2.LV0_PROD_LIST_CODE,''SNULL'') 
                              ';    
     -- 通用/量纲公用变量
        V_SQL_PROD_RND_TEAM_TOTAL := ' T1.LV0_PROD_RND_TEAM_CODE,
                                       T1.LV1_PROD_RND_TEAM_CODE,
                                       T1.LV2_PROD_RND_TEAM_CODE,
                                       T1.LV3_PROD_RND_TEAM_CODE,
                                       T1.LV0_PROD_RD_TEAM_CN_NAME,
                                       T1.LV1_PROD_RD_TEAM_CN_NAME,
                                       T1.LV2_PROD_RD_TEAM_CN_NAME,
                                       T1.LV3_PROD_RD_TEAM_CN_NAME,';                                     
     -- 盈利颗粒度变量
        V_PROFITS_NAME := 'PROFITS_NAME,';
        V_IN_PROFITS_NAME := 'CASE T1.VIEW_FLAG WHEN 3 THEN T1.L1_NAME 
                                                WHEN 4 THEN T1.L2_NAME 
                              ELSE ''''
                              END AS PROFITS_NAME,';
        V_SQL_PROFITS_NAME := 'T1.PROFITS_NAME,
                               T1.L1_NAME,
                               T1.L2_NAME,';
        V_REL_PROFITS_NAME := ' AND NVL(T1.L1_NAME,''SNULL'') = NVL(T2.L1_NAME,''SNULL'') 
                                AND NVL(T1.L2_NAME,''SNULL'') = NVL(T2.L2_NAME,''SNULL'') ';
     -- 量纲颗粒度变量
        V_REL_DMS := '
               AND NVL(T1.DIMENSION_CODE,''SNULL'') = NVL(T2.DIMENSION_CODE,''SNULL'')
               AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''SNULL'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''SNULL'') 
               AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''SNULL'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''SNULL'') ';   --量纲层级关联条件
        V_SQL_DMS_LEV_CODE := '
                           T1.DIMENSION_CODE,
                           T1.DIMENSION_SUBCATEGORY_CODE,
                           T1.DIMENSION_SUB_DETAIL_CODE,';  
        V_SQL_DMS_LEV_NAME := '
                           T1.DIMENSION_CN_NAME,
                           T1.DIMENSION_SUBCATEGORY_CN_NAME,
                           T1.DIMENSION_SUB_DETAIL_CN_NAME,';   
        V_GROUP_DMS_LEV_CODE := V_SQL_DMS_LEV_CODE||V_SQL_DMS_LEV_NAME;   
        
  --通用颗粒度的维度时，不需要L1和L2字段及量纲层级字段
    IF F_DIMENSION_TYPE = 'U' THEN 
             V_SQL_DMS_LEV_CODE := '';
             V_GROUP_DMS_LEV_CODE := '';
             V_SQL_DMS_LEV_NAME := '';
             V_IN_PROFITS_NAME := '';
             V_PROFITS_NAME := '';
             V_SQL_PROFITS_NAME := '';
             V_REL_PROFITS_NAME := '';
             V_REL_DMS := '';
             V_VIEW_NUM := 7;
             V_IN_PROD_RND_TEAM_CODE := '                  
                         CASE T1.VIEW_FLAG WHEN 0 THEN T1.LV0_PROD_RND_TEAM_CODE
                                           WHEN 1 THEN T1.LV1_PROD_RND_TEAM_CODE
                                           WHEN 2 THEN T1.LV2_PROD_RND_TEAM_CODE
                         ELSE T1.LV3_PROD_RND_TEAM_CODE
                         END AS PROD_RND_TEAM_CODE,';    
             V_IN_PROD_RND_TEAM_CN_NAME:='
                         CASE T1.VIEW_FLAG WHEN 0 THEN T1.LV0_PROD_RD_TEAM_CN_NAME
                                           WHEN 1 THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                                           WHEN 2 THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                         ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                         END AS PROD_RD_TEAM_CN_NAME,';
             V_SQL_CEG_PARENT := '
                            CASE WHEN T1.VIEW_FLAG = 0 THEN T1.LV0_PROD_RND_TEAM_CODE
                                 WHEN T1.VIEW_FLAG = 1 THEN T1.LV1_PROD_RND_TEAM_CODE
                                 WHEN T1.VIEW_FLAG = 2 THEN T1.LV2_PROD_RND_TEAM_CODE 
                            ELSE T1.LV3_PROD_RND_TEAM_CODE
                            END AS PARENT_CODE,';    
             V_SQL_CEG_PARENT_NAME := '
                            CASE WHEN T1.VIEW_FLAG = 0 THEN T1.LV0_PROD_RD_TEAM_CN_NAME
                                 WHEN T1.VIEW_FLAG = 1 THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                                 WHEN T1.VIEW_FLAG = 2 THEN T1.LV2_PROD_RD_TEAM_CN_NAME 
                            ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                            END AS PARENT_CN_NAME,';    

  --量纲颗粒度的维度时，不需要L1和L2字段
    ELSIF F_DIMENSION_TYPE = 'D' THEN 
             V_IN_PROFITS_NAME := '';
             V_PROFITS_NAME := '';
             V_SQL_PROFITS_NAME := '';
             V_REL_PROFITS_NAME := '';
             V_VIEW_NUM := 11;
             V_SQL_DMS_CODE := 'CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.DIMENSION_CODE 
                                     WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_SUBCATEGORY_CODE
                                ELSE T1.DIMENSION_SUB_DETAIL_CODE
                                END AS DMS_CODE,';
             V_SQL_DMS_NAME := 'CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.DIMENSION_CN_NAME 
                                     WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_SUBCATEGORY_CN_NAME
                                ELSE T1.DIMENSION_SUB_DETAIL_CN_NAME
                                END AS DMS_CN_NAME,'; 
             V_IN_PROD_RND_TEAM_CODE := '  
                            CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RND_TEAM_CODE
                                 WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RND_TEAM_CODE
                            ELSE T1.LV3_PROD_RND_TEAM_CODE
                            END AS PROD_RND_TEAM_CODE,';    
             V_IN_PROD_RND_TEAM_CN_NAME := '
                            CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                                 WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                            ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                            END AS PROD_RD_TEAM_CN_NAME,';
             V_SQL_CEG_PARENT := '
                            CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.DIMENSION_CODE 
                                 WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_SUBCATEGORY_CODE
                                 WHEN T1.VIEW_FLAG IN (''9'',''10'',''11'') THEN T1.SPART_CODE
                            ELSE T1.DIMENSION_SUB_DETAIL_CODE
                            END AS PARENT_CODE,';   -- 专家团层级的父层级CODE
             V_SQL_CEG_PARENT_NAME := '
                            CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.DIMENSION_CN_NAME 
                                 WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_SUBCATEGORY_CN_NAME
                                 WHEN T1.VIEW_FLAG IN (''9'',''10'',''11'') THEN T1.SPART_CN_NAME 
                            ELSE T1.DIMENSION_SUB_DETAIL_CN_NAME
                            END AS PARENT_CN_NAME,';   -- 专家团层级的父层级
             V_SQL_DMS_PARENT := '
                            CASE WHEN T1.VIEW_FLAG IN (0,1,2) THEN T1.LV1_PROD_RND_TEAM_CODE
                                 WHEN T1.VIEW_FLAG IN (3,4,5) THEN T1.LV2_PROD_RND_TEAM_CODE
                            ELSE T1.LV3_PROD_RND_TEAM_CODE
                            END AS PARENT_CODE,';   -- 量纲层级的父层级CODE
             V_SQL_DMS_PARENT_NAME := '
                            CASE WHEN T1.VIEW_FLAG IN (0,1,2) THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                                 WHEN T1.VIEW_FLAG IN (3,4,5) THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                            ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                            END AS PARENT_CN_NAME,';   -- 量纲层级的父层级
             V_SQL_DMS_CODE_TOTAL := '
                           T1.DMS_CODE,
                           T1.DIMENSION_CODE,
                           T1.DIMENSION_SUBCATEGORY_CODE,
                           T1.DIMENSION_SUB_DETAIL_CODE,';  
             V_SQL_DMS_NAME_TOTAL := '
                           T1.DMS_CN_NAME,
                           T1.DIMENSION_CN_NAME,
                           T1.DIMENSION_SUBCATEGORY_CN_NAME,
                           T1.DIMENSION_SUB_DETAIL_CN_NAME,'; 
                              
  --盈利颗粒度的维度时，不需要LV3字段及量纲层级字段
    ELSIF F_DIMENSION_TYPE = 'P' THEN
                V_SQL_DMS_LEV_CODE := '';
                V_SQL_DMS_LEV_NAME := '';
                V_GROUP_DMS_LEV_CODE := '';
                V_REL_DMS := '';
                V_SQL_PROD_RND_TEAM_TOTAL := ' T1.LV0_PROD_RND_TEAM_CODE,
                                               T1.LV1_PROD_RND_TEAM_CODE,
                                               T1.LV2_PROD_RND_TEAM_CODE,
                                               T1.LV0_PROD_RD_TEAM_CN_NAME,
                                               T1.LV1_PROD_RD_TEAM_CN_NAME,
                                               T1.LV2_PROD_RD_TEAM_CN_NAME,'; 
                V_VIEW_NUM := 8; 
                V_IN_PROD_RND_TEAM_CODE := '                  
                            CASE T1.VIEW_FLAG
                               WHEN 0 THEN T1.LV0_PROD_RND_TEAM_CODE
                               WHEN 1 THEN T1.LV1_PROD_RND_TEAM_CODE
                            ELSE
                                T1.LV2_PROD_RND_TEAM_CODE
                            END AS PROD_RND_TEAM_CODE,';
                V_IN_PROD_RND_TEAM_CN_NAME:='
                            CASE T1.VIEW_FLAG
                               WHEN 0 THEN T1.LV0_PROD_RD_TEAM_CN_NAME
                               WHEN 1 THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                            ELSE 
                                T1.LV2_PROD_RD_TEAM_CN_NAME
                            END AS PROD_RD_TEAM_CN_NAME,';
                V_SQL_CEG_PARENT := '
                            CASE WHEN T1.VIEW_FLAG = 0 THEN T1.LV0_PROD_RND_TEAM_CODE
                                 WHEN T1.VIEW_FLAG = 1 THEN T1.LV1_PROD_RND_TEAM_CODE
                                 WHEN T1.VIEW_FLAG = 2 THEN T1.LV2_PROD_RND_TEAM_CODE 
                                 WHEN T1.VIEW_FLAG = 3 THEN T1.L1_NAME
                            ELSE T1.L2_NAME
                            END AS PARENT_CODE,';        
                V_SQL_CEG_PARENT_NAME := '
                            CASE WHEN T1.VIEW_FLAG = 0 THEN T1.LV0_PROD_RD_TEAM_CN_NAME
                                 WHEN T1.VIEW_FLAG = 1 THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                                 WHEN T1.VIEW_FLAG = 2 THEN T1.LV2_PROD_RD_TEAM_CN_NAME 
                                 WHEN T1.VIEW_FLAG = 3 THEN T1.L1_NAME
                            ELSE T1.L2_NAME
                            END AS PARENT_CN_NAME,';    
                    
     END IF;
  IF F_COST_TYPE = 'P' THEN  -- 采购成本类型
      V_BASE_LEVEL := '';
      V_IN_BASE_LEVEL := '';
  END IF;
    
    -- 把ITEM层级状态码数据插入状态码表中
     V_SQL := '
        INSERT INTO '||V_TO_TABLE||' (
--                     ID,
                     PERIOD_YEAR,
                     VERSION_ID,
                     '||V_PROD_RND_TEAM
                     ||V_DMS_CODE
                     ||V_DMS_LEV_CODE
                     ||V_DMS_NAME
                     ||V_DMS_LEV_NAME
                     ||V_SPART_CODE
                     ||V_SPART_NAME
                     ||V_PROFITS_NAME
                     ||V_PROFITS_L1_L2
                     ||V_BASE_LEVEL
                     ||V_GROUP
                     ||V_INDEX_FLAG
                     ||'STATUS_CODE,
                     PARENT_CODE,
                     '||V_PARENT_NAME||'
                     CREATED_BY,
                     CREATION_DATE,
                     LAST_UPDATED_BY,
                     LAST_UPDATE_DATE,
                     DEL_FLAG,
                     APPEND_YEAR)
        SELECT 
--               '||V_SEQUENCE||'
               T1.PERIOD_YEAR,
               '||V_VERSION_ID||' AS VERSION_ID,
               '||V_IN_PROD_RND_TEAM_CODE
               ||V_IN_PROD_RND_TEAM_CN_NAME
               ||V_SQL_DMS_CODE
               ||V_SQL_DMS_LEV_CODE
               ||V_SQL_DMS_NAME
               ||V_SQL_DMS_LEV_NAME
               ||V_IN_SPART
               ||V_IN_PROFITS_NAME
               ||V_PROFITS_L1_L2
               ||V_BASE_LEVEL
               ||V_IN_LEVEL 
               ||V_INDEX_FLAG 
               ||'T1.STATUS_CODE,
               '||V_SQL_PARENT||'
               -1 AS CREATED_BY,
               CURRENT_TIMESTAMP AS CREATION_DATE,
               -1 AS LAST_UPDATED_BY,
               CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
               ''N'' AS DEL_FLAG,
               T1.APPEND_YEAR
            FROM '||V_TMP2_TABLE||' T1';
       DBMS_OUTPUT.PUT_LINE(V_SQL); 
       EXECUTE IMMEDIATE V_SQL; 
 
            
        --写入日志
   V_STEP_MUM := V_STEP_MUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_MUM,
    F_CAL_LOG_DESC => '版本号为：'||V_VERSION_ID||'的ITEM层级缺失状态码插入'||V_TO_TABLE||'表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
    
    -- 按1-10 ，从品类层级到ICT层级，分不同项目进行循环    
        FOR GRO_LEV IN V_BEGIN_NUM .. V_VIEW_NUM LOOP
                V_GROUP_CODE := '';
                V_SQL_PARENT := '';
                V_PARTITION_DIM := '';
                V_GROUP_LEVEL := '';
        IF GRO_LEV = 1 AND F_COST_TYPE = 'P' THEN   -- 品类层级
                V_GROUP_CODE := ' T1.CATEGORY_CODE AS GROUP_CODE,';
                V_GROUP_LEVEL := 'CATEGORY';
                V_SQL_PARENT := ' T1.L4_CEG_CODE AS PARENT_CODE,
                                   T1.L4_CEG_SHORT_CN_NAME AS PARENT_CN_NAME,';    
                V_PARTITION_DIM := 'T1.CATEGORY_CODE,
                                    T1.L4_CEG_CODE,
                                    T1.L4_CEG_SHORT_CN_NAME,';
        ELSIF GRO_LEV = 2 AND F_COST_TYPE = 'P' THEN   -- 模块层级
                V_GROUP_CODE := ' T1.L4_CEG_CODE AS GROUP_CODE,';
                V_GROUP_LEVEL := 'MODL';
                V_PARTITION_DIM := 'T1.L4_CEG_CODE,
                                    T1.L3_CEG_CODE,
                                    T1.L3_CEG_SHORT_CN_NAME,';
                V_SQL_PARENT := ' T1.L3_CEG_CODE AS PARENT_CODE,
                                   T1.L3_CEG_SHORT_CN_NAME AS PARENT_CN_NAME,'; 
        ELSIF GRO_LEV = 3 AND F_COST_TYPE = 'P' THEN   -- 专家团层级
                V_GROUP_CODE := ' T1.L3_CEG_CODE AS GROUP_CODE,';
                V_GROUP_LEVEL := 'CEG';
                V_PARTITION_DIM := 'T1.L3_CEG_CODE,';
                V_SQL_PARENT := V_SQL_CEG_PARENT||V_SQL_CEG_PARENT_NAME;
        ELSIF GRO_LEV = 2 AND F_COST_TYPE = 'M' THEN   -- 制造对象层级
                V_GROUP_CODE := ' T1.MANUFACTURE_OBJECT_CODE AS GROUP_CODE,';
                V_GROUP_LEVEL := 'MANUFACTURE_OBJECT';
                V_PARTITION_DIM := 'T1.MANUFACTURE_OBJECT_CODE,
                                    T1.MANUFACTURE_OBJECT_CN_NAME,
                                    T1.SHIPPING_OBJECT_CODE,
                                    T1.SHIPPING_OBJECT_CN_NAME,';
                V_SQL_PARENT := ' T1.SHIPPING_OBJECT_CODE AS PARENT_CODE,
                                   T1.SHIPPING_OBJECT_CN_NAME AS PARENT_CN_NAME,'; 
        ELSIF GRO_LEV = 3 AND F_COST_TYPE = 'M' THEN   -- 发货对象层级
                V_GROUP_CODE := ' T1.SHIPPING_OBJECT_CODE AS GROUP_CODE,';
                V_GROUP_LEVEL := 'SHIPPING_OBJECT';
                V_PARTITION_DIM := 'T1.SHIPPING_OBJECT_CODE,
                                    T1.SHIPPING_OBJECT_CN_NAME,';
                V_BASE_LEVEL := '
                         SHIPPING_OBJECT_CODE,
                         SHIPPING_OBJECT_CN_NAME,';
                V_IN_BASE_LEVEL := '
                         T1.SHIPPING_OBJECT_CODE,
                         T1.SHIPPING_OBJECT_CN_NAME,';
                V_REL_BASE_LEVEL := ' AND NVL(T1.SHIPPING_OBJECT_CODE,''SNULL'') = NVL(T2.SHIPPING_OBJECT_CODE,''SNULL'') ';
                V_SQL_PARENT := V_SQL_CEG_PARENT||V_SQL_CEG_PARENT_NAME;
        ELSIF GRO_LEV = 4 THEN   -- 通用：LV3层级/盈利:L2层级/量纲：SPART层级
          V_BASE_LEVEL := '';
          V_IN_BASE_LEVEL := '';
          V_REL_BASE_LEVEL := '';
          IF F_DIMENSION_TYPE = 'U' THEN
                V_GROUP_LEVEL := 'LV3';
                V_GROUP_CODE := ' T1.LV3_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                V_SQL_PARENT := ' T1.LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                   T1.LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                V_IN_PROD_RND_TEAM_CODE := ' T1.LV3_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
          ELSIF F_DIMENSION_TYPE = 'P' THEN    
                   V_GROUP_LEVEL := 'L2';
                   V_GROUP_CODE := ' T1.L2_NAME AS GROUP_CODE,';
                   V_SQL_PARENT := ' T1.L1_NAME AS PARENT_CODE,
                                      T1.L1_NAME AS PARENT_CN_NAME, ';
                   V_IN_PROFITS_NAME := ' T1.L2_NAME AS PROFITS_NAME,';
          ELSIF F_DIMENSION_TYPE = 'D' THEN
                   V_GROUP_LEVEL := 'SPART';
                   V_GROUP_CODE := ' T1.SPART_CODE AS GROUP_CODE,';
                   V_SQL_PARENT := ' T1.DIMENSION_SUB_DETAIL_CODE AS PARENT_CODE,
                                      T1.DIMENSION_SUB_DETAIL_CN_NAME AS PARENT_CN_NAME,';
          END IF;    
        ELSIF GRO_LEV = 5 THEN   -- 通用：LV2层级/盈利:L1层级/量纲：SUB_DETAIL (量纲子类明细)层级
          IF F_DIMENSION_TYPE = 'U' THEN  
                V_GROUP_LEVEL := 'LV2';    
                V_GROUP_CODE := ' T1.LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                V_SQL_PARENT := ' T1.LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                   T1.LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                V_IN_PROD_RND_TEAM_CODE := ' T1.LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
                V_SQL_PROD_RND_TEAM_TOTAL := 'T1.LV2_PROD_RND_TEAM_CODE,
                                              T1.LV1_PROD_RND_TEAM_CODE,
                                              T1.LV1_PROD_RD_TEAM_CN_NAME,';
          ELSIF F_DIMENSION_TYPE = 'P' THEN    
                   V_GROUP_LEVEL := 'L1';    
                   V_GROUP_CODE := ' T1.L1_NAME AS GROUP_CODE,';
                   V_SQL_PARENT := ' T1.LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                      T1.LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                   V_PROFITS_L1_L2 := 'L1_NAME,';
                   V_SQL_PROFITS_NAME := 'T1.PROFITS_NAME,
                                          T1.L1_NAME,';
                   V_IN_PROFITS_NAME := ' T1.L1_NAME AS PROFITS_NAME,';
                   V_REL_PROFITS_NAME := ' AND NVL(T1.L1_NAME,''SNULL'') = NVL(T2.L1_NAME,''SNULL'') ';
          ELSIF F_DIMENSION_TYPE = 'D' THEN
                   V_GROUP_SPART := '';
                   V_IN_SPART_CODE := 'NULL AS SPART_CODE,';
                   V_GROUP_LEVEL := 'SUB_DETAIL';
                   V_GROUP_CODE := ' T1.DIMENSION_SUB_DETAIL_CODE AS GROUP_CODE,';
                   V_SQL_PARENT := ' T1.DIMENSION_SUBCATEGORY_CODE AS PARENT_CODE,
                                      T1.DIMENSION_SUBCATEGORY_CN_NAME AS PARENT_CN_NAME,';
          END IF;
        ELSIF GRO_LEV = 6 THEN   -- 通用：LV1层级/盈利:LV2层级/量纲：SUBCATEGORY (量纲子类)层级
          IF F_DIMENSION_TYPE = 'U' THEN
                V_GROUP_LEVEL := 'LV1';
                V_GROUP_CODE := ' T1.LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                V_SQL_PARENT := ' T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                   T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                V_IN_PROD_RND_TEAM_CODE := ' T1.LV1_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
                V_SQL_PROD_RND_TEAM_TOTAL := 'T1.LV1_PROD_RND_TEAM_CODE,
                                              T1.LV0_PROD_RND_TEAM_CODE,
                                              T1.LV0_PROD_RD_TEAM_CN_NAME,';
          ELSIF F_DIMENSION_TYPE = 'P' THEN    
                   V_GROUP_LEVEL := 'LV2';
                   V_GROUP_CODE := ' T1.LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                   V_SQL_PARENT := ' T1.LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                      T1.LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                   V_IN_PROD_RND_TEAM_CODE := ' T1.LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
                   V_IN_PROFITS_NAME := ' NULL AS PROFITS_NAME,';
                   V_PROFITS_L1_L2 := '';
                   V_SQL_PROFITS_NAME := 'T1.PROFITS_NAME,';
                   V_REL_PROFITS_NAME := ' AND NVL(T1.PROFITS_NAME,''SNULL'') = NVL(T2.PROFITS_NAME,''SNULL'')';
          ELSIF F_DIMENSION_TYPE = 'D' THEN
                   V_GROUP_LEVEL := 'SUBCATEGORY';
                   V_GROUP_CODE := ' T1.DIMENSION_SUBCATEGORY_CODE AS GROUP_CODE,';
                   V_SQL_PARENT := ' T1.DIMENSION_CODE AS PARENT_CODE,
                                      T1.DIMENSION_CN_NAME AS PARENT_CN_NAME,';
                   V_SQL_DMS_LEV_CODE := 'NULL AS DIMENSION_SUB_DETAIL_CODE,
                                          T1.DIMENSION_SUBCATEGORY_CODE,
                                          T1.DIMENSION_CODE,';
                   V_GROUP_DMS_LEV_CODE := 'T1.DIMENSION_SUBCATEGORY_CODE,
                                            T1.DIMENSION_CODE,
                                            T1.DIMENSION_CN_NAME,';
          END IF;
         ELSIF GRO_LEV = 7 THEN   -- 通用：ICT层级/盈利:LV1层级/量纲：DIMENSION (量纲)层级
          IF F_DIMENSION_TYPE = 'U' THEN      
                V_GROUP_LEVEL := 'ICT';
                V_GROUP_CODE := ' T1.LV0_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                V_SQL_PARENT := ' T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                   T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                V_SQL_PROD_RND_TEAM_TOTAL := 'T1.LV0_PROD_RND_TEAM_CODE,
                                              T1.LV0_PROD_RD_TEAM_CN_NAME,';
                V_IN_PROD_RND_TEAM_CODE := ' T1.LV0_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
          ELSIF F_DIMENSION_TYPE = 'P' THEN
                   V_GROUP_LEVEL := 'LV1';
                   V_GROUP_CODE := ' T1.LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                   V_SQL_PARENT := ' T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                      T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                   V_PROFITS_L1_L2 := '';
                   V_IN_PROFITS_NAME := ' NULL AS PROFITS_NAME,';
                   V_SQL_PROFITS_NAME := 'T1.PROFITS_NAME,';
                   V_REL_PROFITS_NAME := ' AND NVL(T1.PROFITS_NAME,''SNULL'') = NVL(T2.PROFITS_NAME,''SNULL'')';
                   V_IN_PROD_RND_TEAM_CODE := ' T1.LV1_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
                   V_SQL_PROD_RND_TEAM_TOTAL := 'T1.LV1_PROD_RND_TEAM_CODE,
                                                 T1.LV0_PROD_RND_TEAM_CODE,
                                                 T1.LV0_PROD_RD_TEAM_CN_NAME,';
          ELSIF F_DIMENSION_TYPE = 'D' THEN
                   V_GROUP_LEVEL := 'DIMENSION';
                   V_GROUP_CODE := ' T1.DIMENSION_CODE AS GROUP_CODE,';
                   V_SQL_PARENT := V_SQL_DMS_PARENT||V_SQL_DMS_PARENT_NAME;
                   V_SQL_DMS_LEV_CODE := 'NULL AS DIMENSION_SUB_DETAIL_CODE,
                                          NULL AS DIMENSION_SUBCATEGORY_CODE,
                                          T1.DIMENSION_CODE,';
                   V_GROUP_DMS_LEV_CODE := 'T1.DIMENSION_CODE,';
          END IF;         
         ELSIF GRO_LEV = 8 THEN   -- 盈利:ICT层级/量纲：LV3
          IF F_DIMENSION_TYPE = 'P' THEN                   
                V_GROUP_LEVEL := 'ICT';
                V_GROUP_CODE := ' T1.LV0_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                V_SQL_PARENT := ' T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                   T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                V_IN_PROFITS_NAME := ' NULL AS PROFITS_NAME,';
                V_SQL_PROFITS_NAME := 'T1.PROFITS_NAME,';
                V_SQL_PROD_RND_TEAM_TOTAL := 'T1.LV0_PROD_RND_TEAM_CODE,
                                              T1.LV0_PROD_RD_TEAM_CN_NAME,';
                V_IN_PROD_RND_TEAM_CODE := ' T1.LV0_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
          ELSIF F_DIMENSION_TYPE = 'D' THEN
                   V_GROUP_LEVEL := 'LV3';
                   V_GROUP_CODE := ' T1.LV3_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                   V_SQL_PARENT := ' T1.LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                      T1.LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                   V_SQL_DMS_LEV_CODE := 'NULL AS DIMENSION_SUB_DETAIL_CODE,
                                          NULL AS DIMENSION_SUBCATEGORY_CODE,
                                          NULL AS DIMENSION_CODE,';          
                   V_GROUP_DMS_LEV_CODE := '';
                   V_IN_PROD_RND_TEAM_CODE := ' T1.LV3_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';                   
          END IF;
          ELSIF GRO_LEV = 9 THEN    -- 量纲：LV2
                   V_GROUP_LEVEL := 'LV2';
                   V_GROUP_CODE := ' T1.LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                   V_SQL_PARENT := ' T1.LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                      T1.LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                   V_IN_PROD_RND_TEAM_CODE := ' T1.LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';   
                   V_SQL_PROD_RND_TEAM_TOTAL := 'T1.LV2_PROD_RND_TEAM_CODE,
                                                 T1.LV1_PROD_RND_TEAM_CODE,
                                                 T1.LV0_PROD_RND_TEAM_CODE,
                                                 T1.LV1_PROD_RD_TEAM_CN_NAME,';
          ELSIF GRO_LEV = 10 THEN   -- 量纲：LV1         
                 V_GROUP_LEVEL := 'LV1';
                 V_GROUP_CODE := ' T1.LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                 V_SQL_PARENT := ' T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                    T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                 V_IN_PROD_RND_TEAM_CODE := ' T1.LV1_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';   
                 V_SQL_PROD_RND_TEAM_TOTAL := 'T1.LV1_PROD_RND_TEAM_CODE,
                                               T1.LV0_PROD_RND_TEAM_CODE,
                                               T1.LV0_PROD_RD_TEAM_CN_NAME,';    
          ELSIF GRO_LEV = 11 THEN   -- 量纲：ICT         
                 V_GROUP_LEVEL := 'ICT';
                 V_GROUP_CODE := ' T1.LV0_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                 V_SQL_PARENT := ' T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                    T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                 V_IN_PROD_RND_TEAM_CODE := ' T1.LV0_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';   
                 V_SQL_PROD_RND_TEAM_TOTAL := 'T1.LV0_PROD_RND_TEAM_CODE,
                                               T1.LV0_PROD_RD_TEAM_CN_NAME,';    
        END IF;

  V_SQL := '
      INSERT INTO '||V_TO_TABLE||' (
--               ID,
               PERIOD_YEAR,
               VERSION_ID,
               '||V_PROD_RND_TEAM
               ||V_DMS_CODE
               ||V_DMS_LEV_CODE
               ||V_DMS_NAME
               ||V_DMS_LEV_NAME
               ||V_SPART_CODE
               ||V_SPART_NAME
               ||V_PROFITS_NAME
               ||V_PROFITS_L1_L2
               ||V_BASE_LEVEL
               ||V_GROUP
               ||V_INDEX_FLAG
               ||'STATUS_CODE,
               PARENT_CODE,
               '||V_PARENT_NAME||'
               CREATED_BY,
               CREATION_DATE,
               LAST_UPDATED_BY,
               LAST_UPDATE_DATE,
               DEL_FLAG,
               APPEND_YEAR
               )
               
      WITH ITEM_STATUS_TMP AS(
           SELECT T1.PERIOD_YEAR,
                  '||V_IN_PROD_RND_TEAM_CODE
                  ||V_SQL_DMS_LEV_CODE
                  ||V_IN_SPART_CODE
                  ||V_IN_PROFITS_NAME
                  ||V_PROFITS_L1_L2
                  ||V_BASE_LEVEL
                  ||V_GROUP_CODE    -- 取T1表中的上一层级CODE作为GROUP_CODE
                  ||V_SQL_PARENT
                  ||V_IN_INDEX_FLAG
                  ||'SUM(CASE WHEN T1.STATUS_CODE IN (3,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋3
                  SUM(DECODE(T1.STATUS_CODE,1,0,1)) AS STATUS_1,     -- 值=0，说明子级都为1，赋1
                  SUM(DECODE(T1.STATUS_CODE,4,0,1)) AS STATUS_4    -- 值=0，说明子级都为4，赋2
               FROM '||V_TMP2_TABLE||' T1
               GROUP BY '
               ||V_SQL_PROD_RND_TEAM_TOTAL
               ||V_GROUP_DMS_LEV_CODE
               ||V_GROUP_SPART
               ||V_PROFITS_L1_L2
               ||V_PARTITION_DIM
               ||V_IN_INDEX_FLAG
               ||'T1.PERIOD_YEAR)
                                    
        SELECT 
--              '||V_SEQUENCE||'
               T1.PERIOD_YEAR,
               '||V_VERSION_ID||' AS VERSION_ID,
               '||V_SQL_PROD_RND_TEAM
               ||V_SQL_DMS_CODE_TOTAL
               ||V_SQL_DMS_NAME_TOTAL
               ||V_IN_SPART
               ||V_SQL_PROFITS_NAME
               ||V_IN_BASE_LEVEL
               ||'T1.GROUP_CODE,
               T1.GROUP_CN_NAME,
               T1.GROUP_LEVEL,
               '||V_IN_INDEX_FLAG||'
               CASE WHEN T2.STATUS_NORMAL > 0 THEN 3
                    WHEN T2.STATUS_1 = 0 THEN 1
                    WHEN T2.STATUS_4 = 0 THEN 2
               ELSE 4 END AS STATUS_CODE,
               T1.PARENT_CODE,
               '||V_IN_PARENT_NAME||'
               -1 AS CREATED_BY,
               CURRENT_TIMESTAMP AS CREATION_DATE,
               -1 AS LAST_UPDATED_BY,
               CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
               ''N'' AS DEL_FLAG,    
               '''' AS APPEND_YEAR
            FROM '||V_FROM_TABLE_2||' T1
            LEFT JOIN ITEM_STATUS_TMP T2
            ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
            AND NVL(T1.GROUP_CODE,''SNULL'') = NVL(T2.GROUP_CODE,''SNULL'')
            AND NVL(T1.PARENT_CODE,''SNULL'') = NVL(T2.PARENT_CODE,''SNULL'')
            '||V_REL_PROD_RND_TEAM
            ||V_REL_DMS
            ||V_REL_SPART
            ||V_REL_PROFITS_NAME
            ||V_REL_INDEX_FLAG
            ||V_REL_BASE_LEVEL||'
            WHERE T1.GROUP_LEVEL = '''||V_GROUP_LEVEL||'''
            AND T1.VERSION_ID = '||V_VERSION_ID ;        

       DBMS_OUTPUT.PUT_LINE(V_SQL); 
       EXECUTE IMMEDIATE V_SQL;        
        
   --写入日志
   V_STEP_MUM := V_STEP_MUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_MUM,
    F_CAL_LOG_DESC => '版本号为：'||V_VERSION_ID||'的其余层级缺失状态码插入'||V_TO_TABLE||'表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');   
     END LOOP;
        
    -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
 
END$$
/

