-- Name: f_fcst_ict_point_base_cus_mon_result_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_fcst_ict_point_base_cus_mon_result_t(f_cost_type character varying, f_granularity_type character varying, f_page_type character varying, f_custom_id character varying, f_keystr text DEFAULT NULL::text, f_version_id character varying DEFAULT NULL::character varying, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************

--选择场景
场景										存储数据	
场景1 路径一+PBI+SPART						SPART				SPART
场景2 路径二+PBI+量纲						量纲				虚化量纲子类明细
场景3 路径二+PBI+量纲子类					量纲子类			虚化量纲子类明细
场景4 路径二+PBI+量纲子类明细				虚化量纲子类明细	量纲子类明细(权重)
场景5 路径二+PBI+量纲+量纲子类				量纲子类			虚化量纲子类明细
场景6 路径二+PBI+量纲子类+量纲子类明细		量纲子类明细		
场景7 路径二+PBI+量纲+量纲子类明细			虚化量纲子类明细	量纲子类明细(权重)

参数描述: 参数一: F_COST_TYPE 成本类型:PSP PSP成本; STD: 标准成本
		参数二:  F_GRANULARITY_TYPE 目录树类型:	IRB:重量级团队目录; INDUS:产业目录;  PROD:销售目录
		参数三： F_PAGE_TYPE 页面类型 'MONTH' 月度页面 'REPLACE_DIM'编码替代页面
		参数四： F_CUSTOM_ID 组合ID
		参数五:  F_KEYSTR	密钥
		参数六:  F_VERSION_ID 版本号
		参数七:  X_RESULT_STATUS 运行状态返回值 ‘1’为成功，‘0’为失败

--------来源表
--虚化维表
重量级团队目录-PSP:	DM_FCST_ICT_PSP_BASE_CUS_DIM_T
重量级团队目录-STD:	DM_FCST_ICT_STD_BASE_CUS_DIM_T	


----权重计算取数表
重量级团队目录		DM_FCST_ICT_PSP_IRB_ANNL_AVG_T		--PSP IRB
产业目录：			DM_FCST_ICT_PSP_INDUS_ANNL_AVG_T	--PSP INDUS
销售目录：			DM_FCST_ICT_PSP_PROD_ANNL_AVG_T		--PSP PROD
重量级团队目录		DM_FCST_ICT_STD_IRB_ANNL_AVG_T		--STD IRB
产业目录：			DM_FCST_ICT_STD_INDUS_ANNL_AVG_T	--STD INDUS
销售目录：			DM_FCST_ICT_STD_PROD_ANNL_AVG_T		--STD PROD

----指数取数表
--月度指数中间表
重量级团队目录		DM_FCST_ICT_PSP_IRB_MON_MID_COST_IDX_T		--PSP IRB
产业目录			DM_FCST_ICT_PSP_INDUS_MON_MID_COST_IDX_T    --PSP INDUS
销售目录			DM_FCST_ICT_PSP_PROD_MON_MID_COST_IDX_T     --PSP PROD
重量级团队目录		DM_FCST_ICT_STD_IRB_MON_MID_COST_IDX_T      --STD IRB
产业目录			DM_FCST_ICT_STD_INDUS_MON_MID_COST_IDX_T    --STD INDUS
销售目录			DM_FCST_ICT_STD_PROD_MON_MID_COST_IDX_T     --STD PROD

--降成本指数中间表
重量级团队目录-PSP:	DM_FCST_ICT_PSP_MON_MID_COST_RED_IDX_T	--PSP
重量级团队目录-STD:	DM_FCST_ICT_STD_MON_MID_COST_RED_IDX_T	--STD

--------目标表
--权重表
重量级团队目录-PSP:	DM_FCST_ICT_PSP_BASE_CUS_MON_WEIGHT_T
重量级团队目录-STD:	DM_FCST_ICT_STD_BASE_CUS_MON_WEIGHT_T

--指数表
重量级团队目录-PSP: DM_FCST_ICT_PSP_BASE_CUS_MON_COST_IDX_T
重量级团队目录-STD: DM_FCST_ICT_STD_BASE_CUS_MON_COST_IDX_T

--降成本指数表
重量级团队目录-PSP: DM_FCST_ICT_PSP_BASE_CUS_MON_COST_RED_IDX_T
重量级团队目录-STD: DM_FCST_ICT_STD_BASE_CUS_MON_COST_RED_IDX_T

--同环比表
重量级团队目录-PSP: DM_FCST_ICT_PSP_BASE_CUS_MON_RATE_T
重量级团队目录-STD: DM_FCST_ICT_STD_BASE_CUS_MON_RATE_T
事例
SELECT F_FCST_ICT_POINT_BASE_CUS_MON_RESULT_T('PSP','IRB','MONTH','171','','');
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                   VARCHAR(200) := 'FIN_DM_OPT_FOI.F_FCST_ICT_POINT_BASE_CUS_MON_RESULT_T';
  V_VERSION                   BIGINT; --版本号
  V_EXCEPTION_FLAG            INT; --异常步骤
  V_BASE_PERIOD_ID            INT := TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '01');
  V_FROM_AMT_TABLE            VARCHAR(200);
  V_FROM_BASE_IDX_TABLE       VARCHAR(200);
  V_FROM_BASE_RED_IDX_TABLE   VARCHAR(200);
  V_FROM_DIM_TABLE            VARCHAR(200);
  V_TO_WEIGHT_TABLE           VARCHAR(200);
  V_TO_RED_IDX_TABLE          VARCHAR(200);
  V_TO_COST_IDX_TABLE         VARCHAR(200);
  V_TO_MONTH_RATE_TABLE       VARCHAR(200);
  V_FROM_MONTH_BASE_IDX_TABLE VARCHAR(200);
  V_TO_MONTH_COST_IDX_TABLE   VARCHAR(200);
  V_FROM_YTD_BASE_IDX_TABLE   VARCHAR(200);
  V_TO_YTD_COST_IDX_TABLE     VARCHAR(200);
  V_FROM_TOP_DIM_TABLE			VARCHAR(200);
  V_LV_CODE                   TEXT;
  V_LV_CN_NAME                TEXT;
  V_CUSTOM_CN_NAME            TEXT;
  V_VIEW_FLAG                 TEXT;
  V_GROUP_LEVEL               TEXT;
  V_GROUP_CODE                TEXT;
  V_PARENT_LEVEL              TEXT;
  V_IN_LV_CODE                TEXT;
  V_LV0_CODE                  TEXT;
  V_LV1_CODE                  TEXT;
  V_LV2_CODE                  TEXT;
  V_LV3_CODE                  TEXT;
  V_LV4_CODE                  TEXT;
  V_OTHER_DIM_PART            TEXT;
  V_SQL_OTHER_DIM_PART        TEXT;
  V_JOIN_OTHER_DIM_PART       TEXT;
  V_PBI_PART                  TEXT;
  V_RMB_COST_AMT              TEXT;
  V_VIEW_SQL                  TEXT;
  V_YEAR3                     TEXT;
  V_YEAR2                     TEXT;
  V_YEAR1                     TEXT;
  V_SQL                       TEXT;
  V_DIMENSION_PART            TEXT;
  V_SQL_DIMENSION_PART        TEXT;
  V_JOIN_DIMENSION_CODE       TEXT;

BEGIN 
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;

  V_FROM_AMT_TABLE            := 'FIN_DM_OPT_FOI.DM_FCST_ICT_' || F_COST_TYPE || '_' || F_GRANULARITY_TYPE ||'_ANNL_AVG_T';
  V_FROM_MONTH_BASE_IDX_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_' || F_COST_TYPE || '_' || F_GRANULARITY_TYPE ||'_MON_MID_COST_IDX_T';
  V_FROM_BASE_RED_IDX_TABLE   := 'FIN_DM_OPT_FOI.DM_FCST_ICT_' || F_COST_TYPE || '_MON_MID_COST_RED_IDX_T';
  V_FROM_DIM_TABLE            := 'FIN_DM_OPT_FOI.DM_FCST_ICT_' || F_COST_TYPE || '_BASE_CUS_DIM_T';
  V_FROM_TOP_DIM_TABLE        := 'FIN_DM_OPT_FOI.DM_FCST_ICT_' || F_COST_TYPE || '_' || F_GRANULARITY_TYPE ||'_TOP_SPART_INFO_T';
  V_TO_WEIGHT_TABLE           := 'FIN_DM_OPT_FOI.DM_FCST_ICT_' || F_COST_TYPE || '_BASE_CUS_MON_WEIGHT_T';
  V_TO_RED_IDX_TABLE          := 'FIN_DM_OPT_FOI.DM_FCST_ICT_' || F_COST_TYPE || '_BASE_CUS_MON_COST_RED_IDX_T';
  V_TO_MONTH_COST_IDX_TABLE   := 'FIN_DM_OPT_FOI.DM_FCST_ICT_' || F_COST_TYPE || '_BASE_CUS_MON_COST_IDX_T';
  V_TO_MONTH_RATE_TABLE       := 'FIN_DM_OPT_FOI.DM_FCST_ICT_' || F_COST_TYPE || '_BASE_CUS_MON_RATE_T';
  
  --编码替代需求表
  V_FROM_YTD_BASE_IDX_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_' || F_COST_TYPE || '_' || F_GRANULARITY_TYPE || '_YTD_MID_COST_IDX_T';
  V_TO_YTD_COST_IDX_TABLE   := 'FIN_DM_OPT_FOI.DM_FCST_ICT_' || F_COST_TYPE || '_BASE_CUS_YTD_COST_IDX_T';
  
  
  IF F_PAGE_TYPE = 'MONTH' THEN
    V_FROM_BASE_IDX_TABLE	:= V_FROM_MONTH_BASE_IDX_TABLE;
	V_TO_COST_IDX_TABLE		:= V_TO_MONTH_COST_IDX_TABLE;
  ELSIF F_PAGE_TYPE = 'REPLACE_DIM' THEN
    V_FROM_BASE_IDX_TABLE	:= V_FROM_YTD_BASE_IDX_TABLE;
	V_TO_COST_IDX_TABLE		:= V_TO_YTD_COST_IDX_TABLE;
  END IF;
  
  V_YEAR3 := TO_CHAR((YEAR(CURRENT_DATE) - 3) || '-' ||(YEAR(CURRENT_DATE) - 2));
  V_YEAR2 := TO_CHAR((YEAR(CURRENT_DATE) - 2) || '-' ||(YEAR(CURRENT_DATE) - 1));
  V_YEAR1 := TO_CHAR((YEAR(CURRENT_DATE) - 1) || '-' || YEAR(CURRENT_DATE));

--IRB跑全流程
--取数用
--场景1+场景4+场景6计算结果仅一层
--场景1:VIEW_FLAG = 'PROD_SPART'
--场景4+场景6:VIEW_FLAG = 'PROD_SPART' AND GROUP_LEVEL = 'SUB_DETAIL'

  V_SQL := '
		SELECT DISTINCT CUSTOM_CN_NAME,LV_CODE,LV_CN_NAME,VIEW_FLAG, GROUP_LEVEL,GROUP_CODE,PARENT_LEVEL,REPLACE(LV_CODE,'','','''''','''''') AS IN_LV_CODE
		  FROM ' || V_FROM_DIM_TABLE || '
		 WHERE CUSTOM_ID = ' || F_CUSTOM_ID || ';';
		 
  EXECUTE V_SQL INTO V_CUSTOM_CN_NAME,V_LV_CODE, V_LV_CN_NAME, V_VIEW_FLAG, V_GROUP_LEVEL, V_GROUP_CODE, V_PARENT_LEVEL, V_IN_LV_CODE;
 
   V_OTHER_DIM_PART:='
			REGION_CODE,
			REGION_CN_NAME,
			REPOFFICE_CODE,
			REPOFFICE_CN_NAME,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			';
  V_SQL_OTHER_DIM_PART:='
			T1.REGION_CODE,
			T1.REGION_CN_NAME,
			T1.REPOFFICE_CODE,
			T1.REPOFFICE_CN_NAME,
			T1.BG_CODE,
			T1.BG_CN_NAME,
			T1.OVERSEA_FLAG,
			';
  V_JOIN_OTHER_DIM_PART:='
		AND T1.REGION_CODE = T2.REGION_CODE
		AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
		AND T1.BG_CODE = T2.BG_CODE
		AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG 
		';
		
  V_DIMENSION_PART:='
					DIMENSION_CODE,
					DIMENSION_CN_NAME,
					DIMENSION_SUBCATEGORY_CODE,
					DIMENSION_SUBCATEGORY_CN_NAME,
					DIMENSION_SUB_DETAIL_CODE,
					DIMENSION_SUB_DETAIL_CN_NAME,
					';
  V_SQL_DIMENSION_PART:='
					T1.DIMENSION_CODE,
					T1.DIMENSION_CN_NAME,
					T1.DIMENSION_SUBCATEGORY_CODE,
					T1.DIMENSION_SUBCATEGORY_CN_NAME,
					T1.DIMENSION_SUB_DETAIL_CODE,
					T1.DIMENSION_SUB_DETAIL_CN_NAME,
					';
  V_JOIN_DIMENSION_CODE:='
					AND NVL(T1.DIMENSION_CODE,''DC'') = NVL(T2.DIMENSION_CODE,''DC'')		
					AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''DSC'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''DSC'')
					AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''DSDC'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''DSDC'')
					';
					
  IF F_GRANULARITY_TYPE = 'IRB' THEN
    V_LV0_CODE := 'T1.LV0_PROD_RND_TEAM_CODE ';
    V_LV1_CODE := 'T1.LV1_PROD_RND_TEAM_CODE ';
    V_LV2_CODE := 'T1.LV2_PROD_RND_TEAM_CODE ';
    V_LV3_CODE := 'T1.LV3_PROD_RND_TEAM_CODE ';
    V_LV4_CODE := 'T1.LV4_PROD_RND_TEAM_CODE ';
    V_PBI_PART := '
				  T1.LV0_PROD_RND_TEAM_CODE,
				  T1.LV1_PROD_RND_TEAM_CODE,
				  T1.LV2_PROD_RND_TEAM_CODE,
				  T1.LV3_PROD_RND_TEAM_CODE,
				  T1.LV4_PROD_RND_TEAM_CODE,
				  T1.LV0_PROD_RD_TEAM_CN_NAME,
				  T1.LV1_PROD_RD_TEAM_CN_NAME,
				  T1.LV2_PROD_RD_TEAM_CN_NAME,
				  T1.LV3_PROD_RD_TEAM_CN_NAME,
				  T1.LV4_PROD_RD_TEAM_CN_NAME,
				  ';
  
  ELSIF F_GRANULARITY_TYPE = 'INDUS' THEN
    V_LV0_CODE := 'T1.LV0_INDUSTRY_CATG_CODE ';
    V_LV1_CODE := 'T1.LV1_INDUSTRY_CATG_CODE ';
    V_LV2_CODE := 'T1.LV2_INDUSTRY_CATG_CODE ';
    V_LV3_CODE := 'T1.LV3_INDUSTRY_CATG_CODE ';
    V_LV4_CODE := 'T1.LV4_INDUSTRY_CATG_CODE ';
    V_PBI_PART := '
				  T1.LV0_INDUSTRY_CATG_CODE    ,
				  T1.LV1_INDUSTRY_CATG_CODE      ,
				  T1.LV2_INDUSTRY_CATG_CODE      ,
				  T1.LV3_INDUSTRY_CATG_CODE      ,
				  T1.LV4_INDUSTRY_CATG_CODE      ,
				  T1.LV0_INDUSTRY_CATG_CN_NAME,
				  T1.LV1_INDUSTRY_CATG_CN_NAME,
				  T1.LV2_INDUSTRY_CATG_CN_NAME,
				  T1.LV3_INDUSTRY_CATG_CN_NAME,
				  T1.LV4_INDUSTRY_CATG_CN_NAME,
				  ';
  ELSIF F_GRANULARITY_TYPE = 'PROD' THEN
    V_LV0_CODE := 'T1.LV0_PROD_LIST_CODE ';
    V_LV1_CODE := 'T1.LV1_PROD_LIST_CODE ';
    V_LV2_CODE := 'T1.LV2_PROD_LIST_CODE ';
    V_LV3_CODE := 'T1.LV3_PROD_LIST_CODE ';
    V_LV4_CODE := 'T1.LV4_PROD_LIST_CODE ';
    V_PBI_PART := '
				  T1.LV0_PROD_LIST_CODE,
				  T1.LV1_PROD_LIST_CODE,
				  T1.LV2_PROD_LIST_CODE,
				  T1.LV3_PROD_LIST_CODE,
				  T1.LV4_PROD_LIST_CODE,
				  T1.LV0_PROD_LIST_CN_NAME,
				  T1.LV1_PROD_LIST_CN_NAME,
				  T1.LV2_PROD_LIST_CN_NAME,
				  T1.LV3_PROD_LIST_CN_NAME,
				  T1.LV4_PROD_LIST_CN_NAME,
				  ';
  END IF;

  IF F_COST_TYPE = 'PSP' THEN
    V_RMB_COST_AMT := ' T1.RMB_COST_AMT,';
  ELSIF F_COST_TYPE = 'STD' THEN
    V_RMB_COST_AMT := '
        TO_NUMBER(GS_DECRYPT(T1.RMB_COST_AMT,
              '''|| F_KEYSTR ||''',
              ''aes128'',
              ''cbc'',
              ''sha256'')) AS RMB_COST_AMT,';
  END IF;

  IF V_VIEW_FLAG = 'PROD_SPART' THEN
    V_VIEW_SQL := 'AND T1.SPART_CODE = D.SPART_CODE ';
  ELSIF V_VIEW_FLAG = 'DIMENSION' THEN
    V_VIEW_SQL := '
    AND NVL(D.DIMENSION_CODE,''DC'') = DECODE(D.DIMENSION_CODE,'''',''DC'',T1.DIMENSION_CODE)
    AND NVL(D.DIMENSION_SUBCATEGORY_CODE,''DSC'') = DECODE(D.DIMENSION_SUBCATEGORY_CODE,'''',''DSC'',T1.DIMENSION_SUBCATEGORY_CODE)
    AND NVL(D.DIMENSION_SUB_DETAIL_CODE,''DSDC'') = DECODE(D.DIMENSION_SUB_DETAIL_CODE,'''',''DSDC'',T1.DIMENSION_SUB_DETAIL_CODE) ';
      
  END IF;

--年均本临时表建表
DROP TABLE IF EXISTS DM_ANNL_AVG_TEMP;
CREATE TEMPORARY TABLE DM_ANNL_AVG_TEMP(
 LV0_CODE		VARCHAR(50),
 LV1_CODE   	VARCHAR(50),
 LV2_CODE   	VARCHAR(50),
 LV3_CODE   	VARCHAR(50),
 LV4_CODE   	VARCHAR(50),	
 LV0_CN_NAME	VARCHAR(200),
 LV1_CN_NAME   	VARCHAR(200),
 LV2_CN_NAME   	VARCHAR(200),
 LV3_CN_NAME   	VARCHAR(200),
 LV4_CN_NAME   	VARCHAR(200), 
 DIMENSION_CODE	VARCHAR(50),
 DIMENSION_CN_NAME	VARCHAR(200),
 DIMENSION_SUBCATEGORY_CODE	VARCHAR(50),
 DIMENSION_SUBCATEGORY_CN_NAME	VARCHAR(200),
 DIMENSION_SUB_DETAIL_CODE	VARCHAR(50),
 DIMENSION_SUB_DETAIL_CN_NAME	VARCHAR(200),
 VIEW_FLAG			VARCHAR(20),
 PERIOD_YEAR		INT,
 PERIOD_ID			INT,
 SPART_CODE			VARCHAR(50)  ,
 SPART_CN_NAME		VARCHAR(200) , 
 RMB_COST_AMT			NUMERIC,
 ACTUAL_QTY				NUMERIC,
 REGION_CODE		VARCHAR(50)  ,
 REGION_CN_NAME		VARCHAR(200) , 
 REPOFFICE_CODE		VARCHAR(50)  ,
 REPOFFICE_CN_NAME	VARCHAR(200) , 
 BG_CODE			VARCHAR(50)  ,
 BG_CN_NAME         VARCHAR(200) , 
 OVERSEA_FLAG		VARCHAR(10),
 MAIN_FLAG			VARCHAR(1),
 CODE_ATTRIBUTES    VARCHAR(20)     --是否主力编码 
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE,OVERSEA_FLAG);
 
 
--年均本表插数
V_SQL:='
  INSERT INTO DM_ANNL_AVG_TEMP
    (PERIOD_YEAR,
     LV0_CODE,	
	 LV1_CODE,   
	 LV2_CODE,   
	 LV3_CODE,   
	 LV4_CODE,   
	 LV0_CN_NAME,
	 LV1_CN_NAME,
	 LV2_CN_NAME,
	 LV3_CN_NAME,
	 LV4_CN_NAME,
	 '||V_DIMENSION_PART||' 
	 SPART_CODE,
     SPART_CN_NAME,
     RMB_COST_AMT,
     '||V_OTHER_DIM_PART||' 
	 MAIN_FLAG,
     CODE_ATTRIBUTES,
     VIEW_FLAG)
    SELECT T1.PERIOD_YEAR,
           '||V_PBI_PART||V_DIMENSION_PART||' 
		   T1.SPART_CODE,
           T1.SPART_CN_NAME,
           '||V_RMB_COST_AMT||' 
		   '||V_SQL_OTHER_DIM_PART||' 
		   T1.MAIN_FLAG,
           T1.CODE_ATTRIBUTES,
           T1.VIEW_FLAG
      FROM '||V_FROM_AMT_TABLE||' T1
     WHERE DECODE('''||V_PARENT_LEVEL||''',
                  ''LV0'','||V_LV0_CODE||',
                  ''LV1'','||V_LV1_CODE||',
                  ''LV2'','||V_LV2_CODE||',
                  ''LV3'','||V_LV3_CODE||',
				  ''LV4'','||V_LV4_CODE||') IN ('||V_IN_LV_CODE||')
		AND T1.APPEND_FLAG = ''N''
       AND EXISTS (SELECT 1
              FROM '||V_FROM_DIM_TABLE||' D
             WHERE D.MAIN_FLAG = T1.MAIN_FLAG
               AND NVL(D.CODE_ATTRIBUTES, ''CA'') =
                   NVL(T1.CODE_ATTRIBUTES, ''CA'')
				   '||V_VIEW_SQL||'		--视角1关联SPART 视角2关联量纲
               AND D.REGION_CODE = T1.REGION_CODE
               AND D.REPOFFICE_CODE = T1.REPOFFICE_CODE
               AND D.BG_CODE = T1.BG_CODE
               AND D.OVERSEA_FLAG = T1.OVERSEA_FLAG
               AND D.VIEW_FLAG = T1.VIEW_FLAG
               AND CUSTOM_ID = '||F_CUSTOM_ID||') 

';
DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE V_SQL;
 
 
DROP TABLE IF EXISTS DM_BASE_WEIGHT_T;
CREATE TEMPORARY TABLE DM_BASE_WEIGHT_T(
 LV4_CODE		VARCHAR(50),	
 DIMENSION_CODE	VARCHAR(50),
 DIMENSION_CN_NAME	VARCHAR(200),
 DIMENSION_SUBCATEGORY_CODE	VARCHAR(50),
 DIMENSION_SUBCATEGORY_CN_NAME	VARCHAR(200),
 DIMENSION_SUB_DETAIL_CODE	VARCHAR(50),
 DIMENSION_SUB_DETAIL_CN_NAME	VARCHAR(200),
 VIEW_FLAG			VARCHAR(20),
 PERIOD_YEAR		VARCHAR(20),
 WEIGHT_RATE		NUMERIC,
 SUB_DETAIL_AMT		NUMERIC,
 GROUP_LEVEL		VARCHAR(50)  ,
 PARENT_LEVEL		VARCHAR(100)  ,
 GROUP_CODE			VARCHAR(50)  ,
 GROUP_CN_NAME		VARCHAR(200) , 
 PARENT_CODE		VARCHAR(500)  ,	--字段长度修改 20240729
 PARENT_CN_NAME		VARCHAR(200) ,
 REGION_CODE		VARCHAR(50)  ,
 REGION_CN_NAME		VARCHAR(200) , 
 REPOFFICE_CODE		VARCHAR(50)  ,
 REPOFFICE_CN_NAME	VARCHAR(200) , 
 BG_CODE			VARCHAR(50)  ,
 BG_CN_NAME         VARCHAR(200) , 
 OVERSEA_FLAG		VARCHAR(10),
 MAIN_FLAG			VARCHAR(1),		--是否主力编码 
 CODE_ATTRIBUTES    VARCHAR(20)     
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(GROUP_CODE,OVERSEA_FLAG);
 
IF V_VIEW_FLAG = 'PROD_SPART' THEN
--计算权重
--由金额表取值
RAISE NOTICE '权重计算';
V_SQL:='
  WITH BASE_AMT AS
   (
    --取入参ID的SPART数据范围
	SELECT T1.PERIOD_YEAR,
		   T1.SPART_CODE AS GROUP_CODE,
		   T1.SPART_CN_NAME AS GROUP_CN_NAME,
		   '||V_SQL_OTHER_DIM_PART||' 
		   T1.LV4_CODE,
		   T1.MAIN_FLAG,
		   T1.CODE_ATTRIBUTES,
		   SUM(RMB_COST_AMT) AS RMB_COST_AMT
	  FROM DM_ANNL_AVG_TEMP T1
	 INNER JOIN (SELECT '||V_SQL_OTHER_DIM_PART||' 
						'||V_LV4_CODE||' AS LV4_CODE,
						T1.MAIN_FLAG,
						T1.CODE_ATTRIBUTES
				   FROM '||V_FROM_TOP_DIM_TABLE||' T1
				  WHERE T1.VERSION_ID = '||V_VERSION||'
					AND T1.IS_TOP_FLAG = ''Y''
					AND T1.DOUBLE_FLAG = ''Y''
					AND T1.TOP_SPART_CODE = '''||V_GROUP_CODE||''') T2
		ON T1.LV4_CODE = T2.LV4_CODE
	   AND T1.MAIN_FLAG = T2.MAIN_FLAG
	   AND NVL(T1.CODE_ATTRIBUTES, ''CA'') =NVL(T2.CODE_ATTRIBUTES, ''CA'')
	 '||V_JOIN_OTHER_DIM_PART||'
	 GROUP BY T1.PERIOD_YEAR,
			  T1.LV4_CODE,
			  T1.MAIN_FLAG,
			  T1.CODE_ATTRIBUTES,
			  '||V_SQL_OTHER_DIM_PART||' 
			  T1.SPART_CODE,
			  T1.SPART_CN_NAME) 
  INSERT INTO DM_BASE_WEIGHT_T
    (PERIOD_YEAR,
     GROUP_CODE,
     GROUP_CN_NAME,
     LV4_CODE,
	 MAIN_FLAG,
	 CODE_ATTRIBUTES,
     PARENT_LEVEL,
     '||V_OTHER_DIM_PART||' 
     WEIGHT_RATE)
  --T-3到T-2权重       
  SELECT '''||V_YEAR3||''' AS PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         LV4_CODE,
		 MAIN_FLAG,
		 CODE_ATTRIBUTES,
         ''LV4'' AS PARENT_LEVEL,
         '||V_OTHER_DIM_PART||'
		 SUM(RMB_COST_AMT) / NULLIF(SUM(SUM(RMB_COST_AMT)) OVER(), 0) AS WEIGHT_RATE
    FROM BASE_AMT
   WHERE PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE) - 3 AND
         YEAR(CURRENT_DATE) - 2
   GROUP BY LV4_CODE,
            '||V_OTHER_DIM_PART||' 
			GROUP_CODE,
            GROUP_CN_NAME,
			MAIN_FLAG,
			CODE_ATTRIBUTES
  UNION ALL
  --T-2到T-1权重
  SELECT '''||V_YEAR2||''' AS PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         LV4_CODE,
		 MAIN_FLAG,
		 CODE_ATTRIBUTES,
         ''LV4'' AS PARENT_LEVEL,
         '||V_OTHER_DIM_PART||'
		 SUM(RMB_COST_AMT) / NULLIF(SUM(SUM(RMB_COST_AMT)) OVER(), 0) AS WEIGHT_RATE
    FROM BASE_AMT
   WHERE PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE) - 2 AND
   YEAR(CURRENT_DATE) - 1
   GROUP BY LV4_CODE,
            '||V_OTHER_DIM_PART||'
			GROUP_CODE,
            GROUP_CN_NAME,
			MAIN_FLAG,
			CODE_ATTRIBUTES
  UNION ALL
  --T-1到T权重
  SELECT '''||V_YEAR1||''' AS PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         LV4_CODE,
		 MAIN_FLAG,
		 CODE_ATTRIBUTES,
         ''LV4'' AS PARENT_LEVEL,
         '||V_OTHER_DIM_PART||'
		 SUM(RMB_COST_AMT) / NULLIF(SUM(SUM(RMB_COST_AMT)) OVER(), 0) AS WEIGHT_RATE
    FROM BASE_AMT
   WHERE PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE) - 1 AND YEAR(CURRENT_DATE)
   GROUP BY LV4_CODE,
            '||V_OTHER_DIM_PART||'
			GROUP_CODE,
            GROUP_CN_NAME,
			MAIN_FLAG,
			CODE_ATTRIBUTES;	';
			
DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE V_SQL;

--权重表插数
RAISE NOTICE '权重删数';
V_SQL:='DELETE FROM '||V_TO_WEIGHT_TABLE||' WHERE CUSTOM_ID = '||F_CUSTOM_ID||';';
EXECUTE V_SQL;

RAISE NOTICE '权重插数';
V_SQL :='
  INSERT INTO '||V_TO_WEIGHT_TABLE||'
    (VERSION_ID,
     CUSTOM_ID,
     CUSTOM_CN_NAME,
     PERIOD_YEAR,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     PARENT_LEVEL,
     WEIGHT_RATE,
     PARENT_CODE,
     PARENT_CN_NAME,
     MAIN_FLAG,
     VIEW_FLAG,
     '||V_OTHER_DIM_PART||'
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     GRANULARITY_TYPE,
     CODE_ATTRIBUTES,
     LV4_CODE)
    SELECT '||V_VERSION||' AS VERSION_ID,
	       '||F_CUSTOM_ID||' AS CUSTOM_ID,
	       '||V_CUSTOM_CN_NAME||' AS CUSTOM_CN_NAME,
	       PERIOD_YEAR,
	       GROUP_CODE,
	       GROUP_CN_NAME,
	       ''SPART'' AS GROUP_LEVEL,
	       '''||V_PARENT_LEVEL||''' AS PARENT_LEVEL,
	       WEIGHT_RATE,
	       '||V_LV_CODE||' AS PARENT_CODE,
	       '''||V_LV_CN_NAME||''' AS PARENT_CN_NAME,
	       MAIN_FLAG,
	       ''PROD_SPART'' AS VIEW_FLAG,
	       '||V_OTHER_DIM_PART||'
	       -1 AS CREATED_BY,
	       CURRENT_TIMESTAMP AS CREATION_DATE,
	       -1 AS LAST_UPDATED_BY,
	       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
	       ''N'' AS DEL_FLAG,
	       '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
	       CODE_ATTRIBUTES,
		   LV4_CODE
	  FROM DM_BASE_WEIGHT_T;';
	  
EXECUTE V_SQL;

RAISE NOTICE '指数删数';
V_SQL:='DELETE FROM '||V_TO_COST_IDX_TABLE||' WHERE CUSTOM_ID = '||F_CUSTOM_ID||';';
EXECUTE V_SQL;		
RAISE NOTICE '指数计算';		
V_SQL:='
--由指数中间表取值
  WITH BASE_IDX AS
   (SELECT T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.GROUP_CODE,
           T1.GROUP_CN_NAME,
           '||V_SQL_OTHER_DIM_PART||' 
		   T1.LV4_CODE,
		   T1.MAIN_FLAG,
		   T1.CODE_ATTRIBUTES,
           T1.COST_INDEX
      FROM '||V_FROM_BASE_IDX_TABLE||' T1
     WHERE T1.VERSION_ID = '||V_VERSION||'
       AND T1.BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'
       AND T1.GROUP_LEVEL = ''SPART''
       AND DECODE('''||V_PARENT_LEVEL||''',
                  ''LV0'',LV0_CODE,
                  ''LV1'',LV1_CODE,
                  ''LV2'',LV2_CODE,
                  ''LV3'',LV3_CODE,
				  ''LV4'',LV4_CODE)IN ('||V_IN_LV_CODE||')
       AND EXISTS (SELECT 1
					  FROM '||V_FROM_DIM_TABLE||' D
					 WHERE D.MAIN_FLAG = T1.MAIN_FLAG
					   AND NVL(D.CODE_ATTRIBUTES, ''CA'') =
						   NVL(T1.CODE_ATTRIBUTES, ''CA'')
					   AND D.GROUP_CODE = T1.SPART_CODE
					   AND D.REGION_CODE = T1.REGION_CODE
					   AND D.REPOFFICE_CODE = T1.REPOFFICE_CODE
					   AND D.BG_CODE = T1.BG_CODE
					   AND D.OVERSEA_FLAG = T1.OVERSEA_FLAG
					   AND D.VIEW_FLAG = T1.VIEW_FLAG
					   AND CUSTOM_ID = '||F_CUSTOM_ID||'))
					   
  INSERT INTO '||V_TO_COST_IDX_TABLE||'
    (VERSION_ID,
     CUSTOM_ID,
     CUSTOM_CN_NAME,
     BASE_PERIOD_ID,
	 PERIOD_YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     PARENT_LEVEL,
     COST_INDEX,
     PARENT_CODE,
     PARENT_CN_NAME,
     MAIN_FLAG,
     VIEW_FLAG,
     '||V_OTHER_DIM_PART||'
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     GRANULARITY_TYPE,
     CODE_ATTRIBUTES)
  SELECT '||V_VERSION||' AS VERSION_ID,
         '||F_CUSTOM_ID||' AS CUSTOM_ID,
         '||V_CUSTOM_CN_NAME||' AS CUSTOM_CN_NAME,
		 '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
         LEFT(T1.PERIOD_ID, 4) AS PERIOD_YEAR,
         T1.PERIOD_ID,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         ''SPART'' AS GROUP_LEVEL,
         '''||V_PARENT_LEVEL||''' AS PARENT_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
         '||V_LV_CODE||' AS PARENT_CODE,
         '''||V_LV_CN_NAME||''' AS PARENT_CN_NAME,
         T1.MAIN_FLAG,
         ''PROD_SPART'' AS VIEW_FLAG,
         '||V_SQL_OTHER_DIM_PART||'
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
		 '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
         T1.CODE_ATTRIBUTES
    FROM BASE_IDX T1
    LEFT JOIN DM_BASE_WEIGHT_T T2
      ON T1.GROUP_CODE = T2.GROUP_CODE
     '||V_JOIN_OTHER_DIM_PART||'
     AND T2.MAIN_FLAG = T1.MAIN_FLAG
	 AND T1.LV4_CODE = T2.LV4_CODE
     AND T2.PERIOD_YEAR = '''||V_YEAR1||'''
     AND NVL(T2.CODE_ATTRIBUTES, ''CA'') =
         NVL(T1.CODE_ATTRIBUTES, ''CA'')
   GROUP BY T1.PERIOD_ID,
            T1.GROUP_CODE,
            T1.GROUP_CN_NAME,
			'||V_SQL_OTHER_DIM_PART||'
			T1.MAIN_FLAG,
			T1.CODE_ATTRIBUTES;';
			
DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE V_SQL;
			
IF F_GRANULARITY_TYPE = 'IRB' AND F_PAGE_TYPE = 'MONTH' THEN 

RAISE NOTICE '降成本指数删数';
V_SQL:='DELETE FROM '||V_TO_RED_IDX_TABLE||' WHERE CUSTOM_ID = '||F_CUSTOM_ID||';';
EXECUTE V_SQL;	

  RAISE NOTICE '降成本指数计算';	
V_SQL:='
--由降成本指数中间表取值
  WITH BASE_IDX AS
   (SELECT T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.GROUP_CODE,
           T1.GROUP_CN_NAME,
           '||V_SQL_OTHER_DIM_PART||' 
		   T1.LV4_PROD_RND_TEAM_CODE AS LV4_CODE,
		   T1.MAIN_FLAG,
		   T1.CODE_ATTRIBUTES,
           T1.COST_INDEX,
		   T1.YTD_COST_INDEX
      FROM '||V_FROM_BASE_RED_IDX_TABLE||' T1
     WHERE T1.GROUP_LEVEL = ''SPART''
	   AND DECODE('''||V_PARENT_LEVEL||''',
                  ''LV0'',T1.LV0_PROD_RND_TEAM_CODE,
                  ''LV1'',T1.LV1_PROD_RND_TEAM_CODE,
                  ''LV2'',T1.LV2_PROD_RND_TEAM_CODE,
                  ''LV3'',T1.LV3_PROD_RND_TEAM_CODE,
				  ''LV4'',T1.LV4_PROD_RND_TEAM_CODE) IN ('||V_IN_LV_CODE||')
       AND EXISTS (SELECT 1
					  FROM '||V_FROM_DIM_TABLE||' D
					 WHERE D.MAIN_FLAG = T1.MAIN_FLAG
					   AND NVL(D.CODE_ATTRIBUTES, ''CA'') =
						   NVL(T1.CODE_ATTRIBUTES, ''CA'')
					   AND D.GROUP_CODE = T1.SPART_CODE
					   AND D.REGION_CODE = T1.REGION_CODE
					   AND D.REPOFFICE_CODE = T1.REPOFFICE_CODE
					   AND D.BG_CODE = T1.BG_CODE
					   AND D.OVERSEA_FLAG = T1.OVERSEA_FLAG
					   AND D.VIEW_FLAG = T1.VIEW_FLAG
					   AND D.CUSTOM_ID = '||F_CUSTOM_ID||'))
					   
  INSERT INTO '||V_TO_RED_IDX_TABLE||'
    (VERSION_ID,
     CUSTOM_ID,
     CUSTOM_CN_NAME,
     PERIOD_YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     PARENT_LEVEL,
     COST_INDEX,
	 YTD_COST_INDEX,
     PARENT_CODE,
     PARENT_CN_NAME,
     MAIN_FLAG,
     VIEW_FLAG,
     '||V_OTHER_DIM_PART||'
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     CODE_ATTRIBUTES)
  SELECT '||V_VERSION||' AS VERSION_ID,
         '||F_CUSTOM_ID||' AS CUSTOM_ID,
         '||V_CUSTOM_CN_NAME||' AS CUSTOM_CN_NAME,
         LEFT(T1.PERIOD_ID, 4) AS PERIOD_YEAR,
         T1.PERIOD_ID,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         ''SPART'' AS GROUP_LEVEL,
         '''||V_PARENT_LEVEL||''' AS PARENT_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
		 SUM(T1.YTD_COST_INDEX * T2.WEIGHT_RATE) AS YTD_COST_INDEX,
         '||V_LV_CODE||' AS PARENT_CODE,
         '''||V_LV_CN_NAME||''' AS PARENT_CN_NAME,
         T1.MAIN_FLAG,
         ''PROD_SPART'' AS VIEW_FLAG,
         '||V_SQL_OTHER_DIM_PART||'
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         T1.CODE_ATTRIBUTES
    FROM BASE_IDX T1
    LEFT JOIN DM_BASE_WEIGHT_T T2
      ON T1.GROUP_CODE = T2.GROUP_CODE
     '||V_JOIN_OTHER_DIM_PART||'
     AND T2.MAIN_FLAG = T1.MAIN_FLAG
	 AND T1.LV4_CODE = T2.LV4_CODE
     AND TO_NUMBER(RIGHT(T2.PERIOD_YEAR,4)) = T1.PERIOD_YEAR
     AND NVL(T2.CODE_ATTRIBUTES, ''CA'') =
         NVL(T1.CODE_ATTRIBUTES, ''CA'')
   GROUP BY T1.PERIOD_ID,
            T1.GROUP_CODE,
            T1.GROUP_CN_NAME,
			'||V_SQL_OTHER_DIM_PART||'
			T1.MAIN_FLAG,
			T1.CODE_ATTRIBUTES;';
			
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE V_SQL;
  END IF ;
	
ELSIF V_VIEW_FLAG = 'DIMENSION' AND F_PAGE_TYPE = 'MONTH' THEN 
--由金额表取值

RAISE NOTICE '虚化量纲子类明细权重计算';
V_SQL:='
  WITH BASE_AMT AS
   (
    --取入参ID的量纲子类明细数据范围
    SELECT T1.PERIOD_YEAR,
           '||V_SQL_DIMENSION_PART||'
		   T1.DIMENSION_SUB_DETAIL_CODE AS GROUP_CODE,
           T1.DIMENSION_SUB_DETAIL_CN_NAME AS GROUP_CN_NAME,
		   DECODE('''||V_GROUP_LEVEL||''',''SUB_DETAIL'','''||V_PARENT_LEVEL||''',''SUB_DETAIL'') AS PARENT_LEVEL,
		   DECODE('''||V_GROUP_LEVEL||''',''SUB_DETAIL'','||V_LV_CODE||',
		   								''SUBCATEGORY'',T1.DIMENSION_SUBCATEGORY_CODE,
		   								''DIMENSION'',T1.DIMENSION_CODE) AS PARENT_CODE,
		   DECODE('''||V_GROUP_LEVEL||''',''SUB_DETAIL'','''||V_LV_CN_NAME||''',
		   								''SUBCATEGORY'',T1.DIMENSION_SUBCATEGORY_CN_NAME,
		   								''DIMENSION'',T1.DIMENSION_CN_NAME) AS PARENT_CN_NAME,
           '||V_SQL_OTHER_DIM_PART||' 
		   T1.LV4_CODE,
           SUM(RMB_COST_AMT) AS RMB_COST_AMT
      FROM DM_ANNL_AVG_TEMP T1
     GROUP BY T1.PERIOD_YEAR,
              '||V_SQL_DIMENSION_PART||V_SQL_OTHER_DIM_PART||'
			  T1.LV4_CODE
			   ) 
  INSERT INTO DM_BASE_WEIGHT_T
    (PERIOD_YEAR,
	 GROUP_CODE,
     GROUP_CN_NAME,
     LV4_CODE,
     PARENT_LEVEL,
	 PARENT_CODE,
	 PARENT_CN_NAME,
	 '||V_DIMENSION_PART||V_OTHER_DIM_PART||'
	 SUB_DETAIL_AMT,
     WEIGHT_RATE)
  --量纲子类明细 T-3到T-2权重       
  SELECT '''||V_YEAR3||''' AS PERIOD_YEAR,
		 GROUP_CODE,
         GROUP_CN_NAME,
         LV4_CODE,
         PARENT_LEVEL,
		 PARENT_CODE,
		 PARENT_CN_NAME,
         '||V_DIMENSION_PART||V_OTHER_DIM_PART||'
		 SUM(RMB_COST_AMT) AS SUB_DETAIL_AMT,
		 SUM(RMB_COST_AMT) / NULLIF(SUM(SUM(RMB_COST_AMT)) OVER(PARTITION BY '||V_OTHER_DIM_PART||'GROUP_CODE), 0) AS WEIGHT_RATE
    FROM BASE_AMT
   WHERE PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE) - 3 AND
         YEAR(CURRENT_DATE) - 2
   GROUP BY LV4_CODE,
            '||V_DIMENSION_PART||V_OTHER_DIM_PART||' 
			GROUP_CODE,
            GROUP_CN_NAME,
			PARENT_LEVEL,
			PARENT_CODE,
			PARENT_CN_NAME
  UNION ALL
  --T-2到T-1权重
  SELECT '''||V_YEAR2||''' AS PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         LV4_CODE,
         PARENT_LEVEL,
		 PARENT_CODE,
		 PARENT_CN_NAME,
         '||V_DIMENSION_PART||V_OTHER_DIM_PART||'
		 SUM(RMB_COST_AMT) AS SUB_DETAIL_AMT,
		 SUM(RMB_COST_AMT) / NULLIF(SUM(SUM(RMB_COST_AMT)) OVER(PARTITION BY '||V_OTHER_DIM_PART||'GROUP_CODE), 0) AS WEIGHT_RATE
    FROM BASE_AMT
   WHERE PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE) - 2 AND
   YEAR(CURRENT_DATE) - 1
   GROUP BY LV4_CODE,
            '||V_DIMENSION_PART||V_OTHER_DIM_PART||'
			GROUP_CODE,
            GROUP_CN_NAME,
			PARENT_LEVEL,
			PARENT_CODE,
			PARENT_CN_NAME
  UNION ALL
  --T-1到T权重
  SELECT '''||V_YEAR1||''' AS PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         LV4_CODE,
         PARENT_LEVEL,
		 PARENT_CODE,
		 PARENT_CN_NAME,
         '||V_DIMENSION_PART||V_OTHER_DIM_PART||'
		 SUM(RMB_COST_AMT) AS SUB_DETAIL_AMT,
		 SUM(RMB_COST_AMT) / NULLIF(SUM(SUM(RMB_COST_AMT)) OVER(PARTITION BY '||V_OTHER_DIM_PART||'GROUP_CODE), 0) AS WEIGHT_RATE
    FROM BASE_AMT
   WHERE PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE) - 1 AND YEAR(CURRENT_DATE)
   GROUP BY LV4_CODE,
            '||V_DIMENSION_PART||V_OTHER_DIM_PART||'
			GROUP_CODE,
            GROUP_CN_NAME,
			PARENT_LEVEL,
			PARENT_CODE,
			PARENT_CN_NAME;';
		
DBMS_OUTPUT.PUT_LINE(V_SQL);		
EXECUTE V_SQL;

--权重表插数

RAISE NOTICE '虚化量纲子类明细权重删数';
V_SQL:='DELETE FROM '||V_TO_WEIGHT_TABLE||' WHERE CUSTOM_ID = '||F_CUSTOM_ID||';';
EXECUTE V_SQL;	

RAISE NOTICE '权重插数';
V_SQL :='
  INSERT INTO '||V_TO_WEIGHT_TABLE||'
    (VERSION_ID,
     CUSTOM_ID,
     CUSTOM_CN_NAME,
     PERIOD_YEAR,
	 '||V_DIMENSION_PART||'
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     PARENT_LEVEL,
     WEIGHT_RATE,
     PARENT_CODE,
     PARENT_CN_NAME,
     MAIN_FLAG,
     VIEW_FLAG,
     '||V_OTHER_DIM_PART||'
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     GRANULARITY_TYPE,
     LV4_CODE)
    SELECT '||V_VERSION||' AS VERSION_ID,
	       '||F_CUSTOM_ID||' AS CUSTOM_ID,
	       '||V_CUSTOM_CN_NAME||' AS CUSTOM_CN_NAME,
	       PERIOD_YEAR,
		   '||V_DIMENSION_PART||'
	       GROUP_CODE,
	       GROUP_CN_NAME,
	       ''SUB_DETAIL'' AS GROUP_LEVEL,
	       PARENT_LEVEL,
	       WEIGHT_RATE,
	       PARENT_CODE,
	       PARENT_CN_NAME,
	       ''N'' AS MAIN_FLAG,
	       ''DIMENSION'' AS VIEW_FLAG,
	       '||V_OTHER_DIM_PART||'
	       -1 AS CREATED_BY,
	       CURRENT_TIMESTAMP AS CREATION_DATE,
	       -1 AS LAST_UPDATED_BY,
	       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
	       ''N'' AS DEL_FLAG,
	       '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
		   LV4_CODE
	  FROM DM_BASE_WEIGHT_T;';
	
DBMS_OUTPUT.PUT_LINE(V_SQL);	
EXECUTE V_SQL;

	
RAISE NOTICE '虚化量纲子类明细指数删数';
V_SQL:='DELETE FROM '||V_TO_COST_IDX_TABLE||' WHERE CUSTOM_ID = '||F_CUSTOM_ID||';';
EXECUTE V_SQL;	
	
RAISE NOTICE '虚化量纲子类明细指数';	
V_SQL:='
--由指数中间表取值
  WITH BASE_IDX AS
   (SELECT T1.PERIOD_YEAR,
           T1.PERIOD_ID,
		   '||V_SQL_DIMENSION_PART||'
           T1.GROUP_CODE,
           T1.GROUP_CN_NAME,
		   DECODE('''||V_GROUP_LEVEL||''',''SUB_DETAIL'','||V_LV_CODE||',
									''SUBCATEGORY'',T1.DIMENSION_SUBCATEGORY_CODE,
									''DIMENSION'',T1.DIMENSION_CODE) AS PARENT_CODE,
		   DECODE('''||V_GROUP_LEVEL||''',''SUB_DETAIL'','''||V_LV_CN_NAME||''',
									''SUBCATEGORY'',T1.DIMENSION_SUBCATEGORY_CN_NAME,
									''DIMENSION'',T1.DIMENSION_CN_NAME) AS PARENT_CN_NAME,
		   
           '||V_SQL_OTHER_DIM_PART||' 
		   T1.LV4_CODE,
		   T1.MAIN_FLAG,
           T1.COST_INDEX
      FROM '||V_FROM_BASE_IDX_TABLE||' T1
     WHERE T1.BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'
       AND T1.GROUP_LEVEL = ''SUB_DETAIL''
       AND DECODE('''||V_PARENT_LEVEL||''',
                  ''LV0'',LV0_CODE,
                  ''LV1'',LV1_CODE,
                  ''LV2'',LV2_CODE,
                  ''LV3'',LV3_CODE,
				  ''LV4'',LV4_CODE) IN ('||V_IN_LV_CODE||')
       AND EXISTS (SELECT 1
					  FROM '||V_FROM_DIM_TABLE||' D
					 WHERE D.MAIN_FLAG = T1.MAIN_FLAG
					   AND NVL(D.CODE_ATTRIBUTES, ''CA'') =
						   NVL(T1.CODE_ATTRIBUTES, ''CA'')
					 --  AND D.GROUP_CODE = T1.DIMENSION_SUB_DETAIL_CODE
					   AND D.REGION_CODE = T1.REGION_CODE
					   AND D.REPOFFICE_CODE = T1.REPOFFICE_CODE
					   AND D.BG_CODE = T1.BG_CODE
					   AND D.OVERSEA_FLAG = T1.OVERSEA_FLAG
					   '||V_VIEW_SQL||'
					   AND D.VIEW_FLAG = T1.VIEW_FLAG
					   AND CUSTOM_ID = '||F_CUSTOM_ID||'))
					   
  INSERT INTO '||V_TO_COST_IDX_TABLE||'
    (VERSION_ID,
     CUSTOM_ID,
     CUSTOM_CN_NAME,
     BASE_PERIOD_ID,
	 PERIOD_YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     PARENT_LEVEL,
     COST_INDEX,
     PARENT_CODE,
     PARENT_CN_NAME,
     MAIN_FLAG,
     VIEW_FLAG,
     '||V_OTHER_DIM_PART||'
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     GRANULARITY_TYPE)
  SELECT '||V_VERSION||' AS VERSION_ID,
         '||F_CUSTOM_ID||' AS CUSTOM_ID,
         '||V_CUSTOM_CN_NAME||' AS CUSTOM_CN_NAME,
		 '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
         LEFT(T1.PERIOD_ID, 4) AS PERIOD_YEAR,
         T1.PERIOD_ID,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         ''SUB_DETAIL'' AS GROUP_LEVEL,
         DECODE('''||V_GROUP_LEVEL||''',''SUB_DETAIL'','''||V_PARENT_LEVEL||''',
									'''||V_GROUP_LEVEL||''') AS PARENT_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
		 T1.PARENT_CODE,
		 T1.PARENT_CN_NAME,
         ''N'' AS MAIN_FLAG,
         ''DIMENSION'' AS VIEW_FLAG,
         '||V_SQL_OTHER_DIM_PART||'
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
		 '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
    FROM BASE_IDX T1
    LEFT JOIN DM_BASE_WEIGHT_T T2
      ON T1.GROUP_CODE = T2.GROUP_CODE
     '||V_JOIN_OTHER_DIM_PART||'
	 '||V_JOIN_DIMENSION_CODE||'
	 AND T1.LV4_CODE = T2.LV4_CODE
     AND T2.PERIOD_YEAR = '''||V_YEAR1||'''
   GROUP BY T1.PERIOD_ID,
			'||V_SQL_OTHER_DIM_PART||'
			T1.PARENT_CODE,
			T1.PARENT_CN_NAME,
			T1.GROUP_CODE,
            T1.GROUP_CN_NAME;';
DBMS_OUTPUT.PUT_LINE(V_SQL);	
EXECUTE V_SQL;
			
 IF F_GRANULARITY_TYPE = 'IRB' THEN 
 
 RAISE NOTICE '虚化量纲子类明细降成本指数删数';
V_SQL:='DELETE FROM '||V_TO_RED_IDX_TABLE||' WHERE CUSTOM_ID = '||F_CUSTOM_ID||';';
EXECUTE V_SQL;	
 
 
  RAISE NOTICE '虚化量纲子类明细降成本指数';
V_SQL:='
--由降成本指数中间表取值
  WITH BASE_IDX AS
   (SELECT T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.GROUP_CODE,
           T1.GROUP_CN_NAME,
           '||V_SQL_DIMENSION_PART||V_SQL_OTHER_DIM_PART||' 
		   T1.LV4_PROD_RND_TEAM_CODE AS LV4_CODE,
		   DECODE('''||V_GROUP_LEVEL||''',''SUB_DETAIL'','||V_LV_CODE||',
										''SUBCATEGORY'',T1.DIMENSION_SUBCATEGORY_CODE,
										''DIMENSION'',T1.DIMENSION_CODE) AS PARENT_CODE,
			DECODE('''||V_GROUP_LEVEL||''',''SUB_DETAIL'','''||V_LV_CN_NAME||''',
										''SUBCATEGORY'',T1.DIMENSION_SUBCATEGORY_CN_NAME,
										''DIMENSION'',T1.DIMENSION_CN_NAME) AS PARENT_CN_NAME,
		   T1.MAIN_FLAG,
           T1.COST_INDEX,
		   T1.YTD_COST_INDEX
      FROM '||V_FROM_BASE_RED_IDX_TABLE||' T1
     WHERE T1.GROUP_LEVEL = ''SUB_DETAIL''
	   AND DECODE('''||V_PARENT_LEVEL||''',
                  ''LV0'',LV0_PROD_RND_TEAM_CODE,
                  ''LV1'',LV1_PROD_RND_TEAM_CODE,
                  ''LV2'',LV2_PROD_RND_TEAM_CODE,
                  ''LV3'',LV3_PROD_RND_TEAM_CODE,
				  ''LV4'',LV4_PROD_RND_TEAM_CODE) IN ('||V_IN_LV_CODE||')
       AND EXISTS (SELECT 1
					  FROM '||V_FROM_DIM_TABLE||' D
					 WHERE D.MAIN_FLAG = T1.MAIN_FLAG
					   AND NVL(D.CODE_ATTRIBUTES, ''CA'') =
						   NVL(T1.CODE_ATTRIBUTES, ''CA'')
					  -- AND D.GROUP_CODE = T1.DIMENSION_SUB_DETAIL_CODE
					   '||V_VIEW_SQL||'
					   AND D.REGION_CODE = T1.REGION_CODE
					   AND D.REPOFFICE_CODE = T1.REPOFFICE_CODE
					   AND D.BG_CODE = T1.BG_CODE
					   AND D.OVERSEA_FLAG = T1.OVERSEA_FLAG
					   AND D.VIEW_FLAG = T1.VIEW_FLAG
					   AND CUSTOM_ID = '||F_CUSTOM_ID||'))
					   
  INSERT INTO '||V_TO_RED_IDX_TABLE||'
    (VERSION_ID,
     CUSTOM_ID,
     CUSTOM_CN_NAME,
     PERIOD_YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     PARENT_LEVEL,
     COST_INDEX,
	 YTD_COST_INDEX,
     PARENT_CODE,
     PARENT_CN_NAME,
     MAIN_FLAG,
     VIEW_FLAG,
     '||V_OTHER_DIM_PART||'
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG)
  SELECT '||V_VERSION||' AS VERSION_ID,
         '||F_CUSTOM_ID||' AS CUSTOM_ID,
         '||V_CUSTOM_CN_NAME||' AS CUSTOM_CN_NAME,
         LEFT(T1.PERIOD_ID, 4) AS PERIOD_YEAR,
         T1.PERIOD_ID,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         ''SUB_DETAIL'' AS GROUP_LEVEL,
		 DECODE('''||V_GROUP_LEVEL||''',''SUB_DETAIL'','''||V_PARENT_LEVEL||''',
									'''||V_GROUP_LEVEL||''') AS PARENT_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
		 SUM(T1.YTD_COST_INDEX * T2.WEIGHT_RATE) AS YTD_COST_INDEX,
		 T1.PARENT_CODE,
		 T1.PARENT_CN_NAME,
         ''N'' AS MAIN_FLAG,
         ''DIMENSION'' AS VIEW_FLAG,
         '||V_SQL_OTHER_DIM_PART||'
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
    FROM BASE_IDX T1
    LEFT JOIN DM_BASE_WEIGHT_T T2
      ON T1.GROUP_CODE = T2.GROUP_CODE
     '||V_JOIN_OTHER_DIM_PART||'
	 '||V_JOIN_DIMENSION_CODE||'
     AND TO_NUMBER(RIGHT(T2.PERIOD_YEAR,4)) = T1.PERIOD_YEAR
	 AND T1.LV4_CODE = T2.LV4_CODE
   GROUP BY T1.PERIOD_ID,
            T1.GROUP_CODE,
            T1.GROUP_CN_NAME,
			'||V_SQL_OTHER_DIM_PART||'
			T1.PARENT_CODE,
			T1.PARENT_CN_NAME';
			
DBMS_OUTPUT.PUT_LINE(V_SQL);	
EXECUTE V_SQL;
  
END IF ;
			
IF V_GROUP_LEVEL <> 'SUB_DETAIL' THEN 
RAISE NOTICE '计算量纲子类明细/量纲子类或量纲权重';
--计算量纲子类明细/量纲子类或量纲
V_SQL:='
  INSERT INTO DM_BASE_WEIGHT_T
    (PERIOD_YEAR,
	 GROUP_CODE,
     GROUP_CN_NAME,
     PARENT_CODE,
	 PARENT_CN_NAME,
     PARENT_LEVEL,
     '||V_OTHER_DIM_PART||' 
     WEIGHT_RATE)
  --量纲子类或量纲 分别三个区间年权重    
  SELECT PERIOD_YEAR,
		 GROUP_CODE,
         GROUP_CN_NAME,
		 PARENT_CODE,
		 PARENT_CN_NAME,
         '''||V_GROUP_LEVEL||''' AS PARENT_LEVEL,
		 '||V_OTHER_DIM_PART||'
		 SUM(SUB_DETAIL_AMT)/
		 NULLIF(SUM(SUM(SUB_DETAIL_AMT))OVER(PARTITION BY PERIOD_YEAR,'||V_OTHER_DIM_PART||'PARENT_CODE )
		 ,0) AS WEIGHT_RATE
    FROM DM_BASE_WEIGHT_T
GROUP BY PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
		 '||V_OTHER_DIM_PART||'
		 PARENT_CODE,
		 PARENT_CN_NAME
		 ;';
									
DBMS_OUTPUT.PUT_LINE(V_SQL);	
EXECUTE V_SQL;
									

--权重插数
RAISE NOTICE '权重插数';
V_SQL:='
  INSERT INTO '||V_TO_WEIGHT_TABLE||'
    (VERSION_ID,
     CUSTOM_ID,
     CUSTOM_CN_NAME,
     PERIOD_YEAR,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     PARENT_LEVEL,
     WEIGHT_RATE,
     PARENT_CODE,
     PARENT_CN_NAME,
     MAIN_FLAG,
     VIEW_FLAG,
     '||V_OTHER_DIM_PART||'
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     GRANULARITY_TYPE)
    SELECT '||V_VERSION||' AS VERSION_ID,
	       '||F_CUSTOM_ID||' AS CUSTOM_ID,
	       '||V_CUSTOM_CN_NAME||' AS CUSTOM_CN_NAME,
	       PERIOD_YEAR,
	       GROUP_CODE,
	       GROUP_CN_NAME,
	       ''SUB_DETAIL'' AS GROUP_LEVEL,
	       '''||V_GROUP_LEVEL||''' AS PARENT_LEVEL,
	       WEIGHT_RATE,
	       PARENT_CODE,
	       PARENT_CN_NAME,
	       ''N'' AS MAIN_FLAG,
	       ''DIMENSION'' AS VIEW_FLAG,
	       '||V_OTHER_DIM_PART||'
	       -1 AS CREATED_BY,
	       CURRENT_TIMESTAMP AS CREATION_DATE,
	       -1 AS LAST_UPDATED_BY,
	       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
	       ''N'' AS DEL_FLAG,
	       '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
	  FROM DM_BASE_WEIGHT_T
	  WHERE PARENT_LEVEL = '''||V_GROUP_LEVEL||''' ;';
	  
EXECUTE V_SQL;
									

--指数计算
RAISE NOTICE '量纲及量纲子类指数计算';
V_SQL:='									
  WITH BASE_IDX AS (
    SELECT PERIOD_ID,GROUP_CODE,GROUP_CN_NAME,COST_INDEX,'||V_OTHER_DIM_PART||' PARENT_CODE,PARENT_CN_NAME
	  FROM '||V_TO_COST_IDX_TABLE||'
	 WHERE PARENT_LEVEL = '''||V_GROUP_LEVEL||'''
	   AND BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'
	   AND CUSTOM_ID =  '||F_CUSTOM_ID||'
  ),
  BASE_WEIGHT AS (
    SELECT GROUP_CODE,GROUP_CN_NAME,WEIGHT_RATE,'||V_OTHER_DIM_PART||' PARENT_CODE,PARENT_CN_NAME
	  FROM DM_BASE_WEIGHT_T
	 WHERE PARENT_LEVEL = '''||V_GROUP_LEVEL||'''
	   AND PERIOD_YEAR = '''||V_YEAR1||'''
  )
    INSERT INTO '||V_TO_COST_IDX_TABLE||'
    (VERSION_ID,
     BASE_PERIOD_ID,
	 CUSTOM_ID,
     CUSTOM_CN_NAME,
     PERIOD_YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     PARENT_LEVEL,
     COST_INDEX,
     PARENT_CODE,
     PARENT_CN_NAME,
     MAIN_FLAG,
     VIEW_FLAG,
     '||V_OTHER_DIM_PART||'
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     GRANULARITY_TYPE)
  SELECT '||V_VERSION||' AS VERSION_ID,
		 '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
		 '||F_CUSTOM_ID||' AS CUSTOM_ID,
		 '||V_CUSTOM_CN_NAME||' AS CUSTOM_CN_NAME,
		 LEFT(T1.PERIOD_ID,4) AS PERIOD_YEAR,
		 T1.PERIOD_ID,
		 T1.PARENT_CODE AS GROUP_CODE,
		 T1.PARENT_CN_NAME AS GROUP_CN_NAME,
		 '''||V_GROUP_LEVEL||''' AS GROUP_LEVEL,
		 '''||V_PARENT_LEVEL||''' AS PARENT_LEVEL,
		 SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
		 '''||V_LV_CN_NAME||''' AS PARENT_CODE,
		 '||V_LV_CODE||' AS PARENT_CN_NAME,
		 ''N'' AS MAIN_FLAG,
		 ''DIMENSION'' AS VIEW_FLAG,
		 '||V_SQL_OTHER_DIM_PART||'
		 -1 AS CREATED_BY,
		 CURRENT_TIMESTAMP AS CREATION_DATE,
		 -1 AS LAST_UPDATED_BY,
		 CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
		 ''N'' AS DEL_FLAG,
		 '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
    FROM BASE_IDX T1
	LEFT JOIN BASE_WEIGHT T2
	  ON T1.GROUP_CODE = T2.GROUP_CODE
	 AND T1.PARENT_CODE = T2.PARENT_CODE
	 AND T2.REGION_CODE = T1.REGION_CODE
	 AND T2.REPOFFICE_CODE = T1.REPOFFICE_CODE
	 AND T2.BG_CODE = T1.BG_CODE
     AND T2.OVERSEA_FLAG = T1.OVERSEA_FLAG
   GROUP BY T1.PERIOD_ID,'||V_SQL_OTHER_DIM_PART||'
		    T1.PARENT_CODE,
			T1.PARENT_CN_NAME;';
			
DBMS_OUTPUT.PUT_LINE(V_SQL);	
EXECUTE V_SQL;
		
IF F_GRANULARITY_TYPE = 'IRB' THEN		
--降成本指数计算
RAISE NOTICE '量纲及量纲子类降成本指数';
V_SQL:='									
  WITH BASE_IDX AS (
  --取基础指数
    SELECT PERIOD_YEAR,PERIOD_ID,GROUP_CODE,GROUP_CN_NAME,COST_INDEX,YTD_COST_INDEX,'||V_OTHER_DIM_PART||' PARENT_CODE,PARENT_CN_NAME
	  FROM '||V_TO_RED_IDX_TABLE||'
	 WHERE PARENT_LEVEL = '''||V_GROUP_LEVEL||'''
	   AND CUSTOM_ID =  '||F_CUSTOM_ID||'
  ),
  BASE_WEIGHT AS (
  --取基础权重
    SELECT PERIOD_YEAR,GROUP_CODE,GROUP_CN_NAME,WEIGHT_RATE,'||V_OTHER_DIM_PART||' PARENT_CODE,PARENT_CN_NAME
	  FROM DM_BASE_WEIGHT_T
	 WHERE PARENT_LEVEL = '''||V_GROUP_LEVEL||'''
	   
  )
    INSERT INTO '||V_TO_RED_IDX_TABLE||'
    (VERSION_ID,
	 CUSTOM_ID,
     CUSTOM_CN_NAME,
     PERIOD_YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     PARENT_LEVEL,
     COST_INDEX,
     PARENT_CODE,
     PARENT_CN_NAME,
     MAIN_FLAG,
     VIEW_FLAG,
     '||V_OTHER_DIM_PART||'
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG)
  SELECT '||V_VERSION||' AS VERSION_ID,
		 '||F_CUSTOM_ID||' AS CUSTOM_ID,
		 '||V_CUSTOM_CN_NAME||' AS CUSTOM_CN_NAME,
		 T1.PERIOD_YEAR,
		 T1.PERIOD_ID,
		 T1.PARENT_CODE AS GROUP_CODE,
		 T1.PARENT_CN_NAME AS GROUP_CN_NAME,
		 '''||V_GROUP_LEVEL||''' AS GROUP_LEVEL,
		 '''||V_PARENT_LEVEL||''' AS PARENT_LEVEL,
		 SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
		 '''||V_LV_CN_NAME||''' AS PARENT_CODE,
		 '||V_LV_CODE||' AS PARENT_CN_NAME,
		 ''N'' AS MAIN_FLAG,
		 ''DIMENSION'' AS VIEW_FLAG,
		 '||V_SQL_OTHER_DIM_PART||'
		 -1 AS CREATED_BY,
		 CURRENT_TIMESTAMP AS CREATION_DATE,
		 -1 AS LAST_UPDATED_BY,
		 CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
		 ''N'' AS DEL_FLAG
    FROM BASE_IDX T1
	LEFT JOIN BASE_WEIGHT T2
	  ON T1.GROUP_CODE = T2.GROUP_CODE
	 AND T1.PARENT_CODE = T2.PARENT_CODE
	 AND T2.REGION_CODE = T1.REGION_CODE
	 AND T2.REPOFFICE_CODE = T1.REPOFFICE_CODE
	 AND T2.BG_CODE = T1.BG_CODE
	 AND TO_NUMBER(RIGHT(T2.PERIOD_YEAR,4)) = T1.PERIOD_YEAR
     AND T2.OVERSEA_FLAG = T1.OVERSEA_FLAG
   GROUP BY T1.PERIOD_ID,'||V_SQL_OTHER_DIM_PART||'
		    T1.PERIOD_YEAR,
			T1.PARENT_CODE,
			T1.PARENT_CN_NAME;';
			
DBMS_OUTPUT.PUT_LINE(V_SQL);	
EXECUTE V_SQL;
END IF;	--降成本指数层
END IF; --量纲及量纲子类层
END IF; 

IF F_PAGE_TYPE = 'MONTH' THEN 

--同环比计算
RAISE NOTICE '同环比删数';
V_SQL:='DELETE FROM '||V_TO_MONTH_RATE_TABLE||' WHERE CUSTOM_ID = '||F_CUSTOM_ID||';';
EXECUTE V_SQL;	

  V_SQL:='
  WITH LEV_INDEX AS
   (SELECT CUSTOM_ID,
           CUSTOM_CN_NAME,
		   PERIOD_ID,
		   SUBSTR(PERIOD_ID, 5, 2) AS MONTH_DAY,
		   GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           PARENT_CODE,
           PARENT_CN_NAME,
		   VIEW_FLAG,
           '||V_OTHER_DIM_PART||' 
		   MAIN_FLAG,
           CODE_ATTRIBUTES
      FROM '||V_TO_COST_IDX_TABLE||'
     WHERE VERSION_ID = '||V_VERSION||'
       AND CUSTOM_ID = '||F_CUSTOM_ID||'
	   AND GROUP_LEVEL = '''||V_GROUP_LEVEL||'''),
  
  BASE_YOY AS
   (SELECT PERIOD_ID,
           CUSTOM_ID,
		   CUSTOM_CN_NAME,
		   GROUP_CODE,
		   GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
		   VIEW_FLAG,
           '||V_OTHER_DIM_PART||' 
		   LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY '||V_OTHER_DIM_PART||' 
														GROUP_CODE, GROUP_LEVEL, PARENT_CODE, MAIN_FLAG,NVL(CODE_ATTRIBUTES,''CA''),
														VIEW_FLAG,MONTH_DAY ORDER BY PERIOD_ID) AS YOY_PERIOD_ID,
           LAG(COST_INDEX, 1, NULL) OVER(PARTITION BY '||V_OTHER_DIM_PART||' 
														GROUP_CODE, GROUP_LEVEL, PARENT_CODE, MAIN_FLAG,NVL(CODE_ATTRIBUTES,''CA''),
														VIEW_FLAG,MONTH_DAY ORDER BY PERIOD_ID) AS YOY_COST_INDEX,
           LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY '||V_OTHER_DIM_PART||' 
														GROUP_CODE, GROUP_LEVEL, PARENT_CODE, MAIN_FLAG, NVL(CODE_ATTRIBUTES,''CA''),
														VIEW_FLAG
														ORDER BY PERIOD_ID) AS POP_PERIOD_ID,
           LAG(COST_INDEX, 1, NULL) OVER(PARTITION BY '||V_OTHER_DIM_PART||' 
														GROUP_CODE, GROUP_LEVEL, PARENT_CODE, MAIN_FLAG, NVL(CODE_ATTRIBUTES,''CA''),
														VIEW_FLAG
														ORDER BY PERIOD_ID) AS POP_COST_INDEX,
           PARENT_CODE,
           PARENT_CN_NAME,
		   MAIN_FLAG,
           CODE_ATTRIBUTES
      FROM LEV_INDEX) 
  INSERT INTO '||V_TO_MONTH_RATE_TABLE||'
    (VERSION_ID,
     CUSTOM_ID,
	 CUSTOM_CN_NAME,
	 PERIOD_YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     RATE,
     RATE_FLAG,
	 PARENT_LEVEL,
     PARENT_CODE,
     PARENT_CN_NAME,
     MAIN_FLAG,
     CODE_ATTRIBUTES,
     VIEW_FLAG,
     '||V_OTHER_DIM_PART||' 
	 CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
	 GRANULARITY_TYPE
     )
  SELECT '||V_VERSION||' AS VERSION_ID,
         CUSTOM_ID,
		 CUSTOM_CN_NAME,
		 LEFT(PERIOD_ID, 4) AS PERIOD_YEAR,
         PERIOD_ID,
		 GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ((COST_INDEX / NULLIF(YOY_COST_INDEX, 0)) - 1) AS RATE,
		 ''YOY''AS RATE_FLAG,
		 '''||V_PARENT_LEVEL||''' AS PARENT_LEVEL,
         PARENT_CODE,
         PARENT_CN_NAME,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         VIEW_FLAG,
         '||V_OTHER_DIM_PART||' 
		 -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
		 ''N''AS DEL_FLAG,
		 '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
    FROM BASE_YOY
   WHERE YOY_COST_INDEX IS NOT NULL
  UNION ALL
  SELECT '||V_VERSION||' AS VERSION_ID,
         CUSTOM_ID,
		 CUSTOM_CN_NAME,
		 LEFT(PERIOD_ID, 4) AS PERIOD_YEAR,
         PERIOD_ID,
		 GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ((COST_INDEX / NULLIF(POP_COST_INDEX, 0)) - 1) AS RATE,
		 ''POP''AS RATE_FLAG,
		 '''||V_PARENT_LEVEL||''' AS PARENT_LEVEL,
         PARENT_CODE,
         PARENT_CN_NAME,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         VIEW_FLAG,
         '||V_OTHER_DIM_PART||' 
		 -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
		 ''N''AS DEL_FLAG,
		 '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
    FROM BASE_YOY
   WHERE POP_COST_INDEX IS NOT NULL ';
   
DBMS_OUTPUT.PUT_LINE(V_SQL);	
EXECUTE V_SQL;
END IF;

  RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
     PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_STEP_NUM => V_EXCEPTION_FLAG,
    F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

 END; $$
/

