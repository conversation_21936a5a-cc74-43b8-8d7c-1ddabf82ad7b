-- Name: f_dm_fom_month_item_sum_amt_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_month_item_sum_amt_t(f_caliber_flag character varying, f_keystr character varying DEFAULT NULL::character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间：2023-12-11
创建人  ：黄心蕊 HWX1187045
背景描述：月度分析-ITEM汇总金额表(规格品监控图) 
参数描述：参数一(F_CALIBER_FLAG)：'E'为EMS，'M'为自制
		  参数二(F_KEYSTR)：绝密数据解密密钥串
		  参数三(F_VERSION_ID)：运行版本号
		  参数四(X_SUCCESS_FLAG)：运行状态返回值 ‘1’为成功，‘0’为失败
来源表：FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T 制造单领域金额表
		FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T 规格品清单
目标表：FIN_DM_OPT_FOI.DM_FOM_MONTH_ITEM_SUM_AMT_T 规格品金额表
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOM_MONTH_ITEM_SUM_AMT_T('E','',''); --EMS一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOM_MONTH_ITEM_SUM_AMT_T('M','密钥串',''); --自制一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME      VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOM_MONTH_ITEM_SUM_AMT_T';
  V_VERSION      BIGINT; --版本号
  V_STEP_NUM     INT := 0; --函数步骤号
  V_KEYSTR       VARCHAR(100) := F_KEYSTR; --解密密钥串
  V_CALIBER_FLAG VARCHAR(2) := F_CALIBER_FLAG;	
  V_BEGIN_DATE TIMESTAMP := TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)-2)||'01','YYYYMM');  --三年前首月  

BEGIN 

X_SUCCESS_FLAG := '1';

--日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  V_STEP_NUM := V_STEP_NUM + 1;
  --版本号入参判断，当入参为空，取规格品清单最新版本号
  IF F_ITEM_VERSION IS NULL THEN
    SELECT VERSION_ID INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
    --入参不为空，则以入参为版本号
  ELSE
    V_VERSION := F_ITEM_VERSION;
  END IF;
  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
  
DELETE FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_ITEM_SUM_AMT_T WHERE VERSION_ID = V_VERSION AND CALIBER_FLAG = V_CALIBER_FLAG;

  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '删除'''||V_CALIBER_FLAG||''' 口径数据成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

  V_STEP_NUM := V_STEP_NUM + 1;
--1.数据解密落表
--1.1 建临时表，承载制造单领域金额
DROP TABLE IF EXISTS DM_DECRYP_AMT_TEMP;
CREATE TEMPORARY TABLE DM_DECRYP_AMT_TEMP(
	PERIOD_YEAR CHARACTER VARYING(50),
	PERIOD_ID CHARACTER VARYING(50),
	LV0_CODE CHARACTER VARYING(50),
	LV0_CN_NAME CHARACTER VARYING(200),
	LV1_CODE CHARACTER VARYING(50),
	LV1_CN_NAME CHARACTER VARYING(200),
	BUSSINESS_OBJECT_CODE CHARACTER VARYING(200),
	BUSSINESS_OBJECT_CN_NAME CHARACTER VARYING(200),
	SHIPPING_OBJECT_CODE CHARACTER VARYING(200),
	SHIPPING_OBJECT_CN_NAME CHARACTER VARYING(200),
	MANUFACTURE_OBJECT_CODE CHARACTER VARYING(200),
	MANUFACTURE_OBJECT_CN_NAME CHARACTER VARYING(200),
	ITEM_CODE CHARACTER VARYING(50),
	ITEM_CN_NAME CHARACTER VARYING(1000),
	CALIBER_FLAG CHARACTER VARYING(2),
	RMB_COST_AMT  NUMERIC
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY ROUNDROBIN;
  
--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '临时表创建成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
  IF V_CALIBER_FLAG = 'E' THEN
  
	--1.2 根据业务口径入参为'E'，将EMS金额落表，无需解密
	V_STEP_NUM := V_STEP_NUM + 1;
    INSERT INTO DM_DECRYP_AMT_TEMP
      (PERIOD_YEAR,
       PERIOD_ID,
       LV0_CODE,
       LV0_CN_NAME,
       LV1_CODE,
       LV1_CN_NAME,
       BUSSINESS_OBJECT_CODE,
       BUSSINESS_OBJECT_CN_NAME,
       SHIPPING_OBJECT_CODE,
       SHIPPING_OBJECT_CN_NAME,
       MANUFACTURE_OBJECT_CODE,
       MANUFACTURE_OBJECT_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       CALIBER_FLAG,
       RMB_COST_AMT)
      SELECT PERIOD_YEAR,
             PERIOD_ID,
             LV0_CODE,
             LV0_CN_NAME,
             LV1_CODE,
             LV1_CN_NAME,
             BUSSINESS_OBJECT_CODE,
             BUSSINESS_OBJECT_CN_NAME,
             SHIPPING_OBJECT_CODE,
             SHIPPING_OBJECT_CN_NAME,
             MANUFACTURE_OBJECT_CODE,
             MANUFACTURE_OBJECT_CN_NAME,
             ITEM_CODE,
             ITEM_CN_NAME,
             CALIBER_FLAG,
             RMB_EMS_AMT
        FROM FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T
       WHERE CALIBER_FLAG = V_CALIBER_FLAG
	     AND PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE)-2 AND YEAR(CURRENT_DATE);
	   
	--写入日志
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => V_STEP_NUM,
	  F_CAL_LOG_DESC => 'EMS金额落表',
	  F_DML_ROW_COUNT => SQL%ROWCOUNT,
	  F_RESULT_STATUS => X_SUCCESS_FLAG,
	  F_ERRBUF => 'SUCCESS'); 
	  
  ELSIF V_CALIBER_FLAG = 'M' THEN
  
	--1.2 根据业务口径入参为'M'，将自制金额落表，需解密
	V_STEP_NUM := V_STEP_NUM + 1;
    INSERT INTO DM_DECRYP_AMT_TEMP
      (PERIOD_YEAR,
       PERIOD_ID,
       LV0_CODE,
       LV0_CN_NAME,
       LV1_CODE,
       LV1_CN_NAME,
       BUSSINESS_OBJECT_CODE,
       BUSSINESS_OBJECT_CN_NAME,
       SHIPPING_OBJECT_CODE,
       SHIPPING_OBJECT_CN_NAME,
       MANUFACTURE_OBJECT_CODE,
       MANUFACTURE_OBJECT_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       CALIBER_FLAG,
       RMB_COST_AMT)
      SELECT PERIOD_YEAR,
             PERIOD_ID,
             LV0_CODE,
             LV0_CN_NAME,
             LV1_CODE,
             LV1_CN_NAME,
             BUSSINESS_OBJECT_CODE,
             BUSSINESS_OBJECT_CN_NAME,
             SHIPPING_OBJECT_CODE,
             SHIPPING_OBJECT_CN_NAME,
             MANUFACTURE_OBJECT_CODE,
             MANUFACTURE_OBJECT_CN_NAME,
             ITEM_CODE,
             ITEM_CN_NAME,
             CALIBER_FLAG,
             TO_NUMBER(GS_DECRYPT(RMB_MADE_AMT,
                                  V_KEYSTR,
                                  'aes128',
                                  'cbc',
                                  'sha256')) AS RMB_COST_AMT
        FROM FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T
       WHERE CALIBER_FLAG = V_CALIBER_FLAG
	     AND PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE)-2 AND YEAR(CURRENT_DATE);
	   
	--写入日志
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => V_STEP_NUM,
	  F_CAL_LOG_DESC => '自制金额落表',
	  F_DML_ROW_COUNT => SQL%ROWCOUNT,
	  F_RESULT_STATUS => X_SUCCESS_FLAG,
	  F_ERRBUF => 'SUCCESS'); 
	  
END IF ;

--2.制造金额卷积落表
--2.1制造对象卷积金额表创建
DROP TABLE IF EXISTS DM_MONTH_MADE_SUM_AMT_T;
CREATE TEMPORARY TABLE DM_MONTH_MADE_SUM_AMT_T(
	PERIOD_ID CHARACTER VARYING(50),
	LV0_CODE CHARACTER VARYING(50),
	LV0_CN_NAME CHARACTER VARYING(200),
	LV1_CODE CHARACTER VARYING(50),
	LV1_CN_NAME CHARACTER VARYING(200),
	BUSSINESS_OBJECT_CODE CHARACTER VARYING(200),
	BUSSINESS_OBJECT_CN_NAME CHARACTER VARYING(200),
	SHIPPING_OBJECT_CODE CHARACTER VARYING(200),
	SHIPPING_OBJECT_CN_NAME CHARACTER VARYING(200),
	MANUFACTURE_OBJECT_CODE CHARACTER VARYING(200),
	MANUFACTURE_OBJECT_CN_NAME CHARACTER VARYING(200),
	CALIBER_FLAG CHARACTER VARYING(2),
	RMB_COST_AMT  NUMERIC,
	TOP_FLAG CHARACTER VARYING(2)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY ROUNDROBIN;

	--写入日志
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => V_STEP_NUM,
	  F_CAL_LOG_DESC => '制造金额表创建成功',
	  F_RESULT_STATUS => X_SUCCESS_FLAG,
	  F_ERRBUF => 'SUCCESS'); 
	  
--2.2 将全量金额与规格品清单关联，分TOP卷积制造对象层级金额
	V_STEP_NUM := V_STEP_NUM + 1;	
  INSERT INTO DM_MONTH_MADE_SUM_AMT_T
    (PERIOD_ID,
     LV0_CODE,
     LV0_CN_NAME,
     LV1_CODE,
     LV1_CN_NAME,
     BUSSINESS_OBJECT_CODE,
     BUSSINESS_OBJECT_CN_NAME,
     SHIPPING_OBJECT_CODE,
     SHIPPING_OBJECT_CN_NAME,
     MANUFACTURE_OBJECT_CODE,
     MANUFACTURE_OBJECT_CN_NAME,
     RMB_COST_AMT,
	 TOP_FLAG,
	 CALIBER_FLAG)
    SELECT T1.PERIOD_ID,
           T1.LV0_CODE,
           T1.LV0_CN_NAME,
           T1.LV1_CODE,
           T1.LV1_CN_NAME,
           T1.BUSSINESS_OBJECT_CODE,
           T1.BUSSINESS_OBJECT_CN_NAME,
           T1.SHIPPING_OBJECT_CODE,
           T1.SHIPPING_OBJECT_CN_NAME,
           T1.MANUFACTURE_OBJECT_CODE,
           T1.MANUFACTURE_OBJECT_CN_NAME,
           SUM(T1.RMB_COST_AMT) AS RMB_COST_AMT,
           DECODE(T2.VERSION_ID, NULL, 'N', 'Y') AS TOP_FLAG,
		   T1.CALIBER_FLAG
      FROM DM_DECRYP_AMT_TEMP T1
      LEFT JOIN ( --取规格品维
                 SELECT DISTINCT LV0_CODE,
                                  LV1_CODE,
                                  BUSSINESS_OBJECT_CODE,
                                  SHIPPING_OBJECT_CODE,
                                  MANUFACTURE_OBJECT_CODE,
                                  TOP_ITEM_CODE,
                                  VERSION_ID
                   FROM FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T
                  WHERE CALIBER_FLAG = V_CALIBER_FLAG
                    AND VERSION_ID = V_VERSION
                    AND IS_TOP_FLAG = 'Y') T2
        ON T1.ITEM_CODE = T2.top_item_code
       AND T1.LV0_CODE = T2.LV0_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.BUSSINESS_OBJECT_CODE = T2.BUSSINESS_OBJECT_CODE
       AND T1.SHIPPING_OBJECT_CODE = T2.SHIPPING_OBJECT_CODE
       AND T1.MANUFACTURE_OBJECT_CODE = T2.MANUFACTURE_OBJECT_CODE
	 WHERE NVL(T1.MANUFACTURE_OBJECT_CODE||T1.SHIPPING_OBJECT_CODE,'SNULL') <> 'SNULL' --正常数据，筛出海思与云核心网的特殊情况
     GROUP BY T1.PERIOD_ID,
              T1.LV0_CODE,
              T1.LV0_CN_NAME,
              T1.LV1_CODE,
              T1.LV1_CN_NAME,
			  T2.VERSION_ID,
              T1.BUSSINESS_OBJECT_CODE,
              T1.BUSSINESS_OBJECT_CN_NAME,
              T1.SHIPPING_OBJECT_CODE,
              T1.SHIPPING_OBJECT_CN_NAME,
              T1.MANUFACTURE_OBJECT_CODE,
              T1.MANUFACTURE_OBJECT_CN_NAME,
			  T1.CALIBER_FLAG
			  
UNION ALL
--加入海思与云核心网的特殊情况的数据卷积
    SELECT T1.PERIOD_ID,
           T1.LV0_CODE,
           T1.LV0_CN_NAME,
           T1.LV1_CODE,
           T1.LV1_CN_NAME,
           T1.BUSSINESS_OBJECT_CODE,
           T1.BUSSINESS_OBJECT_CN_NAME,
           '' ,
           '' ,
           '' ,
           '' ,
           SUM(T1.RMB_COST_AMT) AS RMB_COST_AMT,
           DECODE(T2.VERSION_ID, NULL, 'N', 'Y') AS TOP_FLAG,
		   T1.CALIBER_FLAG
      FROM DM_DECRYP_AMT_TEMP T1
      LEFT JOIN ( --取规格品维
                 SELECT DISTINCT LV0_CODE,
                                  LV1_CODE,
                                  BUSSINESS_OBJECT_CODE,
                                  TOP_ITEM_CODE,
                                  VERSION_ID
                   FROM FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T
                  WHERE CALIBER_FLAG = V_CALIBER_FLAG
                    AND VERSION_ID = V_VERSION
                    AND IS_TOP_FLAG = 'Y') T2
        ON T1.ITEM_CODE = T2.top_item_code
       AND T1.LV0_CODE = T2.LV0_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.BUSSINESS_OBJECT_CODE = T2.BUSSINESS_OBJECT_CODE
	 WHERE NVL(T1.MANUFACTURE_OBJECT_CODE||T1.SHIPPING_OBJECT_CODE,'SNULL') = 'SNULL' 
     GROUP BY T1.PERIOD_ID,
              T1.LV0_CODE,
              T1.LV0_CN_NAME,
              T1.LV1_CODE,
              T1.LV1_CN_NAME,
			  T2.VERSION_ID,
              T1.BUSSINESS_OBJECT_CODE,
              T1.BUSSINESS_OBJECT_CN_NAME,
			  T1.CALIBER_FLAG;
			 
	--写入日志
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => V_STEP_NUM,
	  F_CAL_LOG_DESC => '制造对象金额落表',
	  F_DML_ROW_COUNT => SQL%ROWCOUNT,
	  F_RESULT_STATUS => X_SUCCESS_FLAG,
	  F_ERRBUF => 'SUCCESS'); 
	  
	  
--3.缺失月份金额补0
  WITH ACTUAL_AMT_TEMP AS
   ( --实际存在的所有维度
    SELECT DISTINCT LV0_CODE,
                     LV0_CN_NAME,
                     LV1_CODE,
                     LV1_CN_NAME,
                     BUSSINESS_OBJECT_CODE,
                     BUSSINESS_OBJECT_CN_NAME,
                     SHIPPING_OBJECT_CODE,
                     SHIPPING_OBJECT_CN_NAME,
                     MANUFACTURE_OBJECT_CODE,
                     MANUFACTURE_OBJECT_CN_NAME,
                     TOP_FLAG
      FROM DM_MONTH_MADE_SUM_AMT_T),
  
  PERIOD_DIM_TEMP AS
   ( --生成连续月份, 两年前第1月至当前系统月(不含)
    SELECT CAST(TO_CHAR(ADD_MONTHS(V_BEGIN_DATE, NUM.VAL - 1), 'YYYYMM') AS
                 BIGINT) AS PERIOD_ID
      FROM GENERATE_SERIES(1,
                            TO_NUMBER(TIMESTAMPDIFF(MONTH,
                                                    V_BEGIN_DATE,
                                                    CURRENT_TIMESTAMP)),
                            1) NUM(VAL)),
  
  CROSS_JOIN_TEMP AS
   (
    --生成连续年月的发散维
    SELECT CAST(SUBSTR(B.PERIOD_ID, 1, 4) AS BIGINT) AS PERIOD_YEAR,
            B.PERIOD_ID,
            T1.LV0_CODE,
            T1.LV0_CN_NAME,
            T1.LV1_CODE,
            T1.LV1_CN_NAME,
            T1.BUSSINESS_OBJECT_CODE,
            T1.BUSSINESS_OBJECT_CN_NAME,
            T1.SHIPPING_OBJECT_CODE,
            T1.SHIPPING_OBJECT_CN_NAME,
            T1.MANUFACTURE_OBJECT_CODE,
            T1.MANUFACTURE_OBJECT_CN_NAME,
            T1.TOP_FLAG
      FROM ACTUAL_AMT_TEMP T1, PERIOD_DIM_TEMP B) 
      
   INSERT INTO FIN_DM_OPT_FOI.DM_FOM_MONTH_ITEM_SUM_AMT_T
     (VERSION_ID,
      PERIOD_YEAR,
      PERIOD_ID,
      LV0_CODE,
      LV0_CN_NAME,
      LV1_CODE,
      LV1_CN_NAME,
      BUSSINESS_OBJECT_CODE,
      BUSSINESS_OBJECT_CN_NAME,
      SHIPPING_OBJECT_CODE,
      SHIPPING_OBJECT_CN_NAME,
      GROUP_CODE,
      GROUP_CN_NAME,
      ACTUAL_COST_AMT,
	  TOP_FLAG,
      APPEND_FLAG,
      CREATED_BY,
      CREATION_DATE,
      LAST_UPDATED_BY,
      LAST_UPDATE_DATE,
      DEL_FLAG,
      CALIBER_FLAG)
  SELECT V_VERSION AS VERSION_ID,
         T1.PERIOD_YEAR,
         T1.PERIOD_ID,
         T1.LV0_CODE,
         T1.LV0_CN_NAME,
         T1.LV1_CODE,
         T1.LV1_CN_NAME,
         T1.BUSSINESS_OBJECT_CODE,
         T1.BUSSINESS_OBJECT_CN_NAME,
         T1.SHIPPING_OBJECT_CODE,
         T1.SHIPPING_OBJECT_CN_NAME,
         T1.MANUFACTURE_OBJECT_CODE,
         T1.MANUFACTURE_OBJECT_CN_NAME,
         NVL(T2.RMB_COST_AMT, 0) AS ACTUAL_COST_AMT,
		 T1.TOP_FLAG,
         DECODE(T2.RMB_COST_AMT, '', 'Y', 'N') AS APPEND_FLAG,
         '-1' AS CREATED_BY,
         CURRENT_DATE AS CREATION_DATE,
         '-1' AS LAST_UPDATED_BY,
         CURRENT_DATE AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         V_CALIBER_FLAG AS CALIBER_FLAG
    FROM CROSS_JOIN_TEMP T1
    LEFT JOIN DM_MONTH_MADE_SUM_AMT_T T2
      ON NVL(T1.LV0_CODE, 'LV0') = NVL(T2.LV0_CODE, 'LV0')
     AND NVL(T1.LV1_CODE, 'LV1') = NVL(T2.LV1_CODE, 'LV1')
     AND NVL(T1.BUSSINESS_OBJECT_CODE, 'BOD') =
         NVL(T2.BUSSINESS_OBJECT_CODE, 'BOD')
     AND NVL(T1.SHIPPING_OBJECT_CODE, 'SOD') =
	     NVL(T2.SHIPPING_OBJECT_CODE, 'SOD')
	 AND NVL(T1.MANUFACTURE_OBJECT_CODE, 'MOD') =
	     NVL(T2.MANUFACTURE_OBJECT_CODE, 'MOD')
     AND T1.PERIOD_ID = T2.PERIOD_ID
	 AND T1.TOP_FLAG = T2.TOP_FLAG;

		
	--写入日志
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => V_STEP_NUM,
	  F_CAL_LOG_DESC => '规格品监控图数据落表成功',
	  F_RESULT_STATUS => X_SUCCESS_FLAG,
	  F_ERRBUF => 'SUCCESS'); 
	
ANALYZE FIN_DM_OPT_FOI.DM_FOM_MONTH_ITEM_SUM_AMT_T;
	
RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

