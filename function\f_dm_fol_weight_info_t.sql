-- Name: f_dm_fol_weight_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fol_weight_info_t(p_version_id integer DEFAULT NULL::integer, p_refresh_type character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：2024-02-19
创建人  ：qwx1110218
背景描述：权重表的加工规则：1、计算Top航线清的成本权重；
                            2、层级：运输方式-区域-航线；
                            3、权重计算：用（202001至最新实际履行货量时间）全部时间的成本计算，量×价；以区域下航线权重为例，航线的成本/区域的成本；
                            4、成本计算逻辑：
                               4.1）供应商成本：全部时间加和{（供应商下40HQ柜型的月燃油价格+供应商下40HQ柜型月框招价格） ×供应商下40HQ月量}；
                               4.2）区域下航线成本，全部时间加和{卷积至香港-鹿特丹航线[（供应商下40HQ柜型的月燃油价格+供应商下40HQ柜型月框招价格） ×供应商下40HQ月量]}；
                               4.3）区域成本：卷积至区域{航线全部时间的成本}；
                               4.4）精品海运成本：卷积至精品海运{区域全部时间的成本}
          p_version_id 逻辑：1、自动调度，取版本表最大版本ID+1；2、刷新（页面的刷新价格表、刷新系统）：取java传版本ID；
          p_refresh_type 逻辑：1、自动调度，直接赋值为 4_AUTO；2、刷新（页面的刷新价格表、刷新系统）：取java传的值（1_刷新价格表 或 2_刷新系统）
参数描述：参数(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_fol_weight_info_t()
变更记录：2024-6-5 qwx1110218 新增非Top航线、新增ALL柜型
          2024-8-21 qwx1110218 来源表变更：由 dm_fol_route_info_sum_t （价格补录表内关联航线量集成表）变更为 dm_fol_route_price_info_sum_t （价格补录表左关联航线量集成表），
                                           且需要剔除柜型量为空的数据（即 container_qty is not null）；24年9月7号版本逻辑变更：无需将20GP数量×1，40GP数量×2，40HQ数量×2；

*/


declare
	v_sp_name varchar(100) := 'fin_dm_opt_foi.f_dm_fol_weight_info_t('||p_version_id||','''||p_refresh_type||''')';
	v_tbl_name varchar(100) := 'fin_dm_opt_foi.dm_fol_weight_info_t';
	v_dml_row_count  int default 0 ;
	v_max_version_id int;
	v_route_version_code  varchar(30);
	v_price_version_code  varchar(30);
	v_max_last_update_date timestamp;
	v_version_status      varchar(50);
	v_max_year       int;


begin

  set enable_force_vector_engine to on;
	x_success_flag := '1';        --1表示成功

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '权重信息表 '||v_tbl_name||'：开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  -- 版本信息表中执行失败的版本ID，目标表中需要清理
  with version_info_2001_tmp as(
  select distinct nvl(version_id,0) as version_id
    from fin_dm_opt_foi.dm_fol_version_info_t
   where step = 2001   -- 执行失败
     and upper(del_flag) = 'N'
  )
  delete from fin_dm_opt_foi.dm_fol_weight_info_t where upper(del_flag) = 'N' and nvl(version_id,0) in(select nvl(version_id,0) from version_info_2001_tmp)
  ;

  -- 从航线量汇总表取最大版本ID的数据
  select max(version_id) as max_version_id into v_max_version_id from fin_dm_opt_foi.dm_fol_route_price_info_sum_t where upper(del_flag) = 'N';

  -- 如果是自动调度，版本ID则取版本信息表的最大版本ID+1；如果是刷新按钮，则直接取版本信息表的最大版本ID；
  if((p_version_id is null or p_version_id = '') and (p_refresh_type is null or p_refresh_type = '')) then
    -- 从版本表获取航线量汇总表版本ID对应的航线信息表的版本编码
    select max(version_code) as version_code into v_route_version_code
      from fin_dm_opt_foi.dm_fol_version_info_t t1
     where version_id = v_max_version_id
       and source_en_name = 'f_dm_fol_route_info_t'
       and not exists(select distinct version_id
                        from dm_fol_version_info_t t2
                       where t1.version_id = t2.version_id
                         and t2.step in(2001,0,2)
                         and upper(t2.del_flag) = 'N'
                     )
       and upper(t1.del_flag) = 'N'
    ;

    -- 从版本表获取航线量汇总表版本ID对应的物流航线价格补录表的版本编码
    select max(version_code) as version_code into v_price_version_code
      from fin_dm_opt_foi.dm_fol_version_info_t t1
     where version_id = v_max_version_id
       and source_en_name = 'apd_fol_route_price_info_t'
       and not exists(select distinct version_id
                        from dm_fol_version_info_t t2
                       where t1.version_id = t2.version_id
                         and t2.step in(2001,0,2)
                         and upper(t2.del_flag) = 'N'
                     )
       and upper(t1.del_flag) = 'N'
    ;

    -- 清理已经写入版本信息表的数据
    delete from fin_dm_opt_foi.dm_fol_version_info_t
     where version_id = v_max_version_id
       and source_en_name = 'f_dm_fol_weight_info_t'
       and refresh_type = '4_AUTO'
       and step = 1
       and upper(del_flag) = 'N'
    ;

    -- 成功数据写入到版本信息表
    insert into fin_dm_opt_foi.dm_fol_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select v_max_version_id   as version_id
         , v_route_version_code as version_code
         , 2 as step
         , 'f_dm_fol_weight_info_t' as source_en_name
         , '权重信息函数'           as source_cn_name
         , '4_AUTO' as refresh_type
         , 'version_code 是航线清单表的版本编码' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
  	 union all
  	select v_max_version_id   as version_id
         , v_price_version_code as version_code
         , 2 as step
         , 'f_dm_fol_weight_info_t' as source_en_name
         , '权重信息函数'           as source_cn_name
         , '4_AUTO' as refresh_type
         , 'version_code 是物流航线价格补录表的版本编码' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
    ;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '自动调度，版本ID：'||v_max_version_id||'，航线清单表的版本编码：'||v_route_version_code||'，价格补录表的版本编码：'||v_price_version_code||'，写入到版本信息表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;

  elseif((p_version_id is not null or p_version_id <> '') and (p_refresh_type = '1_刷新价格表')) then
    -- 从版本表获取航线量汇总表版本ID对应的物流航线价格补录表的版本编码
    select max(version_code) as version_code into v_price_version_code
      from fin_dm_opt_foi.dm_fol_version_info_t t1
     where version_id = p_version_id
       and source_en_name is null
       and refresh_type = '1_刷新价格表'
       and upper(del_flag) = 'N'
       --and step = 2  -- 价格表刷新时，java会更新“价格补录表”的step=2，所有表数据刷新完成后，才更新“价格补录表”的step=1
    ;

    -- 从版本表获取航线量汇总表版本ID对应的航线信息表的版本编码
    select max(version_code) as version_code into v_route_version_code
      from fin_dm_opt_foi.dm_fol_version_info_t t1
     where version_id < p_version_id
       and source_en_name = 'f_dm_fol_route_info_t'
       and not exists(select distinct version_id
                        from dm_fol_version_info_t t2
                       where t1.version_id = t2.version_id
                         and t2.step in(2001,0,2)
                         and upper(t2.del_flag) = 'N'
                     )
       and upper(t1.del_flag) = 'N'
    ;

    -- 清理已经写入版本信息表的数据
    delete from fin_dm_opt_foi.dm_fol_version_info_t
     where version_id = p_version_id
       and source_en_name = 'f_dm_fol_weight_info_t'
       and refresh_type = p_refresh_type
       and step = 1
       and upper(del_flag) = 'N'
    ;

    -- 成功数据写入到版本信息表
    insert into fin_dm_opt_foi.dm_fol_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select p_version_id   as version_id
         , v_price_version_code as version_code
         , 2 as step
         , 'f_dm_fol_weight_info_t' as source_en_name
         , '权重信息函数'           as source_cn_name
         , p_refresh_type as refresh_type
         , 'version_code 是物流航线价格补录表的版本编码' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
    ;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入版本ID：'||p_version_id||'，传入刷新类型：'||p_refresh_type||'，航线清单表的版本编码：'||v_route_version_code||'，价格补录表的版本编码：'||v_price_version_code||'，写入到版本信息表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;

  elseif((p_version_id is not null or p_version_id <> '') and p_refresh_type = '2_刷新系统') then
    -- 从版本表获取航线量汇总表版本ID对应的物流航线价格补录表的版本编码
    select max(version_code) as version_code into v_price_version_code
      from fin_dm_opt_foi.dm_fol_version_info_t t1
     where version_id < p_version_id
       and source_en_name = 'apd_fol_route_price_info_t'
       and not exists(select distinct version_id
                        from dm_fol_version_info_t t2
                       where t1.version_id = t2.version_id
                         and t2.step in(2001,0,2)
                         and upper(t2.del_flag) = 'N'
                     )
      and upper(t1.del_flag) = 'N'
    ;

    -- 从版本表获取航线量汇总表版本ID对应的航线信息表的版本编码
    select max(version_code) as version_code into v_route_version_code
      from fin_dm_opt_foi.dm_fol_version_info_t t1
     where version_id = p_version_id
       and source_en_name is null
       and refresh_type = '2_刷新系统'
       and upper(del_flag) = 'N'
       --and step = 2  -- 系统刷新时，java会更新“航线清单表”的step=2，所有表数据刷新完成后，才更新“航线清单表”的step=1
    ;

    -- 清理已经写入版本信息表的数据
    delete from fin_dm_opt_foi.dm_fol_version_info_t
     where version_id = p_version_id
       and source_en_name = 'f_dm_fol_weight_info_t'
       and refresh_type = p_refresh_type
       and step = 1
       and upper(del_flag) = 'N'
    ;

    -- 成功数据写入到版本信息表
    insert into fin_dm_opt_foi.dm_fol_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select p_version_id   as version_id
         , v_route_version_code as version_code
         , 2 as step
         , 'f_dm_fol_weight_info_t' as source_en_name
         , '权重信息函数'           as source_cn_name
         , p_refresh_type as refresh_type
         , 'version_code 是航线清单表的版本编码' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
    ;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入版本ID：'||p_version_id||'，传入刷新类型：'||p_refresh_type||'，航线清单表的版本编码：'||v_route_version_code||'，价格补录表的版本编码：'||v_price_version_code||'，写入到版本信息表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;

  end if;

  -- 如果获取的航线信息表的版本编码不为空值
  if((v_route_version_code is  null or v_route_version_code = '') and p_refresh_type = '2_刷新系统') then

    -- 开始记录日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '版本ID：'||nvl(p_version_id,v_max_version_id)||'，航线信息表的版本编码为空值： '||v_route_version_code||'，需要排查版本信息表的最大版本ID是否有2001（执行失败）、2（执行中）、0（草稿（java））',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
    x_success_flag := 2001;
    return;

  -- 如果获取的价格补录表的版本编码不为空值
  elseif((v_price_version_code is  null or v_price_version_code = '') and p_refresh_type = '1_刷新价格表') then

    -- 开始记录日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '版本ID：'||nvl(p_version_id,v_max_version_id)||'，价格补录表的版本编码为空值： '||v_price_version_code||'，需要排查版本信息表的最大版本ID是否有2001（执行失败）、2（执行中）、0（草稿（java））',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
    x_success_flag := 2001;
    return;

  else

    -- 根据版本ID获取的版本编码，取最大更新时间的数据
    -- 取Top航线、非Top航线的数据
    select max(last_update_date) as max_last_update_date into v_max_last_update_date
      from fin_dm_opt_foi.dm_fol_route_info_t
     where version_code = v_route_version_code
       -- and top_route_flag = 'Y'  -- 只取是Top航线的
       and upper(version_status) <> 'ADJUST'  -- 排除ADJUST版本
       and upper(del_flag) = 'N'
    ;

    -- 根据版本版本+最后更新时间获取对应的状态
    select distinct version_status into v_version_status
      from fin_dm_opt_foi.dm_fol_route_info_t
     where version_code = v_route_version_code
       -- and top_route_flag = 'Y'  -- 只取是Top航线的
       and upper(del_flag) = 'N'
       and last_update_date = v_max_last_update_date
    ;

    -- 取航线量汇总表的最大年
    select max(year) as max_year into v_max_year
      from fin_dm_opt_foi.dm_fol_route_price_info_sum_t
     where version_id = nvl(p_version_id,v_max_version_id)
       and transport_mode = '精品海运'
       and upper(del_flag) = 'N'
    ;

    -- 清理表数据
    delete from fin_dm_opt_foi.dm_fol_weight_info_t where version_id = nvl(p_version_id,v_max_version_id) and upper(del_flag) = 'N';

    -- 数据入到目标表
    insert into fin_dm_opt_foi.dm_fol_weight_info_t(
           version_id            -- 版本ID（java传版本ID，即版本信息表的version_id）
         , period_id             -- 会计期
         , transport_mode        -- 运输方式（精品海运）
         , region_cn_name        -- 区域
         , route                 -- 航线（起始港_目的港）
         , source_country_name   -- 起始国家
         , dest_country_name     -- 目的国家
         , supplier_short_name   -- LST（即供应商）
         , container_type        -- 柜型（20GP、40GP、40HQ）
         , weight_type           -- 权重类型（比如：成本 COST）
         , level_code            -- 层级编码（01、02、03、04）
         , level_desc            -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
         , currency              -- 币种
         , amount                -- 金额（量*价）
         , weight                -- 权重
         , remark                -- 备注
         , created_by            -- 创建人
         , creation_date         -- 创建时间
         , last_updated_by       -- 修改人
         , last_update_date      -- 修改时间
         , del_flag              -- 是否删除
    )
    with route_info_sum_tmp1 as(
    -- 取航线量汇总表的最大版本ID数据
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , period_id
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , route	                  -- 航线（目的港_国家）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , supplier_short_name      -- LST（即供应商）
         , container_type           -- 柜型（20GP、40GP、40HQ）
         , currency                 -- 币种
         , price
         , (case when container_type = '20GP' then container_qty
                 when container_type = '40GP' then container_qty/2
                 when container_type = '40HQ' then container_qty/2
            end) as container_qty
      from fin_dm_opt_foi.dm_fol_route_price_info_sum_t
     where version_id = nvl(p_version_id,v_max_version_id)
       and transport_mode = '精品海运'
       and upper(del_flag) = 'N'
       --and year >= v_max_year -1   -- 取去年+当年YTD
       and container_qty is not null
    ),
    route_info_sum_tmp as(
    -- 取航线量汇总表的最大版本ID数据
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , period_id
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , route	                  -- 航线（目的港_国家）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , supplier_short_name      -- LST（即供应商）
         , container_type           -- 柜型（20GP、40GP、40HQ）
         , currency                 -- 币种
         , round(sum(price*container_qty),10) as cost_amt  -- 成本
      from route_info_sum_tmp1
     group by version_id
         , period_id
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , supplier_short_name
         , container_type
         , currency
    ),
    -- 按供应商汇总的Top航线
    weight_info_tmp1 as(
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , period_id
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , route                    -- 航线（起始港_目的港）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , supplier_short_name      -- LST（即供应商）
         , container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , currency                 -- 币种
         , cost_amt                 -- 成本
      from route_info_sum_tmp
     union all
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , period_id
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , route                    -- 航线（起始港_目的港）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , supplier_short_name      -- LST（即供应商）
         , 'ALL' as container_type  -- 柜型（20GP、40GP、40HQ、ALL）
         , currency                 -- 币种
         , sum(cost_amt) as cost_amt -- 成本
      from route_info_sum_tmp
     group by version_id
         , period_id
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , supplier_short_name
         , currency
    ),
    -- 按航线汇总
    weight_info_tmp2 as(
    select version_id                -- 版本ID（java传版本ID，即版本信息表的version_id）
         , period_id
         , transport_mode            -- 运输方式（精品海运、Xeneta）
         , region_cn_name            -- 目的地区域
         , route                     -- 航线（起始港_目的港）
         , source_country_name       -- 起运地国家
         , dest_country_name         -- 目的地国家
         , container_type            -- 柜型（20GP、40GP、40HQ、ALL）
         , currency                  -- 币种
         , sum(cost_amt) as cost_amt -- 成本
      from weight_info_tmp1
     group by version_id
         , period_id
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
    ),
    -- 按区域汇总
    weight_info_tmp3 as(
    select version_id                -- 版本ID（java传版本ID，即版本信息表的version_id）
         , period_id
         , transport_mode            -- 运输方式（精品海运、Xeneta）
         , region_cn_name            -- 目的地区域
         , container_type            -- 柜型（20GP、40GP、40HQ、ALL）
         , currency                  -- 币种
         , sum(cost_amt) as cost_amt -- 成本
      from weight_info_tmp1
     group by version_id
         , period_id
         , transport_mode
         , region_cn_name
         , container_type
         , currency
    ),
    -- 按运输方式汇总
    weight_info_tmp4 as(
    select version_id     -- 版本ID（java传版本ID，即版本信息表的version_id）
         , period_id
         , transport_mode            -- 运输方式（精品海运、Xeneta）
         , container_type            -- 柜型（20GP、40GP、40HQ、ALL）
         , currency                  -- 币种
         , sum(cost_amt) as cost_amt -- 成本
      from weight_info_tmp1
     group by version_id
         , period_id
         , transport_mode
         , container_type
         , currency
    )
    -- 供应商层级
    select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.period_id
         , t1.transport_mode           -- 运输方式（精品海运）
         , t1.region_cn_name           -- 目的地区域
         , t1.route	                   -- 航线（起始港_目的港）
         , t1.source_country_name      -- 起运地国家
         , t1.dest_country_name        -- 目的地国家
         , t1.supplier_short_name      -- LST（即供应商）
         , t1.container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , 'COST' as weight_type       -- 权重类型（比如：成本 COST）
         , '04'   as level_code        -- 层级编码（01、02、03、04）
         , '供应商' as level_desc      -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
         , t1.currency                 -- 币种
         , t1.cost_amt  as amount      -- 金额（量*价）
         , null as weight  -- 权重
         , '' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
      from weight_info_tmp1 t1
     union all
    -- 航线层级
    select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.period_id
         , t1.transport_mode           -- 运输方式（精品海运）
         , t1.region_cn_name           -- 目的地区域
         , t1.route	                   -- 航线（起始港_目的港）
         , t1.source_country_name      -- 起运地国家
         , t1.dest_country_name        -- 目的地国家
         , '' as supplier_short_name   -- LST（即供应商）
         , t1.container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , 'COST' as weight_type       -- 权重类型（比如：成本 COST）
         , '03'   as level_code        -- 层级编码（01、02、03、04）
         , '航线' as level_desc        -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
         , t1.currency                 -- 币种
         , t1.cost_amt  as amount      -- 金额（量*价）
         , null as weight  -- 权重
         , '' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
      from weight_info_tmp2 t1 
     union all
    -- 区域层级
    select t1.version_id                -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.period_id
         , t1.transport_mode            -- 运输方式（精品海运）
         , t1.region_cn_name            -- 目的地区域
         , '' as route	                -- 航线（起始港_目的港）
         , '' as source_country_name    -- 起运地国家
         , '' as dest_country_name      -- 目的地国家
         , '' as supplier_short_name    -- LST（即供应商）
         , t1.container_type            -- 柜型（20GP、40GP、40HQ、ALL）
         , 'COST' as weight_type        -- 权重类型（比如：成本 COST）
         , '02'   as level_code         -- 层级编码（01、02、03、04）
         , '区域' as level_desc         -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
         , t1.currency                  -- 币种
         , t1.cost_amt  as amount       -- 金额（量*价）
         , null as weight  -- 权重
         , '' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
      from weight_info_tmp3 t1
     union all
    -- 运输方式层级
    select t1.version_id             -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.period_id
         , t1.transport_mode         -- 运输方式（精品海运）
         , '' as region_cn_name      -- 目的地区域
         , '' as route	             -- 航线（起始港_目的港）
         , '' as source_country_name -- 起运地国家
         , '' as dest_country_name   -- 目的地国家
         , '' as supplier_short_name -- LST（即供应商）
         , t1.container_type         -- 柜型（20GP、40GP、40HQ、ALL）
         , 'COST' as weight_type     -- 权重类型（比如：成本 COST）
         , '01'   as level_code      -- 层级编码（01、02、03、04）
         , '运输方式' as level_desc  -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
         , t1.currency               -- 币种
         , t1.cost_amt  as amount    -- 金额（量*价）
         , null as weight  -- 权重
         , '' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
      from weight_info_tmp4 t1
    ;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '航线清单表的状态：'||v_version_status||'，数据入到目标表，数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;

    -- 成功数据写入到版本信息表，需要更新对应的“执行中”数据状态
    update fin_dm_opt_foi.dm_fol_version_info_t set step = 1
     where version_id = nvl(p_version_id,v_max_version_id)
       and source_en_name = 'f_dm_fol_weight_info_t'
       and refresh_type = nvl(p_refresh_type,'4_AUTO')
       and upper(del_flag) = 'N'
    ;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 4,
        p_log_cal_log_desc =>  '版本信息表中的step已更新为完成，运行结束！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;

  end if;

  exception
  	when others then

      perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		    p_log_row_count => null,
		    p_log_errbuf => sqlstate  --错误编码
      ) ;
	x_success_flag := '2001';	         --2001表示失败

  -- 失败信息写入到版本信息表
  insert into fin_dm_opt_foi.dm_fol_version_info_t(
         version_id           -- 版本ID（自动生成）
       , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
       , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
       , source_en_name       -- 来源英文描述（可以是表名、函数名等）
       , source_cn_name       -- 来源中文描述
       , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
       , remark               -- 备注
       , created_by           -- 创建人
       , creation_date        -- 创建时间
       , last_updated_by      -- 修改人
       , last_update_date     -- 修改时间
       , del_flag             -- 是否删除
  )
  select nvl(p_version_id,v_max_version_id) as version_id
       , v_route_version_code as version_code
       , 2001 as step
       , 'f_dm_fol_weight_info_t' as source_en_name
       , '权重信息函数'           as source_cn_name
       , nvl(p_refresh_type,'4_AUTO') as refresh_type
       , 'version_code 是航线清单表的版本编码' as remark
	     , -1 as created_by
	     , current_timestamp as creation_date
	     , -1 as last_updated_by
	     , current_timestamp as last_update_date
	     , 'N' as del_flag
	 union all
	select nvl(p_version_id,v_max_version_id)   as version_id
       , v_price_version_code as version_code
       , 2001 as step
       , 'f_dm_fol_weight_info_t' as source_en_name
       , '权重信息函数'           as source_cn_name
       , nvl(p_refresh_type,'4_AUTO') as refresh_type
       , 'version_code 是物流航线价格补录表的版本编码' as remark
	     , -1 as created_by
	     , current_timestamp as creation_date
	     , -1 as last_updated_by
	     , current_timestamp as last_update_date
	     , 'N' as del_flag
  ;

  --收集统计信息
  analyse fin_dm_opt_foi.dm_fol_weight_info_t;
  analyse fin_dm_opt_foi.dm_fol_version_info_t;

end;
$$
/

