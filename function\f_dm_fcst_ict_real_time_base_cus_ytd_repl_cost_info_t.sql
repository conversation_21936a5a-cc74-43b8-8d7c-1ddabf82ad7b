-- Name: f_dm_fcst_ict_real_time_base_cus_ytd_repl_cost_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_ict_real_time_base_cus_ytd_repl_cost_info_t(f_cost_type character varying, f_granularity_type character varying, f_keystr text, f_custom_id bigint DEFAULT NULL::character varying, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

	/*
创建时间：2024-07-17
创建人  ：QWX1110218
修改人：twx1139790
背景描述：产业成本指数ICT-编码替换关系页面月累计实时虚化逻辑
          1、所有参数都没值时，自动调度（基础数据），只有SPART层级的数据，其它层级的是实时计算；
          2、页面选定筛选条件时，优先查询表，如果表没有数据，则调用函数计算；
          3、虚化不涉及基期切换、不涉及加密；
          4、前台页面选定的参数值由JAVA先入到“组合虚化维表”，JAVA会传递成本类型、秘钥、CUSTOM_ID，根据“成本类型”参数判断是查询PSP的“组合虚化维表”还是STD的“组合虚化维表”：
             4.1、“成本类型”参数=PSP，根据CUSTOM_ID查询“组合虚化维表”值，进行计算；
             4.2、“成本类型”参数=STD，根据CUSTOM_ID查询“组合虚化维表”值，进行计算，此时涉及加解密，会用到传入的秘钥；
参数描述：参数一： F_COST_TYPE 成本类型:PSP PSP成本; STD: 标准成本
          参数二： F_GRANULARITY_TYPE 目录树类型:	IRB:重量级团队目录; INDUS:产业目录;  PROD:销售目录
          参数三： F_KEYSTR	密钥
		      参数四:  F_CUSTOM_ID  组合ID
          返回值： X_RESULT_STATUS 运行状态返回值 '1'为成功，'0'为失败
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_REAL_TIME_BASE_CUS_YTD_REPL_COST_INFO_T()
变更记录-202503：
①部分会话级临时表由先建表再INSERT INTO的形式调整为CREATE TEMPORARY TABLE XXX AS的形式，缩短代码行数；
②本函数内计算的权重数据落到实体表，方便查询问题时可以直接查询数据库数据。
*/


DECLARE
  V_SP_NAME            VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FCST_ICT_REAL_TIME_BASE_CUS_YTD_REPL_COST_INFO_T';
  V_VERSION_ID         BIGINT;        --版本号
  V_FROM_AMT_TABLE     VARCHAR(100);  -- 来源表
  V_FROM_DIM_TABLE     VARCHAR(100);  -- 维表
  V_TO_INDEX_TABLE     VARCHAR(100);  -- 指数目标表
  V_TO_QTY_TABLE       VARCHAR(100);  -- 量目标表
  V_SQL_FROM_PBI_PART    TEXT;	-- 来源表的PBI层级查询字段
  V_GS_DECRYPT_COST_AMT  TEXT;	-- 解密金额（PSP成本不用解密；STD成本需要解密）
  V_CODE_REPL_GTS_TYPE  VARCHAR(50); -- 编码替换关系维表中的目录树类型（IRB 重量级团队目录树、INDUS 产业目录树、PROD 销售目录树）
  V_BASE_PERIOD_ID      INT;  -- 默认基期
  V_MAX_REPL_VERSION_ID INT; -- 替换关系维表最大版本ID
  V_PARENT_LEVEL        VARCHAR(50);
  V_SQL         TEXT;
  V_STEP_NUM    INT;
  V_ANNL_VERSION  BIGINT;
  V_TO_WEIGHT_TABLE VARCHAR(200);
  V_SAME_INDEX_TABLE VARCHAR(200);


BEGIN

	X_RESULT_STATUS := 'SUCCESS';        --1表示成功
	V_STEP_NUM = 1;

	-- 开始记录日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
      F_SP_NAME => V_SP_NAME,    --SP名称
      F_STEP_NUM => V_STEP_NUM,
      F_CAL_LOG_DESC => '函数 '||V_SP_NAME||' 开始运行',--日志描述
      F_FORMULA_SQL_TXT  => V_SQL,
      F_DML_ROW_COUNT => SQL%ROWCOUNT,
      F_RESULT_STATUS => X_RESULT_STATUS,
      F_ERRBUF => 'SUCCESS'
  );


  -- 创建临时表
  DROP TABLE IF EXISTS SPART_LV4_COST_INDEX_TMP;
  CREATE TEMPORARY TABLE SPART_LV4_COST_INDEX_TMP(
         VERSION_ID                   INT             -- 版本ID
       , PERIOD_ID                    INT             -- 会计期
       , PERIOD_YEAR                  INT             -- 会计年
       , BASE_PERIOD_ID               INT             -- 基期
       , LV0_CODE                     VARCHAR(50)     -- LV0编码
       , LV0_CN_NAME                  VARCHAR(200)    -- LV0名称
       , LV1_CODE                     VARCHAR(50)     -- LV1编码
       , LV1_CN_NAME                  VARCHAR(200)    -- LV1名称
       , LV2_CODE                     VARCHAR(50)     -- LV2编码
       , LV2_CN_NAME                  VARCHAR(200)    -- LV2名称
       , LV3_CODE                     VARCHAR(50)     -- LV3编码
       , LV3_CN_NAME                  VARCHAR(200)    -- LV3名称
       , LV4_CODE                     VARCHAR(50)     -- LV4编码
       , LV4_CN_NAME                  VARCHAR(200)    -- LV4名称
       , REGION_CODE                  VARCHAR(50)     -- 地区部编码
       , REGION_CN_NAME               VARCHAR(200)    -- 地区部中文名称
       , REPOFFICE_CODE               VARCHAR(50)     -- 代表处编码
       , REPOFFICE_CN_NAME            VARCHAR(200)    -- 代表处中文名称
       , SPART_CODE                   VARCHAR(50)     -- SPART编码
       , SPART_DESC                   VARCHAR(2000)   -- SPART描述
       , BG_CODE                      VARCHAR(50)     -- BG编码
       , BG_CN_NAME                   VARCHAR(200)    -- BG中文名称
       , OVERSEA_FLAG                 VARCHAR(10)     -- 国内海外标识
	   , SOFTWARE_MARK                VARCHAR(50)
       , VIEW_FLAG                    VARCHAR(50)     -- 路径1：PROD_SPART/路径2：DIMENSION
       , REPLACE_RELATION_NAME        VARCHAR(200)    -- 替换关系名称
       , REPLACE_RELATION_TYPE        VARCHAR(50)     -- 新老编码替换类型（一对一  、一对多 、多对多）
       , RELATION_TYPE                VARCHAR(50)     -- 关系（ 替换 、收编）
       , CODE_TYPE                    VARCHAR(50)     -- 编码类型（NEW:新编码  OLD: 旧编码 ）
       , PROD_QTY                     NUMERIC         -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
       , RMB_COST_AMT                 NUMERIC         -- 标准成本
       , COST_INDEX                   NUMERIC         -- 成本指数
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,SPART_CODE,LV4_CODE,REPLACE_RELATION_NAME,REGION_CODE,REPOFFICE_CODE,BG_CODE)
  ;

  DROP TABLE IF EXISTS SPART_PBI_COST_INFO_TMP;
  CREATE TEMPORARY TABLE SPART_PBI_COST_INFO_TMP(
         VERSION_ID                   INT             -- 版本ID
       , PERIOD_ID                    INT             -- 会计期
       , PERIOD_YEAR                  INT             -- 会计年
       , BASE_PERIOD_ID               INT             -- 基期
       , LV0_CODE                     VARCHAR(50)     -- LV0编码
       , LV0_CN_NAME                  VARCHAR(200)    -- LV0名称
       , LV1_CODE                     VARCHAR(50)     -- LV1编码
       , LV1_CN_NAME                  VARCHAR(200)    -- LV1名称
       , LV2_CODE                     VARCHAR(50)     -- LV2编码
       , LV2_CN_NAME                  VARCHAR(200)    -- LV2名称
       , LV3_CODE                     VARCHAR(50)     -- LV3编码
       , LV3_CN_NAME                  VARCHAR(200)    -- LV3名称
       , GROUP_CODE                   VARCHAR(50)     -- 各层级编码
       , GROUP_CN_NAME                VARCHAR(200)    -- 各层级中文名称
       , PARENT_CODE                  VARCHAR(500)    -- 父级编码
       , PARENT_CN_NAME               VARCHAR(700)    -- 父级中文名称
       , REGION_CODE                  VARCHAR(50)     -- 地区部编码
       , REGION_CN_NAME               VARCHAR(200)    -- 地区部中文名称
       , REPOFFICE_CODE               VARCHAR(50)     -- 代表处编码
       , REPOFFICE_CN_NAME            VARCHAR(200)    -- 代表处中文名称
       , BG_CODE                      VARCHAR(50)     -- BG编码
       , BG_CN_NAME                   VARCHAR(200)    -- BG中文名称
       , OVERSEA_FLAG                 VARCHAR(10)     -- 国内海外标识
	   , SOFTWARE_MARK                VARCHAR(50)
       , REPLACE_RELATION_NAME        VARCHAR(200)    -- 替换关系名称
       , REPLACE_RELATION_TYPE        VARCHAR(50)     -- 新老编码替换类型（一对一  、一对多 、多对多）
       , RELATION_TYPE                VARCHAR(50)     -- 关系（ 替换 、收编）
       , CODE_TYPE                    VARCHAR(50)     -- 编码类型（NEW:新编码  OLD: 旧编码 ）
       , GROUP_LEVEL	                VARCHAR(50)	    -- 各层级（SPART/LV3/LV2/LV1/LV0）
       , PROD_QTY                     NUMERIC         -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
       , RMB_COST_AMT                 NUMERIC         -- 标准成本
       , COST_INDEX                   NUMERIC         -- 成本指数
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,REPLACE_RELATION_NAME,REGION_CODE,REPOFFICE_CODE,BG_CODE)
  ;
  
  -- 创建临时表
  DROP TABLE IF EXISTS SAME_BASE_LV4_INDEX_TMP;
  CREATE TEMPORARY TABLE SAME_BASE_LV4_INDEX_TMP(
         VERSION_ID                   INT             -- 版本ID
       ,  PERIOD_ID                   INT              -- 会计期ID
       , LV0_CODE                     VARCHAR(50)     -- LV0编码
       , LV0_CN_NAME                  VARCHAR(200)    -- LV0名称
       , LV1_CODE                     VARCHAR(50)     -- LV1编码
       , LV1_CN_NAME                  VARCHAR(200)    -- LV1名称
       , LV2_CODE                     VARCHAR(50)     -- LV2编码
       , LV2_CN_NAME                  VARCHAR(200)    -- LV2名称
       , LV3_CODE                     VARCHAR(50)     -- LV3编码
       , LV3_CN_NAME                  VARCHAR(200)    -- LV3名称
       , LV4_CODE                     VARCHAR(50)     -- LV4编码
       , LV4_CN_NAME                  VARCHAR(200)    -- LV4名称
       , REGION_CODE                  VARCHAR(50)     -- 地区部编码
       , REGION_CN_NAME               VARCHAR(200)    -- 地区部中文名称
       , REPOFFICE_CODE               VARCHAR(50)     -- 代表处编码
       , REPOFFICE_CN_NAME            VARCHAR(200)    -- 代表处中文名称
       , BG_CODE                      VARCHAR(50)     -- BG编码
       , BG_CN_NAME                   VARCHAR(200)    -- BG中文名称
       , OVERSEA_FLAG                 VARCHAR(10)     -- 国内海外标识
       , REPLACE_RELATION_NAME        VARCHAR(200)    -- 替换关系名称
       , REPLACE_RELATION_TYPE        VARCHAR(50)     -- 新老编码替换类型（一对一  、一对多 、多对多）
       , RELATION_TYPE                VARCHAR(50)     -- 关系（ 替换 、收编）
	   , SOFTWARE_MARK                VARCHAR(50)
       , VIEW_FLAG                    VARCHAR(50)     -- 路径1：PROD_SPART/路径2：DIMENSION
       , CODE_TYPE                    VARCHAR(50)     -- 编码类型（NEW:新编码  OLD: 旧编码 SAME：同基数）
       , COST_INDEX                 NUMERIC           -- 底层逻辑指数
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(REPLACE_RELATION_NAME,LV4_CODE,REGION_CODE,REPOFFICE_CODE,BG_CODE)
  ;


  -- 来源表
  V_FROM_AMT_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_BASE_DETAIL_SPART_T';  -- 月均价收敛补齐表

  V_FROM_DIM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_DIM_T';  -- 组合虚化维表

  -- 指数目标表
  V_TO_INDEX_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_YTD_REPL_COST_IDX_T';  -- 虚化-编码替换月累计成本指数表

  -- 量目标表
  V_TO_QTY_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_YTD_REPL_INFO_IDX_T';  -- 虚化-编码替换月累计信息表（即成本偏差表）
  
  -- 同基指数表
  V_SAME_INDEX_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_PROD_REPL_SAME_COST_IDX_T';
  
  -- 权重临时表
  V_TO_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_REPL_MID_YTD_BASE_CUS_WEIGHT_T';

  IF(F_COST_TYPE = 'PSP') THEN
    V_GS_DECRYPT_COST_AMT := ', NVL(RMB_COST_AMT::NUMERIC,0) AS RMB_COST_AMT';

  ELSEIF(F_COST_TYPE = 'STD') THEN
    V_GS_DECRYPT_COST_AMT := ', GS_DECRYPT(RMB_COST_AMT,'''||F_KEYSTR||''',''AES128'',''CBC'',''SHA256'') AS RMB_COST_AMT';  -- 解密

  END IF;

  IF(F_GRANULARITY_TYPE = 'IRB') THEN
    -- 来源表的查询字段
    V_SQL_FROM_PBI_PART := ', LV0_PROD_RND_TEAM_CODE AS LV0_CODE
                       , LV0_PROD_RD_TEAM_CN_NAME    AS LV0_CN_NAME
                       , LV1_PROD_RND_TEAM_CODE      AS LV1_CODE
                       , LV1_PROD_RD_TEAM_CN_NAME    AS LV1_CN_NAME
                       , LV2_PROD_RND_TEAM_CODE      AS LV2_CODE
                       , LV2_PROD_RD_TEAM_CN_NAME    AS LV2_CN_NAME
                       , LV3_PROD_RND_TEAM_CODE      AS LV3_CODE
                       , LV3_PROD_RD_TEAM_CN_NAME    AS LV3_CN_NAME
                       , LV4_PROD_RND_TEAM_CODE      AS LV4_CODE
                       , LV4_PROD_RD_TEAM_CN_NAME    AS LV4_CN_NAME
                      ';

    --V_CODE_REPL_GTS_TYPE := 'IRB';

  ELSEIF(F_GRANULARITY_TYPE = 'INDUS') THEN
    -- 来源表的查询字段
    V_SQL_FROM_PBI_PART := ', LV0_INDUSTRY_CATG_CODE   AS LV0_CODE
                       , LV0_INDUSTRY_CATG_CN_NAME     AS LV0_CN_NAME
                       , LV1_INDUSTRY_CATG_CODE        AS LV1_CODE
                       , LV1_INDUSTRY_CATG_CN_NAME     AS LV1_CN_NAME
                       , LV2_INDUSTRY_CATG_CODE        AS LV2_CODE
                       , LV2_INDUSTRY_CATG_CN_NAME     AS LV2_CN_NAME
                       , LV3_INDUSTRY_CATG_CODE        AS LV3_CODE
                       , LV3_INDUSTRY_CATG_CN_NAME     AS LV3_CN_NAME
                       , LV4_INDUSTRY_CATG_CODE        AS LV4_CODE
                       , LV4_INDUSTRY_CATG_CN_NAME     AS LV4_CN_NAME
                      ';

    --V_CODE_REPL_GTS_TYPE := 'INDUS';

  ELSEIF(F_GRANULARITY_TYPE = 'PROD') THEN
    -- 来源表的查询字段
    V_SQL_FROM_PBI_PART := ', LV0_PROD_LIST_CODE  AS LV0_CODE
                       , LV0_PROD_LIST_CN_NAME    AS LV0_CN_NAME
                       , LV1_PROD_LIST_CODE       AS LV1_CODE
                       , LV1_PROD_LIST_CN_NAME    AS LV1_CN_NAME
                       , LV2_PROD_LIST_CODE       AS LV2_CODE
                       , LV2_PROD_LIST_CN_NAME    AS LV2_CN_NAME
                       , LV3_PROD_LIST_CODE       AS LV3_CODE
                       , LV3_PROD_LIST_CN_NAME    AS LV3_CN_NAME
                       , LV4_PROD_LIST_CODE       AS LV4_CODE
                       , LV4_PROD_LIST_CN_NAME    AS LV4_CN_NAME
                      ';

    V_CODE_REPL_GTS_TYPE := 'PROD';


  END IF;

  -- 从版本表取最大版本
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
   WHERE DEL_FLAG = 'N'
     AND STATUS = 1
     AND UPPER(DATA_TYPE) = 'MONTH'
   ORDER BY LAST_UPDATE_DATE DESC
   LIMIT 1
  ;
  
  -- 从版本表取年度最大版本
  SELECT VERSION_ID INTO V_ANNL_VERSION
    FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
   WHERE DEL_FLAG = 'N'
     AND STATUS = 1
     AND UPPER(DATA_TYPE) = 'ANNUAL'
   ORDER BY LAST_UPDATE_DATE DESC
   LIMIT 1
  ;

  -- 从月均价收敛补齐表或取默认基期，默认基期=T-1年的1月
	V_SQL := 'SELECT (SUBSTR(MAX(PERIOD_ID),1,4)::INT - 1)||''01''
              FROM '||V_FROM_AMT_TABLE||'
             WHERE DEL_FLAG = ''N''
               AND VERSION_ID ='|| V_VERSION_ID
  ;

  EXECUTE V_SQL INTO V_BASE_PERIOD_ID;

	RAISE NOTICE '默认基期:%', V_BASE_PERIOD_ID;

  -- 替换关系维表取最大版本的数据
  SELECT MAX(VERSION_ID) INTO V_MAX_REPL_VERSION_ID
    FROM FIN_DM_OPT_FOI.DM_FCST_ICT_CODE_REPL_INFO_T  -- 新旧编码替换关系表（24年7月版只有销售目录树的）
   WHERE DEL_FLAG = 'N'
  ;

  -- 清空权重中间表数据
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_WEIGHT_TABLE||' WHERE GRANULARITY_TYPE = '''||F_GRANULARITY_TYPE||'''';
  
    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '删除权重表：'||V_TO_WEIGHT_TABLE||'，GRANULARITY_TYPE = '||F_GRANULARITY_TYPE||'的数据，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => V_SQL,
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );
  
  IF(F_CUSTOM_ID IS NOT NULL) THEN
    RAISE NOTICE '33333333333333';
    -- 根据传入组合ID查询虚化维表
    V_SQL := '
	  DROP TABLE IF EXISTS BASE_CUS_DIM_TMP;
      CREATE TEMPORARY TABLE BASE_CUS_DIM_TMP AS
              SELECT CUSTOM_ID
                   , LV_CODE
                   , LV_CN_NAME
                   , SPART_CODE
                   , SPART_CN_NAME
                   , PAGE_TYPE
                   , GRANULARITY_TYPE
                   , REGION_CODE
                   , REGION_CN_NAME
                   , REPOFFICE_CODE
                   , REPOFFICE_CN_NAME
                   , BG_CODE
                   , BG_CN_NAME
                   , OVERSEA_FLAG
                   , VIEW_FLAG
                   , STATUS_FLAG
                   , REPLACE_RELATION_NAME
                   , REPLACE_RELATION_TYPE
                   , RELATION_TYPE
                   , CODE_TYPE
                   , PARENT_LEVEL
                   , GROUP_LEVEL
				   , SOFTWARE_MARK
                FROM '||V_FROM_DIM_TABLE||'
               WHERE DEL_FLAG = ''N''
                 AND UPPER(PAGE_TYPE) = ''REPLACE_DIM''
                 AND CUSTOM_ID = '||F_CUSTOM_ID
    ;

    EXECUTE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '传入的组合ID： '||F_CUSTOM_ID||'，获取组合虚化维表的数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => V_SQL,
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    RAISE NOTICE '传入的组合ID:% ', F_CUSTOM_ID;

    -- 计算所有基础数据
    -- 从月均价收敛补齐表取数路径1（即TOP-SPART）的数据
    V_SQL := '
	  DROP TABLE IF EXISTS MON_REPL_COST_INFO_TMP1;
      CREATE TEMPORARY TABLE MON_REPL_COST_INFO_TMP1 AS
              WITH SOURCE_TABLE_INFO_TMP1 AS(
              SELECT VERSION_ID
                   , PERIOD_ID
                   , PERIOD_YEAR
                   '||V_SQL_FROM_PBI_PART||'
                   , REGION_CODE
                   , REGION_CN_NAME
                   , REPOFFICE_CODE
                   , REPOFFICE_CN_NAME
                   , SPART_CODE
                   , BG_CODE
                   , BG_CN_NAME
                   , OVERSEA_FLAG
				   , SOFTWARE_MARK
                   , VIEW_FLAG
                   , NVL(PROD_QTY,0) AS PROD_QTY
                   '||V_GS_DECRYPT_COST_AMT||'
                FROM '||V_FROM_AMT_TABLE||'
               WHERE DEL_FLAG = ''N''
                 AND VERSION_ID = '||V_VERSION_ID||'
                 AND VIEW_FLAG = ''PROD_SPART''
             )
             SELECT VERSION_ID
                  , PERIOD_ID
                  , PERIOD_YEAR
                  , LV0_CODE
                  , LV0_CN_NAME
                  , LV1_CODE
                  , LV1_CN_NAME
                  , LV2_CODE
                  , LV2_CN_NAME
                  , LV3_CODE
                  , LV3_CN_NAME
                  , LV4_CODE
                  , LV4_CN_NAME
                  , REGION_CODE
                  , REGION_CN_NAME
                  , REPOFFICE_CODE
                  , REPOFFICE_CN_NAME
                  , SPART_CODE
                  , BG_CODE
                  , BG_CN_NAME
                  , OVERSEA_FLAG
				  , SOFTWARE_MARK
                  , VIEW_FLAG
                  , SUM(PROD_QTY) OVER(PARTITION BY VERSION_ID, LV0_CODE, LV0_CN_NAME, LV1_CODE, LV1_CN_NAME, LV2_CODE, LV2_CN_NAME, LV3_CODE, LV3_CN_NAME, LV4_CODE, LV4_CN_NAME
                                               , REGION_CODE, REGION_CN_NAME, REPOFFICE_CODE, REPOFFICE_CN_NAME, SPART_CODE, BG_CODE, BG_CN_NAME, OVERSEA_FLAG, SOFTWARE_MARK, VIEW_FLAG
                                           ORDER BY PERIOD_YEAR, PERIOD_ID
                                      ) AS PROD_QTY
                  , SUM(NVL(RMB_COST_AMT,0)) OVER(PARTITION BY VERSION_ID, LV0_CODE, LV0_CN_NAME, LV1_CODE, LV1_CN_NAME, LV2_CODE, LV2_CN_NAME, LV3_CODE, LV3_CN_NAME, LV4_CODE, LV4_CN_NAME
                                                   , REGION_CODE, REGION_CN_NAME, REPOFFICE_CODE, REPOFFICE_CN_NAME, SPART_CODE, BG_CODE, BG_CN_NAME, OVERSEA_FLAG, SOFTWARE_MARK, VIEW_FLAG
                                               ORDER BY PERIOD_YEAR, PERIOD_ID
                                          ) AS RMB_COST_AMT
               FROM SOURCE_TABLE_INFO_TMP1
             ';

    EXECUTE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '从月均价收敛补齐表 '||V_FROM_AMT_TABLE||' 取数路径1（即TOP-SPART）的数据入到临时表，最大版本：'||V_VERSION_ID||'，默认基期：'||V_BASE_PERIOD_ID||'，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => V_SQL,
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    RAISE NOTICE '取数路径1（即TOP-SPART）的数据入到临时表';

    -- 新旧编码替换关系临时表【需要区分目录树】
    V_SQL := '
    DROP TABLE IF EXISTS CODE_REPL_INFO_TMP;
    CREATE TEMPORARY TABLE CODE_REPL_INFO_TMP AS
    WITH BASE_DIM AS
     (
      --关联出软硬件标识
      SELECT T1.VERSION_ID
           , T1.BG_CODE
           , T1.BG_CN_NAME
           , T1.GTS_TYPE
           , T1.LV1_CODE
           , T1.LV1_CN_NAME
           , T1.LV2_CODE
           , T1.LV2_CN_NAME
           , T1.LV3_CODE
           , T1.LV3_CN_NAME
           , T1.LV4_CODE
           , T1.LV4_CN_NAME
           , T1.PROD_CODE
           , T1.PROD_CN_NAME
           , T1.REPLACE_RELATION_NAME
           , T1.REPLACE_RELATION_TYPE
		   , T1.OLD_SPART_CODE
		   , T1.OLD_SPART_DESC
		   , T1.NEW_SPART_CODE
		   , T1.NEW_SPART_DESC
		   , T1.RELATION_TYPE
           , T2.SOFTWARE_MARK
        FROM FIN_DM_OPT_FOI.DM_FCST_ICT_CODE_REPL_INFO_T T1    /* 新旧编码替换关系表（24年7月版只有销售目录树的）*/
        LEFT JOIN FIN_DM_OPT_FOI.DM_FCST_ICT_CODE_REPL_SOFTWARE_INFO_T T2
          ON T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
         AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
         AND T1.RELATION_TYPE = T2.RELATION_TYPE
         AND T1.DEL_FLAG = ''N''
         AND T1.GTS_TYPE = '''||V_CODE_REPL_GTS_TYPE||'''
         AND T2.DEL_FLAG = ''N''
         AND T2.GTS_TYPE = '''||V_CODE_REPL_GTS_TYPE||'''
         AND T2.VERSION_ID = '||V_ANNL_VERSION||'
         AND T1.LV1_CODE = T2.LV1_CODE
         AND NVL(T1.LV2_CODE,''S1'') = NVL(T2.LV2_CODE,''S1'')
         AND NVL(T1.LV3_CODE,''S2'') = NVL(T2.LV3_CODE,''S2'')
         AND NVL(T1.LV4_CODE,''S3'') = NVL(T2.LV4_CODE,''S3'')
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.GTS_TYPE = T2.GTS_TYPE
         WHERE T1.VERSION_ID = '||V_MAX_REPL_VERSION_ID||' )
              SELECT VERSION_ID
                   , BG_CODE
                   , BG_CN_NAME
                   , GTS_TYPE
                   , LV1_CODE
                   , LV1_CN_NAME
                   , LV2_CODE
                   , LV2_CN_NAME
                   , LV3_CODE
                   , LV3_CN_NAME
                   , LV4_CODE
                   , LV4_CN_NAME
                   , PROD_CODE
                   , PROD_CN_NAME
                   , REPLACE_RELATION_NAME
                   , REPLACE_RELATION_TYPE
                   , REGEXP_SPLIT_TO_TABLE(OLD_SPART_CODE,'','') AS SPART_CODE
                   , OLD_SPART_DESC AS SPART_DESC
				   , SOFTWARE_MARK
                   , ''OLD'' AS CODE_TYPE
                   , RELATION_TYPE
                FROM BASE_DIM
              UNION ALL
             SELECT VERSION_ID
                   , BG_CODE
                   , BG_CN_NAME
                   , GTS_TYPE
                   , LV1_CODE
                   , LV1_CN_NAME
                   , LV2_CODE
                   , LV2_CN_NAME
                   , LV3_CODE
                   , LV3_CN_NAME
                   , LV4_CODE
                   , LV4_CN_NAME
                   , PROD_CODE
                   , PROD_CN_NAME
                   , REPLACE_RELATION_NAME
                   , REPLACE_RELATION_TYPE
                   , REGEXP_SPLIT_TO_TABLE(NEW_SPART_CODE,'','') AS SPART_CODE
                   , NEW_SPART_DESC AS SPART_DESC
				   , SOFTWARE_MARK
                   , ''NEW'' AS CODE_TYPE
                   , RELATION_TYPE
                FROM BASE_DIM
    ';

    EXECUTE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '目录树：'||V_CODE_REPL_GTS_TYPE||'，新旧编码替换关系临时表临时表的数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => V_SQL,
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );
    
    RAISE NOTICE'新旧编码替换关系临时表临时表的数据';

    -- TOP-SPART关联新旧编码替换关系临时表
    -- 使用月度累计是从202201（起始点）到终止点（202407YTD）一致累计发货数量与金额，再计算分月累计均本，不以年度进行切分（导致从202301重新累计）
    DROP TABLE IF EXISTS MON_REPL_COST_INFO_TMP2;
    CREATE TEMPORARY TABLE MON_REPL_COST_INFO_TMP2 AS
    SELECT DISTINCT T1.VERSION_ID           -- 版本ID
         , T1.PERIOD_ID                     -- 会计期
         , T1.PERIOD_YEAR                   -- 会计年
         , T1.LV0_CODE                      -- 重量级团队LV0编码
         , T1.LV0_CN_NAME                   -- 重量级团队LV0名称
         , T1.LV1_CODE                      -- 重量级团队LV1编码
         , T1.LV1_CN_NAME                   -- 重量级团队LV1名称
         , T1.LV2_CODE                      -- 重量级团队LV2编码
         , T1.LV2_CN_NAME                   -- 重量级团队LV2名称
         , T1.LV3_CODE                      -- 重量级团队LV3编码
         , T1.LV3_CN_NAME                   -- 重量级团队LV3名称
         , T1.LV4_CODE                      -- 重量级团队LV4编码
         , T1.LV4_CN_NAME                   -- 重量级团队LV4名称
         , T1.REGION_CODE                   -- 地区部编码
         , T1.REGION_CN_NAME                -- 地区部中文名称
         , T1.REPOFFICE_CODE                -- 代表处编码
         , T1.REPOFFICE_CN_NAME             -- 代表处中文名称
         , T1.SPART_CODE                    -- SPART编码
         , T2.SPART_DESC                    -- SPART描述
         , T1.BG_CODE                       -- BG编码
         , T1.BG_CN_NAME                    -- BG中文名称
         , T1.OVERSEA_FLAG                  -- 国内海外标识
		 , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG                     -- 路径1：PROD_SPART/路径2：DIMENSION
         , T2.REPLACE_RELATION_NAME         -- 替换关系名称
         , T2.REPLACE_RELATION_TYPE         -- 新老编码替换类型（一对一  、一对多 、多对多）
         , T2.RELATION_TYPE                 -- 关系（ 替换 、收编）
         , T2.CODE_TYPE                     -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , T1.PROD_QTY                      -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , T1.RMB_COST_AMT::NUMERIC         -- 标准成本
         , (CASE WHEN T1.PROD_QTY = 0 THEN 0 ELSE T1.RMB_COST_AMT/T1.PROD_QTY END)::NUMERIC AS RMB_AVG_AMT -- 月累计均价（月累计金额/月累计量）
      FROM MON_REPL_COST_INFO_TMP1 T1  -- 从月均价收敛补齐表取数路径1（即TOP-SPART）的数据
      JOIN CODE_REPL_INFO_TMP T2  -- 新旧编码替换关系临时表
        ON ((T1.LV1_CODE = T2.LV1_CODE AND T2.LV2_CODE = 'ALL' AND T2.LV3_CODE = 'ALL' AND T2.LV4_CODE = 'ALL') OR
            (T1.LV1_CODE = T2.LV1_CODE AND T1.LV2_CODE = T2.LV2_CODE AND T2.LV3_CODE = 'ALL' AND T2.LV4_CODE = 'ALL') OR
            (T1.LV1_CODE = T2.LV1_CODE AND T1.LV2_CODE = T2.LV2_CODE AND T1.LV3_CODE = T2.LV3_CODE AND T2.LV4_CODE = 'ALL') OR
            (T1.LV1_CODE = T2.LV1_CODE AND T1.LV2_CODE = T2.LV2_CODE AND T1.LV3_CODE = T2.LV3_CODE AND T1.LV4_CODE = T2.LV4_CODE)
           )
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.SPART_CODE = T2.SPART_CODE
	   AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
      JOIN BASE_CUS_DIM_TMP T3  -- 虚化维度临时表
        ON T2.REPLACE_RELATION_NAME = T3.REPLACE_RELATION_NAME
		AND T2.SOFTWARE_MARK = T3.SOFTWARE_MARK   -- 202410版本新增
    ;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '新旧编码替换关系表的版本ID：'||V_MAX_REPL_VERSION_ID||'，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );
    
    RAISE NOTICE'新旧编码替换关系表的版本ID:%', V_MAX_REPL_VERSION_ID;

    -- 获取每年最大月累计的会计期
    DROP TABLE IF EXISTS YEAR_MAX_PERIOD_ID_TMP;
    CREATE TEMPORARY TABLE YEAR_MAX_PERIOD_ID_TMP AS
    SELECT VERSION_ID
         , MAX(PERIOD_ID) AS PERIOD_ID
      FROM MON_REPL_COST_INFO_TMP2
     GROUP BY VERSION_ID
    ;
    
    RAISE NOTICE'888888888';

    -- 该虚化SPART下第I个SPART期间（T-1年+T年YTD）金额临时表
    DROP TABLE IF EXISTS SPART_LV4_REPL_INTERVAL_TMP;
    CREATE TEMPORARY TABLE SPART_LV4_REPL_INTERVAL_TMP AS
    SELECT T1.VERSION_ID                     -- 版本ID
         , T1.LV0_CODE                       -- 重量级团队LV0编码
         , T1.LV0_CN_NAME                    -- 重量级团队LV0名称
         , T1.LV1_CODE                       -- 重量级团队LV1编码
         , T1.LV1_CN_NAME                    -- 重量级团队LV1名称
         , T1.LV2_CODE                       -- 重量级团队LV2编码
         , T1.LV2_CN_NAME                    -- 重量级团队LV2名称
         , T1.LV3_CODE                       -- 重量级团队LV3编码
         , T1.LV3_CN_NAME                    -- 重量级团队LV3名称
         , T1.LV4_CODE                       -- 重量级团队LV4编码
         , T1.LV4_CN_NAME                    -- 重量级团队LV4名称
         , T1.REGION_CODE                    -- 地区部编码
         , T1.REGION_CN_NAME                 -- 地区部中文名称
         , T1.REPOFFICE_CODE                 -- 代表处编码
         , T1.REPOFFICE_CN_NAME              -- 代表处中文名称
         , T1.SPART_CODE                     -- SPART编码
         , T1.SPART_DESC                     -- SPART描述
         , T1.BG_CODE                        -- BG编码
         , T1.BG_CN_NAME                     -- BG中文名称
         , T1.OVERSEA_FLAG                   -- 国内海外标识
		 , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
         , T1.REPLACE_RELATION_NAME          -- 替换关系名称
         , T1.REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
         , T1.RELATION_TYPE                  -- 关系（ 替换 、收编）
         , T1.CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , SUM(T1.RMB_COST_AMT)  AS RMB_COST_AMT -- 标准成本
      FROM MON_REPL_COST_INFO_TMP2 T1  -- TOP-SPART关联新旧编码替换关系临时表
      JOIN YEAR_MAX_PERIOD_ID_TMP T2
        ON T1.VERSION_ID = T2.VERSION_ID
       AND T1.PERIOD_ID = T2.PERIOD_ID
     WHERE SUBSTR(T1.PERIOD_ID,1,4) >= (TO_CHAR(CURRENT_DATE,'YYYY')::NUMERIC)-1  /* T-1年至T年YTD */
     GROUP BY T1.VERSION_ID
         , T1.LV0_CODE
         , T1.LV0_CN_NAME
         , T1.LV1_CODE
         , T1.LV1_CN_NAME
         , T1.LV2_CODE
         , T1.LV2_CN_NAME
         , T1.LV3_CODE
         , T1.LV3_CN_NAME
         , T1.LV4_CODE
         , T1.LV4_CN_NAME
         , T1.REGION_CODE
         , T1.REGION_CN_NAME
         , T1.REPOFFICE_CODE
         , T1.REPOFFICE_CN_NAME
         , T1.SPART_CODE
         , T1.SPART_DESC
         , T1.BG_CODE
         , T1.BG_CN_NAME
         , T1.OVERSEA_FLAG
		 , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG
         , T1.REPLACE_RELATION_NAME
         , T1.REPLACE_RELATION_TYPE
         , T1.RELATION_TYPE
         , T1.CODE_TYPE
    ;

    RAISE NOTICE'该虚化SPART下第I个SPART期间（T-1年+T年YTD）金额临时表';

  V_SQL := '
  INSERT INTO '||V_TO_WEIGHT_TABLE||'(
         VERSION_ID,          
         LV0_CODE,            
         LV0_CN_NAME,         
         LV1_CODE,            
         LV1_CN_NAME,         
         LV2_CODE,            
         LV2_CN_NAME,         
         LV3_CODE,            
         LV3_CN_NAME,         
         LV4_CODE,            
         LV4_CN_NAME,         
         REGION_CODE,         
         REGION_CN_NAME,      
         REPOFFICE_CODE,      
         REPOFFICE_CN_NAME,   
         SPART_CODE,          
         SPART_DESC,          
         GROUP_LEVEL,  
         BG_CODE,             
         BG_CN_NAME,          
         OVERSEA_FLAG,        
         SOFTWARE_MARK,
         VIEW_FLAG,           
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,       
         CODE_TYPE,           
         WEIGHT_RATE,
         GRANULARITY_TYPE,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
    -- 该虚化SPART期间（T-1年+T年YTD）金额临时表
    WITH SPART_LV4_INTERVAL_TMP AS(
    SELECT VERSION_ID                     -- 版本ID
         , LV0_CODE                       -- 重量级团队LV0编码
         , LV0_CN_NAME                    -- 重量级团队LV0名称
         , LV1_CODE                       -- 重量级团队LV1编码
         , LV1_CN_NAME                    -- 重量级团队LV1名称
         , LV2_CODE                       -- 重量级团队LV2编码
         , LV2_CN_NAME                    -- 重量级团队LV2名称
         , LV3_CODE                       -- 重量级团队LV3编码
         , LV3_CN_NAME                    -- 重量级团队LV3名称
         , LV4_CODE                       -- 重量级团队LV4编码
         , LV4_CN_NAME                    -- 重量级团队LV4名称
         , REGION_CODE                    -- 地区部编码
         , REGION_CN_NAME                 -- 地区部中文名称
         , REPOFFICE_CODE                 -- 代表处编码
         , REPOFFICE_CN_NAME              -- 代表处中文名称
         , SPART_CODE                     -- SPART编码
         , SPART_DESC                     -- SPART描述
         , BG_CODE                        -- BG编码
         , BG_CN_NAME                     -- BG中文名称
         , OVERSEA_FLAG                   -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
         , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , SUM(RMB_COST_AMT) AS RMB_COST_AMT -- 标准成本
      FROM SPART_LV4_REPL_INTERVAL_TMP  -- 多SPART新或老编码SPART期间（T-1年至T年YTD）临时表
     GROUP BY VERSION_ID
         , LV0_CODE
         , LV0_CN_NAME
         , LV1_CODE
         , LV1_CN_NAME
         , LV2_CODE
         , LV2_CN_NAME
         , LV3_CODE
         , LV3_CN_NAME
         , LV4_CODE
         , LV4_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , SPART_CODE
         , SPART_DESC
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
         , CODE_TYPE
    )
    -- 该虚化SPART下第I个SPART期间（T-1年+T年YTD）金额/该虚化SPART期间（T-1年+T年YTD）金额
    SELECT T1.VERSION_ID                     -- 版本ID
         , T1.LV0_CODE                       -- 重量级团队LV0编码
         , T1.LV0_CN_NAME                    -- 重量级团队LV0名称
         , T1.LV1_CODE                       -- 重量级团队LV1编码
         , T1.LV1_CN_NAME                    -- 重量级团队LV1名称
         , T1.LV2_CODE                       -- 重量级团队LV2编码
         , T1.LV2_CN_NAME                    -- 重量级团队LV2名称
         , T1.LV3_CODE                       -- 重量级团队LV3编码
         , T1.LV3_CN_NAME                    -- 重量级团队LV3名称
         , T1.LV4_CODE                       -- 重量级团队LV4编码
         , T1.LV4_CN_NAME                    -- 重量级团队LV4名称
         , T1.REGION_CODE                    -- 地区部编码
         , T1.REGION_CN_NAME                 -- 地区部中文名称
         , T1.REPOFFICE_CODE                 -- 代表处编码
         , T1.REPOFFICE_CN_NAME              -- 代表处中文名称
         , T1.SPART_CODE                     -- SPART编码
         , T1.SPART_DESC                     -- SPART描述
		 , ''SPART'' AS GROUP_LEVEL
         , T1.BG_CODE                        -- BG编码
         , T1.BG_CN_NAME                     -- BG中文名称
         , T1.OVERSEA_FLAG                   -- 国内海外标识
		 , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
         , T1.REPLACE_RELATION_NAME          -- 替换关系名称
         , T1.REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
         , T1.RELATION_TYPE                  -- 关系（ 替换 、收编）
         , T1.CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , (CASE WHEN T2.RMB_COST_AMT = 0 THEN 0 ELSE ROUND(T1.RMB_COST_AMT/T2.RMB_COST_AMT,10) END) AS WEIGHT_RATE  -- 权重
		 , '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
		 ,  -1 AS CREATED_BY
 	     , CURRENT_TIMESTAMP AS CREATION_DATE
 	     , -1 AS LAST_UPDATED_BY
 	     , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
 	     , ''N'' AS DEL_FLAG
      FROM SPART_LV4_REPL_INTERVAL_TMP T1  -- 该虚化SPART下第I个SPART期间（T-1年+T年YTD）金额临时表
      LEFT JOIN SPART_LV4_INTERVAL_TMP T2  -- 该虚化SPART期间（T-1年+T年YTD）金额临时表
        ON T1.VERSION_ID = T2.VERSION_ID
       AND T1.LV0_CODE = T2.LV0_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.LV2_CODE = T2.LV2_CODE
       AND T1.LV3_CODE = T2.LV3_CODE
       AND T1.LV4_CODE = T2.LV4_CODE
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.SPART_CODE = T2.SPART_CODE
       AND T1.VIEW_FLAG = T2.VIEW_FLAG
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.CODE_TYPE = T2.CODE_TYPE
	   AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
    ';
    EXECUTE IMMEDIATE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => 'SPART层级权重数据入到结果表：'||V_TO_WEIGHT_TABLE||'，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );
    
    -- 计算每个SPART（包括单SPART、多SPART）在LV4层级的指数
  V_SQL := '
    INSERT INTO SPART_LV4_COST_INDEX_TMP(
         VERSION_ID                      -- 版本ID
       , PERIOD_ID                       -- 会计期
       , PERIOD_YEAR                     -- 会计年
       , BASE_PERIOD_ID                  -- 基期
       , LV0_CODE                        -- 重量级团队LV0编码
       , LV0_CN_NAME                     -- 重量级团队LV0名称
       , LV1_CODE                        -- 重量级团队LV1编码
       , LV1_CN_NAME                     -- 重量级团队LV1名称
       , LV2_CODE                        -- 重量级团队LV2编码
       , LV2_CN_NAME                     -- 重量级团队LV2名称
       , LV3_CODE                        -- 重量级团队LV3编码
       , LV3_CN_NAME                     -- 重量级团队LV3名称
       , LV4_CODE                        -- 重量级团队LV4编码
       , LV4_CN_NAME                     -- 重量级团队LV4名称
       , REGION_CODE                     -- 地区部编码
       , REGION_CN_NAME                  -- 地区部中文名称
       , REPOFFICE_CODE                  -- 代表处编码
       , REPOFFICE_CN_NAME               -- 代表处中文名称
       , SPART_CODE                      -- SPART编码
       , SPART_DESC                      -- SPART描述
       , BG_CODE                         -- BG编码
       , BG_CN_NAME                      -- BG中文名称
       , OVERSEA_FLAG                    -- 国内海外标识
	   , SOFTWARE_MARK
       , VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
       , REPLACE_RELATION_NAME           -- 替换关系名称
       , REPLACE_RELATION_TYPE           -- 新老编码替换类型（一对一  、一对多 、多对多）
       , RELATION_TYPE                   -- 关系（ 替换 、收编）
       , CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
       , PROD_QTY                        -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
       , RMB_COST_AMT                    -- 标准成本
       , COST_INDEX                      -- 成本指数
    )
    -- 默认基期临时表（单SPART月度、月累计需要用到）
    WITH SPART_LV4_BASE_COST_TMP AS(
    SELECT VERSION_ID                     -- 版本ID
         , PERIOD_ID                      -- 会计期
         , PERIOD_YEAR                    -- 会计年
         , LV0_CODE                       -- 重量级团队LV0编码
         , LV0_CN_NAME                    -- 重量级团队LV0名称
         , LV1_CODE                       -- 重量级团队LV1编码
         , LV1_CN_NAME                    -- 重量级团队LV1名称
         , LV2_CODE                       -- 重量级团队LV2编码
         , LV2_CN_NAME                    -- 重量级团队LV2名称
         , LV3_CODE                       -- 重量级团队LV3编码
         , LV3_CN_NAME                    -- 重量级团队LV3名称
         , LV4_CODE                       -- 重量级团队LV4编码
         , LV4_CN_NAME                    -- 重量级团队LV4名称
         , REGION_CODE                    -- 地区部编码
         , REGION_CN_NAME                 -- 地区部中文名称
         , REPOFFICE_CODE                 -- 代表处编码
         , REPOFFICE_CN_NAME              -- 代表处中文名称
         , SPART_CODE                     -- SPART编码
         , SPART_DESC                     -- SPART描述
         , BG_CODE                        -- BG编码
         , BG_CN_NAME                     -- BG中文名称
         , OVERSEA_FLAG                   -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
         , REPLACE_RELATION_NAME          -- 替换关系名称
         , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
         , RELATION_TYPE                  -- 关系（ 替换 、收编）
         , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , PROD_QTY                       -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , RMB_COST_AMT                   -- 标准成本
         , RMB_AVG_AMT                    -- 均本
      FROM MON_REPL_COST_INFO_TMP2  -- TOP-SPART关联新旧编码替换关系临时表
     WHERE PERIOD_ID = '||V_BASE_PERIOD_ID||'  -- 默认基期
    ),
    SPART_LV4_WEIGHT_TMP AS(
    SELECT VERSION_ID        
         , LV0_CODE          
         , LV1_CODE          
         , LV2_CODE          
         , LV3_CODE          
         , LV4_CODE          
         , REGION_CODE       
         , REPOFFICE_CODE    
         , SPART_CODE        
         , BG_CODE              
         , OVERSEA_FLAG         
         , SOFTWARE_MARK
         , VIEW_FLAG            
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE        
         , CODE_TYPE            
         , WEIGHT_RATE
      FROM '||V_TO_WEIGHT_TABLE||'
      WHERE VERSION_ID = '||V_VERSION_ID||'
      AND GRANULARITY_TYPE = '''||F_GRANULARITY_TYPE||'''
      AND GROUP_LEVEL = ''SPART''
    )
    -- 多SPART以及单SPART中每个SPART到关系名称、LV3.5层级的指数
    SELECT T1.VERSION_ID                     -- 版本ID
         , T1.PERIOD_ID                      -- 会计期
         , T1.PERIOD_YEAR                    -- 会计年
         , '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID -- 基期
         , T1.LV0_CODE                       -- 重量级团队LV0编码
         , T1.LV0_CN_NAME                    -- 重量级团队LV0名称
         , T1.LV1_CODE                       -- 重量级团队LV1编码
         , T1.LV1_CN_NAME                    -- 重量级团队LV1名称
         , T1.LV2_CODE                       -- 重量级团队LV2编码
         , T1.LV2_CN_NAME                    -- 重量级团队LV2名称
         , T1.LV3_CODE                       -- 重量级团队LV3编码
         , T1.LV3_CN_NAME                    -- 重量级团队LV3名称
         , T1.LV4_CODE                       -- 重量级团队LV4编码
         , T1.LV4_CN_NAME                    -- 重量级团队LV4名称
         , T1.REGION_CODE                    -- 地区部编码
         , T1.REGION_CN_NAME                 -- 地区部中文名称
         , T1.REPOFFICE_CODE                 -- 代表处编码
         , T1.REPOFFICE_CN_NAME              -- 代表处中文名称
         , T1.SPART_CODE                     -- SPART编码
         , T1.SPART_DESC                     -- SPART描述
         , T1.BG_CODE                        -- BG编码
         , T1.BG_CN_NAME                     -- BG中文名称
         , T1.OVERSEA_FLAG                   -- 国内海外标识
		 , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
         , T1.REPLACE_RELATION_NAME          -- 替换关系名称
         , T1.REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
         , T1.RELATION_TYPE                  -- 关系（ 替换 、收编）
         , T1.CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , T1.PROD_QTY                          -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , T1.RMB_COST_AMT                      -- 标准成本
         , (CASE WHEN T2.RMB_AVG_AMT = 0 THEN 0 ELSE ROUND(T1.RMB_AVG_AMT/T2.RMB_AVG_AMT*100*T3.WEIGHT_RATE,10) END) AS COST_INDEX   -- 成本指数
      FROM MON_REPL_COST_INFO_TMP2 T1  -- TOP-SPART关联新旧编码替换关系临时表
      LEFT JOIN SPART_LV4_BASE_COST_TMP T2  -- SPART默认基期均本
        ON T1.VERSION_ID = T2.VERSION_ID
       AND T1.LV0_CODE = T2.LV0_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.LV2_CODE = T2.LV2_CODE
       AND T1.LV3_CODE = T2.LV3_CODE
       AND T1.LV4_CODE = T2.LV4_CODE
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.SPART_CODE = T2.SPART_CODE
       AND T1.VIEW_FLAG = T2.VIEW_FLAG
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.CODE_TYPE = T2.CODE_TYPE
	   AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
      LEFT JOIN SPART_LV4_WEIGHT_TMP T3  -- 该虚化SPART下第I个SPART期间（T-1年+T年YTD）金额/该虚化SPART期间（T-1年+T年YTD）金额
        ON T1.VERSION_ID = T3.VERSION_ID
       AND T1.LV0_CODE = T3.LV0_CODE
       AND T1.LV1_CODE = T3.LV1_CODE
       AND T1.LV2_CODE = T3.LV2_CODE
       AND T1.LV3_CODE = T3.LV3_CODE
       AND T1.LV4_CODE = T3.LV4_CODE
       AND T1.REGION_CODE = T3.REGION_CODE
       AND T1.REPOFFICE_CODE = T3.REPOFFICE_CODE
       AND T1.SPART_CODE = T3.SPART_CODE
       AND T1.VIEW_FLAG = T3.VIEW_FLAG
       AND T1.BG_CODE = T3.BG_CODE
       AND T1.OVERSEA_FLAG = T3.OVERSEA_FLAG
       AND T1.REPLACE_RELATION_NAME = T3.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T3.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T3.RELATION_TYPE
       AND T1.CODE_TYPE = T3.CODE_TYPE
	   AND T1.SOFTWARE_MARK = T3.SOFTWARE_MARK   -- 202410版本新增
    ';
    EXECUTE IMMEDIATE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '计算每个SPART（包括单SPART、多SPART）在LV4层级的指数，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    RAISE NOTICE'计算每个SPART（包括单SPART、多SPART）在LV4层级的指数';

    -- 根据虚化维度表中 PARENT_LEVEL 判断需要虚化到哪个层级
    SELECT PARENT_LEVEL INTO V_PARENT_LEVEL FROM BASE_CUS_DIM_TMP;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '需要虚化的层级是：'||V_PARENT_LEVEL,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    RAISE NOTICE'66666666';

  V_SQL := '
  INSERT INTO '||V_TO_WEIGHT_TABLE||'(
         VERSION_ID,          
         LV0_CODE,            
         LV0_CN_NAME,         
         LV1_CODE,            
         LV1_CN_NAME,         
         LV2_CODE,            
         LV2_CN_NAME,         
         LV3_CODE,            
         LV3_CN_NAME,         
         LV4_CODE,            
         LV4_CN_NAME,         
         REGION_CODE,         
         REGION_CN_NAME,      
         REPOFFICE_CODE,      
         REPOFFICE_CN_NAME,   
         GROUP_LEVEL,  
         BG_CODE,             
         BG_CN_NAME,          
         OVERSEA_FLAG,        
         SOFTWARE_MARK,
         VIEW_FLAG,           
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,       
         CODE_TYPE,           
         WEIGHT_RATE,
         GRANULARITY_TYPE,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
      -- 关系名称期间（T-1年+T年YTD）金额
      WITH SAME_REPL_LV4_INTERVAL_TMP AS(
      SELECT VERSION_ID                     -- 版本ID
             , LV0_CODE                       -- 重量级团队LV0编码
             , LV0_CN_NAME                    -- 重量级团队LV0名称
             , LV1_CODE                       -- 重量级团队LV1编码
             , LV1_CN_NAME                    -- 重量级团队LV1名称
             , LV2_CODE                       -- 重量级团队LV2编码
             , LV2_CN_NAME                    -- 重量级团队LV2名称
             , LV3_CODE                       -- 重量级团队LV3编码
             , LV3_CN_NAME                    -- 重量级团队LV3名称
             , LV4_CODE                       -- 重量级团队LV4编码
             , LV4_CN_NAME                    -- 重量级团队LV4名称
             , REGION_CODE                    -- 地区部编码
             , REGION_CN_NAME                 -- 地区部中文名称
             , REPOFFICE_CODE                 -- 代表处编码
             , REPOFFICE_CN_NAME              -- 代表处中文名称
             , BG_CODE                        -- BG编码
             , BG_CN_NAME                     -- BG中文名称
             , OVERSEA_FLAG                   -- 国内海外标识
             , REPLACE_RELATION_NAME          -- 替换关系名称
             , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
             , RELATION_TYPE                  -- 关系（ 替换 、收编）
			 , SOFTWARE_MARK
             , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
             , ''SAME'' AS CODE_TYPE            -- 编码类型（NEW:新编码  OLD: 旧编码 SAME：同基数）
             , SUM(RMB_COST_AMT)  AS RMB_COST_AMT -- 标准成本
          FROM SPART_LV4_REPL_INTERVAL_TMP
         GROUP BY VERSION_ID
             , LV0_CODE
             , LV0_CN_NAME
             , LV1_CODE
             , LV1_CN_NAME
             , LV2_CODE
             , LV2_CN_NAME
             , LV3_CODE
             , LV3_CN_NAME
             , LV4_CODE
             , LV4_CN_NAME
             , REGION_CODE
             , REGION_CN_NAME
             , REPOFFICE_CODE
             , REPOFFICE_CN_NAME
             , BG_CODE
             , BG_CN_NAME
             , OVERSEA_FLAG
             , REPLACE_RELATION_NAME
             , REPLACE_RELATION_TYPE
             , RELATION_TYPE
			 , SOFTWARE_MARK
             , VIEW_FLAG
      ),
      -- LV3层级（T-1年+T年YTD）金额
      SAME_REPL_LV3_INTERVAL_TMP AS(
      SELECT VERSION_ID                     -- 版本ID
             , LV0_CODE                       -- 重量级团队LV0编码
             , LV0_CN_NAME                    -- 重量级团队LV0名称
             , LV1_CODE                       -- 重量级团队LV1编码
             , LV1_CN_NAME                    -- 重量级团队LV1名称
             , LV2_CODE                       -- 重量级团队LV2编码
             , LV2_CN_NAME                    -- 重量级团队LV2名称
             , LV3_CODE                       -- 重量级团队LV3编码
             , LV3_CN_NAME                    -- 重量级团队LV3名称
             , REGION_CODE                    -- 地区部编码
             , REGION_CN_NAME                 -- 地区部中文名称
             , REPOFFICE_CODE                 -- 代表处编码
             , REPOFFICE_CN_NAME              -- 代表处中文名称
             , BG_CODE                        -- BG编码
             , BG_CN_NAME                     -- BG中文名称
             , REPLACE_RELATION_NAME          -- 替换关系名称
             , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
             , RELATION_TYPE                  -- 关系（ 替换 、收编）
             , OVERSEA_FLAG                   -- 国内海外标识
			 , SOFTWARE_MARK
             , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
             , ''SAME'' AS CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 SAME：同基数）
             , SUM(RMB_COST_AMT)  AS RMB_COST_AMT -- 标准成本
          FROM SAME_REPL_LV4_INTERVAL_TMP
         GROUP BY VERSION_ID
             , LV0_CODE
             , LV0_CN_NAME
             , LV1_CODE
             , LV1_CN_NAME
             , LV2_CODE
             , LV2_CN_NAME
             , LV3_CODE
             , LV3_CN_NAME
             , REGION_CODE
             , REGION_CN_NAME
             , REPOFFICE_CODE
             , REPOFFICE_CN_NAME
             , BG_CODE
             , BG_CN_NAME
             , REPLACE_RELATION_NAME
             , REPLACE_RELATION_TYPE
             , RELATION_TYPE
             , OVERSEA_FLAG
			 , SOFTWARE_MARK
             , VIEW_FLAG
      )
      -- 虚化后的权重计算值：关系名称期间（T-1年+T年YTD）金额/LV3层级（T-1年+T年YTD）金额
      SELECT T1.VERSION_ID                     -- 版本ID
             , T1.LV0_CODE                       -- 重量级团队LV0编码
             , T1.LV0_CN_NAME                    -- 重量级团队LV0名称
             , T1.LV1_CODE                       -- 重量级团队LV1编码
             , T1.LV1_CN_NAME                    -- 重量级团队LV1名称
             , T1.LV2_CODE                       -- 重量级团队LV2编码
             , T1.LV2_CN_NAME                    -- 重量级团队LV2名称
             , T1.LV3_CODE                       -- 重量级团队LV3编码
             , T1.LV3_CN_NAME                    -- 重量级团队LV3名称
             , T1.LV4_CODE                       -- 重量级团队LV4编码
             , T1.LV4_CN_NAME                    -- 重量级团队LV4名称
             , T1.REGION_CODE                    -- 地区部编码
             , T1.REGION_CN_NAME                 -- 地区部中文名称
             , T1.REPOFFICE_CODE                 -- 代表处编码
             , T1.REPOFFICE_CN_NAME              -- 代表处中文名称
             , ''REPLACE_RELATION'' AS GROUP_LEVEL
             , T1.BG_CODE                        -- BG编码
             , T1.BG_CN_NAME                     -- BG中文名称
             , T1.OVERSEA_FLAG                   -- 国内海外标识
	         , T1.SOFTWARE_MARK
	         , T1.VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
             , T1.REPLACE_RELATION_NAME          -- 替换关系名称
             , T1.REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
             , T1.RELATION_TYPE                  -- 关系（ 替换 、收编） 
             , T1.CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 SAME：同基数）
             , (CASE WHEN T2.RMB_COST_AMT = 0 THEN 0 ELSE ROUND(T1.RMB_COST_AMT/T2.RMB_COST_AMT,10) END) AS WEIGHT_RATE  -- 权重
             , '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
             ,  -1 AS CREATED_BY
             , CURRENT_TIMESTAMP AS CREATION_DATE
             , -1 AS LAST_UPDATED_BY
             , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
             , ''N'' AS DEL_FLAG
        FROM SAME_REPL_LV4_INTERVAL_TMP T1  -- 该虚化SPART下第I个SPART期间（T-1年+T年YTD）金额临时表
        LEFT JOIN SAME_REPL_LV3_INTERVAL_TMP T2  -- 该虚化SPART期间（T-1年+T年YTD）金额临时表
          ON T1.VERSION_ID = T2.VERSION_ID
         AND T1.LV0_CODE = T2.LV0_CODE
         AND T1.LV1_CODE = T2.LV1_CODE
         AND T1.LV2_CODE = T2.LV2_CODE
         AND T1.LV3_CODE = T2.LV3_CODE
         AND T1.VIEW_FLAG = T2.VIEW_FLAG
         AND T1.REGION_CODE = T2.REGION_CODE
         AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
         AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
         AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
         AND T1.RELATION_TYPE = T2.RELATION_TYPE
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
         AND T1.CODE_TYPE = T2.CODE_TYPE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
    ';
    EXECUTE IMMEDIATE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => 'REPLACE_RELATION层级权重数据入到结果表：'||V_TO_WEIGHT_TABLE||'，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

  V_SQL := '
      INSERT INTO SAME_BASE_LV4_INDEX_TMP(
             VERSION_ID           
           , PERIOD_ID            
	       , LV0_CODE             
	       , LV0_CN_NAME          
	       , LV1_CODE             
	       , LV1_CN_NAME          
	       , LV2_CODE             
	       , LV2_CN_NAME          
	       , LV3_CODE             
	       , LV3_CN_NAME          
	       , LV4_CODE             
	       , LV4_CN_NAME          
	       , REGION_CODE          
	       , REGION_CN_NAME       
	       , REPOFFICE_CODE       
	       , REPOFFICE_CN_NAME    
	       , BG_CODE              
	       , BG_CN_NAME           
	       , OVERSEA_FLAG         
	       , REPLACE_RELATION_NAME
	       , REPLACE_RELATION_TYPE
	       , RELATION_TYPE        
	       , SOFTWARE_MARK        
	       , VIEW_FLAG            
           , CODE_TYPE            
           , COST_INDEX 
	    )
      -- 取出同基指数的SPART层级结果值
      SELECT T1.VERSION_ID                     -- 版本ID
             , T2.PERIOD_ID
             , T1.LV0_CODE                       -- 重量级团队LV0编码
             , T1.LV0_CN_NAME                    -- 重量级团队LV0名称
             , T1.LV1_CODE                       -- 重量级团队LV1编码
             , T1.LV1_CN_NAME                    -- 重量级团队LV1名称
             , T1.LV2_CODE                       -- 重量级团队LV2编码
             , T1.LV2_CN_NAME                    -- 重量级团队LV2名称
             , T1.LV3_CODE                       -- 重量级团队LV3编码
             , T1.LV3_CN_NAME                    -- 重量级团队LV3名称
             , T1.LV4_CODE                       -- 重量级团队LV4编码
             , T1.LV4_CN_NAME                    -- 重量级团队LV4名称
             , T1.REGION_CODE                    -- 地区部编码
             , T1.REGION_CN_NAME                 -- 地区部中文名称
             , T1.REPOFFICE_CODE                 -- 代表处编码
             , T1.REPOFFICE_CN_NAME              -- 代表处中文名称
             , T1.BG_CODE                        -- BG编码
             , T1.BG_CN_NAME                     -- BG中文名称
             , T1.OVERSEA_FLAG                   -- 国内海外标识
             , T1.REPLACE_RELATION_NAME          -- 替换关系名称
             , T1.REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
             , T1.RELATION_TYPE                  -- 关系（ 替换 、收编）
			 , T1.SOFTWARE_MARK
             , T1.VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
             , T1.CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 SAME：同基数）
             , T2.COST_INDEX*T1.WEIGHT_RATE AS COST_INDEX  -- 同基指数
        FROM '||V_TO_WEIGHT_TABLE||' T1  -- 该虚化权重临时表
        INNER JOIN '||V_SAME_INDEX_TABLE||' T2  -- 同基指数结果表
          ON T1.VERSION_ID = T2.VERSION_ID
         AND T1.LV4_CODE = T2.PARENT_CODE
         AND T1.REGION_CODE = T2.REGION_CODE
         AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
         AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
         AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
         AND T1.RELATION_TYPE = T2.RELATION_TYPE
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
	    WHERE UPPER(T2.GROUP_LEVEL) = ''SPART''
		AND T1.VERSION_ID = '||V_VERSION_ID||'
	    AND T1.GRANULARITY_TYPE = '''||F_GRANULARITY_TYPE||'''
	    AND T1.GROUP_LEVEL = ''REPLACE_RELATION''
		AND T1.CODE_TYPE = ''SAME''';
    EXECUTE IMMEDIATE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '同基指数的底层数据值插入临时表',--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );
	
  IF(V_PARENT_LEVEL = 'LV3') THEN
  -- 虚化到LV3层级
  V_SQL := '
  INSERT INTO '||V_TO_WEIGHT_TABLE||' (
         VERSION_ID,          
         LV0_CODE,            
         LV0_CN_NAME,         
         LV1_CODE,            
         LV1_CN_NAME,         
         LV2_CODE,            
         LV2_CN_NAME,         
         LV3_CODE,            
         LV3_CN_NAME,         
         LV4_CODE,            
         LV4_CN_NAME,         
         REGION_CODE,         
         REGION_CN_NAME,      
         REPOFFICE_CODE,      
         REPOFFICE_CN_NAME,   
         SPART_CODE,
         SPART_DESC,
         GROUP_LEVEL,  
         BG_CODE,             
         BG_CN_NAME,          
         OVERSEA_FLAG,        
         SOFTWARE_MARK,
         VIEW_FLAG,           
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,       
         CODE_TYPE,           
         WEIGHT_RATE,
         GRANULARITY_TYPE,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
      WITH SPART_LV3_REPL_INTERVAL_TMP AS(
      SELECT VERSION_ID            -- 版本ID
           , LV0_CODE                       -- 重量级团队LV0编码
           , LV0_CN_NAME                    -- 重量级团队LV0名称
           , LV1_CODE                       -- 重量级团队LV1编码
           , LV1_CN_NAME                    -- 重量级团队LV1名称
           , LV2_CODE                       -- 重量级团队LV2编码
           , LV2_CN_NAME                    -- 重量级团队LV2名称
           , LV3_CODE                       -- 重量级团队LV3编码
           , LV3_CN_NAME                    -- 重量级团队LV3名称
           , LV4_CODE                       -- 重量级团队LV4编码
           , LV4_CN_NAME                    -- 重量级团队LV4名称
           , REGION_CODE                    -- 地区部编码
           , REGION_CN_NAME                 -- 地区部中文名称
           , REPOFFICE_CODE                 -- 代表处编码
           , REPOFFICE_CN_NAME              -- 代表处中文名称
           , SPART_CODE                     -- SPART编码
           , SPART_DESC                     -- SPART描述
           , BG_CODE                        -- BG编码
           , BG_CN_NAME                     -- BG中文名称
           , OVERSEA_FLAG                   -- 国内海外标识
		   , SOFTWARE_MARK
           , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
           , REPLACE_RELATION_NAME          -- 替换关系名称
           , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
           , RELATION_TYPE                  -- 关系（ 替换 、收编）
           , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , RMB_COST_AMT                   -- 标准成本
        FROM SPART_LV4_REPL_INTERVAL_TMP
      ),
      -- SPART+关系名称期间（T-1年+T年YTD）金额
      SPART_LV3_INTERVAL_TMP AS(
      SELECT VERSION_ID                     -- 版本ID
             , LV0_CODE                       -- 重量级团队LV0编码
             , LV0_CN_NAME                    -- 重量级团队LV0名称
             , LV1_CODE                       -- 重量级团队LV1编码
             , LV1_CN_NAME                    -- 重量级团队LV1名称
             , LV2_CODE                       -- 重量级团队LV2编码
             , LV2_CN_NAME                    -- 重量级团队LV2名称
             , LV3_CODE                       -- 重量级团队LV3编码
             , LV3_CN_NAME                    -- 重量级团队LV3名称
             , REGION_CODE                    -- 地区部编码
             , REGION_CN_NAME                 -- 地区部中文名称
             , REPOFFICE_CODE                 -- 代表处编码
             , REPOFFICE_CN_NAME              -- 代表处中文名称
             , SPART_CODE                     -- SPART编码
             , SPART_DESC                     -- SPART描述
             , BG_CODE                        -- BG编码
             , BG_CN_NAME                     -- BG中文名称
             , OVERSEA_FLAG                   -- 国内海外标识
			 , SOFTWARE_MARK
             , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
             , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
             , SUM(RMB_COST_AMT)  AS RMB_COST_AMT -- 标准成本
          FROM SPART_LV3_REPL_INTERVAL_TMP
         GROUP BY VERSION_ID
             , LV0_CODE
             , LV0_CN_NAME
             , LV1_CODE
             , LV1_CN_NAME
             , LV2_CODE
             , LV2_CN_NAME
             , LV3_CODE
             , LV3_CN_NAME
             , REGION_CODE
             , REGION_CN_NAME
             , REPOFFICE_CODE
             , REPOFFICE_CN_NAME
             , SPART_CODE
             , SPART_DESC
             , BG_CODE
             , BG_CN_NAME
             , OVERSEA_FLAG
			 , SOFTWARE_MARK
             , VIEW_FLAG
             , CODE_TYPE
      ),
      -- 该虚化SPART下第I个SPART期间（T-1年+T年YTD）金额/该虚化SPART期间（T-1年+T年YTD）金额
      -- SPART+LV1+关系名称期间（T-1年+T年YTD）金额/SPART+关系名称期间（T-1年+T年YTD）金额
      SELECT T1.VERSION_ID                     -- 版本ID
           , T1.LV0_CODE                       -- 重量级团队LV0编码
           , T1.LV0_CN_NAME                    -- 重量级团队LV0名称
           , T1.LV1_CODE                       -- 重量级团队LV1编码
           , T1.LV1_CN_NAME                    -- 重量级团队LV1名称
           , T1.LV2_CODE                       -- 重量级团队LV2编码
           , T1.LV2_CN_NAME                    -- 重量级团队LV2名称
           , T1.LV3_CODE                       -- 重量级团队LV3编码
           , T1.LV3_CN_NAME                    -- 重量级团队LV3名称
           , T1.LV4_CODE                       -- 重量级团队LV4编码
           , T1.LV4_CN_NAME                    -- 重量级团队LV4名称
           , T1.REGION_CODE                    -- 地区部编码
           , T1.REGION_CN_NAME                 -- 地区部中文名称
           , T1.REPOFFICE_CODE                 -- 代表处编码
           , T1.REPOFFICE_CN_NAME              -- 代表处中文名称
           , T1.SPART_CODE                     -- SPART编码
           , T1.SPART_DESC                     -- SPART描述
           , ''SPART_LV3_RELATION'' AS GROUP_LEVEL
           , T1.BG_CODE                        -- BG编码
           , T1.BG_CN_NAME                     -- BG中文名称
           , T1.OVERSEA_FLAG                   -- 国内海外标识
           , T1.SOFTWARE_MARK
           , T1.VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
           , T1.REPLACE_RELATION_NAME          -- 替换关系名称
           , T1.REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
           , T1.RELATION_TYPE                  -- 关系（ 替换 、收编）
           , T1.CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , (CASE WHEN T2.RMB_COST_AMT = 0 THEN 0 ELSE ROUND(T1.RMB_COST_AMT/T2.RMB_COST_AMT,10) END) AS WEIGHT_RATE  -- 权重
           , '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
           ,  -1 AS CREATED_BY
           , CURRENT_TIMESTAMP AS CREATION_DATE
           , -1 AS LAST_UPDATED_BY
           , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
           , ''N'' AS DEL_FLAG
        FROM SPART_LV3_REPL_INTERVAL_TMP T1  -- 该虚化SPART下第I个SPART期间（T-1年+T年YTD）金额临时表
        LEFT JOIN SPART_LV3_INTERVAL_TMP T2  -- 该虚化SPART期间（T-1年+T年YTD）金额临时表
          ON T1.VERSION_ID = T2.VERSION_ID
         AND T1.LV0_CODE = T2.LV0_CODE
         AND T1.LV1_CODE = T2.LV1_CODE
         AND T1.LV2_CODE = T2.LV2_CODE
         AND T1.LV3_CODE = T2.LV3_CODE
         AND T1.VIEW_FLAG = T2.VIEW_FLAG
         AND T1.REGION_CODE = T2.REGION_CODE
         AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
         AND T1.SPART_CODE = T2.SPART_CODE
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
         AND T1.CODE_TYPE = T2.CODE_TYPE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
    ';
    EXECUTE IMMEDIATE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => 'SPART_LV3_RELATION层级权重数据入到结果表：'||V_TO_WEIGHT_TABLE||'，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );
  
  V_SQL := '
      INSERT INTO SPART_PBI_COST_INFO_TMP(
         VERSION_ID                      -- 版本ID
       , PERIOD_ID                       -- 会计期
       , PERIOD_YEAR                     -- 会计年
       , BASE_PERIOD_ID                  -- 基期
       , LV0_CODE                        -- LV0编码
       , LV0_CN_NAME                     -- LV0名称
       , LV1_CODE                        -- LV1编码
       , LV1_CN_NAME                     -- LV1名称
       , LV2_CODE                        -- LV2编码
       , LV2_CN_NAME                     -- LV2名称
       , LV3_CODE                        -- LV3编码
       , LV3_CN_NAME                     -- LV3名称
       , GROUP_CODE                      -- 各层级编码
       , GROUP_CN_NAME                   -- 各层级中文名称
       , PARENT_CODE                     -- 父级编码
       , PARENT_CN_NAME                  -- 父级中文名称
       , REGION_CODE                     -- 地区部编码
       , REGION_CN_NAME                  -- 地区部中文名称
       , REPOFFICE_CODE                  -- 代表处编码
       , REPOFFICE_CN_NAME               -- 代表处中文名称
       , BG_CODE                         -- BG编码
       , BG_CN_NAME                      -- BG中文名称
       , OVERSEA_FLAG                    -- 国内海外标识
	   , SOFTWARE_MARK
       , REPLACE_RELATION_NAME           -- 替换关系名称
       , REPLACE_RELATION_TYPE           -- 新老编码替换类型（一对一  、一对多 、多对多）
       , RELATION_TYPE                   -- 关系（ 替换 、收编）
       , CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
       , GROUP_LEVEL                     -- 各层级（SPART/LV3/LV2/LV1/LV0）
       , PROD_QTY                        -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
       , RMB_COST_AMT                    -- 标准成本
       , COST_INDEX                      -- 成本指数
      )
      -- SPART+LV1+关系名称期间（T-1年+T年YTD）金额
      WITH SPART_LV3_REPL_INTERVAL_TMP AS(
      SELECT VERSION_ID            -- 版本ID
           , LV0_CODE                       -- 重量级团队LV0编码
           , LV0_CN_NAME                    -- 重量级团队LV0名称
           , LV1_CODE                       -- 重量级团队LV1编码
           , LV1_CN_NAME                    -- 重量级团队LV1名称
           , LV2_CODE                       -- 重量级团队LV2编码
           , LV2_CN_NAME                    -- 重量级团队LV2名称
           , LV3_CODE                       -- 重量级团队LV3编码
           , LV3_CN_NAME                    -- 重量级团队LV3名称
           , LV4_CODE                       -- 重量级团队LV4编码
           , LV4_CN_NAME                    -- 重量级团队LV4名称
           , REGION_CODE                    -- 地区部编码
           , REGION_CN_NAME                 -- 地区部中文名称
           , REPOFFICE_CODE                 -- 代表处编码
           , REPOFFICE_CN_NAME              -- 代表处中文名称
           , SPART_CODE                     -- SPART编码
           , SPART_DESC                     -- SPART描述
           , BG_CODE                        -- BG编码
           , BG_CN_NAME                     -- BG中文名称
           , OVERSEA_FLAG                   -- 国内海外标识
		   , SOFTWARE_MARK
           , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
           , REPLACE_RELATION_NAME          -- 替换关系名称
           , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
           , RELATION_TYPE                  -- 关系（ 替换 、收编）
           , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , RMB_COST_AMT                   -- 标准成本
        FROM SPART_LV4_REPL_INTERVAL_TMP
      ),
      SPART_LV3_WEIGHT_TMP AS(
      SELECT VERSION_ID        
         , LV0_CODE          
         , LV1_CODE          
         , LV2_CODE          
         , LV3_CODE          
         , LV4_CODE          
         , REGION_CODE       
         , REPOFFICE_CODE    
         , SPART_CODE        
         , BG_CODE              
         , OVERSEA_FLAG         
         , SOFTWARE_MARK
         , VIEW_FLAG            
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE        
         , CODE_TYPE            
         , WEIGHT_RATE
      FROM '||V_TO_WEIGHT_TABLE||'
      WHERE VERSION_ID = '||V_VERSION_ID||'
      AND GRANULARITY_TYPE = '''||F_GRANULARITY_TYPE||'''
      AND GROUP_LEVEL = ''SPART_LV3_RELATION''
      ),
        -- 第一步：单个具体新编码虚化
        SPART_LV3_CUS_REPL_INFO_TMP2 AS(
        SELECT T1.VERSION_ID
             , T1.PERIOD_ID
             , T1.PERIOD_YEAR
             , T1.BASE_PERIOD_ID
             , T1.LV0_CODE
             , T1.LV0_CN_NAME
             , T1.LV1_CODE
             , T1.LV1_CN_NAME
             , T1.LV2_CODE
             , T1.LV2_CN_NAME
             , T1.LV3_CODE
             , T1.LV3_CN_NAME
             , T1.REGION_CODE
             , T1.REGION_CN_NAME
             , T1.REPOFFICE_CODE
             , T1.REPOFFICE_CN_NAME
             , T1.SPART_CODE
             , T1.SPART_DESC
             , T1.BG_CODE
             , T1.BG_CN_NAME
             , T1.OVERSEA_FLAG
			 , T1.SOFTWARE_MARK
             , T1.REPLACE_RELATION_NAME
             , T1.REPLACE_RELATION_TYPE
             , T1.RELATION_TYPE
             , T1.CODE_TYPE
             , SUM(T1.PROD_QTY)     AS PROD_QTY
             , SUM(T1.RMB_COST_AMT) AS RMB_COST_AMT
             , ROUND(SUM(NVL(T1.COST_INDEX,0)*NVL(T2.WEIGHT_RATE,0)),10) AS COST_INDEX
        FROM SPART_LV4_COST_INDEX_TMP T1  -- LV4层级成本指数临时表
        LEFT JOIN SPART_LV3_WEIGHT_TMP T2
          ON T1.VERSION_ID = T2.VERSION_ID
         AND T1.LV0_CODE = T2.LV0_CODE
         AND T1.LV1_CODE = T2.LV1_CODE
         AND T1.LV2_CODE = T2.LV2_CODE
         AND T1.LV3_CODE = T2.LV3_CODE
         AND T1.LV4_CODE = T2.LV4_CODE
         AND T1.REGION_CODE = T2.REGION_CODE
         AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
         AND T1.SPART_CODE = T2.SPART_CODE
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
         AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
         AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
         AND T1.RELATION_TYPE = T2.RELATION_TYPE
         AND T1.CODE_TYPE = T2.CODE_TYPE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
       GROUP BY T1.VERSION_ID
             , T1.PERIOD_ID
             , T1.PERIOD_YEAR
             , T1.BASE_PERIOD_ID
             , T1.LV0_CODE
             , T1.LV0_CN_NAME
             , T1.LV1_CODE
             , T1.LV1_CN_NAME
             , T1.LV2_CODE
             , T1.LV2_CN_NAME
             , T1.LV3_CODE
             , T1.LV3_CN_NAME
             , T1.REGION_CODE
             , T1.REGION_CN_NAME
             , T1.REPOFFICE_CODE
             , T1.REPOFFICE_CN_NAME
             , T1.SPART_CODE
             , T1.SPART_DESC
             , T1.BG_CODE
             , T1.BG_CN_NAME
             , T1.OVERSEA_FLAG
			 , T1.SOFTWARE_MARK
             , T1.REPLACE_RELATION_NAME
             , T1.REPLACE_RELATION_TYPE
             , T1.RELATION_TYPE
             , T1.CODE_TYPE
      ),
    -- SPART+关系名称到LV3层级期间（T-1年+T年YTD）金额
    SPART_LV3_CUS_REPL_INFO_TMP1 AS(
    SELECT VERSION_ID                     -- 版本ID
           , LV0_CODE                       -- 重量级团队LV0编码
           , LV0_CN_NAME                    -- 重量级团队LV0名称
           , LV1_CODE                       -- 重量级团队LV1编码
           , LV1_CN_NAME                    -- 重量级团队LV1名称
           , LV2_CODE                       -- 重量级团队LV2编码
           , LV2_CN_NAME                    -- 重量级团队LV2名称
           , LV3_CODE                       -- 重量级团队LV3编码
           , LV3_CN_NAME                    -- 重量级团队LV3名称
           , REGION_CODE                    -- 地区部编码
           , REGION_CN_NAME                 -- 地区部中文名称
           , REPOFFICE_CODE                 -- 代表处编码
           , REPOFFICE_CN_NAME              -- 代表处中文名称
           , SPART_CODE                     -- SPART编码
           , SPART_DESC                     -- SPART描述
           , BG_CODE                        -- BG编码
           , BG_CN_NAME                     -- BG中文名称
           , OVERSEA_FLAG                   -- 国内海外标识
		   , SOFTWARE_MARK
           , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
           , REPLACE_RELATION_NAME          -- 替换关系名称
           , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
           , RELATION_TYPE                  -- 关系（ 替换 、收编）
           , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , SUM(RMB_COST_AMT)  AS RMB_COST_AMT -- 标准成本
        FROM SPART_LV3_REPL_INTERVAL_TMP T1
       GROUP BY VERSION_ID
           , LV0_CODE
           , LV0_CN_NAME
           , LV1_CODE
           , LV1_CN_NAME
           , LV2_CODE
           , LV2_CN_NAME
           , LV3_CODE
           , LV3_CN_NAME
           , REGION_CODE
           , REGION_CN_NAME
           , REPOFFICE_CODE
           , REPOFFICE_CN_NAME
           , SPART_CODE
           , SPART_DESC
           , BG_CODE
           , BG_CN_NAME
           , OVERSEA_FLAG
		   , SOFTWARE_MARK
           , VIEW_FLAG
           , REPLACE_RELATION_NAME
           , REPLACE_RELATION_TYPE
           , RELATION_TYPE
           , CODE_TYPE
      ),
      -- 关系名称到LV3层级期间（T-1年+T年YTD）金额
      SPART_LV3_CUS_REPL_INFO_TMP3 AS(
      SELECT VERSION_ID                     -- 版本ID
         , LV0_CODE                       -- 重量级团队LV0编码
         , LV0_CN_NAME                    -- 重量级团队LV0名称
         , LV1_CODE                       -- 重量级团队LV1编码
         , LV1_CN_NAME                    -- 重量级团队LV1名称
         , LV2_CODE                       -- 重量级团队LV2编码
         , LV2_CN_NAME                    -- 重量级团队LV2名称
         , LV3_CODE                       -- 重量级团队LV3编码
         , LV3_CN_NAME                    -- 重量级团队LV3名称
         , REGION_CODE                    -- 地区部编码
         , REGION_CN_NAME                 -- 地区部中文名称
         , REPOFFICE_CODE                 -- 代表处编码
         , REPOFFICE_CN_NAME              -- 代表处中文名称
         , BG_CODE                        -- BG编码
         , BG_CN_NAME                     -- BG中文名称
         , OVERSEA_FLAG                   -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
         , REPLACE_RELATION_NAME          -- 替换关系名称
         , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
         , RELATION_TYPE                  -- 关系（ 替换 、收编）
         , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , SUM(RMB_COST_AMT) AS RMB_COST_AMT -- 标准成本
        FROM SPART_LV3_CUS_REPL_INFO_TMP1
       GROUP BY VERSION_ID
         , LV0_CODE
         , LV0_CN_NAME
         , LV1_CODE
         , LV1_CN_NAME
         , LV2_CODE
         , LV2_CN_NAME
         , LV3_CODE
         , LV3_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE
         , CODE_TYPE
      ),
      -- SPART+关系名称到LV3层级期间（T-1年+T年YTD）金额/关系名称到LV3层级期间（T-1年+T年YTD）金额
      SPART_LV3_CUS_REPL_INFO_TMP4 AS(
      SELECT T1.VERSION_ID                      -- 版本ID
           , T1.LV0_CODE                        -- LV0编码
           , T1.LV0_CN_NAME                     -- LV0名称
           , T1.LV1_CODE                        -- LV1编码
           , T1.LV1_CN_NAME                     -- LV1名称
           , T1.LV2_CODE                        -- LV2编码
           , T1.LV2_CN_NAME                     -- LV2名称
           , T1.LV3_CODE                        -- LV3编码
           , T1.LV3_CN_NAME                     -- LV3名称
           , T1.REGION_CODE                     -- 地区部编码
           , T1.REGION_CN_NAME                  -- 地区部中文名称
           , T1.REPOFFICE_CODE                  -- 代表处编码
           , T1.REPOFFICE_CN_NAME               -- 代表处中文名称
           , T1.SPART_CODE                      -- SPART编码
           , T1.SPART_DESC                      -- SPART描述
           , T1.BG_CODE                         -- BG编码
           , T1.BG_CN_NAME                      -- BG中文名称
           , T1.OVERSEA_FLAG                    -- 国内海外标识
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME           -- 替换关系名称
           , T1.REPLACE_RELATION_TYPE           -- 新老编码替换类型（一对一  、一对多 、多对多）
           , T1.RELATION_TYPE                   -- 关系（ 替换 、收编）
           , T1.CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , (CASE WHEN T2.RMB_COST_AMT = 0 THEN 0 ELSE T1.RMB_COST_AMT/T2.RMB_COST_AMT END) AS WEIGHT_RATE  -- 权重
        FROM SPART_LV3_CUS_REPL_INFO_TMP1 T1
        LEFT JOIN SPART_LV3_CUS_REPL_INFO_TMP3 T2
          ON T1.VERSION_ID            = T2.VERSION_ID
         AND T1.LV0_CODE              = T2.LV0_CODE
         AND T1.LV1_CODE              = T2.LV1_CODE
         AND T1.LV2_CODE              = T2.LV2_CODE
         AND T1.LV3_CODE              = T2.LV3_CODE
         AND T1.REGION_CODE           = T2.REGION_CODE
         AND T1.REPOFFICE_CODE        = T2.REPOFFICE_CODE
         AND T1.BG_CODE               = T2.BG_CODE
         AND T1.OVERSEA_FLAG          = T2.OVERSEA_FLAG
         AND T1.VIEW_FLAG             = T2.VIEW_FLAG
         AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
         AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
         AND T1.RELATION_TYPE         = T2.RELATION_TYPE
         AND T1.CODE_TYPE             = T2.CODE_TYPE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
      )
      -- 第二步：单个SPART虚化到LV3层级，然后汇总到LV3
      SELECT T1.VERSION_ID
           , T1.PERIOD_ID
           , CAST(T1.PERIOD_YEAR AS INT) AS PERIOD_YEAR
           , T1.BASE_PERIOD_ID
           , T1.LV0_CODE
           , T1.LV0_CN_NAME
           , T1.LV1_CODE    AS LV1_CODE
           , T1.LV1_CN_NAME AS LV1_CN_NAME
           , T1.LV2_CODE
           , T1.LV2_CN_NAME
           , T1.LV3_CODE
           , T1.LV3_CN_NAME
           , T1.LV3_CODE    AS GROUP_CODE
           , T1.LV3_CN_NAME AS GROUP_CN_NAME
           , T1.LV2_CODE AS PARENT_CODE
           , T1.LV2_CODE AS PARENT_CN_NAME
           , T1.REGION_CODE
           , T1.REGION_CN_NAME
           , T1.REPOFFICE_CODE
           , T1.REPOFFICE_CN_NAME
           , T1.BG_CODE
           , T1.BG_CN_NAME
           , T1.OVERSEA_FLAG
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME
           , T1.REPLACE_RELATION_TYPE
           , T1.RELATION_TYPE
           , T1.CODE_TYPE
           , ''LV3''       AS GROUP_LEVEL
           , SUM(T1.PROD_QTY)     AS PROD_QTY
           , SUM(T1.RMB_COST_AMT) AS RMB_COST_AMT
           , ROUND(SUM(NVL(T1.COST_INDEX,0)*NVL(T2.WEIGHT_RATE,0)),10) AS COST_INDEX
        FROM SPART_LV3_CUS_REPL_INFO_TMP2 T1
        LEFT JOIN SPART_LV3_CUS_REPL_INFO_TMP4 T2
          ON T1.VERSION_ID            = T2.VERSION_ID
         AND T1.LV0_CODE              = T2.LV0_CODE
         AND T1.LV1_CODE              = T2.LV1_CODE
         AND T1.LV2_CODE              = T2.LV2_CODE
         AND T1.LV3_CODE              = T2.LV3_CODE
         AND T1.REGION_CODE           = T2.REGION_CODE
         AND T1.REPOFFICE_CODE        = T2.REPOFFICE_CODE
         AND T1.SPART_CODE            = T2.SPART_CODE
         AND T1.BG_CODE               = T2.BG_CODE
         AND T1.OVERSEA_FLAG          = T2.OVERSEA_FLAG
         AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
         AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
         AND T1.RELATION_TYPE         = T2.RELATION_TYPE
         AND T1.CODE_TYPE             = T2.CODE_TYPE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
       GROUP BY T1.VERSION_ID
           , T1.PERIOD_ID
           , T1.PERIOD_YEAR
           , T1.BASE_PERIOD_ID
           , T1.LV0_CODE
           , T1.LV0_CN_NAME
           , T1.LV1_CODE
           , T1.LV1_CN_NAME
           , T1.LV2_CODE
           , T1.LV2_CN_NAME
           , T1.LV3_CODE
           , T1.LV3_CN_NAME
           , T1.REGION_CODE
           , T1.REGION_CN_NAME
           , T1.REPOFFICE_CODE
           , T1.REPOFFICE_CN_NAME
           , T1.BG_CODE
           , T1.BG_CN_NAME
           , T1.OVERSEA_FLAG
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME
           , T1.REPLACE_RELATION_TYPE
           , T1.RELATION_TYPE
           , T1.CODE_TYPE
      UNION ALL 
	  -- 同基指数虚化结果值
      SELECT T1.VERSION_ID
           , T1.PERIOD_ID
           , CAST(SUBSTR(T1.PERIOD_ID,1,4) AS INT) AS PERIOD_YEAR
           , '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID 
           , T1.LV0_CODE
           , T1.LV0_CN_NAME
           , T1.LV1_CODE    AS LV1_CODE
           , T1.LV1_CN_NAME AS LV1_CN_NAME
           , T1.LV2_CODE
           , T1.LV2_CN_NAME
           , T1.LV3_CODE
           , T1.LV3_CN_NAME
           , T1.LV3_CODE    AS GROUP_CODE
           , T1.LV3_CN_NAME AS GROUP_CN_NAME
           , T1.LV2_CODE AS PARENT_CODE
           , T1.LV2_CODE AS PARENT_CN_NAME
           , T1.REGION_CODE
           , T1.REGION_CN_NAME
           , T1.REPOFFICE_CODE
           , T1.REPOFFICE_CN_NAME
           , T1.BG_CODE
           , T1.BG_CN_NAME
           , T1.OVERSEA_FLAG
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME
           , T1.REPLACE_RELATION_TYPE
           , T1.RELATION_TYPE
           , T1.CODE_TYPE
           , ''LV3''       AS GROUP_LEVEL
           , NULL AS PROD_QTY
           , NULL AS RMB_COST_AMT
           , ROUND(SUM(NVL(T1.COST_INDEX,0)),10) AS COST_INDEX
		FROM SAME_BASE_LV4_INDEX_TMP T1
		GROUP BY T1.VERSION_ID
           , T1.PERIOD_ID
           , T1.LV0_CODE
           , T1.LV0_CN_NAME
           , T1.LV1_CODE    
           , T1.LV1_CN_NAME
           , T1.LV2_CODE    
           , T1.LV2_CN_NAME 
           , T1.LV3_CODE    
           , T1.LV3_CN_NAME
           , T1.REGION_CODE
           , T1.REGION_CN_NAME
           , T1.REPOFFICE_CODE
           , T1.REPOFFICE_CN_NAME
           , T1.BG_CODE
           , T1.BG_CN_NAME
           , T1.OVERSEA_FLAG
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME
           , T1.REPLACE_RELATION_TYPE
           , T1.RELATION_TYPE
           , T1.CODE_TYPE
      ';
    EXECUTE IMMEDIATE V_SQL;

      V_STEP_NUM := V_STEP_NUM+1;
      PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '需要到LV3层级的数据量：'||V_PARENT_LEVEL,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
      );

    ELSEIF(V_PARENT_LEVEL = 'LV2') THEN
    RAISE NOTICE '222222222222';
      -- 虚化到LV2层级
   V_SQL := '
  INSERT INTO '||V_TO_WEIGHT_TABLE||' (
         VERSION_ID,          
         LV0_CODE,            
         LV0_CN_NAME,         
         LV1_CODE,            
         LV1_CN_NAME,         
         LV2_CODE,            
         LV2_CN_NAME,         
         LV3_CODE,            
         LV3_CN_NAME,         
         LV4_CODE,            
         LV4_CN_NAME,         
         REGION_CODE,         
         REGION_CN_NAME,      
         REPOFFICE_CODE,      
         REPOFFICE_CN_NAME,   
         SPART_CODE,
         SPART_DESC,
         GROUP_LEVEL,  
         BG_CODE,             
         BG_CN_NAME,          
         OVERSEA_FLAG,        
         SOFTWARE_MARK,
         VIEW_FLAG,           
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,       
         CODE_TYPE,           
         WEIGHT_RATE,
         GRANULARITY_TYPE,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
      -- SPART+LV1+关系名称期间（T-1年+T年YTD）金额
      WITH SPART_LV2_REPL_INTERVAL_TMP AS(
      SELECT VERSION_ID            -- 版本ID
           , LV0_CODE                       -- 重量级团队LV0编码
           , LV0_CN_NAME                    -- 重量级团队LV0名称
           , LV1_CODE                       -- 重量级团队LV1编码
           , LV1_CN_NAME                    -- 重量级团队LV1名称
           , LV2_CODE                       -- 重量级团队LV2编码
           , LV2_CN_NAME                    -- 重量级团队LV2名称
           , LV3_CODE                       -- 重量级团队LV3编码
           , LV3_CN_NAME                    -- 重量级团队LV3名称
           , LV4_CODE                       -- 重量级团队LV4编码
           , LV4_CN_NAME                    -- 重量级团队LV4名称
           , REGION_CODE                    -- 地区部编码
           , REGION_CN_NAME                 -- 地区部中文名称
           , REPOFFICE_CODE                 -- 代表处编码
           , REPOFFICE_CN_NAME              -- 代表处中文名称
           , SPART_CODE                     -- SPART编码
           , SPART_DESC                     -- SPART描述
           , BG_CODE                        -- BG编码
           , BG_CN_NAME                     -- BG中文名称
           , OVERSEA_FLAG                   -- 国内海外标识
		   , SOFTWARE_MARK
           , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
           , REPLACE_RELATION_NAME          -- 替换关系名称
           , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
           , RELATION_TYPE                  -- 关系（ 替换 、收编）
           , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , RMB_COST_AMT                   -- 标准成本
        FROM SPART_LV4_REPL_INTERVAL_TMP
      ),
      -- SPART+关系名称期间（T-1年+T年YTD）金额
      SPART_LV2_INTERVAL_TMP AS(
      SELECT VERSION_ID                     -- 版本ID
             , LV0_CODE                       -- 重量级团队LV0编码
             , LV0_CN_NAME                    -- 重量级团队LV0名称
             , LV1_CODE                       -- 重量级团队LV1编码
             , LV1_CN_NAME                    -- 重量级团队LV1名称
             , LV2_CODE                       -- 重量级团队LV2编码
             , LV2_CN_NAME                    -- 重量级团队LV2名称
             , REGION_CODE                    -- 地区部编码
             , REGION_CN_NAME                 -- 地区部中文名称
             , REPOFFICE_CODE                 -- 代表处编码
             , REPOFFICE_CN_NAME              -- 代表处中文名称
             , SPART_CODE                     -- SPART编码
             , SPART_DESC                     -- SPART描述
             , BG_CODE                        -- BG编码
             , BG_CN_NAME                     -- BG中文名称
             , OVERSEA_FLAG                   -- 国内海外标识
			 , SOFTWARE_MARK
             , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
             , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
             , SUM(RMB_COST_AMT)  AS RMB_COST_AMT -- 标准成本
          FROM SPART_LV2_REPL_INTERVAL_TMP
         GROUP BY VERSION_ID
             , LV0_CODE
             , LV0_CN_NAME
	         , LV1_CODE
             , LV1_CN_NAME
	         , LV2_CODE
             , LV2_CN_NAME
             , REGION_CODE
             , REGION_CN_NAME
             , REPOFFICE_CODE
             , REPOFFICE_CN_NAME
             , SPART_CODE
             , SPART_DESC
             , BG_CODE
             , BG_CN_NAME
             , OVERSEA_FLAG
			 , SOFTWARE_MARK
             , VIEW_FLAG
             , CODE_TYPE
      )
      -- 该虚化SPART下第I个SPART期间（T-1年+T年YTD）金额/该虚化SPART期间（T-1年+T年YTD）金额
      -- SPART+LV1+关系名称期间（T-1年+T年YTD）金额/SPART+关系名称期间（T-1年+T年YTD）金额
      SELECT T1.VERSION_ID                     -- 版本ID
           , T1.LV0_CODE                       -- 重量级团队LV0编码
           , T1.LV0_CN_NAME                    -- 重量级团队LV0名称
           , T1.LV1_CODE                       -- 重量级团队LV1编码
           , T1.LV1_CN_NAME                    -- 重量级团队LV1名称
           , T1.LV2_CODE                       -- 重量级团队LV2编码
           , T1.LV2_CN_NAME                    -- 重量级团队LV2名称
           , T1.LV3_CODE                       -- 重量级团队LV3编码
           , T1.LV3_CN_NAME                    -- 重量级团队LV3名称
           , T1.LV4_CODE                       -- 重量级团队LV4编码
           , T1.LV4_CN_NAME                    -- 重量级团队LV4名称
           , T1.REGION_CODE                    -- 地区部编码
           , T1.REGION_CN_NAME                 -- 地区部中文名称
           , T1.REPOFFICE_CODE                 -- 代表处编码
           , T1.REPOFFICE_CN_NAME              -- 代表处中文名称
           , T1.SPART_CODE                     -- SPART编码
           , T1.SPART_DESC                     -- SPART描述
           , ''SPART_LV2_RELATION'' AS GROUP_LEVEL
           , T1.BG_CODE                        -- BG编码
           , T1.BG_CN_NAME                     -- BG中文名称
           , T1.OVERSEA_FLAG                   -- 国内海外标识
	       , T1.SOFTWARE_MARK
           , T1.VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
           , T1.REPLACE_RELATION_NAME          -- 替换关系名称
           , T1.REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
           , T1.RELATION_TYPE                  -- 关系（ 替换 、收编）
           , T1.CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , (CASE WHEN T2.RMB_COST_AMT = 0 THEN 0 ELSE ROUND(T1.RMB_COST_AMT/T2.RMB_COST_AMT,10) END) AS WEIGHT_RATE  -- 权重
           , '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
           ,  -1 AS CREATED_BY
           , CURRENT_TIMESTAMP AS CREATION_DATE
           , -1 AS LAST_UPDATED_BY
           , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
           , ''N'' AS DEL_FLAG
        FROM SPART_LV2_REPL_INTERVAL_TMP T1  -- 该虚化SPART下第I个SPART期间（T-1年+T年YTD）金额临时表
        LEFT JOIN SPART_LV2_INTERVAL_TMP T2  -- 该虚化SPART期间（T-1年+T年YTD）金额临时表
          ON T1.VERSION_ID = T2.VERSION_ID
         AND T1.LV0_CODE = T2.LV0_CODE
         AND T1.LV1_CODE = T2.LV1_CODE
         AND T1.LV2_CODE = T2.LV2_CODE
         AND T1.REGION_CODE = T2.REGION_CODE
         AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
         AND T1.SPART_CODE = T2.SPART_CODE
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
         AND T1.CODE_TYPE = T2.CODE_TYPE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
    ';
    EXECUTE IMMEDIATE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => 'SPART_LV2_RELATION层级权重数据入到结果表：'||V_TO_WEIGHT_TABLE||'，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

  V_SQL := '
      INSERT INTO SPART_PBI_COST_INFO_TMP(
         VERSION_ID                      -- 版本ID
       , PERIOD_ID                       -- 会计期
       , PERIOD_YEAR                     -- 会计年
       , BASE_PERIOD_ID                  -- 基期
       , LV0_CODE                        -- LV0编码
       , LV0_CN_NAME                     -- LV0名称
       , LV1_CODE                        -- LV1编码
       , LV1_CN_NAME                     -- LV1名称
       , LV2_CODE                        -- LV2编码
       , LV2_CN_NAME                     -- LV2名称
       , LV3_CODE                        -- LV3编码
       , LV3_CN_NAME                     -- LV3名称
       , GROUP_CODE                      -- 各层级编码
       , GROUP_CN_NAME                   -- 各层级中文名称
       , PARENT_CODE                     -- 父级编码
       , PARENT_CN_NAME                  -- 父级中文名称
       , REGION_CODE                     -- 地区部编码
       , REGION_CN_NAME                  -- 地区部中文名称
       , REPOFFICE_CODE                  -- 代表处编码
       , REPOFFICE_CN_NAME               -- 代表处中文名称
       , BG_CODE                         -- BG编码
       , BG_CN_NAME                      -- BG中文名称
       , OVERSEA_FLAG                    -- 国内海外标识
       , REPLACE_RELATION_NAME           -- 替换关系名称
	   , SOFTWARE_MARK
       , REPLACE_RELATION_TYPE           -- 新老编码替换类型（一对一  、一对多 、多对多）
       , RELATION_TYPE                   -- 关系（ 替换 、收编）
       , CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
       , GROUP_LEVEL                     -- 各层级（SPART/LV3/LV2/LV1/LV0）
       , PROD_QTY                        -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
       , RMB_COST_AMT                    -- 标准成本
       , COST_INDEX                      -- 成本指数
      )
      -- SPART+LV1+关系名称期间（T-1年+T年YTD）金额
      WITH SPART_LV2_REPL_INTERVAL_TMP AS(
      SELECT VERSION_ID            -- 版本ID
           , LV0_CODE                       -- 重量级团队LV0编码
           , LV0_CN_NAME                    -- 重量级团队LV0名称
           , LV1_CODE                       -- 重量级团队LV1编码
           , LV1_CN_NAME                    -- 重量级团队LV1名称
           , LV2_CODE                       -- 重量级团队LV2编码
           , LV2_CN_NAME                    -- 重量级团队LV2名称
           , LV3_CODE                       -- 重量级团队LV3编码
           , LV3_CN_NAME                    -- 重量级团队LV3名称
           , LV4_CODE                       -- 重量级团队LV4编码
           , LV4_CN_NAME                    -- 重量级团队LV4名称
           , REGION_CODE                    -- 地区部编码
           , REGION_CN_NAME                 -- 地区部中文名称
           , REPOFFICE_CODE                 -- 代表处编码
           , REPOFFICE_CN_NAME              -- 代表处中文名称
           , SPART_CODE                     -- SPART编码
           , SPART_DESC                     -- SPART描述
           , BG_CODE                        -- BG编码
           , BG_CN_NAME                     -- BG中文名称
           , OVERSEA_FLAG                   -- 国内海外标识
		   , SOFTWARE_MARK
           , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
           , REPLACE_RELATION_NAME          -- 替换关系名称
           , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
           , RELATION_TYPE                  -- 关系（ 替换 、收编）
           , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , RMB_COST_AMT                   -- 标准成本
        FROM SPART_LV4_REPL_INTERVAL_TMP
      ),
      SPART_LV2_WEIGHT_TMP AS(
      SELECT VERSION_ID        
         , LV0_CODE          
         , LV1_CODE          
         , LV2_CODE          
         , LV3_CODE          
         , LV4_CODE          
         , REGION_CODE       
         , REPOFFICE_CODE    
         , SPART_CODE        
         , BG_CODE              
         , OVERSEA_FLAG         
         , SOFTWARE_MARK
         , VIEW_FLAG            
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE        
         , CODE_TYPE            
         , WEIGHT_RATE
      FROM '||V_TO_WEIGHT_TABLE||'
      WHERE VERSION_ID = '||V_VERSION_ID||'
      AND GRANULARITY_TYPE = '''||F_GRANULARITY_TYPE||'''
      AND GROUP_LEVEL = ''SPART_LV2_RELATION''
      ),
        -- 第一步：单个具体新编码虚化
        SPART_LV2_CUS_REPL_INFO_TMP2 AS(
        SELECT T1.VERSION_ID
             , T1.PERIOD_ID
             , T1.PERIOD_YEAR
             , T1.BASE_PERIOD_ID
             , T1.LV0_CODE
             , T1.LV0_CN_NAME
             , T1.LV1_CODE
             , T1.LV1_CN_NAME
	         , T1.LV2_CODE
             , T1.LV2_CN_NAME
             , T1.REGION_CODE
             , T1.REGION_CN_NAME
             , T1.REPOFFICE_CODE
             , T1.REPOFFICE_CN_NAME
             , T1.SPART_CODE
             , T1.SPART_DESC
             , T1.BG_CODE
             , T1.BG_CN_NAME
             , T1.OVERSEA_FLAG
			 , T1.SOFTWARE_MARK
             , T1.REPLACE_RELATION_NAME
             , T1.REPLACE_RELATION_TYPE
             , T1.RELATION_TYPE
             , T1.CODE_TYPE
             , SUM(T1.PROD_QTY)     AS PROD_QTY
             , SUM(T1.RMB_COST_AMT) AS RMB_COST_AMT
             , ROUND(SUM(NVL(T1.COST_INDEX,0)*NVL(T2.WEIGHT_RATE,0)),10) AS COST_INDEX
        FROM SPART_LV4_COST_INDEX_TMP T1  -- LV4层级成本指数临时表
        LEFT JOIN SPART_LV2_WEIGHT_TMP T2
          ON T1.VERSION_ID = T2.VERSION_ID
         AND T1.LV0_CODE = T2.LV0_CODE
         AND T1.LV1_CODE = T2.LV1_CODE
         AND T1.LV2_CODE = T2.LV2_CODE
         AND T1.LV3_CODE = T2.LV3_CODE
         AND T1.LV4_CODE = T2.LV4_CODE
         AND T1.REGION_CODE = T2.REGION_CODE
         AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
         AND T1.SPART_CODE = T2.SPART_CODE
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
         AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
         AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
         AND T1.RELATION_TYPE = T2.RELATION_TYPE
         AND T1.CODE_TYPE = T2.CODE_TYPE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
       GROUP BY T1.VERSION_ID
             , T1.PERIOD_ID
             , T1.PERIOD_YEAR
             , T1.BASE_PERIOD_ID
             , T1.LV0_CODE
             , T1.LV0_CN_NAME
	     , T1.LV1_CODE
             , T1.LV1_CN_NAME
	     , T1.LV2_CODE
             , T1.LV2_CN_NAME
             , T1.REGION_CODE
             , T1.REGION_CN_NAME
             , T1.REPOFFICE_CODE
             , T1.REPOFFICE_CN_NAME
             , T1.SPART_CODE
             , T1.SPART_DESC
             , T1.BG_CODE
             , T1.BG_CN_NAME
             , T1.OVERSEA_FLAG
			 , T1.SOFTWARE_MARK
             , T1.REPLACE_RELATION_NAME
             , T1.REPLACE_RELATION_TYPE
             , T1.RELATION_TYPE
             , T1.CODE_TYPE
      ),
    -- SPART+关系名称到LV2层级期间（T-1年+T年YTD）金额
    SPART_LV2_CUS_REPL_INFO_TMP1 AS(
    SELECT VERSION_ID                     -- 版本ID
           , LV0_CODE                       -- 重量级团队LV0编码
           , LV0_CN_NAME                    -- 重量级团队LV0名称
           , LV1_CODE                       -- 重量级团队LV1编码
           , LV1_CN_NAME                    -- 重量级团队LV1名称
           , LV2_CODE                       -- 重量级团队LV2编码
           , LV2_CN_NAME                    -- 重量级团队LV2名称
           , REGION_CODE                    -- 地区部编码
           , REGION_CN_NAME                 -- 地区部中文名称
           , REPOFFICE_CODE                 -- 代表处编码
           , REPOFFICE_CN_NAME              -- 代表处中文名称
           , SPART_CODE                     -- SPART编码
           , SPART_DESC                     -- SPART描述
           , BG_CODE                        -- BG编码
           , BG_CN_NAME                     -- BG中文名称
           , OVERSEA_FLAG                   -- 国内海外标识
		   , SOFTWARE_MARK
           , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
           , REPLACE_RELATION_NAME          -- 替换关系名称
           , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
           , RELATION_TYPE                  -- 关系（ 替换 、收编）
           , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , SUM(RMB_COST_AMT)  AS RMB_COST_AMT -- 标准成本
        FROM SPART_LV2_REPL_INTERVAL_TMP T1
       GROUP BY VERSION_ID
           , LV0_CODE
           , LV0_CN_NAME
	   , LV1_CODE
           , LV1_CN_NAME
	   , LV2_CODE
           , LV2_CN_NAME
           , REGION_CODE
           , REGION_CN_NAME
           , REPOFFICE_CODE
           , REPOFFICE_CN_NAME
           , SPART_CODE
           , SPART_DESC
           , BG_CODE
           , BG_CN_NAME
           , OVERSEA_FLAG
		   , SOFTWARE_MARK
           , VIEW_FLAG
           , REPLACE_RELATION_NAME
           , REPLACE_RELATION_TYPE
           , RELATION_TYPE
           , CODE_TYPE
      ),
      -- 关系名称到LV2层级期间（T-1年+T年YTD）金额
      SPART_LV2_CUS_REPL_INFO_TMP3 AS(
      SELECT VERSION_ID                     -- 版本ID
         , LV0_CODE                       -- 重量级团队LV0编码
         , LV0_CN_NAME                    -- 重量级团队LV0名称
         , LV1_CODE                       -- 重量级团队LV1编码
         , LV1_CN_NAME                    -- 重量级团队LV1名称
         , LV2_CODE                       -- 重量级团队LV2编码
         , LV2_CN_NAME                    -- 重量级团队LV2名称
         , REGION_CODE                    -- 地区部编码
         , REGION_CN_NAME                 -- 地区部中文名称
         , REPOFFICE_CODE                 -- 代表处编码
         , REPOFFICE_CN_NAME              -- 代表处中文名称
         , BG_CODE                        -- BG编码
         , BG_CN_NAME                     -- BG中文名称
         , OVERSEA_FLAG                   -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
         , REPLACE_RELATION_NAME          -- 替换关系名称
         , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
         , RELATION_TYPE                  -- 关系（ 替换 、收编）
         , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , SUM(RMB_COST_AMT) AS RMB_COST_AMT -- 标准成本
        FROM SPART_LV2_CUS_REPL_INFO_TMP1
       GROUP BY VERSION_ID
         , LV0_CODE
         , LV0_CN_NAME
	 , LV1_CODE
         , LV1_CN_NAME
	 , LV2_CODE
         , LV2_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE
         , CODE_TYPE
      ),
      -- SPART+关系名称到LV2层级期间（T-1年+T年YTD）金额/关系名称到LV2层级期间（T-1年+T年YTD）金额
      SPART_LV2_CUS_REPL_INFO_TMP4 AS(
      SELECT T1.VERSION_ID                      -- 版本ID
           , T1.LV0_CODE                        -- LV0编码
           , T1.LV0_CN_NAME                     -- LV0名称
           , T1.LV1_CODE                        -- LV1编码
           , T1.LV1_CN_NAME                     -- LV1名称
           , T1.LV2_CODE                        -- LV2编码
           , T1.LV2_CN_NAME                     -- LV2名称
           , T1.REGION_CODE                     -- 地区部编码
           , T1.REGION_CN_NAME                  -- 地区部中文名称
           , T1.REPOFFICE_CODE                  -- 代表处编码
           , T1.REPOFFICE_CN_NAME               -- 代表处中文名称
           , T1.SPART_CODE                      -- SPART编码
           , T1.SPART_DESC                      -- SPART描述
           , T1.BG_CODE                         -- BG编码
           , T1.BG_CN_NAME                      -- BG中文名称
           , T1.OVERSEA_FLAG                    -- 国内海外标识
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME           -- 替换关系名称
           , T1.REPLACE_RELATION_TYPE           -- 新老编码替换类型（一对一  、一对多 、多对多）
           , T1.RELATION_TYPE                   -- 关系（ 替换 、收编）
           , T1.CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , (CASE WHEN T2.RMB_COST_AMT = 0 THEN 0 ELSE T1.RMB_COST_AMT/T2.RMB_COST_AMT END) AS WEIGHT_RATE  -- 权重
        FROM SPART_LV2_CUS_REPL_INFO_TMP1 T1
        LEFT JOIN SPART_LV2_CUS_REPL_INFO_TMP3 T2
          ON T1.VERSION_ID            = T2.VERSION_ID
         AND T1.LV0_CODE              = T2.LV0_CODE
         AND T1.LV1_CODE              = T2.LV1_CODE
         AND T1.LV2_CODE              = T2.LV2_CODE
         AND T1.REGION_CODE           = T2.REGION_CODE
         AND T1.REPOFFICE_CODE        = T2.REPOFFICE_CODE
         AND T1.BG_CODE               = T2.BG_CODE
         AND T1.OVERSEA_FLAG          = T2.OVERSEA_FLAG
         AND T1.VIEW_FLAG             = T2.VIEW_FLAG
         AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
         AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
         AND T1.RELATION_TYPE         = T2.RELATION_TYPE
         AND T1.CODE_TYPE             = T2.CODE_TYPE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
      )
      -- 第二步：单个SPART虚化到LV2层级，然后汇总到LV2
      SELECT T1.VERSION_ID
           , T1.PERIOD_ID
           , CAST(T1.PERIOD_YEAR AS INT) AS PERIOD_YEAR
           , T1.BASE_PERIOD_ID
           , T1.LV0_CODE
           , T1.LV0_CN_NAME
           , T1.LV1_CODE    AS LV1_CODE
           , T1.LV1_CN_NAME AS LV1_CN_NAME
           , T1.LV2_CODE
           , T1.LV2_CN_NAME
           , NULL AS LV3_CODE
           , NULL AS LV3_CN_NAME
           , T1.LV2_CODE    AS GROUP_CODE
           , T1.LV2_CN_NAME AS GROUP_CN_NAME
           , T1.LV1_CODE AS PARENT_CODE
           , T1.LV1_CODE AS PARENT_CN_NAME
           , T1.REGION_CODE
           , T1.REGION_CN_NAME
           , T1.REPOFFICE_CODE
           , T1.REPOFFICE_CN_NAME
           , T1.BG_CODE
           , T1.BG_CN_NAME
           , T1.OVERSEA_FLAG
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME
           , T1.REPLACE_RELATION_TYPE
           , T1.RELATION_TYPE
           , T1.CODE_TYPE
           , ''LV2'' AS GROUP_LEVEL
           , SUM(T1.PROD_QTY)     AS PROD_QTY
           , SUM(T1.RMB_COST_AMT) AS RMB_COST_AMT
           , ROUND(SUM(NVL(T1.COST_INDEX,0)*NVL(T2.WEIGHT_RATE,0)),10) AS COST_INDEX
        FROM SPART_LV2_CUS_REPL_INFO_TMP2 T1
        LEFT JOIN SPART_LV2_CUS_REPL_INFO_TMP4 T2
          ON T1.VERSION_ID            = T2.VERSION_ID
         AND T1.LV0_CODE              = T2.LV0_CODE
         AND T1.LV1_CODE              = T2.LV1_CODE
         AND T1.LV2_CODE              = T2.LV2_CODE
         AND T1.REGION_CODE           = T2.REGION_CODE
         AND T1.REPOFFICE_CODE        = T2.REPOFFICE_CODE
         AND T1.SPART_CODE            = T2.SPART_CODE
         AND T1.BG_CODE               = T2.BG_CODE
         AND T1.OVERSEA_FLAG          = T2.OVERSEA_FLAG
         AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
         AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
         AND T1.RELATION_TYPE         = T2.RELATION_TYPE
         AND T1.CODE_TYPE             = T2.CODE_TYPE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
       GROUP BY T1.VERSION_ID
           , T1.PERIOD_ID
           , T1.PERIOD_YEAR
           , T1.BASE_PERIOD_ID
           , T1.LV0_CODE
           , T1.LV0_CN_NAME
	   , T1.LV1_CODE
           , T1.LV1_CN_NAME
	   , T1.LV2_CODE
	   , T1.LV2_CN_NAME
           , T1.REGION_CODE
           , T1.REGION_CN_NAME
           , T1.REPOFFICE_CODE
           , T1.REPOFFICE_CN_NAME
           , T1.BG_CODE
           , T1.BG_CN_NAME
           , T1.OVERSEA_FLAG
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME
           , T1.REPLACE_RELATION_TYPE
           , T1.RELATION_TYPE
           , T1.CODE_TYPE
	  UNION ALL 
	  -- 同基指数虚化结果值
      SELECT T1.VERSION_ID
           , T1.PERIOD_ID
           , CAST(SUBSTR(T1.PERIOD_ID,1,4) AS INT) AS PERIOD_YEAR
           , '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID 
           , T1.LV0_CODE
           , T1.LV0_CN_NAME
           , T1.LV1_CODE    AS LV1_CODE
           , T1.LV1_CN_NAME AS LV1_CN_NAME
           , T1.LV2_CODE
           , T1.LV2_CN_NAME
           , NULL AS LV3_CODE
           , NULL AS LV3_CN_NAME
           , T1.LV2_CODE    AS GROUP_CODE
           , T1.LV2_CN_NAME AS GROUP_CN_NAME
           , T1.LV1_CODE AS PARENT_CODE
           , T1.LV1_CODE AS PARENT_CN_NAME
           , T1.REGION_CODE
           , T1.REGION_CN_NAME
           , T1.REPOFFICE_CODE
           , T1.REPOFFICE_CN_NAME
           , T1.BG_CODE
           , T1.BG_CN_NAME
           , T1.OVERSEA_FLAG
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME
           , T1.REPLACE_RELATION_TYPE
           , T1.RELATION_TYPE
           , T1.CODE_TYPE
           , ''LV2''       AS GROUP_LEVEL
           , NULL AS PROD_QTY
           , NULL AS RMB_COST_AMT
           , ROUND(SUM(NVL(T1.COST_INDEX,0)),10) AS COST_INDEX
		FROM SAME_BASE_LV4_INDEX_TMP T1
		GROUP BY T1.VERSION_ID
           , T1.PERIOD_ID
           , T1.LV0_CODE
           , T1.LV0_CN_NAME
           , T1.LV1_CODE    
           , T1.LV1_CN_NAME
           , T1.LV2_CODE    
           , T1.LV2_CN_NAME 
           , T1.REGION_CODE
           , T1.REGION_CN_NAME
           , T1.REPOFFICE_CODE
           , T1.REPOFFICE_CN_NAME
           , T1.BG_CODE
           , T1.BG_CN_NAME
           , T1.OVERSEA_FLAG
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME
           , T1.REPLACE_RELATION_TYPE
           , T1.RELATION_TYPE
           , T1.CODE_TYPE
      ';
    EXECUTE IMMEDIATE V_SQL;

      V_STEP_NUM := V_STEP_NUM+1;
      PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '需要到LV2层级的数据量：'||V_PARENT_LEVEL,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
      );

      ELSEIF(V_PARENT_LEVEL = 'LV1') THEN
      RAISE NOTICE '11111111111';
      -- 虚化到LV1层级
   V_SQL := '
  INSERT INTO '||V_TO_WEIGHT_TABLE||' (
         VERSION_ID,          
         LV0_CODE,            
         LV0_CN_NAME,         
         LV1_CODE,            
         LV1_CN_NAME,         
         LV2_CODE,            
         LV2_CN_NAME,         
         LV4_CODE,            
         LV4_CN_NAME,         
         REGION_CODE,         
         REGION_CN_NAME,      
         REPOFFICE_CODE,      
         REPOFFICE_CN_NAME,   
         SPART_CODE,
         SPART_DESC,
         GROUP_LEVEL,  
         BG_CODE,             
         BG_CN_NAME,          
         OVERSEA_FLAG,        
         SOFTWARE_MARK,
         VIEW_FLAG,           
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,       
         CODE_TYPE,           
         WEIGHT_RATE,
         GRANULARITY_TYPE,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
      -- SPART+LV1+关系名称期间（T-1年+T年YTD）金额
      WITH SPART_LV1_REPL_INTERVAL_TMP AS(
      SELECT VERSION_ID            -- 版本ID
           , LV0_CODE                       -- 重量级团队LV0编码
           , LV0_CN_NAME                    -- 重量级团队LV0名称
           , LV1_CODE                       -- 重量级团队LV1编码
           , LV1_CN_NAME                    -- 重量级团队LV1名称
           , LV2_CODE                       -- 重量级团队LV2编码
           , LV2_CN_NAME                    -- 重量级团队LV2名称
           , LV4_CODE                       -- 重量级团队LV4编码
           , LV4_CN_NAME                    -- 重量级团队LV4名称
           , REGION_CODE                    -- 地区部编码
           , REGION_CN_NAME                 -- 地区部中文名称
           , REPOFFICE_CODE                 -- 代表处编码
           , REPOFFICE_CN_NAME              -- 代表处中文名称
           , SPART_CODE                     -- SPART编码
           , SPART_DESC                     -- SPART描述
           , BG_CODE                        -- BG编码
           , BG_CN_NAME                     -- BG中文名称
           , OVERSEA_FLAG                   -- 国内海外标识
		   , SOFTWARE_MARK
           , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
           , REPLACE_RELATION_NAME          -- 替换关系名称
           , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
           , RELATION_TYPE                  -- 关系（ 替换 、收编）
           , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , SUM(RMB_COST_AMT) AS RMB_COST_AMT -- 标准成本
        FROM SPART_LV4_REPL_INTERVAL_TMP
       GROUP BY VERSION_ID
           , LV0_CODE
           , LV0_CN_NAME
           , LV1_CODE
           , LV1_CN_NAME
           , LV2_CODE
           , LV2_CN_NAME
           , LV4_CODE
           , LV4_CN_NAME
           , REGION_CODE
           , REGION_CN_NAME
           , REPOFFICE_CODE
           , REPOFFICE_CN_NAME
           , SPART_CODE
           , SPART_DESC
           , BG_CODE
           , BG_CN_NAME
           , OVERSEA_FLAG
		   , SOFTWARE_MARK
           , VIEW_FLAG
           , REPLACE_RELATION_NAME
           , REPLACE_RELATION_TYPE
           , RELATION_TYPE
           , CODE_TYPE
      ),
      -- SPART+关系名称期间（T-1年+T年YTD）金额
      SPART_LV1_INTERVAL_TMP AS(
      SELECT VERSION_ID                     -- 版本ID
             , LV0_CODE                       -- 重量级团队LV0编码
             , LV0_CN_NAME                    -- 重量级团队LV0名称
             , LV1_CODE                       -- 重量级团队LV1编码
             , LV1_CN_NAME                    -- 重量级团队LV1名称
             , REGION_CODE                    -- 地区部编码
             , REGION_CN_NAME                 -- 地区部中文名称
             , REPOFFICE_CODE                 -- 代表处编码
             , REPOFFICE_CN_NAME              -- 代表处中文名称
             , SPART_CODE                     -- SPART编码
             , SPART_DESC                     -- SPART描述
             , BG_CODE                        -- BG编码
             , BG_CN_NAME                     -- BG中文名称
             , OVERSEA_FLAG                   -- 国内海外标识
			 , SOFTWARE_MARK
             , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
             , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
             , SUM(RMB_COST_AMT)  AS RMB_COST_AMT -- 标准成本
          FROM SPART_LV1_REPL_INTERVAL_TMP
         GROUP BY VERSION_ID
             , LV0_CODE
             , LV0_CN_NAME
             , LV1_CODE
             , LV1_CN_NAME
             , REGION_CODE
             , REGION_CN_NAME
             , REPOFFICE_CODE
             , REPOFFICE_CN_NAME
             , SPART_CODE
             , SPART_DESC
             , BG_CODE
             , BG_CN_NAME
             , OVERSEA_FLAG
			 , SOFTWARE_MARK
             , VIEW_FLAG
             , CODE_TYPE
      )
      -- 该虚化SPART下第I个SPART期间（T-1年+T年YTD）金额/该虚化SPART期间（T-1年+T年YTD）金额
      -- SPART+LV1+关系名称期间（T-1年+T年YTD）金额/SPART+关系名称期间（T-1年+T年YTD）金额
      SELECT T1.VERSION_ID                     -- 版本ID
           , T1.LV0_CODE                       -- 重量级团队LV0编码
           , T1.LV0_CN_NAME                    -- 重量级团队LV0名称
           , T1.LV1_CODE                       -- 重量级团队LV1编码
           , T1.LV1_CN_NAME                    -- 重量级团队LV1名称
           , T1.LV2_CODE                       -- 重量级团队LV2编码
           , T1.LV2_CN_NAME                    -- 重量级团队LV2名称
           , T1.LV4_CODE                       -- 重量级团队LV4编码
           , T1.LV4_CN_NAME                    -- 重量级团队LV4名称
           , T1.REGION_CODE                    -- 地区部编码
           , T1.REGION_CN_NAME                 -- 地区部中文名称
           , T1.REPOFFICE_CODE                 -- 代表处编码
           , T1.REPOFFICE_CN_NAME              -- 代表处中文名称
           , T1.SPART_CODE                     -- SPART编码
           , T1.SPART_DESC                     -- SPART描述
           , ''SPART_LV1_RELATION'' AS GROUP_LEVEL
           , T1.BG_CODE                        -- BG编码
           , T1.BG_CN_NAME                     -- BG中文名称
           , T1.OVERSEA_FLAG                   -- 国内海外标识
		   , T1.SOFTWARE_MARK
           , T1.VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
           , T1.REPLACE_RELATION_NAME          -- 替换关系名称
           , T1.REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
           , T1.RELATION_TYPE                  -- 关系（ 替换 、收编）
           , T1.CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , (CASE WHEN T2.RMB_COST_AMT = 0 THEN 0 ELSE ROUND(T1.RMB_COST_AMT/T2.RMB_COST_AMT,10) END) AS WEIGHT_RATE  -- 权重
           , '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
           ,  -1 AS CREATED_BY
           , CURRENT_TIMESTAMP AS CREATION_DATE
           , -1 AS LAST_UPDATED_BY
           , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
           , ''N'' AS DEL_FLAG
        FROM SPART_LV1_REPL_INTERVAL_TMP T1  -- 该虚化SPART下第I个SPART期间（T-1年+T年YTD）金额临时表
        LEFT JOIN SPART_LV1_INTERVAL_TMP T2  -- 该虚化SPART期间（T-1年+T年YTD）金额临时表
          ON T1.VERSION_ID = T2.VERSION_ID
         AND T1.LV0_CODE = T2.LV0_CODE
         AND T1.LV1_CODE = T2.LV1_CODE
         AND T1.VIEW_FLAG = T2.VIEW_FLAG 
         AND T1.REGION_CODE = T2.REGION_CODE
         AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
         AND T1.SPART_CODE = T2.SPART_CODE
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
         AND T1.CODE_TYPE = T2.CODE_TYPE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
    ';
    EXECUTE IMMEDIATE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => 'SPART_LV2_RELATION层级权重数据入到结果表：'||V_TO_WEIGHT_TABLE||'，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

  V_SQL := '
      INSERT INTO SPART_PBI_COST_INFO_TMP(
         VERSION_ID                      -- 版本ID
       , PERIOD_ID                       -- 会计期
       , PERIOD_YEAR                     -- 会计年
       , BASE_PERIOD_ID                  -- 基期
       , LV0_CODE                        -- LV0编码
       , LV0_CN_NAME                     -- LV0名称
       , LV1_CODE                        -- LV1编码
       , LV1_CN_NAME                     -- LV1名称
       , LV2_CODE                        -- LV2编码
       , LV2_CN_NAME                     -- LV2名称
       , LV3_CODE                        -- LV3编码
       , LV3_CN_NAME                     -- LV3名称
       , GROUP_CODE                      -- 各层级编码
       , GROUP_CN_NAME                   -- 各层级中文名称
       , PARENT_CODE                     -- 父级编码
       , PARENT_CN_NAME                  -- 父级中文名称
       , REGION_CODE                     -- 地区部编码
       , REGION_CN_NAME                  -- 地区部中文名称
       , REPOFFICE_CODE                  -- 代表处编码
       , REPOFFICE_CN_NAME               -- 代表处中文名称
       --, SPART_CODE                      -- SPART编码
       --, SPART_DESC                      -- SPART描述
       , BG_CODE                         -- BG编码
       , BG_CN_NAME                      -- BG中文名称
       , OVERSEA_FLAG                    -- 国内海外标识
	   , SOFTWARE_MARK
       , REPLACE_RELATION_NAME           -- 替换关系名称
       , REPLACE_RELATION_TYPE           -- 新老编码替换类型（一对一  、一对多 、多对多）
       , RELATION_TYPE                   -- 关系（ 替换 、收编）
       , CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
       , GROUP_LEVEL                     -- 各层级（SPART/LV3/LV2/LV1/LV0）
       , PROD_QTY                        -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
       , RMB_COST_AMT                    -- 标准成本
       , COST_INDEX                      -- 成本指数
      )
      -- SPART+LV1+关系名称期间（T-1年+T年YTD）金额
      WITH SPART_LV1_REPL_INTERVAL_TMP AS(
      SELECT VERSION_ID            -- 版本ID
           , LV0_CODE                       -- 重量级团队LV0编码
           , LV0_CN_NAME                    -- 重量级团队LV0名称
           , LV1_CODE                       -- 重量级团队LV1编码
           , LV1_CN_NAME                    -- 重量级团队LV1名称
           , LV2_CODE                       -- 重量级团队LV2编码
           , LV2_CN_NAME                    -- 重量级团队LV2名称
           , LV4_CODE                       -- 重量级团队LV4编码
           , LV4_CN_NAME                    -- 重量级团队LV4名称
           , REGION_CODE                    -- 地区部编码
           , REGION_CN_NAME                 -- 地区部中文名称
           , REPOFFICE_CODE                 -- 代表处编码
           , REPOFFICE_CN_NAME              -- 代表处中文名称
           , SPART_CODE                     -- SPART编码
           , SPART_DESC                     -- SPART描述
           , BG_CODE                        -- BG编码
           , BG_CN_NAME                     -- BG中文名称
           , OVERSEA_FLAG                   -- 国内海外标识
		   , SOFTWARE_MARK
           , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
           , REPLACE_RELATION_NAME          -- 替换关系名称
           , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
           , RELATION_TYPE                  -- 关系（ 替换 、收编）
           , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , SUM(RMB_COST_AMT) AS RMB_COST_AMT -- 标准成本
        FROM SPART_LV4_REPL_INTERVAL_TMP
       GROUP BY VERSION_ID
           , LV0_CODE
           , LV0_CN_NAME
           , LV1_CODE
           , LV1_CN_NAME
           , LV2_CODE
           , LV2_CN_NAME
           , LV4_CODE
           , LV4_CN_NAME
           , REGION_CODE
           , REGION_CN_NAME
           , REPOFFICE_CODE
           , REPOFFICE_CN_NAME
           , SPART_CODE
           , SPART_DESC
           , BG_CODE
           , BG_CN_NAME
           , OVERSEA_FLAG
		   , SOFTWARE_MARK
           , VIEW_FLAG
           , REPLACE_RELATION_NAME
           , REPLACE_RELATION_TYPE
           , RELATION_TYPE
           , CODE_TYPE
      ),
      SPART_LV1_WEIGHT_TMP AS(
      SELECT VERSION_ID        
         , LV0_CODE          
         , LV1_CODE          
         , LV2_CODE          
         , LV3_CODE          
         , LV4_CODE          
         , REGION_CODE       
         , REPOFFICE_CODE    
         , SPART_CODE        
         , BG_CODE              
         , OVERSEA_FLAG         
         , SOFTWARE_MARK
         , VIEW_FLAG            
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE        
         , CODE_TYPE            
         , WEIGHT_RATE
      FROM '||V_TO_WEIGHT_TABLE||'
      WHERE VERSION_ID = '||V_VERSION_ID||'
      AND GRANULARITY_TYPE = '''||F_GRANULARITY_TYPE||'''
      AND GROUP_LEVEL = ''SPART_LV1_RELATION''
      ),
        -- 第一步：单个具体新编码虚化
        SPART_LV1_CUS_REPL_INFO_TMP2 AS(
        SELECT T1.VERSION_ID
             , T1.PERIOD_ID
             , T1.PERIOD_YEAR
             , T1.BASE_PERIOD_ID
             , T1.LV0_CODE
             , T1.LV0_CN_NAME
             , T1.LV1_CODE
             , T1.LV1_CN_NAME
             , T1.REGION_CODE
             , T1.REGION_CN_NAME
             , T1.REPOFFICE_CODE
             , T1.REPOFFICE_CN_NAME
             , T1.SPART_CODE
             , T1.SPART_DESC
             , T1.BG_CODE
             , T1.BG_CN_NAME
             , T1.OVERSEA_FLAG
			 , T1.SOFTWARE_MARK
             , T1.REPLACE_RELATION_NAME
             , T1.REPLACE_RELATION_TYPE
             , T1.RELATION_TYPE
             , T1.CODE_TYPE
             , SUM(T1.PROD_QTY)     AS PROD_QTY
             , SUM(T1.RMB_COST_AMT) AS RMB_COST_AMT
             , ROUND(SUM(NVL(T1.COST_INDEX,0)*NVL(T2.WEIGHT_RATE,0)),10) AS COST_INDEX
        FROM SPART_LV4_COST_INDEX_TMP T1  -- LV4层级成本指数临时表
        LEFT JOIN SPART_LV1_WEIGHT_TMP T2
          ON T1.VERSION_ID = T2.VERSION_ID
         AND T1.LV0_CODE = T2.LV0_CODE
         AND T1.LV1_CODE = T2.LV1_CODE
         AND T1.LV2_CODE = T2.LV2_CODE
         AND T1.LV4_CODE = T2.LV4_CODE
         AND T1.REGION_CODE = T2.REGION_CODE
         AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
         AND T1.SPART_CODE = T2.SPART_CODE
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
         AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
         AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
         AND T1.RELATION_TYPE = T2.RELATION_TYPE
         AND T1.CODE_TYPE = T2.CODE_TYPE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
       GROUP BY T1.VERSION_ID
             , T1.PERIOD_ID
             , T1.PERIOD_YEAR
             , T1.BASE_PERIOD_ID
             , T1.LV0_CODE
             , T1.LV0_CN_NAME
             , T1.LV1_CODE
             , T1.LV1_CN_NAME
             , T1.REGION_CODE
             , T1.REGION_CN_NAME
             , T1.REPOFFICE_CODE
             , T1.REPOFFICE_CN_NAME
             , T1.SPART_CODE
             , T1.SPART_DESC
             , T1.BG_CODE
             , T1.BG_CN_NAME
             , T1.OVERSEA_FLAG
			 , T1.SOFTWARE_MARK
             , T1.REPLACE_RELATION_NAME
             , T1.REPLACE_RELATION_TYPE
             , T1.RELATION_TYPE
             , T1.CODE_TYPE
      ),
    -- SPART+关系名称到LV1层级期间（T-1年+T年YTD）金额
    SPART_LV1_CUS_REPL_INFO_TMP1 AS(
    SELECT VERSION_ID                     -- 版本ID
           , LV0_CODE                       -- 重量级团队LV0编码
           , LV0_CN_NAME                    -- 重量级团队LV0名称
           , LV1_CODE                       -- 重量级团队LV1编码
           , LV1_CN_NAME                    -- 重量级团队LV1名称
           , REGION_CODE                    -- 地区部编码
           , REGION_CN_NAME                 -- 地区部中文名称
           , REPOFFICE_CODE                 -- 代表处编码
           , REPOFFICE_CN_NAME              -- 代表处中文名称
           , SPART_CODE                     -- SPART编码
           , SPART_DESC                     -- SPART描述
           , BG_CODE                        -- BG编码
           , BG_CN_NAME                     -- BG中文名称
           , OVERSEA_FLAG                   -- 国内海外标识
		   , SOFTWARE_MARK
           , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
           , REPLACE_RELATION_NAME          -- 替换关系名称
           , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
           , RELATION_TYPE                  -- 关系（ 替换 、收编）
           , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , SUM(RMB_COST_AMT)  AS RMB_COST_AMT -- 标准成本
        FROM SPART_LV1_REPL_INTERVAL_TMP T1
       GROUP BY VERSION_ID
           , LV0_CODE
           , LV0_CN_NAME
           , LV1_CODE
           , LV1_CN_NAME
           , REGION_CODE
           , REGION_CN_NAME
           , REPOFFICE_CODE
           , REPOFFICE_CN_NAME
           , SPART_CODE
           , SPART_DESC
           , BG_CODE
           , BG_CN_NAME
           , OVERSEA_FLAG
		   , SOFTWARE_MARK
           , VIEW_FLAG
           , REPLACE_RELATION_NAME
           , REPLACE_RELATION_TYPE
           , RELATION_TYPE
           , CODE_TYPE
      ),
      -- 关系名称到LV1层级期间（T-1年+T年YTD）金额
      SPART_LV1_CUS_REPL_INFO_TMP3 AS(
      SELECT VERSION_ID                     -- 版本ID
         , LV0_CODE                       -- 重量级团队LV0编码
         , LV0_CN_NAME                    -- 重量级团队LV0名称
         , LV1_CODE                       -- 重量级团队LV1编码
         , LV1_CN_NAME                    -- 重量级团队LV1名称
         , REGION_CODE                    -- 地区部编码
         , REGION_CN_NAME                 -- 地区部中文名称
         , REPOFFICE_CODE                 -- 代表处编码
         , REPOFFICE_CN_NAME              -- 代表处中文名称
         , BG_CODE                        -- BG编码
         , BG_CN_NAME                     -- BG中文名称
         , OVERSEA_FLAG                   -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
         , REPLACE_RELATION_NAME          -- 替换关系名称
         , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
         , RELATION_TYPE                  -- 关系（ 替换 、收编）
         , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , SUM(RMB_COST_AMT) AS RMB_COST_AMT -- 标准成本
        FROM SPART_LV1_CUS_REPL_INFO_TMP1
       GROUP BY VERSION_ID
         , LV0_CODE
         , LV0_CN_NAME
         , LV1_CODE
         , LV1_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE
         , CODE_TYPE
      ),
      -- SPART+关系名称到LV1层级期间（T-1年+T年YTD）金额/关系名称到LV1层级期间（T-1年+T年YTD）金额
      SPART_LV1_CUS_REPL_INFO_TMP4 AS(
      SELECT T1.VERSION_ID                      -- 版本ID
           , T1.LV0_CODE                        -- LV0编码
           , T1.LV0_CN_NAME                     -- LV0名称
           , T1.LV1_CODE                        -- LV1编码
           , T1.LV1_CN_NAME                     -- LV1名称
           , T1.REGION_CODE                     -- 地区部编码
           , T1.REGION_CN_NAME                  -- 地区部中文名称
           , T1.REPOFFICE_CODE                  -- 代表处编码
           , T1.REPOFFICE_CN_NAME               -- 代表处中文名称
           , T1.SPART_CODE                      -- SPART编码
           , T1.SPART_DESC                      -- SPART描述
           , T1.BG_CODE                         -- BG编码
           , T1.BG_CN_NAME                      -- BG中文名称
           , T1.OVERSEA_FLAG                    -- 国内海外标识
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME           -- 替换关系名称
           , T1.REPLACE_RELATION_TYPE           -- 新老编码替换类型（一对一  、一对多 、多对多）
           , T1.RELATION_TYPE                   -- 关系（ 替换 、收编）
           , T1.CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , (CASE WHEN T2.RMB_COST_AMT = 0 THEN 0 ELSE T1.RMB_COST_AMT/T2.RMB_COST_AMT END) AS WEIGHT_RATE  -- 权重
        FROM SPART_LV1_CUS_REPL_INFO_TMP1 T1
        LEFT JOIN SPART_LV1_CUS_REPL_INFO_TMP3 T2
          ON T1.VERSION_ID            = T2.VERSION_ID
         AND T1.LV0_CODE              = T2.LV0_CODE
         AND T1.LV1_CODE              = T2.LV1_CODE
         AND T1.REGION_CODE           = T2.REGION_CODE
         AND T1.REPOFFICE_CODE        = T2.REPOFFICE_CODE
         AND T1.BG_CODE               = T2.BG_CODE
         AND T1.OVERSEA_FLAG          = T2.OVERSEA_FLAG
         AND T1.VIEW_FLAG             = T2.VIEW_FLAG
         AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
         AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
         AND T1.RELATION_TYPE         = T2.RELATION_TYPE
         AND T1.CODE_TYPE             = T2.CODE_TYPE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
      )
      -- 第二步：单个SPART虚化到LV1层级，然后汇总到LV1
      SELECT T1.VERSION_ID
           , T1.PERIOD_ID
           , CAST(T1.PERIOD_YEAR AS INT) AS PERIOD_YEAR
           , T1.BASE_PERIOD_ID
           , T1.LV0_CODE
           , T1.LV0_CN_NAME
           , T1.LV1_CODE    AS LV1_CODE
           , T1.LV1_CN_NAME AS LV1_CN_NAME
           , NULL AS LV2_CODE
           , NULL AS LV2_CN_NAME
           , NULL AS LV3_CODE
           , NULL AS LV3_CN_NAME
           , T1.LV1_CODE    AS GROUP_CODE
           , T1.LV1_CN_NAME AS GROUP_CN_NAME
           , T1.LV0_CODE AS PARENT_CODE
           , T1.LV0_CODE AS PARENT_CN_NAME
           , T1.REGION_CODE
           , T1.REGION_CN_NAME
           , T1.REPOFFICE_CODE
           , T1.REPOFFICE_CN_NAME
           , T1.BG_CODE
           , T1.BG_CN_NAME
           , T1.OVERSEA_FLAG
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME
           , T1.REPLACE_RELATION_TYPE
           , T1.RELATION_TYPE
           , T1.CODE_TYPE
           , ''LV1''       AS GROUP_LEVEL
           , SUM(T1.PROD_QTY)     AS PROD_QTY
           , SUM(T1.RMB_COST_AMT) AS RMB_COST_AMT
           , ROUND(SUM(NVL(T1.COST_INDEX,0)*NVL(T2.WEIGHT_RATE,0)),10) AS COST_INDEX
        FROM SPART_LV1_CUS_REPL_INFO_TMP2 T1
        LEFT JOIN SPART_LV1_CUS_REPL_INFO_TMP4 T2
          ON T1.VERSION_ID            = T2.VERSION_ID
         AND T1.LV0_CODE              = T2.LV0_CODE
         AND T1.LV1_CODE              = T2.LV1_CODE
         AND T1.REGION_CODE           = T2.REGION_CODE
         AND T1.REPOFFICE_CODE        = T2.REPOFFICE_CODE
         AND T1.SPART_CODE            = T2.SPART_CODE
         AND T1.BG_CODE               = T2.BG_CODE
         AND T1.OVERSEA_FLAG          = T2.OVERSEA_FLAG
         AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
         AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
         AND T1.RELATION_TYPE         = T2.RELATION_TYPE
         AND T1.CODE_TYPE             = T2.CODE_TYPE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
       GROUP BY T1.VERSION_ID
           , T1.PERIOD_ID
           , T1.PERIOD_YEAR
           , T1.BASE_PERIOD_ID
           , T1.LV0_CODE
           , T1.LV0_CN_NAME
	   , T1.LV1_CODE
	   , T1.LV1_CN_NAME
           , T1.REGION_CODE
           , T1.REGION_CN_NAME
           , T1.REPOFFICE_CODE
           , T1.REPOFFICE_CN_NAME
           , T1.BG_CODE
           , T1.BG_CN_NAME
           , T1.OVERSEA_FLAG
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME
           , T1.REPLACE_RELATION_TYPE
           , T1.RELATION_TYPE
           , T1.CODE_TYPE
	  UNION ALL 
	  -- 同基指数虚化结果值
      SELECT T1.VERSION_ID
           , T1.PERIOD_ID
           , CAST(SUBSTR(T1.PERIOD_ID,1,4) AS INT) AS PERIOD_YEAR
           , '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID 
           , T1.LV0_CODE
           , T1.LV0_CN_NAME
           , T1.LV1_CODE    AS LV1_CODE
           , T1.LV1_CN_NAME AS LV1_CN_NAME
           , NULL AS LV2_CODE
           , NULL AS LV2_CN_NAME
           , NULL AS LV3_CODE
           , NULL AS LV3_CN_NAME
           , T1.LV1_CODE    AS GROUP_CODE
           , T1.LV1_CN_NAME AS GROUP_CN_NAME
           , T1.LV0_CODE AS PARENT_CODE
           , T1.LV0_CODE AS PARENT_CN_NAME
           , T1.REGION_CODE
           , T1.REGION_CN_NAME
           , T1.REPOFFICE_CODE
           , T1.REPOFFICE_CN_NAME
           , T1.BG_CODE
           , T1.BG_CN_NAME
           , T1.OVERSEA_FLAG
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME
           , T1.REPLACE_RELATION_TYPE
           , T1.RELATION_TYPE
           , T1.CODE_TYPE
           , ''LV1''       AS GROUP_LEVEL
           , NULL AS PROD_QTY
           , NULL AS RMB_COST_AMT
           , ROUND(SUM(NVL(T1.COST_INDEX,0)),10) AS COST_INDEX
		FROM SAME_BASE_LV4_INDEX_TMP T1
		GROUP BY T1.VERSION_ID
           , T1.PERIOD_ID
           , T1.LV0_CODE
           , T1.LV0_CN_NAME
           , T1.LV1_CODE    
           , T1.LV1_CN_NAME
           , T1.REGION_CODE
           , T1.REGION_CN_NAME
           , T1.REPOFFICE_CODE
           , T1.REPOFFICE_CN_NAME
           , T1.BG_CODE
           , T1.BG_CN_NAME
           , T1.OVERSEA_FLAG
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME
           , T1.REPLACE_RELATION_TYPE
           , T1.RELATION_TYPE
           , T1.CODE_TYPE
      ';
    EXECUTE IMMEDIATE V_SQL;

      V_STEP_NUM := V_STEP_NUM+1;
      PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '需要到LV1层级的数据量：'||V_PARENT_LEVEL,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
      );

     ELSEIF(V_PARENT_LEVEL = 'LV0') THEN
      RAISE NOTICE '00000000000';
      -- 虚化到LV0层级
  V_SQL := '
  INSERT INTO '||V_TO_WEIGHT_TABLE||' (
         VERSION_ID,          
         LV0_CODE,            
         LV0_CN_NAME,         
         LV1_CODE,            
         LV1_CN_NAME,           
         LV4_CODE,            
         LV4_CN_NAME,         
         REGION_CODE,         
         REGION_CN_NAME,      
         REPOFFICE_CODE,      
         REPOFFICE_CN_NAME,   
         SPART_CODE,
         SPART_DESC,
         GROUP_LEVEL,  
         BG_CODE,             
         BG_CN_NAME,          
         OVERSEA_FLAG,        
         SOFTWARE_MARK,
         VIEW_FLAG,           
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,       
         CODE_TYPE,           
         WEIGHT_RATE,
         GRANULARITY_TYPE,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  ) 
      -- SPART+LV1+关系名称期间（T-1年+T年YTD）金额
      WITH SPART_LV0_REPL_INTERVAL_TMP AS(
      SELECT VERSION_ID            -- 版本ID
           , LV0_CODE                       -- 重量级团队LV0编码
           , LV0_CN_NAME                    -- 重量级团队LV0名称
           , LV1_CODE                       -- 重量级团队LV1编码
           , LV1_CN_NAME                    -- 重量级团队LV1名称
           , LV4_CODE                       -- 重量级团队LV4编码
           , LV4_CN_NAME                    -- 重量级团队LV4名称
           , REGION_CODE                    -- 地区部编码
           , REGION_CN_NAME                 -- 地区部中文名称
           , REPOFFICE_CODE                 -- 代表处编码
           , REPOFFICE_CN_NAME              -- 代表处中文名称
           , SPART_CODE                     -- SPART编码
           , SPART_DESC                     -- SPART描述
           , BG_CODE                        -- BG编码
           , BG_CN_NAME                     -- BG中文名称
           , OVERSEA_FLAG                   -- 国内海外标识
		   , SOFTWARE_MARK
           , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
           , REPLACE_RELATION_NAME          -- 替换关系名称
           , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
           , RELATION_TYPE                  -- 关系（ 替换 、收编）
           , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , SUM(RMB_COST_AMT) AS RMB_COST_AMT -- 标准成本
        FROM SPART_LV4_REPL_INTERVAL_TMP
       GROUP BY VERSION_ID
           , LV0_CODE
           , LV0_CN_NAME
           , LV1_CODE
           , LV1_CN_NAME
           , LV4_CODE
           , LV4_CN_NAME
           , REGION_CODE
           , REGION_CN_NAME
           , REPOFFICE_CODE
           , REPOFFICE_CN_NAME
           , SPART_CODE
           , SPART_DESC
           , BG_CODE
           , BG_CN_NAME
           , OVERSEA_FLAG
		   , SOFTWARE_MARK
           , VIEW_FLAG
           , REPLACE_RELATION_NAME
           , REPLACE_RELATION_TYPE
           , RELATION_TYPE
           , CODE_TYPE
      ),
      -- SPART+关系名称期间（T-1年+T年YTD）金额
      SPART_LV0_INTERVAL_TMP AS(
      SELECT VERSION_ID                     -- 版本ID
             , LV0_CODE                       -- 重量级团队LV0编码
             , LV0_CN_NAME                    -- 重量级团队LV0名称
             , REGION_CODE                    -- 地区部编码
             , REGION_CN_NAME                 -- 地区部中文名称
             , REPOFFICE_CODE                 -- 代表处编码
             , REPOFFICE_CN_NAME              -- 代表处中文名称
             , SPART_CODE                     -- SPART编码
             , SPART_DESC                     -- SPART描述
             , BG_CODE                        -- BG编码
             , BG_CN_NAME                     -- BG中文名称
             , OVERSEA_FLAG                   -- 国内海外标识
			 , SOFTWARE_MARK
             , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
             , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
             , SUM(RMB_COST_AMT)  AS RMB_COST_AMT -- 标准成本
          FROM SPART_LV0_REPL_INTERVAL_TMP
         GROUP BY VERSION_ID
             , LV0_CODE
             , LV0_CN_NAME
             , REGION_CODE
             , REGION_CN_NAME
             , REPOFFICE_CODE
             , REPOFFICE_CN_NAME
             , SPART_CODE
             , SPART_DESC
             , BG_CODE
             , BG_CN_NAME
             , OVERSEA_FLAG
			 , SOFTWARE_MARK
             , VIEW_FLAG
             , CODE_TYPE
      ),
      -- 该虚化SPART下第I个SPART期间（T-1年+T年YTD）金额/该虚化SPART期间（T-1年+T年YTD）金额
      -- SPART+LV1+关系名称期间（T-1年+T年YTD）金额/SPART+关系名称期间（T-1年+T年YTD）金额
      SELECT T1.VERSION_ID                     -- 版本ID
           , T1.LV0_CODE                       -- 重量级团队LV0编码
           , T1.LV0_CN_NAME                    -- 重量级团队LV0名称
           , T1.LV1_CODE                       -- 重量级团队LV1编码
           , T1.LV1_CN_NAME                    -- 重量级团队LV1名称
           , T1.LV4_CODE                       -- 重量级团队LV4编码
           , T1.LV4_CN_NAME                    -- 重量级团队LV4名称
           , T1.REGION_CODE                    -- 地区部编码
           , T1.REGION_CN_NAME                 -- 地区部中文名称
           , T1.REPOFFICE_CODE                 -- 代表处编码
           , T1.REPOFFICE_CN_NAME              -- 代表处中文名称
           , T1.SPART_CODE                     -- SPART编码
           , T1.SPART_DESC                     -- SPART描述
           , ''SPART_LV0_RELATION'' AS GROUP_LEVEL
           , T1.BG_CODE                        -- BG编码
           , T1.BG_CN_NAME                     -- BG中文名称
           , T1.OVERSEA_FLAG                   -- 国内海外标识
		   , T1.SOFTWARE_MARK
           , T1.VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
           , T1.REPLACE_RELATION_NAME          -- 替换关系名称
           , T1.REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
           , T1.RELATION_TYPE                  -- 关系（ 替换 、收编）
           , T1.CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , (CASE WHEN T2.RMB_COST_AMT = 0 THEN 0 ELSE ROUND(T1.RMB_COST_AMT/T2.RMB_COST_AMT,10) END) AS WEIGHT_RATE  -- 权重
           , '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
           ,  -1 AS CREATED_BY
           , CURRENT_TIMESTAMP AS CREATION_DATE
           , -1 AS LAST_UPDATED_BY
           , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
           , ''N'' AS DEL_FLAG
        FROM SPART_LV0_REPL_INTERVAL_TMP T1  -- 该虚化SPART下第I个SPART期间（T-1年+T年YTD）金额临时表
        LEFT JOIN SPART_LV0_INTERVAL_TMP T2  -- 该虚化SPART期间（T-1年+T年YTD）金额临时表
          ON T1.VERSION_ID = T2.VERSION_ID
         AND T1.LV0_CODE = T2.LV0_CODE
         AND T1.REGION_CODE = T2.REGION_CODE
         AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
         AND T1.SPART_CODE = T2.SPART_CODE
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
         AND T1.CODE_TYPE = T2.CODE_TYPE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
    ';
    EXECUTE IMMEDIATE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => 'SPART_LV0_RELATION层级权重数据入到结果表：'||V_TO_WEIGHT_TABLE||'，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

  V_SQL := '
      INSERT INTO SPART_PBI_COST_INFO_TMP(
         VERSION_ID                      -- 版本ID
       , PERIOD_ID                       -- 会计期
       , PERIOD_YEAR                     -- 会计年
       , BASE_PERIOD_ID                  -- 基期
       , LV0_CODE                        -- LV0编码
       , LV0_CN_NAME                     -- LV0名称
       , LV1_CODE                        -- LV1编码
       , LV1_CN_NAME                     -- LV1名称
       , LV2_CODE                        -- LV2编码
       , LV2_CN_NAME                     -- LV2名称
       , LV3_CODE                        -- LV3编码
       , LV3_CN_NAME                     -- LV3名称
       , GROUP_CODE                      -- 各层级编码
       , GROUP_CN_NAME                   -- 各层级中文名称
       , PARENT_CODE                     -- 父级编码
       , PARENT_CN_NAME                  -- 父级中文名称
       , REGION_CODE                     -- 地区部编码
       , REGION_CN_NAME                  -- 地区部中文名称
       , REPOFFICE_CODE                  -- 代表处编码
       , REPOFFICE_CN_NAME               -- 代表处中文名称
       , BG_CODE                         -- BG编码
       , BG_CN_NAME                      -- BG中文名称
       , OVERSEA_FLAG                    -- 国内海外标识
	   , SOFTWARE_MARK
       , REPLACE_RELATION_NAME           -- 替换关系名称
       , REPLACE_RELATION_TYPE           -- 新老编码替换类型（一对一  、一对多 、多对多）
       , RELATION_TYPE                   -- 关系（ 替换 、收编）
       , CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
       , GROUP_LEVEL                     -- 各层级（SPART/LV3/LV2/LV1/LV0）
       , PROD_QTY                        -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
       , RMB_COST_AMT                    -- 标准成本
       , COST_INDEX                      -- 成本指数
      )
      -- SPART+LV1+关系名称期间（T-1年+T年YTD）金额
      WITH SPART_LV0_REPL_INTERVAL_TMP AS(
      SELECT VERSION_ID            -- 版本ID
           , LV0_CODE                       -- 重量级团队LV0编码
           , LV0_CN_NAME                    -- 重量级团队LV0名称
           , LV1_CODE                       -- 重量级团队LV1编码
           , LV1_CN_NAME                    -- 重量级团队LV1名称
           , LV4_CODE                       -- 重量级团队LV4编码
           , LV4_CN_NAME                    -- 重量级团队LV4名称
           , REGION_CODE                    -- 地区部编码
           , REGION_CN_NAME                 -- 地区部中文名称
           , REPOFFICE_CODE                 -- 代表处编码
           , REPOFFICE_CN_NAME              -- 代表处中文名称
           , SPART_CODE                     -- SPART编码
           , SPART_DESC                     -- SPART描述
           , BG_CODE                        -- BG编码
           , BG_CN_NAME                     -- BG中文名称
           , OVERSEA_FLAG                   -- 国内海外标识
		   , SOFTWARE_MARK
           , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
           , REPLACE_RELATION_NAME          -- 替换关系名称
           , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
           , RELATION_TYPE                  -- 关系（ 替换 、收编）
           , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , SUM(RMB_COST_AMT) AS RMB_COST_AMT -- 标准成本
        FROM SPART_LV4_REPL_INTERVAL_TMP
       GROUP BY VERSION_ID
           , LV0_CODE
           , LV0_CN_NAME
           , LV1_CODE
           , LV1_CN_NAME
           , LV4_CODE
           , LV4_CN_NAME
           , REGION_CODE
           , REGION_CN_NAME
           , REPOFFICE_CODE
           , REPOFFICE_CN_NAME
           , SPART_CODE
           , SPART_DESC
           , BG_CODE
           , BG_CN_NAME
           , OVERSEA_FLAG
		   , SOFTWARE_MARK
           , VIEW_FLAG
           , REPLACE_RELATION_NAME
           , REPLACE_RELATION_TYPE
           , RELATION_TYPE
           , CODE_TYPE
      ),
     SPART_LV0_WEIGHT_TMP AS(
      SELECT VERSION_ID        
         , LV0_CODE          
         , LV1_CODE          
         , LV2_CODE          
         , LV3_CODE          
         , LV4_CODE          
         , REGION_CODE       
         , REPOFFICE_CODE    
         , SPART_CODE        
         , BG_CODE              
         , OVERSEA_FLAG         
         , SOFTWARE_MARK
         , VIEW_FLAG            
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE        
         , CODE_TYPE            
         , WEIGHT_RATE
      FROM '||V_TO_WEIGHT_TABLE||'
      WHERE VERSION_ID = '||V_VERSION_ID||'
      AND GRANULARITY_TYPE = '''||F_GRANULARITY_TYPE||'''
      AND GROUP_LEVEL = ''SPART_LV0_RELATION''
      ),
        -- 第一步：单个具体新编码虚化
        SPART_LV0_CUS_REPL_INFO_TMP2 AS(
        SELECT T1.VERSION_ID
             , T1.PERIOD_ID
             , T1.PERIOD_YEAR
             , T1.BASE_PERIOD_ID
             , T1.LV0_CODE
             , T1.LV0_CN_NAME
             , T1.REGION_CODE
             , T1.REGION_CN_NAME
             , T1.REPOFFICE_CODE
             , T1.REPOFFICE_CN_NAME
             , T1.SPART_CODE
             , T1.SPART_DESC
             , T1.BG_CODE
             , T1.BG_CN_NAME
             , T1.OVERSEA_FLAG
			 , T1.SOFTWARE_MARK
             , T1.REPLACE_RELATION_NAME
             , T1.REPLACE_RELATION_TYPE
             , T1.RELATION_TYPE
             , T1.CODE_TYPE
             , SUM(T1.PROD_QTY)     AS PROD_QTY
             , SUM(T1.RMB_COST_AMT) AS RMB_COST_AMT
             , ROUND(SUM(NVL(T1.COST_INDEX,0)*NVL(T2.WEIGHT_RATE,0)),10) AS COST_INDEX
        FROM SPART_LV4_COST_INDEX_TMP T1  -- LV4层级成本指数临时表
        LEFT JOIN SPART_LV0_WEIGHT_TMP T2
          ON T1.VERSION_ID = T2.VERSION_ID
         AND T1.LV0_CODE = T2.LV0_CODE
         AND T1.LV1_CODE = T2.LV1_CODE
         AND T1.LV4_CODE = T2.LV4_CODE
         AND T1.REGION_CODE = T2.REGION_CODE
         AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
         AND T1.SPART_CODE = T2.SPART_CODE
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
         AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
         AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
         AND T1.RELATION_TYPE = T2.RELATION_TYPE
         AND T1.CODE_TYPE = T2.CODE_TYPE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
       GROUP BY T1.VERSION_ID
             , T1.PERIOD_ID
             , T1.PERIOD_YEAR
             , T1.BASE_PERIOD_ID
             , T1.LV0_CODE
             , T1.LV0_CN_NAME
             , T1.REGION_CODE
             , T1.REGION_CN_NAME
             , T1.REPOFFICE_CODE
             , T1.REPOFFICE_CN_NAME
             , T1.SPART_CODE
             , T1.SPART_DESC
             , T1.BG_CODE
             , T1.BG_CN_NAME
             , T1.OVERSEA_FLAG
			 , T1.SOFTWARE_MARK
             , T1.REPLACE_RELATION_NAME
             , T1.REPLACE_RELATION_TYPE
             , T1.RELATION_TYPE
             , T1.CODE_TYPE
      ),
    -- SPART+关系名称到LV0层级期间（T-1年+T年YTD）金额
    SPART_LV0_CUS_REPL_INFO_TMP1 AS(
    SELECT VERSION_ID                     -- 版本ID
           , LV0_CODE                       -- 重量级团队LV0编码
           , LV0_CN_NAME                    -- 重量级团队LV0名称
           , REGION_CODE                    -- 地区部编码
           , REGION_CN_NAME                 -- 地区部中文名称
           , REPOFFICE_CODE                 -- 代表处编码
           , REPOFFICE_CN_NAME              -- 代表处中文名称
           , SPART_CODE                     -- SPART编码
           , SPART_DESC                     -- SPART描述
           , BG_CODE                        -- BG编码
           , BG_CN_NAME                     -- BG中文名称
           , OVERSEA_FLAG                   -- 国内海外标识
		   , SOFTWARE_MARK
           , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
           , REPLACE_RELATION_NAME          -- 替换关系名称
           , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
           , RELATION_TYPE                  -- 关系（ 替换 、收编）
           , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , SUM(RMB_COST_AMT)  AS RMB_COST_AMT -- 标准成本
        FROM SPART_LV0_REPL_INTERVAL_TMP T1
       GROUP BY VERSION_ID
           , LV0_CODE
           , LV0_CN_NAME
           , REGION_CODE
           , REGION_CN_NAME
           , REPOFFICE_CODE
           , REPOFFICE_CN_NAME
           , SPART_CODE
           , SPART_DESC
           , BG_CODE
           , BG_CN_NAME
           , OVERSEA_FLAG
		   , SOFTWARE_MARK
           , VIEW_FLAG
           , REPLACE_RELATION_NAME
           , REPLACE_RELATION_TYPE
           , RELATION_TYPE
           , CODE_TYPE
      ),
      -- 关系名称到LV0层级期间（T-1年+T年YTD）金额
      SPART_LV0_CUS_REPL_INFO_TMP3 AS(
      SELECT VERSION_ID                     -- 版本ID
         , LV0_CODE                       -- 重量级团队LV0编码
         , LV0_CN_NAME                    -- 重量级团队LV0名称
         , REGION_CODE                    -- 地区部编码
         , REGION_CN_NAME                 -- 地区部中文名称
         , REPOFFICE_CODE                 -- 代表处编码
         , REPOFFICE_CN_NAME              -- 代表处中文名称
         , BG_CODE                        -- BG编码
         , BG_CN_NAME                     -- BG中文名称
         , OVERSEA_FLAG                   -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
         , REPLACE_RELATION_NAME          -- 替换关系名称
         , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
         , RELATION_TYPE                  -- 关系（ 替换 、收编）
         , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , SUM(RMB_COST_AMT) AS RMB_COST_AMT -- 标准成本
        FROM SPART_LV0_CUS_REPL_INFO_TMP1
       GROUP BY VERSION_ID
         , LV0_CODE
         , LV0_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE
         , CODE_TYPE
      ),
      -- SPART+关系名称到LV0层级期间（T-1年+T年YTD）金额/关系名称到LV0层级期间（T-1年+T年YTD）金额
      SPART_LV0_CUS_REPL_INFO_TMP4 AS(
      SELECT T1.VERSION_ID                      -- 版本ID
           , T1.LV0_CODE                        -- LV0编码
           , T1.LV0_CN_NAME                     -- LV0名称
           , T1.REGION_CODE                     -- 地区部编码
           , T1.REGION_CN_NAME                  -- 地区部中文名称
           , T1.REPOFFICE_CODE                  -- 代表处编码
           , T1.REPOFFICE_CN_NAME               -- 代表处中文名称
           , T1.SPART_CODE                      -- SPART编码
           , T1.SPART_DESC                      -- SPART描述
           , T1.BG_CODE                         -- BG编码
           , T1.BG_CN_NAME                      -- BG中文名称
           , T1.OVERSEA_FLAG                    -- 国内海外标识
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME           -- 替换关系名称
           , T1.REPLACE_RELATION_TYPE           -- 新老编码替换类型（一对一  、一对多 、多对多）
           , T1.RELATION_TYPE                   -- 关系（ 替换 、收编）
           , T1.CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
           , (CASE WHEN T2.RMB_COST_AMT = 0 THEN 0 ELSE T1.RMB_COST_AMT/T2.RMB_COST_AMT END) AS WEIGHT_RATE  -- 权重
        FROM SPART_LV0_CUS_REPL_INFO_TMP1 T1
        LEFT JOIN SPART_LV0_CUS_REPL_INFO_TMP3 T2
          ON T1.VERSION_ID            = T2.VERSION_ID
         AND T1.LV0_CODE              = T2.LV0_CODE
         AND T1.REGION_CODE           = T2.REGION_CODE
         AND T1.REPOFFICE_CODE        = T2.REPOFFICE_CODE
         AND T1.BG_CODE               = T2.BG_CODE
         AND T1.OVERSEA_FLAG          = T2.OVERSEA_FLAG
         AND T1.VIEW_FLAG             = T2.VIEW_FLAG
         AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
         AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
         AND T1.RELATION_TYPE         = T2.RELATION_TYPE
         AND T1.CODE_TYPE             = T2.CODE_TYPE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
      )
      -- 第二步：单个SPART虚化到LV0层级，然后汇总到LV0
      SELECT T1.VERSION_ID
           , T1.PERIOD_ID
           , CAST(T1.PERIOD_YEAR AS INT) AS PERIOD_YEAR
           , T1.BASE_PERIOD_ID
           , T1.LV0_CODE
           , T1.LV0_CN_NAME
           , NULL AS LV1_CODE
           , NULL AS LV1_CN_NAME
           , NULL AS LV2_CODE
           , NULL AS LV2_CN_NAME
           , NULL AS LV3_CODE
           , NULL AS LV3_CN_NAME
           , T1.LV0_CODE    AS GROUP_CODE
           , T1.LV0_CN_NAME AS GROUP_CN_NAME
           , NULL AS PARENT_CODE
           , NULL AS PARENT_CN_NAME
           , T1.REGION_CODE
           , T1.REGION_CN_NAME
           , T1.REPOFFICE_CODE
           , T1.REPOFFICE_CN_NAME
           , T1.BG_CODE
           , T1.BG_CN_NAME
           , T1.OVERSEA_FLAG
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME
           , T1.REPLACE_RELATION_TYPE
           , T1.RELATION_TYPE
           , T1.CODE_TYPE
           , ''LV0''       AS GROUP_LEVEL
           , SUM(T1.PROD_QTY)     AS PROD_QTY
           , SUM(T1.RMB_COST_AMT) AS RMB_COST_AMT
           , ROUND(SUM(NVL(T1.COST_INDEX,0)*NVL(T2.WEIGHT_RATE,0)),10) AS COST_INDEX
        FROM SPART_LV0_CUS_REPL_INFO_TMP2 T1
        LEFT JOIN SPART_LV0_CUS_REPL_INFO_TMP4 T2
          ON T1.VERSION_ID            = T2.VERSION_ID
         AND T1.LV0_CODE              = T2.LV0_CODE
         AND T1.REGION_CODE           = T2.REGION_CODE
         AND T1.REPOFFICE_CODE        = T2.REPOFFICE_CODE
         AND T1.SPART_CODE            = T2.SPART_CODE
         AND T1.BG_CODE               = T2.BG_CODE
         AND T1.OVERSEA_FLAG          = T2.OVERSEA_FLAG
         AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
         AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
         AND T1.RELATION_TYPE         = T2.RELATION_TYPE
         AND T1.CODE_TYPE             = T2.CODE_TYPE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK   -- 202410版本新增
       GROUP BY T1.VERSION_ID
           , T1.PERIOD_ID
           , T1.PERIOD_YEAR
           , T1.BASE_PERIOD_ID
           , T1.LV0_CODE
           , T1.LV0_CN_NAME
           , T1.REGION_CODE
           , T1.REGION_CN_NAME
           , T1.REPOFFICE_CODE
           , T1.REPOFFICE_CN_NAME
           , T1.BG_CODE
           , T1.BG_CN_NAME
           , T1.OVERSEA_FLAG
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME
           , T1.REPLACE_RELATION_TYPE
           , T1.RELATION_TYPE
           , T1.CODE_TYPE
	  UNION ALL 
	  -- 同基指数虚化结果值
      SELECT T1.VERSION_ID
           , T1.PERIOD_ID
           , CAST(SUBSTR(T1.PERIOD_ID,1,4) AS INT) AS PERIOD_YEAR
           , '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID 
           , T1.LV0_CODE
           , T1.LV0_CN_NAME
           , NULL AS LV1_CODE
           , NULL AS LV1_CN_NAME
           , NULL AS LV2_CODE
           , NULL AS LV2_CN_NAME
           , NULL AS LV3_CODE
           , NULL AS LV3_CN_NAME
           , T1.LV0_CODE    AS GROUP_CODE
           , T1.LV0_CN_NAME AS GROUP_CN_NAME
           , NULL AS PARENT_CODE
           , NULL AS PARENT_CN_NAME
           , T1.REGION_CODE
           , T1.REGION_CN_NAME
           , T1.REPOFFICE_CODE
           , T1.REPOFFICE_CN_NAME
           , T1.BG_CODE
           , T1.BG_CN_NAME
           , T1.OVERSEA_FLAG
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME
           , T1.REPLACE_RELATION_TYPE
           , T1.RELATION_TYPE
           , T1.CODE_TYPE
           , ''LV0''       AS GROUP_LEVEL
           , NULL AS PROD_QTY
           , NULL AS RMB_COST_AMT
           , ROUND(SUM(NVL(T1.COST_INDEX,0)),10) AS COST_INDEX
		FROM SAME_BASE_LV4_INDEX_TMP T1
		GROUP BY T1.VERSION_ID
           , T1.PERIOD_ID
           , T1.LV0_CODE
           , T1.LV0_CN_NAME
           , T1.REGION_CODE
           , T1.REGION_CN_NAME
           , T1.REPOFFICE_CODE
           , T1.REPOFFICE_CN_NAME
           , T1.BG_CODE
           , T1.BG_CN_NAME
           , T1.OVERSEA_FLAG
		   , T1.SOFTWARE_MARK
           , T1.REPLACE_RELATION_NAME
           , T1.REPLACE_RELATION_TYPE
           , T1.RELATION_TYPE
           , T1.CODE_TYPE
      ';
    EXECUTE IMMEDIATE V_SQL;

      V_STEP_NUM := V_STEP_NUM+1;
      PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '需要到LV0层级的数据量：'||V_PARENT_LEVEL,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
      );

    END IF;

    -- 清理目标表对应版本ID的数据
    V_SQL := 'DELETE FROM '||V_TO_INDEX_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' AND CUSTOM_ID = '||F_CUSTOM_ID;  -- 删除版本数据

    EXECUTE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '删除月度虚化成本指数 '||V_TO_INDEX_TABLE||' 表版本为：'||V_VERSION_ID||' ，组合编码为：'||F_CUSTOM_ID||' 的数据',--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    -- 数据入到虚化成本指数目标表
    V_SQL := 'INSERT INTO '||V_TO_INDEX_TABLE||'(
           VERSION_ID                          /* 版本ID                                              */
         , CUSTOM_ID                           /* 组合ID                                              */
         , CUSTOM_CN_NAME                      /* 组合名称                                            */
         , PERIOD_YEAR                         /* 会计年                                              */
         , PERIOD_ID                           /* 会计月                                              */
         , BASE_PERIOD_ID                      /* 基期                                                */
         , PBI_DIM_CODE                        /* PBI维度编码（包括LV0、LV1、LV2、LV3）               */
         , PBI_DIM_CN_NAME                     /* PBI维度中文名称                                     */
         , GROUP_CODE                          /* 各层级编码                                          */
         , GROUP_CN_NAME                       /* 各层级中文名称                                      */
         , GROUP_LEVEL                         /* 各层级（LV3/LV2/LV1/LV0）                           */
         , GROUP_LEVEL_TYPE                    /* 层级类型（值包括：PBI等）                           */
         , COST_INDEX                          /* 成本指数值                                          */
         , REPLACE_RELATION_NAME               /* 替换关系名称                                        */
         , REPLACE_RELATION_TYPE               /* 替换关系类型（一对一 、一对多 、多对多）            */
         , RELATION_TYPE                       /* 关系（替换、收编）                                  */
         , CODE_TYPE                           /* 编码类型（NEW:新编码  OLD: 旧编码 ）                */
         , PARENT_CODE                         /*父类编码*/
         , PARENT_CN_NAME                      /*父类中文名称*/
         , REGION_CODE                         /* 地区部编码                                          */
         , REGION_CN_NAME                      /* 地区部中文名称                                      */
         , REPOFFICE_CODE                      /* 代表处编码                                          */
         , REPOFFICE_CN_NAME                   /* 代表处中文名称                                      */
         , BG_CODE                             /* BG编码                                              */
         , BG_CN_NAME                          /* BG中文名称                                          */
         , OVERSEA_FLAG                        /* 国内海外标识                                        */
		 , SOFTWARE_MARK
         , GRANULARITY_TYPE                    /* 重量级团队目录（IRB 产业目录：INDUS 销售目录：PROD）*/
         , CREATED_BY                          /* 创建人                                              */
         , CREATION_DATE                       /* 创建时间                                            */
         , LAST_UPDATED_BY                     /* 修改人                                              */
         , LAST_UPDATE_DATE                    /* 修改时间                                            */
         , DEL_FLAG                            /* 删除标识(未删除：N，已删除：Y)                      */
    )
    SELECT VERSION_ID
         , '||F_CUSTOM_ID||' AS CUSTOM_ID
         , '''' AS CUSTOM_CN_NAME
         , PERIOD_YEAR
         , PERIOD_ID
         , BASE_PERIOD_ID
         , LV3_CODE    AS PBI_DIM_CODE
         , LV3_CN_NAME AS PBI_DIM_CN_NAME
         , LV3_CODE    AS GROUP_CODE
         , LV3_CN_NAME AS GROUP_CN_NAME
         , GROUP_LEVEL
         , ''PBI'' AS GROUP_LEVEL_TYPE
         , COST_INDEX
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE
         , CODE_TYPE
         , LV2_CODE    AS PARENT_CODE
         , LV2_CN_NAME AS PARENT_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
         , -1 AS CREATED_BY
 	       , CURRENT_TIMESTAMP AS CREATION_DATE
 	       , -1 AS LAST_UPDATED_BY
 	       , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
 	       , ''N'' AS DEL_FLAG
      FROM SPART_PBI_COST_INFO_TMP
     WHERE GROUP_LEVEL = ''LV3''
     UNION ALL
    SELECT VERSION_ID
         , '||F_CUSTOM_ID||' AS CUSTOM_ID
         , '''' AS CUSTOM_CN_NAME
         , PERIOD_YEAR
         , PERIOD_ID
         , BASE_PERIOD_ID
         , LV2_CODE    AS PBI_DIM_CODE
         , LV2_CN_NAME AS PBI_DIM_CN_NAME
         , LV2_CODE    AS GROUP_CODE
         , LV2_CN_NAME AS GROUP_CN_NAME
         , GROUP_LEVEL
         , ''PBI'' AS GROUP_LEVEL_TYPE
         , COST_INDEX
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE
         , CODE_TYPE
         , LV1_CODE    AS PARENT_CODE
         , LV1_CN_NAME AS PARENT_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
         , -1 AS CREATED_BY
 	       , CURRENT_TIMESTAMP AS CREATION_DATE
 	       , -1 AS LAST_UPDATED_BY
 	       , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
 	       , ''N'' AS DEL_FLAG
      FROM SPART_PBI_COST_INFO_TMP
     WHERE GROUP_LEVEL = ''LV2''
     UNION ALL
    SELECT VERSION_ID
         , '||F_CUSTOM_ID||' AS CUSTOM_ID
         , '''' AS CUSTOM_CN_NAME
         , PERIOD_YEAR
         , PERIOD_ID
         , BASE_PERIOD_ID
         , LV1_CODE    AS PBI_DIM_CODE
         , LV1_CN_NAME AS PBI_DIM_CN_NAME
         , LV1_CODE    AS GROUP_CODE
         , LV1_CN_NAME AS GROUP_CN_NAME
         , GROUP_LEVEL
         , ''PBI'' AS GROUP_LEVEL_TYPE
         , COST_INDEX
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE
         , CODE_TYPE
         , LV0_CODE    AS PARENT_CODE
         , LV0_CN_NAME AS PARENT_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
         , -1 AS CREATED_BY
 	       , CURRENT_TIMESTAMP AS CREATION_DATE
 	       , -1 AS LAST_UPDATED_BY
 	       , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
 	       , ''N'' AS DEL_FLAG
      FROM SPART_PBI_COST_INFO_TMP
     WHERE GROUP_LEVEL = ''LV1''
     UNION ALL
    SELECT VERSION_ID
         , '||F_CUSTOM_ID||' AS CUSTOM_ID
         , '''' AS CUSTOM_CN_NAME
         , PERIOD_YEAR
         , PERIOD_ID
         , BASE_PERIOD_ID
         , LV0_CODE    AS PBI_DIM_CODE
         , LV0_CN_NAME AS PBI_DIM_CN_NAME
         , LV0_CODE    AS GROUP_CODE
         , LV0_CN_NAME AS GROUP_CN_NAME
         , GROUP_LEVEL
         , ''PBI'' AS GROUP_LEVEL_TYPE
         , COST_INDEX
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE
         , CODE_TYPE
         , '''' AS PARENT_CODE
         , '''' AS PARENT_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
         , -1 AS CREATED_BY
 	       , CURRENT_TIMESTAMP AS CREATION_DATE
 	       , -1 AS LAST_UPDATED_BY
 	       , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
 	       , ''N'' AS DEL_FLAG
      FROM SPART_PBI_COST_INFO_TMP
     WHERE GROUP_LEVEL = ''LV0''
    ';

    EXECUTE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '数据入到月度虚化成本指数目标表，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => V_SQL,
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );


    -- 清理目标表对应版本ID的数据
    V_SQL := 'DELETE FROM '||V_TO_QTY_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' AND CUSTOM_ID = '||F_CUSTOM_ID;  -- 删除版本数据

    EXECUTE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '删除月度虚化成本偏差 '||V_TO_QTY_TABLE||' 表版本为：'||V_VERSION_ID||' ，组合编码为：'||F_CUSTOM_ID||' 的数据',--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    -- 数据入到月度虚化成本信息表
    V_SQL := 'INSERT INTO '||V_TO_QTY_TABLE||'(
           VERSION_ID                          /* 版本ID                                              */
         , CUSTOM_ID                           /* 组合ID                                              */
         , CUSTOM_CN_NAME                      /* 组合名称                                            */
         , PERIOD_YEAR                         /* 会计年                                              */
         , PERIOD_ID                           /* 会计月                                              */
         , BASE_PERIOD_ID                      /* 基期                                                */
         , PBI_DIM_CODE                        /* PBI维度编码（包括LV0、LV1、LV2、LV3）               */
         , PBI_DIM_CN_NAME                     /* PBI维度中文名称                                     */
         , GROUP_CODE                          /* 各层级编码                                          */
         , GROUP_CN_NAME                       /* 各层级中文名称                                      */
         , GROUP_LEVEL                         /* 各层级（SPART/LV3/LV2/LV1/LV0）                     */
         , GROUP_LEVEL_TYPE                    /* 层级类型（值包括：TOP-SPART、PBI等）                */
         , PART_QTY                            /* 数量                                                */
         , RMB_COST_AMT                        /* 标准成本                                            */
         , REPLACE_RELATION_NAME               /* 替换关系名称                                        */
         , REPLACE_RELATION_TYPE               /* 替换关系类型（一对一 、一对多 、多对多）            */
         , RELATION_TYPE                       /* 关系（替换、收编）                                  */
         , CODE_TYPE                           /* 编码类型（NEW:新编码  OLD: 旧编码 ）                */
         , PARENT_CODE                         /*父类编码*/
         , PARENT_CN_NAME                      /*父类中文名称*/
         , REGION_CODE                         /* 地区部编码                                          */
         , REGION_CN_NAME                      /* 地区部中文名称                                      */
         , REPOFFICE_CODE                      /* 代表处编码                                          */
         , REPOFFICE_CN_NAME                   /* 代表处中文名称                                      */
         , BG_CODE                             /* BG编码                                              */
         , BG_CN_NAME                          /* BG中文名称                                          */
         , OVERSEA_FLAG                        /* 国内海外标识                                        */
		 , SOFTWARE_MARK
         , GRANULARITY_TYPE                    /* 重量级团队目录：IRB 产业目录：INDUS 销售目录：PROD  */
         , CREATED_BY                          /* 创建人                                              */
         , CREATION_DATE                       /* 创建时间                                            */
         , LAST_UPDATED_BY                     /* 修改人                                              */
         , LAST_UPDATE_DATE                    /* 修改时间                                            */
         , DEL_FLAG                            /* 删除标识(未删除：N，已删除：Y)                      */
    )
    SELECT VERSION_ID
         , '||F_CUSTOM_ID||' AS CUSTOM_ID
         , '''' AS CUSTOM_CN_NAME
         , PERIOD_YEAR
         , PERIOD_ID
         , BASE_PERIOD_ID
         , LV3_CODE    AS PBI_DIM_CODE
         , LV3_CN_NAME AS PBI_DIM_CN_NAME
         , LV3_CODE    AS GROUP_CODE
         , LV3_CN_NAME AS GROUP_CN_NAME
         , GROUP_LEVEL
         , ''PBI'' AS GROUP_LEVEL_TYPE
         , PROD_QTY AS PART_QTY
         , RMB_COST_AMT
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE
         , CODE_TYPE
         , LV2_CODE    AS PARENT_CODE
         , LV2_CN_NAME AS PARENT_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
         , -1 AS CREATED_BY
 	       , CURRENT_TIMESTAMP AS CREATION_DATE
 	       , -1 AS LAST_UPDATED_BY
 	       , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
 	       , ''N'' AS DEL_FLAG
      FROM SPART_PBI_COST_INFO_TMP
     WHERE GROUP_LEVEL = ''LV3''
     UNION ALL
    SELECT VERSION_ID
         , '||F_CUSTOM_ID||' AS CUSTOM_ID
         , '''' AS CUSTOM_CN_NAME
         , PERIOD_YEAR
         , PERIOD_ID
         , BASE_PERIOD_ID
         , LV2_CODE    AS PBI_DIM_CODE
         , LV2_CN_NAME AS PBI_DIM_CN_NAME
         , LV2_CODE    AS GROUP_CODE
         , LV2_CN_NAME AS GROUP_CN_NAME
         , GROUP_LEVEL
         , ''PBI'' AS GROUP_LEVEL_TYPE
         , PROD_QTY AS PART_QTY
         , RMB_COST_AMT
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE
         , CODE_TYPE
         , LV1_CODE    AS PARENT_CODE
         , LV1_CN_NAME AS PARENT_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
         , -1 AS CREATED_BY
 	       , CURRENT_TIMESTAMP AS CREATION_DATE
 	       , -1 AS LAST_UPDATED_BY
 	       , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
 	       , ''N'' AS DEL_FLAG
      FROM SPART_PBI_COST_INFO_TMP
     WHERE GROUP_LEVEL = ''LV2''
     UNION ALL
    SELECT VERSION_ID
         , '||F_CUSTOM_ID||' AS CUSTOM_ID
         , '''' AS CUSTOM_CN_NAME
         , PERIOD_YEAR
         , PERIOD_ID
         , BASE_PERIOD_ID
         , LV1_CODE    AS PBI_DIM_CODE
         , LV1_CN_NAME AS PBI_DIM_CN_NAME
         , LV1_CODE    AS GROUP_CODE
         , LV1_CN_NAME AS GROUP_CN_NAME
         , GROUP_LEVEL
         , ''PBI'' AS GROUP_LEVEL_TYPE
         , PROD_QTY AS PART_QTY
         , RMB_COST_AMT
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE
         , CODE_TYPE
         , LV0_CODE    AS PARENT_CODE
         , LV0_CN_NAME AS PARENT_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
         , -1 AS CREATED_BY
 	       , CURRENT_TIMESTAMP AS CREATION_DATE
 	       , -1 AS LAST_UPDATED_BY
 	       , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
 	       , ''N'' AS DEL_FLAG
      FROM SPART_PBI_COST_INFO_TMP
     WHERE GROUP_LEVEL = ''LV1''
     UNION ALL
    SELECT VERSION_ID
         , '||F_CUSTOM_ID||' AS CUSTOM_ID
         , '''' AS CUSTOM_CN_NAME
         , PERIOD_YEAR
         , PERIOD_ID
         , BASE_PERIOD_ID
         , LV0_CODE    AS PBI_DIM_CODE
         , LV0_CN_NAME AS PBI_DIM_CN_NAME
         , LV0_CODE    AS GROUP_CODE
         , LV0_CN_NAME AS GROUP_CN_NAME
         , GROUP_LEVEL
         , ''PBI'' AS GROUP_LEVEL_TYPE
         , PROD_QTY AS PART_QTY
         , RMB_COST_AMT
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE
         , CODE_TYPE
         , '''' AS PARENT_CODE
         , '''' AS PARENT_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
         , -1 AS CREATED_BY
 	       , CURRENT_TIMESTAMP AS CREATION_DATE
 	       , -1 AS LAST_UPDATED_BY
 	       , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
 	       , ''N'' AS DEL_FLAG
      FROM SPART_PBI_COST_INFO_TMP
     WHERE GROUP_LEVEL = ''LV0''
    ';

    EXECUTE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '数据入到月度虚化成本信息目标表，数据量：'||SQL%ROWCOUNT||'，运行结束！',--日志描述
        F_FORMULA_SQL_TXT  => V_SQL,
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

  END IF;

  --收集统计信息
	V_SQL := 'ANALYSE '||V_TO_INDEX_TABLE;
	EXECUTE V_SQL;

	V_SQL := 'ANALYSE '||V_TO_QTY_TABLE;
	EXECUTE V_SQL;


  EXCEPTION
  	WHEN OTHERS THEN

      PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
         F_SP_NAME => V_SP_NAME,    -- SP名称
         F_STEP_NUM => V_STEP_NUM,
         F_CAL_LOG_DESC => V_SP_NAME||'：运行错误',-- 日志描述
         F_FORMULA_SQL_TXT  => V_SQL,
         F_DML_ROW_COUNT => SQL%ROWCOUNT,
         F_RESULT_STATUS => '0',
         F_ERRBUF => SQLSTATE  -- 错误编码
      ) ;

      X_RESULT_STATUS := 'FAILED';



END

$$
/

