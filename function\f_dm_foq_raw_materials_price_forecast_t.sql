-- Name: f_dm_foq_raw_materials_price_forecast_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foq_raw_materials_price_forecast_t(p_fcst_period_id integer, p_compare_period integer, p_index_code character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：2023-08-09
创建人  ：鲁广武  lwx1186472
背景描述：原材料价格预测表,提供给前台查询，包括计算差异额、差异率的功能
参数描述：参数一(p_fcst_period_id)：   传入 预测期  如：202308
          参数二(p_compare_period)：   传入 对比期  如：202306
		  参数三(p_index_code)：       传入指标编码 如：s20003505
		  参数四(x_success_flag):      返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_foq_raw_materials_price_forecast_t(202308,202306,'s20003505')
*/


declare
	v_sp_name varchar(200) := 'fin_dm_opt_foi.f_dm_foq_raw_materials_price_forecast_t('||p_fcst_period_id||','||p_compare_period||','||p_index_code||')';
	v_tbl_name varchar(100) := 'fin_dm_opt_foi.dm_foq_raw_materials_price_forecast_t';
	v_dml_row_count number default 0 ;
	
begin
	x_success_flag := '1';           --1表示成功	


	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '原材料价格预测表'||v_tbl_name||',开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

--判断传入参数格式
--参数一、参数二、参数三不为空
  if((p_fcst_period_id is null or p_fcst_period_id = '') 
	     or (p_compare_period is null or p_compare_period = '') 
		 or (p_index_code is null or p_index_code = ''))  then
	  perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入参数一：'||p_fcst_period_id||'，传入参数二：'||p_compare_period||'，传入参数三：'||p_index_code||'，传入参数一、传入参数二、传入参数三不能为空，请重新传入正确格式的参数，对应的格式分别为：yyyymm，yyyymm，''DD'' ！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => null,
        p_log_errbuf => null  --错误编码
      ) ;
    x_success_flag := 2001;
    return;

--传入参数长度为6 
 elseif(length(p_fcst_period_id)<>6 or length(p_compare_period)<>6) then 
	  perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入参数一：'||p_fcst_period_id||'，传入参数二：'||p_compare_period||'，传入参数一、传入参数二格式错误，请重新传入正确格式的参数，对应的格式分别为：yyyymm，yyyymm！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => null,
        p_log_errbuf => null  --错误编码
      ) ;
    x_success_flag := 2001;
    return; 

--传入参数一要大于参数二
 elseif(p_fcst_period_id <= p_compare_period) then 
	  perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入参数一：'||p_fcst_period_id||'，传入参数二：'||p_compare_period||'，传入参数二要小于传入参数一，重新传入参数！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => null,
        p_log_errbuf => null  --错误编码
      ) ;
    x_success_flag := 2001;
    return; 

end if
;

  --前台展示最新的会计期版本数据，历史版本删除
    delete from fin_dm_opt_foi.dm_foq_raw_materials_price_forecast_t 
	      where substr(version_code,1,6) < p_fcst_period_id;

	v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写入日志
    perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '删除历史版本的数据量:'||v_dml_row_count||',结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;   

   
--判断表中是否有数据，有则直取，无则计算
if  exists(select 1 
            from fin_dm_opt_foi.dm_foq_raw_materials_price_forecast_t 
	       where version_code = p_fcst_period_id||p_compare_period
             and index_code = p_index_code
			 ) 
 then 


  -- 写入日志
    perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '原材料价格预测表'||v_tbl_name||',有值，直取值！结束运行！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

    	  
else
---表中没有传入参数的数据，需要计算
insert into fin_dm_opt_foi.dm_foq_raw_materials_price_forecast_t           --原材料价格预测表
            (
			   version_code	            --版本编码（编码规则：预测期||对比期）
              ,period_id	            --对比期
              ,data_date	            --数据日期
              ,data_type	            --数据类别（ACT 实际数、FCST 预测数）
              ,index_code	            --指标编码
              ,index_name	            --指标名称
              ,index_short_name	        --指标简称
              ,metal_short_cn_name	    --金属简称
              ,source_name	            --数据源
			  ,source_table             --来源表
              ,compare_type	            --对比类型（预实对比、预测对比）
              ,frequency_name	        --频率
              ,unit_name	            --单位
              ,data_value	            --数据值
              ,data_value_gap	        --差异额
              ,data_value_gap_ratio     --差异率
              ,remark	                --备注
              ,created_by	            --创建人
              ,creation_date	        --创建时间
              ,last_updated_by	        --修改人
              ,last_update_date	        --修改时间
              ,del_flag	                --是否删除
             )

with fcst_tmp as (
--预测期的预测数
 select distinct
        p_fcst_period_id||p_compare_period as version_code 
	   ,period_id              --会计期
	   ,index_code             --指标编码
	   ,index_name             --指标名称
	   ,index_short_name       --指标简称
       ,unit_name              --单位
	   ,frequency_name         --频率
	   ,source_name            --数据源
	   ,source_table           --来源表
	   ,data_value             --数据值
	   ,data_category          --数据类别
	   ,data_date              --数据时间
  from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i   --外购大宗商品与有色金属信息
 where data_category = '预测数'	
   and period_id = p_fcst_period_id             --传入最大预测期
   and index_code = p_index_code   --传入指标编码
   and del_flag = 'N'
),
fcst_act_tmp as (
--预测期的实际数  
 select distinct
        t1.version_code  
	   ,substr(t1.version_code,1,6)::numeric as period_id              --会计期
	   ,t1.index_code             --指标编码
	   ,t2.index_name             --指标名称
	   ,t2.index_short_name       --指标简称
       ,t2.unit_name              --单位
	   ,t2.frequency_name         --频率
	   ,t2.source_name            --数据源
	   ,t2.source_table           --来源表
	   ,t2.data_value             --数据值
	   ,t2.data_category          --数据类别
	   ,t2.data_date              --数据时间
  from fcst_tmp t1
  left join fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i t2    --外购大宗商品与有色金属信息
         on t1.index_code       = t2.index_code           
        and t2.data_category = '实际数'	   
        and t2.period_id < substr(t1.version_code,1,6)             --传入最大预测期
        and t2.period_id >= substr(t1.version_code,7,6)            --对比期
        and t2.index_code = p_index_code    --传入指标编码  
union all 
 select distinct 
        version_code
	   ,period_id              --会计期
	   ,index_code             --指标编码
	   ,index_name             --指标名称
	   ,index_short_name       --指标简称
       ,unit_name              --单位
	   ,frequency_name         --频率
	   ,source_name            --数据源
	   ,source_table           --来源表
	   ,data_value             --数据值
	   ,data_category          --数据类别
	   ,data_date              --数据时间
  from fcst_tmp 
),
--对比期的预测数据
diff_fcst_tmp as (
 select distinct
        p_fcst_period_id||p_compare_period as version_code 
	   ,p_compare_period as period_id              --会计期
	   ,index_code             --指标编码
	   ,index_name             --指标名称
	   ,index_short_name       --指标简称
       ,unit_name              --单位
	   ,frequency_name         --频率
	   ,source_name            --数据源
	   ,source_table           --来源表
	   ,data_value             --数据值
	   ,data_category          --数据类别
	   ,data_date              --数据时间
  from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i   --外购大宗商品与有色金属信息 
 where data_category = '预测数'	   
   and period_id = p_compare_period            --对比期
   and index_code = p_index_code    --传入指标编码 
),
value_gap_tmp as (
---计算差异额、差异率,标签打在预测期上
 select distinct 
        t1.version_code
	   ,t1.period_id                     --会计期
	   ,t1.index_code                    --指标编码
	   ,t1.index_name                    --指标名称
	   ,t1.index_short_name              --指标简称
       ,t1.unit_name                     --单位
	   ,t1.frequency_name                --频率
	   ,t1.source_name                   --数据源
	   ,t1.source_table                  --来源表
	   ,t1.data_value			         --数据值   
	   ,t1.data_date                     --数据时间
	   ,case when t1.data_category='实际数' and t2.data_value is not null then '预实对比' 
	         when t1.data_category='预测数' and t2.data_value is not null then '预测对比'
	         else null                            --预测期多出的会计期给空
			 end as compare_type                  --对比类型（预实对比、预测对比）
	   ,case when t1.data_category='实际数' then '实际数' 
	         when t1.data_category='预测数' then '预测数'
			 end as data_type                      --数据类别（ACT 实际数、FCST 预测数）
       ,case when t2.data_value is null then null
             else (nvl(t1.data_value,0)-nvl(t2.data_value,0))
             end as data_value_gap                 --差异额
       ,case when nvl(t2.data_value,0) = 0 or nvl(t2.data_value,0) is null then null
	         else (nvl(t1.data_value,0)-nvl(t2.data_value,0))/nvl(t2.data_value,0)   
			 end as data_value_gap_ratio           --差异率
  from fcst_act_tmp t1                --预测期数据
  left join diff_fcst_tmp t2          --对比期数据
         on t1.version_code     = t2.version_code
		and t1.index_code       = t2.index_code         
        and t1.data_date        = t2.data_date
union all
 select distinct
        t2.version_code 
	   ,t2.period_id                     --会计期
	   ,t2.index_code                    --指标编码
	   ,t2.index_name                    --指标名称
	   ,t2.index_short_name              --指标简称
       ,t2.unit_name                     --单位
	   ,t2.frequency_name                --频率
	   ,t2.source_name                   --数据源
	   ,t2.source_table                  --来源表
	   ,t2.data_value			         --数据值   
	   ,t2.data_date                     --数据时间
	   ,null as compare_type             --对比类型（预实对比、预测对比）
	   ,case when t2.data_category='预测数' then '预测数' 
	         end as data_type            --数据类别（ACT 实际数、FCST 预测数）  
       ,null::numeric as data_value_gap  --差异额
       ,null::numeric as data_value_gap_ratio    --差异率
  from fcst_act_tmp t1                --预测期数据
  join diff_fcst_tmp t2          --对比期数据
    on t1.version_code     = t2.version_code
   and t1.index_code       = t2.index_code         
   and t1.data_date        = t2.data_date		
)
---取最大会计期下的预测数的金属简称
      select distinct 
			 t1.version_code	              --版本编码（编码规则：预测期||对比期）
            ,t1.period_id	                  --会计期
            ,t1.data_date	                  --数据日期
            ,t1.data_type	                  --数据类别（ACT 实际数、FCST 预测数）
            ,t1.index_code	                  --指标编码
            ,t1.index_name	                  --指标名称
            ,t1.index_short_name	          --指标简称
            ,t2.index_short_name as metal_short_cn_name	          --金属简称
            ,t1.source_name	                  --数据源
			,t1.source_table                  --来源表
            ,t1.compare_type	              --对比类型（预实对比、预测对比）
            ,t1.frequency_name	              --频率
            ,t1.unit_name	                  --单位
            ,t1.data_value	                  --数据值
            ,t1.data_value_gap	              --差异额
            ,t1.data_value_gap_ratio          --差异率
			,'' as remark							---备注
			,-1 as created_by                       ---创建人
			,current_timestamp as creation_date     ---创建时间
			,-1 as last_updated_by                  ---修改人
			,current_timestamp as last_update_date  ---修改时间
			,'N' as del_flag                         ---是否删除
       from value_gap_tmp t1
  left join (select index_code,index_short_name,max(period_id) as period_id 
               from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i
			  where data_category='预测数'
              group by index_code,index_short_name)t2 
         on t1.index_code=t2.index_code		 
;

	v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 结束日志
    perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '原材料价格预测表'||v_tbl_name||',计算后的数据量:'||v_dml_row_count||',结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;


end if
;


exception
  	when others then

      perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		p_log_row_count => null,
		p_log_errbuf => sqlstate  --错误编码
        ) ;
	x_success_flag := '2001';	       --2001表示失败
	
    --收集统计信息
    analyse fin_dm_opt_foi.dm_foq_raw_materials_price_forecast_t;	

end;
$$
/

