-- Name: f_dm_fom_month_rate_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_month_rate_t(f_caliber_flag character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间：2023-12-11
创建人  ：黄心蕊 hwx1187045
背景描述：月度分析-同环比表数据初始化
参数描述：参数一(F_CALIBER_FLAG)：'E'为EMS，'M'为自制
		  参数二(F_VERSION_ID)：运行版本号
		  参数三(X_SUCCESS_FLAG)：运行状态返回值 ‘1’为成功，‘0’为失败
来源表	：FIN_DM_OPT_FOI.DM_FOM_MONTH_COST_IDX_T 月度分析指数表
目标表	：FIN_DM_OPT_FOI.DM_FOM_MONTH_RATE_T 月度分析指数表
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOM_MONTH_RATE_T('E'); EMS一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOM_MONTH_RATE_T('M'); 自制一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME        VARCHAR(50) := 'FIN_DM_OPT_FOI.f_dm_fom_month_rate_t';
  V_VERSION        BIGINT; --版本号
  V_BASE_PERIOD_ID INT := TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '01'); --基期会计期
  V_CALIBER_FLAG   VARCHAR(2) := F_CALIBER_FLAG; --维度标识
  
BEGIN 
X_SUCCESS_FLAG := '1';

--日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  --版本号入参判断，当入参为空，取规格品清单最新版本号
  IF F_ITEM_VERSION IS NULL THEN
    SELECT VERSION_ID INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
    --入参不为空，则以入参为版本号
  ELSE
    V_VERSION := F_ITEM_VERSION;
  END IF;
  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
 
--1.删除同版本同口径数据 
DELETE FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_RATE_T WHERE VERSION_ID = V_VERSION AND CALIBER_FLAG = V_CALIBER_FLAG;

  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 2,
  F_CAL_LOG_DESC => '删除'''||V_CALIBER_FLAG||''' 口径数据成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
  
-- 2.同环比计算
  WITH LEV_INDEX AS
   (SELECT PERIOD_ID,
			SUBSTR(PERIOD_ID, 5, 2) AS MONTH_DAY,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           BUSSINESS_OBJECT_CODE,
           BUSSINESS_OBJECT_CN_NAME,
           SHIPPING_OBJECT_CODE,
           SHIPPING_OBJECT_CN_NAME,
           MANUFACTURE_OBJECT_CODE,
           MANUFACTURE_OBJECT_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           PARENT_CODE,
           PARENT_CN_NAME,
		   CALIBER_FLAG
      FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_COST_IDX_T
     WHERE VERSION_ID = V_VERSION
       AND BASE_PERIOD_ID = V_BASE_PERIOD_ID
	   AND CALIBER_FLAG = V_CALIBER_FLAG ),
  
  BASE_YOY AS
   (SELECT PERIOD_ID,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           BUSSINESS_OBJECT_CODE,
           BUSSINESS_OBJECT_CN_NAME,
           SHIPPING_OBJECT_CODE,
           SHIPPING_OBJECT_CN_NAME,
           MANUFACTURE_OBJECT_CODE,
           MANUFACTURE_OBJECT_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY CALIBER_FLAG, LV0_CODE, NVL(LV1_CODE, 'LV1'), NVL(BUSSINESS_OBJECT_CODE, 'BOD'), NVL(SHIPPING_OBJECT_CODE, 'SOD'), NVL(MANUFACTURE_OBJECT_CODE, 'MOD'), GROUP_CODE, MONTH_DAY ORDER BY PERIOD_ID) AS YOY_PERIOD_ID,
           LAG(COST_INDEX, 1, NULL) OVER(PARTITION BY CALIBER_FLAG, LV0_CODE, NVL(LV1_CODE, 'LV1'), NVL(BUSSINESS_OBJECT_CODE, 'BOD'), NVL(SHIPPING_OBJECT_CODE, 'SOD'), NVL(MANUFACTURE_OBJECT_CODE, 'MOD'), GROUP_CODE, MONTH_DAY ORDER BY PERIOD_ID) AS YOY_COST_INDEX,
           LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY CALIBER_FLAG, LV0_CODE, NVL(LV1_CODE, 'LV1'), NVL(BUSSINESS_OBJECT_CODE, 'BOD'), NVL(SHIPPING_OBJECT_CODE, 'SOD'), NVL(MANUFACTURE_OBJECT_CODE, 'MOD'), GROUP_CODE ORDER BY PERIOD_ID) AS POP_PERIOD_ID,
           LAG(COST_INDEX, 1, NULL) OVER(PARTITION BY CALIBER_FLAG, LV0_CODE, NVL(LV1_CODE, 'LV1'), NVL(BUSSINESS_OBJECT_CODE, 'BOD'), NVL(SHIPPING_OBJECT_CODE, 'SOD'), NVL(MANUFACTURE_OBJECT_CODE, 'MOD'), GROUP_CODE ORDER BY PERIOD_ID) AS POP_COST_INDEX,
           PARENT_CODE,
           PARENT_CN_NAME,
           CALIBER_FLAG
      FROM LEV_INDEX)
  
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_MONTH_RATE_T
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     BASE_PERIOD_ID,
     LV0_CODE,
     LV0_CN_NAME,
     LV1_CODE,
     LV1_CN_NAME,
     BUSSINESS_OBJECT_CODE,
     BUSSINESS_OBJECT_CN_NAME,
     SHIPPING_OBJECT_CODE,
     SHIPPING_OBJECT_CN_NAME,
     MANUFACTURE_OBJECT_CODE,
     MANUFACTURE_OBJECT_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     RATE,
     RATE_FLAG,
     PARENT_CODE,
     PARENT_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     CALIBER_FLAG)
  SELECT V_VERSION AS VERSION_ID,
         LEFT(PERIOD_ID, 4),
         PERIOD_ID,
         V_BASE_PERIOD_ID AS BASE_PERIOD_ID,
         LV0_CODE,
         LV0_CN_NAME,
         LV1_CODE,
         LV1_CN_NAME,
         BUSSINESS_OBJECT_CODE,
         BUSSINESS_OBJECT_CN_NAME,
         SHIPPING_OBJECT_CODE,
         SHIPPING_OBJECT_CN_NAME,
         MANUFACTURE_OBJECT_CODE,
         MANUFACTURE_OBJECT_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ((COST_INDEX / NULLIF(YOY_COST_INDEX, 0)) - 1) AS RATE,
         'YOY' AS RATE_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         '-1' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         '-1' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         CALIBER_FLAG
    FROM BASE_YOY
   WHERE YOY_COST_INDEX IS NOT NULL
  UNION ALL
  SELECT V_VERSION AS VERSION_ID,
         LEFT(PERIOD_ID, 4),
         PERIOD_ID,
         V_BASE_PERIOD_ID AS BASE_PERIOD_ID,
         LV0_CODE,
         LV0_CN_NAME,
         LV1_CODE,
         LV1_CN_NAME,
         BUSSINESS_OBJECT_CODE,
         BUSSINESS_OBJECT_CN_NAME,
         SHIPPING_OBJECT_CODE,
         SHIPPING_OBJECT_CN_NAME,
         MANUFACTURE_OBJECT_CODE,
         MANUFACTURE_OBJECT_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ((COST_INDEX / NULLIF(POP_COST_INDEX, 0)) - 1) AS RATE,
         'POP' AS RATE_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         '-1' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         '-1' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         CALIBER_FLAG
    FROM BASE_YOY
   WHERE POP_COST_INDEX IS NOT NULL;
   
--3.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 3,
  F_CAL_LOG_DESC => '同环比表插数成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; $$
/

