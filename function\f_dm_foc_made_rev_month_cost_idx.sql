-- Name: f_dm_foc_made_rev_month_cost_idx; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_made_rev_month_cost_idx(f_industry_flag character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$


/***************************************************************************************************************************************************************
最近修改时间: 2024年6月20日11点18分
修改人   ：黄心蕊 hwx1187045
修改内容： 202407版本 新增IAS部分,及通用颗粒度和量纲颗粒度IAS新增LV4层级
修改时间:  2024年4月18日18点28分
修改人   ：黄心蕊 hwx1187045
修改内容： 202405版本 新增数字能源部分,及量纲增加COA层级
创建时间：2023-09-15
创建人  ：黄心蕊 hwx1187045
背景描述：月度分析-通用颗粒度反向视角指数插数
参数描述：参数一(F_ITEM_VERSION)：通用版本号
		  参数三(X_RESULT_STATUS)：运行状态返回值 ‘1’为成功，‘0’为失败
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_MADE_REV_MONTH_COST_IDX(''); --通用颗粒度反向视角一个版本数据
****************************************************************************************************************************************************************/

DECLARE
  V_SP_NAME                      VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_MADE_REV_MONTH_COST_IDX';
  V_VERSION                      BIGINT;
  V_STEP_NUM                     BIGINT := 0; --函数步骤号
  V_BASE_PERIOD_ID               INT := TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '01');
  V_LV0_PROD_RND_TEAM_CODE       TEXT; --字段LV0CODE
  V_LV0_PROD_RD_TEAM_CN_NAME     TEXT; --字段LV0NAME
  V_LV1_PROD_RND_TEAM_CODE       TEXT; --字段LV1CODE 
  V_LV1_PROD_RD_TEAM_CN_NAME     TEXT; --字段LV1NAME
  V_LV2_PROD_RND_TEAM_CODE       TEXT; --字段LV2CODE
  V_LV2_PROD_RD_TEAM_CN_NAME     TEXT; --字段LV2NAME
  V_LV3_PROD_RND_TEAM_CODE       TEXT; --字段LV3CODE
  V_LV3_PROD_RD_TEAM_CN_NAME     TEXT; --字段LV3NAME
  V_SQL_LV0_PROD_RND_TEAM_CODE   TEXT; --取数字段LV0CODE
  V_SQL_LV0_PROD_RD_TEAM_CN_NAME TEXT; --取数字段LV0NAME
  V_SQL_LV1_PROD_RND_TEAM_CODE   TEXT; --取数字段LV1CODE 
  V_SQL_LV1_PROD_RD_TEAM_CN_NAME TEXT; --取数字段LV1NAME
  V_SQL_LV2_PROD_RND_TEAM_CODE   TEXT; --取数字段LV2CODE
  V_SQL_LV2_PROD_RD_TEAM_CN_NAME TEXT; --取数字段LV2NAME
  V_SQL_LV3_PROD_RND_TEAM_CODE   TEXT; --取数字段LV3CODE
  V_SQL_LV3_PROD_RD_TEAM_CN_NAME TEXT; --取数字段LV3NAME
  V_TOP_SHIPPING_OBJECT_CODE     TEXT; --字段专家团CODE
  V_TOP_SHIPPING_OBJECT_CN_NAME     TEXT; --字段专家团NAME
  V_SQL_TOP_SHIPPING_OBJECT_CODE  TEXT; --取数字段专家团CODE
  V_SQL_TOP_SHIPPING_OBJECT_CN_NAME  TEXT; --取数字段专家团NAME

  V_TOP_MANUFACTURE_OBJECT_CODE        TEXT; --字段模块CODE
  V_TOP_MANUFACTURE_OBJECT_CN_NAME        TEXT; --字段模块NAME
  V_SQL_TOP_MANUFACTURE_OBJECT_CODE    TEXT; --取数字段品类CODE
  V_SQL_TOP_MANUFACTURE_OBJECT_CN_NAME    TEXT; --取数字段品类NAME
  V_SQL_MAN_CODE                 TEXT; --取数字段模块CODE
  V_SQL_MAN_CN_NAME              TEXT; --取数字段模块NAME
  V_VIEW_FLAG                    TEXT;
  V_CHILD_LEVEL                  TEXT;
  V_GROUP_LEVEL                  TEXT;
  V_GROUP_CODE                   TEXT;
  V_GROUP_NAME                   TEXT;
  V_SQL_PARENT_CODE              TEXT;
  V_SQL                          TEXT; --执行语句
  
  --202405版本 新增数字能源部分
  V_FROM_TABLE					TEXT;
  V_TO_TABLE                    TEXT;
  V_WEIGHT_TABLE				TEXT;
  
  --202407版本 
  V_LV4_PART		 TEXT :='';
  V_SQL_LV4_PART		 TEXT :='';
  V_LV3_PART			TEXT :='';
  V_SQL_LV3_PART		TEXT :='';
  V_REV_VIEW_FLAG		VARCHAR(5);
  V_BEGIN_NUM			INT;
  V_SQL_ITEM_PARENT		TEXT;

BEGIN
  
  X_RESULT_STATUS:='1';
  
  --日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  --1.版本号入参判断，当入参为空，取TOP规格品清单最新版本号
  IF F_ITEM_VERSION IS NULL THEN
  /*SELECT VERSION_ID INTO V_VERSION
    FROM FIN_DM_OPT_FOI.DM_FOC_MADE_TOP_ITEM_INFO_T
   ORDER BY LAST_UPDATE_DATE DESC
   LIMIT 1;*/
   
	IF F_INDUSTRY_FLAG = 'I' THEN
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
	 --20240327 修改版本号取数逻辑
	ELSIF F_INDUSTRY_FLAG = 'E' THEN		--202405版本 新增数字能源部分
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
	ELSIF F_INDUSTRY_FLAG = 'IAS' THEN	--202407版本 新增IAS部分
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
	END IF ; 
	--入参不为空，则以入参为版本号
  ELSE 
    V_VERSION := F_ITEM_VERSION;
  END IF;
  
  --写入日志
  V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行数据产业项目标识：'||F_INDUSTRY_FLAG||'，以及版本号：'||V_VERSION ,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
 
--2.ITEM指数插数
--2.1建临时表
RAISE NOTICE '临时表建表';
V_STEP_NUM:=V_STEP_NUM+1;
DROP TABLE IF EXISTS DM_MADE_MID_REV_MONTH_IDX;
CREATE TEMPORARY TABLE DM_MADE_MID_REV_MONTH_IDX (
	PERIOD_YEAR BIGINT,
	PERIOD_ID BIGINT,
	BASE_PERIOD_ID BIGINT,
	VIEW_FLAG CHARACTER VARYING(2),
	LV0_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
	LV0_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(200),
	LV1_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
	LV1_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(200),
	LV2_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
	LV2_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(200),
	LV3_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
	LV3_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(200),
	LV4_PROD_RND_TEAM_CODE CHARACTER VARYING(50),		--202407版本 IAS新增LV4层级
	LV4_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(200),
	TOP_SHIPPING_OBJECT_CODE CHARACTER VARYING(200),
	TOP_SHIPPING_OBJECT_CN_NAME CHARACTER VARYING(200),
	TOP_MANUFACTURE_OBJECT_CODE CHARACTER VARYING(200),
	TOP_MANUFACTURE_OBJECT_CN_NAME CHARACTER VARYING(200),
	MAN_CODE  CHARACTER VARYING(50),
	MAN_CN_NAME CHARACTER VARYING(1000),
	GROUP_CODE CHARACTER VARYING(200),
	GROUP_CN_NAME CHARACTER VARYING(2000),
	GROUP_LEVEL CHARACTER VARYING(50),
	COST_INDEX NUMERIC,
	PARENT_CODE CHARACTER VARYING(200),
	PARENT_CN_NAME CHARACTER VARYING(1000),
	CREATED_BY CHARACTER VARYING(200),
	CREATION_DATE TIMESTAMP WITHOUT TIME ZONE,
	LAST_UPDATED_BY CHARACTER VARYING(200),
	LAST_UPDATE_DATE TIMESTAMP WITHOUT TIME ZONE,
	DEL_FLAG CHARACTER VARYING(2),
	SCENARIO_FLAG CHARACTER VARYING(2),
	APPEND_FLAG CHARACTER VARYING(2),
	CALIBER_FLAG CHARACTER VARYING(2),
	OVERSEA_FLAG CHARACTER VARYING(2),
	LV0_PROD_LIST_CODE CHARACTER VARYING(50),
	LV0_PROD_LIST_CN_NAME CHARACTER VARYING(200),
	CUSTOM_FLAG CHARACTER VARYING(2)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(PARENT_CODE, GROUP_CODE);

  --2.2变量定义

  	IF F_INDUSTRY_FLAG = 'I' THEN
	  V_FROM_TABLE:= ' FIN_DM_OPT_FOI.DM_FOC_MADE_MID_MONTH_IDX_T ';
	  V_WEIGHT_TABLE:='FIN_DM_OPT_FOI.DM_FOC_MADE_REV_MONTH_WEIGHT_T';
	  V_TO_TABLE   :=' FIN_DM_OPT_FOI.DM_FOC_MADE_REV_MONTH_COST_IDX_T';
      V_REV_VIEW_FLAG   := '''3''';
      V_SQL_ITEM_PARENT := 'LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,
							LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      V_BEGIN_NUM       := 2;
	ELSIF F_INDUSTRY_FLAG = 'E' THEN		--202405版本 新增数字能源部分
	  V_FROM_TABLE:= ' FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_MID_MONTH_IDX_T ';
	  V_WEIGHT_TABLE:='FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_REV_MONTH_WEIGHT_T';
	  V_TO_TABLE   :=' FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_REV_MONTH_COST_IDX_T';
      V_REV_VIEW_FLAG   := '''3''';
      V_SQL_ITEM_PARENT := 'LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,
							LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      V_BEGIN_NUM       := 2;
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN
    --202407版本 新增IAS部分
    V_FROM_TABLE      := ' FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_MID_MONTH_IDX_T ';
    V_WEIGHT_TABLE    := ' FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_REV_MONTH_WEIGHT_T';
    V_TO_TABLE        := ' FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_REV_MONTH_COST_IDX_T';
    V_REV_VIEW_FLAG   := '''7'''; --202407版本 IAS新增视角7
    V_LV4_PART        := ' LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME,'; --202407版本 IAS新增LV4层级
    V_SQL_LV4_PART    := ' T2.LV4_PROD_RND_TEAM_CODE,T2.LV4_PROD_RD_TEAM_CN_NAME,';
    V_SQL_ITEM_PARENT := ' LV4_PROD_RND_TEAM_CODE AS PARENT_CODE,
							LV4_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
    V_BEGIN_NUM       := 1;
	END IF ; 
 
 V_SQL:='  
INSERT INTO DM_MADE_MID_REV_MONTH_IDX
  (PERIOD_YEAR,
   PERIOD_ID,
   BASE_PERIOD_ID,
   VIEW_FLAG,
   TOP_SHIPPING_OBJECT_CODE,
   TOP_SHIPPING_OBJECT_CN_NAME,
   TOP_MANUFACTURE_OBJECT_CODE,
   TOP_MANUFACTURE_OBJECT_CN_NAME,
   MAN_CODE,
   MAN_CN_NAME,
   LV0_PROD_RND_TEAM_CODE,
   LV0_PROD_RD_TEAM_CN_NAME,
   LV1_PROD_RND_TEAM_CODE,
   LV1_PROD_RD_TEAM_CN_NAME,
   LV2_PROD_RND_TEAM_CODE,
   LV2_PROD_RD_TEAM_CN_NAME,
   LV3_PROD_RND_TEAM_CODE,
   LV3_PROD_RD_TEAM_CN_NAME,
   '||V_LV4_PART||'			--202407版本 IAS新增LV4层级
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   COST_INDEX,
   PARENT_CODE,
   PARENT_CN_NAME,
   SCENARIO_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG,
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME)

  SELECT PERIOD_YEAR,
         PERIOD_ID,
         '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
         ''5'' AS VIEW_FLAG,
         SHIPPING_OBJECT_CODE,
         SHIPPING_OBJECT_CN_NAME,
         MANUFACTURE_OBJECT_CODE,
         MANUFACTURE_OBJECT_CN_NAME,
         MANUFACTURE_OBJECT_CODE AS MAN_CODE,
         MANUFACTURE_OBJECT_CN_NAME AS MAN_CN_NAME,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV3_PROD_RND_TEAM_CODE,
         LV3_PROD_RD_TEAM_CN_NAME,
		 '||V_LV4_PART||'			--202407版本 IAS新增LV4层级
         GROUP_CODE,
         GROUP_CN_NAME,
         ''ITEM'' AS GROUP_LEVEL,
         COST_INDEX,
         '||V_SQL_ITEM_PARENT||'		--202407版本 IAS新增LV4层级 IAS反向视角中ITEM父级为LV4
         SCENARIO_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM '||V_FROM_TABLE||' 
   WHERE VERSION_ID = '||V_VERSION||'
     AND BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'
	 AND GROUP_LEVEL = ''ITEM''
     AND VIEW_FLAG = '||V_REV_VIEW_FLAG||';
 ';
  
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;	

  
  --写入日志
  V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '反向视角规格品指数插数完成',
 -- F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  


--3.其他层级指数插数
--3.1变量定义
  /*查询字段定义*/
  V_LV0_PROD_RND_TEAM_CODE       := 'LV0_PROD_RND_TEAM_CODE,';
  V_LV0_PROD_RD_TEAM_CN_NAME     := 'LV0_PROD_RD_TEAM_CN_NAME,';
  V_TOP_SHIPPING_OBJECT_CODE     	:= 'TOP_SHIPPING_OBJECT_CODE,';
  V_TOP_SHIPPING_OBJECT_CN_NAME     := 'TOP_SHIPPING_OBJECT_CN_NAME,';
  V_TOP_MANUFACTURE_OBJECT_CODE     	:= 'TOP_MANUFACTURE_OBJECT_CODE, ';
  V_TOP_MANUFACTURE_OBJECT_CN_NAME      := 'TOP_MANUFACTURE_OBJECT_CN_NAME, ';

  
  V_LV1_PROD_RND_TEAM_CODE       := 'LV1_PROD_RND_TEAM_CODE,';
  V_LV1_PROD_RD_TEAM_CN_NAME     := 'LV1_PROD_RD_TEAM_CN_NAME,';
  V_LV2_PROD_RND_TEAM_CODE       := 'LV2_PROD_RND_TEAM_CODE,';
  V_LV2_PROD_RD_TEAM_CN_NAME     := 'LV2_PROD_RD_TEAM_CN_NAME,';
  
  V_SQL_LV0_PROD_RND_TEAM_CODE   := 'T1.LV0_PROD_RND_TEAM_CODE,';
  V_SQL_LV0_PROD_RD_TEAM_CN_NAME := 'T1.LV0_PROD_RD_TEAM_CN_NAME,';
  V_SQL_TOP_SHIPPING_OBJECT_CODE 	:= 'T1.TOP_SHIPPING_OBJECT_CODE,';
  V_SQL_TOP_SHIPPING_OBJECT_CN_NAME := 'T1.TOP_SHIPPING_OBJECT_CN_NAME,';
  V_SQL_TOP_MANUFACTURE_OBJECT_CODE     	:= 'T1.TOP_MANUFACTURE_OBJECT_CODE, ';
  V_SQL_TOP_MANUFACTURE_OBJECT_CN_NAME      := 'T1.TOP_MANUFACTURE_OBJECT_CN_NAME, ';


  V_SQL_LV1_PROD_RND_TEAM_CODE   	:= 'T1.LV1_PROD_RND_TEAM_CODE,';
  V_SQL_LV1_PROD_RD_TEAM_CN_NAME 	:= 'T1.LV1_PROD_RD_TEAM_CN_NAME,';
  V_SQL_LV2_PROD_RND_TEAM_CODE   	:= 'T1.LV2_PROD_RND_TEAM_CODE,';
  V_SQL_LV2_PROD_RD_TEAM_CN_NAME	:= 'T1.LV2_PROD_RD_TEAM_CN_NAME,';
  V_SQL_MAN_CODE					:= 'T1.MAN_CODE,';
  V_SQL_MAN_CN_NAME					:= 'T1.MAN_CN_NAME,';
  V_LV3_PART						:='LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,';
  V_SQL_LV3_PART					:='T1.LV3_PROD_RND_TEAM_CODE,T1.LV3_PROD_RD_TEAM_CN_NAME,';
  
  /*字段值定义*/
  /*
  V_GROUP_CODE                   := 'LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,';
  V_GROUP_NAME                   := 'LV3_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
  V_CHILD_LEVEL                  := '''ITEM''';
  V_GROUP_LEVEL                  := '''LV3''';
  V_SQL_PARENT_CODE              := 'T1.LV2_PROD_RND_TEAM_CODE AS PARENT_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, ';
  */
  
  /*字段值定义*/
  V_GROUP_CODE                   := 'LV4_PROD_RND_TEAM_CODE AS PARENT_CODE,';
  V_GROUP_NAME                   := 'LV4_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
  V_CHILD_LEVEL                  := '''ITEM''';
  V_GROUP_LEVEL                  := '''LV4''';
  V_SQL_PARENT_CODE              := 'T1.LV3_PROD_RND_TEAM_CODE AS PARENT_CODE, T1.LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, ';
  
  
--3.2循环卷积指数
  
  FOR V_LEVEL_NUM IN V_BEGIN_NUM .. 7 LOOP
  
    IF V_LEVEL_NUM = 1 THEN
      --3.2.1 LV4卷积
      NULL;
    
    ELSIF V_LEVEL_NUM = 2 THEN
      --3.2.2 LV3卷积
      V_LV3_PART        := '';
      V_SQL_LV3_PART    := '';
      V_GROUP_CODE      := 'LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_NAME      := 'LV3_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_SQL_PARENT_CODE := 'T1.LV2_PROD_RND_TEAM_CODE AS PARENT_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, ';
      V_GROUP_LEVEL     := '''LV3''';
    
      IF F_INDUSTRY_FLAG IN ('I', 'E') THEN
        V_CHILD_LEVEL := '''ITEM''';
      ELSIF F_INDUSTRY_FLAG = 'IAS' THEN
        V_CHILD_LEVEL := '''LV4''';
      END IF;
    ELSIF V_LEVEL_NUM = 3 THEN
    --3.2.3 LV2卷积
      V_LV2_PROD_RND_TEAM_CODE       := '';
      V_LV2_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV2_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV2_PROD_RD_TEAM_CN_NAME := '';
      V_GROUP_CODE                   := 'LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_NAME                   := 'LV2_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''LV3''';
      V_GROUP_LEVEL                  := '''LV2''';
      V_SQL_PARENT_CODE              := 'T1.LV1_PROD_RND_TEAM_CODE AS PARENT_CODE ,T1.LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, ';
    
    ELSIF V_LEVEL_NUM = 4 THEN
    --3.2.4 LV1卷积
      V_LV1_PROD_RND_TEAM_CODE       := '';
      V_LV1_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV1_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV1_PROD_RD_TEAM_CN_NAME := '';
      V_GROUP_CODE                   := 'LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_NAME                   := 'LV1_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''LV2''';
      V_GROUP_LEVEL                  := '''LV1''';
      V_SQL_PARENT_CODE              := 'T1.MAN_CODE AS PARENT_CODE , T1.MAN_CN_NAME AS PARENT_CN_NAME,';
    
    ELSIF V_LEVEL_NUM = 5 THEN
    --3.2.5 制造CODE卷积  本版本只有5视角，则本层级为制造对象   
      --V_TOP_CATEGORY_CODE        := '';
      --V_TOP_CATEGORY_CN_NAME     := '';
      --V_SQL_TOP_CATEGORY_CODE    := '';
      --V_SQL_TOP_CATEGORY_CN_NAME := '';
      V_GROUP_CODE               := 'TOP_MANUFACTURE_OBJECT_CODE  AS PARENT_CODE,';
      V_GROUP_NAME               := 'TOP_MANUFACTURE_OBJECT_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL              := '''LV1''';
      V_GROUP_LEVEL              := '''MANUFACTURE_OBJECT''';
      V_SQL_PARENT_CODE          := 'T1.TOP_SHIPPING_OBJECT_CODE AS PARENT_CODE, T1.TOP_SHIPPING_OBJECT_CN_NAME AS PARENT_CN_NAME,';
      V_SQL_MAN_CODE             := 'T1.PARENT_CODE,';
      V_SQL_MAN_CN_NAME          := 'T1.PARENT_NAME,';
                                            
    ELSIF V_LEVEL_NUM = 6 THEN
    --3.2.6 发货对象卷积  
  V_TOP_MANUFACTURE_OBJECT_CODE     	:= '';
  V_TOP_MANUFACTURE_OBJECT_CN_NAME      := '';
  V_SQL_TOP_MANUFACTURE_OBJECT_CODE     	:= '';
  V_SQL_TOP_MANUFACTURE_OBJECT_CN_NAME      := '';
	  
      V_GROUP_CODE                   := 'TOP_SHIPPING_OBJECT_CODE AS PARENT_CODE,';
      V_GROUP_NAME                   := 'TOP_SHIPPING_OBJECT_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''MANUFACTURE_OBJECT''';
      V_GROUP_LEVEL                  := '''SHIPPING_OBJECT''';
      V_SQL_PARENT_CODE              := 'T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, ';
	  
 
    ELSIF V_LEVEL_NUM = 7 THEN
    --3.2.7 ICT卷积  
	  V_TOP_SHIPPING_OBJECT_CODE	:='';
	  V_TOP_SHIPPING_OBJECT_CN_NAME  :='';
	  V_SQL_TOP_SHIPPING_OBJECT_CODE	:='';
	  V_SQL_TOP_SHIPPING_OBJECT_CN_NAME  :='';
      V_LV0_PROD_RND_TEAM_CODE       := '';
      V_LV0_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV0_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV0_PROD_RD_TEAM_CN_NAME := '';
      
      V_GROUP_CODE                   := 'LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_NAME                   := 'LV0_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''SHIPPING_OBJECT''';
      V_GROUP_LEVEL                  := '''LV0''';		--202405版本 ICT更新为LV0
      V_SQL_PARENT_CODE              := ''''','''',';
    
    END IF;
  
      V_SQL := '
	  WITH BASE_INDEX AS
	   (SELECT PERIOD_YEAR,
			 PERIOD_ID,
			 '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
			 VIEW_FLAG,
			 TOP_SHIPPING_OBJECT_CODE,
			  TOP_SHIPPING_OBJECT_CN_NAME,
			  TOP_MANUFACTURE_OBJECT_CODE, 
			  TOP_MANUFACTURE_OBJECT_CN_NAME,
			 MAN_CODE,
			 MAN_CN_NAME,
			 '||V_LV0_PROD_RND_TEAM_CODE
			  ||V_LV0_PROD_RD_TEAM_CN_NAME
			  ||V_LV1_PROD_RND_TEAM_CODE
			  ||V_LV1_PROD_RD_TEAM_CN_NAME
			  ||V_LV2_PROD_RND_TEAM_CODE
			  ||V_LV2_PROD_RD_TEAM_CN_NAME
			  ||V_LV3_PART			--202407版本 IAS新增LV4层级 2次循环依赖LV3字段
			  ||V_GROUP_CODE 
			  ||V_GROUP_NAME||'
			 GROUP_CODE,
			 COST_INDEX,
			 SCENARIO_FLAG,
			 CALIBER_FLAG,
			 OVERSEA_FLAG,
			 LV0_PROD_LIST_CODE,
			 LV0_PROD_LIST_CN_NAME,
			 GROUP_LEVEL
		  FROM DM_MADE_MID_REV_MONTH_IDX
		 WHERE UPPER(GROUP_LEVEL) = '||V_CHILD_LEVEL||'
		   AND BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'),
       
	  LEV_WEIGHT AS
	   (SELECT SHIPPING_OBJECT_CODE,
	           MANUFACTURE_OBJECT_CODE,
			   GROUP_CODE, 
			   WEIGHT_RATE,
			   PARENT_CODE, 
			   VIEW_FLAG,
			   CALIBER_FLAG,
			   OVERSEA_FLAG,  
			   LV0_PROD_LIST_CODE, 
			   LV0_PROD_LIST_CN_NAME,
			   
			   GROUP_LEVEL
		  FROM '||V_WEIGHT_TABLE||'		--202405版本 新增数字能源部分
		 WHERE VERSION_ID = '||V_VERSION||'
		   AND UPPER(GROUP_LEVEL) = '||V_CHILD_LEVEL||' ) 
       
	  INSERT INTO DM_MADE_MID_REV_MONTH_IDX
	   (PERIOD_YEAR,
		PERIOD_ID,
		BASE_PERIOD_ID,
		VIEW_FLAG,
		'||V_LV0_PROD_RND_TEAM_CODE
		 ||V_LV0_PROD_RD_TEAM_CN_NAME
		 ||V_TOP_SHIPPING_OBJECT_CODE
		 ||V_TOP_SHIPPING_OBJECT_CN_NAME
		 ||V_TOP_MANUFACTURE_OBJECT_CODE  
		 ||V_TOP_MANUFACTURE_OBJECT_CN_NAME||'
		MAN_CODE,
		MAN_CN_NAME,    
		'||V_LV1_PROD_RND_TEAM_CODE
		 ||V_LV1_PROD_RD_TEAM_CN_NAME
		 ||V_LV2_PROD_RND_TEAM_CODE
		 ||V_LV2_PROD_RD_TEAM_CN_NAME
		 ||V_LV3_PART			--202407版本 IAS新增LV4层级 2次循环依赖LV3字段
		 ||'
		GROUP_CODE,
		GROUP_CN_NAME,
		GROUP_LEVEL,
		COST_INDEX,
		PARENT_CODE,
		PARENT_CN_NAME,
		SCENARIO_FLAG,
		CALIBER_FLAG,
		OVERSEA_FLAG,
		LV0_PROD_LIST_CODE,
		LV0_PROD_LIST_CN_NAME
		)
		
	  SELECT T1.PERIOD_YEAR,
			 T1.PERIOD_ID,
			 '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
			 T1.VIEW_FLAG,
			 '||V_SQL_LV0_PROD_RND_TEAM_CODE
			  ||V_SQL_LV0_PROD_RD_TEAM_CN_NAME
			  ||V_SQL_TOP_SHIPPING_OBJECT_CODE
			  ||V_SQL_TOP_SHIPPING_OBJECT_CN_NAME
			  ||V_SQL_TOP_MANUFACTURE_OBJECT_CODE   
			  ||V_SQL_TOP_MANUFACTURE_OBJECT_CN_NAME
			  
			  
			  ||V_SQL_MAN_CODE
			  ||V_SQL_MAN_CN_NAME    
			  ||V_SQL_LV1_PROD_RND_TEAM_CODE
			  ||V_SQL_LV1_PROD_RD_TEAM_CN_NAME
			  ||V_SQL_LV2_PROD_RND_TEAM_CODE
			  ||V_SQL_LV2_PROD_RD_TEAM_CN_NAME
			  ||V_SQL_LV3_PART			--202407版本 IAS新增LV4层级 2次循环依赖LV3字段
			  ||'
			 T1.PARENT_CODE AS GROUP_CODE,
			 T1.PARENT_NAME AS GROUP_CN_NAME,
			 '||V_GROUP_LEVEL||' AS GROUP_LEVEL,
			 SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
			 '||V_SQL_PARENT_CODE||'
			 T1.SCENARIO_FLAG,
			 T1.CALIBER_FLAG,
			 T1.OVERSEA_FLAG,
			 T1.LV0_PROD_LIST_CODE,
			 T1.LV0_PROD_LIST_CN_NAME
		FROM BASE_INDEX T1
		JOIN LEV_WEIGHT T2
		  ON T1.GROUP_CODE = T2.GROUP_CODE
		 AND T1.VIEW_FLAG = T2.VIEW_FLAG
		 AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
		 AND T1.PARENT_CODE = T2.PARENT_CODE
		 AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
		 AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
		 AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
		 AND NVL(T1.TOP_SHIPPING_OBJECT_CN_NAME||T1.TOP_MANUFACTURE_OBJECT_CODE,''AA'') 
				= NVL(T2.SHIPPING_OBJECT_CODE||T2.MANUFACTURE_OBJECT_CODE,''AA'')
	   GROUP BY '||V_SQL_LV0_PROD_RND_TEAM_CODE
				 ||V_SQL_LV0_PROD_RD_TEAM_CN_NAME
				 ||V_SQL_TOP_SHIPPING_OBJECT_CODE
				 ||V_SQL_TOP_SHIPPING_OBJECT_CN_NAME
				 ||V_SQL_TOP_MANUFACTURE_OBJECT_CODE   
				 ||V_SQL_TOP_MANUFACTURE_OBJECT_CN_NAME
				 ||V_SQL_MAN_CODE
				 ||V_SQL_MAN_CN_NAME
				 ||V_SQL_LV1_PROD_RND_TEAM_CODE
				 ||V_SQL_LV1_PROD_RD_TEAM_CN_NAME
				 ||V_SQL_LV3_PART			--202407版本 IAS新增LV4层级 2次循环依赖LV3字段
				 ||V_SQL_LV2_PROD_RND_TEAM_CODE
				 ||V_SQL_LV2_PROD_RD_TEAM_CN_NAME||'
				 T1.SCENARIO_FLAG,
				 T1.PERIOD_YEAR,
				 T1.PERIOD_ID,
				 T1.PARENT_NAME,
				 T1.PARENT_CODE,
				 T1.VIEW_FLAG,
				 T1.GROUP_LEVEL,
				 T1.CALIBER_FLAG,
				 T1.OVERSEA_FLAG,  
				 T1.LV0_PROD_LIST_CODE, 
				 T1.LV0_PROD_LIST_CN_NAME;';

  DBMS_OUTPUT.PUT_LINE(V_SQL);
  DBMS_OUTPUT.PUT_LINE(V_GROUP_LEVEL||'卷积完成');
  EXECUTE IMMEDIATE V_SQL;	
  
  --写入日志
  V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '第'||V_LEVEL_NUM||'次循环开始，'||V_GROUP_LEVEL||'层级指数卷积完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  END LOOP;

  V_STEP_NUM:=V_STEP_NUM+1;
  
  V_SQL:='
  DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION;
  EXECUTE V_SQL;	--202405版本 新增数字能源部分
  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '反向视角删除同版本数据',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
   V_STEP_NUM:=V_STEP_NUM+1; --202405版本 新增数字能源部分
V_SQL:='
INSERT INTO '||V_TO_TABLE||'
  (VERSION_ID,
   PERIOD_YEAR,
   PERIOD_ID,
   BASE_PERIOD_ID,
   SHIPPING_OBJECT_CODE,
   SHIPPING_OBJECT_CN_NAME,
   MANUFACTURE_OBJECT_CODE,
   MANUFACTURE_OBJECT_CN_NAME,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   COST_INDEX,
   PARENT_CODE,
   PARENT_CN_NAME,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   VIEW_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG,
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME)
SELECT '||V_VERSION||' AS VERSION_ID,
       PERIOD_YEAR,
       PERIOD_ID,
       '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
	   TOP_SHIPPING_OBJECT_CODE,
	   TOP_SHIPPING_OBJECT_CN_NAME,
	   TOP_MANUFACTURE_OBJECT_CODE,
	   TOP_MANUFACTURE_OBJECT_CN_NAME,
       GROUP_CODE,
       GROUP_CN_NAME,
       GROUP_LEVEL,
       COST_INDEX,
       PARENT_CODE,
	   PARENT_CN_NAME,
       ''-1'' AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       ''-1'' AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       ''N'' AS DEL_FLAG,
       VIEW_FLAG,
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM DM_MADE_MID_REV_MONTH_IDX
 WHERE GROUP_LEVEL <> ''ITEM'';';
 EXECUTE V_SQL;
 
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '反向视角指数插数成功',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
EXECUTE 'ANALYZE '||V_TO_TABLE;
  
  RETURN 'SUCCESS';

  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; 

$$
/

