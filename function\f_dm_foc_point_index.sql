-- Name: f_dm_foc_point_index; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_point_index(f_industry_flag character varying, f_version_id bigint, f_base_period character varying, f_prod_code character varying, f_group_code character varying, f_view_flag character varying, f_sublevel character varying, f_l1_name character varying, f_l2_name character varying, f_caliber character varying, f_custom_id character varying, f_granularity_type character varying, f_dms_code character varying, f_oversea_flag character varying, f_lv0_prod_list_code character varying, f_grouplevel character varying, f_rev_code character varying, f_cost_type character varying, f_shipping_code character varying, f_spart_code character varying, f_coa_code character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2023-03-22
  创建人  ：唐钦
  修改时间：2023-6-25
  修改人：唐钦
  背景描述：指数计算-基期切换函数
  参数描述：x_success_flag ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_POINT_INDEX()
*/
DECLARE
  V_SP_NAME            VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_POINT_INDEX'; --存储过程名称
  V_STEP_NUM           BIGINT := 0; --步骤号
  V_VERSION_ID         BIGINT := F_VERSION_ID; --版本号ID
  V_DEFAULT_PERIOD     TEXT; -- 默认基期
  V_CHANGE_BASE_PERIOD TEXT := F_BASE_PERIOD; -- 切换后基期
  V_PROD_CODE          VARCHAR(200) := ''''||replace(F_PROD_CODE,',',''',''')||''''; -- 入参重量级团队CODE
  V_GROUP_CODE         VARCHAR(1000) := ''''||replace(F_GROUP_CODE,',',''',''')||''''; -- 需要切换基期的CODE入参
  V_VIEW_FLAG          VARCHAR(10) := F_VIEW_FLAG; -- 入参视角标识
  V_SQL                TEXT;
  V_VARA1              TEXT; -- 变量1
  V_VARA2              TEXT; -- 变量2
  V_SQL_PROFITS        TEXT;
  V_SQL_CALIBER        TEXT;
  V_TO_TABLE           VARCHAR(200);
  V_PROFITS_NAME       VARCHAR(200);
  V_SQL_PROFITS_NAME   VARCHAR(200);
  V_PROFITS_NAME1      VARCHAR(200);
  V_SQL1_PROFITS_NAME  VARCHAR(200);
  V_TEXT_PROFITS_NAME  VARCHAR(200);

  -- 2023/09月版本新增
  V_SQL_DMS_CODE  VARCHAR(500);
  V_REL_DMS_CODE  VARCHAR(500);
  V_REL1_DMS_CODE VARCHAR(500);
  V_DMS_CODE_TOTAL      VARCHAR(500);
  V_DMS_NAME      VARCHAR(500);
  V_IN_DMS_CODE   VARCHAR(500);
  V_IN_DMS_NAME   VARCHAR(500);
  V_SEQUENCE      VARCHAR(500);
  V_REL_DIMENSION_CODE   VARCHAR(500);
  V_REL1_DIMENSION_CODE  VARCHAR(500);
  V_DMS_CODE      VARCHAR(200) := ''''||REPLACE(F_DMS_CODE,',',''',''')||''''; 
  V_L1_NAME       VARCHAR(200) := ''''||REPLACE(F_L1_NAME,',',''',''')||''''; 
  V_L2_NAME       VARCHAR(200) := ''''||REPLACE(F_L2_NAME,',',''',''')||'''';
  V_REV_CODE      VARCHAR(200) := ''''||REPLACE(F_REV_CODE,',',''',''')||'''';
  V_SPART_CODE      VARCHAR(200) := ''''||REPLACE(F_SPART_CODE,',',''',''')||'''';
  V_CUSTOM_ID     VARCHAR(200) := F_CUSTOM_ID;
  
  -- 11月版本新增
  V_SHIPPING_CODE VARCHAR(200) := ''''||REPLACE(F_SHIPPING_CODE,',',''',''')||''''; 
  V_MADE_TOTAL    VARCHAR(500);
  V_REL_MADE_CODE VARCHAR(500);
  V_REL1_MADE_CODE VARCHAR(500);
  V_IN_MADE_TOTAL  VARCHAR(500);
  V_REV_TOTAL      VARCHAR(500);
  V_IN_REV_TOTAL   VARCHAR(500);
  V_REL_REV_CODE   VARCHAR(500);
  V_REL1_REV_CODE  VARCHAR(500);
  V_REL_SPART VARCHAR(500);
  V_REL1_SPART VARCHAR(500);
  V_SPART_TOTAL VARCHAR(200);
  V_IN_SPART_TOTAL VARCHAR(200);
  V_SQL_SPART VARCHAR(500);
  
  -- 202405版本新增
  V_COA_CODE VARCHAR(200) := ''''||REPLACE(F_COA_CODE,',',''',''')||'''';
  V_REL_COA VARCHAR(500);
  V_REL1_COA VARCHAR(500);
  V_COA_TOTAL VARCHAR(200);
  V_IN_COA_TOTAL VARCHAR(200);
  V_SQL_COA VARCHAR(500);
  V_VERSION_TABLE VARCHAR(100);
  V_ANNUAL_TABLE VARCHAR(100);
    
BEGIN
  X_RESULT_STATUS = '1';
  
  -- 重置公共变量
    V_PROFITS_NAME := ' PROFITS_NAME,
                        L1_NAME,
                        L2_NAME, ';
    V_PROFITS_NAME1 := 'T1.PROFITS_NAME,
                        T1.L1_NAME,
                        T1.L2_NAME, ';
    V_SQL_PROFITS_NAME := ' AND NVL(T1.L1_NAME,''SNULL'') = NVL(T3.L1_NAME,''SNULL'') 
                           AND NVL(T1.L2_NAME,''SNULL'') = NVL(T3.L2_NAME,''SNULL'') ';
    V_SQL1_PROFITS_NAME := ' AND NVL(T1.L1_NAME,''SNULL'') = NVL(T2.L1_NAME,''SNULL'')
                            AND NVL(T1.L2_NAME,''SNULL'') = NVL(T2.L2_NAME,''SNULL'') ';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

 -- 判断产业项目为ICT还是数字能源
  IF F_INDUSTRY_FLAG = 'I' THEN   -- ICT  
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T';
     V_ANNUAL_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ANNUAL_AMP_T';
  -- 判断入参是哪个成本类型，结果表不同
    IF F_COST_TYPE = 'P' AND F_GRANULARITY_TYPE = 'U' THEN
          V_TO_TABLE := ' FIN_DM_OPT_FOI.DM_FOC_MONTH_COST_IDX_T';
    ELSIF F_COST_TYPE = 'P' AND F_GRANULARITY_TYPE = 'P' THEN
          V_TO_TABLE := ' FIN_DM_OPT_FOI.DM_FOC_PFT_MONTH_COST_IDX_T'; 
    ELSIF F_COST_TYPE = 'P' AND F_GRANULARITY_TYPE = 'D' THEN
          V_TO_TABLE := ' FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_COST_IDX_T';
    ELSIF F_COST_TYPE = 'M' AND F_GRANULARITY_TYPE = 'U' THEN
          V_TO_TABLE := ' FIN_DM_OPT_FOI.DM_FOC_MADE_MONTH_COST_IDX_T';
    ELSIF F_COST_TYPE = 'M' AND F_GRANULARITY_TYPE = 'P' THEN
          V_TO_TABLE := ' FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_MONTH_COST_IDX_T';
    ELSIF F_COST_TYPE = 'M' AND F_GRANULARITY_TYPE = 'D' THEN
          V_TO_TABLE := ' FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MONTH_COST_IDX_T';
    END IF;
  ELSIF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源 
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T';
     V_ANNUAL_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_ANNUAL_AMP_T';
  -- 判断入参是哪个成本类型，结果表不同
    IF F_COST_TYPE = 'P' AND F_GRANULARITY_TYPE = 'U' THEN
          V_TO_TABLE := ' FIN_DM_OPT_FOI.DM_FOC_ENERGY_MONTH_COST_IDX_T';
    ELSIF F_COST_TYPE = 'P' AND F_GRANULARITY_TYPE = 'P' THEN
          V_TO_TABLE := ' FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_MONTH_COST_IDX_T';
    ELSIF F_COST_TYPE = 'P' AND F_GRANULARITY_TYPE = 'D' THEN
          V_TO_TABLE := ' FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MONTH_COST_IDX_T';
    ELSIF F_COST_TYPE = 'M' AND F_GRANULARITY_TYPE = 'U' THEN
          V_TO_TABLE := ' FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_MONTH_COST_IDX_T';
    ELSIF F_COST_TYPE = 'M' AND F_GRANULARITY_TYPE = 'P' THEN
          V_TO_TABLE := ' FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_MONTH_COST_IDX_T';
    ELSIF F_COST_TYPE = 'M' AND F_GRANULARITY_TYPE = 'D' THEN
          V_TO_TABLE := ' FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_MONTH_COST_IDX_T';
    END IF;
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407版本新增IAS 
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T';
     V_ANNUAL_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_ANNUAL_AMP_T';
  -- 判断入参是哪个成本类型，结果表不同
    IF F_COST_TYPE = 'P' AND F_GRANULARITY_TYPE = 'U' THEN
          V_TO_TABLE := ' FIN_DM_OPT_FOI.DM_FOC_IAS_MONTH_COST_IDX_T';
    ELSIF F_COST_TYPE = 'P' AND F_GRANULARITY_TYPE = 'P' THEN
          V_TO_TABLE := ' FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_MONTH_COST_IDX_T';
    ELSIF F_COST_TYPE = 'P' AND F_GRANULARITY_TYPE = 'D' THEN
          V_TO_TABLE := ' FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MONTH_COST_IDX_T';
    ELSIF F_COST_TYPE = 'M' AND F_GRANULARITY_TYPE = 'U' THEN
          V_TO_TABLE := ' FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_MONTH_COST_IDX_T';
    ELSIF F_COST_TYPE = 'M' AND F_GRANULARITY_TYPE = 'P' THEN
          V_TO_TABLE := ' FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_MONTH_COST_IDX_T';
    ELSIF F_COST_TYPE = 'M' AND F_GRANULARITY_TYPE = 'D' THEN
          V_TO_TABLE := ' FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_MONTH_COST_IDX_T';
    END IF;
  END IF;
     
  -- 默认基期取值
 V_SQL := '
  SELECT MAX(PERIOD_YEAR)-1||''01''
     FROM '||V_ANNUAL_TABLE||'
     WHERE VERSION_ID = (SELECT VERSION_ID 
                                  FROM '||V_VERSION_TABLE||'
                                  WHERE
                                      DEL_FLAG = ''N''
                                      AND STATUS = 1
                                      AND UPPER(DATA_TYPE) = ''CATEGORY''
                                      AND IS_RUNNING = ''N''   -- 取页面展示的版本数据
                                      ORDER BY LAST_UPDATE_DATE DESC
                                      LIMIT 1)';
    EXECUTE IMMEDIATE V_SQL INTO V_DEFAULT_PERIOD;
   
  -- 判断切换后的基期和默认基期是否一致，一致时，直接插入日志表，不一致执行切换逻辑
  IF V_CHANGE_BASE_PERIOD = V_DEFAULT_PERIOD THEN
  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '切换基期与默认基期一致，不执行切换基期逻辑',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   ELSE 
  -- 当入参是制造成本类型时，需增加制造成本2个对象的CODE和中文名称
  IF F_COST_TYPE = 'M' THEN
        V_MADE_TOTAL := '
             SHIPPING_OBJECT_CODE,
             SHIPPING_OBJECT_CN_NAME,
             MANUFACTURE_OBJECT_CODE,
             MANUFACTURE_OBJECT_CN_NAME,';
        V_IN_MADE_TOTAL := '
             T1.SHIPPING_OBJECT_CODE,
             T1.SHIPPING_OBJECT_CN_NAME,
             T1.MANUFACTURE_OBJECT_CODE,
             T1.MANUFACTURE_OBJECT_CN_NAME,';
        V_REL_MADE_CODE := ' AND NVL(T1.SHIPPING_OBJECT_CODE ,''SNULL'') = NVL(T3.SHIPPING_OBJECT_CODE ,''SNULL'')';
        V_REL1_MADE_CODE := ' AND NVL(T1.SHIPPING_OBJECT_CODE ,''SNULL1'') = NVL(T2.SHIPPING_OBJECT_CODE ,''SNULL1'')';
  END IF;
          
  -- 通用颗粒度的维度时
  IF F_GRANULARITY_TYPE = 'U' THEN 
             V_PROFITS_NAME := '';
             V_PROFITS_NAME1 := '';
             V_SQL_PROFITS_NAME := '';
             V_SQL1_PROFITS_NAME := '';
  
  -- 盈利颗粒度的维度时
  ELSIF F_GRANULARITY_TYPE = 'P' THEN
     IF F_L2_NAME IS NOT NULL THEN
       V_TEXT_PROFITS_NAME := ' AND L2_NAME IN ('||V_L2_NAME||')';  
       V_SQL_PROFITS := ' AND L1_NAME IN ('||V_L1_NAME||')';      
     ELSIF F_L2_NAME IS NULL 
       AND F_L1_NAME IS NOT NULL THEN
        V_SQL_PROFITS := ' AND L1_NAME IN ('||V_L1_NAME||')';  
     ELSIF F_L2_NAME IS NULL 
       AND F_L1_NAME IS NULL THEN
        V_TEXT_PROFITS_NAME := '';   
        V_SQL_PROFITS := '';
     END IF;   
        
  -- 量纲颗粒度的维度时
  ELSIF F_GRANULARITY_TYPE = 'D' THEN
        V_PROFITS_NAME := '';
        V_PROFITS_NAME1 := '';
        V_SQL_PROFITS_NAME := '';
        V_SQL1_PROFITS_NAME := '';
        V_DMS_CODE_TOTAL := 'DMS_CODE,
                       DIMENSION_CODE,
                       DIMENSION_SUBCATEGORY_CODE,
                       DIMENSION_SUB_DETAIL_CODE,
                       ';
        V_DMS_NAME:= 'DMS_CN_NAME,
                      DIMENSION_CN_NAME,
                      DIMENSION_SUBCATEGORY_CN_NAME,
                      DIMENSION_SUB_DETAIL_CN_NAME,
                      ';
        V_SPART_TOTAL := 'SPART_CODE,
                          SPART_CN_NAME,';
        V_IN_DMS_CODE := 'T1.DMS_CODE,
                          T1.DIMENSION_CODE,
                          T1.DIMENSION_SUBCATEGORY_CODE,
                          T1.DIMENSION_SUB_DETAIL_CODE,
                          ';
        V_IN_DMS_NAME := 'T1.DMS_CN_NAME,
                          T1.DIMENSION_CN_NAME,
                          T1.DIMENSION_SUBCATEGORY_CN_NAME,
                          T1.DIMENSION_SUB_DETAIL_CN_NAME,
                          '; 
        V_IN_SPART_TOTAL := 'T1.SPART_CODE,
                             T1.SPART_CN_NAME,';
        V_REL_SPART := ' AND NVL(T1.SPART_CODE,''SNULL4'') = NVL(T3.SPART_CODE,''SNULL4'') ';
        V_REL1_SPART := ' AND NVL(T1.SPART_CODE,''SNULL5'') = NVL(T2.SPART_CODE,''SNULL5'') ';
        V_REL_DMS_CODE := ' AND NVL(T1.DMS_CODE,''SNULL'') = NVL(T3.DMS_CODE,''SNULL'')';
        V_REL1_DMS_CODE := ' AND NVL(T1.DMS_CODE,''SNULL1'') = NVL(T2.DMS_CODE,''SNULL1'')';
        V_REL_DIMENSION_CODE := ' AND NVL(T1.DIMENSION_CODE,''SNULL2'') = NVL(T3.DIMENSION_CODE,''SNULL2'')
                                  AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''SNULL8'') = NVL(T3.DIMENSION_SUBCATEGORY_CODE,''SNULL8'')
                                  AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''SNULL9'') = NVL(T3.DIMENSION_SUB_DETAIL_CODE,''SNULL9'') ';
        V_REL1_DIMENSION_CODE := ' AND NVL(T1.DIMENSION_CODE,''SNULL3'') = NVL(T2.DIMENSION_CODE,''SNULL3'') 
                                   AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''SNULL6'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''SNULL6'') 
                                   AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''SNULL7'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''SNULL7'') ';  
       IF F_SPART_CODE IS NULL THEN
          V_SQL_SPART := '';
       ELSE 
          V_SQL_SPART := ' AND SPART_CODE IN ('||V_SPART_CODE||') '; 
        END IF;

  -- 当产业项目标识为：E时，加COA层级变量
       IF F_INDUSTRY_FLAG = 'E' THEN 
        V_COA_TOTAL := 'COA_CODE,
                        COA_CN_NAME,';
        V_IN_COA_TOTAL := 'T1.COA_CODE,
                           T1.COA_CN_NAME,';
        V_REL_COA := ' AND NVL(T1.COA_CODE,''SNU1'') = NVL(T3.COA_CODE,''SNU1'') ';
        V_REL1_COA := ' AND NVL(T1.COA_CODE,''SNU2'') = NVL(T2.COA_CODE,''SNU2'') ';
        -- 判断COA_CODE为空时，判断条件逻辑处理不同
           IF F_COA_CODE IS NULL THEN
              V_SQL_COA := '';
           ELSE 
              V_SQL_COA := ' AND COA_CODE IN ('||V_COA_CODE||') '; 
           END IF;
       END IF;

     -- F_DMS_CODE有值时，加上筛选条件，否则置空
      IF F_DMS_CODE IS NOT NULL THEN
        V_SQL_DMS_CODE := ' AND DMS_CODE IN ('||V_DMS_CODE||')'; 
      ELSE V_SQL_DMS_CODE := '';
      END IF;
        
  ELSE NULL;
  END IF;
  
  -- 判断计算的数据是自选组合的指数还是正常维度指数逻辑
  IF F_CUSTOM_ID IS NULL AND F_REV_CODE IS NULL THEN   -- 自选组合ID字段为空，且不是反向视角，走正常逻辑
  
  -- 删除相同的GROUP_CODE的数据
    V_SQL := 'DELETE FROM '||V_TO_TABLE||' WHERE BASE_PERIOD_ID = '''|| V_CHANGE_BASE_PERIOD ||'''
         AND PROD_RND_TEAM_CODE IN ('||V_PROD_CODE||') AND VIEW_FLAG = '''||V_VIEW_FLAG||''' AND GROUP_CODE IN ('||V_GROUP_CODE||') AND LV0_PROD_LIST_CODE = '''||F_LV0_PROD_LIST_CODE||'''
         AND CALIBER_FLAG = '''||F_CALIBER||''' AND VERSION_ID = '||V_VERSION_ID||V_SQL_PROFITS||V_SQL_COA||V_SQL_SPART||V_TEXT_PROFITS_NAME||V_SQL_DMS_CODE||' AND OVERSEA_FLAG = '''||F_OVERSEA_FLAG||'''';
      DBMS_OUTPUT.PUT_LINE(V_SQL);   
      EXECUTE IMMEDIATE V_SQL;  
      DBMS_OUTPUT.PUT_LINE('数据删除完成');  
          
  --1.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除版本号为：'||V_VERSION_ID||'且基期为：'||V_CHANGE_BASE_PERIOD||'的'||V_TO_TABLE||'表数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
     
     -- 判断入参CODE的子级是什么层级
     IF UPPER(F_SUBLEVEL) IN ('ITEM','MANUFACTURE_OBJECT') AND F_COST_TYPE = 'M' THEN
         V_VARA1 := ' AND PROD_RND_TEAM_CODE IN ('||V_PROD_CODE||')
                      AND SHIPPING_OBJECT_CODE IN ('||V_SHIPPING_CODE||')';
     ELSIF UPPER(F_SUBLEVEL) IN ('ITEM','CATEGORY','MODL','CEG','L1','L2','SHIPPING_OBJECT') THEN
         V_VARA1 := ' AND PROD_RND_TEAM_CODE IN ('||V_PROD_CODE||')';
     ELSIF UPPER(F_SUBLEVEL) IN ('SPART') THEN 
         V_SQL_DMS_CODE := '';
         V_SQL_SPART := '';
         V_VARA1 := ' AND PROD_RND_TEAM_CODE IN ('||V_PROD_CODE||')';
     ELSIF UPPER(F_SUBLEVEL) IN ('DIMENSION','SUBCATEGORY','SUB_DETAIL') THEN 
         V_SQL_DMS_CODE := '';
         V_SQL_SPART := '';
         V_VARA1 := ' AND PROD_RND_TEAM_CODE IN ('||V_PROD_CODE||')';
     ELSIF UPPER(F_SUBLEVEL) IN ('COA') THEN 
         V_SQL_DMS_CODE := '';
         V_SQL_SPART := '';
         V_SQL_COA := '';
         V_VARA1 := ' AND PROD_RND_TEAM_CODE IN ('||V_PROD_CODE||')';
     ELSIF UPPER(F_SUBLEVEL) IN ('LV1','LV2','LV3','LV4') THEN   -- 202407版本新增LV4层级
         V_VARA1 := '';
         V_SQL_SPART := '';
         V_SQL_COA := '';
         V_SQL_PROFITS := '';
         V_TEXT_PROFITS_NAME := '';  
         V_SQL_DMS_CODE := '';
     END IF;    
     
     V_SQL := '
     INSERT INTO '||V_TO_TABLE||' (
                VERSION_ID,
                PERIOD_YEAR,
                PERIOD_ID,
                BASE_PERIOD_ID,
                PROD_RND_TEAM_CODE,
                PROD_RND_TEAM_CN_NAME,
                '||V_DMS_CODE_TOTAL
                ||V_DMS_NAME
                ||V_SPART_TOTAL
                ||V_COA_TOTAL
                ||V_PROFITS_NAME
                ||V_MADE_TOTAL||'
                GROUP_CODE,
                GROUP_CN_NAME,
                GROUP_LEVEL,
                COST_INDEX,
                PARENT_CODE,
                PARENT_CN_NAME,
                CREATED_BY,
                CREATION_DATE,
                LAST_UPDATED_BY,
                LAST_UPDATE_DATE,
                DEL_FLAG,
                VIEW_FLAG,
                APPEND_FLAG,
                SCENARIO_FLAG,
                CALIBER_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME
     )
     -- 取出入参CODE对应的版本号以及默认基期的数据和其子级的指数数据
     WITH CODE_SUB_TMP AS(
     -- 取出F_GROUP_CODE本身的指数数据            
                    SELECT VERSION_ID,
                           PERIOD_ID,
                           BASE_PERIOD_ID,
                           PROD_RND_TEAM_CODE,
                           PROD_RND_TEAM_CN_NAME,
                           '||V_DMS_CODE_TOTAL
                           ||V_DMS_NAME
                           ||V_SPART_TOTAL
                           ||V_COA_TOTAL
                           ||V_PROFITS_NAME
                           ||V_MADE_TOTAL||'
                           GROUP_CODE,
                           GROUP_CN_NAME,
                           GROUP_LEVEL,
                           COST_INDEX,
                           PARENT_CODE,
                           PARENT_CN_NAME,
                           VIEW_FLAG,
                           APPEND_FLAG,
                           SCENARIO_FLAG,
                           CALIBER_FLAG,
                           OVERSEA_FLAG,
                           LV0_PROD_LIST_CODE,
                           LV0_PROD_LIST_CN_NAME
                       FROM '||V_TO_TABLE||'
                       WHERE VERSION_ID = '''||V_VERSION_ID||''''
                       ||V_SQL_PROFITS||V_TEXT_PROFITS_NAME||V_SQL_DMS_CODE||V_SQL_SPART||V_SQL_COA||'
                       AND BASE_PERIOD_ID = '''||V_DEFAULT_PERIOD||'''
                       AND GROUP_CODE IN ('||V_GROUP_CODE||')
                       AND PROD_RND_TEAM_CODE IN ('||V_PROD_CODE||')
                       AND VIEW_FLAG = '''||V_VIEW_FLAG||'''
                       AND GROUP_LEVEL = '''||F_GROUPLEVEL||'''
                       AND CALIBER_FLAG = '''||F_CALIBER||'''
                       AND OVERSEA_FLAG = '''||F_OVERSEA_FLAG||'''
                       AND LV0_PROD_LIST_CODE = '''||F_LV0_PROD_LIST_CODE||'''
                       AND UPPER(DEL_FLAG) = ''N''
                    UNION ALL
     -- 取出F_GROUP_CODE子级的指数数据                
                    SELECT VERSION_ID,
                           PERIOD_ID,
                           BASE_PERIOD_ID,
                           PROD_RND_TEAM_CODE,
                           PROD_RND_TEAM_CN_NAME,
                           '||V_DMS_CODE_TOTAL
                           ||V_DMS_NAME
                           ||V_SPART_TOTAL
                           ||V_COA_TOTAL
                           ||V_PROFITS_NAME
                           ||V_MADE_TOTAL||'
                           GROUP_CODE,
                           GROUP_CN_NAME,
                           GROUP_LEVEL,
                           COST_INDEX,
                           PARENT_CODE,
                           PARENT_CN_NAME,
                           VIEW_FLAG,
                           APPEND_FLAG,
                           SCENARIO_FLAG,
                           CALIBER_FLAG,
                           OVERSEA_FLAG,
                           LV0_PROD_LIST_CODE,
                           LV0_PROD_LIST_CN_NAME
                       FROM '||V_TO_TABLE||'
                       WHERE VERSION_ID = '''||V_VERSION_ID||''''
                       ||V_SQL_PROFITS||V_TEXT_PROFITS_NAME||V_SQL_DMS_CODE||V_SQL_SPART||V_SQL_COA||'
                       AND BASE_PERIOD_ID = '''||V_DEFAULT_PERIOD||'''
                       AND PARENT_CODE IN ('||V_GROUP_CODE||')     -- 取父级CODE为入参GROUP_CODE的数据
                       AND VIEW_FLAG = '''||V_VIEW_FLAG||'''
                       AND CALIBER_FLAG = '''||F_CALIBER||'''
                       AND GROUP_LEVEL = '''||F_SUBLEVEL||'''     -- 取GROUP_LEVEL为子级层级的数据
                       AND OVERSEA_FLAG = '''||F_OVERSEA_FLAG||'''
                       AND LV0_PROD_LIST_CODE = '''||F_LV0_PROD_LIST_CODE||'''
                       AND UPPER(DEL_FLAG) = ''N'''||
                       V_VARA1 ||'
                )
     -- 基期切换后的计算逻辑            
                  SELECT T1.VERSION_ID,
                         SUBSTR(T1.PERIOD_ID,1,4) AS PERIOD_YEAR,
                         T1.PERIOD_ID,
                         '||V_CHANGE_BASE_PERIOD ||' AS BASE_PERIOD_ID,
                         T1.PROD_RND_TEAM_CODE,
                         T1.PROD_RND_TEAM_CN_NAME,
                         '||V_IN_DMS_CODE
                         ||V_IN_DMS_NAME
                         ||V_IN_SPART_TOTAL
                         ||V_IN_COA_TOTAL
                         ||V_PROFITS_NAME1
                         ||V_IN_MADE_TOTAL||'
                         T1.GROUP_CODE,
                         T1.GROUP_CN_NAME,
                         T1.GROUP_LEVEL,
                         (T1.COST_INDEX / NULLIF(T2.COST_INDEX,0))*100 AS COST_INDEX,
                         T1.PARENT_CODE,
                         T1.PARENT_CN_NAME,
                         -1 AS CREATED_BY,
                         CURRENT_TIMESTAMP AS CREATION_DATE,
                         ''-1'' AS LAST_UPDATED_BY,
                         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                         ''N'' AS DEL_FLAG,
                         T1.VIEW_FLAG,
                         T1.APPEND_FLAG,
                         T1.SCENARIO_FLAG,
                         T1.CALIBER_FLAG,
                         T1.OVERSEA_FLAG,
                         T1.LV0_PROD_LIST_CODE,
                         T1.LV0_PROD_LIST_CN_NAME
                     FROM CODE_SUB_TMP T1
                     LEFT JOIN ( -- 取出切换基期的月份的数据
                                SELECT PROD_RND_TEAM_CODE,
                                       '||V_DMS_CODE_TOTAL
                                       ||V_SPART_TOTAL
                                       ||V_COA_TOTAL
                                       ||V_PROFITS_NAME
                                       ||V_MADE_TOTAL||'
                                       GROUP_CODE,
                                       PARENT_CODE,
                                       GROUP_LEVEL,
                                       COST_INDEX,
                                       VIEW_FLAG,
                                       CALIBER_FLAG,
                                       OVERSEA_FLAG,
                                       LV0_PROD_LIST_CODE
                                    FROM CODE_SUB_TMP
                                    WHERE PERIOD_ID = '''||V_CHANGE_BASE_PERIOD||'''
                                                ) T2        
                     ON T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE
                     '||V_SQL1_PROFITS_NAME
                     ||V_REL1_DMS_CODE
                     ||V_REL1_DIMENSION_CODE
                     ||V_REL1_SPART
                     ||V_REL1_COA
                     ||V_REL1_MADE_CODE||'
                     AND T1.GROUP_CODE = T2.GROUP_CODE
                     AND NVL(T1.PARENT_CODE,''SNULL0'') = NVL(T2.PARENT_CODE,''SNULL0'')
                     AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
                     AND T1.VIEW_FLAG = T2.VIEW_FLAG
                     AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
                     AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
                     AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
       --排除原先指数表里已经存在相同版本的数据情况，而不存在的就插入
              WHERE NOT EXISTS (SELECT 1 FROM '||V_TO_TABLE||' T3 
                                   WHERE T1.PERIOD_ID = T3.PERIOD_ID 
                                   AND (CASE WHEN T1.GROUP_CODE IS NULL THEN '' '' ELSE T1.GROUP_CODE END) =
                                       (CASE WHEN T3.GROUP_CODE IS NULL THEN '' '' ELSE T3.GROUP_CODE END) 
                                   AND (CASE WHEN T1.PROD_RND_TEAM_CODE IS NULL THEN '' '' ELSE T1.PROD_RND_TEAM_CODE END)=
                                       (CASE WHEN T3.PROD_RND_TEAM_CODE IS NULL THEN '' '' ELSE T3.PROD_RND_TEAM_CODE END) 
                                   AND T1.VERSION_ID = T3.VERSION_ID 
                                   AND T1.VIEW_FLAG = T3.VIEW_FLAG
                                   AND T1.CALIBER_FLAG = T3.CALIBER_FLAG
                                   AND T1.OVERSEA_FLAG = T3.OVERSEA_FLAG
                                   AND T1.LV0_PROD_LIST_CODE = T3.LV0_PROD_LIST_CODE
                                   AND T1.GROUP_LEVEL = T3.GROUP_LEVEL
                                   '||V_SQL_PROFITS_NAME
                                   ||V_REL_DMS_CODE
                                   ||V_REL_DIMENSION_CODE
                                   ||V_REL_SPART
                                   ||V_REL_COA
                                   ||V_REL_MADE_CODE||'
                                   AND T3.BASE_PERIOD_ID = '''||V_CHANGE_BASE_PERIOD||''')'
                            ;
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;
                            
     --2.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入基期切换为：'''||V_CHANGE_BASE_PERIOD||'''，版本号为：'''||V_VERSION_ID||'''，入参CODE为：'''||V_GROUP_CODE||'''的指数到'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');                        
                                                                
  ELSIF F_REV_CODE IS NULL AND F_CUSTOM_ID IS NOT NULL THEN    -- F_CUSTOM_ID 不为空时，走自选组合计算逻辑

  -- 目标表重赋值
  IF F_COST_TYPE = 'P' AND F_INDUSTRY_FLAG = 'I' THEN  -- 采购成本-ICT
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_CUSTOM_MONTH_COST_INDEX_T ';
  ELSIF F_COST_TYPE = 'P' AND F_INDUSTRY_FLAG = 'E' THEN  -- 采购成本-数字能源
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_CUSTOM_MONTH_COST_INDEX_T ';
  ELSIF F_COST_TYPE = 'P' AND F_INDUSTRY_FLAG = 'IAS' THEN  -- 采购成本-IAS
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_CUSTOM_MONTH_COST_INDEX_T ';
  ELSIF F_COST_TYPE = 'M' AND F_INDUSTRY_FLAG = 'I' THEN  -- 制造成本-ICT
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_CUSTOM_MONTH_COST_INDEX_T ';
  ELSIF F_COST_TYPE = 'M' AND F_INDUSTRY_FLAG = 'E' THEN  -- 制造成本-数字能源
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_CUSTOM_MONTH_COST_INDEX_T ';
  ELSIF F_COST_TYPE = 'M' AND F_INDUSTRY_FLAG = 'IAS' THEN  -- 制造成本-IAS
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_CUSTOM_MONTH_COST_INDEX_T ';
  END IF;
  -- 删除对应切换基期的数据
     V_SQL := '
      DELETE FROM '||V_TO_TABLE||'
       WHERE BASE_PERIOD_ID = '||V_CHANGE_BASE_PERIOD||'
         AND CUSTOM_ID IN ( SELECT REGEXP_SPLIT_TO_TABLE('''||V_CUSTOM_ID||''','',''))
         AND VIEW_FLAG = '''||V_VIEW_FLAG||'''
         AND LV0_PROD_LIST_CODE = '''||F_LV0_PROD_LIST_CODE||'''
         AND CALIBER_FLAG = '''||F_CALIBER||'''
         AND VERSION_ID = '||V_VERSION_ID||'
         AND GRANULARITY_TYPE = '''||F_GRANULARITY_TYPE||'''
         AND OVERSEA_FLAG = '''||F_OVERSEA_FLAG||'''';
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;
      
    -- 计算切换基期后，本次入参组合ID下所有CODE的指数
    V_SQL := '
      WITH BASE_INDEX AS
       (SELECT CUSTOM_ID,
               CUSTOM_CN_NAME,
               PERIOD_YEAR,
               PERIOD_ID,
               GROUP_CODE,
               GROUP_CN_NAME,
               GROUP_LEVEL,
               COST_INDEX,
               APPEND_FLAG,
               SCENARIO_FLAG,
               GRANULARITY_TYPE,
               VIEW_FLAG,
               CALIBER_FLAG,
               OVERSEA_FLAG,
               LV0_PROD_LIST_CODE,
               LV0_PROD_LIST_CN_NAME,
               PARENT_CODE,
               PARENT_CN_NAME,
               PARENT_LEVEL
          FROM '||V_TO_TABLE||'
         WHERE VIEW_FLAG = '''||V_VIEW_FLAG||'''
           AND LV0_PROD_LIST_CODE = '''||F_LV0_PROD_LIST_CODE||'''
           AND CALIBER_FLAG = '''||F_CALIBER||'''
           AND VERSION_ID = '||V_VERSION_ID||'
           AND GRANULARITY_TYPE = '''||F_GRANULARITY_TYPE||'''
           AND OVERSEA_FLAG = '''||F_OVERSEA_FLAG||'''
           AND BASE_PERIOD_ID = '||V_DEFAULT_PERIOD||'
           AND CUSTOM_ID IN( SELECT REGEXP_SPLIT_TO_TABLE('''||V_CUSTOM_ID||''','','') ) 
                     ) 
      INSERT INTO '||V_TO_TABLE||'
        (CUSTOM_ID,
         CUSTOM_CN_NAME,
         VERSION_ID,
         BASE_PERIOD_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         COST_INDEX,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         APPEND_FLAG,
         SCENARIO_FLAG,
         GRANULARITY_TYPE,
         VIEW_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         PARENT_CODE,
         PARENT_CN_NAME,
         PARENT_LEVEL)
      SELECT TO_NUMBER(T1.CUSTOM_ID) AS  CUSTOM_ID,
             T1.CUSTOM_CN_NAME,
             '||V_VERSION_ID||' AS VERSION_ID,
             '||V_CHANGE_BASE_PERIOD||' AS BASE_PERIOD_ID,
             T1.PERIOD_YEAR,
             T1.PERIOD_ID,
             T1.GROUP_CODE,
             T1.GROUP_CN_NAME,
             T1.GROUP_LEVEL,
             (T1.COST_INDEX / NULLIF(T2.BASE_PERIOD_INDEX, 0)) * 100 AS COST_INDEX,
             -1 AS CREATED_BY,
             CURRENT_TIMESTAMP AS CREATION_DATE,
             -1 AS LAST_UPDATED_BY,
             CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
             ''N'' AS DEL_FLAG,
             T1.APPEND_FLAG,
             T1.SCENARIO_FLAG,
             T1.GRANULARITY_TYPE,
             T1.VIEW_FLAG,
             T1.CALIBER_FLAG,
             T1.OVERSEA_FLAG,
             T1.LV0_PROD_LIST_CODE,
             T1.LV0_PROD_LIST_CN_NAME,
             T1.PARENT_CODE,
             T1.PARENT_CN_NAME,
             T1.PARENT_LEVEL
        FROM BASE_INDEX T1
        LEFT JOIN (SELECT CUSTOM_ID,
                          GROUP_CODE,
                          COST_INDEX AS BASE_PERIOD_INDEX,
                          GROUP_LEVEL,
                          PARENT_CODE,
                          PARENT_LEVEL
                     FROM BASE_INDEX
                    WHERE PERIOD_ID = '||V_CHANGE_BASE_PERIOD||') T2
          ON T1.GROUP_CODE = T2.GROUP_CODE
         AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
          AND T1.CUSTOM_ID = T2.CUSTOM_ID
         AND NVL(T1.PARENT_CODE, 1) = NVL(T2.PARENT_CODE, 1)
         AND NVL(T1.PARENT_LEVEL, 2) = NVL(T2.PARENT_LEVEL, 2)';
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;

  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入基期切换为：'''||V_CHANGE_BASE_PERIOD||'''，版本号为：'''||V_VERSION_ID||'''，组合ID为：'''||V_CUSTOM_ID||'''的指数到'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');  

    -- 3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --4.日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集FIN_DM_OPT_FOI.DM_FOC_MONTH_COST_IDX_T统计信息完成!');
   
  ELSIF F_REV_CODE IS NOT NULL THEN   -- 反向视角CODE不为空
       -- 判断入参CODE的子级是什么层级
     IF UPPER(F_SUBLEVEL) IN ('CATEGORY','MODL','CEG','SHIPPING_OBJECT') THEN
         V_VARA1 := '';
     ELSIF UPPER(F_SUBLEVEL) IN ('LV1','LV2','LV3') AND F_COST_TYPE = 'P' THEN 
         V_VARA1 := ' AND PUR_CODE IN ('||V_REV_CODE||')';
     ELSIF UPPER(F_SUBLEVEL) IN ('LV1','LV2','LV3') AND F_COST_TYPE = 'M' THEN 
         V_VARA1 := ' AND MANUFACTURE_OBJECT_CODE IN ('||V_REV_CODE||')
                      AND SHIPPING_OBJECT_CODE IN ('||V_SHIPPING_CODE||') ';
     ELSIF UPPER(F_SUBLEVEL) IN ('MANUFACTURE_OBJECT') AND F_COST_TYPE = 'M' THEN 
         V_VARA1 := ' AND SHIPPING_OBJECT_CODE IN ('||V_SHIPPING_CODE||') ';
     END IF;    
  -- 目标表重赋值,变量重赋值
  IF F_COST_TYPE = 'P' THEN  -- 采购成本
     V_REV_TOTAL := '
                    PUR_CODE,
                    PUR_CN_NAME,';
     V_IN_REV_TOTAL := '
                    T1.PUR_CODE,
                    T1.PUR_CN_NAME,';
     V_REL_REV_CODE := ' AND T1.PUR_CODE = T3.PUR_CODE ';
     V_REL1_REV_CODE := ' AND T1.PUR_CODE = T2.PUR_CODE ';
    IF F_INDUSTRY_FLAG = 'I' THEN   -- ICT
       V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REV_MONTH_COST_IDX_T ';
    ELSIF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源
       V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_REV_MONTH_COST_IDX_T ';
    ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- IAS
       V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_REV_MONTH_COST_IDX_T ';
    END IF;
  ELSIF F_COST_TYPE = 'M' THEN  -- 制造成本
          V_REV_TOTAL := '
                    MANUFACTURE_OBJECT_CODE,
                    MANUFACTURE_OBJECT_CN_NAME,
                    SHIPPING_OBJECT_CODE,
                    SHIPPING_OBJECT_CN_NAME,';
          V_IN_REV_TOTAL := '
                    T1.MANUFACTURE_OBJECT_CODE,
                    T1.MANUFACTURE_OBJECT_CN_NAME,
                    T1.SHIPPING_OBJECT_CODE,
                    T1.SHIPPING_OBJECT_CN_NAME,';
         V_REL_REV_CODE := ' AND NVL(T1.SHIPPING_OBJECT_CODE,''SNULL0'') = NVL(T3.SHIPPING_OBJECT_CODE,''SNULL0'') 
                             AND NVL(T1.MANUFACTURE_OBJECT_CODE,''SNULL1'') = NVL(T3.MANUFACTURE_OBJECT_CODE,''SNULL1'') ';
         V_REL1_REV_CODE := ' AND NVL(T1.SHIPPING_OBJECT_CODE,''SNULL0'') = NVL(T2.SHIPPING_OBJECT_CODE,''SNULL0'') 
                              AND NVL(T1.MANUFACTURE_OBJECT_CODE,''SNULL1'') = NVL(T2.MANUFACTURE_OBJECT_CODE,''SNULL1'') ';
    IF F_INDUSTRY_FLAG = 'I' THEN   -- ICT
       V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_REV_MONTH_COST_IDX_T ';
    ELSIF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源
       V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_REV_MONTH_COST_IDX_T ';
    ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- IAS
       V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_REV_MONTH_COST_IDX_T ';
    END IF;
  END IF;
                    
     V_SQL := '
     INSERT INTO '||V_TO_TABLE||' (
                VERSION_ID,
                PERIOD_YEAR,
                PERIOD_ID,
                BASE_PERIOD_ID,
                '||V_REV_TOTAL||'
                GROUP_CODE,
                GROUP_CN_NAME,
                GROUP_LEVEL,
                COST_INDEX,
                PARENT_CODE,
                PARENT_CN_NAME,
                CREATED_BY,
                CREATION_DATE,
                LAST_UPDATED_BY,
                LAST_UPDATE_DATE,
                DEL_FLAG,
                VIEW_FLAG,
                CALIBER_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME
     )
     -- 取出入参CODE对应的版本号以及默认基期的数据和其子级的指数数据
     WITH CODE_SUB_TMP AS(
     -- 取出F_GROUP_CODE本身的指数数据            
                    SELECT VERSION_ID,
                           PERIOD_ID,
                           BASE_PERIOD_ID,
                           '||V_REV_TOTAL||'
                           GROUP_CODE,
                           GROUP_CN_NAME,
                           GROUP_LEVEL,
                           COST_INDEX,
                           PARENT_CODE,
                           PARENT_CN_NAME,
                           VIEW_FLAG,
                           CALIBER_FLAG,
                           OVERSEA_FLAG,
                           LV0_PROD_LIST_CODE,
                           LV0_PROD_LIST_CN_NAME
                       FROM '||V_TO_TABLE||'
                       WHERE VERSION_ID = '''||V_VERSION_ID||'''
                       AND BASE_PERIOD_ID = '''||V_DEFAULT_PERIOD||'''
                       AND GROUP_CODE IN ('||V_GROUP_CODE||')
                       '||V_VARA1||'
                       AND VIEW_FLAG = '''||V_VIEW_FLAG||'''
                       AND GROUP_LEVEL = '''||F_GROUPLEVEL||'''
                       AND CALIBER_FLAG = '''||F_CALIBER||'''
                       AND OVERSEA_FLAG = '''||F_OVERSEA_FLAG||'''
                       AND LV0_PROD_LIST_CODE = '''||F_LV0_PROD_LIST_CODE||'''
                       AND UPPER(DEL_FLAG) = ''N''
                    UNION ALL
     -- 取出F_GROUP_CODE子级的指数数据                
                    SELECT VERSION_ID,
                           PERIOD_ID,
                           BASE_PERIOD_ID,
                           '||V_REV_TOTAL||'
                           GROUP_CODE,
                           GROUP_CN_NAME,
                           GROUP_LEVEL,
                           COST_INDEX,
                           PARENT_CODE,
                           PARENT_CN_NAME,
                           VIEW_FLAG,
                           CALIBER_FLAG,
                           OVERSEA_FLAG,
                           LV0_PROD_LIST_CODE,
                           LV0_PROD_LIST_CN_NAME
                       FROM '||V_TO_TABLE||'
                       WHERE VERSION_ID = '''||V_VERSION_ID||'''
                       AND BASE_PERIOD_ID = '''||V_DEFAULT_PERIOD||'''
                       AND PARENT_CODE IN ('||V_GROUP_CODE||')     -- 取父级CODE为入参GROUP_CODE的数据
                       AND VIEW_FLAG = '''||V_VIEW_FLAG||'''
                       AND CALIBER_FLAG = '''||F_CALIBER||'''
                       AND GROUP_LEVEL = '''||F_SUBLEVEL||'''     -- 取GROUP_LEVEL为子级层级的数据
                       AND OVERSEA_FLAG = '''||F_OVERSEA_FLAG||'''
                       AND LV0_PROD_LIST_CODE = '''||F_LV0_PROD_LIST_CODE||'''
                       AND UPPER(DEL_FLAG) = ''N'''||
                       V_VARA1 ||'
                )
     -- 基期切换后的计算逻辑            
                  SELECT T1.VERSION_ID,
                         SUBSTR(T1.PERIOD_ID,1,4) AS PERIOD_YEAR,
                         T1.PERIOD_ID,
                         '||V_CHANGE_BASE_PERIOD ||' AS BASE_PERIOD_ID,
                         '||V_IN_REV_TOTAL||'
                         T1.GROUP_CODE,
                         T1.GROUP_CN_NAME,
                         T1.GROUP_LEVEL,
                         (T1.COST_INDEX / NULLIF(T2.COST_INDEX,0))*100 AS COST_INDEX,
                         T1.PARENT_CODE,
                         T1.PARENT_CN_NAME,
                         -1 AS CREATED_BY,
                         CURRENT_TIMESTAMP AS CREATION_DATE,
                         ''-1'' AS LAST_UPDATED_BY,
                         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                         ''N'' AS DEL_FLAG,
                         T1.VIEW_FLAG,
                         T1.CALIBER_FLAG,
                         T1.OVERSEA_FLAG,
                         T1.LV0_PROD_LIST_CODE,
                         T1.LV0_PROD_LIST_CN_NAME
                     FROM CODE_SUB_TMP T1
                     LEFT JOIN ( -- 取出切换基期的月份的数据
                                SELECT '||V_REV_TOTAL||'
                                       GROUP_CODE,
                                       PARENT_CODE,
                                       GROUP_LEVEL,
                                       COST_INDEX,
                                       VIEW_FLAG,
                                       CALIBER_FLAG,
                                       OVERSEA_FLAG,
                                       LV0_PROD_LIST_CODE
                                    FROM CODE_SUB_TMP
                                    WHERE PERIOD_ID = '''||V_CHANGE_BASE_PERIOD||'''
                                                ) T2        
                     ON T1.GROUP_CODE = T2.GROUP_CODE
                     '||V_REL1_REV_CODE||' 
                     AND NVL(T1.PARENT_CODE,''SNULL0'') = NVL(T2.PARENT_CODE,''SNULL0'')
                     AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
                     AND T1.VIEW_FLAG = T2.VIEW_FLAG
                     AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
                     AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
                     AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
       --排除原先指数表里已经存在相同版本的数据情况，而不存在的就插入
              WHERE NOT EXISTS (SELECT 1 FROM '||V_TO_TABLE||' T3 
                                   WHERE T1.PERIOD_ID = T3.PERIOD_ID 
                                   AND (CASE WHEN T1.GROUP_CODE IS NULL THEN '' '' ELSE T1.GROUP_CODE END) =
                                       (CASE WHEN T3.GROUP_CODE IS NULL THEN '' '' ELSE T3.GROUP_CODE END) 
                                   '||V_REL_REV_CODE||' 
                                   AND T1.VERSION_ID = T3.VERSION_ID 
                                   AND T1.VIEW_FLAG = T3.VIEW_FLAG
                                   AND T1.CALIBER_FLAG = T3.CALIBER_FLAG
                                   AND T1.OVERSEA_FLAG = T3.OVERSEA_FLAG
                                   AND T1.LV0_PROD_LIST_CODE = T3.LV0_PROD_LIST_CODE
                                   AND T1.GROUP_LEVEL = T3.GROUP_LEVEL
                                   AND T3.BASE_PERIOD_ID = '''||V_CHANGE_BASE_PERIOD||''')'
                            ;
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;
                            
     --2.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入基期切换为：'''||V_CHANGE_BASE_PERIOD||'''，版本号为：'''||V_VERSION_ID||'''，入参CODE为：'''||V_GROUP_CODE||'''的反向视角的指数到'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');   
   
   END IF;
   END IF;
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

