-- Name: f_dm_fcst_price_mid_mon_spart_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_mid_mon_spart_t(f_period_year bigint DEFAULT NULL::bigint, f_version_id bigint DEFAULT NULL::bigint, f_year_diff integer DEFAULT NULL::integer, OUT x_result_status text)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
创建时间：20241106
创建人  ：qwx1110218
背景描述：定价指数ICT-定价指数-MID_MON_SPART表，只保留1个版本，取数逻辑:
          将做完ETL处理后的源表数据，做如下处理：
          ① 先按合同号层级，分年对每个月的数据进行月累计，月累计之后的值，需要将累计结果值，量为0、为空、为负数或者美元金额为空、为负数的值作无效化处理（即满足其中1种都属于无效数据），不参与后续所有逻辑计算。（额为0需要保留）
          只要满足量的条件或者额的条件之一，整条数据作无效化处理；
          并对缺失月份的合同号数据进行补齐（不跨年）；
          ② 将累计完的每个月的合同号层级的数据，按月卷积到SPART层级；
          ③ 路径1：国内海外、地区部、代表处等3个字段值需要造一版全球/全选的数据；
          路径2：子网系统部需要造一版全选数据
参数描述：参数一： f_period_year 年份
          参数二： f_version_id 版本号
          参数五： x_result_status 运行状态返回值 'SUCCESS'为成功，'FAILED'为失败
事例：SELECT FIN_DM_OPT_FOI.f_dm_fcst_price_mid_mon_spart_t()
*/

DECLARE
  V_SP_NAME    VARCHAR2(100) := 'FIN_DM_OPT_FOI.F_DM_FCST_PRICE_MID_MON_SPART_T'; --存储过程名称
  V_STEP_MUM   BIGINT; --步骤号
  V_VERSION_ID BIGINT; --新的版本号
  V_SOURCE_VERSION_ID BIGINT; --目标表的版本号
  V_PERIOD_YEAR BIGINT; -- 年份
  V_BEGIN_DATE TIMESTAMP ;  -- 取4年的数据
  V_SQL  TEXT; -- SQL逻辑
  V_FROM_TABLE     VARCHAR(100);
  V_TO_TABLE       VARCHAR(100);
  V_TEMP_TABLE     VARCHAR(100);
  V_YEAR           INT;

BEGIN
  X_RESULT_STATUS = 'SUCCESS';

  --1.开始日志
  V_STEP_MUM := 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
     F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_MUM,
     F_CAL_LOG_DESC => V_SP_NAME||'开始执行'
  );

  V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_PRICE_MID_MON_SPART_01_T';
  V_TO_TABLE   := 'FIN_DM_OPT_FOI.DM_FCST_PRICE_MID_MON_SPART_T';
  V_TEMP_TABLE := 'ACTUAL_APD_TEMP';

    if MONTH(CURRENT_TIMESTAMP) = 1
  then V_YEAR = YEAR(NOW()) -1;
  ELSE
  V_YEAR = YEAR(NOW());
  END IF ;
  
  V_BEGIN_DATE := TO_DATE(TO_CHAR(V_YEAR-3)||'01','YYYYMM');


  -- 从版本表取最新版本
  IF(F_VERSION_ID IS NULL) THEN
    SELECT VERSION_ID INTO V_VERSION_ID
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'ANNUAL'  -- 用年度版本号
     ORDER BY LAST_UPDATE_DATE DESC
     LIMIT 1
    ;

  ELSE
    V_VERSION_ID := F_VERSION_ID;
  END IF;

  raise notice'V_VERSION_ID：%',V_VERSION_ID;
  
  -- 创建临时表
  DROP TABLE IF EXISTS ALL_ACTUAL_ITEM_TEMP1;
  CREATE TEMPORARY TABLE ALL_ACTUAL_ITEM_TEMP1(
			   PERIOD_YEAR                           BIGINT           -- 会计年
       , PERIOD_ID                             BIGINT           -- 会计月
       , BG_CODE                               VARCHAR(50)      -- BG编码
       , BG_CN_NAME                            VARCHAR(200)     -- BG中文名称
       , LV0_PROD_LIST_CODE                    VARCHAR(50)      -- LV0重量级团队编码
       , LV1_PROD_LIST_CODE                    VARCHAR(50)      -- LV1重量级团队编码
       , LV2_PROD_LIST_CODE                    VARCHAR(50)      -- LV2重量级团队编码
       , LV3_PROD_LIST_CODE                    VARCHAR(50)      -- LV3重量级团队编码
       , LV4_PROD_LIST_CODE                    VARCHAR(50)      -- LV3.5重量级团队编码
       , LV0_PROD_LIST_CN_NAME                 VARCHAR(200)     -- LV0重量级团队中文名称
       , LV1_PROD_LIST_CN_NAME                 VARCHAR(200)     -- LV1重量级团队中文名称
       , LV2_PROD_LIST_CN_NAME                 VARCHAR(200)     -- LV2重量级团队中文名称
       , LV3_PROD_LIST_CN_NAME                 VARCHAR(200)     -- LV3重量级团队中文名称
       , LV4_PROD_LIST_CN_NAME                 VARCHAR(200)     -- LV3.5重量级团队中文名称
       , OVERSEA_FLAG                          VARCHAR(10)      -- 国内海外标识
       , REGION_CODE                           VARCHAR(50)      -- 地区部编码
       , REGION_CN_NAME                        VARCHAR(200)     -- 地区部名称
       , REPOFFICE_CODE                        VARCHAR(50)      -- 代表处编码
       , REPOFFICE_CN_NAME                     VARCHAR(200)     -- 代表处名称
       , SIGN_TOP_CUST_CATEGORY_CODE           VARCHAR(50)      -- 签约客户_大T系统部编码
       , SIGN_TOP_CUST_CATEGORY_CN_NAME        VARCHAR(200)     -- 签约客户_大T系统部名称
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME      VARCHAR(200)     -- 签约客户_子网系统部名称
       , SPART_CODE                            VARCHAR(50)      -- SPART编码
       , SPART_CN_NAME                         VARCHAR(2000)    -- SPART中文名称
       , ENABLE_FLAG                           VARCHAR(2)       -- 有效标识（Y：有效数据、N：无效数据）
       , APPEND_FLAG                           VARCHAR(2)       -- 补齐标识（Y：补齐数据、N：真实数据）
       , PNP_QTY                               NUMERIC          -- PNP数量
       , USD_PNP_AMT                           NUMERIC          -- PNP(CNP)_美元
       , RMB_PNP_AMT                           NUMERIC          -- PNP(CNP)_人民币
       , APPEND_FLAG_CNT                       NUMERIC
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,SPART_CODE)
  ;

	RAISE NOTICE'11111111111111';

  -- 年份
  IF(F_PERIOD_YEAR IS NULL and F_YEAR_DIFF is null ) THEN

    V_PERIOD_YEAR := V_YEAR - 3; -- 取4年的数据（包括当年）

    -- 1.清空目标表数据:
    EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_TO_TABLE;

    --1.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
     F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_MUM,
     F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据',
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS'
    );

    -- 从来源表取数入到临时表
    V_SQL := '
    INSERT INTO ALL_ACTUAL_ITEM_TEMP1(
  			   PERIOD_YEAR
  			 , PERIOD_ID
  			 , BG_CODE
  			 , BG_CN_NAME
         , LV0_PROD_LIST_CODE
         , LV1_PROD_LIST_CODE
         , LV2_PROD_LIST_CODE
         , LV3_PROD_LIST_CODE
         , LV4_PROD_LIST_CODE
         , LV0_PROD_LIST_CN_NAME
         , LV1_PROD_LIST_CN_NAME
         , LV2_PROD_LIST_CN_NAME
         , LV3_PROD_LIST_CN_NAME
         , LV4_PROD_LIST_CN_NAME
         , OVERSEA_FLAG
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , SIGN_TOP_CUST_CATEGORY_CODE
         , SIGN_TOP_CUST_CATEGORY_CN_NAME
         , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
         , SPART_CODE
         , SPART_CN_NAME
         , ENABLE_FLAG
         , APPEND_FLAG
         , PNP_QTY
         , USD_PNP_AMT
         , RMB_PNP_AMT
         , APPEND_FLAG_CNT
    )
    SELECT PERIOD_YEAR
  			 , PERIOD_ID
  			 , BG_CODE
  			 , BG_CN_NAME
         , LV0_PROD_LIST_CODE
         , LV1_PROD_LIST_CODE
         , LV2_PROD_LIST_CODE
         , LV3_PROD_LIST_CODE
         , LV4_PROD_LIST_CODE
         , LV0_PROD_LIST_CN_NAME
         , LV1_PROD_LIST_CN_NAME
         , LV2_PROD_LIST_CN_NAME
         , LV3_PROD_LIST_CN_NAME
         , LV4_PROD_LIST_CN_NAME
         , OVERSEA_FLAG
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , SIGN_TOP_CUST_CATEGORY_CODE
         , SIGN_TOP_CUST_CATEGORY_CN_NAME
         , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
         , SPART_CODE
         , SPART_CN_NAME
         , ENABLE_FLAG
         , APPEND_FLAG
         , SUM(NVL(PNP_QTY,0)) AS PNP_QTY
         , SUM(NVL(USD_PNP_AMT,0)) AS USD_PNP_AMT
         , SUM(NVL(RMB_PNP_AMT,0)) AS RMB_PNP_AMT
         , SUM(CASE WHEN APPEND_FLAG = ''N'' THEN 1 ELSE 0 END ) AS APPEND_FLAG_CNT
      FROM '||V_FROM_TABLE||'
     WHERE PERIOD_YEAR >= '||V_PERIOD_YEAR||'
       AND VERSION_ID = '||V_VERSION_ID||'
       AND DEL_FLAG = ''N''
     GROUP BY PERIOD_YEAR
  			 , PERIOD_ID
  			 , BG_CODE
  			 , BG_CN_NAME
         , LV0_PROD_LIST_CODE
         , LV1_PROD_LIST_CODE
         , LV2_PROD_LIST_CODE
         , LV3_PROD_LIST_CODE
         , LV4_PROD_LIST_CODE
         , LV0_PROD_LIST_CN_NAME
         , LV1_PROD_LIST_CN_NAME
         , LV2_PROD_LIST_CN_NAME
         , LV3_PROD_LIST_CN_NAME
         , LV4_PROD_LIST_CN_NAME
         , OVERSEA_FLAG
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , SIGN_TOP_CUST_CATEGORY_CODE
         , SIGN_TOP_CUST_CATEGORY_CN_NAME
         , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
         , SPART_CODE
         , SPART_CN_NAME
         , ENABLE_FLAG
         , APPEND_FLAG
    ';

    RAISE NOTICE'44444444444';

    DBMS_OUTPUT.PUT_LINE(V_SQL);

    EXECUTE IMMEDIATE V_SQL;

    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
       F_SP_NAME => V_SP_NAME,
       F_STEP_NUM =>  V_STEP_MUM,
       F_CAL_LOG_DESC => '从来源表取数入到临时表',
       F_FORMULA_SQL_TXT => V_SQL,
       F_DML_ROW_COUNT => SQL%ROWCOUNT,
       F_RESULT_STATUS => X_RESULT_STATUS,
       F_ERRBUF => 'SUCCESS'
    );

  -- 根据传入的年份取值
  ELSE

    V_PERIOD_YEAR := V_YEAR - F_YEAR_DIFF ;

    -- 按年删数
    EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' AND PERIOD_YEAR = '||V_PERIOD_YEAR;  -- 按年清理目标表新版本的数据
    
    --1.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
     F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_MUM,
     F_CAL_LOG_DESC => '清理'||V_TO_TABLE||'数据',
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS'
    );

    -- 从来源表取数入到临时表
    V_SQL := '
    INSERT INTO ALL_ACTUAL_ITEM_TEMP1(
  			   PERIOD_YEAR
  			 , PERIOD_ID
  			 , BG_CODE
  			 , BG_CN_NAME
         , LV0_PROD_LIST_CODE
         , LV1_PROD_LIST_CODE
         , LV2_PROD_LIST_CODE
         , LV3_PROD_LIST_CODE
         , LV4_PROD_LIST_CODE
         , LV0_PROD_LIST_CN_NAME
         , LV1_PROD_LIST_CN_NAME
         , LV2_PROD_LIST_CN_NAME
         , LV3_PROD_LIST_CN_NAME
         , LV4_PROD_LIST_CN_NAME
         , OVERSEA_FLAG
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , SIGN_TOP_CUST_CATEGORY_CODE
         , SIGN_TOP_CUST_CATEGORY_CN_NAME
         , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
         , SPART_CODE
         , SPART_CN_NAME
         , ENABLE_FLAG
         , APPEND_FLAG
         , PNP_QTY
         , USD_PNP_AMT
         , RMB_PNP_AMT
         , APPEND_FLAG_CNT
    )
    SELECT PERIOD_YEAR
  			 , PERIOD_ID
  			 , BG_CODE
  			 , BG_CN_NAME
         , LV0_PROD_LIST_CODE
         , LV1_PROD_LIST_CODE
         , LV2_PROD_LIST_CODE
         , LV3_PROD_LIST_CODE
         , LV4_PROD_LIST_CODE
         , LV0_PROD_LIST_CN_NAME
         , LV1_PROD_LIST_CN_NAME
         , LV2_PROD_LIST_CN_NAME
         , LV3_PROD_LIST_CN_NAME
         , LV4_PROD_LIST_CN_NAME
         , OVERSEA_FLAG
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , SIGN_TOP_CUST_CATEGORY_CODE
         , SIGN_TOP_CUST_CATEGORY_CN_NAME
         , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
         , SPART_CODE
         , SPART_CN_NAME
         , ENABLE_FLAG
         , APPEND_FLAG
         , SUM(NVL(PNP_QTY,0)) AS PNP_QTY
         , SUM(NVL(USD_PNP_AMT,0)) AS USD_PNP_AMT
         , SUM(NVL(RMB_PNP_AMT,0)) AS RMB_PNP_AMT
         , SUM(CASE WHEN APPEND_FLAG = ''N'' THEN 1 ELSE 0 END ) AS APPEND_FLAG_CNT
      FROM '||V_FROM_TABLE||'
     WHERE PERIOD_YEAR = '||V_PERIOD_YEAR||'
       AND VERSION_ID = '||V_VERSION_ID||'
       AND DEL_FLAG = ''N''
     GROUP BY PERIOD_YEAR
  			 , PERIOD_ID
  			 , BG_CODE
  			 , BG_CN_NAME
         , LV0_PROD_LIST_CODE
         , LV1_PROD_LIST_CODE
         , LV2_PROD_LIST_CODE
         , LV3_PROD_LIST_CODE
         , LV4_PROD_LIST_CODE
         , LV0_PROD_LIST_CN_NAME
         , LV1_PROD_LIST_CN_NAME
         , LV2_PROD_LIST_CN_NAME
         , LV3_PROD_LIST_CN_NAME
         , LV4_PROD_LIST_CN_NAME
         , OVERSEA_FLAG
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , SIGN_TOP_CUST_CATEGORY_CODE
         , SIGN_TOP_CUST_CATEGORY_CN_NAME
         , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
         , SPART_CODE
         , SPART_CN_NAME
         , ENABLE_FLAG
         , APPEND_FLAG
    ';

    RAISE NOTICE'444444444444';

    DBMS_OUTPUT.PUT_LINE(V_SQL);

    EXECUTE IMMEDIATE V_SQL;

    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
       F_SP_NAME => V_SP_NAME,
       F_STEP_NUM =>  V_STEP_MUM,
       F_CAL_LOG_DESC => '从来源表取数入到临时表',
       F_FORMULA_SQL_TXT => V_SQL,
       F_DML_ROW_COUNT => SQL%ROWCOUNT,
       F_RESULT_STATUS => X_RESULT_STATUS,
       F_ERRBUF => 'SUCCESS'
    );

  END IF;

  -- 创建临时表（国内、海外、全球、地区部（包括全部）、代表处（包括全部））
  DROP TABLE IF EXISTS SPART_NO_DT_INTO_ALL_TMP;
  CREATE TEMPORARY TABLE SPART_NO_DT_INTO_ALL_TMP(
         PERIOD_YEAR                      BIGINT          -- 会计年
       , PERIOD_ID                        BIGINT          -- 会计月
       , BG_CODE                          VARCHAR(50)     -- BG编码
  		 , BG_CN_NAME                       VARCHAR(200)    -- BG中文名称
       , LV0_PROD_LIST_CODE               VARCHAR(50)     -- LV0重量级团队编码
       , LV1_PROD_LIST_CODE               VARCHAR(50)     -- LV1重量级团队编码
       , LV2_PROD_LIST_CODE               VARCHAR(50)     -- LV2重量级团队编码
       , LV3_PROD_LIST_CODE               VARCHAR(50)     -- LV3重量级团队编码
       , LV4_PROD_LIST_CODE               VARCHAR(50)     -- LV3.5重量级团队编码
       , LV0_PROD_LIST_CN_NAME            VARCHAR(200)    -- LV0重量级团队中文名称
       , LV1_PROD_LIST_CN_NAME            VARCHAR(200)    -- LV1重量级团队中文名称
       , LV2_PROD_LIST_CN_NAME            VARCHAR(200)    -- LV2重量级团队中文名称
       , LV3_PROD_LIST_CN_NAME            VARCHAR(200)    -- LV3重量级团队中文名称
       , LV4_PROD_LIST_CN_NAME            VARCHAR(200)    -- LV3.5重量级团队中文名称
       , OVERSEA_FLAG                     VARCHAR(10)     -- 国内海外标识
       , REGION_CODE                      VARCHAR(50)     -- 地区部编码
       , REGION_CN_NAME                   VARCHAR(200)    -- 地区部名称
       , REPOFFICE_CODE                   VARCHAR(50)     -- 代表处编码
       , REPOFFICE_CN_NAME                VARCHAR(200)    -- 代表处名称
       , SPART_CODE                       VARCHAR(50)     -- SPART编码
       , SPART_CN_NAME                    VARCHAR(2000)   -- SPART中文名称
       , APPEND_FLAG                      VARCHAR(2)      -- 补齐标识（Y：补齐数据、N：真实数据）
       , PNP_QTY                          NUMERIC         -- PNP数量
       , USD_PNP_AMT                      NUMERIC         -- PNP(CNP)_美元
       , RMB_PNP_AMT                      NUMERIC         -- PNP(CNP)_人民币
       , APPEND_FLAG_CNT                  NUMERIC
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,SPART_CODE)
  ;

  -- 国内、海外、全球、地区部（包括全部）、代表处（包括全部）的数据入到临时表
  V_SQL := '
  INSERT INTO SPART_NO_DT_INTO_ALL_TMP(
         PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , APPEND_FLAG
       , PNP_QTY
       , USD_PNP_AMT
       , RMB_PNP_AMT
       , APPEND_FLAG_CNT
  )
  -- 计算国内海外、全球的数据
  WITH SPART_NO_DT_INTO_TMP1 AS(
  SELECT PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , SUM(PNP_QTY) AS PNP_QTY    
       , SUM(USD_PNP_AMT) AS USD_PNP_AMT
       , SUM(RMB_PNP_AMT) AS RMB_PNP_AMT
       , SUM(CASE WHEN APPEND_FLAG = ''N'' THEN 1 ELSE 0 END) AS APPEND_FLAG_CNT
    FROM ALL_ACTUAL_ITEM_TEMP1
   GROUP BY PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
  ),
  SPART_NO_DT_INTO_TMP2 AS(
  SELECT PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , (CASE WHEN APPEND_FLAG_CNT >0 THEN ''N'' ELSE ''Y'' END) AS APPEND_FLAG
       , PNP_QTY
       , USD_PNP_AMT
       , RMB_PNP_AMT
       , APPEND_FLAG_CNT
    FROM SPART_NO_DT_INTO_TMP1
   UNION ALL
  SELECT PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , ''G'' AS OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , '''' AS APPEND_FLAG
       , SUM(PNP_QTY)      AS PNP_QTY
       , SUM(USD_PNP_AMT)  AS USD_PNP_AMT
       , SUM(RMB_PNP_AMT)  AS RMB_PNP_AMT
       , SUM(APPEND_FLAG_CNT) AS APPEND_FLAG_CNT
    FROM SPART_NO_DT_INTO_TMP1
   GROUP BY PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
  ),
  -- 在国内、海外、全球的基础上计算地区部、全部的数据
  SPART_NO_DT_INTO_TMP3 AS(
  SELECT PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , (CASE WHEN OVERSEA_FLAG = ''G'' AND APPEND_FLAG_CNT > 0 THEN ''N''
               WHEN OVERSEA_FLAG = ''G'' AND APPEND_FLAG_CNT = 0 THEN ''Y'' 
               ELSE APPEND_FLAG 
          END) AS APPEND_FLAG
       , PNP_QTY
       , USD_PNP_AMT
       , RMB_PNP_AMT
       , APPEND_FLAG_CNT
    FROM SPART_NO_DT_INTO_TMP2
   UNION ALL
  SELECT PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , ''ALL''  AS REGION_CODE
       , ''全选'' AS REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , '''' AS APPEND_FLAG
       , SUM(PNP_QTY)      AS PNP_QTY
       , SUM(USD_PNP_AMT)  AS USD_PNP_AMT
       , SUM(RMB_PNP_AMT)  AS RMB_PNP_AMT
       , SUM(APPEND_FLAG_CNT) AS APPEND_FLAG_CNT
    FROM SPART_NO_DT_INTO_TMP2
   GROUP BY PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
  )
  -- 在国内、海外、全球、地区部（包括全部）的基础上计算代表处、全部的数据
  SELECT PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , (CASE WHEN REGION_CODE = ''ALL'' AND APPEND_FLAG_CNT > 0 THEN ''N''
               WHEN REGION_CODE = ''ALL'' AND APPEND_FLAG_CNT = 0 THEN ''Y'' 
               ELSE APPEND_FLAG 
          END) AS APPEND_FLAG
       , PNP_QTY
       , USD_PNP_AMT
       , RMB_PNP_AMT
       , APPEND_FLAG_CNT
    FROM SPART_NO_DT_INTO_TMP3
   UNION ALL
  SELECT PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , ''ALL''  AS REPOFFICE_CODE
       , ''全选'' AS REPOFFICE_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , '''' AS APPEND_FLAG
       , SUM(PNP_QTY)      AS PNP_QTY
       , SUM(USD_PNP_AMT)  AS USD_PNP_AMT
       , SUM(RMB_PNP_AMT)  AS RMB_PNP_AMT
       , SUM(APPEND_FLAG_CNT) AS APPEND_FLAG_CNT
    FROM SPART_NO_DT_INTO_TMP3
   GROUP BY PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
  ';

  RAISE NOTICE'5555555555';

  DBMS_OUTPUT.PUT_LINE(V_SQL);

  EXECUTE IMMEDIATE V_SQL;

  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
     F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_MUM,
     F_CAL_LOG_DESC => '国内、海外、全球、地区部（包括全部）、代表处（包括全部）的数据入到临时表',
     F_FORMULA_SQL_TXT => V_SQL,
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS'
  );

  -- 创建临时表（大T系统部（包括全部）、子网系统部（包括全部））
  DROP TABLE IF EXISTS SPART_DT_INTO_ALL_TMP;
  CREATE TEMPORARY TABLE SPART_DT_INTO_ALL_TMP(
         PERIOD_YEAR                      BIGINT        -- 会计年
       , PERIOD_ID                        BIGINT        -- 会计月
       , BG_CODE                          VARCHAR(50)
  		 , BG_CN_NAME                       VARCHAR(200)
       , LV0_PROD_LIST_CODE               VARCHAR(50)   -- LV0重量级团队编码
       , LV1_PROD_LIST_CODE               VARCHAR(50)   -- LV1重量级团队编码
       , LV2_PROD_LIST_CODE               VARCHAR(50)   -- LV2重量级团队编码
       , LV3_PROD_LIST_CODE               VARCHAR(50)   -- LV3重量级团队编码
       , LV4_PROD_LIST_CODE               VARCHAR(50)   -- LV3.5重量级团队编码
       , LV0_PROD_LIST_CN_NAME            VARCHAR(200)  -- LV0重量级团队中文名称
       , LV1_PROD_LIST_CN_NAME            VARCHAR(200)  -- LV1重量级团队中文名称
       , LV2_PROD_LIST_CN_NAME            VARCHAR(200)  -- LV2重量级团队中文名称
       , LV3_PROD_LIST_CN_NAME            VARCHAR(200)  -- LV3重量级团队中文名称
       , LV4_PROD_LIST_CN_NAME            VARCHAR(200)  -- LV3.5重量级团队中文名称
       , SIGN_TOP_CUST_CATEGORY_CODE      VARCHAR(50)   -- 签约客户_大T系统部编码
       , SIGN_TOP_CUST_CATEGORY_CN_NAME   VARCHAR(200)  -- 签约客户_大T系统部
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME VARCHAR(200)  -- 签约客户_子网系统部
       , SPART_CODE                       VARCHAR(50)   -- SPART编码
       , SPART_CN_NAME                    VARCHAR(2000) -- SPART中文名称
       , APPEND_FLAG                      VARCHAR(2)    -- 补齐标识（Y：补齐数据、N：真实数据）
       , PNP_QTY                          NUMERIC       -- PNP数量
       , USD_PNP_AMT                      NUMERIC       -- PNP(CNP)_美元
       , RMB_PNP_AMT                      NUMERIC       -- PNP(CNP)_人民币
       , APPEND_FLAG_CNT                  NUMERIC
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,SPART_CODE)
  ;


  -- 大T系统部（包括全部）、子网系统部（包括全部）的数据入到临时表
  V_SQL := '
  INSERT INTO SPART_DT_INTO_ALL_TMP(
         PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , APPEND_FLAG
       , PNP_QTY
       , USD_PNP_AMT
       , RMB_PNP_AMT
       , APPEND_FLAG_CNT
  )
  -- 计算大T系统部、全部的数据
  WITH SPART_DT_INTO_TMP1 AS(
  SELECT PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , '''' AS APPEND_FLAG
       , SUM(PNP_QTY) AS PNP_QTY    
       , SUM(USD_PNP_AMT) AS USD_PNP_AMT
       , SUM(RMB_PNP_AMT) AS RMB_PNP_AMT
       , SUM(CASE WHEN APPEND_FLAG = ''N'' THEN 1 ELSE 0 END) AS APPEND_FLAG_CNT
    FROM ALL_ACTUAL_ITEM_TEMP1
   GROUP BY PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
  ),
  SPART_DT_INTO_TMP2 AS(
  SELECT PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , (CASE WHEN APPEND_FLAG_CNT >0 THEN ''N'' ELSE ''Y'' END) AS APPEND_FLAG
       , PNP_QTY
       , USD_PNP_AMT
       , RMB_PNP_AMT
       , APPEND_FLAG_CNT
    FROM SPART_DT_INTO_TMP1
   UNION ALL
  SELECT PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , ''ALL''  AS SIGN_TOP_CUST_CATEGORY_CODE
       , ''全选'' AS SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , '''' AS APPEND_FLAG
       , SUM(PNP_QTY)     AS PNP_QTY
       , SUM(USD_PNP_AMT) AS USD_PNP_AMT
       , SUM(RMB_PNP_AMT) AS RMB_PNP_AMT
       , SUM(APPEND_FLAG_CNT) AS APPEND_FLAG_CNT
    FROM SPART_DT_INTO_TMP1
   GROUP BY PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
  )
  -- 在大T系统部（包括全部）的基础上计算子网系统部、全部的数据
  SELECT PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , (CASE WHEN SIGN_TOP_CUST_CATEGORY_CODE = ''ALL'' AND APPEND_FLAG_CNT > 0 THEN ''N''
               WHEN SIGN_TOP_CUST_CATEGORY_CODE = ''ALL'' AND APPEND_FLAG_CNT = 0 THEN ''Y'' 
               ELSE APPEND_FLAG 
          END) AS APPEND_FLAG
       , PNP_QTY
       , USD_PNP_AMT
       , RMB_PNP_AMT
       , APPEND_FLAG_CNT
    FROM SPART_DT_INTO_TMP2
   UNION ALL
  SELECT PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , ''全选'' AS SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , '''' AS APPEND_FLAG
       , SUM(PNP_QTY)     AS PNP_QTY
       , SUM(USD_PNP_AMT) AS USD_PNP_AMT
       , SUM(RMB_PNP_AMT) AS RMB_PNP_AMT
       , SUM(APPEND_FLAG_CNT) AS APPEND_FLAG_CNT
    FROM SPART_DT_INTO_TMP2
   GROUP BY PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
  ';

  RAISE NOTICE'666666666';

  DBMS_OUTPUT.PUT_LINE(V_SQL);

  EXECUTE IMMEDIATE V_SQL;

  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
     F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_MUM,
     F_CAL_LOG_DESC => '大T系统部（包括全部）、子网系统部（包括全部）的数据入到临时表',
     F_FORMULA_SQL_TXT => V_SQL,
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS'
  );

  -- 从来源表取数入到临时表
  DROP TABLE IF EXISTS ALL_ACTUAL_ITEM_TEMP;
  CREATE TEMPORARY TABLE ALL_ACTUAL_ITEM_TEMP(
			   PERIOD_YEAR NUMERIC
			 , PERIOD_ID   NUMERIC
			 , BG_CODE     VARCHAR(50)
  		 , BG_CN_NAME  VARCHAR(200)
       , LV0_PROD_LIST_CODE VARCHAR(50)
       , LV1_PROD_LIST_CODE VARCHAR(50)
       , LV2_PROD_LIST_CODE VARCHAR(50)
       , LV3_PROD_LIST_CODE VARCHAR(50)
       , LV4_PROD_LIST_CODE VARCHAR(50)
       , LV0_PROD_LIST_CN_NAME VARCHAR(200)
       , LV1_PROD_LIST_CN_NAME VARCHAR(200)
       , LV2_PROD_LIST_CN_NAME VARCHAR(200)
       , LV3_PROD_LIST_CN_NAME VARCHAR(200)
       , LV4_PROD_LIST_CN_NAME VARCHAR(200)
       , OVERSEA_FLAG                          VARCHAR(10)      -- 国内海外标识
       , REGION_CODE                           VARCHAR(50)      -- 地区部编码
       , REGION_CN_NAME                        VARCHAR(200)     -- 地区部名称
       , REPOFFICE_CODE                        VARCHAR(50)      -- 代表处编码
       , REPOFFICE_CN_NAME                     VARCHAR(200)     -- 代表处名称
       , SIGN_TOP_CUST_CATEGORY_CODE           VARCHAR(50)      -- 签约客户_大T系统部编码
       , SIGN_TOP_CUST_CATEGORY_CN_NAME        VARCHAR(200)     -- 签约客户_大T系统部名称
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME      VARCHAR(200)     -- 签约客户_子网系统部名称
       , SPART_CODE                            VARCHAR(50)      -- SPART编码
       , SPART_CN_NAME                         VARCHAR(2000)    -- SPART中文名称
       , VIEW_FLAG                             VARCHAR(50)      -- 视角标识，用于区分不同视角下的数据(地代办 LOCAL_AGENT（国内海外、地区部、代表处）；系统部 SYS_DEPT（大T系统、子网系统）)
       , ENABLE_FLAG                           VARCHAR(2)       -- 有效标识（Y：有效数据、N：无效数据）
       , APPEND_FLAG                           VARCHAR(2)       -- 补齐标识（Y：补齐数据、N：真实数据）
       , PNP_QTY     NUMERIC
       , USD_PNP_AMT NUMERIC
       , RMB_PNP_AMT NUMERIC
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,SPART_CODE)
  ;

  -- 国内海外（包括全球）、地区部（包括全选）、代表处（包括全选）、大T系统部（包括全选）、子网系统部（包括全选）的数据入到临时表
  V_SQL := '
  INSERT INTO ALL_ACTUAL_ITEM_TEMP(
			   PERIOD_YEAR
			 , PERIOD_ID
			 , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , VIEW_FLAG
       , ENABLE_FLAG
       , APPEND_FLAG
       , PNP_QTY
       , USD_PNP_AMT
       , RMB_PNP_AMT
  )
  SELECT PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , '''' AS SIGN_TOP_CUST_CATEGORY_CODE
       , '''' AS SIGN_TOP_CUST_CATEGORY_CN_NAME
       , '''' AS SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , ''LOCAL_AGENT'' AS VIEW_FLAG
       , (CASE WHEN (PNP_QTY = 0 OR PNP_QTY IS NULL OR PNP_QTY < 0 OR USD_PNP_AMT IS NULL OR USD_PNP_AMT < 0) THEN ''N'' ELSE ''Y'' END) AS ENABLE_FLAG
       , (CASE WHEN REPOFFICE_CODE = ''ALL'' AND APPEND_FLAG_CNT > 0 THEN ''N''
               WHEN REPOFFICE_CODE = ''ALL'' AND APPEND_FLAG_CNT = 0 THEN ''Y''  
          ELSE APPEND_FLAG END) AS APPEND_FLAG
       , PNP_QTY
       , USD_PNP_AMT
       , RMB_PNP_AMT
    FROM SPART_NO_DT_INTO_ALL_TMP -- 国内、海外、全球、地区部（包括全部）、代表处（包括全部）临时表
  UNION ALL
  SELECT PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , '''' AS OVERSEA_FLAG
       , '''' AS REGION_CODE
       , '''' AS REGION_CN_NAME
       , '''' AS REPOFFICE_CODE
       , '''' AS REPOFFICE_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , ''SYS_DEPT'' AS VIEW_FLAG
       , (CASE WHEN (PNP_QTY = 0 OR PNP_QTY IS NULL OR PNP_QTY < 0 OR USD_PNP_AMT IS NULL OR USD_PNP_AMT < 0) THEN ''N'' ELSE ''Y'' END) AS ENABLE_FLAG
       , (CASE WHEN SIGN_SUBSIDIARY_CUSTCATG_CN_NAME = ''全选'' AND APPEND_FLAG_CNT > 0 THEN ''N''
               WHEN SIGN_SUBSIDIARY_CUSTCATG_CN_NAME = ''全选'' AND APPEND_FLAG_CNT = 0 THEN ''Y''  
          ELSE APPEND_FLAG END) AS APPEND_FLAG
       , PNP_QTY
       , USD_PNP_AMT
       , RMB_PNP_AMT
    FROM SPART_DT_INTO_ALL_TMP  -- 大T系统部（包括全选）、子网系统部（包括全选）临时表
  ';

  RAISE NOTICE'777777777777';

  DBMS_OUTPUT.PUT_LINE(V_SQL);

  EXECUTE IMMEDIATE V_SQL;

  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
     F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_MUM,
     F_CAL_LOG_DESC => '国内海外（包括全球）、地区部（包括全选）、代表处（包括全选）、大T系统部（包括全选）、子网系统部（包括全选）的数据入到临时表',
     F_FORMULA_SQL_TXT => V_SQL,
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS'
  );


  -- 数据入到目标表
  V_SQL := '
  INSERT INTO '||V_TO_TABLE||'(
         VERSION_ID
       , PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
       , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , VIEW_FLAG
       , ENABLE_FLAG
       , APPEND_FLAG
       , PNP_QTY
       , USD_PNP_AMT
       , RMB_PNP_AMT
       , CREATED_BY
       , CREATION_DATE
       , LAST_UPDATED_BY
       , LAST_UPDATE_DATE
       , DEL_FLAG
  )
  SELECT '||V_VERSION_ID||' AS VERSION_ID
       , PERIOD_YEAR
			 , PERIOD_ID
			 , BG_CODE
  		 , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , VIEW_FLAG
       , ENABLE_FLAG
       , APPEND_FLAG
       , (CASE WHEN APPEND_FLAG = ''Y'' AND PNP_QTY = 0 AND USD_PNP_AMT = 0 AND RMB_PNP_AMT = 0 THEN NULL ELSE PNP_QTY END) AS PNP_QTY
       , (CASE WHEN APPEND_FLAG = ''Y'' AND PNP_QTY = 0 AND USD_PNP_AMT = 0 AND RMB_PNP_AMT = 0 THEN NULL ELSE USD_PNP_AMT END) AS USD_PNP_AMT
       , (CASE WHEN APPEND_FLAG = ''Y'' AND PNP_QTY = 0 AND USD_PNP_AMT = 0 AND RMB_PNP_AMT = 0 THEN NULL ELSE RMB_PNP_AMT END) AS RMB_PNP_AMT
       , -1 AS CREATED_BY
       , CURRENT_TIMESTAMP AS CREATION_DATE
       , -1 AS LAST_UPDATED_BY
       , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
       , ''N'' AS DEL_FLAG
    FROM ALL_ACTUAL_ITEM_TEMP
  ';

  RAISE NOTICE'777777777777';

  DBMS_OUTPUT.PUT_LINE(V_SQL);

  EXECUTE IMMEDIATE V_SQL;

  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
     F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_MUM,
     F_CAL_LOG_DESC => '数据入到目标表',
     F_FORMULA_SQL_TXT => V_SQL,
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS'
  );


  --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;

  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := 'FAILED';

  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败',
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

