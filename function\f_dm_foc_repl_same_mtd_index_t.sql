-- Name: f_dm_foc_repl_same_mtd_index_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_repl_same_mtd_index_t(f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建人：唐钦 twx1139790

SELECT FIN_DM_OPT_FOI.f_DM_FOC_REPL_SAME_MTD_INDEX_T();
***************************************************************************************************************************************************************/
DECLARE 
  V_SP_NAME        VARCHAR(100) := 'FIN_DM_OPT_FOI.f_DM_FOC_REPL_SAME_MTD_INDEX_T';
  V_VERSION        BIGINT;
  V_ANNL_VERSION   BIGINT;
  V_EXCEPTION_FLAG INT;
  V_HIS_VERSION BIGINT;
  V_HIS_VERSION_NAME INT;
  V_NOW_PERIOD     INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYYMM') AS INT);
  V_NOW_YEAR       INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY') AS INT);     -- 取当前年份（若当月为1月时，即取去年年份）
  -- 取当年所有版本游标
  CURSOR C_DIM IS SELECT VERSION_ID,CAST(REPLACE(VERSION,'-','') AS INT) AS VERSION_NAME
                     FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T 
                     WHERE DEL_FLAG = 'N'
                       AND STATUS = 1
                       AND UPPER(DATA_TYPE) = 'CATEGORY'
                       AND CAST(REPLACE(SUBSTR(VERSION,1,7),'-','') AS INT) BETWEEN CAST(V_NOW_YEAR||'02' AS INT) AND CAST(TO_CHAR(CURRENT_TIMESTAMP,'YYYYMM') AS INT)   -- 每年1月时，取到当前年份1月的版本
					   AND SUBSTR(VERSION,1,4) IN ( V_NOW_YEAR,V_NOW_YEAR+1 )
                       ORDER BY VERSION;
  
BEGIN

X_RESULT_STATUS :='1';

 --写入日志
 V_EXCEPTION_FLAG	:= 0;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 0,
  F_CAL_LOG_DESC => '开始执行',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

  --月度版本号取数
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'ITEM'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;
  
  --年度版本号取数 
  SELECT VERSION_ID
    INTO V_ANNL_VERSION
    FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
   WHERE DEL_FLAG = 'N'
     AND STATUS = 1
     AND UPPER(DATA_TYPE) = 'CATEGORY'
   ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
--采购指数部分取数
DELETE FROM FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_TOTAL_MTD_INDEX_T WHERE VERSION_ID = V_VERSION;

  -- 取历史版本的同编码权重
  OPEN C_DIM;
  LOOP
  FETCH  C_DIM   INTO  V_HIS_VERSION,V_HIS_VERSION_NAME;
  EXIT WHEN C_DIM%NOTFOUND;
  -- 循环提取权重表中历史版本的最新年份数据，作为历史月份的数据重新存储到月度累计权重表
  -- 同编码-总成本
INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_TOTAL_MTD_INDEX_T
  (VERSION_ID,
   PERIOD_YEAR,
   PERIOD_ID,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   COST_INDEX,
   PARENT_CODE,
   PARENT_CN_NAME,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   VIEW_FLAG,
   CALIBER_FLAG)
  SELECT V_VERSION AS VERSION_ID,
         V_NOW_YEAR AS PERIOD_YEAR,
         CAST(TO_CHAR(ADD_MONTHS(TO_DATE(V_HIS_VERSION_NAME,'YYYYMM'),-1),'YYYYMM') AS INT) AS PERIOD_ID,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP AS COST_INDEX,
         DECODE(GROUP_LEVEL,'LV0',NULL,PARENT_CODE) AS PARENT_CODE,
         DECODE(GROUP_LEVEL,'LV0',NULL,PARENT_CN_NAME) AS PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         VIEW_FLAG,
         CALIBER_FLAG
	  FROM FIN_DM_OPT_FOI.DM_FOC_TOTAL_ANNUAL_AMP_T
	  WHERE VERSION_ID = V_HIS_VERSION
        AND OVERSEA_FLAG = 'G'
        AND LV0_PROD_LIST_CODE = 'GR'
        AND PERIOD_YEAR = V_NOW_YEAR
        AND GROUP_LEVEL IN ('LV0', 'LV1', 'LV2', 'LV3');
     
   --写入日志
   V_EXCEPTION_FLAG	:= 2;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => 2,
    F_CAL_LOG_DESC => '月份为：'||TO_CHAR(ADD_MONTHS(TO_DATE(V_HIS_VERSION_NAME,'YYYYMM'),-1),'YYYYMM')||'的同编码指数计算完成',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
  END LOOP ;
  CLOSE  C_DIM;
	
  RETURN 'SUCCESS';
  

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
     PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_STEP_NUM => V_EXCEPTION_FLAG,
    F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

 END; 
$$
/

