-- Name: f_dm_foi_lev_annual_amp; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_lev_annual_amp(f_caliber_flag character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年2月20日11点14分
  创建人  ：唐钦
  背景描述：采购ICT&数字能源-年度涨跌幅表(年度分析-柱状图)
  参数描述：f_caliber_flag ：I：采购价格指数、E：数字能源指数
            f_version_id ：年度分析页面：版本号入参
            x_success_flag ：是否成功
  事例    ：select FIN_DM_OPT_FOI.F_DM_FOI_LEV_ANNUAL_AMP('I')
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOI_LEV_ANNUAL_AMP'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_YEAR  BIGINT := YEAR(CURRENT_TIMESTAMP);   -- 当前年份
  V_SQL        TEXT; 
  V_TMP_TABLE  VARCHAR(200);
  V_FROM_TABLE VARCHAR(200);
  V_FROM1_TABLE VARCHAR(200);
  V_TO_TABLE   VARCHAR(200);
  V_COLUMN     VARCHAR(50);
  V_PARENT_NAME VARCHAR(50);
  -- 202407版本新增
  V_SQL_CONDITION VARCHAR(200);
  V_CALIBER VARCHAR(100);
  V_IN_CALIBER VARCHAR(200);
  V_VERSION_TABLE VARCHAR(200);
  V_SQLT2_CONDITION VARCHAR(200);
  
BEGIN
  X_RESULT_STATUS = '1';
  
-- 判断不同入参，对应不同变量、参数
IF F_CALIBER_FLAG = 'I' THEN  -- 采购价格指数
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ANNUAL_COST_T';  -- 年均本表
    V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_LEV_ANNUAL_WEIGHT_T';  -- 单年权重表
    V_TMP_TABLE := 'DM_FOI_MID_GROUP_AMP_TMP';  
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_LEV_ANNUAL_AMP_T';
    V_COLUMN := 'CONTINUITY_TYPE';
    V_PARENT_NAME := '';
    V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_PLAN_VERSION_T';
ELSIF F_CALIBER_FLAG = 'E' THEN  -- 数字能源指数
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_MID_ANNUAL_COST_T';  -- 年均本表
    V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_LEV_ANNUAL_WEIGHT_T';  -- 单年权重表
    V_TMP_TABLE := 'DM_FOI_ENERGY_MID_GROUP_AMP_TMP';  
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_LEV_ANNUAL_AMP_T';
    V_COLUMN := 'GROUP_PUR_FLAG';
    V_PARENT_NAME := 'PARENT_CN_NAME,';
    V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T';
ELSIF F_CALIBER_FLAG IN ('IAS','EAST_CHINA_PQC') THEN  -- IAS/华东采购
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_MID_ANNUAL_COST_T';  -- 年均本表
    V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_LEV_ANNUAL_WEIGHT_T';  -- 单年权重表
    V_TMP_TABLE := 'DM_FOI_IAS_ECPQC_MID_GROUP_AMP_TMP';  
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_LEV_ANNUAL_AMP_T';
    V_COLUMN := 'GROUP_PUR_FLAG';
    V_PARENT_NAME := 'PARENT_CN_NAME,';
    V_CALIBER := 'CALIBER_FLAG,';
    V_IN_CALIBER := ''''||F_CALIBER_FLAG||''' AS CALIBER_FLAG,';
    V_SQL_CONDITION := ' AND CALIBER_FLAG = '''||F_CALIBER_FLAG||'''';
    V_SQLT2_CONDITION := ' AND T2.CALIBER_FLAG = '''||F_CALIBER_FLAG||'''';
    IF F_CALIBER_FLAG = 'IAS' THEN   -- IAS
       V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_PLAN_VERSION_T';
    ELSIF F_CALIBER_FLAG = 'EAST_CHINA_PQC' THEN   -- 华东采购
       V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ECPQC_PLAN_VERSION_T';
    END IF;
END IF;
  
-- 版本号取值
  IF F_VERSION_ID IS NULL THEN
     V_SQL := '   
         SELECT VERSION_ID
            FROM '||V_VERSION_TABLE||'
            WHERE
             DEL_FLAG = ''N''
             AND STATUS = 1
             AND UPPER(DATA_TYPE) = ''CATEGORY''
             AND UPPER(VERSION_TYPE) IN (''AUTO'',''FINAL'')
             ORDER BY LAST_UPDATE_DATE DESC
             LIMIT 1';
   EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF; 
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  --1.创建涨跌幅基础表(中间表)
  V_SQL := '
   DROP TABLE IF EXISTS '||V_TMP_TABLE||';
   CREATE TEMPORARY TABLE '||V_TMP_TABLE||' (
          VERSION_ID INT8,
          PERIOD_YEAR VARCHAR(50),
          GROUP_CODE VARCHAR(50),
          GROUP_CN_NAME VARCHAR(1000),
          GROUP_LEVEL VARCHAR(50),
          ANNUAL_AMP NUMERIC,
          PARENT_CODE VARCHAR(50),
          PARENT_CN_NAME VARCHAR(1000),
          L2_CEG_CODE VARCHAR(50),
          L2_CEG_CN_NAME VARCHAR(200),
          L3_CEG_CODE VARCHAR(50),
          L3_CEG_SHORT_CN_NAME VARCHAR(200),
          L4_CEG_CODE VARCHAR(50),
          L4_CEG_SHORT_CN_NAME VARCHAR(200),
          CATEGORY_CODE VARCHAR(50),
          CATEGORY_CN_NAME VARCHAR(200),
          ITEM_CODE VARCHAR(50),
          ITEM_CN_NAME VARCHAR(1000),
          APPEND_FLAG VARCHAR(2),
          '||V_COLUMN||' VARCHAR(50),
          LEVEL_TYPE VARCHAR(50) -- 标记数据类型为:YTD实际数，YTD_PREDICT实际数+预测数
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY ROUNDROBIN';
    EXECUTE IMMEDIATE V_SQL;
  
  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '涨跌幅临时表创建完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 将SUPPLIER层级的年度涨跌幅数据插入中间表
V_SQL := '
  INSERT INTO '||V_TMP_TABLE||'(
              VERSION_ID,
              PERIOD_YEAR,
              L2_CEG_CODE,
              L2_CEG_CN_NAME,
              L3_CEG_CODE,
              L3_CEG_SHORT_CN_NAME,
              L4_CEG_CODE,
              L4_CEG_SHORT_CN_NAME,
              CATEGORY_CODE,
              CATEGORY_CN_NAME,
              ITEM_CODE,
              ITEM_CN_NAME,
              GROUP_CODE,
              GROUP_CN_NAME,
              GROUP_LEVEL,
              ANNUAL_AMP,
              PARENT_CODE,
              PARENT_CN_NAME,
              LEVEL_TYPE
  )
  -- SUPPLIER层级年度涨跌幅的计算逻辑
 WITH AVG_TMP AS(
       SELECT PERIOD_YEAR,
              LEVEL_TYPE, -- 标记数据类型为 YTD实际数,SUPPLIER层无预测数
              L2_CEG_CODE,
              L2_CEG_CN_NAME,
              L3_CEG_CODE,
              L3_CEG_SHORT_CN_NAME,
              L4_CEG_CODE,
              L4_CEG_SHORT_CN_NAME,
              CATEGORY_CODE,
              CATEGORY_CN_NAME,
              ITEM_CODE,
              ITEM_CN_NAME,
              SUPPLIER_CODE AS GROUP_CODE,
              SUPPLIER_CN_NAME AS GROUP_CN_NAME,  
              ''SUPPLIER'' AS GROUP_LEVEL,
              RMB_AVG_AMT,-- 年均本数据
              ITEM_CODE AS PARENT_CODE,
              ITEM_CN_NAME AS PARENT_CN_NAME
         FROM '||V_FROM_TABLE||'
        WHERE GROUP_LEVEL = ''SUPPLIER'' -- SUPPLIER 层实际数
          AND VERSION_ID = '||V_VERSION_ID||'
          '||V_SQL_CONDITION||'
   )
  -- 将年均本数据按年份行转列 
  , BY_YEAR_AVG_TMP AS(
       SELECT L2_CEG_CODE,
              L2_CEG_CN_NAME,
              L3_CEG_CODE,
              L3_CEG_SHORT_CN_NAME,
              L4_CEG_CODE,
              L4_CEG_SHORT_CN_NAME,
              CATEGORY_CODE,
              CATEGORY_CN_NAME,
              ITEM_CODE,
              ITEM_CN_NAME,
              GROUP_CODE,
              GROUP_CN_NAME,
              GROUP_LEVEL,
              PARENT_CODE,
              PARENT_CN_NAME,
              SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR||'-3 THEN RMB_AVG_AMT ELSE 0 END) AS LAST_THREE_YEAR_AVG,
              SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR||'-2 THEN RMB_AVG_AMT ELSE 0 END) AS LAST_TWO_YEAR_AVG,
              SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR||'-1 THEN RMB_AVG_AMT ELSE 0 END) AS LAST_YEAR_AVG,
              SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR||'   THEN RMB_AVG_AMT ELSE 0 END) AS YEAR_AVG,
              LEVEL_TYPE -- 标记数据类型为:YTD实际数，YTD_PREDICT实际数+预测数
         FROM AVG_TMP
     GROUP BY L2_CEG_CODE,
              L2_CEG_CN_NAME,
              L3_CEG_CODE,
              L3_CEG_SHORT_CN_NAME,
              L4_CEG_CODE,
              L4_CEG_SHORT_CN_NAME,
              CATEGORY_CODE,
              CATEGORY_CN_NAME,
              ITEM_CODE,
              ITEM_CN_NAME,
              GROUP_CODE,
              GROUP_CN_NAME,
              GROUP_LEVEL,
              PARENT_CODE,
              PARENT_CN_NAME,
              LEVEL_TYPE
    )
  -- 计算SUPPLIER层级的年度涨跌幅数据
       SELECT '||V_VERSION_ID||' AS VERSION_ID,
              T.PERIOD_YEAR,
              T.L2_CEG_CODE,
              T.L2_CEG_CN_NAME,
              T.L3_CEG_CODE,
              T.L3_CEG_SHORT_CN_NAME,
              T.L4_CEG_CODE,
              T.L4_CEG_SHORT_CN_NAME,
              T.CATEGORY_CODE,
              T.CATEGORY_CN_NAME,
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              T.GROUP_CODE,
              T.GROUP_CN_NAME,
              T.GROUP_LEVEL,
              NVL(T.ANNUAL_AMP,0) AS ANNUAL_AMP,
              T.PARENT_CODE,
              T.PARENT_CN_NAME,
              T.LEVEL_TYPE
                     FROM (      --前年涨跌幅
                                 SELECT '||V_YEAR||'-2 AS PERIOD_YEAR,
                                                    L2_CEG_CODE,
                                                    L2_CEG_CN_NAME,
                                                    L3_CEG_CODE,
                                                    L3_CEG_SHORT_CN_NAME,
                                                    L4_CEG_CODE,
                                                    L4_CEG_SHORT_CN_NAME,
                                                    CATEGORY_CODE,
                                                    CATEGORY_CN_NAME,
                                                    ITEM_CODE,
                                                    ITEM_CN_NAME,
                                                    GROUP_CODE,
                                                    GROUP_CN_NAME,
                                                    GROUP_LEVEL,
                                                    ((LAST_TWO_YEAR_AVG/NULLIF(LAST_THREE_YEAR_AVG,0))-1) AS ANNUAL_AMP,
                                                    PARENT_CODE,
                                                    PARENT_CN_NAME,
                                                    LEVEL_TYPE
                                               FROM BY_YEAR_AVG_TMP
                                 UNION ALL
                                 --去年涨跌幅
                                 SELECT '||V_YEAR||'-1 AS PERIOD_YEAR,
                                                    L2_CEG_CODE,
                                                    L2_CEG_CN_NAME,
                                                    L3_CEG_CODE,
                                                    L3_CEG_SHORT_CN_NAME,
                                                    L4_CEG_CODE,
                                                    L4_CEG_SHORT_CN_NAME,
                                                    CATEGORY_CODE,
                                                    CATEGORY_CN_NAME,
                                                    ITEM_CODE,
                                                    ITEM_CN_NAME,
                                                    GROUP_CODE,
                                                    GROUP_CN_NAME,
                                                    GROUP_LEVEL,
                                                    ((LAST_YEAR_AVG/NULLIF(LAST_TWO_YEAR_AVG,0))-1) AS ANNUAL_AMP,
                                                    PARENT_CODE,
                                                    PARENT_CN_NAME,
                                                    LEVEL_TYPE
                                               FROM BY_YEAR_AVG_TMP   
                                   UNION ALL
                                   --今年涨跌幅
                                   SELECT '||V_YEAR||' AS PERIOD_YEAR,
                                                    L2_CEG_CODE,
                                                    L2_CEG_CN_NAME,
                                                    L3_CEG_CODE,
                                                    L3_CEG_SHORT_CN_NAME,
                                                    L4_CEG_CODE,
                                                    L4_CEG_SHORT_CN_NAME,
                                                    CATEGORY_CODE,
                                                    CATEGORY_CN_NAME,
                                                    ITEM_CODE,
                                                    ITEM_CN_NAME,
                                                    GROUP_CODE,
                                                    GROUP_CN_NAME,
                                                    GROUP_LEVEL,
                                                    ((YEAR_AVG/NULLIF(LAST_YEAR_AVG,0))-1) AS ANNUAL_AMP,
                                                    PARENT_CODE,
                                                    PARENT_CN_NAME,
                                                    LEVEL_TYPE
                                               FROM BY_YEAR_AVG_TMP 
                                            ) T';           
                              
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
                              
   --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入领域标识为：'||F_CALIBER_FLAG||'，VERSION_ID= '||V_VERSION_ID ||' 的SUPPLIER层级的数据到'||V_TMP_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 将ITEM层级的年度涨跌幅数据插入中间表
V_SQL := '
  INSERT INTO '||V_TMP_TABLE||'(
              VERSION_ID,
              PERIOD_YEAR,
              L2_CEG_CODE,
              L2_CEG_CN_NAME,
              L3_CEG_CODE,
              L3_CEG_SHORT_CN_NAME,
              L4_CEG_CODE,
              L4_CEG_SHORT_CN_NAME,
              CATEGORY_CODE,
              CATEGORY_CN_NAME,
              ITEM_CODE,
              ITEM_CN_NAME,
              GROUP_CODE,
              GROUP_CN_NAME,
              GROUP_LEVEL,
              ANNUAL_AMP,
              PARENT_CODE,
              PARENT_CN_NAME,
              LEVEL_TYPE
  )
  -- ITEM层级年度涨跌幅的计算逻辑
 WITH AVG_TMP AS(
       SELECT PERIOD_YEAR,
              LEVEL_TYPE, -- 标记数据类型为:YTD实际数，YTD_PREDICT实际数+预测数
              L2_CEG_CODE,
              L2_CEG_CN_NAME,
              L3_CEG_CODE,
              L3_CEG_SHORT_CN_NAME,
              L4_CEG_CODE,
              L4_CEG_SHORT_CN_NAME,
              CATEGORY_CODE,
              CATEGORY_CN_NAME,
              ITEM_CODE,
              ITEM_CN_NAME,
              ITEM_CODE AS GROUP_CODE,
              ITEM_CN_NAME AS GROUP_CN_NAME,  
              ''ITEM'' AS GROUP_LEVEL,
              RMB_AVG_AMT,-- 年均本数据
              CATEGORY_CODE AS PARENT_CODE,
              CATEGORY_CN_NAME AS PARENT_CN_NAME
         FROM '||V_FROM_TABLE||'
        WHERE GROUP_LEVEL = ''ITEM'' -- 包含 ITEM 层的实际数 和 实际数+预测数
          AND VERSION_ID = '||V_VERSION_ID||'
          '||V_SQL_CONDITION||'
   )
  -- 将年均本数据按年份行转列 
  , BY_YEAR_AVG_TMP AS(
       SELECT L2_CEG_CODE,
              L2_CEG_CN_NAME,
              L3_CEG_CODE,
              L3_CEG_SHORT_CN_NAME,
              L4_CEG_CODE,
              L4_CEG_SHORT_CN_NAME,
              CATEGORY_CODE,
              CATEGORY_CN_NAME,
              ITEM_CODE,
              ITEM_CN_NAME,
              GROUP_CODE,
              GROUP_CN_NAME,
              GROUP_LEVEL,
              PARENT_CODE,
              PARENT_CN_NAME,
              SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR||'-3 THEN RMB_AVG_AMT ELSE 0 END) AS LAST_THREE_YEAR_AVG,
              SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR||'-2 THEN RMB_AVG_AMT ELSE 0 END) AS LAST_TWO_YEAR_AVG,
              SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR||'-1 THEN RMB_AVG_AMT ELSE 0 END) AS LAST_YEAR_AVG,
              SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR||'   THEN RMB_AVG_AMT ELSE 0 END) AS YEAR_AVG,
              LEVEL_TYPE
         FROM AVG_TMP
     GROUP BY L2_CEG_CODE,
              L2_CEG_CN_NAME,
              L3_CEG_CODE,
              L3_CEG_SHORT_CN_NAME,
              L4_CEG_CODE,
              L4_CEG_SHORT_CN_NAME,
              CATEGORY_CODE,
              CATEGORY_CN_NAME,
              ITEM_CODE,
              ITEM_CN_NAME,
              GROUP_CODE,
              GROUP_CN_NAME,
              GROUP_LEVEL,
              PARENT_CODE,
              PARENT_CN_NAME,
              LEVEL_TYPE
    )
  -- 计算ITEM层级的年度涨跌幅数据
       SELECT '||V_VERSION_ID||' AS VERSION_ID,
              T.PERIOD_YEAR,
              T.L2_CEG_CODE,
              T.L2_CEG_CN_NAME,
              T.L3_CEG_CODE,
              T.L3_CEG_SHORT_CN_NAME,
              T.L4_CEG_CODE,
              T.L4_CEG_SHORT_CN_NAME,
              T.CATEGORY_CODE,
              T.CATEGORY_CN_NAME,
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              T.GROUP_CODE,
              T.GROUP_CN_NAME,
              T.GROUP_LEVEL,
              NVL(T.ANNUAL_AMP,0) AS ANNUAL_AMP,
              T.PARENT_CODE,
              T.PARENT_CN_NAME,
              T.LEVEL_TYPE
                     FROM (      --前年涨跌幅
                                 SELECT '||V_YEAR||'-2 AS PERIOD_YEAR,
                                                    L2_CEG_CODE,
                                                    L2_CEG_CN_NAME,
                                                    L3_CEG_CODE,
                                                    L3_CEG_SHORT_CN_NAME,
                                                    L4_CEG_CODE,
                                                    L4_CEG_SHORT_CN_NAME,
                                                    CATEGORY_CODE,
                                                    CATEGORY_CN_NAME,
                                                    ITEM_CODE,
                                                    ITEM_CN_NAME,
                                                    GROUP_CODE,
                                                    GROUP_CN_NAME,
                                                    GROUP_LEVEL,
                                                    ((LAST_TWO_YEAR_AVG/NULLIF(LAST_THREE_YEAR_AVG,0))-1) AS ANNUAL_AMP,
                                                    PARENT_CODE,
                                                    PARENT_CN_NAME,
                                                    LEVEL_TYPE
                                               FROM BY_YEAR_AVG_TMP
                                 UNION ALL
                                 --去年涨跌幅
                                 SELECT '||V_YEAR||'-1 AS PERIOD_YEAR,
                                                    L2_CEG_CODE,
                                                    L2_CEG_CN_NAME,
                                                    L3_CEG_CODE,
                                                    L3_CEG_SHORT_CN_NAME,
                                                    L4_CEG_CODE,
                                                    L4_CEG_SHORT_CN_NAME,
                                                    CATEGORY_CODE,
                                                    CATEGORY_CN_NAME,
                                                    ITEM_CODE,
                                                    ITEM_CN_NAME,
                                                    GROUP_CODE,
                                                    GROUP_CN_NAME,
                                                    GROUP_LEVEL,
                                                    ((LAST_YEAR_AVG/NULLIF(LAST_TWO_YEAR_AVG,0))-1) AS ANNUAL_AMP,
                                                    PARENT_CODE,
                                                    PARENT_CN_NAME,
                                                    LEVEL_TYPE
                                               FROM BY_YEAR_AVG_TMP   
                                   UNION ALL
                                   --今年涨跌幅
                                   SELECT '||V_YEAR||' AS PERIOD_YEAR,
                                                    L2_CEG_CODE,
                                                    L2_CEG_CN_NAME,
                                                    L3_CEG_CODE,
                                                    L3_CEG_SHORT_CN_NAME,
                                                    L4_CEG_CODE,
                                                    L4_CEG_SHORT_CN_NAME,
                                                    CATEGORY_CODE,
                                                    CATEGORY_CN_NAME,
                                                    ITEM_CODE,
                                                    ITEM_CN_NAME,
                                                    GROUP_CODE,
                                                    GROUP_CN_NAME,
                                                    GROUP_LEVEL,
                                                    ((YEAR_AVG/NULLIF(LAST_YEAR_AVG,0))-1) AS ANNUAL_AMP,
                                                    PARENT_CODE,
                                                    PARENT_CN_NAME,
                                                    LEVEL_TYPE
                                               FROM BY_YEAR_AVG_TMP 
                                            ) T';
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
                              
   --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入领域标识为：'||F_CALIBER_FLAG||'，VERSION_ID= '||V_VERSION_ID ||' 的ITEM层级的数据到'||V_TMP_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
     
  -- 将品类层级的年度涨跌幅数据插入中间表
V_SQL := '
  INSERT INTO '||V_TMP_TABLE||'(
              VERSION_ID,
              PERIOD_YEAR,
              L2_CEG_CODE,
              L2_CEG_CN_NAME,
              L3_CEG_CODE,
              L3_CEG_SHORT_CN_NAME,
              L4_CEG_CODE,
              L4_CEG_SHORT_CN_NAME,
              CATEGORY_CODE,
              CATEGORY_CN_NAME,
              GROUP_CODE,
              GROUP_CN_NAME,
              GROUP_LEVEL,
              ANNUAL_AMP,
              PARENT_CODE,
              PARENT_CN_NAME,
              LEVEL_TYPE
  )
  -- 取出ITEM层级年度涨跌幅*权重数据
    WITH BY_YEAR_CATEGORY_TMP AS(
                     SELECT T1.PERIOD_YEAR,
                            T1.L2_CEG_CODE,
                            T1.L2_CEG_CN_NAME,
                            T1.L3_CEG_CODE,
                            T1.L3_CEG_SHORT_CN_NAME,
                            T1.L4_CEG_CODE,
                            T1.L4_CEG_SHORT_CN_NAME,
                            T1.CATEGORY_CODE,
                            T1.CATEGORY_CN_NAME,
                            T1.GROUP_CODE,
                            T1.GROUP_CN_NAME,
                            T1.GROUP_LEVEL,
                            T1.ANNUAL_AMP * T2.WEIGHT_RATE AS ANNUAL_AMP_WEIGHT,
                            T1.PARENT_CODE,
                            T1.PARENT_CN_NAME,
                            T1.LEVEL_TYPE
                       FROM '||V_TMP_TABLE||' T1
                 INNER JOIN '||V_FROM1_TABLE||' T2
                         ON T1.VERSION_ID = T2.VERSION_ID
                        AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
                        AND T1.L2_CEG_CODE = T2.L2_CEG_CODE
                        AND T1.L3_CEG_CODE = T2.L3_CEG_CODE
                        AND T1.L4_CEG_CODE = T2.L4_CEG_CODE
                        AND T1.CATEGORY_CODE = T2.CATEGORY_CODE
                        AND T1.GROUP_CODE = T2.GROUP_CODE
                        AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
                      WHERE T1.VERSION_ID = '||V_VERSION_ID||'
                        AND T2.VERSION_ID = '||V_VERSION_ID||'
                        AND T1.GROUP_LEVEL = ''ITEM''
                        '||V_SQLT2_CONDITION||'
                       )
                     -- 品类层级年度涨跌幅计算逻辑
                     SELECT '||V_VERSION_ID||' AS VERSION_ID,
                            S1.PERIOD_YEAR,
                            S1.L2_CEG_CODE,
                            S1.L2_CEG_CN_NAME,
                            S1.L3_CEG_CODE,
                            S1.L3_CEG_SHORT_CN_NAME,
                            S1.L4_CEG_CODE,
                            S1.L4_CEG_SHORT_CN_NAME,
                            S1.CATEGORY_CODE,
                            S1.CATEGORY_CN_NAME,
                            S1.CATEGORY_CODE AS GROUP_CODE,
                            S1.CATEGORY_CN_NAME AS GROUP_CN_NAME,
                            ''CATEGORY'' AS GROUP_LEVEL,
                            NVL(SUM(S1.ANNUAL_AMP_WEIGHT),0) AS ANNUAL_AMP,
                            S1.L4_CEG_CODE AS PARENT_CODE,
                            S1.L4_CEG_SHORT_CN_NAME AS PARENT_CN_NAME,
                            S1.LEVEL_TYPE
                            FROM BY_YEAR_CATEGORY_TMP S1
                            GROUP BY S1.PERIOD_YEAR,
                                     S1.L2_CEG_CODE,
                                     S1.L2_CEG_CN_NAME,
                                     S1.L3_CEG_CODE,
                                     S1.L3_CEG_SHORT_CN_NAME,
                                     S1.L4_CEG_CODE,
                                     S1.L4_CEG_SHORT_CN_NAME,
                                     S1.CATEGORY_CODE,
                                     S1.CATEGORY_CN_NAME,
                                     S1.LEVEL_TYPE';
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
  
    --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入领域标识为：'||F_CALIBER_FLAG||'，VERSION_ID= '||V_VERSION_ID ||' 的品类层级的数据到'||V_TMP_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');                                             

 -- 将模块L4_CEG层级的年度涨跌幅数据插入中间表
V_SQL := '
  INSERT INTO '||V_TMP_TABLE||'(
              VERSION_ID,
              PERIOD_YEAR,
              L2_CEG_CODE,
              L2_CEG_CN_NAME,
              L3_CEG_CODE,
              L3_CEG_SHORT_CN_NAME,
              L4_CEG_CODE,
              L4_CEG_SHORT_CN_NAME,
              GROUP_CODE,
              GROUP_CN_NAME,
              GROUP_LEVEL,
              ANNUAL_AMP,
              PARENT_CODE,
              PARENT_CN_NAME,
              LEVEL_TYPE
  )
  -- 取出品类层级年度涨跌幅*权重数据
    WITH BY_YEAR_L4_TMP AS(
                     SELECT T1.PERIOD_YEAR,
                            T1.L2_CEG_CODE,
                            T1.L2_CEG_CN_NAME,
                            T1.L3_CEG_CODE,
                            T1.L3_CEG_SHORT_CN_NAME,
                            T1.L4_CEG_CODE,
                            T1.L4_CEG_SHORT_CN_NAME,
                            T1.GROUP_CODE,
                            T1.GROUP_CN_NAME,
                            T1.GROUP_LEVEL,
                            T1.ANNUAL_AMP * T2.WEIGHT_RATE AS ANNUAL_AMP_WEIGHT,
                            T1.PARENT_CODE,
                            T1.PARENT_CN_NAME,
                            T1.LEVEL_TYPE
                       FROM '||V_TMP_TABLE||' T1
                 INNER JOIN '||V_FROM1_TABLE||' T2
                         ON T1.VERSION_ID = T2.VERSION_ID
                        AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
                        AND T1.L2_CEG_CODE = T2.L2_CEG_CODE
                        AND T1.L3_CEG_CODE = T2.L3_CEG_CODE
                        AND T1.L4_CEG_CODE = T2.L4_CEG_CODE
                        AND T1.GROUP_CODE = T2.GROUP_CODE
                        AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
                      WHERE T1.VERSION_ID = '||V_VERSION_ID||'
                        AND T2.VERSION_ID = '||V_VERSION_ID||'
                        AND T1.GROUP_LEVEL = ''CATEGORY''
                        '||V_SQLT2_CONDITION||'
                       )
                     -- 模块L4_CEG层级年度涨跌幅计算逻辑
                     SELECT '||V_VERSION_ID||' AS VERSION_ID,
                            S1.PERIOD_YEAR,
                            S1.L2_CEG_CODE,
                            S1.L2_CEG_CN_NAME,
                            S1.L3_CEG_CODE,
                            S1.L3_CEG_SHORT_CN_NAME,
                            S1.L4_CEG_CODE,
                            S1.L4_CEG_SHORT_CN_NAME,
                            S1.L4_CEG_CODE AS GROUP_CODE,
                            S1.L4_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,
                            ''LV4'' AS GROUP_LEVEL,
                            NVL(SUM(S1.ANNUAL_AMP_WEIGHT),0) AS ANNUAL_AMP,
                            S1.L3_CEG_CODE AS PARENT_CODE,
                            S1.L3_CEG_SHORT_CN_NAME AS PARENT_CN_NAME,
                            S1.LEVEL_TYPE
                            FROM BY_YEAR_L4_TMP S1
                            GROUP BY S1.PERIOD_YEAR,
                                     S1.L2_CEG_CODE,
                                     S1.L2_CEG_CN_NAME,
                                     S1.L3_CEG_CODE,
                                     S1.L3_CEG_SHORT_CN_NAME,
                                     S1.L4_CEG_CODE,
                                     S1.L4_CEG_SHORT_CN_NAME,
                                     S1.LEVEL_TYPE';
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
  
    --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入领域标识为：'||F_CALIBER_FLAG||'，VERSION_ID= '||V_VERSION_ID ||' 的L4模块层级的数据到'||V_TMP_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS'); 

 -- 将专家团L3_CEG层级的年度涨跌幅数据插入中间表
V_SQL := '
  INSERT INTO '||V_TMP_TABLE||'(
              VERSION_ID,
              PERIOD_YEAR,
              L2_CEG_CODE,
              L2_CEG_CN_NAME,
              L3_CEG_CODE,
              L3_CEG_SHORT_CN_NAME,
              GROUP_CODE,
              GROUP_CN_NAME,
              GROUP_LEVEL,
              ANNUAL_AMP,
              PARENT_CODE,
              PARENT_CN_NAME,
              LEVEL_TYPE
  )
  -- 取出模块L4_CEG层级年度涨跌幅*权重数据
    WITH BY_YEAR_L3_TMP AS(
                     SELECT T1.PERIOD_YEAR,
                            T1.L2_CEG_CODE,
                            T1.L2_CEG_CN_NAME,
                            T1.L3_CEG_CODE,
                            T1.L3_CEG_SHORT_CN_NAME,
                            T1.GROUP_CODE,
                            T1.GROUP_CN_NAME,
                            T1.GROUP_LEVEL,
                            T1.ANNUAL_AMP * T2.WEIGHT_RATE AS ANNUAL_AMP_WEIGHT,
                            T1.PARENT_CODE,
                            T1.PARENT_CN_NAME,
                            T1.LEVEL_TYPE
                       FROM '||V_TMP_TABLE||' T1
                 INNER JOIN '||V_FROM1_TABLE||' T2
                         ON T1.VERSION_ID = T2.VERSION_ID
                        AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
                        AND T1.L2_CEG_CODE = T2.L2_CEG_CODE
                        AND T1.L3_CEG_CODE = T2.L3_CEG_CODE
                        AND T1.GROUP_CODE = T2.GROUP_CODE
                        AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
                      WHERE T1.VERSION_ID = '||V_VERSION_ID||'
                        AND T2.VERSION_ID = '||V_VERSION_ID||'
                        AND T1.GROUP_LEVEL = ''LV4''
                        '||V_SQLT2_CONDITION||'
                       )
                     -- 专家团L3_CEG层级年度涨跌幅计算逻辑
                     SELECT '||V_VERSION_ID||' AS VERSION_ID,
                            S1.PERIOD_YEAR,
                            S1.L2_CEG_CODE,
                            S1.L2_CEG_CN_NAME,
                            S1.L3_CEG_CODE,
                            S1.L3_CEG_SHORT_CN_NAME,
                            S1.L3_CEG_CODE AS GROUP_CODE,
                            S1.L3_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,
                            ''LV3'' AS GROUP_LEVEL,
                            NVL(SUM(S1.ANNUAL_AMP_WEIGHT),0) AS ANNUAL_AMP,
                            S1.L2_CEG_CODE AS PARENT_CODE,
                            S1.L2_CEG_CN_NAME AS PARENT_CN_NAME,
                            S1.LEVEL_TYPE
                            FROM BY_YEAR_L3_TMP S1
                            GROUP BY S1.PERIOD_YEAR,
                                     S1.L2_CEG_CODE,
                                     S1.L2_CEG_CN_NAME,
                                     S1.L3_CEG_CODE,
                                     S1.L3_CEG_SHORT_CN_NAME,
                                     S1.LEVEL_TYPE';
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
  
    --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入领域标识为：'||F_CALIBER_FLAG||'，VERSION_ID= '||V_VERSION_ID ||' 的L3层级的数据到'||V_TMP_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS'); 

 -- 将采购组织L2_CEG层级的年度涨跌幅数据插入中间表
V_SQL := '
  INSERT INTO '||V_TMP_TABLE||'(
              VERSION_ID,
              PERIOD_YEAR,
              L2_CEG_CODE,
              L2_CEG_CN_NAME,
              GROUP_CODE,
              GROUP_CN_NAME,
              GROUP_LEVEL,
              ANNUAL_AMP,
              PARENT_CODE,
              PARENT_CN_NAME,
              LEVEL_TYPE,
              '||V_COLUMN||' -- 连续性类型取值
  )
  -- 取出专家团L3_CEG层级年度涨跌幅*权重数据
    WITH BY_YEAR_L2_TMP AS(
                     SELECT T1.PERIOD_YEAR,
                            T1.L2_CEG_CODE,
                            T1.L2_CEG_CN_NAME,
                            T1.GROUP_CODE,
                            T1.GROUP_CN_NAME,
                            T1.GROUP_LEVEL,
                            T1.ANNUAL_AMP * T2.WEIGHT_RATE AS ANNUAL_AMP_WEIGHT,
                            T1.PARENT_CODE,
                            T1.PARENT_CN_NAME,
                            T1.LEVEL_TYPE,
                            T2.'||V_COLUMN||'
                       FROM '||V_TMP_TABLE||' T1
                 INNER JOIN ( --取出带连续性标签/是否含集团代采的专家团权重
                              SELECT DISTINCT PERIOD_YEAR, GROUP_CODE, GROUP_LEVEL, WEIGHT_RATE, '||V_COLUMN||'
                                FROM '||V_FROM1_TABLE||'
                               WHERE VERSION_ID = '||V_VERSION_ID||'
                                 AND UPPER(GROUP_LEVEL) = ''LV3''
                                 '||V_SQL_CONDITION||'
                             ) T2
                         ON T1.GROUP_CODE = T2.GROUP_CODE
                        AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
                        AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
                        AND T1.VERSION_ID = '||V_VERSION_ID||'
                           )
                     -- 采购组织L2_CEG层级年度涨跌幅计算逻辑
                     SELECT '||V_VERSION_ID||' AS VERSION_ID,
                            S1.PERIOD_YEAR,
                            S1.L2_CEG_CODE,
                            S1.L2_CEG_CN_NAME,
                            S1.L2_CEG_CODE AS GROUP_CODE,
                            S1.L2_CEG_CN_NAME AS GROUP_CN_NAME,
                            ''LV2'' AS GROUP_LEVEL,
                            NVL(SUM(S1.ANNUAL_AMP_WEIGHT),0) AS ANNUAL_AMP,
                            S1.L2_CEG_CODE AS PARENT_CODE,
                            S1.L2_CEG_CN_NAME AS PARENT_CN_NAME,
                            S1.LEVEL_TYPE,
                            S1.'||V_COLUMN||'
                            FROM BY_YEAR_L2_TMP S1
                            GROUP BY S1.PERIOD_YEAR,
                                     S1.L2_CEG_CODE,
                                     S1.L2_CEG_CN_NAME,
                                     S1.LEVEL_TYPE,
                                     S1.'||V_COLUMN;
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
  
    --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入领域标识为：'||F_CALIBER_FLAG||'，VERSION_ID= '||V_VERSION_ID ||' 的L2层级的数据到'||V_TMP_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS'); 

 -- 删除年度涨跌幅表同版本的数据
 V_SQL := '
 DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||V_SQL_CONDITION;
 EXECUTE IMMEDIATE V_SQL;
 
 --8.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除领域标识为：'||F_CALIBER_FLAG||'，VERSION_ID= '||V_VERSION_ID ||' 的年度涨跌幅('||V_TO_TABLE||')数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
 -- 插入所有数据到年度涨跌幅表
 V_SQL := '
 INSERT INTO '||V_TO_TABLE||'(
                              VERSION_ID,
                              PERIOD_YEAR,
                              GROUP_CODE,
                              GROUP_CN_NAME,
                              GROUP_LEVEL,
                              ANNUAL_AMP,
                              PARENT_CODE,
                              '||V_PARENT_NAME||'
                              ITEM_CODE,
                              ITEM_NAME,
                              CATEGORY_CODE,
                              CATEGORY_NAME,
                              L4_CEG_CODE,
                              L4_CEG_SHORT_CN_NAME,
                              L3_CEG_CODE,
                              L3_CEG_SHORT_CN_NAME,
                              L2_CEG_CODE,
                              L2_CEG_CN_NAME,
                              '||V_CALIBER||'
                              CREATED_BY,
                              CREATION_DATE,
                              LAST_UPDATED_BY,
                              LAST_UPDATE_DATE,
                              DEL_FLAG,
                              '||V_COLUMN||',
                              LEVEL_TYPE
                                ) 
     -- 各层级的涨跌幅数据插入年度涨跌幅表   
     SELECT 
            '||V_VERSION_ID||' AS VERSION_ID,
            T1.PERIOD_YEAR,
            T1.GROUP_CODE,
            T1.GROUP_CN_NAME,
            T1.GROUP_LEVEL,
            NVL(T1.ANNUAL_AMP, 0) AS ANNUAL_AMP,  -- 没有涨跌幅直接赋0
            T1.PARENT_CODE,
            '||V_PARENT_NAME||'
            T1.ITEM_CODE,
            T1.ITEM_CN_NAME,
            T1.CATEGORY_CODE,
            T1.CATEGORY_CN_NAME,
            T1.L4_CEG_CODE,
            T1.L4_CEG_SHORT_CN_NAME,
            T1.L3_CEG_CODE,
            T1.L3_CEG_SHORT_CN_NAME,
            T1.L2_CEG_CODE,
            T1.L2_CEG_CN_NAME,
            '||V_IN_CALIBER||'
            -1 AS CREATED_BY,
            CURRENT_TIMESTAMP AS CREATION_DATE,
            -1 AS LAST_UPDATED_BY,
            CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
            ''N'' AS DEL_FLAG,
            T1.'||V_COLUMN||',
            T1.LEVEL_TYPE
       FROM '||V_TMP_TABLE||' T1
      WHERE T1.LEVEL_TYPE = ''YTD'' OR (T1.LEVEL_TYPE = ''YTD_PREDICT'' AND T1.PERIOD_YEAR = YEAR(CURRENT_TIMESTAMP))'; --保留一般年、当年实际数和当年(实际+预测数)

    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;

--9.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入领域标识为：'||F_CALIBER_FLAG||'，VERSION_ID= '||V_VERSION_ID ||' 的数据到年度涨跌幅表('||V_TO_TABLE||')',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');                         
                 
 -- 10.收集统计信息
  V_SQL := 'ANALYZE '||V_TO_TABLE;
  EXECUTE IMMEDIATE V_SQL;
  
  --11.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END$$
/

