-- Name: f_dwl_prod_bom_item_rev_detail_i_quarter_merge; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dwl_prod_bom_item_rev_detail_i_quarter_merge(OUT x_success_flag text)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
创建时间：2023-8-18
创建人  ：李志勇 00808731 
背景描述：收入时点添加主键后小表合并到目标表
参数描述：参数一(p_period)：传入会计期（年月）,改成全量新增，所以不需要会计期参数
		  参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dwl_prod_bom_item_rev_detail_i_quarter_merge();

*/

declare
	v_sp_name varchar(100) := 'fin_dm_opt_foi.f_dwl_prod_bom_item_rev_detail_i_quarter_merge';
	v_tbl_name varchar(100) := 'fin_dm_opt_foi.dwl_prod_bom_item_rev_detail_i  ';     ----收入明细数据添加主键后小表合并后的表
	v_dml_row_count  number default 0 ;
	V_FROM_TABLE varchar(200) := ' fin_dm_opt_foi.dwl_prod_bom_item_rev_detail_i_';
	V_SQL TEXT;
	V_SQL_TAIL TEXT; --tt_202101_202103  V_SQL_TAIL用于标识202103
	V_PERIOD_ID BIGINT;
	V_PERIOD_ID_TAIL  BIGINT;
	V_NUM BIGINT := -31;  --用于标识 202101
	V_STEP_MUM BIGINT := 0;


begin
	x_success_flag := '1';       --1表示成功
	

	 --写日志,开始
	insert into fin_dm_opt_foi.dm_foi_log_t
		(log_id,
		 version_id,
		 sp_name,
		 para_list,
		 step_num,
		 cal_log_desc,
		 formula_sql_txt,
		 dml_row_count	,
		 result_status,
		 errbuf,
		 created_by,
		 creation_date)
	values
		(fin_dm_opt_foi.dm_foi_log_s.nextval,
		 null,
		 v_sp_name,
		 '',
		 1,                                             --第一步
		 '收入数据添加主键后小表合并到'||v_tbl_name||'：开始运行',
		 null,
		 v_dml_row_count,
		 x_success_flag,
		 null,
		 1,
		 current_timestamp);


		---支持重跑，清除目标表要插入会计期的数据
-- 		delete from fin_dm_opt_foi.DWL_PROD_BOM_ITEM_REV_DETAIL_TEMP 
-- 		where period_id in (select distinct period_id from fin_dm_opt_foi.DWL_PROD_BOM_ITEM_SHIP_DTL_I_TEMP);    ---temp表的日期 
			
		FOR NUM_FLAG IN 0 .. 9 LOOP
			V_SQL := 'SELECT TO_CHAR(ADD_MONTHS(CURRENT_DATE,'||V_NUM||'),''YYYYMM'')';  --ADD_MONTHS 增加
			V_SQL_TAIL := 'SELECT TO_CHAR(ADD_MONTHS(CURRENT_DATE,'||V_NUM + 2||'),''YYYYMM'')';  --ADD_MONTHS 增加
			EXECUTE IMMEDIATE V_SQL INTO V_PERIOD_ID; 
			EXECUTE IMMEDIATE V_SQL_TAIL INTO V_PERIOD_ID_TAIL; 
			
			
		---插入目标表数据
--		V_SQL := '
--		insert into fin_dm_opt_foi.DWL_PROD_BOM_ITEM_REV_DETAIL_I         
--		(
--			period_id ,
--			item_code ,
--			item_cn_name ,
--			quantity ,
--			rmb_item_unit_cost_amt ,
--			per_unit_qty, 
--			parentpartnumber ,
--			parent_quantity ,
--			rmb_fact_rate_gc_amt ,
--			non_child_flag ,
--			level_rel ,
--			non_sale_flag ,
--			consignment_type_code ,
--			consignment_type ,
--			model_num ,
--			item_subtype_code ,
--			item_subtype_cn_name ,
--			prod_key ,
--			geo_pc_key ,
--			sign_cust_key ,
--			end_cust_key ,
--			agent_distribution_cust_key ,
--			enterprise_cust_key ,
--			account_dept_cust_key ,
--			cust_key ,
--			contract_key ,
--			hw_contract_num,
--			proj_key ,
--			src_sys_name ,
--			crt_cycle_id ,
--			last_upd_cycle_id, 
--			dw_last_update_date,
--			del_flag,
--			primary_id
--		)
--		select 
--			period_id ,
--			item_code ,
--			item_cn_name ,
--			quantity ,
--			rmb_item_unit_cost_amt ,
--			per_unit_qty, 
--			parentpartnumber ,
--			parent_quantity ,
--			rmb_fact_rate_gc_amt ,
--			non_child_flag ,
--			level_rel ,
--			non_sale_flag ,
--			consignment_type_code ,
--			consignment_type ,
--			model_num ,
--			item_subtype_code ,
--			item_subtype_cn_name ,
--			prod_key ,
--			geo_pc_key ,
--			sign_cust_key ,
--			end_cust_key ,
--			agent_distribution_cust_key ,
--			enterprise_cust_key ,
--			account_dept_cust_key ,
--			cust_key ,
--			contract_key ,
--			hw_contract_num,
--			proj_key ,
--			src_sys_name ,
--			crt_cycle_id ,
--			last_upd_cycle_id, 
--			dw_last_update_date,
--			del_flag,
--			primary_id	
--	   from '||V_FROM_TABLE||V_PERIOD_ID||'_'||V_PERIOD_ID_TAIL;
	   
	   
	   	V_SQL := '
	   	insert into fin_dm_opt_foi.DWL_PROD_BOM_ITEM_REV_DETAIL_I         
	   	select *
	      from '||V_FROM_TABLE||V_PERIOD_ID||'_'||V_PERIOD_ID_TAIL;
	   
		EXECUTE IMMEDIATE V_SQL;
		DBMS_OUTPUT.PUT_LINE(V_SQL);
		
		V_STEP_MUM := V_STEP_MUM + 1;
	PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
	(F_SP_NAME => V_SP_NAME,
		F_STEP_NUM =>  V_STEP_MUM,
		F_CAL_LOG_DESC => '将表'||V_FROM_TABLE||V_PERIOD_ID||'_'||V_PERIOD_ID_TAIL||'的数据插入表中',
		F_DML_ROW_COUNT => SQL%ROWCOUNT,
		F_ERRBUF => 'SUCCESS');  
		
		V_NUM := V_NUM +3 ;
		
	
	END LOOP;

	 -- 写结束日志
	insert into fin_dm_opt_foi.dm_foi_log_t
		(log_id,
		version_id,
		sp_name,
		para_list,
		step_num,
		cal_log_desc,
		formula_sql_txt,
		dml_row_count,
		result_status,
		errbuf,
		created_by,
		creation_date)
	values
		(
		fin_dm_opt_foi.dm_foi_log_s.nextval,
		null,
		v_sp_name,
		'',
		2,                                             --最后一步
		'收入数据添加主键后小表合并到'||v_tbl_name||'：结束运行',
		null,
		v_dml_row_count,
		'1',
		null,
		1,
		current_timestamp);
		

exception
  	when others then

--     perform fin_dm_opt_foi.p_dm_pf_capture_exception(
--       p_log_version_id => null,                 --版本
--       p_log_sp_name => v_sp_name,    --sp名称
--       p_log_para_list => '',--参数
--       p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
--       p_log_formula_sql_txt => sqlerrm,--错误信息
--       p_log_errbuf => sqlstate  --错误编码
--     ) ;

		PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
		(F_SP_NAME => V_SP_NAME, 
		F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
		F_RESULT_STATUS => X_RESULT_STATUS, 
		F_ERRBUF => SQLSTATE||':'||SQLERRM
		);

	x_success_flag := '2001';       --fail表示失败
	
    --收集统计信息
    analyse fin_dm_opt_foi.dwl_prod_bom_item_rev_detail_i;	

end;
$$
/

