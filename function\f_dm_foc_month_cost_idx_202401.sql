-- Name: f_dm_foc_month_cost_idx_202401; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_month_cost_idx_202401(f_dimension_type character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$


/***************************************************************************************************************************************************************
创建时间：2023-03-28
创建人  ：黄心蕊 hwx1187045
修改时间：2023-08-16
修改人  ：黄心蕊 hwx1187045
背景描述：月度分析-指数表数据初始化
参数描述：参数一(F_ITEM_VERSION)：通用版本号
		  参数二(F_DIMENSION_TYPE)：维度入参，入参值为'U'为通用颗粒度，入参值为'U'则为盈利颗粒度
		  参数三(X_SUCCESS_FLAG)：运行状态返回值 ‘1’为成功，‘0’为失败
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_MONTH_COST_IDX('U',''); --通用颗粒度一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_MONTH_COST_IDX('P',''); --盈利颗粒度一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_MONTH_COST_IDX('D',''); --量纲颗粒度一个版本的数据
--202401  量纲修改结果表及来源表名
--20240121 
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                              VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FOC_MONTH_COST_IDX_202401';
  V_VERSION                              BIGINT;
  V_STEP_NUM                             BIGINT := 0; --函数步骤号
  V_BASE_PERIOD_ID                       INT := TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '01');
  V_SQL                                  TEXT; --执行语句
  V_PROD_RND_TEAM_CODE                   TEXT; --字段重量级团队CODE
  V_PROD_RND_TEAM_CN_NAME                TEXT; --字段重量级团队NAME
  V_PROFITS_NAME                         TEXT; --字段盈利颗粒度
  V_LV0_PROD_RND_TEAM_CODE               TEXT; --字段LV0CODE
  V_LV0_PROD_RD_TEAM_CN_NAME             TEXT; --字段LV0NAME
  V_LV1_PROD_RND_TEAM_CODE               TEXT; --字段LV1CODE 
  V_LV1_PROD_RD_TEAM_CN_NAME             TEXT; --字段LV1NAME
  V_LV2_PROD_RND_TEAM_CODE               TEXT; --字段LV2CODE
  V_LV2_PROD_RD_TEAM_CN_NAME             TEXT; --字段LV2NAME
  V_LV3_PROD_RND_TEAM_CODE               TEXT; --字段LV3CODE
  V_LV3_PROD_RD_TEAM_CN_NAME             TEXT; --字段LV3NAME
  V_L1_NAME                              TEXT; --盈利颗粒度L1字段
  V_L2_NAME                              TEXT; --盈利颗粒度L2字段
  V_INSERT_L1_NAME                       TEXT; --盈利颗粒度备份L1字段
  V_INSERT_L2_NAME                       TEXT; --盈利颗粒度备份L2字段
  V_TOP_L3_CEG_CODE                      TEXT; --字段专家团CODE
  V_TOP_L3_CEG_SHORT_CN_NAME             TEXT; --字段专家团NAME
  V_GROUP_CODE                           TEXT; --本层级CODE
  V_GROUP_NAME                           TEXT; --本层级CODE
  V_CHILD_LEVEL                          TEXT; --取数层级所在LEVEL
  V_SQL_LV0_PROD_RND_TEAM_CODE           TEXT; --取数字段LV0CODE
  V_SQL_LV0_PROD_RD_TEAM_CN_NAME         TEXT; --取数字段LV0NAME
  V_SQL_LV1_PROD_RND_TEAM_CODE           TEXT; --取数字段LV1CODE 
  V_SQL_LV1_PROD_RD_TEAM_CN_NAME         TEXT; --取数字段LV1NAME
  V_SQL_LV2_PROD_RND_TEAM_CODE           TEXT; --取数字段LV2CODE
  V_SQL_LV2_PROD_RD_TEAM_CN_NAME         TEXT; --取数字段LV2NAME
  V_SQL_LV3_PROD_RND_TEAM_CODE           TEXT; --取数字段LV3CODE
  V_SQL_LV3_PROD_RD_TEAM_CN_NAME         TEXT; --取数字段LV3NAME
  V_SQL_L1_NAME                          TEXT; --取数盈利颗粒度L1字段
  V_SQL_L2_NAME                          TEXT; --取数盈利颗粒度L2字段
  V_SQL_TOP_L3_CEG_CODE                  TEXT; --取数字段专家团CODE
  V_SQL_TOP_L3_CEG_SHORT_CN_NAME         TEXT; --取数字段专家团NAME
  V_SQL_PROD_RND_TEAM_CODE               TEXT; --本层级重量级团队CODE逻辑
  V_SQL_PROD_RND_TEAM_CN_NAME            TEXT; --本层级重量级团队NAME逻辑
  V_SQL_PROFITS_NAME                     TEXT; --本层级盈利颗粒度取数逻辑
  V_JOIN_PROD_RND_TEAM_CODE              TEXT; --重量级团队关联条件
  V_JOIN_PROFITS_NAME                    TEXT; --盈利颗粒度关联条件
  V_JOIN_L1NAME                          TEXT; --盈利颗粒度L1NAME关联条件
  V_JOIN_L2NAME                          TEXT; --盈利颗粒度L2NAME关联条件
  V_SQL_PARENT_CODE                      TEXT; --上层级取数逻辑
  V_GROUP_LEVEL                          TEXT; --本层级所在LEVEL值
  V_LEVEL_NUM                            NUMERIC; --循环层级
  V_MID_TABLE                            VARCHAR(200); --中间表名称
  V_WEIGHT_TABLE                         VARCHAR(200); --权重取数表名称
  V_TARGET_TABLE                         VARCHAR(200); --结果表名称
  V_DIMENSION_TYPE                       VARCHAR(50) := F_DIMENSION_TYPE;
  V_INSERT_PROFITS_NAME                  VARCHAR(200); --查询盈利颗粒度字段
  V_SEQUENCE                             VARCHAR(50); --序列作为结果表主键
  V_TOP_L4_CEG_CODE                      TEXT; --字段模块CODE
  V_TOP_L4_CEG_SHORT_CN_NAME             TEXT; --字段模块NAME
  V_SQL_TOP_L4_CEG_CODE                  TEXT; --取数字段模块CODE
  V_SQL_TOP_L4_CEG_SHORT_CN_NAME         TEXT; --取数字段模块NAME
  V_DIMENSION_CODE                       TEXT; --字段量纲CODE
  V_DIMENSION_CN_NAME                    TEXT; --字段量纲NAME
  V_DIMENSION_SUBCATEGORY_CODE           TEXT; --字段量纲子类CODE
  V_DIMENSION_SUBCATEGORY_CN_NAME        TEXT; --字段量纲子类NAME
  V_DIMENSION_SUB_DETAIL_CODE            TEXT; --字段量纲子类明细CODE
  V_DIMENSION_SUB_DETAIL_CN_NAME         TEXT; --字段量纲子类明细NAME
  V_DMS_CODE                             TEXT; --字段量纲颗粒度CODE
  V_DMS_CN_NAME                          TEXT; --字段量纲颗粒度NAME
  V_SQL_DIMENSION_CODE                   TEXT; --取数字段量纲CODE
  V_SQL_DIMENSION_CN_NAME                TEXT; --取数字段量纲NAME
  V_SQL_DIMENSION_SUBCATEGORY_CODE       TEXT; --取数字段量纲子类CODE
  V_SQL_DIMENSION_SUBCATEGORY_CN_NAME    TEXT; --取数字段量纲子类NAME
  V_SQL_DIMENSION_SUB_DETAIL_CODE        TEXT; --取数字段量纲子类明细CODE
  V_SQL_DIMENSION_SUB_DETAIL_CN_NAME     TEXT; --取数字段量纲子类明细NAME
  V_SQL_DMS_CODE                         TEXT; --取数字段量纲颗粒度CODE
  V_SQL_DMS_CN_NAME                      TEXT; --取数字段量纲颗粒度NAME
  V_JOIN_DMS_CODE                        TEXT; --量纲颗粒度关联条件
  V_JOIN_DIMENSION_CODE                  TEXT; --量纲CODE关联条件
  V_JOIN_DIMENSION_SUBCATEGORY_CODE      TEXT; --量纲子类CODE关联条件
  V_JOIN_DIMENSION_SUB_DETAIL_CODE       TEXT; --量纲子类明细CODE关联条件
  V_INSERT_DIMENSION_CODE                TEXT; --备份插数字段量纲CODE
  V_INSERT_DIMENSION_CN_NAME             TEXT; --备份插数字段量纲NAME
  V_INSERT_DIMENSION_SUBCATEGORY_CODE    TEXT; --备份插数字段量纲子类CODE
  V_INSERT_DIMENSION_SUBCATEGORY_CN_NAME TEXT; --备份插数字段量纲子类NAME
  V_INSERT_DIMENSION_SUB_DETAIL_CODE     TEXT; --备份插数字段量纲子类明细CODE
  V_INSERT_DIMENSION_SUB_DETAIL_CN_NAME  TEXT; --备份插数字段量纲子类明细NAME
  V_INSERT_DMS_CODE                      TEXT; --备份插数量纲颗粒度CODE
  V_INSERT_DMS_CN_NAME                   TEXT; --备份插数量纲颗粒度NAME
  V_RESERVE_SQL                          TEXT; --反向视角筛选条件

  --202401版本新增SPART层级
  V_SPART_CODE                     	   TEXT; --量纲颗粒度
  V_SPART_CN_NAME                  	   TEXT;
  V_JOIN_SPART_CODE                	   TEXT; --量纲关联条件
  V_SQL_SPART_CODE                 	   TEXT; --量纲查询表字段
  V_SQL_SPART_CN_NAME				   TEXT; --量纲查询表字段
  V_INSERT_SPART_CODE                    TEXT;
  V_INSERT_SPART_CN_NAME                 TEXT;
  
  V_ITEM_SQL						TEXT; --20240120 加入结果表插数语句中
  
BEGIN

  X_SUCCESS_FLAG:='1';
  
  --日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  --版本号入参判断，当入参为空，取TOP规格品清单最新版本号
  IF F_ITEM_VERSION IS NULL THEN
  SELECT VERSION_ID INTO V_VERSION
    FROM FIN_DM_OPT_FOI.DM_FOC_TOP_ITEM_INFO_T
   ORDER BY LAST_UPDATE_DATE DESC
   LIMIT 1;
  --入参不为空，则以入参为版本号
  ELSE 
    V_VERSION := F_ITEM_VERSION;
  END IF;
  

  
  --写入日志
  V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION||'，并删除同版本数据',
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
  /*字段值定义*/
  V_PROD_RND_TEAM_CODE       := 'PROD_RND_TEAM_CODE,';
  V_PROD_RND_TEAM_CN_NAME    := 'PROD_RND_TEAM_CN_NAME,';
  V_LV0_PROD_RND_TEAM_CODE   := 'LV0_PROD_RND_TEAM_CODE,';
  V_LV0_PROD_RD_TEAM_CN_NAME := 'LV0_PROD_RD_TEAM_CN_NAME,';
  V_LV1_PROD_RND_TEAM_CODE   := 'LV1_PROD_RND_TEAM_CODE,';
  V_LV1_PROD_RD_TEAM_CN_NAME := 'LV1_PROD_RD_TEAM_CN_NAME,';
  V_LV2_PROD_RND_TEAM_CODE   := 'LV2_PROD_RND_TEAM_CODE,';
  V_LV2_PROD_RD_TEAM_CN_NAME := 'LV2_PROD_RD_TEAM_CN_NAME,';
  V_LV3_PROD_RND_TEAM_CODE   := 'LV3_PROD_RND_TEAM_CODE,';
  V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
  V_TOP_L3_CEG_CODE          := 'TOP_L3_CEG_CODE,';
  V_TOP_L3_CEG_SHORT_CN_NAME := 'TOP_L3_CEG_SHORT_CN_NAME,';
  V_GROUP_CODE               := 'TOP_CATEGORY_CODE AS PARENT_CODE,';
  V_GROUP_NAME               := 'TOP_CATEGORY_CN_NAME AS PARENT_NAME,';
  V_CHILD_LEVEL              := '''ITEM''';
  V_TOP_L4_CEG_CODE          := 'TOP_L4_CEG_CODE,'; /*九月新增*/
  V_TOP_L4_CEG_SHORT_CN_NAME := 'TOP_L4_CEG_SHORT_CN_NAME,';

  V_SQL_PROD_RND_TEAM_CODE       := 'T1.PROD_RND_TEAM_CODE,';
  V_SQL_PROD_RND_TEAM_CN_NAME    := 'T1.PROD_RND_TEAM_CN_NAME,';
  V_SQL_LV0_PROD_RND_TEAM_CODE   := 'T1.LV0_PROD_RND_TEAM_CODE,';
  V_SQL_LV0_PROD_RD_TEAM_CN_NAME := 'T1.LV0_PROD_RD_TEAM_CN_NAME,';
  V_SQL_LV1_PROD_RND_TEAM_CODE   := 'T1.LV1_PROD_RND_TEAM_CODE,';
  V_SQL_LV1_PROD_RD_TEAM_CN_NAME := 'T1.LV1_PROD_RD_TEAM_CN_NAME,';
  V_SQL_LV2_PROD_RND_TEAM_CODE   := 'T1.LV2_PROD_RND_TEAM_CODE,';
  V_SQL_LV2_PROD_RD_TEAM_CN_NAME := 'T1.LV2_PROD_RD_TEAM_CN_NAME,';
  V_SQL_LV3_PROD_RND_TEAM_CODE   := 'T1.LV3_PROD_RND_TEAM_CODE,';
  V_SQL_LV3_PROD_RD_TEAM_CN_NAME := 'T1.LV3_PROD_RD_TEAM_CN_NAME,';
  V_SQL_TOP_L3_CEG_CODE          := 'T1.TOP_L3_CEG_CODE,';
  V_SQL_TOP_L3_CEG_SHORT_CN_NAME := 'T1.TOP_L3_CEG_SHORT_CN_NAME,';
  V_SQL_TOP_L4_CEG_CODE          := 'T1.TOP_L4_CEG_CODE,'; /*九月新增*/
  V_SQL_TOP_L4_CEG_SHORT_CN_NAME := 'T1.TOP_L4_CEG_SHORT_CN_NAME,';
  V_SQL_PARENT_CODE              := 'T1.TOP_L4_CEG_CODE'; /*九月版本加入模块层级*/
  V_GROUP_LEVEL                  := '''CATEGORY''';
  
  V_ITEM_SQL := ' OR GROUP_LEVEL = ''ITEM''  ';  --20240120 加入结果表插数语句中

  /*条件定义*/
  V_JOIN_PROD_RND_TEAM_CODE := ' AND T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE';
  V_RESERVE_SQL :='';

  IF V_DIMENSION_TYPE = 'U' THEN
    -- 通用颗粒度
    V_MID_TABLE    := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_IDX_T'; --中间表
    V_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MONTH_WEIGHT_T';
    V_TARGET_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MONTH_COST_IDX_T';
    V_SEQUENCE     := 'FIN_DM_OPT_FOI.DM_FOC_MONTH_COST_IDX_S.NEXTVAL';
	V_RESERVE_SQL :=' AND VIEW_FLAG <> ''4'' ';
    V_LEVEL_NUM    := 7; --通用颗粒度6层级，9月加入模块层级
  
  ELSIF V_DIMENSION_TYPE = 'P' THEN
    -- 盈利颗粒度
	/*表定义*/
    V_MID_TABLE                    := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_IDX_T'; --中间表
    V_WEIGHT_TABLE                 := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MONTH_WEIGHT_T';
    V_TARGET_TABLE                 := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MONTH_COST_IDX_T';
    V_SEQUENCE                     := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MONTH_COST_IDX_S.NEXTVAL';
	
    V_LEVEL_NUM                    := 8; --LOOP次数
	
	/*字段值定义*/
    V_L1_NAME                      := 'L1_NAME,';
    V_L2_NAME                      := 'L2_NAME,';
    V_PROFITS_NAME                 := 'PROFITS_NAME,';
	
    V_SQL_L1_NAME                  := 'T1.L1_NAME,';
    V_SQL_L2_NAME                  := 'T1.L2_NAME,';
    V_SQL_PROFITS_NAME             := 'T1.PROFITS_NAME,';
    V_LV3_PROD_RND_TEAM_CODE       := '';
    V_LV3_PROD_RD_TEAM_CN_NAME     := '';
    V_SQL_LV3_PROD_RND_TEAM_CODE   := '';
    V_SQL_LV3_PROD_RD_TEAM_CN_NAME := '';
	
	/*条件定义*/
    V_JOIN_PROFITS_NAME            := ' AND NVL(T1.PROFITS_NAME,2)= NVL(T2.PROFITS_NAME,2)';
    V_JOIN_L1NAME                  := ' AND NVL(T1.L1_NAME,3)= NVL(T2.L1_NAME,3)';
    V_JOIN_L2NAME                  := ' AND NVL(T1.L2_NAME,4)= NVL(T2.L2_NAME,4)';
  
  ELSIF V_DIMENSION_TYPE = 'D' THEN
    -- 量纲颗粒度
	/*表定义*/
    V_MID_TABLE                         := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T'; --中间表 202401无需修改
    V_WEIGHT_TABLE                      := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_WEIGHT_T_202401';	 --20240119 修改结果表及来源表表名
    V_TARGET_TABLE                      := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_COST_IDX_T_202401';
    V_SEQUENCE                          := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_COST_IDX_S.NEXTVAL';

    V_LEVEL_NUM                         := 11; --LOOP次数
	
	/*字段值定义*/
    V_DIMENSION_CODE                    := 'DIMENSION_CODE,';
    V_DIMENSION_CN_NAME                 := 'DIMENSION_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE        := 'DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME     := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE         := 'DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME      := 'DIMENSION_SUB_DETAIL_CN_NAME,';
    V_DMS_CODE                          := 'DMS_CODE,';
    V_DMS_CN_NAME                       := 'DMS_CN_NAME,';
	--202401版本新增SPART层级
	V_SPART_CODE	:='SPART_CODE,'; 
	V_SPART_CN_NAME :='SPART_CN_NAME,';
	
    V_SQL_DIMENSION_CODE                := 'T1.DIMENSION_CODE,';
    V_SQL_DIMENSION_CN_NAME             := 'T1.DIMENSION_CN_NAME,';
    V_SQL_DIMENSION_SUBCATEGORY_CODE    := 'T1.DIMENSION_SUBCATEGORY_CODE,';
    V_SQL_DIMENSION_SUBCATEGORY_CN_NAME := 'T1.DIMENSION_SUBCATEGORY_CN_NAME,';
    V_SQL_DIMENSION_SUB_DETAIL_CODE     := 'T1.DIMENSION_SUB_DETAIL_CODE,';
    V_SQL_DIMENSION_SUB_DETAIL_CN_NAME  := 'T1.DIMENSION_SUB_DETAIL_CN_NAME,';
    V_SQL_DMS_CODE                      := 'T1.DMS_CODE,';
    V_SQL_DMS_CN_NAME                   := 'T1.DMS_CN_NAME,';
	--202401版本新增SPART层级
	V_SQL_SPART_CODE	:='T1.SPART_CODE,'; 
	V_SQL_SPART_CN_NAME :='T1.SPART_CN_NAME,';
	
    /*条件定义*/
    V_JOIN_DIMENSION_CODE             	:= ' AND NVL(T1.DIMENSION_CODE,''D1'') = NVL(T2.DIMENSION_CODE,''D1'') ';
    V_JOIN_DIMENSION_SUBCATEGORY_CODE 	:= ' AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''D2'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''D2'') ';
    V_JOIN_DIMENSION_SUB_DETAIL_CODE  	:= ' AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''D3'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''D3'') ';
    V_JOIN_DMS_CODE                   	:= ' AND NVL(T1.DMS_CODE,''DD'') = NVL(T2.DMS_CODE,''DD'') ';
	--202401版本新增SPART层级
	V_JOIN_SPART_CODE :='AND NVL(T1.SPART_CODE,''D4'') = NVL(T2.SPART_CODE,''D4'') ';
	
  END IF;
  
  --备份插数字段
  V_INSERT_PROFITS_NAME                  := V_PROFITS_NAME;
  V_INSERT_L1_NAME                       := V_L1_NAME;
  V_INSERT_L2_NAME                       := V_L2_NAME;
  V_INSERT_DIMENSION_CODE                := V_DIMENSION_CODE;
  V_INSERT_DIMENSION_CN_NAME             := V_DIMENSION_CN_NAME;
  V_INSERT_DIMENSION_SUBCATEGORY_CODE    := V_DIMENSION_SUBCATEGORY_CODE;
  V_INSERT_DIMENSION_SUBCATEGORY_CN_NAME := V_DIMENSION_SUBCATEGORY_CN_NAME;
  V_INSERT_DIMENSION_SUB_DETAIL_CODE     := V_DIMENSION_SUB_DETAIL_CODE;
  V_INSERT_DIMENSION_SUB_DETAIL_CN_NAME  := V_DIMENSION_SUB_DETAIL_CN_NAME;
  V_INSERT_DMS_CODE                      := V_DMS_CODE;
  V_INSERT_DMS_CN_NAME                   := V_DMS_CN_NAME;
  --202401版本新增SPART层级
  V_INSERT_SPART_CODE	 :=V_SPART_CODE	; 
  V_INSERT_SPART_CN_NAME :=V_SPART_CN_NAME;
  
  
  V_SQL := 'DELETE FROM '||V_TARGET_TABLE||' WHERE VERSION_ID = '||V_VERSION||';';
  --DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;	--删同版本数
  
     --写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '指数表同版本数据删除完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
	F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_ERRBUF => 'SUCCESS'); 
  
  FOR LEVEL_FLAG IN 1 .. V_LEVEL_NUM LOOP

    IF LEVEL_FLAG = 1 THEN
      NULL;
	  
    ELSIF LEVEL_FLAG = 2 THEN/*九月版本加入模块层级*/
	--卷积模块
      V_TOP_L4_CEG_CODE              :='';
      V_TOP_L4_CEG_SHORT_CN_NAME     :='';
      V_SQL_TOP_L4_CEG_CODE          :='';
      V_SQL_TOP_L4_CEG_SHORT_CN_NAME :='';
      V_GROUP_CODE                   := 'TOP_L4_CEG_CODE AS PARENT_CODE,';
      V_GROUP_NAME                   := 'TOP_L4_CEG_SHORT_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  :='''CATEGORY''';
      V_GROUP_LEVEL                  :='''MODL''';
	  V_SQL_PARENT_CODE 			 := 'T1.TOP_L3_CEG_CODE'; 
	  
	  V_ITEM_SQL 					 := '';   --20240120 增加结果表插数筛选条件;
      
    ELSIF LEVEL_FLAG = 3 THEN
	--卷积专家团
      V_TOP_L3_CEG_CODE              :='';
      V_TOP_L3_CEG_SHORT_CN_NAME     :='';
      V_SQL_TOP_L3_CEG_CODE          :='';
      V_SQL_TOP_L3_CEG_SHORT_CN_NAME :='';
      V_GROUP_CODE                   := 'TOP_L3_CEG_CODE AS PARENT_CODE,';
      V_GROUP_NAME                   := 'TOP_L3_CEG_SHORT_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  :='''MODL''';
      V_GROUP_LEVEL                  :='''CEG''';
	  
      IF V_DIMENSION_TYPE = 'U' THEN
        V_SQL_PARENT_CODE := 'T1.PROD_RND_TEAM_CODE'; --通用颗粒，上层级直达重量级团队
      ELSIF V_DIMENSION_TYPE = 'P' THEN
        V_SQL_PARENT_CODE := '
         CASE
           WHEN T1.VIEW_FLAG IN (0, 1, 2) THEN T1.PROD_RND_TEAM_CODE
           ELSE T1.PROFITS_NAME
         END
      '; --盈利颗粒，012视角为重量级团队，34视角为L1L2
	  ELSIF V_DIMENSION_TYPE = 'D' THEN --量纲颗粒度专家团上层级为量纲
	   V_SQL_PARENT_CODE := 'T1.DMS_CODE';
      END IF;
	  
    ELSIF LEVEL_FLAG = 4 AND V_DIMENSION_TYPE = 'U' THEN
      --从专家团层级之上开始分情况处理
      V_LV3_PROD_RND_TEAM_CODE       :='';
      V_LV3_PROD_RD_TEAM_CN_NAME     :='';
      V_SQL_LV3_PROD_RND_TEAM_CODE   :='';
      V_SQL_LV3_PROD_RD_TEAM_CN_NAME :='';
      V_SQL_PROD_RND_TEAM_CODE       := 'T1.PARENT_CODE,'; --从重量级团队维中字段重量级团队都为其本身
      V_SQL_PROD_RND_TEAM_CN_NAME    := 'T1.PARENT_NAME,';
      V_JOIN_PROD_RND_TEAM_CODE      :='';
      V_GROUP_CODE                   := 'PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_NAME                   := 'PROD_RND_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  :='''CEG''';
      V_GROUP_LEVEL                  := 
      'CASE T1.VIEW_FLAG 
            WHEN ''0'' THEN ''ICT''
            WHEN ''1'' THEN ''LV1''
            WHEN ''2'' THEN ''LV2''
            WHEN ''3'' THEN ''LV3''
        END';
        
      V_SQL_PARENT_CODE              := 
      'CASE T1.VIEW_FLAG
            WHEN ''0'' THEN ''''
            WHEN ''1'' THEN T1.LV0_PROD_RND_TEAM_CODE
            WHEN ''2'' THEN T1.LV1_PROD_RND_TEAM_CODE
            WHEN ''3'' THEN T1.LV2_PROD_RND_TEAM_CODE
        END';
      
    ELSIF LEVEL_FLAG = 5 AND V_DIMENSION_TYPE = 'U' THEN
      V_LV2_PROD_RND_TEAM_CODE       :='';
      V_LV2_PROD_RD_TEAM_CN_NAME     :='';
      V_SQL_LV2_PROD_RND_TEAM_CODE   :='';
      V_SQL_LV2_PROD_RD_TEAM_CN_NAME :='';
      V_GROUP_CODE                   := 'LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_NAME                   := 'LV2_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  :='''LV3''';
      V_GROUP_LEVEL                  :='''LV2''';
      V_SQL_PARENT_CODE              := 'T1.LV1_PROD_RND_TEAM_CODE';
      
    ELSIF LEVEL_FLAG = 6 AND V_DIMENSION_TYPE = 'U' THEN
      V_LV1_PROD_RND_TEAM_CODE       :='';
      V_LV1_PROD_RD_TEAM_CN_NAME     :='';
      V_SQL_LV1_PROD_RND_TEAM_CODE   :='';
      V_SQL_LV1_PROD_RD_TEAM_CN_NAME :='';
      V_GROUP_CODE                   := 'LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_NAME                   := 'LV1_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  :='''LV2''';
      V_GROUP_LEVEL                  :='''LV1''';
      V_SQL_PARENT_CODE              := 'T1.LV0_PROD_RND_TEAM_CODE';
      
    ELSIF LEVEL_FLAG = 7 AND V_DIMENSION_TYPE = 'U' THEN
      V_GROUP_CODE      := 'LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_NAME      := 'LV0_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL     :='''LV1''';
      V_GROUP_LEVEL     :='''ICT''';
      V_SQL_PARENT_CODE :='''''';
	
      /*盈利颗粒度卷积*/
    ELSIF LEVEL_FLAG = 4 AND V_DIMENSION_TYPE = 'P' THEN
      V_GROUP_CODE      := 'PARENT_CODE,';
      V_GROUP_NAME      := '
       CASE
         WHEN VIEW_FLAG IN (0, 1, 2) THEN
          PROD_RND_TEAM_CN_NAME
         ELSE
          PROFITS_NAME
       END AS PARENT_NAME,';
      V_CHILD_LEVEL     := '''CEG''';
      V_GROUP_LEVEL     := '
      CASE T1.VIEW_FLAG 
           WHEN ''0'' THEN ''ICT''
           WHEN ''1'' THEN ''LV1''
           WHEN ''2'' THEN ''LV2''
           WHEN ''3'' THEN ''L1''
           WHEN ''4'' THEN ''L2''
       END';
      V_SQL_PARENT_CODE := '
      CASE T1.VIEW_FLAG
           WHEN ''0'' THEN ''''
           WHEN ''1'' THEN T1.LV0_PROD_RND_TEAM_CODE
           WHEN ''2'' THEN T1.LV1_PROD_RND_TEAM_CODE
           WHEN ''3'' THEN T1.LV2_PROD_RND_TEAM_CODE
           WHEN ''4'' THEN T1.L1_NAME
      END';
    
    ELSIF LEVEL_FLAG = 5 AND V_DIMENSION_TYPE = 'P' THEN
      --卷积L1
      V_L2_NAME          := '';
      V_SQL_L2_NAME      := '';
      V_JOIN_L2NAME      := '';
      V_CHILD_LEVEL      := '''L2''';
      V_GROUP_CODE       := 'PARENT_CODE,';
      V_GROUP_NAME       := 'L1_NAME AS PARENT_NAME,';
      V_SQL_PROFITS_NAME := 'T1.PARENT_CODE,';
      V_GROUP_LEVEL      := '''L1''';
      V_SQL_PARENT_CODE  := 'T1.LV2_PROD_RND_TEAM_CODE ';
    
    ELSIF LEVEL_FLAG = 6 AND V_DIMENSION_TYPE = 'P' THEN
      --卷积LV2
      V_L1_NAME                      := '';
      V_SQL_L1_NAME                  := '';
      V_JOIN_L1NAME                  := '';
      V_LV2_PROD_RND_TEAM_CODE       := '';
      V_LV2_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV2_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV2_PROD_RD_TEAM_CN_NAME := '';
	  
      V_PROFITS_NAME                 := '';
      V_GROUP_CODE                   := 'PARENT_CODE,';
      V_GROUP_NAME                   := 'LV2_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''L1''';
      V_SQL_PROFITS_NAME             := '';
      V_GROUP_LEVEL                  := '''LV2''';
      V_SQL_PARENT_CODE              := 'T1.LV1_PROD_RND_TEAM_CODE ';
      V_JOIN_PROFITS_NAME            := '';
    
    ELSIF LEVEL_FLAG = 7 AND V_DIMENSION_TYPE = 'P' THEN
      --卷积LV1
      V_LV1_PROD_RND_TEAM_CODE       := '';
      V_LV1_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV1_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV1_PROD_RD_TEAM_CN_NAME := '';
      V_GROUP_CODE                   := 'PARENT_CODE,';
      V_GROUP_NAME                   := 'LV1_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_PROFITS_NAME                 := '';
      V_CHILD_LEVEL                  := '''LV2''';
      V_SQL_PROFITS_NAME             := '';
      V_SQL_PROD_RND_TEAM_CODE       := 'T1.PARENT_CODE,'; --直取取不到LV1CODE
      V_SQL_PROD_RND_TEAM_CN_NAME    := 'T1.PARENT_NAME,';
      V_GROUP_LEVEL                  := '''LV1''';
      V_SQL_PARENT_CODE              := 'T1.LV0_PROD_RND_TEAM_CODE ';
    
    ELSIF LEVEL_FLAG = 8 AND V_DIMENSION_TYPE = 'P' THEN
      --卷积LV0
      V_LV0_PROD_RND_TEAM_CODE       := '';
      V_LV0_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV0_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV0_PROD_RD_TEAM_CN_NAME := '';
      V_GROUP_CODE                   := 'PARENT_CODE,';
      V_GROUP_NAME                   := 'LV0_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''LV1''';
      V_SQL_PROD_RND_TEAM_CODE       := 'T1.PARENT_CODE,'; --直取取不到LV1CODE
      V_SQL_PROD_RND_TEAM_CN_NAME    := 'T1.PARENT_NAME,';
      V_GROUP_LEVEL                  := '''ICT''';
      V_SQL_PARENT_CODE              := '''''';
	  
      /*量纲颗粒度卷积*/
    ELSIF LEVEL_FLAG = 4 AND V_DIMENSION_TYPE = 'D' THEN
      --从专家团层级之上开始分情况处理
      V_GROUP_CODE      := 'DMS_CODE AS PARENT_CODE,'; --所有视角专家团父层级皆为量纲颗粒度CODE
      V_GROUP_NAME      := 'DMS_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL     := '''CEG''';
      V_GROUP_LEVEL     := '
       CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN ''DIMENSION''
            WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN ''SUBCATEGORY''
			WHEN T1.VIEW_FLAG IN (''2'',''5'',''8'') THEN ''SUB_DETAIL''
            ELSE ''SPART''
        END '; --不同视角专家团父层级值不同，需定义
      V_SQL_PARENT_CODE := '
      CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.PROD_RND_TEAM_CODE
           WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_CODE
		   WHEN T1.VIEW_FLAG IN (''2'',''5'',''8'') THEN T1.DIMENSION_SUBCATEGORY_CODE
           ELSE T1.DIMENSION_SUB_DETAIL_CODE
         END '; --不同视角量纲颗粒度CODE父层级不同，需定义 
				--202401版本新增SPART层级
    
    ELSIF LEVEL_FLAG = 5 AND V_DIMENSION_TYPE = 'D' THEN
      --量纲子类明细卷积 202401版本新增SPART层级 上一次循环中已完成对SPART的卷积 
      V_SPART_CODE        := '';
      V_SPART_CN_NAME     := '';
      V_SQL_SPART_CODE    := '';
      V_SQL_SPART_CN_NAME := '';
      V_JOIN_SPART_CODE   := '';
	  
      V_GROUP_CODE                       := 'DIMENSION_SUB_DETAIL_CODE    AS PARENT_CODE,'; --所有视角专家团父层级皆为量纲颗粒度CODE
      V_GROUP_NAME                       := 'DIMENSION_SUB_DETAIL_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                      := '''SPART''';
      V_GROUP_LEVEL                      := '''SUB_DETAIL''';
      V_SQL_PARENT_CODE                  := ' T1.DIMENSION_SUBCATEGORY_CODE ';
	  V_SQL_DMS_CODE                      := 'T1.DIMENSION_SUB_DETAIL_CODE,';
	  V_SQL_DMS_CN_NAME                   := 'T1.DIMENSION_SUB_DETAIL_CN_NAME,';
	   
	
    ELSIF LEVEL_FLAG = 6 AND V_DIMENSION_TYPE = 'D' THEN
      --量纲子类卷积
      V_DIMENSION_SUB_DETAIL_CODE        := '';
      V_DIMENSION_SUB_DETAIL_CN_NAME     := '';
      V_SQL_DIMENSION_SUB_DETAIL_CODE    := '';
      V_SQL_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_JOIN_DIMENSION_SUB_DETAIL_CODE   := '';
	  
      V_GROUP_CODE                       := 'DIMENSION_SUBCATEGORY_CODE AS PARENT_CODE,'; --所有视角专家团父层级皆为量纲颗粒度CODE
      V_GROUP_NAME                       := 'DIMENSION_SUBCATEGORY_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                      := '''SUB_DETAIL''';
      V_GROUP_LEVEL                      := '''SUBCATEGORY''';
      V_SQL_PARENT_CODE                  := ' T1.DIMENSION_CODE ';
	  V_SQL_DMS_CODE                      := 'T1.DIMENSION_SUBCATEGORY_CODE,';
	  V_SQL_DMS_CN_NAME                   := 'T1.DIMENSION_SUBCATEGORY_CN_NAME,';
    
    ELSIF LEVEL_FLAG = 7 AND V_DIMENSION_TYPE = 'D' THEN
      --量纲卷积
      V_DIMENSION_SUBCATEGORY_CODE        := '';
      V_DIMENSION_SUBCATEGORY_CN_NAME     := '';
      V_SQL_DIMENSION_SUBCATEGORY_CODE    := '';
      V_SQL_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_JOIN_DIMENSION_SUBCATEGORY_CODE   := '';
	  
      V_GROUP_CODE                        := 'DIMENSION_CODE AS PARENT_CODE,'; --所有视角专家团父层级皆为量纲颗粒度CODE
      V_GROUP_NAME                        := 'DIMENSION_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                       := '''SUBCATEGORY''';
      V_GROUP_LEVEL                       := '''DIMENSION'''; --不同视角专家团父层级值不同，需定义  
      V_SQL_PARENT_CODE                   := 'T1.PROD_RND_TEAM_CODE '; --不同视角量纲颗粒度CODE父层级不同，需定义
	  V_SQL_DMS_CODE                      := 'T1.DIMENSION_CODE,';
	  V_SQL_DMS_CN_NAME                   := 'T1.DIMENSION_CN_NAME,';
    
    ELSIF LEVEL_FLAG = 8 AND V_DIMENSION_TYPE = 'D' THEN
      --分视角卷积量纲父级-重量级团队
      V_DIMENSION_CODE               := '';
      V_DIMENSION_CN_NAME            := '';
      V_SQL_DIMENSION_CODE           := '';
      V_SQL_DIMENSION_CN_NAME        := '';
      V_JOIN_DIMENSION_CODE          := '';
      V_DMS_CODE                     := '';
      V_DMS_CN_NAME                  := '';
      V_SQL_DMS_CODE                 := '';
      V_SQL_DMS_CN_NAME              := '';
      V_JOIN_DMS_CODE                := '';
      V_LV3_PROD_RND_TEAM_CODE       := '';
      V_LV3_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV3_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV3_PROD_RD_TEAM_CN_NAME := '';
	  
      V_GROUP_CODE                   := 'PROD_RND_TEAM_CODE AS PARENT_CODE,'; --所有视角专家团父层级皆为量纲颗粒度CODE
      V_GROUP_NAME                   := 'PROD_RND_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''DIMENSION''';
      V_GROUP_LEVEL                  := '
      CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN ''LV1''
           WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN ''LV2''
           ELSE ''LV3''
      END '; --不同视角量纲父级值不同，需定义  202401版本新增9，10，11视角，对应重量级团队分为别，LV1，LV2，LV3
      V_SQL_PARENT_CODE              := '
      CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN T1.LV0_PROD_RND_TEAM_CODE
           WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV1_PROD_RND_TEAM_CODE
           ELSE T1.LV2_PROD_RND_TEAM_CODE
      END ';  --202401版本新增9，10，11视角，对应重量级团队分为别，LV1，LV2，LV3
    
    ELSIF LEVEL_FLAG = 9 AND V_DIMENSION_TYPE = 'D' THEN
      --卷积LV2层级
      V_LV2_PROD_RND_TEAM_CODE       := '';
      V_LV2_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV2_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV2_PROD_RD_TEAM_CN_NAME := '';
	  
      V_GROUP_CODE                   := 'LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,'; --所有视角专家团父层级皆为量纲颗粒度CODE
      V_GROUP_NAME                   := 'LV2_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''LV3''';
      V_GROUP_LEVEL                  := '''LV2''';
      V_SQL_PARENT_CODE              := ' T1.LV1_PROD_RND_TEAM_CODE ';
      V_SQL_PROD_RND_TEAM_CODE       := 'T1.PARENT_CODE,';
      V_SQL_PROD_RND_TEAM_CN_NAME    := 'T1.PARENT_NAME,';
    
    ELSIF LEVEL_FLAG = 10 AND V_DIMENSION_TYPE = 'D' THEN
      --卷积LV1层级
	  
      V_LV1_PROD_RND_TEAM_CODE       := '';
      V_LV1_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV1_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV1_PROD_RD_TEAM_CN_NAME := '';
	  
      V_GROUP_CODE                   := 'LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,'; --所有视角专家团父层级皆为量纲颗粒度CODE
      V_GROUP_NAME                   := 'LV1_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''LV2''';
      V_GROUP_LEVEL                  := '''LV1''';
      V_SQL_PARENT_CODE              := ' T1.LV0_PROD_RND_TEAM_CODE ';
    
    ELSIF LEVEL_FLAG = 11 AND V_DIMENSION_TYPE = 'D' THEN
      --卷积LV0层级
      V_LV0_PROD_RND_TEAM_CODE       := '';
      V_LV0_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV0_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV0_PROD_RD_TEAM_CN_NAME := '';
	  
      V_GROUP_CODE                   := 'LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,'; --所有视角专家团父层级皆为量纲颗粒度CODE
      V_GROUP_NAME                   := 'LV0_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''LV1''';
      V_GROUP_LEVEL                  := '''ICT''';
      V_SQL_PARENT_CODE              := ' '''' ';
    
    END IF;
    
	V_STEP_NUM:=V_STEP_NUM+1;
	
  /*主逻辑定义*/
  V_SQL := '
  WITH BASE_INDEX AS
   (SELECT PERIOD_YEAR,
           PERIOD_ID,
           '||V_PROD_RND_TEAM_CODE ||V_PROD_RND_TEAM_CN_NAME 
			||V_PROFITS_NAME
			||V_DMS_CODE ||V_DMS_CN_NAME
			||V_LV0_PROD_RND_TEAM_CODE ||V_LV0_PROD_RD_TEAM_CN_NAME 
			||V_LV1_PROD_RND_TEAM_CODE ||V_LV1_PROD_RD_TEAM_CN_NAME
			||V_LV2_PROD_RND_TEAM_CODE ||V_LV2_PROD_RD_TEAM_CN_NAME
			||V_LV3_PROD_RND_TEAM_CODE ||V_LV3_PROD_RD_TEAM_CN_NAME
			||V_L1_NAME ||V_L2_NAME
			||V_DIMENSION_CODE||V_DIMENSION_CN_NAME
			||V_DIMENSION_SUBCATEGORY_CODE ||V_DIMENSION_SUBCATEGORY_CN_NAME 
			||V_DIMENSION_SUB_DETAIL_CODE ||V_DIMENSION_SUB_DETAIL_CN_NAME 
			||V_SPART_CODE||V_SPART_CN_NAME
			||V_TOP_L3_CEG_CODE ||V_TOP_L3_CEG_SHORT_CN_NAME
			||V_TOP_L4_CEG_CODE ||V_TOP_L4_CEG_SHORT_CN_NAME
			||V_GROUP_CODE ||V_GROUP_NAME||'  --202401版本新增SPART层级
           GROUP_CODE,
           COST_INDEX,
           VIEW_FLAG,
           SCENARIO_FLAG,
           CALIBER_FLAG,
		   OVERSEA_FLAG,  
		   LV0_PROD_LIST_CODE, 
		   LV0_PROD_LIST_CN_NAME,
		   GROUP_LEVEL
      FROM '||V_MID_TABLE||'
     WHERE 
	 --VERSION_ID = '||V_VERSION||'				--20240121 修改，去除版本号的影响
     --AND
	   UPPER(GROUP_LEVEL) = '||V_CHILD_LEVEL||'
       AND BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||V_RESERVE_SQL||'),
       
  LEV_WEIGHT AS
   (SELECT PROD_RND_TEAM_CODE, 
           '||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
		    ||V_DIMENSION_CODE ||V_DIMENSION_SUBCATEGORY_CODE ||V_DIMENSION_SUB_DETAIL_CODE
			||V_SPART_CODE
			||V_DMS_CODE ||V_DMS_CN_NAME||'  --202401版本新增SPART层级
           GROUP_CODE, 
           WEIGHT_RATE,
           PARENT_CODE, 
           VIEW_FLAG,
           CALIBER_FLAG,
		   OVERSEA_FLAG,  
		   LV0_PROD_LIST_CODE, 
		   LV0_PROD_LIST_CN_NAME,
		   GROUP_LEVEL
      FROM '||V_WEIGHT_TABLE||'
     WHERE VERSION_ID = '||V_VERSION||'
       AND UPPER(GROUP_LEVEL) = '||V_CHILD_LEVEL||V_RESERVE_SQL||' ) 
       
  INSERT INTO '||V_MID_TABLE||'
    (VERSION_ID,
     BASE_PERIOD_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     '||V_LV0_PROD_RND_TEAM_CODE||V_LV0_PROD_RD_TEAM_CN_NAME 
  	  ||V_LV1_PROD_RND_TEAM_CODE||V_LV1_PROD_RD_TEAM_CN_NAME
  	  ||V_LV2_PROD_RND_TEAM_CODE||V_LV2_PROD_RD_TEAM_CN_NAME
  	  ||V_LV3_PROD_RND_TEAM_CODE||V_LV3_PROD_RD_TEAM_CN_NAME
  	  ||V_L1_NAME||V_L2_NAME
	  ||V_DIMENSION_CODE||V_DIMENSION_CN_NAME 
	  ||V_DIMENSION_SUBCATEGORY_CODE ||V_DIMENSION_SUBCATEGORY_CN_NAME 
	  ||V_DIMENSION_SUB_DETAIL_CODE ||V_DIMENSION_SUB_DETAIL_CN_NAME 
	  ||V_SPART_CODE||V_SPART_CN_NAME
  	  ||V_TOP_L3_CEG_CODE||V_TOP_L3_CEG_SHORT_CN_NAME
	  ||V_TOP_L4_CEG_CODE||V_TOP_L4_CEG_SHORT_CN_NAME||'
     VIEW_FLAG,
     PROD_RND_TEAM_CODE,
     PROD_RND_TEAM_CN_NAME,
     '||V_PROFITS_NAME||V_DMS_CODE ||V_DMS_CN_NAME||'
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_INDEX,
     PARENT_CODE,
     SCENARIO_FLAG,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     CALIBER_FLAG,
	 OVERSEA_FLAG,  
	 LV0_PROD_LIST_CODE, 
	 LV0_PROD_LIST_CN_NAME)
  SELECT '||V_STEP_NUM||' AS VERSION_ID,     --控制
         '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
         T1.PERIOD_YEAR,
         T1.PERIOD_ID,
         '||V_SQL_LV0_PROD_RND_TEAM_CODE
          ||V_SQL_LV0_PROD_RD_TEAM_CN_NAME
          ||V_SQL_LV1_PROD_RND_TEAM_CODE
          ||V_SQL_LV1_PROD_RD_TEAM_CN_NAME
          ||V_SQL_LV2_PROD_RND_TEAM_CODE
          ||V_SQL_LV2_PROD_RD_TEAM_CN_NAME
          ||V_SQL_LV3_PROD_RND_TEAM_CODE
          ||V_SQL_LV3_PROD_RD_TEAM_CN_NAME
          ||V_SQL_L1_NAME
          ||V_SQL_L2_NAME
		  ||V_SQL_DIMENSION_CODE||V_SQL_DIMENSION_CN_NAME
		  ||V_SQL_DIMENSION_SUBCATEGORY_CODE ||V_SQL_DIMENSION_SUBCATEGORY_CN_NAME 
		  ||V_SQL_DIMENSION_SUB_DETAIL_CODE ||V_SQL_DIMENSION_SUB_DETAIL_CN_NAME 
		  ||V_SQL_SPART_CODE||V_SQL_SPART_CN_NAME
          ||V_SQL_TOP_L3_CEG_CODE
          ||V_SQL_TOP_L3_CEG_SHORT_CN_NAME
		  ||V_SQL_TOP_L4_CEG_CODE
		  ||V_SQL_TOP_L4_CEG_SHORT_CN_NAME||'  --202401版本新增SPART层级
         T1.VIEW_FLAG,
         '||V_SQL_PROD_RND_TEAM_CODE
          ||V_SQL_PROD_RND_TEAM_CN_NAME
          ||V_SQL_PROFITS_NAME
		  ||V_SQL_DMS_CODE ||V_SQL_DMS_CN_NAME||'
         T1.PARENT_CODE AS GROUP_CODE,
         T1.PARENT_NAME AS GROUP_CN_NAME,
         '||V_GROUP_LEVEL||' AS GROUP_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
         '||V_SQL_PARENT_CODE||' AS PARENT_CODE,
         T1.SCENARIO_FLAG,
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         T1.CALIBER_FLAG,
		 T1.OVERSEA_FLAG,  
		 T1.LV0_PROD_LIST_CODE, 
		 T1.LV0_PROD_LIST_CN_NAME
    FROM BASE_INDEX T1
    JOIN LEV_WEIGHT T2
      ON T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.VIEW_FLAG = T2.VIEW_FLAG
	 AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
	 AND T1.PARENT_CODE = T2.PARENT_CODE
	 AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
	 AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
     '||V_JOIN_PROD_RND_TEAM_CODE|| V_JOIN_PROFITS_NAME|| V_JOIN_L1NAME|| V_JOIN_L2NAME
	  ||V_JOIN_DMS_CODE || V_JOIN_DIMENSION_CODE || V_JOIN_DIMENSION_SUBCATEGORY_CODE ||V_JOIN_DIMENSION_SUB_DETAIL_CODE ||'
     AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
   GROUP BY '||V_SQL_LV0_PROD_RND_TEAM_CODE
             ||V_SQL_LV0_PROD_RD_TEAM_CN_NAME
             ||V_SQL_LV1_PROD_RND_TEAM_CODE
             ||V_SQL_LV1_PROD_RD_TEAM_CN_NAME
             ||V_SQL_LV2_PROD_RND_TEAM_CODE
             ||V_SQL_LV2_PROD_RD_TEAM_CN_NAME
             ||V_SQL_LV3_PROD_RND_TEAM_CODE
             ||V_SQL_LV3_PROD_RD_TEAM_CN_NAME
             ||V_SQL_L1_NAME
             ||V_SQL_L2_NAME
			 ||V_SQL_DIMENSION_CODE||V_SQL_DIMENSION_CN_NAME
			 ||V_SQL_DIMENSION_SUBCATEGORY_CODE ||V_SQL_DIMENSION_SUBCATEGORY_CN_NAME
			 ||V_SQL_DIMENSION_SUB_DETAIL_CODE ||V_SQL_DIMENSION_SUB_DETAIL_CN_NAME
			 ||V_SQL_SPART_CODE||V_SQL_SPART_CN_NAME
             ||V_SQL_TOP_L3_CEG_CODE
             ||V_SQL_TOP_L3_CEG_SHORT_CN_NAME
			 ||V_SQL_TOP_L4_CEG_CODE
			 ||V_SQL_TOP_L4_CEG_SHORT_CN_NAME
             ||V_SQL_PROD_RND_TEAM_CODE
             ||V_SQL_PROD_RND_TEAM_CN_NAME
             ||V_SQL_PROFITS_NAME||V_SQL_DMS_CODE ||V_SQL_DMS_CN_NAME||'  --202401版本新增SPART层级
			 T1.SCENARIO_FLAG,
			 T1.PERIOD_YEAR,
			 T1.PERIOD_ID,
			 T1.PARENT_NAME,
			 T1.PARENT_CODE,
			 T1.VIEW_FLAG,
			 T1.GROUP_LEVEL,
			 T1.CALIBER_FLAG,
			 T1.OVERSEA_FLAG,  
			 T1.LV0_PROD_LIST_CODE, 
			 T1.LV0_PROD_LIST_CN_NAME;';
			
  EXECUTE IMMEDIATE V_SQL;
  
   --写入日志 
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '第'||LEVEL_FLAG||'次循环开始'||V_GROUP_LEVEL||'层级指数收敛完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
	F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_ERRBUF => 'SUCCESS');   

  V_SQL:='
  INSERT INTO '||V_TARGET_TABLE||'
    (--ID,
     VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     BASE_PERIOD_ID,
     '||V_PROD_RND_TEAM_CODE||V_PROD_RND_TEAM_CN_NAME||
      V_INSERT_PROFITS_NAME||V_INSERT_L1_NAME||V_INSERT_L2_NAME||
	  V_INSERT_DIMENSION_CODE|| V_INSERT_DIMENSION_CN_NAME||
	  V_INSERT_DIMENSION_SUBCATEGORY_CODE|| V_INSERT_DIMENSION_SUBCATEGORY_CN_NAME||
	  V_INSERT_DIMENSION_SUB_DETAIL_CODE|| V_INSERT_DIMENSION_SUB_DETAIL_CN_NAME||
	  V_INSERT_SPART_CODE||V_INSERT_SPART_CN_NAME||
	  V_INSERT_DMS_CODE|| V_INSERT_DMS_CN_NAME||'
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_INDEX,
     PARENT_CODE,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     VIEW_FLAG,
     APPEND_FLAG,
     SCENARIO_FLAG,
     CALIBER_FLAG,
	 OVERSEA_FLAG,  
	 LV0_PROD_LIST_CODE, 
	 LV0_PROD_LIST_CN_NAME)
  SELECT -- '||V_SEQUENCE||' AS ID,
         '||V_VERSION||' AS VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         BASE_PERIOD_ID,
         '||V_PROD_RND_TEAM_CODE||V_PROD_RND_TEAM_CN_NAME||
		  V_INSERT_PROFITS_NAME||
		  V_INSERT_L1_NAME||
		  V_INSERT_L2_NAME||
		  V_INSERT_DIMENSION_CODE|| V_INSERT_DIMENSION_CN_NAME||
		  V_INSERT_DIMENSION_SUBCATEGORY_CODE|| V_INSERT_DIMENSION_SUBCATEGORY_CN_NAME||
		  V_INSERT_DIMENSION_SUB_DETAIL_CODE|| V_INSERT_DIMENSION_SUB_DETAIL_CN_NAME||
		  V_INSERT_SPART_CODE||V_INSERT_SPART_CN_NAME||
		  V_INSERT_DMS_CODE|| V_INSERT_DMS_CN_NAME||'
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         COST_INDEX,
         PARENT_CODE,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         VIEW_FLAG,
         APPEND_FLAG,
         SCENARIO_FLAG,
         CALIBER_FLAG,
		 OVERSEA_FLAG,  
		 LV0_PROD_LIST_CODE, 
		 LV0_PROD_LIST_CN_NAME
    FROM '||V_MID_TABLE||'
	WHERE VERSION_ID = '||V_STEP_NUM|| V_ITEM_SQL ||';';
    
  EXECUTE IMMEDIATE V_SQL;	
  
     --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '第'||LEVEL_FLAG||'次循环指数插数完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
	F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_ERRBUF => 'SUCCESS');  
  
  END LOOP;

/*************
  V_SQL := 'DELETE FROM '||V_TARGET_TABLE||' WHERE VERSION_ID = '||V_VERSION||';';
  EXECUTE IMMEDIATE V_SQL;	
*************/
  
 /*
   --写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '指数表同版本数据删除完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
	F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_ERRBUF => 'SUCCESS');  
  */
  
/*************************************************
  V_SQL:='
  INSERT INTO '||V_TARGET_TABLE||'
    (--ID,
     VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     BASE_PERIOD_ID,
     '||V_PROD_RND_TEAM_CODE||V_PROD_RND_TEAM_CN_NAME||
      V_INSERT_PROFITS_NAME||V_INSERT_L1_NAME||V_INSERT_L2_NAME||
	  V_INSERT_DIMENSION_CODE|| V_INSERT_DIMENSION_CN_NAME||
	  V_INSERT_DIMENSION_SUBCATEGORY_CODE|| V_INSERT_DIMENSION_SUBCATEGORY_CN_NAME||
	  V_INSERT_DIMENSION_SUB_DETAIL_CODE|| V_INSERT_DIMENSION_SUB_DETAIL_CN_NAME||
	  V_INSERT_SPART_CODE||V_INSERT_SPART_CN_NAME||
	  V_INSERT_DMS_CODE|| V_INSERT_DMS_CN_NAME||'
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_INDEX,
     PARENT_CODE,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     VIEW_FLAG,
     APPEND_FLAG,
     SCENARIO_FLAG,
     CALIBER_FLAG,
	 OVERSEA_FLAG,  
	 LV0_PROD_LIST_CODE, 
	 LV0_PROD_LIST_CN_NAME)
  SELECT -- '||V_SEQUENCE||' AS ID,
         '||V_VERSION||' AS VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         BASE_PERIOD_ID,
         '||V_PROD_RND_TEAM_CODE||V_PROD_RND_TEAM_CN_NAME||
		  V_INSERT_PROFITS_NAME||
		  V_INSERT_L1_NAME||
		  V_INSERT_L2_NAME||
		  V_INSERT_DIMENSION_CODE|| V_INSERT_DIMENSION_CN_NAME||
		  V_INSERT_DIMENSION_SUBCATEGORY_CODE|| V_INSERT_DIMENSION_SUBCATEGORY_CN_NAME||
		  V_INSERT_DIMENSION_SUB_DETAIL_CODE|| V_INSERT_DIMENSION_SUB_DETAIL_CN_NAME||
		  V_INSERT_SPART_CODE||V_INSERT_SPART_CN_NAME||
		  V_INSERT_DMS_CODE|| V_INSERT_DMS_CN_NAME||'
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         COST_INDEX,
         PARENT_CODE,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         VIEW_FLAG,
         APPEND_FLAG,
         SCENARIO_FLAG,
         CALIBER_FLAG,
		 OVERSEA_FLAG,  
		 LV0_PROD_LIST_CODE, 
		 LV0_PROD_LIST_CN_NAME
    FROM '||V_MID_TABLE||';';
    
  EXECUTE IMMEDIATE V_SQL;	
*************************************************/


 --写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '指数表插数完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
	F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_ERRBUF => 'SUCCESS');  
    
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; 

$$
/

