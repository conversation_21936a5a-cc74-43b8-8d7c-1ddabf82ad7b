-- Name: f_dm_foi_top_cate_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_top_cate_info_t(f_caliber_flag character varying, f_version bigint, f_dim_version bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/***************************************************************************************************************************************************************
最近更新时间:2024年6月21日14点50分
修改人:		黄心蕊
修改内容:	202407版本 新增华东采购
更  新  人 ：杨泽宝 ywx1106160
背景描述：根据历史下单明细表和预测数表生成和数据筛选条件选出4年的TOP品类集合，再关联ICT映射表和专家团维表生成四年的综合TOP品类清单，
根据上游的历史下单明细数据更新，就可以调度函数重新生成一版数据，按版本号全量抽取，支持删除重跑。
参数描述：参数一(f_version)：导入版本号
        参数二(x_success_flag)：运行状态返回值-成功或者失败
        参数三(F_CALIBER_FLAG):入参 I 代表ICT采购，E代表数字能源
        参数四(f_dim_version):映射表的版本号
用例 	： SELECT FIN_DM_OPT_FOI.F_DM_FOI_TOP_CATE_INFO_T('IAS','','');		--IAS一个版本数据
		SELECT FIN_DM_OPT_FOI.F_DM_FOI_TOP_CATE_INFO_T('EAST_CHINA_PQC','','');		--华东采购一个版本数据
****************************************************************************************************************************************************************/
DECLARE 
  v_sp_name             VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOI_TOP_CATE_INFO_T';
  V_STEP_NUM            BIGINT := 0; --步骤号
  v_version_public1     TEXT := NULL;
  v_var_para1           TEXT := NULL;
  v_var_para2           TEXT := NULL;
  v_var_para3           TEXT := NULL;
  v_var_para4           TEXT := NULL;
  v_schedule_public     TEXT := NULL;
  v_schedule_public_bak TEXT := NULL;
  v_part_public         TEXT := NULL;
  v_execute_sql         TEXT := NULL;
  v_catg_version        BIGINT; -- 取出品类分类、品类特征可以取到数据的版本号
  v_dim_version         BIGINT; -- 映射表最新的版本号
  v_save_method         VARCHAR(2); -- 映射表最新版本的保存方式字段值
  v_cate_rule           NUMERIC; -- TOP品类挑选规则的占比
  v_version             BIGINT;
  v_current_version     BIGINT; -- 当前生成的版本号 
  V_CURRENT_FLAG        TEXT := NULL;
  V_PART1_PUBLIC        TEXT := NULL;

  V_SQL            TEXT; --SQL逻辑
  V_ACTUAL_TABLE   VARCHAR(100); -- 来源表1
  V_FCST_TABLE     VARCHAR(100); -- 来源表2
  V_VERSION_TABLE  VARCHAR(100); -- 来源表3
  V_ITEM_CEG_TABLE VARCHAR(100); -- 来源表4
  V_CEG_TABLE      VARCHAR(100); -- 来源表5
  V_TO_TABLE       VARCHAR(100); -- 目标表    
  V_ID             VARCHAR(10); --目标表字段
  V_INSERT_ID      VARCHAR(100); --目标表字段
  V_APPEND_FLAG    VARCHAR(50); --目标表字段
  V_TYPE_TABLE     VARCHAR(50);

  --202407版本 新增华东采购与IAS
  V_CALIBER_FLAG  TEXT; --华东采购IAS目标表加入CALIBER_FLAG字段 区分数据来源
  V_IN_CALIBER    TEXT; --CALIBER_FLAG赋值
  V_DATA_TYPE     TEXT; --IAS版本表取映射表版本号的版本类型值区别于其他入参
  V_IAS_ECPQC_SQL TEXT; --华东采购IAS表新增CALIBER_FLAG字段

begin
    x_success_flag := '1';
    
--通过F_DIMENSION_TYPE传参,确认来源表和目标表
  IF F_CALIBER_FLAG = 'I' THEN
    -- ICT采购
    V_ACTUAL_TABLE   := 'FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_REPORT_T'; --来源表1:历史数下单&到货明细表
    V_FCST_TABLE     := 'FIN_DM_OPT_FOI.DM_FOI_FCST_SUM_T'; --来源表2: 预测汇总表
    V_VERSION_TABLE  := 'FIN_DM_OPT_FOI.DM_FOI_PLAN_VERSION_T'; --来源表3:版本表
    V_ITEM_CEG_TABLE := 'FIN_DM_OPT_FOI.DM_DIM_FOI_ITEM_CATG_MODL_CEG_T'; --来源表4:维度关联表-ITEM层级
    V_CEG_TABLE      := 'FIN_DM_OPT_FOI.DM_DIM_CATG_MODL_CEG_ICT_T'; --来源表5:维度关联表-品类层级
    V_TO_TABLE       := 'FIN_DM_OPT_FOI.DM_FOI_TOP_CATE_INFO_T'; --目标表
    V_DATA_TYPE      := '''dimension''';
  ELSIF F_CALIBER_FLAG = 'E' THEN
    -- 数字能源
    V_ACTUAL_TABLE   := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T'; --来源表1:历史数下单&到货明细表
    V_FCST_TABLE     := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_FCST_SUM_T'; --来源表2     
    V_VERSION_TABLE  := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T'; --来源表3:版本表
    V_ITEM_CEG_TABLE := 'FIN_DM_OPT_FOI.DM_DIM_ITEM_CATG_MODL_CEG_ENERGY_T'; --来源表4 ：维度关联表-ITEM层级
    V_CEG_TABLE      := 'FIN_DM_OPT_FOI.DM_DIM_CATG_MODL_CEG_ENERGY_T'; --来源表5:维度关联表-品类层级
    V_TO_TABLE       := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_CATE_INFO_T'; --目标表
    V_DATA_TYPE      := '''dimension''';
  ELSE
    -- 202407版本 新增华东采购与IAS
    V_ACTUAL_TABLE   := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_HIS_ORD_REC_REPORT_T'; --来源表1:历史数下单&到货明细表
    V_FCST_TABLE     := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_FCST_SUM_T'; --来源表2     
    V_ITEM_CEG_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_VIEW_INFO_T T1
						WHERE GROUP_LEVEL = ''ITEM'' '; --来源表4 : 下拉框维表，用于关联出结果表维度
    V_TO_TABLE       := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_TOP_CATE_INFO_T'; --目标表
    V_CALIBER_FLAG   := 'CALIBER_FLAG,';
    V_IN_CALIBER     := ''''||F_CALIBER_FLAG||''' AS CALIBER_FLAG,';
	V_IAS_ECPQC_SQL	 := ' AND T1.CALIBER_FLAG = '''||F_CALIBER_FLAG||''' ';
  
    IF F_CALIBER_FLAG = 'IAS' THEN
      -- IAS
      V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_PLAN_VERSION_T';
      V_DATA_TYPE     := '''dim'''; --IAS版本表中dimension版本号对应表为ITEM表
      V_CEG_TABLE     := 'FIN_DM_OPT_FOI.DM_DIM_CATG_MODL_CEG_IAS_T'; --来源表5:维度关联表-品类层级
    ELSIF F_CALIBER_FLAG = 'EAST_CHINA_PQC' THEN
      -- 华东采购
      V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ECPQC_PLAN_VERSION_T';
      V_DATA_TYPE     := '''dimension''';
      V_CEG_TABLE     := 'FIN_DM_OPT_FOI.DM_DIM_CATG_MODL_CEG_ECPQC_T'; --来源表5:维度关联表-品类层级
    END IF;
  
  END IF;
  
--日志开始
    perform FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (f_sp_name => V_SP_NAME,
   f_step_num => V_STEP_NUM,
   f_cal_log_desc => V_SP_NAME||'开始执行');
    
-- 提取取版本号SQL公共部分
    v_version_public1 := '    
    FROM
        '||V_VERSION_TABLE||' 
    WHERE
        SUBSTR(version,1,6) = $schedule_sql$ :: TEXT
        AND DEL_FLAG = ''N''
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = ''CATEGORY''
        ';
                 
-- 取出映射表最新的版本号        
  IF F_DIM_VERSION IS NULL THEN 
    V_SQL := 'SELECT VERSION_ID 
    FROM
        '||V_VERSION_TABLE||' 
    WHERE
        creation_date =
         (SELECT MAX(creation_date)
            FROM '||V_VERSION_TABLE||'
           WHERE LOWER(data_type) = '||V_DATA_TYPE||'
             AND LOWER(version_type) IN (''auto'', ''adjust'')
             AND UPPER( del_flag ) = ''N''
             AND status = 1
             )';
    EXECUTE V_SQL INTO V_DIM_VERSION;
    DBMS_OUTPUT.PUT_LINE('5');	
	ELSIF F_CALIBER_FLAG = 'IAS' THEN
    V_SQL := 'SELECT VERSION_ID 
    FROM '||V_VERSION_TABLE||' 
    WHERE creation_date =
         (SELECT MAX(creation_date)
            FROM '||V_VERSION_TABLE||'
           WHERE LOWER(data_type) = '||V_DATA_TYPE||'
             AND LOWER(version_type) IN (''auto'', ''adjust'')
             AND UPPER( del_flag ) = ''N''
             AND status = 1
             )';
    EXECUTE V_SQL INTO V_DIM_VERSION;
  ELSE V_DIM_VERSION := F_DIM_VERSION;
  END IF;

    --写入日志
    V_STEP_NUM := V_STEP_NUM + 1;
    perform FIN_DM_OPT_FOI.f_dm_foi_cal_log_t
    (f_sp_name => v_sp_name,
    f_step_num => V_STEP_NUM,
    f_cal_log_desc => '映射表最新的版本号为：'||V_DIM_VERSION,
    f_result_status => x_success_flag,
    f_errbuf => 'success');

-- 取出映射表最新版本的保存方式字段值(P:页面保存，需要从TOP品类清单表上一版本取值;A:手动后台保存，直取映射表数据)     
  /*  IF F_CALIBER_FLAG = 'I' THEN -- ICT采购
        SELECT
            DISTINCT save_method INTO v_save_method 
        FROM
            FIN_DM_OPT_FOI.DM_DIM_CATG_MODL_CEG_ICT_T --ICT维度关联表
        WHERE
            version_id = v_dim_version;
    ELSIF F_CALIBER_FLAG = 'E' THEN -- 数字能源
        SELECT
            DISTINCT save_method INTO v_save_method 
        FROM
            FIN_DM_OPT_FOI.DM_DIM_CATG_MODL_CEG_ENERGY_T --维度关联表-品类层级
        WHERE
            version_id = v_dim_version;
    ELSE
        NULL;
    END IF; 
	*/
	--202407版本 
	V_SQL := '
  SELECT DISTINCT SAVE_METHOD
    FROM '||V_CEG_TABLE||' /*维度关联表-品类层级*/
   WHERE VERSION_ID = '||V_DIM_VERSION||'; ';
    EXECUTE V_SQL INTO V_SAVE_METHOD;

    --写入日志
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '映射表最新版本的保存方式为'||V_SAVE_METHOD,
    F_RESULT_STATUS => X_SUCCESS_FLAG,
    F_ERRBUF => 'SUCCESS');    

-- 提取品类分类、品类特征2个字段数据插入到临时表（category_type_feature_temp）
--(P:页面保存，需要从TOP品类清单表上一版本取值;A:手动后台保存，直取映射表数据)
    if upper(v_save_method) = 'P' then
        v_type_table := V_CEG_TABLE; /*TOP品类目标表*/    
        v_execute_sql := 'SELECT VERSION_ID FROM '||V_VERSION_TABLE||' 
                            WHERE  del_flag = ''N''
                            AND status = 1
                            AND LOWER(data_type) ='||V_DATA_TYPE||' 
                            AND UPPER(VERSION_TYPE) IN (''AUTO'')
                            ORDER BY LAST_UPDATE_DATE DESC LIMIT 1';     
							
        execute v_execute_sql into v_catg_version;    
        DBMS_OUTPUT.PUT_LINE('6');
    else 
        v_type_table := V_CEG_TABLE;--维度关联表-品类层级
        v_catg_version := v_dim_version;
    end if;

--月度调度的时候，传参为NULL，查询是否存在当前月的版本号,若存在，沿用之前版本号，若不存在，往版本表插入2条新的版本号，作为TOP品类清单和规格品清单的auto版本号，并将新生成的TOP品类版本号作为生成本次数据的版本号
IF F_VERSION IS NULL THEN     
    -- 查询是否存在当前月度的版本，若存在，保存至V_CURRENT_FLAG变量中
    V_VAR_PARA1 := ' SELECT VERSION_ID ';
    V_VAR_PARA2 := 'TO_CHAR(CURRENT_DATE, ''YYYYMM'') ';        
    V_VAR_PARA3 := ' AND UPPER(VERSION_TYPE) = ''AUTO''';
    V_VAR_PARA4 := ' SELECT COUNT(1) ';
    V_EXECUTE_SQL := V_VAR_PARA4 || REPLACE( v_version_public1,'$schedule_sql$',V_VAR_PARA2) || V_VAR_PARA3;
    EXECUTE V_EXECUTE_SQL INTO V_CURRENT_FLAG;    
    DBMS_OUTPUT.PUT_LINE('1');
    
    
    -- 判断，如果当前的版本号已经生成，即沿用之前生成的版本号，如果没有生成，即生成新的版本号
    IF V_CURRENT_FLAG <> 0 THEN
        V_EXECUTE_SQL := V_VAR_PARA1 || REPLACE( v_version_public1,'$schedule_sql$',V_VAR_PARA2) || V_VAR_PARA3;
        EXECUTE V_EXECUTE_SQL INTO V_VERSION;
        DBMS_OUTPUT.PUT_LINE('2');
    ELSE 
    -- 插入版本数据公共部分
    v_schedule_public := '
    INSERT INTO '||V_VERSION_TABLE||'(
    version_id ,
    parent_version_id ,
    version ,
    status ,
    version_type ,
    data_type ,
    created_by ,
    creation_date,
    last_updated_by ,
    last_update_date ,
    del_flag 
    )
    VALUES
    (
    ''$current_version$'',
    ''$current_parent_version$'',
    to_char( sysdate, ''yyyymm'' ) || ''-'' || ''$verion_type$'',
    1,
    ''auto'',
    ''$verion_data_type$'',
    - 1,
    current_timestamp,
    - 1,
    current_timestamp,
    ''N'' 
    );
    ';
    v_schedule_public_bak := v_schedule_public;  -- 备份
    IF F_CALIBER_FLAG = 'I' THEN
    v_current_version := FIN_DM_OPT_FOI.dm_foi_plan_version_s.nextval;
    ELSIF F_CALIBER_FLAG = 'E' THEN
    v_current_version := FIN_DM_OPT_FOI.dm_foi_energy_plan_version_s.nextval;
    ELSIF F_CALIBER_FLAG = 'EAST_CHINA_PQC' THEN	--202407版本 新增华东采购
    v_current_version := FIN_DM_OPT_FOI.dm_foi_ecpqc_plan_version_s.nextval;
    ELSIF F_CALIBER_FLAG = 'IAS' THEN				--202407版本 新增IAS
    v_current_version := FIN_DM_OPT_FOI.dm_foi_ias_plan_version_s.nextval;
    END IF;
    
    -- 插入TOP品类清单版本号到版本表
    v_var_para1 := '';
    v_var_para2 := 'TOP品类-Auto';
    v_var_para3 := 'category';
    v_schedule_public := REPLACE ( v_schedule_public, '$current_version$', v_current_version );
    v_schedule_public := REPLACE ( v_schedule_public, '$current_parent_version$', v_var_para1 );
    v_schedule_public := REPLACE ( v_schedule_public, '$verion_type$', v_var_para2 );
    v_schedule_public := REPLACE ( v_schedule_public, '$verion_data_type$', v_var_para3 );
    v_execute_sql := v_schedule_public;
    EXECUTE v_execute_sql;
    DBMS_OUTPUT.PUT_LINE('3');
    
    -- 取出当前TOP品类清单需要的版本号    
    SELECT
        v_current_version INTO v_version ;
        
    -- 插入规格品清单版本号到版本表
    v_schedule_public := v_schedule_public_bak;  -- 重置sql
    IF F_CALIBER_FLAG = 'I' THEN
    v_var_para1 := FIN_DM_OPT_FOI.dm_foi_plan_version_s.nextval;
    ELSIF F_CALIBER_FLAG = 'E' THEN
    v_var_para1 := FIN_DM_OPT_FOI.dm_foi_energy_plan_version_s.nextval;
    ELSIF F_CALIBER_FLAG = 'EAST_CHINA_PQC' THEN	--202407版本 新增华东采购
    v_var_para1 := FIN_DM_OPT_FOI.dm_foi_ecpqc_plan_version_s.nextval;
    ELSIF F_CALIBER_FLAG = 'IAS' THEN				--202407版本 新增IAS
    v_var_para1 := FIN_DM_OPT_FOI.dm_foi_ias_plan_version_s.nextval;
    END IF;
    v_var_para2 := 'ITEM-Auto';
    v_var_para3 := 'item';
    v_schedule_public := REPLACE ( v_schedule_public, '$current_version$', v_var_para1 );
    v_schedule_public := REPLACE ( v_schedule_public, '$current_parent_version$', v_current_version );
    v_schedule_public := REPLACE ( v_schedule_public, '$verion_type$', v_var_para2 );
    v_schedule_public := REPLACE ( v_schedule_public, '$verion_data_type$', v_var_para3 );
    v_execute_sql := v_schedule_public;
    EXECUTE v_execute_sql;
    DBMS_OUTPUT.PUT_LINE('4');
    END IF;    
--业务在前台对TOP品类清单进行调整时，Java通过传参TOP品类版本号生成本次数据            
ELSE v_version := f_version;
END IF;
 
    --写入日志
    V_STEP_NUM := V_STEP_NUM + 1;
    perform FIN_DM_OPT_FOI.f_dm_foi_cal_log_t
    (f_sp_name => v_sp_name,
    f_step_num => V_STEP_NUM,
    f_cal_log_desc => '当前TOP品类清单需要的版本号为：'||v_version,
    f_result_status => x_success_flag,
    f_errbuf => 'success');    
                         
--创建会话级临时表存放数据
    DROP TABLE IF EXISTS CATEGORY_TYPE_FEATURE_TEMP;
    CREATE TEMPORARY TABLE  IF NOT EXISTS CATEGORY_TYPE_FEATURE_TEMP (
        CATEGORY_CODE VARCHAR(50),
        CATEGORY_NAME VARCHAR(60),
        L3_CEG_SHORT_CN_NAME  VARCHAR(60),
        L3_CEG_CN_NAME VARCHAR(60),
        L4_CEG_SHORT_CN_NAME  VARCHAR(60),
        L4_CEG_CN_NAME VARCHAR(60),
        CATEGORY_TYPE VARCHAR(60),
        CATEGORY_FEATURE VARCHAR(60)
        )
        ON COMMIT PRESERVE ROWS
        DISTRIBUTE BY REPLICATION;
    
    v_part_public := '
                            category_code,
                             category_name, 
                             l3_ceg_short_cn_name,
                             l3_ceg_cn_name,
                             l4_ceg_cn_name,
                             l4_ceg_short_cn_name,
                             category_type, 
                             category_feature
                             ' ;
        v_execute_sql := ' insert into category_type_feature_temp ('||v_part_public||') 
							SELECT '||v_part_public||' 
							FROM '||v_type_table||' where version_id = '||v_catg_version||' ;';                 
    DBMS_OUTPUT.PUT_LINE(V_EXECUTE_SQL);  
        execute v_execute_sql;
        DBMS_OUTPUT.PUT_LINE('7');      
        
    --写入日志
    V_STEP_NUM := V_STEP_NUM + 1;
    perform FIN_DM_OPT_FOI.f_dm_foi_cal_log_t
  (f_sp_name => v_sp_name,
   f_step_num => V_STEP_NUM,
   f_cal_log_desc => '取出对应的值存放在相应的变量,并取到映射表版本号为：'||v_catg_version||'的数据放入临时表中',
   f_result_status => x_success_flag,
   f_errbuf => 'success');    

   -- 取出TOP品类挑选规则的占比
    SELECT 
    TO_NUMBER(VALUE)
    INTO
        v_cate_rule 
    FROM
        FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T 
    WHERE
        ENABLE_FLAG = 'Y' 
        AND UPPER ( PARA_NAME ) = 'TOP_CATE';
   
--删除重跑
    V_SQL := 'DELETE FROM '||V_TO_TABLE||' T1 WHERE T1.VERSION_ID = '|| V_VERSION||V_IAS_ECPQC_SQL||' ';
    EXECUTE IMMEDIATE V_SQL ;
    DBMS_OUTPUT.PUT_LINE('8');
    
--2.写入日志
    V_STEP_NUM := V_STEP_NUM + 1;
    perform FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (f_sp_name => v_sp_name,
   f_step_num => V_STEP_NUM,
   f_cal_log_desc => '清空'||V_TO_TABLE||'表的数据,并取到version_id='||V_VERSION,
   f_dml_row_count => sql%rowcount,
   f_result_status => x_success_flag,
   f_errbuf => 'success');

--插入目标表数据：分为ICT采购和数字能源，两个模块
--ICT采购
IF F_CALIBER_FLAG = 'I' THEN
  /*  V_ID := ' ID, ';
    V_INSERT_ID := ' FIN_DM_OPT_FOI.DM_FOI_TOP_CATE_INFO_S.NEXTVAL AS ID, ';*/
    V_APPEND_FLAG := ' AND APPEND_FLAG = ''N'' ';

    
ELSE -- 数字能源	--202407版本 新增华东采购与IAS
  /*  V_ID := '';
    V_INSERT_ID := '';*/
    V_APPEND_FLAG := '';
    
END IF;
    V_SQL:='    
INSERT INTO '||V_TO_TABLE||'
  ( L2_CEG_CN_NAME,
   L3_CEG_CN_NAME,
   L4_CEG_CN_NAME,
   L3_CEG_SHORT_CN_NAME,
   L4_CEG_SHORT_CN_NAME,
   L2_CEG_CODE,
   L3_CEG_CODE,
   L4_CEG_CODE,
   CATEGORY_CODE,
   CATEGORY_NAME,
   CATEGORY_TYPE,
   CATEGORY_FEATURE,
   VERSION_ID,
   '||V_CALIBER_FLAG||'		--202407版本 新增华东采购与IAS
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG)
  WITH OPT_LIST_TEMP AS
   (
    --2022清单
    SELECT A1.YEAR,
            A1.CATEGORY_CODE,
            A1.CATEGORY_NAME,
            SUM(A1.RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY
      FROM ( --当年实际数的到货总金额
             SELECT T1.YEAR,
                    T1.CATEGORY_CODE,
                    T1.CATEGORY_NAME,
                    SUM(T1.RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY
               FROM '||V_ACTUAL_TABLE||' T1
              WHERE T1.YEAR = YEAR(NOW()) '||V_APPEND_FLAG||'
			  '||V_IAS_ECPQC_SQL||'
              GROUP BY T1.YEAR, T1.CATEGORY_CODE, T1.CATEGORY_NAME
             UNION ALL
             --当年预测数的到货总金额
             SELECT T1.YEAR,
                     T1.CATEGORY_CODE,
                     T1.CATEGORY_NAME,
                     SUM(T1.RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY
               FROM '||V_FCST_TABLE||' T1
              WHERE T1.YEAR = YEAR(NOW())
                AND T1.APPEND_FLAG = ''N'' --N:原始数据
				'||V_IAS_ECPQC_SQL||'
              GROUP BY T1.YEAR, T1.CATEGORY_CODE, T1.CATEGORY_NAME) A1
     GROUP BY A1.YEAR, A1.CATEGORY_CODE, A1.CATEGORY_NAME
    -- 历史3年每年的清单总金额
    UNION ALL
    SELECT T1.YEAR,
            T1.CATEGORY_CODE,
            T1.CATEGORY_NAME,
            SUM(T1.RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY
      FROM '||V_ACTUAL_TABLE||' T1
     WHERE YEAR IN (YEAR(NOW()) - 1, YEAR(NOW()) - 2, YEAR(NOW()) - 3)
			'||V_APPEND_FLAG||'
			'||V_IAS_ECPQC_SQL||'
     GROUP BY YEAR, T1.CATEGORY_CODE, T1.CATEGORY_NAME),
  
  -- 历史3年数据+当年数据，根据每个品类总金额从高到低金额累积
  OPT_AMT_TMP AS
   (SELECT H.YEAR,
           H.CATEGORY_CODE,
           H.CATEGORY_NAME,
           H.RECEIVE_AMT_CNY,
           ROW_NUMBER() OVER(PARTITION BY H.YEAR ORDER BY H.RECEIVE_AMT_CNY DESC) AS NUM, -- 根据总金额从高到低排序
           SUM(RECEIVE_AMT_CNY) OVER(PARTITION BY H.YEAR ORDER BY H.RECEIVE_AMT_CNY DESC) AS SUM_AMT_CNY, -- 按金额从高到低累计总金额
           (SUM(RECEIVE_AMT_CNY) OVER(PARTITION BY H.YEAR)) * '||V_CATE_RULE||' AS MAX_AMT_CNY -- 根据历史3年和当年，分别计算总金额*0.9
      FROM OPT_LIST_TEMP H),
  
  -- 取到累计金额大于总金额*0.9的第一个排序  
  OPT_MIN_ID_TMP AS
   (SELECT YEAR, MIN(NUM) AS NUM1
      FROM OPT_AMT_TMP
     WHERE SUM_AMT_CNY - MAX_AMT_CNY > 0
     GROUP BY YEAR),
  
  -- 取到历史数3年、当年实际数+预测数，满足筛选条件的所有品类
  OPT_ALL_TEMP AS
   (SELECT H.CATEGORY_CODE,
           H.CATEGORY_NAME,
           SUM(H.RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY
      FROM (
            -- 2022年所有TOP品类
            SELECT A1.CATEGORY_CODE, A1.CATEGORY_NAME, A1.RECEIVE_AMT_CNY
              FROM OPT_AMT_TMP A1
              LEFT JOIN OPT_MIN_ID_TMP T1
                ON A1.YEAR = T1.YEAR
             WHERE A1.NUM <= T1.NUM1
               AND A1.YEAR IN (YEAR(NOW()),
                               YEAR(NOW()) - 1,
                               YEAR(NOW()) - 2,
                               YEAR(NOW()) - 3)) H
     GROUP BY H.CATEGORY_CODE, H.CATEGORY_NAME)
  
  SELECT C.L2_CEG_CN_NAME,
         C.L3_CEG_CN_NAME,
         C.L4_CEG_CN_NAME,
         C.L3_CEG_SHORT_CN_NAME,
         C.L4_CEG_SHORT_CN_NAME,
         C.L2_CEG_CODE,
         C.L3_CEG_CODE,
         C.L4_CEG_CODE,
         T.CATEGORY_CODE,
         T.CATEGORY_NAME,
         ICT.CATEGORY_TYPE,
         ICT.CATEGORY_FEATURE,
         '||V_VERSION||' AS VERSION_ID,
		 '||V_IN_CALIBER||'		--202407版本 新增华东采购与IAS
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
    FROM OPT_ALL_TEMP T
    LEFT JOIN CATEGORY_TYPE_FEATURE_TEMP ICT
      ON T.CATEGORY_CODE = ICT.CATEGORY_CODE
    LEFT JOIN (SELECT DISTINCT L2_CEG_CN_NAME,
                               L3_CEG_CN_NAME,
                               L4_CEG_CN_NAME,
                               L3_CEG_SHORT_CN_NAME,
                               L4_CEG_SHORT_CN_NAME,
                               L2_CEG_CODE,
                               L3_CEG_CODE,
                               L4_CEG_CODE,
                               CATEGORY_CODE,
                               CATEGORY_NAME
                 FROM '||V_ITEM_CEG_TABLE||V_IAS_ECPQC_SQL||' ) C
      ON T.CATEGORY_CODE = C.CATEGORY_CODE
		' ;
        DBMS_OUTPUT.PUT_LINE(V_SQL);    
        EXECUTE IMMEDIATE V_SQL;
        DBMS_OUTPUT.PUT_LINE('9');        

--3.写入日志
    V_STEP_NUM := V_STEP_NUM + 1;
  perform FIN_DM_OPT_FOI.f_dm_foi_cal_log_t
  (f_sp_name => v_sp_name,
   f_step_num => V_STEP_NUM,
   f_cal_log_desc => '插入TOP品类清单数据到 '||V_TO_TABLE||'表',
   f_dml_row_count => sql%rowcount,
   f_result_status => x_success_flag,
   f_errbuf => 'success');
   
--4.收集统计信息
  V_SQL :='analyze '||V_TO_TABLE;
  EXECUTE IMMEDIATE V_SQL;  

--5.日志结束
    V_STEP_NUM := V_STEP_NUM + 1;
  perform FIN_DM_OPT_FOI.f_dm_foi_cal_log_t
  (f_sp_name => v_sp_name,
   f_step_num => V_STEP_NUM,
   f_cal_log_desc => v_sp_name||'运行结束，收集'||V_TO_TABLE||'统计信息完成!');
   
   return 'SUCCESS';
    
--处理异常信息
     exception
   when others then
   x_success_flag := 0;
   
   perform FIN_DM_OPT_FOI.f_dm_foi_cal_log_t
   (f_sp_name => v_sp_name, 
    f_cal_log_desc => v_sp_name||'运行失败', 
    f_result_status => x_success_flag, 
    f_errbuf => sqlstate||':'||sqlerrm
    );
END;

$$
/

