-- Name: f_fcst_ict_point_cus_mon_result_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_fcst_ict_point_cus_mon_result_t(f_cost_type character varying, f_granularity_type character varying, f_page_type character varying, f_ytd_flag character varying DEFAULT NULL::character varying, f_custom_id character varying, f_keystr text DEFAULT NULL::text, f_version_id character varying, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
 
/***************************************************************************************************************************************************************
创建时间: 20240928
创建人  : 黄心蕊 hwx1187045
背景描述: 汇总组合结果表计算 权重&指数&同环比&降成本指数
参数描述: 
	参数一:  F_COST_TYPE 成本类型:PSP PSP成本; STD: 标准成本
	参数二:  F_GRANULARITY_TYPE 目录树类型:	IRB:重量级团队目录; INDUS:产业目录;  PROD:销售目录
	参数三： F_PAGE_TYPE 页面类型 'MONTH' 月度页面，'INTERLOCK'勾稽管理页面
	参数四： F_YTD_FLAG 是否月累计 'YTD' 月累计 ,'MON' 月度 
	参数五:  F_CUSTOM_ID 组合ID
	参数六:  F_KEYSTR	密钥
	参数七:  F_VERSION_ID 版本号
	参数八:  X_RESULT_STATUS 运行状态返回值 ‘1’为成功，‘0’为失败

----来源表
--指数表
重量级团队目录		DM_FCST_ICT_PSP_IRB_MON_COST_IDX_T
产业目录：			DM_FCST_ICT_PSP_INDUS_MON_COST_IDX_T
销售目录：			DM_FCST_ICT_PSP_PROD_MON_COST_IDX_T
重量级团队目录		DM_FCST_ICT_STD_IRB_MON_COST_IDX_T
产业目录：			DM_FCST_ICT_STD_INDUS_MON_COST_IDX_T
销售目录：			DM_FCST_ICT_STD_PROD_MON_COST_IDX_T

--月累计指数表
重量级团队目录		DM_FCST_ICT_PSP_IRB_YTD_COST_IDX_T
产业目录：			DM_FCST_ICT_PSP_INDUS_YTD_COST_IDX_T
销售目录：			DM_FCST_ICT_PSP_PROD_YTD_COST_IDX_T
重量级团队目录		DM_FCST_ICT_STD_IRB_YTD_COST_IDX_T
产业目录：			DM_FCST_ICT_STD_INDUS_YTD_COST_IDX_T
销售目录：			DM_FCST_ICT_STD_PROD_YTD_COST_IDX_T

--金额表(计算权重)
重量级团队目录		DM_FCST_ICT_PSP_IRB_ANNL_AVG_T		--PSP IRB
产业目录：			DM_FCST_ICT_PSP_INDUS_ANNL_AVG_T	--PSP INDUS
销售目录：			DM_FCST_ICT_PSP_PROD_ANNL_AVG_T		--PSP PROD
重量级团队目录		DM_FCST_ICT_STD_IRB_ANNL_AVG_T		--STD IRB
产业目录：			DM_FCST_ICT_STD_INDUS_ANNL_AVG_T	--STD INDUS
销售目录：			DM_FCST_ICT_STD_PROD_ANNL_AVG_T		--STD PROD

--降成本指数表
PSP: DM_FCST_ICT_PSP_MON_COST_RED_IDX_T
STD: DM_FCST_ICT_STD_MON_COST_RED_IDX_T

--维表
PSP:		DM_FCST_ICT_PSP_CUSTOM_COMB_DIM_T
STD:		DM_FCST_ICT_STD_CUSTOM_COMB_DIM_T

----目标表
--权重表
PSP:		DM_FCST_ICT_PSP_CUS_MON_WEIGHT_T
STD:		DM_FCST_ICT_STD_CUS_MON_WEIGHT_T

--指数表
PSP:		DM_FCST_ICT_PSP_CUS_MON_COST_IDX_T
STD:		DM_FCST_ICT_STD_CUS_MON_COST_IDX_T

--同环比表
PSP:		DM_FCST_ICT_PSP_CUS_MON_RATE_T
STD:		DM_FCST_ICT_STD_CUS_MON_RATE_T

--降成本指数表
PSP:		DM_FCST_ICT_PSP_CUS_MON_COST_RED_IDX_T
STD:		DM_FCST_ICT_STD_CUS_MON_COST_RED_IDX_T

--成本分布图表
PSP:		DM_FCST_ICT_PSP_CUS_MON_COST_AMT_T
STD:		DM_FCST_ICT_STD_CUS_MON_COST_AMT_T

SELECT F_FCST_ICT_POINT_CUS_MON_RESULT_T('PSP','IRB','MONTH','','','','');
SELECT F_FCST_ICT_POINT_CUS_MON_RESULT_T('PSP','IRB','INTERLOCK','YTD','','','');

****************************************************************************************************************************************************************/

DECLARE
  V_SP_NAME                 VARCHAR(200) := 'FIN_DM_OPT_FOI.F_FCST_ICT_POINT_CUS_MON_RESULT_T';
  V_VERSION                 BIGINT; --版本号
  V_EXCEPTION_FLAG          INT; --异常步骤
  V_BASE_PERIOD_ID          INT := TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '01');
  V_FROM_YEARS_AMT_TABLE    VARCHAR(200);
  V_FROM_IDX_TABLE          VARCHAR(200);
  V_FROM_DIM_TABLE          VARCHAR(200);
  V_TO_WEIGHT_TABLE         VARCHAR(200);
  V_TO_IDX_TABLE            VARCHAR(200);
  V_TO_MON_IDX_TABLE		VARCHAR(200);
  V_TO_YTD_IDX_TABLE		VARCHAR(200);
  V_TO_RATE_TABLE           VARCHAR(200);
  V_FROM_RED_IDX_TABLE      VARCHAR(200);
  V_TO_RED_IDX_TABLE        VARCHAR(200);
  V_LV4_CODE                TEXT;
  V_LV4_CN_NAME             TEXT;
  V_OTHER_DIM_PART          TEXT;
  V_YEAR3                   TEXT;
  V_YEAR2                   TEXT;
  V_YEAR1                   TEXT;
  V_SQL                     TEXT;
  V_CUSTOM_ID_SQL           TEXT;
  V_YTD_FLAG_SQL            TEXT;
  V_PROD_PRAT				TEXT;
  V_FROM_MON_IDX_TABLE      VARCHAR(200);
  V_FROM_YTD_IDX_TABLE      VARCHAR(200);
  V_FROM_MON_AMT_TABLE      VARCHAR(200);
  V_FROM_MON_COST_AMT_TABLE VARCHAR(200);
  V_FROM_MON_YTD_AMT_TABLE  VARCHAR(200);
  V_TO_MON_AMT_TABLE		VARCHAR(200);
  
BEGIN

X_RESULT_STATUS := '1';

 --写入日志
 V_EXCEPTION_FLAG	:= 0;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 0,
  F_CAL_LOG_DESC => '开始执行',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
--定义变量 表 基期
  V_FROM_YEARS_AMT_TABLE	:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_'||F_GRANULARITY_TYPE||'_ANNL_AVG_T';	
  V_FROM_MON_IDX_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_'||F_GRANULARITY_TYPE||'_MON_COST_IDX_T';
  V_FROM_DIM_TABLE			:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_CUSTOM_COMB_DIM_T';
  V_FROM_RED_IDX_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_MON_COST_RED_IDX_T';
  V_FROM_YTD_IDX_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_'||F_GRANULARITY_TYPE||'_YTD_COST_IDX_T';	--月累计指数表
  V_FROM_MON_COST_AMT_TABLE	:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_'||F_GRANULARITY_TYPE||'_MON_COST_AMT_T';	--月度成本分布图表
  V_FROM_MON_YTD_AMT_TABLE 	:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_'||F_GRANULARITY_TYPE||'_MON_YTD_AMT_T';	--月度成本分布图表
  
  V_TO_WEIGHT_TABLE			:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_CUS_MON_WEIGHT_T';
  V_TO_RATE_TABLE			:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_CUS_MON_RATE_T';
  V_TO_RED_IDX_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_CUS_MON_COST_RED_IDX_T';
  V_TO_MON_IDX_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_CUS_MON_COST_IDX_T';
  V_TO_YTD_IDX_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_CUS_YTD_COST_IDX_T';
  V_TO_MON_AMT_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_CUS_MON_COST_AMT_T';	--汇总组合 
  
  V_YEAR3 := TO_CHAR((YEAR(CURRENT_DATE) - 3) || '-' ||(YEAR(CURRENT_DATE) - 2));
  V_YEAR2 := TO_CHAR((YEAR(CURRENT_DATE) - 2) || '-' ||(YEAR(CURRENT_DATE) - 1));
  V_YEAR1 := TO_CHAR((YEAR(CURRENT_DATE) - 1) || '-' || YEAR(CURRENT_DATE));

  IF F_GRANULARITY_TYPE = 'IRB' THEN
    V_LV4_CODE    := 'LV4_PROD_RND_TEAM_CODE';
    V_LV4_CN_NAME := 'LV4_PROD_RD_TEAM_CN_NAME';
    V_PROD_PRAT   := 'PROD_RND_TEAM_CODE,PROD_RD_TEAM_CN_NAME,';
  
  ELSIF F_GRANULARITY_TYPE = 'INDUS' THEN
    V_LV4_CODE    := 'LV4_INDUSTRY_CATG_CODE';
    V_LV4_CN_NAME := 'LV4_INDUSTRY_CATG_CN_NAME';
    V_PROD_PRAT   := 'INDUSTRY_CATG_CODE,INDUSTRY_CATG_CN_NAME,';
  
  ELSIF F_GRANULARITY_TYPE = 'PROD' THEN
    V_LV4_CODE    := 'LV4_PROD_LIST_CODE';
    V_LV4_CN_NAME := 'LV4_PROD_LIST_CN_NAME';
    V_PROD_PRAT   := 'PROD_LIST_CODE,PROD_LIST_CN_NAME,';
  
  END IF;
  
  IF F_CUSTOM_ID IS NULL THEN
    V_CUSTOM_ID_SQL := '';
  ELSIF F_CUSTOM_ID IS NOT NULL THEN
    V_CUSTOM_ID_SQL := ' AND CUSTOM_ID = '||F_CUSTOM_ID||' ';
  END IF;
  
  IF F_PAGE_TYPE = 'INTERLOCK'  THEN
    IF F_YTD_FLAG = 'YTD' THEN
      --勾稽管理页,月累计指数及月累计成本分布图表判断
      V_FROM_IDX_TABLE     := V_FROM_YTD_IDX_TABLE;
      V_FROM_MON_AMT_TABLE := V_FROM_MON_YTD_AMT_TABLE;
      V_TO_IDX_TABLE       := V_TO_YTD_IDX_TABLE;
    ELSIF F_YTD_FLAG = 'MON' THEN
      --勾稽管理页,月度指数及月成本分布图表判断
      V_FROM_IDX_TABLE     := V_FROM_MON_IDX_TABLE;
      V_FROM_MON_AMT_TABLE := V_FROM_MON_COST_AMT_TABLE;
      V_TO_IDX_TABLE       := V_TO_MON_IDX_TABLE;
    END IF;
  ELSIF F_PAGE_TYPE = 'MONTH' THEN
    --月度页面，月度指数及成本分布图表判断
    V_FROM_IDX_TABLE     := V_FROM_MON_IDX_TABLE;
    V_FROM_MON_AMT_TABLE := V_FROM_MON_COST_AMT_TABLE;
    V_TO_IDX_TABLE       := V_TO_MON_IDX_TABLE;
  
  END IF;
  
  
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '取得本次执行数据成本类型：'||F_COST_TYPE||'，目录树：'||F_GRANULARITY_TYPE||'，页面：'||F_PAGE_TYPE||'，YTD_FLAG为'||F_YTD_FLAG||'，版本号：'||V_VERSION||'，汇总组合ID：'||F_CUSTOM_ID,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  

--建临时表，取出本次计算的底层LV4与SPART并落表
DROP TABLE IF EXISTS DM_CUSTOM_DIM_TEMP;
CREATE TEMPORARY TABLE DM_CUSTOM_DIM_TEMP(
	LV4_CODE 	CHARACTER VARYING(50),
	LV4_CN_NAME CHARACTER VARYING(200),
	LV1_CODE 	CHARACTER VARYING(50),
	LV1_CN_NAME CHARACTER VARYING(200),
	LV2_CODE 	CHARACTER VARYING(50),
	LV2_CN_NAME CHARACTER VARYING(200),
	CUSTOM_ID   	CHARACTER VARYING(50),
	CUSTOM_CN_NAME  CHARACTER VARYING(200),
	SPART_CODE CHARACTER VARYING(50),
	SPART_CN_NAME CHARACTER VARYING(10000),
	GROUP_LEVEL  CHARACTER VARYING(200),
	REGION_CODE CHARACTER VARYING(50),
	REGION_CN_NAME CHARACTER VARYING(200),
	REPOFFICE_CODE CHARACTER VARYING(50),
	REPOFFICE_CN_NAME CHARACTER VARYING(200),
	BG_CODE CHARACTER VARYING(50),
	BG_CN_NAME CHARACTER VARYING(200),
	OVERSEA_FLAG CHARACTER VARYING(10),
	MAIN_FLAG CHARACTER VARYING(2),
	CODE_ATTRIBUTES CHARACTER VARYING(20),
	SOFTWARE_MARK CHARACTER VARYING(20)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY REPLICATION;

V_SQL:= '
  INSERT INTO DM_CUSTOM_DIM_TEMP
    (LV1_CODE,
	 LV2_CODE,
	 LV1_CN_NAME,
	 LV2_CN_NAME,
	 LV4_CODE,
     LV4_CN_NAME,
	 CUSTOM_ID,
	 CUSTOM_CN_NAME,
     SPART_CODE,
     SPART_CN_NAME,
	 GROUP_LEVEL,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     BG_CODE,
     BG_CN_NAME,
     OVERSEA_FLAG,
     MAIN_FLAG,
     CODE_ATTRIBUTES,
     SOFTWARE_MARK)
    SELECT LV1_CODE,
           LV2_CODE,
		   LV1_CN_NAME,
		   LV2_CN_NAME,	--降成本目标需求
		   LV4_CODE,
		   LV4_CN_NAME,
           CUSTOM_ID,
		   CUSTOM_CN_NAME,
		   SPART_CODE,
           SPART_CODE AS SPART_CN_NAME,
		   GROUP_LEVEL,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
           MAIN_FLAG,
           CODE_ATTRIBUTES,
           SOFTWARE_MARK
      FROM '||V_FROM_DIM_TABLE||' T1
     WHERE USE_FLAG = ''CALC''
       AND PAGE_FLAG IN (''MONTH'', ''INTERLOCK'',''ALL_MONTH'')
       AND ENABLE_FLAG = ''Y''
       '||V_CUSTOM_ID_SQL;

  EXECUTE V_SQL;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 2;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 2,
  F_CAL_LOG_DESC => '汇总组合ID：'||F_CUSTOM_ID||'，本次计算维度插表完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

--关联出相应SPART金额，并计算权重
--年金额临时表建表
DROP TABLE IF EXISTS DM_ANNL_AVG_TEMP;
CREATE TEMPORARY TABLE DM_ANNL_AVG_TEMP(
 LV4_CODE   		VARCHAR(50),	
 LV4_CN_NAME   		VARCHAR(200), 
 CUSTOM_ID   		VARCHAR(50),	
 CUSTOM_CN_NAME		VARCHAR(200), 
 PERIOD_YEAR		INT,
 SPART_CODE			VARCHAR(50)  ,
 SPART_CN_NAME		VARCHAR(1000) , 
 RMB_COST_AMT			NUMERIC,
 ACTUAL_QTY				NUMERIC,
 REGION_CODE		VARCHAR(50)  ,
 REGION_CN_NAME		VARCHAR(200) , 
 REPOFFICE_CODE		VARCHAR(50)  ,
 REPOFFICE_CN_NAME	VARCHAR(200) , 
 BG_CODE			VARCHAR(50)  ,
 BG_CN_NAME         VARCHAR(200) , 
 OVERSEA_FLAG		VARCHAR(10),
 SOFTWARE_MARK		VARCHAR(20),
 MAIN_FLAG			VARCHAR(1),
 CODE_ATTRIBUTES    VARCHAR(20)     --是否主力编码 
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE,OVERSEA_FLAG);

IF F_PAGE_TYPE = 'MONTH' THEN
--成本分布图金额表建表
DROP TABLE IF EXISTS DM_MONTH_AMT_TEMP;
CREATE TEMPORARY TABLE DM_MONTH_AMT_TEMP(
PERIOD_YEAR			INT,
PERIOD_ID			INT,
CUSTOM_ID			VARCHAR(50),
CUSTOM_CN_NAME      VARCHAR(200),
LV4_CODE			VARCHAR(50)  ,
LV4_CN_NAME			VARCHAR(200) ,
GROUP_CODE			VARCHAR(50)  ,
GROUP_CN_NAME   	VARCHAR(200) ,
GROUP_LEVEL			VARCHAR(50)  ,
RMB_COST_AMT		NUMERIC,
ACTUAL_QTY			NUMERIC,
MAIN_FLAG			VARCHAR(1),
REGION_CODE			VARCHAR(50)  ,
REGION_CN_NAME  	VARCHAR(200) ,
REPOFFICE_CODE		VARCHAR(50)  ,
REPOFFICE_CN_NAME   VARCHAR(200) ,
BG_CODE				VARCHAR(50)  ,
BG_CN_NAME  		VARCHAR(200) ,
SOFTWARE_MARK		VARCHAR(20),
OVERSEA_FLAG		VARCHAR(10)  ,
CODE_ATTRIBUTES     VARCHAR(20)
 )ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(GROUP_CODE,LV4_CODE);

V_SQL:='
  INSERT INTO DM_MONTH_AMT_TEMP
    (PERIOD_YEAR,
     PERIOD_ID,
     CUSTOM_ID,
     CUSTOM_CN_NAME,
	 GROUP_LEVEL,
     LV4_CODE,
     LV4_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     RMB_COST_AMT,
	 ACTUAL_QTY,
     MAIN_FLAG,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     BG_CODE,
     BG_CN_NAME,
     SOFTWARE_MARK,
     OVERSEA_FLAG,
     CODE_ATTRIBUTES)
    SELECT T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T2.CUSTOM_ID,
           T2.CUSTOM_CN_NAME,
		   T2.GROUP_LEVEL,
           T2.LV4_CODE,
           T2.LV4_CN_NAME,
           T1.GROUP_CODE,
           T1.GROUP_CN_NAME,
           T1.RMB_COST_AMT,
		   T1.ACTUAL_QTY,
           T2.MAIN_FLAG,
           T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.SOFTWARE_MARK,
           T1.OVERSEA_FLAG,
           T2.CODE_ATTRIBUTES
      FROM '||V_FROM_MON_AMT_TABLE||' T1
      INNER JOIN DM_CUSTOM_DIM_TEMP T2
        ON T1.PARENT_CODE = T2.LV4_CODE
       AND T1.GROUP_CODE = T2.SPART_CODE
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.VIEW_FLAG = ''PROD_SPART''
       AND T1.GROUP_LEVEL = ''SPART''
       AND T1.VERSION_ID = '||V_VERSION||'
     WHERE T1.VIEW_FLAG = ''PROD_SPART''
       AND T1.GROUP_LEVEL = ''SPART''
       AND T1.VERSION_ID = '||V_VERSION||';';
	   
  EXECUTE V_SQL;

 --写入日志
 V_EXCEPTION_FLAG	:= 3;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 3,
  F_CAL_LOG_DESC => '汇总组合ID：'||F_CUSTOM_ID||'，成本分布图金额基础数插数完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  V_SQL:='
  DELETE FROM '||V_TO_MON_AMT_TABLE||' WHERE VERSION_ID = '||V_VERSION||V_CUSTOM_ID_SQL||' ;
  
  INSERT INTO '||V_TO_MON_AMT_TABLE||'
    (CUSTOM_ID,
     CUSTOM_CN_NAME,
     VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     RMB_COST_AMT,
     MAIN_FLAG,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     BG_CODE,
     BG_CN_NAME,
     OVERSEA_FLAG,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     CODE_ATTRIBUTES,
     ACTUAL_QTY,
     SOFTWARE_MARK,
     GRANULARITY_TYPE,
     YTD_FLAG)
    SELECT CUSTOM_ID,
           CUSTOM_CN_NAME,
           '||V_VERSION||' AS VERSION_ID,
           PERIOD_YEAR,
           PERIOD_ID,
           CUSTOM_ID AS GROUP_CODE,
           CUSTOM_CN_NAME AS GROUP_CN_NAME,
           GROUP_LEVEL,
           SUM(RMB_COST_AMT) AS RMB_COST_AMT,
           MAIN_FLAG,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           CODE_ATTRIBUTES,
           SUM(ACTUAL_QTY) AS ACTUAL_QTY,
           SOFTWARE_MARK,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           '''||F_YTD_FLAG||''' AS YTD_FLAG
      FROM DM_MONTH_AMT_TEMP
	GROUP BY CUSTOM_ID,
	         CUSTOM_CN_NAME,
	         PERIOD_YEAR,
	         PERIOD_ID,
	         GROUP_LEVEL,
	         MAIN_FLAG,
	         REGION_CODE,
	         REGION_CN_NAME,
	         REPOFFICE_CODE,
	         REPOFFICE_CN_NAME,
	         BG_CODE,
	         BG_CN_NAME,
	         OVERSEA_FLAG,
	         CODE_ATTRIBUTES,
	         SOFTWARE_MARK;
			 
			 ';
  EXECUTE V_SQL;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 4;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 4,
  F_CAL_LOG_DESC => '汇总组合ID：'||F_CUSTOM_ID||'，成本分布图卷积完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  END IF;

  IF F_COST_TYPE = 'PSP' THEN
  
  V_SQL:='
    INSERT INTO DM_ANNL_AVG_TEMP
      (LV4_CODE,
       LV4_CN_NAME,
	   CUSTOM_ID,
	   CUSTOM_CN_NAME,
       PERIOD_YEAR,
       SPART_CODE,
       SPART_CN_NAME,
       RMB_COST_AMT,
       REGION_CODE,
       REGION_CN_NAME,
       REPOFFICE_CODE,
       REPOFFICE_CN_NAME,
       BG_CODE,
       BG_CN_NAME,
       OVERSEA_FLAG,
	   SOFTWARE_MARK,
       MAIN_FLAG,
       CODE_ATTRIBUTES)
      SELECT T1.'||V_LV4_CODE||',
             T1.'||V_LV4_CN_NAME||',
			 T2.CUSTOM_ID,
			 T2.CUSTOM_CN_NAME,
             T1.PERIOD_YEAR,
             T1.SPART_CODE,
             T1.SPART_CN_NAME,
             T1.RMB_COST_AMT,
             T1.REGION_CODE,
             T1.REGION_CN_NAME,
             T1.REPOFFICE_CODE,
             T1.REPOFFICE_CN_NAME,
             T1.BG_CODE,
             T1.BG_CN_NAME,
             T1.OVERSEA_FLAG,
			 T1.SOFTWARE_MARK,
             T2.MAIN_FLAG,
             T2.CODE_ATTRIBUTES
        FROM '||V_FROM_YEARS_AMT_TABLE||' T1
        LEFT JOIN DM_CUSTOM_DIM_TEMP T2
          ON T1.'||V_LV4_CODE||' = T2.LV4_CODE
         AND T1.SPART_CODE = T2.SPART_CODE
         AND T1.REGION_CODE = T2.REGION_CODE
         AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
         AND T1.BG_CODE = T2.BG_CODE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
         AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       WHERE T1.VIEW_FLAG = ''PROD_SPART''
         AND (T1.CODE_ATTRIBUTES <> ''全选'' OR T1.CODE_ATTRIBUTES IS NULL)
		 AND T1.YTD_FLAG = ''N''
		 ;';
		 
  EXECUTE V_SQL;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 5;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 5,
  F_CAL_LOG_DESC => '汇总组合ID：'||F_CUSTOM_ID||'，权重金额插数完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
	  
  ELSIF F_COST_TYPE = 'STD' THEN
  
  V_SQL:='
    INSERT INTO DM_ANNL_AVG_TEMP
      (LV4_CODE,
       LV4_CN_NAME,
	   CUSTOM_ID,
	   CUSTOM_CN_NAME,
       PERIOD_YEAR,
       SPART_CODE,
       SPART_CN_NAME,
       RMB_COST_AMT,
       REGION_CODE,
       REGION_CN_NAME,
       REPOFFICE_CODE,
       REPOFFICE_CN_NAME,
       BG_CODE,
       BG_CN_NAME,
       OVERSEA_FLAG,
	   SOFTWARE_MARK,
       MAIN_FLAG,
       CODE_ATTRIBUTES)
      SELECT T1.'||V_LV4_CODE||',
             T1.'||V_LV4_CN_NAME||',
			 T2.CUSTOM_ID,
			 T2.CUSTOM_CN_NAME,
             T1.PERIOD_YEAR,
             T1.SPART_CODE,
             T1.SPART_CN_NAME,
			  TO_NUMBER(GS_DECRYPT(T1.RMB_COST_AMT,
			    '''||F_KEYSTR||''',
			    ''aes128'',
			    ''cbc'',
			    ''sha256'')) AS RMB_COST_AMT,
             T1.REGION_CODE,
             T1.REGION_CN_NAME,
             T1.REPOFFICE_CODE,
             T1.REPOFFICE_CN_NAME,
             T1.BG_CODE,
             T1.BG_CN_NAME,
             T1.OVERSEA_FLAG,
			 T1.SOFTWARE_MARK,
             T2.MAIN_FLAG,
             T2.CODE_ATTRIBUTES
        FROM '||V_FROM_YEARS_AMT_TABLE||' T1
        LEFT JOIN DM_CUSTOM_DIM_TEMP T2
          ON T1.'||V_LV4_CODE||' = T2.LV4_CODE
         AND T1.SPART_CODE = T2.SPART_CODE
         AND T1.REGION_CODE = T2.REGION_CODE
         AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       WHERE T1.VIEW_FLAG = ''PROD_SPART''
         AND (T1.CODE_ATTRIBUTES <> ''全选'' OR T1.CODE_ATTRIBUTES IS NULL)
		 AND T1.YTD_FLAG = ''N'' ;
		 ';
		 
  EXECUTE V_SQL;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 5;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 5,
  F_CAL_LOG_DESC => '汇总组合ID：'||F_CUSTOM_ID||'，权重金额插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  END IF ;

--计算权重
V_SQL:='

DELETE FROM '||V_TO_WEIGHT_TABLE||' WHERE VERSION_ID = '||V_VERSION||V_CUSTOM_ID_SQL||' ;

INSERT INTO '||V_TO_WEIGHT_TABLE||'(
		CUSTOM_ID,
		CUSTOM_CN_NAME,
		VERSION_ID,
		PERIOD_YEAR,
		GROUP_CODE,
		GROUP_CN_NAME,
		WEIGHT_RATE,
		PARENT_CODE,
		PARENT_CN_NAME,
		REGION_CODE,
		REGION_CN_NAME,
		REPOFFICE_CODE,
		REPOFFICE_CN_NAME,
		BG_CODE,
		BG_CN_NAME,
		OVERSEA_FLAG,
		CREATED_BY,
		CREATION_DATE,
		LAST_UPDATED_BY,
		LAST_UPDATE_DATE,
		DEL_FLAG,
		MAIN_FLAG,
		CODE_ATTRIBUTES,
		SOFTWARE_MARK,
		GRANULARITY_TYPE
		)
  SELECT CUSTOM_ID,
		 CUSTOM_CN_NAME,
		 '||V_VERSION||' AS VERSION_ID,
		 '''||V_YEAR3||''' AS PERIOD_YEAR,
		 SPART_CODE AS GROUP_CODE,
         SPART_CN_NAME AS GROUP_CN_NAME,
		 SUM(RMB_COST_AMT) / NULLIF(SUM(SUM(RMB_COST_AMT)) OVER(PARTITION BY CUSTOM_ID), 0) AS WEIGHT_RATE,
         LV4_CODE AS PARENT_CODE,
         LV4_CN_NAME AS PARENT_CN_NAME,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         SOFTWARE_MARK,
         '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
    FROM DM_ANNL_AVG_TEMP
   WHERE PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE) - 3 AND
         YEAR(CURRENT_DATE) - 2
   GROUP BY	LV4_CODE,
			LV4_CN_NAME,
			CUSTOM_ID,
			CUSTOM_CN_NAME,
			SPART_CODE,
			SPART_CN_NAME,
            REGION_CODE,
            REGION_CN_NAME,
            REPOFFICE_CODE,
            REPOFFICE_CN_NAME,
            BG_CODE,
            BG_CN_NAME,
            OVERSEA_FLAG,
			MAIN_FLAG,
			CODE_ATTRIBUTES,
            SOFTWARE_MARK
  UNION ALL
  --T-2到T-1权重
  SELECT CUSTOM_ID,
		 CUSTOM_CN_NAME,
		 '||V_VERSION||' AS VERSION_ID,
		 '''||V_YEAR2||''' AS PERIOD_YEAR,
		 SPART_CODE AS GROUP_CODE,
         SPART_CN_NAME AS GROUP_CN_NAME,
		 SUM(RMB_COST_AMT) / NULLIF(SUM(SUM(RMB_COST_AMT)) OVER(PARTITION BY CUSTOM_ID), 0) AS WEIGHT_RATE,
         LV4_CODE AS PARENT_CODE,
         LV4_CN_NAME AS PARENT_CN_NAME,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         SOFTWARE_MARK,
         '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
    FROM DM_ANNL_AVG_TEMP
   WHERE PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE) - 2 AND
   YEAR(CURRENT_DATE) - 1
   GROUP BY	LV4_CODE,
			LV4_CN_NAME,
			CUSTOM_ID,
			CUSTOM_CN_NAME,
			SPART_CODE,
			SPART_CN_NAME,
            REGION_CODE,
            REGION_CN_NAME,
            REPOFFICE_CODE,
            REPOFFICE_CN_NAME,
            BG_CODE,
            BG_CN_NAME,
            OVERSEA_FLAG,
			MAIN_FLAG,
			CODE_ATTRIBUTES,
            SOFTWARE_MARK
			
  UNION ALL
  --T-1到T权重
  SELECT CUSTOM_ID,
		 CUSTOM_CN_NAME,
		 '||V_VERSION||' AS VERSION_ID,
		 '''||V_YEAR1||''' AS PERIOD_YEAR,
		 SPART_CODE AS GROUP_CODE,
         SPART_CN_NAME AS GROUP_CN_NAME,
		 SUM(RMB_COST_AMT) / NULLIF(SUM(SUM(RMB_COST_AMT)) OVER(PARTITION BY CUSTOM_ID), 0) AS WEIGHT_RATE,
         LV4_CODE AS PARENT_CODE,
         LV4_CN_NAME AS PARENT_CN_NAME,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         SOFTWARE_MARK,
         '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
    FROM DM_ANNL_AVG_TEMP
   WHERE PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE) - 1 AND YEAR(CURRENT_DATE)
   GROUP BY	LV4_CODE,
			LV4_CN_NAME,
			CUSTOM_ID,
			CUSTOM_CN_NAME,
			SPART_CODE,
			SPART_CN_NAME,
            REGION_CODE,
            REGION_CN_NAME,
            REPOFFICE_CODE,
            REPOFFICE_CN_NAME,
            BG_CODE,
            BG_CN_NAME,
            OVERSEA_FLAG,
			MAIN_FLAG,
			CODE_ATTRIBUTES,
            SOFTWARE_MARK;';
			
  EXECUTE V_SQL;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 6;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 6,
  F_CAL_LOG_DESC => '汇总组合ID：'||F_CUSTOM_ID||'，权重插数完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

--关联计算指数

V_SQL:='
DELETE FROM '||V_TO_IDX_TABLE||' WHERE VERSION_ID = '||V_VERSION||V_CUSTOM_ID_SQL||';

INSERT INTO '||V_TO_IDX_TABLE||'
  (CUSTOM_ID,
   CUSTOM_CN_NAME,
   VERSION_ID,
   BASE_PERIOD_ID,
   PERIOD_YEAR,
   PERIOD_ID,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   COST_INDEX,
   REGION_CODE,
   REGION_CN_NAME,
   REPOFFICE_CODE,
   REPOFFICE_CN_NAME,
   BG_CODE,
   BG_CN_NAME,
   OVERSEA_FLAG,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   MAIN_FLAG,
   CODE_ATTRIBUTES,
   SOFTWARE_MARK,
   GRANULARITY_TYPE)
   WITH BASE_IDX AS (
   --SPART指数取数
   SELECT T2.CUSTOM_ID,
		  T2.CUSTOM_CN_NAME,
		  T2.GROUP_LEVEL,
		  T1.PERIOD_ID,
		  T1.GROUP_CODE,
		  T1.GROUP_CN_NAME,
		  T1.COST_INDEX,
		  T1.PARENT_CODE,
		  T1.REGION_CODE,
		  T1.REGION_CN_NAME,
		  T1.REPOFFICE_CODE,
		  T1.REPOFFICE_CN_NAME,
		  T1.BG_CODE,
		  T1.BG_CN_NAME,
		  T1.OVERSEA_FLAG,
		  T1.MAIN_FLAG,
		  T1.CODE_ATTRIBUTES,
		  T1.SOFTWARE_MARK
     FROM '||V_FROM_IDX_TABLE||' T1
	INNER JOIN DM_CUSTOM_DIM_TEMP T2
	   ON T1.PARENT_CODE = T2.LV4_CODE
	  AND T1.GROUP_CODE = T2.SPART_CODE 
	  AND T1.VERSION_ID = '||V_VERSION||' 
	  AND T1.BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'
	  AND T1.GROUP_LEVEL = ''SPART''
	  AND T1.REGION_CODE = T2.REGION_CODE 
	  AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE 
	  AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK 
	  AND T1.BG_CODE = T2.BG_CODE 
	  AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
	  AND NVL(T1.MAIN_FLAG,''MF'') = NVL(T2.MAIN_FLAG,''MF'')
	  AND NVL(T1.CODE_ATTRIBUTES,''CA'') = NVL(T2.CODE_ATTRIBUTES,''CA'')
	WHERE T1.VERSION_ID = '||V_VERSION||' 
	  AND T1.BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'
	  AND T1.GROUP_LEVEL = ''SPART''
),
  BASE_WEIGHT AS (
  --权重取数
  SELECT CUSTOM_ID,
		 GROUP_CODE,
		 GROUP_CN_NAME,
		 WEIGHT_RATE,
		 PARENT_CODE,
		 REGION_CODE,
		 REPOFFICE_CODE,
		 BG_CODE,
		 OVERSEA_FLAG,
		 MAIN_FLAG,
		 CODE_ATTRIBUTES,
		 SOFTWARE_MARK
    FROM '||V_TO_WEIGHT_TABLE||'
	WHERE VERSION_ID = '||V_VERSION||'
	  AND PERIOD_YEAR = '''||V_YEAR1||'''
	  '||V_CUSTOM_ID_SQL||'
  )
  SELECT T1.CUSTOM_ID,
         T1.CUSTOM_CN_NAME,
         '||V_VERSION||' AS VERSION_ID,
		 '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
         LEFT(T1.PERIOD_ID,4) AS PERIOD_YEAR,
         T1.PERIOD_ID,
         T1.CUSTOM_ID AS GROUP_CODE,
         T1.CUSTOM_CN_NAME AS GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         SUM(T1.COST_INDEX*T2.WEIGHT_RATE) AS COST_INDEX ,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME,
         T1.OVERSEA_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         T1.MAIN_FLAG,
         T1.CODE_ATTRIBUTES,
         T1.SOFTWARE_MARK,
         '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
    FROM BASE_IDX T1
	LEFT JOIN BASE_WEIGHT T2
	  ON T1.GROUP_CODE = T2.GROUP_CODE
	 AND T1.PARENT_CODE = T2.PARENT_CODE
	 AND T1.REGION_CODE = T2.REGION_CODE 
	 AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE 
	 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK 
	 AND T1.BG_CODE = T2.BG_CODE 
	 AND T1.CUSTOM_ID = T2.CUSTOM_ID
	 AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
	 AND NVL(T1.MAIN_FLAG,''MF'') = NVL(T2.MAIN_FLAG,''MF'')
	 AND NVL(T1.CODE_ATTRIBUTES,''CA'') = NVL(T2.CODE_ATTRIBUTES,''CA'')
   GROUP BY T1.MAIN_FLAG,
            T1.CODE_ATTRIBUTES,
            T1.SOFTWARE_MARK,
			T1.REGION_CODE,
			T1.REGION_CN_NAME,
			T1.REPOFFICE_CODE,
			T1.REPOFFICE_CN_NAME,
			T1.BG_CODE,
			T1.BG_CN_NAME,
			T1.OVERSEA_FLAG,
			T1.CUSTOM_ID,
            T1.CUSTOM_CN_NAME,
			T1.PERIOD_ID,
			T1.GROUP_LEVEL;';
			
  EXECUTE V_SQL;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 7;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 7,
  F_CAL_LOG_DESC => '汇总组合ID：'||F_CUSTOM_ID||'，指数计算完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
			
	 
--计算同环比
IF F_PAGE_TYPE = 'MONTH' THEN 
V_SQL:='
DELETE FROM '||V_TO_RATE_TABLE||' WHERE VERSION_ID = '||V_VERSION||V_CUSTOM_ID_SQL||';

  WITH LEV_INDEX AS
   (SELECT PERIOD_ID,
           SUBSTR(PERIOD_ID, 5, 2) AS MONTH_DAY,
           CUSTOM_ID,
		   CUSTOM_CN_NAME,
		   GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
		   REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   MAIN_FLAG,
		   SOFTWARE_MARK,		
           CODE_ATTRIBUTES
      FROM '||V_TO_IDX_TABLE||'
     WHERE VERSION_ID = '||V_VERSION||'
       AND BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'
	   '||V_CUSTOM_ID_SQL||'),
  
  BASE_YOY AS
   (SELECT CUSTOM_ID,
		   CUSTOM_CN_NAME,
		   PERIOD_ID,
		   GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
		   REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   MAIN_FLAG,
		   SOFTWARE_MARK,		
           CODE_ATTRIBUTES,
		   LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY CUSTOM_ID,REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG,
														GROUP_CODE, GROUP_LEVEL, MAIN_FLAG,NVL(CODE_ATTRIBUTES,''CA''),
														NVL(SOFTWARE_MARK,''SW'') , 
														MONTH_DAY ORDER BY PERIOD_ID) AS YOY_PERIOD_ID,
           LAG(COST_INDEX, 1, NULL) OVER(PARTITION BY CUSTOM_ID,REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG,
														GROUP_CODE, GROUP_LEVEL, MAIN_FLAG,NVL(CODE_ATTRIBUTES,''CA''),
														NVL(SOFTWARE_MARK,''SW'') ,
														MONTH_DAY ORDER BY PERIOD_ID) AS YOY_COST_INDEX,
           LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY CUSTOM_ID,REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG,
														GROUP_CODE, GROUP_LEVEL, MAIN_FLAG, NVL(CODE_ATTRIBUTES,''CA''),
														NVL(SOFTWARE_MARK,''SW'') 
														ORDER BY PERIOD_ID) AS POP_PERIOD_ID,
           LAG(COST_INDEX, 1, NULL) OVER(PARTITION BY CUSTOM_ID,REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG,
														GROUP_CODE, GROUP_LEVEL, MAIN_FLAG, NVL(CODE_ATTRIBUTES,''CA''),
														NVL(SOFTWARE_MARK,''SW'') 
														ORDER BY PERIOD_ID) AS POP_COST_INDEX
      FROM LEV_INDEX) 
INSERT INTO '||V_TO_RATE_TABLE||'
  (CUSTOM_ID,
   CUSTOM_CN_NAME,
   VERSION_ID,
   PERIOD_YEAR,
   PERIOD_ID,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   RATE,
   RATE_FLAG,
   REGION_CODE,
   REGION_CN_NAME,
   REPOFFICE_CODE,
   REPOFFICE_CN_NAME,
   BG_CODE,
   BG_CN_NAME,
   OVERSEA_FLAG,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   MAIN_FLAG,
   CODE_ATTRIBUTES,
   SOFTWARE_MARK,
   GRANULARITY_TYPE)

  SELECT CUSTOM_ID,
         CUSTOM_CN_NAME,
         '||V_VERSION||' AS VERSION_ID,
         LEFT(PERIOD_ID,4) AS PERIOD_YEAR,
         PERIOD_ID,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ((COST_INDEX / NULLIF(YOY_COST_INDEX, 0)) - 1) AS RATE,
         ''YOY''AS RATE_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N''AS DEL_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         SOFTWARE_MARK,
         '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
    FROM BASE_YOY
   WHERE YOY_COST_INDEX IS NOT NULL
  UNION ALL
  SELECT CUSTOM_ID,
         CUSTOM_CN_NAME,
         '||V_VERSION||' AS VERSION_ID,
         LEFT(PERIOD_ID,4) AS PERIOD_YEAR,
         PERIOD_ID,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ((COST_INDEX / NULLIF(POP_COST_INDEX, 0)) - 1) AS RATE,
         ''POP''AS RATE_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N''AS DEL_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         SOFTWARE_MARK,
         '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
    FROM BASE_YOY
   WHERE POP_COST_INDEX IS NOT NULL;';
   
  EXECUTE V_SQL;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 8;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 8,
  F_CAL_LOG_DESC => '汇总组合ID：'||F_CUSTOM_ID||'，同环比计算完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
END IF;
   

/****************************************************计算降成本指数************************************************/
IF F_GRANULARITY_TYPE = 'IRB' AND F_PAGE_TYPE = 'MONTH' THEN  

-------------------------------计算降成本目标值
--建临时表,降成本目标取值
DROP TABLE IF EXISTS DM_CUSTOM_COST_RED_RATE_TEMP;
CREATE TEMPORARY TABLE DM_CUSTOM_COST_RED_RATE_TEMP(
	CUSTOM_ID   	CHARACTER VARYING(1000),
	COST_REDUCTION_RATE NUMERIC,	--降成本目标值
	COST_REDUCTION_CODE	VARCHAR(50),  --降成本目标编码
	COST_REDUCTION_CN_NAME	VARCHAR(200),  --降成本目标中文名称
	COST_REDUCTION_LEVEL	VARCHAR(10)	--降成本目标层级
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY REPLICATION;
 
  WITH BASE_RATE AS
   ( --降成本目标值
    SELECT LV1_PROD_RD_TEAM_CN_NAME AS LV1_CN_NAME,
            LV2_PROD_RD_TEAM_CN_NAME AS LV2_CN_NAME,
            OBJECTIVE
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_COST_RED_OBJ_T
     WHERE VERSION_ID =
           (SELECT VERSION_ID
              FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
             WHERE DEL_FLAG = 'N'
               AND STATUS = 0
               AND UPPER(DATA_TYPE) = 'RED_DIM'
             ORDER BY LAST_UPDATE_DATE DESC LIMIT 1)
       AND NVL(OBJECTIVE, 9999) <> 9999),
  BASE_DIM AS
   ( --组合维度
    SELECT CUSTOM_ID,
            LV1_CN_NAME,
            LV2_CN_NAME,
            LV1_CODE,
            LV2_CODE,
            COUNT(DISTINCT LV1_CODE) AS LV1_CT --每个组合下LV1数量
      FROM DM_CUSTOM_DIM_TEMP
     GROUP BY CUSTOM_ID, LV1_CODE, LV1_CN_NAME, LV2_CODE, LV2_CN_NAME),
  
  RED_RATE_TEMP AS
   (SELECT T1.CUSTOM_ID,
           T1.LV1_CODE,
           T1.LV1_CN_NAME,
           T1.LV2_CODE,
           T1.LV2_CN_NAME,
           DECODE(T2.LV2_CN_NAME, '合计', 1, 0) AS IS_LV1_FLAG, -- 用于删除后续合计数据
           SUM(DECODE(T2.LV2_CN_NAME, '合计', -9999, NULL, 0, 1)) OVER(PARTITION BY CUSTOM_ID) AS LV2_SUM, --给合计数据特殊值,方便后续判断
           COUNT(DECODE(T2.LV2_CN_NAME, NULL, 1, 1)) OVER(PARTITION BY CUSTOM_ID) AS LV2_CT, --用于判断包含合计在内,每个组合下LV2数量
           SUM(DECODE(T2.LV2_CN_NAME, '合计', T2.OBJECTIVE)) OVER(PARTITION BY CUSTOM_ID) AS LV1_RED_RATE, --将合计值(即LV1值单独成字段,方便后续取数)
           T2.OBJECTIVE AS LV2_RED_RATE --全部LV2降成本目标值
      FROM BASE_DIM T1
      LEFT JOIN BASE_RATE T2
        ON T1.LV1_CN_NAME = T2.LV1_CN_NAME
       AND (T1.LV2_CN_NAME = T2.LV2_CN_NAME OR T2.LV2_CN_NAME = '合计') --降成本目标表LV2_NAME为合计即为LV1值
     WHERE T1.LV1_CT = 1 --排除多LV1组合 多LV1没有单一降成本目标值
     GROUP BY T1.CUSTOM_ID,
              T1.LV1_CODE,
              T1.LV2_CODE,
              T1.LV1_CN_NAME,
              T1.LV2_CN_NAME,
              T2.LV2_CN_NAME,
              T2.OBJECTIVE),
  CUSTOM_RATES_TEMP AS
   (SELECT CUSTOM_ID,
           CASE
             WHEN LV2_SUM >= 0 AND LV2_CT >= 2 THEN
              NULL --单LV1,多LV2,无合计 (包含LV2都为空),降成本指数取空值
             WHEN LV2_SUM < 0 AND LV2_CT > 2 THEN
              LV1_RED_RATE --单LV1,多LV2,有合计,取合计(即LV1值)
             WHEN (-9999 < LV2_SUM AND LV2_SUM < 0) AND LV2_CT = 2 THEN
              LV2_RED_RATE --单LV1,单LV2,LV2不为空且有合计,取LV2值
             WHEN LV2_SUM = (-9999) AND LV2_CT = 2 THEN
              LV1_RED_RATE --单LV1,单LV2,LV2为空,有合计,取合计(即LV1值)
           END AS CUSTOM_RED_RATE,
           CASE
             WHEN LV2_SUM >= 0 AND LV2_CT >= 2 THEN
              NULL
             WHEN LV2_SUM < 0 AND LV2_CT > 2 THEN
              'LV1' --单LV1,多LV2,有合计,取合计(即LV1值)
             WHEN (-9999 < LV2_SUM AND LV2_SUM < 0) AND LV2_CT = 2 THEN
              'LV2' --单LV1,单LV2,LV2不为空且有合计,取LV2值
             WHEN LV2_SUM = -9999 AND LV2_CT = 2 THEN
              'LV1' --单LV1,单LV2,LV2为空,有合计,取合计(即LV1值)
           END AS RED_RATE_LEVEL,
           LV1_CODE,
           LV2_CODE,
           LV1_CN_NAME,
           LV2_CN_NAME
      FROM RED_RATE_TEMP
     WHERE IS_LV1_FLAG <> 1)
  
  INSERT INTO DM_CUSTOM_COST_RED_RATE_TEMP(
  CUSTOM_ID,
  COST_REDUCTION_RATE,
  COST_REDUCTION_CODE,
  COST_REDUCTION_CN_NAME,
  COST_REDUCTION_LEVEL
  )
  --多LV1组合降成本目标为空
  SELECT DISTINCT CUSTOM_ID,
         NULL:: NUMERIC AS COST_REDUCTION_RATE,
         NULL AS COST_REDUCTION_CODE,
         NULL AS COST_REDUCTION_CN_NAME,
         NULL AS COST_REDUCTION_LEVEL
    FROM BASE_DIM
   WHERE LV1_CT > 1
  UNION ALL
  --单LV1降成本目标数据
  SELECT DISTINCT CUSTOM_ID,
				  ((1-CUSTOM_RED_RATE)*100) AS COST_REDUCTION_RATE,
                  DECODE(RED_RATE_LEVEL, 'LV1', LV1_CODE,'LV2',LV2_CODE,NULL) AS COST_REDUCTION_CODE,
                  DECODE(RED_RATE_LEVEL, 'LV1', LV1_CN_NAME,'LV2',LV2_CN_NAME,NULL) AS COST_REDUCTION_CN_NAME,
                  RED_RATE_LEVEL AS COST_REDUCTION_LEVEL
    FROM CUSTOM_RATES_TEMP;

---------计算降成本指数
V_SQL:='
  DELETE FROM '||V_TO_RED_IDX_TABLE||' WHERE VERSION_ID = '||V_VERSION||V_CUSTOM_ID_SQL||' ;

  INSERT INTO '||V_TO_RED_IDX_TABLE||'
    (CUSTOM_ID,
     CUSTOM_CN_NAME,
     VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_INDEX,
     YTD_COST_INDEX,
     COST_REDUCTION_RATE,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     BG_CODE,
     BG_CN_NAME,
     OVERSEA_FLAG,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     MAIN_FLAG,
     CODE_ATTRIBUTES,
     COST_REDUCTION_CODE,
     COST_REDUCTION_CN_NAME,
     COST_REDUCTION_LEVEL,
     SOFTWARE_MARK,
     GRANULARITY_TYPE)
   WITH BASE_IDX AS (
   --SPART指数取数
   SELECT T2.CUSTOM_ID,
		  T2.CUSTOM_CN_NAME,
		  T2.GROUP_LEVEL,
		  T1.PERIOD_YEAR,
		  T1.PERIOD_ID,
		  T1.GROUP_CODE,
		  T1.GROUP_CN_NAME,
		  T1.COST_INDEX,
		  T1.YTD_COST_INDEX,
		  T1.PARENT_CODE,
		  T1.REGION_CODE,
		  T1.REGION_CN_NAME,
		  T1.REPOFFICE_CODE,
		  T1.REPOFFICE_CN_NAME,
		  T1.BG_CODE,
		  T1.BG_CN_NAME,
		  T1.OVERSEA_FLAG,
		  T1.MAIN_FLAG,
		  T1.CODE_ATTRIBUTES,
		  T1.SOFTWARE_MARK
     FROM '||V_FROM_RED_IDX_TABLE||' T1
	INNER JOIN DM_CUSTOM_DIM_TEMP T2
	   ON T1.PARENT_CODE = T2.LV4_CODE
	  AND T1.GROUP_CODE = T2.SPART_CODE 
	  AND T1.VERSION_ID = '||V_VERSION||' 
	  AND T1.GROUP_LEVEL = ''SPART''
	  AND T1.REGION_CODE = T2.REGION_CODE 
	  AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE 
	  AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK 
	  AND T1.BG_CODE = T2.BG_CODE 
	  AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
	  AND NVL(T1.MAIN_FLAG,''MF'') = NVL(T2.MAIN_FLAG,''MF'')
	  AND NVL(T1.CODE_ATTRIBUTES,''CA'') = NVL(T2.CODE_ATTRIBUTES,''CA'')
	WHERE T1.VERSION_ID = '||V_VERSION||' 
	  AND T1.GROUP_LEVEL = ''SPART''
),
  BASE_WEIGHT AS (
  --权重取数
  SELECT CUSTOM_ID,
		 PERIOD_YEAR,
		 GROUP_CODE,
		 GROUP_CN_NAME,
		 WEIGHT_RATE,
		 PARENT_CODE,
		 REGION_CODE,
		 REPOFFICE_CODE,
		 BG_CODE,
		 OVERSEA_FLAG,
		 MAIN_FLAG,
		 CODE_ATTRIBUTES,
		 SOFTWARE_MARK
    FROM '||V_TO_WEIGHT_TABLE||'
	WHERE VERSION_ID = '||V_VERSION||'
	  '||V_CUSTOM_ID_SQL||'
  )
  SELECT T1.CUSTOM_ID,
         T1.CUSTOM_CN_NAME,
         '||V_VERSION||' AS VERSION_ID,
         LEFT(T1.PERIOD_ID,4) AS PERIOD_YEAR,
         T1.PERIOD_ID,
         T1.CUSTOM_ID AS GROUP_CODE,
         T1.CUSTOM_CN_NAME AS GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         SUM(T1.COST_INDEX*T2.WEIGHT_RATE) AS COST_INDEX ,
         SUM(T1.YTD_COST_INDEX*T2.WEIGHT_RATE) AS YTD_COST_INDEX,
         T3.COST_REDUCTION_RATE, 	--降成本目标值
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME,
         T1.OVERSEA_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N''AS DEL_FLAG,
         T1.MAIN_FLAG,
         T1.CODE_ATTRIBUTES,
         T3.COST_REDUCTION_CODE,		--降成本目标CODE
         T3.COST_REDUCTION_CN_NAME,		--降成本目标NAME
         T3.COST_REDUCTION_LEVEL,		--降成本目标LEVEL
         T1.SOFTWARE_MARK,
         '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
    FROM BASE_IDX T1
	LEFT JOIN BASE_WEIGHT T2
	  ON T1.GROUP_CODE = T2.GROUP_CODE
	 AND T1.PARENT_CODE = T2.PARENT_CODE
	 AND T1.REGION_CODE = T2.REGION_CODE 
	 AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE 
	 AND T1.PERIOD_YEAR = SUBSTR(T2.PERIOD_YEAR,-4,4)
	 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK 
	 AND T1.BG_CODE = T2.BG_CODE 
	 AND T1.CUSTOM_ID = T2.CUSTOM_ID
	 AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
	 AND NVL(T1.MAIN_FLAG,''MF'') = NVL(T2.MAIN_FLAG,''MF'')
	 AND NVL(T1.CODE_ATTRIBUTES,''CA'') = NVL(T2.CODE_ATTRIBUTES,''CA'')
	LEFT JOIN DM_CUSTOM_COST_RED_RATE_TEMP T3
	  ON T1.CUSTOM_ID = T3.CUSTOM_ID
   GROUP BY T1.MAIN_FLAG,
            T1.CODE_ATTRIBUTES,
            T1.SOFTWARE_MARK,
			T1.REGION_CODE,
			T1.REGION_CN_NAME,
			T1.REPOFFICE_CODE,
			T1.REPOFFICE_CN_NAME,
			T3.COST_REDUCTION_RATE,
			T3.COST_REDUCTION_CODE,	
			T3.COST_REDUCTION_CN_NAME,	
			T3.COST_REDUCTION_LEVEL,	
			T1.BG_CODE,
			T1.BG_CN_NAME,
			T1.OVERSEA_FLAG,
			T1.CUSTOM_ID,
            T1.CUSTOM_CN_NAME,
			T1.PERIOD_ID,
			T1.GROUP_LEVEL;';
  EXECUTE V_SQL;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 9;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 9,
  F_CAL_LOG_DESC => '汇总组合ID：'||F_CUSTOM_ID||'，降成本指数计算完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
END IF ;

 --写入日志
 V_EXCEPTION_FLAG	:= 8;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 8,
  F_CAL_LOG_DESC => '汇总组合ID：'||F_CUSTOM_ID||'，计算完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

  RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
     PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_STEP_NUM => V_EXCEPTION_FLAG,
    F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

 END; 
$$
/

