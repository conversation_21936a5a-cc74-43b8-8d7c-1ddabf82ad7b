-- Name: f_dm_foc_mid_month_idx_dms_202401; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_mid_month_idx_dms_202401(f_dimension_type character varying, f_keystr character varying, f_caliber_flag character varying, f_view_flag character varying, f_oversea_flag character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/***************************************************************************************************************************************************************
创建时间：2023-08-15 
创建人  ：黄心蕊 HWX1187045
背景描述：月度分析-规格品指数初始化
修改时间：2023-12-22 
修改人  ：黄心蕊 HWX1187045
背景描述：202401版本新增SPART层级
参数描述：参数一(F_KEYSTR)：绝密数据解密密钥串
		  参数二(F_ITEM_VERSION)：通用版本号
		  参数三(F_DIMENSION_TYPE)：维度类型（U：通用/P：盈利颗粒度）
		  参数四(X_SUCCESS_FLAG)：运行状态返回值 ‘1’为成功，‘0’为失败
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_MID_MONTH_IDX('U','密钥串'); --通用颗粒度一个版本的数据
		SELECT FIN_DM_OPT_FOI.f_dm_foc_mid_month_idx_dms('D','5a1e3b8d669845cd','C','9','G'); --量纲颗粒度发货口径9视角全球数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                           VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_MID_MONTH_IDX_DMS_202401';
  V_VERSION                           BIGINT; --版本号
  V_STEP_NUM                          INT := 0; --函数步骤号
  V_KEYSTR                            VARCHAR(100) := F_KEYSTR; --解密密钥串
  V_BASE_PERIOD_ID                    INT := TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '01'); --基期会计期
  V_DIMENSION_TYPE                    VARCHAR(2) := F_DIMENSION_TYPE; --维度标识
  V_LV3_PROD_RND_TEAM_CODE            TEXT; --LV3CODE字段名
  V_LV3_PROD_RD_TEAM_CN_NAME          TEXT; --LV3NAME字段名
  V_L1_NAME                           TEXT; --盈利颗粒度L1名称字段
  V_L2_NAME                           TEXT; --盈利颗粒度L2名称字段
  V_TOP_ITEM_INFO_T                   TEXT; --规格品维取数表
  V_PROFITS_NAME                      TEXT; --盈利颗粒度字段
  V_BASE_DETAIL_ITEM_T                TEXT; --均本取数表
  V_SQL_LV3_PROD_RND_TEAM_CODE        TEXT; --LV3CODE查询字段
  V_SQL_LV3_PROD_RD_TEAM_CN_NAME      TEXT; --LV3NAME查询字段
  V_SQL_L1_NAME                       TEXT; --盈利颗粒度L1名称查询字段
  V_SQL_L2_NAME                       TEXT; --盈利颗粒度L2名称查询字段
  V_SQL_PROD_RND_TEAM_CODE            TEXT; --重量级团队CODE判断逻辑
  V_SQL_PROD_RND_TEAM_CN_NAME         TEXT; --重量级团队NAME判断逻辑
  V_SQL_PROFITS_NAME                  TEXT; --盈利颗粒度维判断逻辑
  V_JION_LV3_PROD_RND_TEAM_CODE       TEXT; --重量级团队CODE关联逻辑
  V_JOIN_L1_NAME                      TEXT; --盈利颗粒度L1关联逻辑
  V_JOIN_L2_NAME                      TEXT; --盈利颗粒度L2关联逻辑
  V_JOIN_PROFITS_NAME                 TEXT; --盈利颗粒度关联逻辑
  V_SQL_DIMENSION_PART                TEXT; --维度查询字段
  V_MID_TABLE                         TEXT; --中间表
  V_DECRYP_AVG_TEMP                   TEXT; --解密临时表
  V_SQL                               TEXT; --执行语句
  /*九月新增*/
  V_DIMENSION_CODE                    TEXT; --量纲颗粒度
  V_DIMENSION_CN_NAME                 TEXT;
  V_DIMENSION_SUBCATEGORY_CODE        TEXT; --量纲子类
  V_DIMENSION_SUBCATEGORY_CN_NAME     TEXT;
  V_DIMENSION_SUB_DETAIL_CODE         TEXT; --量纲子类明细
  V_DIMENSION_SUB_DETAIL_CN_NAME      TEXT;
  V_DMS_CODE                          TEXT; --量纲粒度
  V_DMS_CN_NAME                       TEXT; --量纲粒度
  V_JOIN_DIMENSION_CODE               TEXT; --量纲关联条件
  V_JOIN_DIMENSION_SUBCATEGORY_CODE   TEXT; --量纲子类关联条件
  V_JOIN_DIMENSION_SUB_DETAIL_CODE    TEXT; --子类明细关联条件
  V_JOIN_DMS_CODE                     TEXT; --量纲颗粒度关联条件
  V_SQL_DIMENSION_CODE                TEXT; --量纲查询表字段
  V_SQL_DIMENSION_CN_NAME             TEXT; --量纲查询表字段
  V_SQL_DIMENSION_SUBCATEGORY_CODE    TEXT; --量纲子类查询表字段
  V_SQL_DIMENSION_SUBCATEGORY_CN_NAME TEXT; --量纲子类查询表字段
  V_SQL_DIMENSION_SUB_DETAIL_CODE     TEXT; --子类明细查询表字段
  V_SQL_DIMENSION_SUB_DETAIL_CN_NAME  TEXT; --子类明细查询表字段
  V_SQL_DMS_CODE                      TEXT;
  V_SQL_DMS_CN_NAME                   TEXT;
  V_RESERVE_SQL						TEXT;--通用层级反向视角VIEW条件字段
	V_VIEW_FLAG							VARCHAR(2):=F_VIEW_FLAG;
	V_OVERSEA_FLAG					TEXT;
  V_CALIBER_FLAG 					VARCHAR(2):=F_CALIBER_FLAG;
  
  /*202401版本新增SPART层级*/
  V_SPART_CODE         				TEXT;		
  V_SPART_CN_NAME      	            TEXT;
  V_JOIN_SPART_CODE                 TEXT;
  V_SQL_SPART_CODE                  TEXT;
  V_SQL_SPART_CN_NAME	            TEXT;

BEGIN 
X_SUCCESS_FLAG := '1';

--日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

V_STEP_NUM:=V_STEP_NUM+1; 
--版本号入参判断，当入参为空，取TOP规格品清单最新版本号
IF F_ITEM_VERSION IS NULL THEN
SELECT VERSION_ID INTO V_VERSION
  FROM FIN_DM_OPT_FOI.DM_FOC_TOP_ITEM_INFO_T
 ORDER BY LAST_UPDATE_DATE DESC
 LIMIT 1;
--入参不为空，则以入参为版本号
ELSE V_VERSION := F_ITEM_VERSION;
END IF;
  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

  /*量纲颗粒度变量*/
  V_DIMENSION_CODE                               :='';
  V_DIMENSION_CN_NAME                            :='';
  V_DIMENSION_SUBCATEGORY_CODE                   :='';
  V_DIMENSION_SUBCATEGORY_CN_NAME                :='';
  V_DIMENSION_SUB_DETAIL_CODE                    :='';
  V_DIMENSION_SUB_DETAIL_CN_NAME                 :='';
  V_DMS_CODE                                     :='';
  V_DMS_CN_NAME                                  :='';
  V_JOIN_DIMENSION_CODE                          :='';
  V_JOIN_DIMENSION_SUBCATEGORY_CODE              :='';
  V_JOIN_DIMENSION_SUB_DETAIL_CODE               :='';
  V_SQL_DIMENSION_CODE                           :='';
  V_SQL_DIMENSION_CN_NAME                        :='';
  V_SQL_DIMENSION_SUBCATEGORY_CODE               :='';
  V_SQL_DIMENSION_SUBCATEGORY_CN_NAME            :='';
  V_SQL_DIMENSION_SUB_DETAIL_CODE                :='';
  V_SQL_DIMENSION_SUB_DETAIL_CN_NAME             :='';
  V_SQL_DMS_CODE                                 :='';
  V_SQL_DMS_CN_NAME                              :='';
  
  --202401版本新增SPART层级
  V_SPART_CODE       							:=''; 
  V_SPART_CN_NAME                               :=''; 
  V_JOIN_SPART_CODE                             :=''; 
  V_SQL_SPART_CODE                              :=''; 
  V_SQL_SPART_CN_NAME                           :=''; 
  
  
  /*盈利颗粒度变量*/
   V_L1_NAME            := '';
   V_L2_NAME            := '';
   V_PROFITS_NAME       := '';
   V_SQL_L1_NAME        := '';
   V_SQL_L2_NAME        := '';
   V_JOIN_L1_NAME       := '';
   V_JOIN_L2_NAME       := '';
   V_JOIN_PROFITS_NAME  := '';
   V_SQL_PROFITS_NAME   := '';
   V_SQL_DIMENSION_PART := '';
   
   /*通用颗粒度变量*/
   V_LV3_PROD_RND_TEAM_CODE       := '';
   V_LV3_PROD_RD_TEAM_CN_NAME     := '';
   V_SQL_LV3_PROD_RND_TEAM_CODE   := '';
   V_SQL_LV3_PROD_RD_TEAM_CN_NAME := '';
   V_JION_LV3_PROD_RND_TEAM_CODE  := '';
   V_RESERVE_SQL:='';
  
  IF V_DIMENSION_TYPE = 'U' THEN
  
    /*表定义*/
    V_TOP_ITEM_INFO_T    := 'FIN_DM_OPT_FOI.DM_FOC_TOP_ITEM_INFO_T';
    V_BASE_DETAIL_ITEM_T := 'FIN_DM_OPT_FOI.DM_FOC_BASE_DETAIL_ITEM_T';
    V_MID_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_IDX_T';
    V_DECRYP_AVG_TEMP    := 'DM_FOC_DECRYP_AVG_TEMP';
  
    /*字段值定义*/
    V_LV3_PROD_RND_TEAM_CODE       := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME     := 'LV3_PROD_RD_TEAM_CN_NAME,';
    V_SQL_LV3_PROD_RND_TEAM_CODE   := 'T2.LV3_PROD_RND_TEAM_CODE,';
    V_SQL_LV3_PROD_RD_TEAM_CN_NAME := 'T2.LV3_PROD_RD_TEAM_CN_NAME,';
    V_SQL_PROD_RND_TEAM_CODE       := '         
         CASE T2.VIEW_FLAG
           WHEN ''0'' THEN T2.LV0_PROD_RND_TEAM_CODE
           WHEN ''1'' THEN T2.LV1_PROD_RND_TEAM_CODE
           WHEN ''2'' THEN T2.LV2_PROD_RND_TEAM_CODE
		   ELSE T2.LV3_PROD_RND_TEAM_CODE
         END AS PROD_RND_TEAM_CODE,';
    V_SQL_PROD_RND_TEAM_CN_NAME    := '
     CASE T2.VIEW_FLAG
           WHEN ''0'' THEN T2.LV0_PROD_RD_TEAM_CN_NAME
           WHEN ''1'' THEN T2.LV1_PROD_RD_TEAM_CN_NAME
           WHEN ''2'' THEN T2.LV2_PROD_RD_TEAM_CN_NAME
		   ELSE T2.LV3_PROD_RD_TEAM_CN_NAME
         END AS PROD_RND_TEAM_CN_NAME,';
  
    /*条件定义*/
    V_JION_LV3_PROD_RND_TEAM_CODE := ' AND NVL(T1.LV3_PROD_RND_TEAM_CODE,3) = NVL(T2.LV3_PROD_RND_TEAM_CODE,3)';
	V_RESERVE_SQL:=' AND VIEW_FLAG IN (''0'',''1'',''2'',''3'') '; --正向视角中，视角数据不参与计算
  
  ELSIF V_DIMENSION_TYPE = 'P' THEN
  
    /*表定义*/
    V_TOP_ITEM_INFO_T    := 'FIN_DM_OPT_FOI.DM_FOC_PFT_TOP_ITEM_INFO_T';
    V_BASE_DETAIL_ITEM_T := 'FIN_DM_OPT_FOI.DM_FOC_PFT_BASE_DETAIL_ITEM_T';
    V_MID_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_IDX_T';
    V_DECRYP_AVG_TEMP    := 'DM_FOC_PFT_DECRYP_AVG_TEMP';
  
    /*字段值定义*/
    V_L1_NAME                   := 'L1_NAME,';
    V_L2_NAME                   := 'L2_NAME,';
    V_PROFITS_NAME              := 'PROFITS_NAME,';
    V_SQL_L1_NAME               := 'T2.L1_NAME,';
    V_SQL_L2_NAME               := 'T2.L2_NAME,';
    V_SQL_DIMENSION_PART        := 'T2.PROFITS_NAME,';
    V_SQL_PROD_RND_TEAM_CODE    := '         
       CASE T2.VIEW_FLAG
         WHEN ''0'' THEN T2.LV0_PROD_RND_TEAM_CODE
         WHEN ''1'' THEN T2.LV1_PROD_RND_TEAM_CODE
         ELSE T2.LV2_PROD_RND_TEAM_CODE
       END AS PROD_RND_TEAM_CODE,';
    V_SQL_PROD_RND_TEAM_CN_NAME := '
       CASE T2.VIEW_FLAG
         WHEN ''0'' THEN T2.LV0_PROD_RD_TEAM_CN_NAME
         WHEN ''1'' THEN T2.LV1_PROD_RD_TEAM_CN_NAME
         ELSE T2.LV2_PROD_RD_TEAM_CN_NAME
       END AS PROD_RND_TEAM_CN_NAME,';
    V_SQL_PROFITS_NAME          := '
       CASE T2.VIEW_FLAG
         WHEN ''3'' THEN T2.L1_NAME
         WHEN ''4'' THEN T2.L2_NAME
         ELSE ''''
       END AS PROFITS_NAME,';
  
    /*条件定义*/
    V_JOIN_L1_NAME      := ' AND NVL(T1.L1_NAME,3) = NVL(T2.L1_NAME,3)';
    V_JOIN_L2_NAME      := ' AND NVL(T1.L2_NAME,4) = NVL(T2.L2_NAME,4)';
    V_JOIN_PROFITS_NAME := ' AND NVL(T1.PROFITS_NAME,5) = NVL(T2.PROFITS_NAME,5) ';
		
		V_OVERSEA_FLAG := ' OVERSEA_FLAG IN (''O'',''G'',''I'') ';
  
  ELSIF V_DIMENSION_TYPE = 'D' THEN
    /*表定义*/
    V_TOP_ITEM_INFO_T    := 'FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_ITEM_INFO_T_202401';  --20240119 修改来源表表名
    V_BASE_DETAIL_ITEM_T := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_202401';
    --V_MID_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T';
    --V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP';
  
        /*对输入参数做判断，确定往对应目标表插数*/ /*表定义按照口径+视角创建表，比如发货口视角1、发货口径视角2*/
        --发货口径不同视角目标表，包含国内、海外、全球3份数据
        IF F_CALIBER_FLAG = 'C' AND F_VIEW_FLAG = '0' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_C_VIEW0'; --发货口径视角0
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_C_VIEW0';
        ELSIF F_CALIBER_FLAG = 'C' AND F_VIEW_FLAG = '1' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_C_VIEW1'; --发货口径视角1
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_C_VIEW1';
        ELSIF F_CALIBER_FLAG = 'C' AND F_VIEW_FLAG = '2' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_C_VIEW2'; --发货口径视角2
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_C_VIEW2';
        ELSIF F_CALIBER_FLAG = 'C' AND F_VIEW_FLAG = '3' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_C_VIEW3'; --发货口径视角3
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_C_VIEW3';
        ELSIF F_CALIBER_FLAG = 'C' AND F_VIEW_FLAG = '4' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_C_VIEW4'; --发货口径视角4
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_C_VIEW4';
        ELSIF F_CALIBER_FLAG = 'C' AND F_VIEW_FLAG = '5' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_C_VIEW5'; --发货口径视角5
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_C_VIEW5';
        ELSIF F_CALIBER_FLAG = 'C' AND F_VIEW_FLAG = '6' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_C_VIEW6'; --发货口径视角6
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_C_VIEW6';
        ELSIF F_CALIBER_FLAG = 'C' AND F_VIEW_FLAG = '7' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_C_VIEW7'; --发货口径视角7
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_C_VIEW7';
        ELSIF F_CALIBER_FLAG = 'C' AND F_VIEW_FLAG = '8' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_C_VIEW8'; --发货口径视角8
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_C_VIEW8';
	    ELSIF F_CALIBER_FLAG = 'C' AND F_VIEW_FLAG = '9' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_C_VIEW9'; --发货口径视角8
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_C_VIEW9';
		ELSIF F_CALIBER_FLAG = 'C' AND F_VIEW_FLAG = '10' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_C_VIEW10'; --发货口径视角8
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_C_VIEW10';
		ELSIF F_CALIBER_FLAG = 'C' AND F_VIEW_FLAG = '11' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_C_VIEW11'; --发货口径视角8
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_C_VIEW11';

        --收入口径不同视角目标表，包含国内、海外、全球3份数据
        ELSIF F_CALIBER_FLAG = 'R' AND F_VIEW_FLAG = '0' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_R_VIEW0'; --收入口径视角0
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_R_VIEW0';
        ELSIF F_CALIBER_FLAG = 'R' AND F_VIEW_FLAG = '1' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_R_VIEW1'; --收入口径视角1
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_R_VIEW1';
        ELSIF F_CALIBER_FLAG = 'R' AND F_VIEW_FLAG = '2' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_R_VIEW2'; --收入口径视角2
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_R_VIEW2';
        ELSIF F_CALIBER_FLAG = 'R' AND F_VIEW_FLAG = '3' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_R_VIEW3'; --收入口径视角3
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_R_VIEW3';
        ELSIF F_CALIBER_FLAG = 'R' AND F_VIEW_FLAG = '4' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_R_VIEW4'; --收入口径视角4
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_R_VIEW4';
        ELSIF F_CALIBER_FLAG = 'R' AND F_VIEW_FLAG = '5' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_R_VIEW5'; --收入口径视角5
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_R_VIEW5';
        ELSIF F_CALIBER_FLAG = 'R' AND F_VIEW_FLAG = '6' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_R_VIEW6'; --收入口径视角6
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_R_VIEW6';
        ELSIF F_CALIBER_FLAG = 'R' AND F_VIEW_FLAG = '7' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_R_VIEW7'; --收入口径视角7
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_R_VIEW7';
        ELSIF F_CALIBER_FLAG = 'R' AND F_VIEW_FLAG = '8' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_R_VIEW8'; --收入口径视角8
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_R_VIEW8';
		ELSIF F_CALIBER_FLAG = 'R' AND F_VIEW_FLAG = '9' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_R_VIEW9'; --发货口径视角8
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_R_VIEW9';
		ELSIF F_CALIBER_FLAG = 'R' AND F_VIEW_FLAG = '10' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_R_VIEW10'; --发货口径视角8
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_R_VIEW10';
		ELSIF F_CALIBER_FLAG = 'R' AND F_VIEW_FLAG = '11' THEN
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_R_VIEW11'; --发货口径视角8
			V_DECRYP_AVG_TEMP    := 'DM_FOC_DMS_DECRYP_AVG_TEMP_R_VIEW11';
        END IF;
  
    /*字段值定义*/
    V_LV3_PROD_RND_TEAM_CODE   := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
  
    /*九月新增*/
    V_DIMENSION_CODE                := 'DIMENSION_CODE,';
    V_DIMENSION_CN_NAME             := 'DIMENSION_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE    := 'DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE     := 'DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME  := 'DIMENSION_SUB_DETAIL_CN_NAME,';
    V_DMS_CODE                      := 'DMS_CODE,';
    V_DMS_CN_NAME                   := 'DMS_CN_NAME,';
	--202401版本新增SPART层级
	V_SPART_CODE	:='SPART_CODE,'; 
	V_SPART_CN_NAME :='SPART_CN_NAME,';
  
    V_SQL_LV3_PROD_RND_TEAM_CODE        := 'T2.LV3_PROD_RND_TEAM_CODE,';
    V_SQL_LV3_PROD_RD_TEAM_CN_NAME      := 'T2.LV3_PROD_RD_TEAM_CN_NAME,';
    V_SQL_DIMENSION_CODE                := 'T2.DIMENSION_CODE,';
    V_SQL_DIMENSION_CN_NAME             := 'T2.DIMENSION_CN_NAME,';
    V_SQL_DIMENSION_SUBCATEGORY_CODE    := 'T2.DIMENSION_SUBCATEGORY_CODE,';
    V_SQL_DIMENSION_SUBCATEGORY_CN_NAME := 'T2.DIMENSION_SUBCATEGORY_CN_NAME,';
    V_SQL_DIMENSION_SUB_DETAIL_CODE     := 'T2.DIMENSION_SUB_DETAIL_CODE,';
    V_SQL_DIMENSION_SUB_DETAIL_CN_NAME  := 'T2.DIMENSION_SUB_DETAIL_CN_NAME,';
    V_SQL_DIMENSION_PART                := 'T2.DMS_CODE,T2.DMS_CN_NAME,';
    V_SQL_DMS_CODE   					:= '         
      CASE WHEN T2.VIEW_FLAG IN (''0'',''3'',''6'') THEN T2.DIMENSION_CODE
           WHEN T2.VIEW_FLAG IN (''1'',''4'',''7'') THEN T2.DIMENSION_SUBCATEGORY_CODE
           WHEN T2.VIEW_FLAG IN (''2'',''5'',''8'') THEN T2.DIMENSION_SUB_DETAIL_CODE
		   ELSE T2.SPART_CODE
         END AS DMS_CODE,';  --202401版本新增SPART层级 202401版本新增9，10，11视角
    V_SQL_DMS_CN_NAME   				:= '
      CASE WHEN T2.VIEW_FLAG IN (''0'',''3'',''6'') THEN T2.DIMENSION_CN_NAME
           WHEN T2.VIEW_FLAG IN (''1'',''4'',''7'') THEN T2.DIMENSION_SUBCATEGORY_CN_NAME
		   WHEN T2.VIEW_FLAG IN (''2'',''5'',''8'') THEN T2.DIMENSION_SUB_DETAIL_CN_NAME
		   ELSE T2.SPART_CN_NAME
       END AS DMS_CN_NAME,';  --202401版本新增SPART层级 202401版本新增9，10，11视角
  
    V_SQL_PROD_RND_TEAM_CODE    := '         
      CASE  WHEN T2.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN T2.LV1_PROD_RND_TEAM_CODE
            WHEN T2.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T2.LV2_PROD_RND_TEAM_CODE
         ELSE T2.LV3_PROD_RND_TEAM_CODE
         END AS PROD_RND_TEAM_CODE,'; --202401版本新增9，10，11视角
    V_SQL_PROD_RND_TEAM_CN_NAME := '
       CASE WHEN T2.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN T2.LV1_PROD_RD_TEAM_CN_NAME
            WHEN T2.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T2.LV2_PROD_RD_TEAM_CN_NAME
       ELSE T2.LV3_PROD_RD_TEAM_CN_NAME
       END AS PROD_RND_TEAM_CN_NAME,'; --202401版本新增9，10，11视角
	
	--202401版本新增SPART层级
	V_SQL_SPART_CODE	:='T2.SPART_CODE,'; 	
    V_SQL_SPART_CN_NAME :='T2.SPART_CN_NAME,';
	
	
    /*条件定义*/
    V_JION_LV3_PROD_RND_TEAM_CODE     := ' AND NVL(T1.LV3_PROD_RND_TEAM_CODE,3) = NVL(T2.LV3_PROD_RND_TEAM_CODE,3)';
    V_JOIN_DIMENSION_CODE             := ' AND NVL(T1.DIMENSION_CODE,''D1'') = NVL(T2.DIMENSION_CODE,''D1'')';
    V_JOIN_DIMENSION_SUBCATEGORY_CODE := ' AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''D2'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''D2'')';
    V_JOIN_DIMENSION_SUB_DETAIL_CODE  := ' AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''D3'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''D3'')';
    V_JOIN_DMS_CODE                   := ' AND NVL(T1.DMS_CODE,''DD'') = NVL(T2.DMS_CODE,''DD'') ';
	V_JOIN_SPART_CODE					:= ' AND NVL(T1.SPART_CODE,''D4'') = NVL(T2.SPART_CODE,''D4'') '; 
		
		V_OVERSEA_FLAG := ' OVERSEA_FLAG = '''||F_OVERSEA_FLAG||''' ';
  
  END IF;
  
V_STEP_NUM:=V_STEP_NUM+1;
V_SQL := '
DROP TABLE IF EXISTS '||V_DECRYP_AVG_TEMP||';
CREATE TEMPORARY TABLE '||V_DECRYP_AVG_TEMP||'(
	VIEW_FLAG VARCHAR(2),
	PERIOD_YEAR BIGINT,
	PERIOD_ID BIGINT,
	LV0_PROD_RND_TEAM_CODE	VARCHAR(50),
	LV1_PROD_RND_TEAM_CODE	VARCHAR(50),
	LV2_PROD_RND_TEAM_CODE	VARCHAR(50),
	LV3_PROD_RND_TEAM_CODE  VARCHAR(50),
	LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200),
	L1_NAME	VARCHAR(200),/*7月新增盈利颗粒度维度*/
	L2_NAME	VARCHAR(200),
	DIMENSION_CODE VARCHAR(50), 
	DIMENSION_SUBCATEGORY_CODE VARCHAR(50), 
	DIMENSION_SUB_DETAIL_CODE VARCHAR(50), 
	SPART_CODE 		VARCHAR(50), 
	SPART_CN_NAME   VARCHAR(50),   --202401版本新增SPART层级
	L3_CEG_CODE	VARCHAR(50),
	L4_CEG_CODE    VARCHAR(50),  
	CATEGORY_CODE VARCHAR(50),
	ITEM_CODE CHARACTER VARYING(50),
	AVG_AMT NUMERIC,
	SCENARIO_FLAG VARCHAR(5),
	APPEND_FLAG VARCHAR(5),
	CALIBER_FLAG VARCHAR(5), /*7月新增业务口径字段*/
	OVERSEA_FLAG  VARCHAR(2), 
	LV0_PROD_LIST_CODE VARCHAR(50)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(ITEM_CODE,SPART_CODE,LV3_PROD_RND_TEAM_CODE);

INSERT INTO '||V_DECRYP_AVG_TEMP||'
  (VIEW_FLAG,
   PERIOD_YEAR,
   PERIOD_ID,
   LV0_PROD_RND_TEAM_CODE,
   LV1_PROD_RND_TEAM_CODE,
   LV2_PROD_RND_TEAM_CODE,
   '||V_LV3_PROD_RND_TEAM_CODE
    ||V_L1_NAME
    ||V_L2_NAME
    ||V_DIMENSION_CODE
    ||V_DIMENSION_SUBCATEGORY_CODE
    ||V_DIMENSION_SUB_DETAIL_CODE
	||V_SPART_CODE||'   --202401版本新增SPART层级
   L3_CEG_CODE,
   L4_CEG_CODE,
   CATEGORY_CODE,
   ITEM_CODE,
   AVG_AMT,
   SCENARIO_FLAG,
   APPEND_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG,
   LV0_PROD_LIST_CODE
   )
 SELECT VIEW_FLAG,
       PERIOD_YEAR,
       PERIOD_ID,
       LV0_PROD_RND_TEAM_CODE,
       LV1_PROD_RND_TEAM_CODE,
       LV2_PROD_RND_TEAM_CODE,
       '||V_LV3_PROD_RND_TEAM_CODE
  	    ||V_L1_NAME
  	    ||V_L2_NAME
        ||V_DIMENSION_CODE
        ||V_DIMENSION_SUBCATEGORY_CODE
		||V_DIMENSION_SUB_DETAIL_CODE
		||V_SPART_CODE||'   --202401版本新增SPART层级	
       L3_CEG_CODE,
	     L4_CEG_CODE,
       CATEGORY_CODE,
       ITEM_CODE,
			 RMB_AVG_AMT,
--        TO_NUMBER(GS_DECRYPT(RMB_AVG_AMT,
--                             '''||V_KEYSTR||''',
--                   							''aes128'',
--                   							''cbc'',
--                   							''sha256'')) AS AVG_AMT,
       SCENARIO_FLAG,
       APPEND_FLAG,
       CALIBER_FLAG,
  	   OVERSEA_FLAG,
  	   LV0_PROD_LIST_CODE
  FROM '||V_BASE_DETAIL_ITEM_T||'
 WHERE PERIOD_YEAR BETWEEN (YEAR(CURRENT_DATE) - 2) AND YEAR(CURRENT_DATE) 
   AND VIEW_FLAG = '||V_VIEW_FLAG||'
	 AND '||V_OVERSEA_FLAG||'
	 AND CALIBER_FLAG = '''||V_CALIBER_FLAG||'''  ';
 
 dbms_output.PUT_LINE(V_SQL);
 EXECUTE IMMEDIATE V_SQL;
 
 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '数据解密完成',
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

--IF V_VIEW_FLAG = '0' AND V_OVERSEA_FLAG = 'O' AND V_CALIBER_FLAG = 'C' THEN
--V_SQL := 'TRUNCATE TABLE '||V_MID_TABLE||' ;';
-- EXECUTE IMMEDIATE V_SQL;
--ELSE NULL;
--END IF;

 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '指数中间表数据清除完成',
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

V_STEP_NUM:=V_STEP_NUM+1;  
V_SQL:='
DELETE FROM '||V_MID_TABLE||' WHERE  VIEW_FLAG = '||V_VIEW_FLAG||' AND '||V_OVERSEA_FLAG||' AND CALIBER_FLAG = '''||V_CALIBER_FLAG||''' ;

WITH TOP_ITEM AS
 (SELECT VIEW_FLAG,
       LV0_PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME,
       LV1_PROD_RND_TEAM_CODE,
       LV1_PROD_RD_TEAM_CN_NAME,
       LV2_PROD_RND_TEAM_CODE,
       LV2_PROD_RD_TEAM_CN_NAME,
       '||V_LV3_PROD_RND_TEAM_CODE||
       V_LV3_PROD_RD_TEAM_CN_NAME||
       V_L1_NAME||
       V_L2_NAME||
       V_DIMENSION_CODE||
       V_DIMENSION_CN_NAME||
       V_DIMENSION_SUBCATEGORY_CODE||
       V_DIMENSION_SUBCATEGORY_CN_NAME||
       V_DIMENSION_SUB_DETAIL_CODE||
       V_DIMENSION_SUB_DETAIL_CN_NAME
	   ||V_SPART_CODE	
	   ||V_SPART_CN_NAME||'   --202401版本新增SPART层级
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
  	   TOP_L4_CEG_CODE,
  	   TOP_L4_CEG_SHORT_CN_NAME,
       TOP_CATEGORY_CODE,
       TOP_CATEGORY_CN_NAME,
       TOP_ITEM_CODE,
       TOP_ITEM_CN_NAME,
       CALIBER_FLAG,
  	   OVERSEA_FLAG,  
  	   LV0_PROD_LIST_CODE, 
  	   LV0_PROD_LIST_CN_NAME 
  FROM '||V_TOP_ITEM_INFO_T||'
 WHERE VERSION_ID = '||V_VERSION||'
   AND IS_TOP_FLAG =''Y''/*取规格品*/
   AND DOUBLE_FLAG =''Y'' 
	 AND VIEW_FLAG = '||V_VIEW_FLAG||'
	 AND '||V_OVERSEA_FLAG||'
	 AND CALIBER_FLAG = '''||V_CALIBER_FLAG||'''
	 '||V_RESERVE_SQL||' ),
 
--关联得出全量TOP规格品三年月均本
TOP_AVG AS
 (SELECT T1.PERIOD_YEAR,
       T1.PERIOD_ID,
       T2.LV0_PROD_RND_TEAM_CODE,
       T2.LV0_PROD_RD_TEAM_CN_NAME,
       T2.LV1_PROD_RND_TEAM_CODE,
       T2.LV1_PROD_RD_TEAM_CN_NAME,
       T2.LV2_PROD_RND_TEAM_CODE,
       T2.LV2_PROD_RD_TEAM_CN_NAME,
       '||V_SQL_LV3_PROD_RND_TEAM_CODE
        ||V_SQL_LV3_PROD_RD_TEAM_CN_NAME
        ||V_SQL_L1_NAME
        ||V_SQL_L2_NAME
        ||V_SQL_DIMENSION_CODE
        ||V_SQL_DIMENSION_CN_NAME
        ||V_SQL_DIMENSION_SUBCATEGORY_CODE
        ||V_SQL_DIMENSION_SUBCATEGORY_CN_NAME
        ||V_SQL_DIMENSION_SUB_DETAIL_CODE
        ||V_SQL_DIMENSION_SUB_DETAIL_CN_NAME
		||V_SQL_SPART_CODE	
		||V_SQL_SPART_CN_NAME||'   --202401版本新增SPART层级
       T2.TOP_L3_CEG_CODE,
       T2.TOP_L3_CEG_SHORT_CN_NAME,
  	   T2.TOP_L4_CEG_CODE,
  	   T2.TOP_L4_CEG_SHORT_CN_NAME,
       T2.TOP_CATEGORY_CODE,
       T2.TOP_CATEGORY_CN_NAME,
       '||V_SQL_PROD_RND_TEAM_CODE||
        V_SQL_PROD_RND_TEAM_CN_NAME||
        V_SQL_PROFITS_NAME||
    		V_SQL_DMS_CODE||
    		V_SQL_DMS_CN_NAME||' 
       T2.TOP_ITEM_CODE AS GROUP_CODE,
       T2.TOP_ITEM_CN_NAME AS GROUP_CN_NAME,
       T1.AVG_AMT,
       T2.TOP_CATEGORY_CODE AS PARENT_CODE,
       T2.VIEW_FLAG,
       T1.SCENARIO_FLAG,
       T1.APPEND_FLAG,
       T2.CALIBER_FLAG,
	   T2.OVERSEA_FLAG,  
	   T2.LV0_PROD_LIST_CODE,
	   T2.LV0_PROD_LIST_CN_NAME
  FROM '||V_DECRYP_AVG_TEMP||' T1
 INNER JOIN TOP_ITEM T2
    ON T1.VIEW_FLAG = T2.VIEW_FLAG
   AND T1.LV0_PROD_RND_TEAM_CODE = T2.LV0_PROD_RND_TEAM_CODE
   AND NVL(T1.LV1_PROD_RND_TEAM_CODE, 1) =
       NVL(T2.LV1_PROD_RND_TEAM_CODE, 1)
   AND NVL(T1.LV2_PROD_RND_TEAM_CODE, 2) =
       NVL(T2.LV2_PROD_RND_TEAM_CODE, 2)
	   '||V_JION_LV3_PROD_RND_TEAM_CODE
	    ||V_JOIN_L1_NAME
	    ||V_JOIN_L2_NAME
	    ||V_JOIN_DIMENSION_CODE
	    ||V_JOIN_DIMENSION_SUBCATEGORY_CODE
	    ||V_JOIN_DIMENSION_SUB_DETAIL_CODE
		||V_JOIN_SPART_CODE||'   --202401版本新增SPART层级
   AND T1.L3_CEG_CODE = T2.TOP_L3_CEG_CODE
   AND T1.L4_CEG_CODE = T2.TOP_L4_CEG_CODE
   AND T1.CATEGORY_CODE = T2.TOP_CATEGORY_CODE
   AND T1.ITEM_CODE = T2.TOP_ITEM_CODE
   AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
   AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG  
   AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE 
   ),

BASE_PERIOD_AVG AS
 (--取基期均本
SELECT VIEW_FLAG,
       PROD_RND_TEAM_CODE,
       '||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME||
    		V_DMS_CODE||V_DMS_CN_NAME||
    		V_DIMENSION_CODE||
    		V_DIMENSION_SUBCATEGORY_CODE||
    		V_DIMENSION_SUB_DETAIL_CODE
			||V_SPART_CODE||'   --202401版本新增SPART层级
       GROUP_CODE,
       NULLIF(AVG_AMT, 0) AS AVG_AMT,
       CALIBER_FLAG,
	   OVERSEA_FLAG,  
	   LV0_PROD_LIST_CODE,
	   LV0_PROD_LIST_CN_NAME
  FROM TOP_AVG
 WHERE PERIOD_ID = '||V_BASE_PERIOD_ID||')
 
--规格品层级指数计算   
INSERT INTO '||V_MID_TABLE||'
  (PERIOD_YEAR,
   PERIOD_ID,
   VERSION_ID,
   BASE_PERIOD_ID,
   VIEW_FLAG,
   LV0_PROD_RND_TEAM_CODE,
   LV0_PROD_RD_TEAM_CN_NAME,
   LV1_PROD_RND_TEAM_CODE,
   LV1_PROD_RD_TEAM_CN_NAME,
   LV2_PROD_RND_TEAM_CODE,
   LV2_PROD_RD_TEAM_CN_NAME,
   '||V_LV3_PROD_RND_TEAM_CODE
   ||V_LV3_PROD_RD_TEAM_CN_NAME
   ||V_L1_NAME
   ||V_L2_NAME
   ||V_DIMENSION_CODE
   ||V_DIMENSION_CN_NAME
   ||V_DIMENSION_SUBCATEGORY_CODE
   ||V_DIMENSION_SUBCATEGORY_CN_NAME
   ||V_DIMENSION_SUB_DETAIL_CODE
   ||V_DIMENSION_SUB_DETAIL_CN_NAME
   ||V_SPART_CODE	
   ||V_SPART_CN_NAME||'   --202401版本新增SPART层级
   TOP_L3_CEG_CODE,
   TOP_L3_CEG_SHORT_CN_NAME,
   TOP_L4_CEG_CODE,
   TOP_L4_CEG_SHORT_CN_NAME,
   TOP_CATEGORY_CODE,
   TOP_CATEGORY_CN_NAME,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
   '||V_PROFITS_NAME||
    V_DMS_CODE||
    V_DMS_CN_NAME||'
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   COST_INDEX,
   PARENT_CODE,
   SCENARIO_FLAG,
   APPEND_FLAG,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG,
   LV0_PROD_LIST_CODE ,
   LV0_PROD_LIST_CN_NAME)
SELECT T2.PERIOD_YEAR,
       T2.PERIOD_ID,
       '||V_VERSION||' AS VERSION_ID,
       '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
       T2.VIEW_FLAG,
       T2.LV0_PROD_RND_TEAM_CODE,
       T2.LV0_PROD_RD_TEAM_CN_NAME,
       T2.LV1_PROD_RND_TEAM_CODE,
       T2.LV1_PROD_RD_TEAM_CN_NAME,
       T2.LV2_PROD_RND_TEAM_CODE,
       T2.LV2_PROD_RD_TEAM_CN_NAME,
       '||V_SQL_LV3_PROD_RND_TEAM_CODE
        ||V_SQL_LV3_PROD_RD_TEAM_CN_NAME
        ||V_SQL_L1_NAME
        ||V_SQL_L2_NAME
    	||V_SQL_DIMENSION_CODE 
    	||V_SQL_DIMENSION_CN_NAME
    	||V_SQL_DIMENSION_SUBCATEGORY_CODE 
    	||V_SQL_DIMENSION_SUBCATEGORY_CN_NAME
    	||V_SQL_DIMENSION_SUB_DETAIL_CODE 
    	||V_SQL_DIMENSION_SUB_DETAIL_CN_NAME
		||V_SQL_SPART_CODE	
		||V_SQL_SPART_CN_NAME||'   --202401版本新增SPART层级
       T2.TOP_L3_CEG_CODE,
       T2.TOP_L3_CEG_SHORT_CN_NAME,
  	   T2.TOP_L4_CEG_CODE,
  	   T2.TOP_L4_CEG_SHORT_CN_NAME, 
       T2.TOP_CATEGORY_CODE,
       T2.TOP_CATEGORY_CN_NAME,
       T2.PROD_RND_TEAM_CODE,
       T2.PROD_RND_TEAM_CN_NAME,
       '||V_SQL_DIMENSION_PART||' /*盈利颗粒度为盈利NAME，量纲颗粒度为量纲CODE与量纲NAME*/
       T2.GROUP_CODE,
       T2.GROUP_CN_NAME,
       ''ITEM'' AS GROUP_LEVEL,
       (T2.AVG_AMT / NULLIF(T1.AVG_AMT,0)*100) AS COST_INDEX,
       T2.TOP_CATEGORY_CODE AS PARENT_CODE,
       T2.SCENARIO_FLAG,
       T2.APPEND_FLAG,
       ''-1'' AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       ''-1'' AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       ''N'' AS DEL_FLAG,
       T2.CALIBER_FLAG,
  	   T2.OVERSEA_FLAG,  
  	   T2.LV0_PROD_LIST_CODE, 
  	   T2.LV0_PROD_LIST_CN_NAME
  FROM TOP_AVG T2
  JOIN BASE_PERIOD_AVG T1
    ON T1.VIEW_FLAG = T2.VIEW_FLAG
   AND T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE
   '||V_JOIN_PROFITS_NAME|| V_JOIN_L1_NAME|| V_JOIN_L2_NAME|| V_JOIN_DMS_CODE||
    V_JOIN_DIMENSION_CODE||
	V_JOIN_DIMENSION_SUBCATEGORY_CODE||
	V_JOIN_DIMENSION_SUB_DETAIL_CODE||
	V_JOIN_SPART_CODE||'   --202401版本新增SPART层级
   AND T1.GROUP_CODE = T2.GROUP_CODE
   AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
   AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
   AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE;
 ';
 dbms_output.PUT_LINE(V_SQL);
 EXECUTE IMMEDIATE V_SQL;
	 
--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '月度分析-规格品指数插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
  
RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; 
$$
/

