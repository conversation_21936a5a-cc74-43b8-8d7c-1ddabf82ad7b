-- Name: f_dm_foc_mid_month_item0708; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_mid_month_item0708(f_industry_flag character varying, f_period_id bigint, f_caliber_flag character varying DEFAULT NULL::character varying, f_dimension_type character varying DEFAULT NULL::bigint, f_keystr character varying DEFAULT NULL::character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
参数描述:f_caliber_flag : 业务口径(R：收入时点,C：发货成本), f_dimension_type : 维度类型(通用颗粒度：U,盈利颗粒度：P), x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T(发货成本),FIN_DM_OPT_FOI.DM_FOC_REVENUE_ITEM_SHIP_DTL_T(收入时点)
目标表:FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T(通用颗粒度),FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_ITEM_T(盈利颗粒度),FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T(量纲颗粒度)
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_MID_MONTH_ITEM()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_MID_MONTH_ITEM_202401'; --存储过程名称
  V_VERSION_ID BIGINT; --版本号, 取自 FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T的版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_BEGIN_YEAR BIGINT := YEAR(CURRENT_TIMESTAMP)-3;  -- 系统年-3的年份
  V_END_YEAR BIGINT := YEAR(CURRENT_TIMESTAMP);  -- 系统年
  V_SQL        TEXT;   --SQL逻辑
  V_LV1_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_LV2_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_LV3_PROD_RND_TEAM_CODE VARCHAR(50); -- 7月版本需求新增
  V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  
  V_LV4_PROD_RND_TEAM_CODE VARCHAR(50); -- 7月版本需求新增
  V_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_L1_NAME VARCHAR(100);
  V_L2_NAME VARCHAR(100);
  V_IN_LV1_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV1_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_IN_LV2_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV2_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_IN_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_IN_LV4_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_IN_L1_NAME VARCHAR(100);
  V_IN_L2_NAME VARCHAR(100);
  V_INSERT_LV1_PROD_RND_TEAM_CODE VARCHAR(200);
  V_INSERT_LV2_PROD_RND_TEAM_CODE VARCHAR(200);
  V_INSERT_LV3_PROD_RND_TEAM_CODE VARCHAR(200);
  V_INSERT_LV4_PROD_RND_TEAM_CODE VARCHAR(200);
  V_INSERT_L1_NAME VARCHAR(200);
  V_INSERT_L2_NAME VARCHAR(200);
  
  -- 7月版本需求新增
  V_FROM_TABLE VARCHAR(200); -- 来源表
  V_TO_TABLE VARCHAR(200); -- 目标表
  V_JOIN_TABLE VARCHAR(200); --解密表
  PARA VARCHAR(50);
  AMT_PARA VARCHAR(200);
  M_AMT_PARA VARCHAR(200);

  
BEGIN
  X_RESULT_STATUS = '1';
  
  --开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  IF F_CALIBER_FLAG = 'C' THEN  
   
 V_TO_TABLE := 'DWL_PROD_BOM_ITEM_SHIP_DIM_I_EXTRA';
 V_FROM_TABLE := 'DWL_PROD_BOM_ITEM_SHIP_DIM_I';
 V_JOIN_TABLE := 'DM_FOC_DATA_PRIMARY_ENCRYPT_T';
 PARA := ' AND T1.RECOGNISE_TYPE_ID = 4 ';
 AMT_PARA := ' TO_NUMBER(GS_DECRYPT(T2.RMB_COST_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256'')) AS RMB_COST_AMT ';
 M_AMT_PARA := ' IM.RMB_AVG_AMT * T1.ship_quantity AS RMB_COST_AMT';
 
 
 ELSIF F_CALIBER_FLAG = 'R' THEN
 V_TO_TABLE := 'DWL_PROD_BOM_ITEM_REV_DETAIL_I_EXTRA';
 V_FROM_TABLE := 'DWL_PROD_BOM_ITEM_REV_DETAIL_I';
 V_JOIN_TABLE := 'DM_FOC_REVENUE_DATA_PRIMARY_ENCRYPT_T';
 PARA := '';
 AMT_PARA := ' (TO_NUMBER(GS_DECRYPT(T2.RMB_AVG_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256''))* T1.quantity ) AS RMB_COST_AMT ';
 M_AMT_PARA := ' IM.RMB_AVG_AMT * T1.quantity AS RMB_COST_AMT';
 
 
 ELSE NULL;
  
 END IF;
  
  
  --清空目标表数据
 DBMS_OUTPUT.PUT_LINE('-----清空临时目标表开始------'); 
 EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_TO_TABLE;
 
 DBMS_OUTPUT.PUT_LINE('-----清空临时目标表结束------'); 
  
  
  
  V_SQL := '
  WITH SPART_RAW_DATA AS (
  SELECT 
	T1.PERIOD_ID ,
	T1.PARENTPARTNUMBER AS SPART_CODE,
	T1.ITEM_CODE ,
	T1.ITEM_CN_NAME , 
	T1.LEVEL_REL,
	'||AMT_PARA||'
	,''SPART'' COST_TYPE
  FROM '||V_FROM_TABLE||' T1
  INNER JOIN '||V_JOIN_TABLE||' T2
  ON T1.PRIMARY_ID = T2.PRIMARY_ID  
  INNER JOIN  DMDIM.DM_DIM_PRODUCT_D CP --重量级团队匹配维表(9月需求调整)
 ON T1.PROD_KEY = CP.PROD_KEY
 AND CP.LV0_PROD_RND_TEAM_CODE = ''104364''
 '||PARA||'
  AND CP.LV1_PROD_RND_TEAM_CODE IN(''100011'',''134557'',''101775'',''137565'',	''100001'',	''133277'')
  WHERE T1.LEVEL_REL = ''X''
  AND SUBSTR(T1.PERIOD_ID ,0,4) IN ( ''2022'',''2023'')
  
  
  ),
  
  
  CAIGOU_RAW_DATA AS (
  SELECT 
	T1.PERIOD_ID ,
	T1.ITEM_CODE ,
	T1.ITEM_CN_NAME , 
	T1.LEVEL_REL,
	'||AMT_PARA||'
	,''采购成本'' COST_TYPE
  FROM '||V_FROM_TABLE||' T1
  INNER JOIN '||V_JOIN_TABLE||' T2
  ON T1.PRIMARY_ID = T2.PRIMARY_ID
  '||PARA||'
  INNER JOIN  DMDIM.DM_DIM_PRODUCT_D CP --重量级团队匹配维表(9月需求调整)
        ON T1.PROD_KEY = CP.PROD_KEY

  WHERE T1.LEVEL_REL = ''X''
  AND MODEL_NUM IN (''P'', ''SI'')
   '||PARA||'
  AND SUBSTR(T1.PERIOD_ID ,0,4) IN ( ''2022'',''2023'')
  AND CP.LV0_PROD_RND_TEAM_CODE = ''104364''
  AND CP.LV1_PROD_RND_TEAM_CODE IN(''100011'',''134557'',''101775'',''137565'',	''100001'',	''133277'')
  

  ),
  ZHIZAO_RAW_DATA AS (
    SELECT 
	T1.PERIOD_ID ,
	T1.ITEM_CODE ,
	T1.ITEM_CN_NAME , 
	T1.LEVEL_REL,
	'||M_AMT_PARA||',
	''制造成本'' COST_TYPE
  FROM '||V_FROM_TABLE||' T1
         INNER JOIN (SELECT DISTINCT IM.PERIOD_ID,
                                    IM.COST_TYPE_NAME,
                                    IM.MATERIAL_ID,
                                    DM.MATERIAL_CODE,
                                    IM.INVENTORY_ORG_ID,
                                    IO.INVENTORY_ORG_CODE,
                                    (NVL(IM.TL_RESOURCE,0) +
                                    NVL(IM.TL_OUTSIDE_PROCESSING,0) +
                                    NVL(IM.TL_OVERHEAD,0)) AS RMB_AVG_AMT
                      FROM FIN_DM_OPT_FOI.DWL_PROD_INV_MATERIAL_COST_I IM
                      LEFT JOIN DWRDIM.DWR_DIM_INVENTORY_ORG_D IO
                        ON IM.INVENTORY_ORG_ID = IO.INVENTORY_ORG_ID
                      JOIN DWRDIM.DWR_DIM_MATERIAL_D DM
                        ON IM.MATERIAL_ID = DM.MATERIAL_ID
                       WHERE (NVL(IM.TL_RESOURCE,33333) + NVL(IM.TL_OUTSIDE_PROCESSING,33333) + NVL(IM.TL_OVERHEAD,33333)) <> 99999  -- 11月版本调整  物料标准成本卷积历史
                       AND IO.INVENTORY_ORG_CODE = ''H80'' --获取其INVENTORY_ORG_CODE(库存组织代码)作为筛选字段=NY1  --202405版本修改 原为H80
                       AND IM.COST_TYPE_NAME = ''Group''
                       AND DM.SCD_ACTIVE_IND = 1
					   --AND IM.BASED_ON_ROLLUP_FLAG = 1   --202405版本新增
					   ) IM
            ON T1.ITEM_CODE = IM.MATERIAL_CODE
           AND T1.PERIOD_ID = IM.PERIOD_ID
		   '||PARA||'
		   AND CAST(SUBSTR(TO_CHAR(T1.PERIOD_ID),1,4) AS BIGINT) >= YEAR(CURRENT_TIMESTAMP) -3 
		   
		   INNER JOIN  DMDIM.DM_DIM_PRODUCT_D CP --重量级团队匹配维表(9月需求调整)
        ON T1.PROD_KEY = CP.PROD_KEY
       AND CP.LV0_PROD_RND_TEAM_CODE = ''104364''
	   AND CP.LV1_PROD_RND_TEAM_CODE IN(''100011'',''134557'',''101775'',''137565'',	''100001'',	''133277'')
	   AND T1.MODEL_NUM IN (''AI'', ''PH'', ''FG'')
	   AND SUBSTR(T1.PERIOD_ID ,0,4) IN ( ''2022'',''2023'')
		   )
  ,
ITEM_SUM_AMT AS (
  SELECT 	
	PERIOD_ID ,
	'''' SPART_CODE,
	ITEM_CODE ,
	ITEM_CN_NAME , 
	LEVEL_REL,
	SUM(RMB_COST_AMT) AS SUM_AMT,
	COST_TYPE
	FROM CAIGOU_RAW_DATA
	GROUP BY 
	PERIOD_ID ,
	ITEM_CODE ,
	ITEM_CN_NAME ,
	LEVEL_REL,
	COST_TYPE
	UNION ALL 
	 SELECT 	
	PERIOD_ID ,
	'''' SPART_CODE,
	ITEM_CODE ,
	ITEM_CN_NAME , 
	LEVEL_REL,
	SUM(RMB_COST_AMT) AS SUM_AMT,
	COST_TYPE
	FROM ZHIZAO_RAW_DATA
	GROUP BY 
	PERIOD_ID ,
	ITEM_CODE ,
	ITEM_CN_NAME ,
	LEVEL_REL,
	COST_TYPE
),
SPART_SUM_AMT AS (
  SELECT 	
	PERIOD_ID ,
	SPART_CODE,
	'''' AS ITEM_CODE ,
	'''' AS ITEM_CN_NAME , 
	COST_TYPE,
	SUM(RMB_COST_AMT) AS SUM_AMT,
	''SPART'' AS LEVEL
	FROM SPART_RAW_DATA
	GROUP BY 
	PERIOD_ID ,
	SPART_CODE,
	COST_TYPE
),
RESULT_DATE AS (
SELECT * FROM ITEM_SUM_AMT
UNION ALL 
SELECT * FROM SPART_SUM_AMT
)
INSERT INTO '||V_TO_TABLE||' (
    PERIOD_ID ,
	SPART_CODE,
    ITEM_CODE ,
    ITEM_CN_NAME ,
    LEVEL_REL ,
	RMB_COST_AMT,
	COST_TYPE,
    CREATED_BY ,
    CREATION_DATE ,
    LAST_UPDATED_BY ,
	LAST_UPDATE_DATE,
    DEL_FLAG 
	)
	SELECT 	
	PERIOD_ID ,
	SPART_CODE,
	ITEM_CODE ,
	ITEM_CN_NAME , 
	LEVEL_REL,
	SUM_AMT,
	COST_TYPE,
	-1 AS CREATED_BY,
    CURRENT_TIMESTAMP AS CREATION_DATE,
    -1 AS LAST_UPDATED_BY,
    CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
    ''N'' AS DEL_FLAG
	FROM RESULT_DATE
	';
	DBMS_OUTPUT.PUT_LINE(V_SQL); 
	EXECUTE IMMEDIATE V_SQL;
	


  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
  
  RETURN 'SUCCESS';
        
    EXCEPTION
    WHEN OTHERS THEN
    X_RESULT_STATUS := '0';
    
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME, 
    F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );
   
END
$$
/

