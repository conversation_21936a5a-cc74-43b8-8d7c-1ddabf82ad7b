-- Name: f_dm_foc_total_annual_weight; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_total_annual_weight(f_industry_flag character varying, f_dimension_type character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2023-10-22
  创建人  ：唐钦
  背景描述：总成本权重表/成本分布图(年度分析-一览表)
  参数描述：x_success_flag ：是否成功
  事例    ：select opt_fcst.F_DM_FOC_TOTAL_ANNUAL_WEIGHT('U')
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_TOTAL_ANNUAL_WEIGHT'; --存储过程名称
  V_STEP_NUM INT := 0;
  V_FROM_TABLE VARCHAR2(500);
  V_FROM1_TABLE VARCHAR2(500);
  V_TO_TABLE VARCHAR2(500);
  V_VERSION_ID BIGINT;
  V_SQL TEXT;
  V_DIMENSION_CODE VARCHAR(500);
  V_IN_DIMENSION_CODE VARCHAR(500);
  V_DIMENSION_CN_NAME VARCHAR(500);
  V_IN_DIMENSION_CN_NAME VARCHAR(500);
  V_DIMENSION_SUBCATEGORY_CODE VARCHAR(500);
  V_IN_DIMENSION_SUBCATEGORY_CODE VARCHAR(500);
  V_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(500);
  V_IN_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(500);
  V_DIMENSION_SUB_DETAIL_CODE VARCHAR(500);
  V_IN_DIMENSION_SUB_DETAIL_CODE VARCHAR(500);
  V_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(500);
  V_IN_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(500);
  V_L1_NAME VARCHAR(500);
  V_IN_L1_NAME VARCHAR(500);
  V_L2_NAME VARCHAR(500);
  V_IN_L2_NAME VARCHAR(500);
  V_REL_DIMENSION_CODE VARCHAR(500);
  V_REL_DIMENSION_SUBCATEGORY_CODE VARCHAR(500);
  V_REL_DIMENSION_SUB_DETAIL_CODE VARCHAR(500);
  V_REL_L1_NAME VARCHAR(500);
  V_REL_L2_NAME VARCHAR(500);
  V_GROUP_LEVEL VARCHAR(500);
  V_PARENT_AMT VARCHAR(500);
  V_GRO_NUM INT;
  V_SPART_CODE VARCHAR(200);
  V_SPART_NAME VARCHAR(200);
  V_IN_SPART VARCHAR(200);
  V_REL_SPART VARCHAR(500);
  
  -- 202405版本新增
  V_DIFF_COLUMN_CODE VARCHAR(200);
  V_DIFF_COLUMN_NAME VARCHAR(200);
  V_IN_DIFF_COLUMN VARCHAR(200);
  V_REL_DIFF_COLUMN VARCHAR(500);
  V_VERSION_TABLE VARCHAR(100);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  -- 判断不同颗粒度入参，变量赋值不同
  IF F_DIMENSION_TYPE = 'U' THEN
      V_GRO_NUM := 2;
  ELSIF F_DIMENSION_TYPE = 'P' THEN
      V_GRO_NUM := 2;
      V_L1_NAME := 'L1_NAME,';
      V_L2_NAME := 'L2_NAME,';
      V_IN_L1_NAME := 'T1.L1_NAME,';
      V_IN_L2_NAME := 'T1.L2_NAME,';
      V_REL_L1_NAME := ' AND NVL(T1.L1_NAME,''SNULL'') = NVL(T2.L1_NAME,''SNULL'')';
      V_REL_L2_NAME := ' AND NVL(T1.L2_NAME,''SNULL'') = NVL(T2.L2_NAME,''SNULL'')';
  ELSIF F_DIMENSION_TYPE = 'D' THEN
      V_GRO_NUM := 5;
      V_SPART_CODE := 'SPART_CODE,';
      V_SPART_NAME := 'SPART_CN_NAME,';
      V_IN_SPART := 'T1.SPART_CODE,T1.SPART_CN_NAME,';
      V_REL_SPART := ' AND NVL(T1.SPART_CODE,''S1'') = NVL(T2.SPART_CODE,''S1'') ';
      V_DIMENSION_CODE := 'DIMENSION_CODE,';
      V_IN_DIMENSION_CODE := 'T1.DIMENSION_CODE,';
      V_REL_DIMENSION_CODE := ' AND NVL(T1.DIMENSION_CODE,''SNULL'') = NVL(T2.DIMENSION_CODE,''SNULL'')';
      V_DIMENSION_CN_NAME := 'DIMENSION_CN_NAME,';
      V_IN_DIMENSION_CN_NAME := 'T1.DIMENSION_CN_NAME,';
      V_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE,';
      V_IN_DIMENSION_SUBCATEGORY_CODE := 'T1.DIMENSION_SUBCATEGORY_CODE,';
      V_REL_DIMENSION_SUBCATEGORY_CODE := ' AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''SNULL'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''SNULL'')';
      V_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
      V_IN_DIMENSION_SUBCATEGORY_CN_NAME := 'T1.DIMENSION_SUBCATEGORY_CN_NAME,';
      V_DIMENSION_SUB_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE,';
      V_IN_DIMENSION_SUB_DETAIL_CODE := 'T1.DIMENSION_SUB_DETAIL_CODE,';
      V_REL_DIMENSION_SUB_DETAIL_CODE := ' AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''SNULL'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''SNULL'')';
      V_DIMENSION_SUB_DETAIL_CN_NAME := 'DIMENSION_SUB_DETAIL_CN_NAME,';
      V_IN_DIMENSION_SUB_DETAIL_CN_NAME := 'T1.DIMENSION_SUB_DETAIL_CN_NAME,';
  -- 当产业项目标识为：E时，加COA层级变量
   IF F_INDUSTRY_FLAG = 'E' THEN 
      V_DIFF_COLUMN_CODE := 'COA_CODE,';
      V_DIFF_COLUMN_NAME := 'COA_CN_NAME,';
      V_IN_DIFF_COLUMN := 'T1.COA_CODE,T1.COA_CN_NAME,';
      V_REL_DIFF_COLUMN := ' AND NVL(T1.COA_CODE,''S2'') = NVL(T2.COA_CODE,''S2'') ';
  END IF;
  END IF;
   
 -- 判断产业项目为ICT还是数字能源
  IF F_INDUSTRY_FLAG = 'I' THEN   -- ICT  
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T';
  -- 判断不同颗粒度入参，变量赋值不同
   IF F_DIMENSION_TYPE = 'U' THEN
      V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ANNUAL_WEIGHT_T';
      V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_ANNUAL_WEIGHT_T';
      V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_ANNUAL_WEIGHT_T';
   ELSIF F_DIMENSION_TYPE = 'P' THEN
      V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_ANNUAL_WEIGHT_T';
      V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_ANNUAL_WEIGHT_T';
      V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_PFT_ANNUAL_WEIGHT_T';
   ELSIF F_DIMENSION_TYPE = 'D' THEN
      V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_ANNUAL_WEIGHT_T';
      V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_ANNUAL_WEIGHT_T';
      V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_DMS_ANNUAL_WEIGHT_T';
   END IF;
  ELSIF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源 
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T';
  -- 判断不同颗粒度入参，变量赋值不同
   IF F_DIMENSION_TYPE = 'U' THEN
      V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_ANNUAL_WEIGHT_T';
      V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_ANNUAL_WEIGHT_T';
      V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_ANNUAL_WEIGHT_T';
   ELSIF F_DIMENSION_TYPE = 'P' THEN
      V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_ANNUAL_WEIGHT_T';
      V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_ANNUAL_WEIGHT_T';
      V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_PFT_ANNUAL_WEIGHT_T';
   ELSIF F_DIMENSION_TYPE = 'D' THEN
      V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_ANNUAL_WEIGHT_T';
      V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_ANNUAL_WEIGHT_T';
      V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_DMS_ANNUAL_WEIGHT_T';
   END IF;
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- IAS 
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T';
  -- 判断不同颗粒度入参，变量赋值不同
   IF F_DIMENSION_TYPE = 'U' THEN
      V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_ANNUAL_WEIGHT_T';
      V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_ANNUAL_WEIGHT_T';
      V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_ANNUAL_WEIGHT_T';
   ELSIF F_DIMENSION_TYPE = 'P' THEN
      V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_ANNUAL_WEIGHT_T';
      V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_ANNUAL_WEIGHT_T';
      V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_PFT_ANNUAL_WEIGHT_T';
   ELSIF F_DIMENSION_TYPE = 'D' THEN
      V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_ANNUAL_WEIGHT_T';
      V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_ANNUAL_WEIGHT_T';
      V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_DMS_ANNUAL_WEIGHT_T';
   END IF;
  END IF;
   
  -- 版本号赋值
  V_SQL := '
    SELECT VERSION_ID 
    FROM
        '||V_VERSION_TABLE||'
    WHERE
        DEL_FLAG = ''N''
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = ''CATEGORY''
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1';
    EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
    
  --1.删除目标表数据:
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;

  --1.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除'||V_TO_TABLE||'表版本号为：'||V_VERSION_ID||'的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 建临时表
    DROP TABLE IF EXISTS PUR_MADE_AMT_TMP;
    CREATE TEMPORARY TABLE PUR_MADE_AMT_TMP (
        PERIOD_YEAR BIGINT,
        DIMENSION_CODE VARCHAR(500),
        DIMENSION_CN_NAME VARCHAR(2000),
        DIMENSION_SUBCATEGORY_CODE VARCHAR(500),
        DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(2000),
        DIMENSION_SUB_DETAIL_CODE VARCHAR(500),
        DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(2000),
        SPART_CODE VARCHAR(200),
        SPART_CN_NAME VARCHAR(200),
        COA_CODE VARCHAR(200),
        COA_CN_NAME VARCHAR(200),
        PROD_RND_TEAM_CODE    VARCHAR(50),
        PROD_RND_TEAM_CN_NAME VARCHAR(200),
        L1_NAME VARCHAR(200),
        L2_NAME VARCHAR(200),
        GROUP_CODE CHARACTER VARYING(50),
        GROUP_CN_NAME CHARACTER VARYING(2000),
        GROUP_LEVEL CHARACTER VARYING(50),
        RMB_COST_AMT NUMERIC,
        ABSOLUTE_PARENT_AMT NUMERIC,
        PARENT_CODE VARCHAR(50),
        PARENT_CN_NAME VARCHAR(2000),
        VIEW_FLAG VARCHAR(2),
        CALIBER_FLAG VARCHAR(2),
        OVERSEA_FLAG VARCHAR(2),
        LV0_PROD_LIST_CODE VARCHAR(50),
        LV0_PROD_LIST_CN_NAME VARCHAR(200),
        COST_TYPE VARCHAR(2)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(PARENT_CODE,GROUP_CODE);

    --2.写入日志
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_NUM,
     F_CAL_LOG_DESC => '采购成本+制造成本数据临时表创建完成',
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS');  

  -- 取出采购成本以及制造成本的数据
  V_SQL := '
  INSERT INTO PUR_MADE_AMT_TMP(
        PERIOD_YEAR,
        '||V_DIMENSION_CODE
        ||V_DIMENSION_CN_NAME
        ||V_DIMENSION_SUBCATEGORY_CODE
        ||V_DIMENSION_SUBCATEGORY_CN_NAME
        ||V_DIMENSION_SUB_DETAIL_CODE
        ||V_DIMENSION_SUB_DETAIL_CN_NAME
        ||V_SPART_CODE
        ||V_SPART_NAME
        ||V_DIFF_COLUMN_CODE
        ||V_DIFF_COLUMN_NAME||'
        PROD_RND_TEAM_CODE,
        PROD_RND_TEAM_CN_NAME,
        '||V_L1_NAME
        ||V_L2_NAME||'
        GROUP_CODE,
        GROUP_CN_NAME,
        GROUP_LEVEL,
        RMB_COST_AMT,
        ABSOLUTE_PARENT_AMT,
        PARENT_CODE,
        PARENT_CN_NAME,
        VIEW_FLAG,
        CALIBER_FLAG,
        OVERSEA_FLAG,
        LV0_PROD_LIST_CODE,
        LV0_PROD_LIST_CN_NAME,
        COST_TYPE
        )
   SELECT CAST(PERIOD_YEAR AS BIGINT) AS PERIOD_YEAR,
          '||V_DIMENSION_CODE
          ||V_DIMENSION_CN_NAME
          ||V_DIMENSION_SUBCATEGORY_CODE
          ||V_DIMENSION_SUBCATEGORY_CN_NAME
          ||V_DIMENSION_SUB_DETAIL_CODE
          ||V_DIMENSION_SUB_DETAIL_CN_NAME
          ||V_SPART_CODE
          ||V_SPART_NAME
          ||V_DIFF_COLUMN_CODE
          ||V_DIFF_COLUMN_NAME||'
          PROD_RND_TEAM_CODE,
          PROD_RND_TEAM_CN_NAME,
          '||V_L1_NAME
          ||V_L2_NAME||'
          GROUP_CODE,
          GROUP_CN_NAME,
          GROUP_LEVEL,
          RMB_COST_AMT,
          ABSOLUTE_PARENT_AMT,
          PARENT_CODE,
          PARENT_CN_NAME,
          VIEW_FLAG,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME,
          ''P'' AS COST_TYPE
       FROM '||V_FROM_TABLE||'
       WHERE VERSION_ID = '||V_VERSION_ID||'
       AND GROUP_LEVEL NOT IN (''ITEM'',''CATEGORY'',''MODL'',''CEG'')
   UNION ALL
   SELECT CAST(PERIOD_YEAR AS BIGINT) AS PERIOD_YEAR,
          '||V_DIMENSION_CODE
          ||V_DIMENSION_CN_NAME
          ||V_DIMENSION_SUBCATEGORY_CODE
          ||V_DIMENSION_SUBCATEGORY_CN_NAME
          ||V_DIMENSION_SUB_DETAIL_CODE
          ||V_DIMENSION_SUB_DETAIL_CN_NAME
          ||V_SPART_CODE
          ||V_SPART_NAME
          ||V_DIFF_COLUMN_CODE
          ||V_DIFF_COLUMN_NAME||'
          PROD_RND_TEAM_CODE,
          PROD_RND_TEAM_CN_NAME,
          '||V_L1_NAME
          ||V_L2_NAME||'
          GROUP_CODE,
          GROUP_CN_NAME,
          GROUP_LEVEL,
          RMB_COST_AMT,
          ABSOLUTE_PARENT_AMT,
          PARENT_CODE,
          PARENT_CN_NAME,
          VIEW_FLAG,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME,
          ''M'' AS COST_TYPE
       FROM '||V_FROM1_TABLE||'
       WHERE VERSION_ID = '||V_VERSION_ID||'
       AND GROUP_LEVEL NOT IN (''ITEM'',''SHIPPING_OBJECT'',''MANUFACTURE_OBJECT'')';
       EXECUTE IMMEDIATE V_SQL; 
       DBMS_OUTPUT.PUT_LINE(V_SQL);
  
  -- 写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '采购成本+制造成本的数据，插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');    
   
  -- 分为3类不同层级，进行循环：1：（'L1','L2','DIMENSION','SUBCATEGORY'）、2：('LV3','LV2','LV1','LV0'）、3：（'SUB_DETAIL'）
        FOR GRO_NUM IN 1 .. V_GRO_NUM LOOP
            IF GRO_NUM = 1 THEN
                V_PARENT_AMT := '
                SUM(SUM(RMB_COST_AMT)) OVER(PARTITION BY PERIOD_YEAR,PROD_RND_TEAM_CODE,'||V_DIFF_COLUMN_CODE||'GROUP_LEVEL,PARENT_CODE,VIEW_FLAG,CALIBER_FLAG,OVERSEA_FLAG,LV0_PROD_LIST_CODE) AS PARENT_AMT, ';
                V_GROUP_LEVEL := ' 
                WHERE T1.GROUP_LEVEL IN (''L1'',''L2'',''DIMENSION'',''SUBCATEGORY'') ';    
            ELSIF GRO_NUM = 2 THEN
                V_PARENT_AMT := '
                SUM(SUM(RMB_COST_AMT)) OVER(PARTITION BY PERIOD_YEAR,GROUP_LEVEL,PARENT_CODE,VIEW_FLAG,CALIBER_FLAG,OVERSEA_FLAG,LV0_PROD_LIST_CODE) AS PARENT_AMT, ';
                V_GROUP_LEVEL := ' 
                WHERE T1.GROUP_LEVEL IN (''LV4'',''LV3'',''LV2'',''LV1'',''LV0'') ';    -- 202407新增LV4层级
            ELSIF GRO_NUM = 3 THEN
                V_PARENT_AMT := '
                SUM(SUM(RMB_COST_AMT)) OVER(PARTITION BY PERIOD_YEAR,PROD_RND_TEAM_CODE,'||V_DIFF_COLUMN_CODE||'DIMENSION_SUBCATEGORY_CODE,DIMENSION_CODE,GROUP_LEVEL,PARENT_CODE,VIEW_FLAG,CALIBER_FLAG,OVERSEA_FLAG,LV0_PROD_LIST_CODE) AS PARENT_AMT, ';
                V_GROUP_LEVEL := ' 
                WHERE T1.GROUP_LEVEL IN (''SUB_DETAIL'') ';   
            ELSIF GRO_NUM = 4 THEN
                V_PARENT_AMT := '
                SUM(SUM(RMB_COST_AMT)) OVER(PARTITION BY PERIOD_YEAR,PROD_RND_TEAM_CODE,'||V_DIFF_COLUMN_CODE||'DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUBCATEGORY_CODE,DIMENSION_CODE,GROUP_LEVEL,PARENT_CODE,VIEW_FLAG,CALIBER_FLAG,OVERSEA_FLAG,LV0_PROD_LIST_CODE) AS PARENT_AMT, ';
                V_GROUP_LEVEL := ' 
                WHERE T1.GROUP_LEVEL IN (''SPART'') ';   
            ELSIF GRO_NUM = 5 THEN
                V_PARENT_AMT := '
                SUM(SUM(RMB_COST_AMT)) OVER(PARTITION BY PERIOD_YEAR,PROD_RND_TEAM_CODE,GROUP_LEVEL,PARENT_CODE,VIEW_FLAG,CALIBER_FLAG,OVERSEA_FLAG,LV0_PROD_LIST_CODE) AS PARENT_AMT, ';
                V_GROUP_LEVEL := ' 
                WHERE T1.GROUP_LEVEL IN (''COA'') ';   
            END IF;        
  -- 权重数据计算逻辑
  V_SQL := '
  INSERT INTO '||V_TO_TABLE||'(
         VERSION_ID,
         PERIOD_YEAR,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
         '||V_DIMENSION_CODE
         ||V_DIMENSION_CN_NAME
         ||V_DIMENSION_SUBCATEGORY_CODE
         ||V_DIMENSION_SUBCATEGORY_CN_NAME
         ||V_DIMENSION_SUB_DETAIL_CODE
         ||V_DIMENSION_SUB_DETAIL_CN_NAME
         ||V_SPART_CODE
         ||V_SPART_NAME
         ||V_DIFF_COLUMN_CODE
         ||V_DIFF_COLUMN_NAME
         ||V_L1_NAME
         ||V_L2_NAME||'
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         WEIGHT_RATE,
         RMB_COST_AMT,
         COST_TYPE,
         ABSOLUTE_WEIGHT,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         VIEW_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         PERCENTAGE,
         APPEND_FLAG
         )
  WITH TOTAL_AMT_TMP AS(
  -- 汇总采购成本+制造成本的总金额
       SELECT PERIOD_YEAR,
             '||V_DIMENSION_CODE
             ||V_DIMENSION_CN_NAME
             ||V_DIMENSION_SUBCATEGORY_CODE
             ||V_DIMENSION_SUBCATEGORY_CN_NAME
             ||V_DIMENSION_SUB_DETAIL_CODE
             ||V_DIMENSION_SUB_DETAIL_CN_NAME
             ||V_SPART_CODE
             ||V_SPART_NAME
             ||V_DIFF_COLUMN_CODE
             ||V_DIFF_COLUMN_NAME||'
             PROD_RND_TEAM_CODE,
             PROD_RND_TEAM_CN_NAME,
             '||V_L1_NAME
             ||V_L2_NAME||'
             GROUP_CODE,
             GROUP_CN_NAME,
             GROUP_LEVEL,
             SUM(RMB_COST_AMT) AS RMB_COST_AMT,
             '||V_PARENT_AMT||'
             PARENT_CODE,
             PARENT_CN_NAME,
             VIEW_FLAG,
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME
          FROM PUR_MADE_AMT_TMP T1
          '||V_GROUP_LEVEL||'
          GROUP BY PERIOD_YEAR,
                   '||V_DIMENSION_CODE
                   ||V_DIMENSION_CN_NAME
                   ||V_DIMENSION_SUBCATEGORY_CODE
                   ||V_DIMENSION_SUBCATEGORY_CN_NAME
                   ||V_DIMENSION_SUB_DETAIL_CODE
                   ||V_DIMENSION_SUB_DETAIL_CN_NAME
                   ||V_SPART_CODE
                   ||V_SPART_NAME
                   ||V_DIFF_COLUMN_CODE
                   ||V_DIFF_COLUMN_NAME||'
                   PROD_RND_TEAM_CODE,
                   PROD_RND_TEAM_CN_NAME,
                   '||V_L1_NAME
                   ||V_L2_NAME||'
                   GROUP_CODE,
                   GROUP_CN_NAME,
                   GROUP_LEVEL,
                   PARENT_CODE,
                   PARENT_CN_NAME,
                   VIEW_FLAG,
                   CALIBER_FLAG,
                   OVERSEA_FLAG,
                   LV0_PROD_LIST_CODE,
                   LV0_PROD_LIST_CN_NAME
        )
   SELECT 
          '||V_VERSION_ID||' AS VERSION_ID,
          T1.PERIOD_YEAR,
          T1.PROD_RND_TEAM_CODE,
          T1.PROD_RND_TEAM_CN_NAME,
          '||V_IN_DIMENSION_CODE
          ||V_DIMENSION_CN_NAME
          ||V_IN_DIMENSION_SUBCATEGORY_CODE
          ||V_DIMENSION_SUBCATEGORY_CN_NAME
          ||V_IN_DIMENSION_SUB_DETAIL_CODE
          ||V_DIMENSION_SUB_DETAIL_CN_NAME
          ||V_IN_SPART
          ||V_IN_DIFF_COLUMN
          ||V_IN_L1_NAME
          ||V_IN_L2_NAME||'
          T1.GROUP_CODE,
          T1.GROUP_CN_NAME,
          T1.GROUP_LEVEL,
          DECODE(NVL(T1.RMB_COST_AMT,0),0,0,T1.RMB_COST_AMT / NULLIF(T1.PARENT_AMT,0)) AS WEIGHT_RATE,
          NVL(T1.RMB_COST_AMT,0) AS RMB_COST_AMT,
          ''T'' AS COST_TYPE,
          DECODE(NVL(T1.RMB_COST_AMT,0),0,0,T1.RMB_COST_AMT / NULLIF(T2.ABSOLUTE_PARENT_AMT,0)) AS ABSOLUTE_WEIGHT,
          T1.PARENT_CODE,
          T1.PARENT_CN_NAME,
          -1 AS CREATED_BY,
          CURRENT_TIMESTAMP AS CREATION_DATE,
          -1 AS LAST_UPDATED_BY,
          CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
          ''N'' AS DEL_FLAG,
          T1.VIEW_FLAG,
          T1.CALIBER_FLAG,
          T1.OVERSEA_FLAG,
          T1.LV0_PROD_LIST_CODE,
          T1.LV0_PROD_LIST_CN_NAME,
          NULL AS PERCENTAGE,
          DECODE(T1.RMB_COST_AMT,NULL,''Y'',''N'') AS APPEND_FLAG
       FROM TOTAL_AMT_TMP T1
       LEFT JOIN (
                  SELECT VERSION_ID,
                         PERIOD_YEAR,
                         VIEW_FLAG,
                         CALIBER_FLAG,
                         OVERSEA_FLAG,
                         LV0_PROD_LIST_CODE,
                         GRANULARITY_TYPE,
                         SUM(ABSOLUTE_PARENT_AMT) AS ABSOLUTE_PARENT_AMT
                      FROM FIN_DM_OPT_FOI.DM_FOC_ABSOLUTE_AMT_T
                      WHERE VERSION_ID =  '||V_VERSION_ID||'
                      AND GRANULARITY_TYPE = '''||F_DIMENSION_TYPE||'''
                      GROUP BY VERSION_ID,
                               PERIOD_YEAR,
                               VIEW_FLAG,
                               CALIBER_FLAG,
                               OVERSEA_FLAG,
                               LV0_PROD_LIST_CODE,
                               GRANULARITY_TYPE
                   ) T2
       ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
       AND T1.VIEW_FLAG = T2.VIEW_FLAG
       AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
   UNION ALL
   -- 成本分布图逻辑
   SELECT 
           '||V_VERSION_ID||' AS VERSION_ID,
           T1.PERIOD_YEAR,
           T1.PROD_RND_TEAM_CODE,
           T1.PROD_RND_TEAM_CN_NAME,
           '||V_IN_DIMENSION_CODE
          ||V_IN_DIMENSION_CN_NAME
          ||V_IN_DIMENSION_SUBCATEGORY_CODE
          ||V_IN_DIMENSION_SUBCATEGORY_CN_NAME
          ||V_IN_DIMENSION_SUB_DETAIL_CODE
          ||V_IN_DIMENSION_SUB_DETAIL_CN_NAME
          ||V_IN_SPART
          ||V_IN_DIFF_COLUMN
          ||V_IN_L1_NAME
          ||V_IN_L2_NAME||'
           T1.GROUP_CODE,
           T1.GROUP_CN_NAME,
           T1.GROUP_LEVEL,
           NULL AS WEIGHT_RATE,
           NVL(T1.RMB_COST_AMT,0) AS RMB_COST_AMT,
           T1.COST_TYPE,
           NULL AS ABSOLUTE_WEIGHT,
           T1.PARENT_CODE,
           T1.PARENT_CN_NAME,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           T1.VIEW_FLAG,
           T1.CALIBER_FLAG,
           T1.OVERSEA_FLAG,
           T1.LV0_PROD_LIST_CODE,
           T1.LV0_PROD_LIST_CN_NAME,
           DECODE(NVL(T1.RMB_COST_AMT,0),0,0,T1.RMB_COST_AMT / NULLIF(T2.RMB_COST_AMT,0)) AS PERCENTAGE,
           DECODE(T1.RMB_COST_AMT,NULL,''Y'',''N'') AS APPEND_FLAG
      FROM PUR_MADE_AMT_TMP T1
      LEFT JOIN TOTAL_AMT_TMP T2
      ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
      AND T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE
      AND T1.GROUP_CODE = T2.GROUP_CODE
      AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
      AND T1.PARENT_CODE = T2.PARENT_CODE
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
      AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
      AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
      '||V_REL_DIMENSION_CODE
      ||V_REL_DIMENSION_SUBCATEGORY_CODE
      ||V_REL_DIMENSION_SUB_DETAIL_CODE
      ||V_REL_SPART
      ||V_REL_DIFF_COLUMN
      ||V_REL_L1_NAME
      ||V_REL_L2_NAME||'
      '||V_GROUP_LEVEL;
 
      DBMS_OUTPUT.PUT_LINE(V_SQL);
      EXECUTE IMMEDIATE V_SQL;
  
  -- 写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '第'||GRO_NUM||'次循环，总成本权重值/成本分布图数据计算完成，插入结果表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');    
   END LOOP;

    --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
 
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END$$
/

