-- Name: f_dm_foc_mid_month_item_flag_0119; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_mid_month_item_flag_0119(f_caliber_flag character varying, f_dimension_type character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
创建时间:2023/03/21
创建人  :刘必华
最后修改时间:2023/05/31
最后修改人:曹昆
背景描述:分视角统计ITEM的月卷积发货额
参数描述:f_caliber_flag : 业务口径(R：收入时点,C：发货成本), f_dimension_type : 维度类型(通用颗粒度：U,盈利颗粒度：P), x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T(发货成本),FIN_DM_OPT_FOI.DM_FOC_REVENUE_ITEM_SHIP_DTL_T(收入时点)
目标表:FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T(通用颗粒度),FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_ITEM_T(盈利颗粒度),FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T(量纲颗粒度)
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_MID_MONTH_ITEM()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_MID_MONTH_ITEM_FLAG'; --存储过程名称
  V_VERSION_ID BIGINT; --版本号, 取自 FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T的版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_SQL        TEXT;   --SQL逻辑
  V_LV1_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_LV2_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_LV3_PROD_RND_TEAM_CODE VARCHAR(50); -- 7月版本需求新增
  V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_L1_NAME VARCHAR(200);
  V_L2_NAME VARCHAR(200);
  V_IN_LV1_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_LV2_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_L1_NAME VARCHAR(200);
  V_IN_L2_NAME VARCHAR(200);
  V_INSERT_LV1_PROD_RND_TEAM_CODE VARCHAR(500);
  V_INSERT_LV2_PROD_RND_TEAM_CODE VARCHAR(500);
  V_INSERT_LV3_PROD_RND_TEAM_CODE VARCHAR(500);
  V_INSERT_L1_NAME VARCHAR(500);
  V_INSERT_L2_NAME VARCHAR(500);
  V_JOIN_TABLE VARCHAR(500);
  
  -- 7月版本需求新增
  V_FROM_TABLE VARCHAR(50); -- 来源表
  V_TO_TABLE VARCHAR(50); -- 目标表
  V_VIEW_CNT BIGINT; -- 处理通用颗粒度和盈利颗粒度视角数目不同

  -- 9月版本需求新增
  V_DIMENSION_CODE VARCHAR(500);
  V_DIMENSION_CN_NAME VARCHAR(2000);
  V_DIMENSION_EN_NAME VARCHAR(2000);
  V_DIMENSION_SUBCATEGORY_CODE VARCHAR(500);
  V_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(2000);
  V_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(2000);
  V_DIMENSION_SUB_DETAIL_CODE VARCHAR(500);
  V_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(2000);
  V_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(2000);
  V_IN_DIMENSION_CODE VARCHAR(200);
  V_IN_DIMENSION_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_EN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_CODE VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_CODE VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(200);
  V_INSERT_DIMENSION_CODE VARCHAR(500);
  V_INSERT_DIMENSION_SUBCATEGORY_CODE VARCHAR(500);
  V_INSERT_DIMENSION_SUB_DETAIL_CODE VARCHAR(500);
  
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  --对入参合法性判断
  IF F_DIMENSION_TYPE NOT IN ('U','P','D') OR F_CALIBER_FLAG NOT IN ('C','R') THEN
     DBMS_OUTPUT.PUT_LINE ('入参有误，请重新输入！');
     RETURN '0';
  END IF;
  

    
  IF F_DIMENSION_TYPE = 'U' THEN -- 通用颗粒度
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T';--目标表
     V_VIEW_CNT := 3; --通用颗粒度视角从0到3
  ELSIF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_ITEM_T'; --目标表 
     V_VIEW_CNT := 4; --盈利颗粒度视角从0到4
  ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T'; --目标表 
     V_VIEW_CNT := 8; --量纲颗粒度视角从0到8
  ELSE
    NULL;
  END IF;
  
  
   IF F_DIMENSION_TYPE = 'U' AND F_CALIBER_FLAG = 'C'  THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_CU';--来源表
	 
  ELSIF F_DIMENSION_TYPE = 'P' AND F_CALIBER_FLAG = 'C'  THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_CP'; --来源表

  ELSIF F_DIMENSION_TYPE = 'D' AND F_CALIBER_FLAG = 'C'  THEN -- 量纲颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_CD'; --来源表 

	 
   ELSIF F_DIMENSION_TYPE = 'U' AND F_CALIBER_FLAG = 'R'  THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_RU';--来源表

  ELSIF F_DIMENSION_TYPE = 'P' AND F_CALIBER_FLAG = 'R'  THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_RP'; --来源表 

  ELSIF F_DIMENSION_TYPE = 'D' AND F_CALIBER_FLAG = 'R'  THEN -- 量纲颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_RD'; --来源表
 
	  
  ELSE
    NULL;
  END IF;  
  
  
  
  
  
  --清空目标表数据
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' T WHERE T.CALIBER_FLAG = '''||F_CALIBER_FLAG||'''';
  
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');


    --7月版本需求新增
    --重置变量入参
    V_LV1_PROD_RND_TEAM_CODE := 'LV1_PROD_RND_TEAM_CODE,';
    V_LV1_PROD_RD_TEAM_CN_NAME := 'LV1_PROD_RD_TEAM_CN_NAME,';
    V_LV2_PROD_RND_TEAM_CODE := 'LV2_PROD_RND_TEAM_CODE,';
    V_LV2_PROD_RD_TEAM_CN_NAME := 'LV2_PROD_RD_TEAM_CN_NAME,';
    V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
    V_L1_NAME := 'L1_NAME,';
    V_L2_NAME := 'L2_NAME,';

    V_IN_LV1_PROD_RND_TEAM_CODE := 'A.LV1_PROD_RND_TEAM_CODE,';
    V_IN_LV1_PROD_RD_TEAM_CN_NAME :='A.LV1_PROD_RD_TEAM_CN_NAME,';    
    V_IN_LV2_PROD_RND_TEAM_CODE := 'A.LV2_PROD_RND_TEAM_CODE,';
    V_IN_LV2_PROD_RD_TEAM_CN_NAME :='A.LV2_PROD_RD_TEAM_CN_NAME,';
    V_IN_LV3_PROD_RND_TEAM_CODE := 'A.LV3_PROD_RND_TEAM_CODE,';
    V_IN_LV3_PROD_RD_TEAM_CN_NAME := 'A.LV3_PROD_RD_TEAM_CN_NAME,';
    V_IN_L1_NAME := 'A.L1_NAME,';
    V_IN_L2_NAME := 'A.L2_NAME,';

    --9月版本需求新增量纲
    V_DIMENSION_CODE := 'DIMENSION_CODE,';
    V_DIMENSION_CN_NAME := 'DIMENSION_CN_NAME,';
    V_DIMENSION_EN_NAME := 'DIMENSION_EN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_EN_NAME := 'DIMENSION_SUBCATEGORY_EN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME := 'DIMENSION_SUB_DETAIL_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_EN_NAME := 'DIMENSION_SUB_DETAIL_EN_NAME,';
    V_IN_DIMENSION_CODE := 'A.DIMENSION_CODE,';
    V_IN_DIMENSION_CN_NAME := 'A.DIMENSION_CN_NAME,';
    V_IN_DIMENSION_EN_NAME := 'A.DIMENSION_EN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_CODE := 'A.DIMENSION_SUBCATEGORY_CODE,';
    V_IN_DIMENSION_SUBCATEGORY_CN_NAME := 'A.DIMENSION_SUBCATEGORY_CN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_EN_NAME := 'A.DIMENSION_SUBCATEGORY_EN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_CODE := 'A.DIMENSION_SUB_DETAIL_CODE,';
    V_IN_DIMENSION_SUB_DETAIL_CN_NAME := 'A.DIMENSION_SUB_DETAIL_CN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_EN_NAME := 'A.DIMENSION_SUB_DETAIL_EN_NAME,';

    --通用颗粒度的维度时，不需要L1、L2字段和所有量纲字段
    IF F_DIMENSION_TYPE = 'U' THEN 
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';

       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
       
    --盈利颗粒度的维度时，不需要LV3字段和所有量纲字段
    ELSIF F_DIMENSION_TYPE = 'P' THEN
       V_LV3_PROD_RND_TEAM_CODE := '';
       V_LV3_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV3_PROD_RND_TEAM_CODE := '';
       V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
       
       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
        
    --量纲颗粒度的维度时，不需要L1、L2字段
    ELSIF F_DIMENSION_TYPE = 'D' THEN

       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
    ELSE
      NULL;
    END IF;
    

  --版本号赋值.
  V_SQL := 'SELECT VERSION_ID FROM FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T T LIMIT 1 ';
  EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;


 
 -- FOR V_VIEW_NUM IN 0..V_VIEW_CNT LOOP 
    --从上游临时表取数，打上单item品类标签，插入结果表

  --创建单item品类临时表
    DROP TABLE IF EXISTS  DIST_DATA_FLAG;
    CREATE TEMPORARY TABLE  DIST_DATA_FLAG (
        VIEW_FLAG VARCHAR(2),
        LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV3_PROD_RND_TEAM_CODE    VARCHAR(50),
        L1_NAME VARCHAR(200),
        L2_NAME VARCHAR(200),
        DIMENSION_CODE    VARCHAR(500),
        DIMENSION_SUBCATEGORY_CODE    VARCHAR(500),
        DIMENSION_SUB_DETAIL_CODE    VARCHAR(500),
        L3_CEG_CODE    VARCHAR(50),
        L4_CEG_CODE    VARCHAR(50), -- 9月版本需求新增
        CATEGORY_CODE CHARACTER VARYING(50),
        ITEM_CODE CHARACTER VARYING(50),
        OVERSEA_FLAG VARCHAR(2),-- 9月版本需求新增
        LV0_PROD_LIST_CODE VARCHAR(50)
    )
    ON COMMIT PRESERVE ROWS 
    DISTRIBUTE BY ROUNDROBIN;


 
 
----将加工好的数据插入单item品类临时表
    V_SQL := 
    'INSERT INTO DIST_DATA_FLAG
    (VIEW_FLAG,
    OVERSEA_FLAG,
    LV0_PROD_LIST_CODE,
    L3_CEG_CODE,
    L4_CEG_CODE,
    CATEGORY_CODE,
    ITEM_CODE,'||
    V_DIMENSION_SUB_DETAIL_CODE ||
    V_DIMENSION_SUBCATEGORY_CODE ||
    V_DIMENSION_CODE ||
    V_L1_NAME ||
    V_L2_NAME ||
    V_LV1_PROD_RND_TEAM_CODE ||
    V_LV2_PROD_RND_TEAM_CODE ||
    V_LV3_PROD_RND_TEAM_CODE ||'
    LV0_PROD_RND_TEAM_CODE
    )
      --筛选出单item品类的维度信息
      SELECT B.VIEW_FLAG,B.OVERSEA_FLAG,B.LV0_PROD_LIST_CODE,B.L3_CEG_CODE, B.L4_CEG_CODE, B.CATEGORY_CODE, B.ITEM_CODE ,'||
             V_DIMENSION_SUB_DETAIL_CODE || V_DIMENSION_SUBCATEGORY_CODE ||V_DIMENSION_CODE || V_L1_NAME || V_L2_NAME || V_LV1_PROD_RND_TEAM_CODE || V_LV2_PROD_RND_TEAM_CODE || V_LV3_PROD_RND_TEAM_CODE ||' B.LV0_PROD_RND_TEAM_CODE
        FROM (SELECT A.LV0_PROD_RND_TEAM_CODE,'||
                     V_LV1_PROD_RND_TEAM_CODE ||
                     V_LV2_PROD_RND_TEAM_CODE ||
                     V_LV3_PROD_RND_TEAM_CODE ||
                     V_L1_NAME || 
                     V_L2_NAME ||
                     V_DIMENSION_CODE ||
                     V_DIMENSION_SUBCATEGORY_CODE ||
                     V_DIMENSION_SUB_DETAIL_CODE ||'
                     A.L3_CEG_CODE,
                     A.L4_CEG_CODE,
                     A.CATEGORY_CODE,
                     A.ITEM_CODE,
                     A.VIEW_FLAG,
                     A.OVERSEA_FLAG,
                     A.LV0_PROD_LIST_CODE,
                     COUNT(1) OVER(PARTITION BY '||V_DIMENSION_SUB_DETAIL_CODE||V_DIMENSION_SUBCATEGORY_CODE||V_DIMENSION_CODE||V_L1_NAME||V_L2_NAME||V_LV3_PROD_RND_TEAM_CODE||V_LV2_PROD_RND_TEAM_CODE||V_LV1_PROD_RND_TEAM_CODE||'A.LV0_PROD_RND_TEAM_CODE,A.L3_CEG_CODE,A.L4_CEG_CODE, A.CATEGORY_CODE, A.VIEW_FLAG,A.OVERSEA_FLAG,A.LV0_PROD_LIST_CODE) AS ITEM_FLAG
                  FROM (SELECT DISTINCT VIEW_FLAG,OVERSEA_FLAG,LV0_PROD_LIST_CODE,L3_CEG_CODE,L4_CEG_CODE,CATEGORY_CODE,ITEM_CODE,
                               '||V_DIMENSION_SUB_DETAIL_CODE||V_DIMENSION_SUBCATEGORY_CODE||V_DIMENSION_CODE||V_L1_NAME||V_L2_NAME||V_LV3_PROD_RND_TEAM_CODE||V_LV2_PROD_RND_TEAM_CODE||V_LV1_PROD_RND_TEAM_CODE||' LV0_PROD_RND_TEAM_CODE
                          FROM '||V_FROM_TABLE||' T
                          
                          ) A
                     ) B
       WHERE ITEM_FLAG = 1;
    ';
 
 EXECUTE IMMEDIATE V_SQL;
 
 
    V_SQL := 
    'INSERT INTO '|| V_TO_TABLE ||' 
      (VERSION_ID,
       PERIOD_YEAR,
       PERIOD_ID,
       LV0_PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME,'||
       V_LV1_PROD_RND_TEAM_CODE ||
       V_LV1_PROD_RD_TEAM_CN_NAME ||
       V_LV2_PROD_RND_TEAM_CODE ||
       V_LV2_PROD_RD_TEAM_CN_NAME ||
       V_LV3_PROD_RND_TEAM_CODE ||
       V_LV3_PROD_RD_TEAM_CN_NAME ||
       V_L1_NAME ||
       V_L2_NAME ||
       
       V_DIMENSION_CODE ||
       V_DIMENSION_CN_NAME ||
       V_DIMENSION_EN_NAME||
       V_DIMENSION_SUBCATEGORY_CODE ||
       V_DIMENSION_SUBCATEGORY_CN_NAME ||
       V_DIMENSION_SUBCATEGORY_EN_NAME||
       V_DIMENSION_SUB_DETAIL_CODE ||
       V_DIMENSION_SUB_DETAIL_CN_NAME ||
       V_DIMENSION_SUB_DETAIL_EN_NAME ||'
       L3_CEG_CODE,
       L3_CEG_CN_NAME,
       L3_CEG_SHORT_CN_NAME,
       L4_CEG_CODE,
       L4_CEG_CN_NAME,
       L4_CEG_SHORT_CN_NAME,
       CATEGORY_CODE,
       CATEGORY_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       SHIP_QUANTITY,
       RMB_COST_AMT,
       CREATED_BY,
       CREATION_DATE,
       LAST_UPDATED_BY,
       LAST_UPDATE_DATE,
       DEL_FLAG,
       VIEW_FLAG,
       ONLY_ITEM_FLAG,
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME,
       LV0_PROD_LIST_EN_NAME)
   
      SELECT A.VERSION_ID,
                        A.PERIOD_YEAR,
                        A.PERIOD_ID,
                        A.LV0_PROD_RND_TEAM_CODE,
                        A.LV0_PROD_RD_TEAM_CN_NAME,' ||
                        V_IN_LV1_PROD_RND_TEAM_CODE ||
                        V_IN_LV1_PROD_RD_TEAM_CN_NAME ||
                        V_IN_LV2_PROD_RND_TEAM_CODE ||
                        V_IN_LV2_PROD_RD_TEAM_CN_NAME ||
                        V_IN_LV3_PROD_RND_TEAM_CODE ||
                        V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
                        V_IN_L1_NAME ||
                        V_IN_L2_NAME ||
                        V_IN_DIMENSION_CODE ||
                        V_IN_DIMENSION_CN_NAME ||
                        V_IN_DIMENSION_EN_NAME||
                        V_IN_DIMENSION_SUBCATEGORY_CODE ||
                        V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
                        V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
                        V_IN_DIMENSION_SUB_DETAIL_CODE ||
                        V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
                        V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||'
                        A.L3_CEG_CODE,
                        A.L3_CEG_CN_NAME,
                        A.L3_CEG_SHORT_CN_NAME,
                        A.L4_CEG_CODE,
                        A.L4_CEG_CN_NAME,
                        A.L4_CEG_SHORT_CN_NAME,
                        A.CATEGORY_CODE,
                        A.CATEGORY_CN_NAME,
                        A.ITEM_CODE,
                        A.ITEM_CN_NAME,
                        A.SHIP_QUANTITY,
                        A.COST_AMT,
                        -1 AS CREATED_BY,
                        CURRENT_TIMESTAMP AS CREATION_DATE,
                        -1 AS LAST_UPDATED_BY,
                        CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                        ''N'' AS DEL_FLAG,
                        A.VIEW_FLAG,
                        DECODE(B.ITEM_CODE, NULL, ''N'', ''Y'') AS ONLY_ITEM_FLAG,
                        '''||F_CALIBER_FLAG||''',
                        A.OVERSEA_FLAG,
                        A.LV0_PROD_LIST_CODE,
                        A.LV0_PROD_LIST_CN_NAME,
                        A.LV0_PROD_LIST_EN_NAME
                    FROM '||V_FROM_TABLE||' A
               LEFT JOIN DIST_DATA_FLAG B
                     ON A.L3_CEG_CODE = B.L3_CEG_CODE
                    AND A.L4_CEG_CODE = B.L4_CEG_CODE
                    AND A.CATEGORY_CODE = B.CATEGORY_CODE
                    AND A.ITEM_CODE = B.ITEM_CODE
                    AND A.OVERSEA_FLAG = B.OVERSEA_FLAG
                    AND A.VIEW_FLAG = B.VIEW_FLAG
                    AND A.LV0_PROD_LIST_CODE = B.LV0_PROD_LIST_CODE
                    AND A.LV0_PROD_RND_TEAM_CODE = B.LV0_PROD_RND_TEAM_CODE
                   AND NVL(A.LV1_PROD_RND_TEAM_CODE,''SNULL'') = NVL(B.LV1_PROD_RND_TEAM_CODE,''SNULL'')
                   AND NVL(A.LV2_PROD_RND_TEAM_CODE,''SNULL'') = NVL(B.LV2_PROD_RND_TEAM_CODE,''SNULL'')
				   AND NVL(A.LV3_PROD_RND_TEAM_CODE,''SNULL'') = NVL(B.LV3_PROD_RND_TEAM_CODE,''SNULL'')
                   AND NVL(A.L1_NAME,''SNULL'') = NVL(B.L1_NAME,''SNULL'') 
                   AND NVL(A.L2_NAME,''SNULL'') = NVL(B.L2_NAME,''SNULL'')  
                   AND NVL(A.DIMENSION_CODE,''SNULL'') = NVL(B.DIMENSION_CODE,''SNULL'')   
                   AND NVL(A.DIMENSION_SUBCATEGORY_CODE,''SNULL'') = NVL(B.DIMENSION_SUBCATEGORY_CODE,''SNULL'')   
                   AND NVL(A.DIMENSION_SUB_DETAIL_CODE,''SNULL'') = NVL(B.DIMENSION_SUB_DETAIL_CODE,''SNULL'')   
                  '
                    ;


 EXECUTE IMMEDIATE V_SQL;

--END LOOP ;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '视角标识:, 计算ITEM的月卷积发货额, 版本号='||V_VERSION_ID||', 并给单ITEM的品类打上标识,F_CALIBER_FLAG:'||F_CALIBER_FLAG||',F_DIMENSION_TYPE:'||F_DIMENSION_TYPE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS'); 
 

  --收集统计信息
  ----EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
  
  RETURN 'SUCCESS';
    
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END
$$
/

