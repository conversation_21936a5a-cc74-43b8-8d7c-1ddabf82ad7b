-- Name: f_dm_foc_repl_view_info; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_repl_view_info(f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年10月9日
  创建人  ：唐钦
  背景描述：根据同编码维度数据和研发替代指数维度数据，生成最新替代指数的维度数据
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_REPL_VIEW_INFO();
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_REPL_VIEW_INFO'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_SQL TEXT; --执行语句
  V_PARENT_AMT VARCHAR(500);
  V_GROUP_LEVEL VARCHAR(200);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  -- 取出对应的版本号
  IF F_VERSION_ID IS NULL THEN 
    SELECT VERSION_ID INTO V_VERSION_ID   -- 年度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到最新年度版本号为：'||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 删除对应版本的结果表数据
  EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_REPL_VIEW_INFO_D';

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '清空FIN_DM_OPT_FOI.DM_FOC_REPL_VIEW_INFO_D的表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 插入对应的研发替代指数的维度数据
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_VIEW_INFO_D(
         VERSION_ID,
         GROUP_LEVEL,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV3_PROD_RND_TEAM_CODE,
         LV3_PROD_RD_TEAM_CN_NAME,
         REPLACEMENT_GROUP_ID,
         REPLACEMENT_GROUP_CN_NAME,
         CALIBER_FLAG,
         VIEW_FLAG,
         DATA_FLAG,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
    )
  -- 替代ID层级
  SELECT DISTINCT VERSION_ID,
         'BIND' AS GROUP_LEVEL,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV3_PROD_RND_TEAM_CODE,
         LV3_PROD_RD_TEAM_CN_NAME,
         REPLACEMENT_GROUP_ID,
         REPLACEMENT_DESCRIPTION AS REPLACEMENT_GROUP_CN_NAME,
         CALIBER_FLAG,
         VIEW_FLAG,
         'REPLACE' AS DATA_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
	  FROM FIN_DM_OPT_FOI.DM_FOC_REPL_DECODE_T
	  WHERE VERSION_ID = V_VERSION_ID
  UNION ALL 
  -- LV3层级
  SELECT DISTINCT VERSION_ID,
         'LV3' AS GROUP_LEVEL,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV3_PROD_RND_TEAM_CODE,
         LV3_PROD_RD_TEAM_CN_NAME,
         NULL AS REPLACEMENT_GROUP_ID,
         NULL AS REPLACEMENT_GROUP_CN_NAME,
         CALIBER_FLAG,
         VIEW_FLAG,
         'REPLACE' AS DATA_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
	  FROM FIN_DM_OPT_FOI.DM_FOC_REPL_DECODE_T
	  WHERE VERSION_ID = V_VERSION_ID
	  AND VIEW_FLAG = '3'   -- 视角4时，才需要LV3层级数据
  UNION ALL 
  -- LV2层级
  SELECT DISTINCT VERSION_ID,
         'LV2' AS GROUP_LEVEL,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         NULL AS LV3_PROD_RND_TEAM_CODE,
         NULL AS LV3_PROD_RD_TEAM_CN_NAME,
         NULL AS REPLACEMENT_GROUP_ID,
         NULL AS REPLACEMENT_GROUP_CN_NAME,
         CALIBER_FLAG,
         VIEW_FLAG,
         'REPLACE' AS DATA_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
	  FROM FIN_DM_OPT_FOI.DM_FOC_REPL_DECODE_T
	  WHERE VERSION_ID = V_VERSION_ID
	  AND VIEW_FLAG IN ('3','2')   -- 视角4/3时，才需要LV2层级数据
  UNION ALL 
  -- LV1层级
  SELECT DISTINCT VERSION_ID,
         'LV1' AS GROUP_LEVEL,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         NULL AS LV2_PROD_RND_TEAM_CODE,
         NULL AS LV2_PROD_RD_TEAM_CN_NAME,
         NULL AS LV3_PROD_RND_TEAM_CODE,
         NULL AS LV3_PROD_RD_TEAM_CN_NAME,
         NULL AS REPLACEMENT_GROUP_ID,
         NULL AS REPLACEMENT_GROUP_CN_NAME,
         CALIBER_FLAG,
         VIEW_FLAG,
         'REPLACE' AS DATA_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
	  FROM FIN_DM_OPT_FOI.DM_FOC_REPL_DECODE_T
	  WHERE VERSION_ID = V_VERSION_ID
	  AND VIEW_FLAG IN ('3','2','1')   -- 视角4/3/2时，才需要LV1层级数据
  UNION ALL 
  -- LV0层级
  SELECT DISTINCT VERSION_ID,
         'LV0' AS GROUP_LEVEL,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         NULL AS LV1_PROD_RND_TEAM_CODE,
         NULL AS LV1_PROD_RD_TEAM_CN_NAME,
         NULL AS LV2_PROD_RND_TEAM_CODE,
         NULL AS LV2_PROD_RD_TEAM_CN_NAME,
         NULL AS LV3_PROD_RND_TEAM_CODE,
         NULL AS LV3_PROD_RD_TEAM_CN_NAME,
         NULL AS REPLACEMENT_GROUP_ID,
         NULL AS REPLACEMENT_GROUP_CN_NAME,
         CALIBER_FLAG,
         VIEW_FLAG,
         'REPLACE' AS DATA_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
	  FROM FIN_DM_OPT_FOI.DM_FOC_REPL_DECODE_T
	  WHERE VERSION_ID = V_VERSION_ID;
	
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将研发替代指数的维度层级，插入到FIN_DM_OPT_FOI.DM_FOC_REPL_VIEW_INFO_D的表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
	
  -- 将标准成本的维度层级数据，插入到结果表
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_VIEW_INFO_D(
         VERSION_ID,
         GROUP_LEVEL,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV3_PROD_RND_TEAM_CODE,
         LV3_PROD_RD_TEAM_CN_NAME,
         CALIBER_FLAG,
         VIEW_FLAG,
         DATA_FLAG,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
    )
  WITH DIM_SAME_REPL_TMP AS(
  -- 同编码维度数据
  SELECT GROUP_LEVEL,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV3_PROD_RND_TEAM_CODE,
         LV3_PROD_RD_TEAM_CN_NAME,
         CALIBER_FLAG,
         VIEW_FLAG
	  FROM FIN_DM_OPT_FOI.DM_FOC_TOTAL_VIEW_INFO_D
	  WHERE VERSION_ID = V_VERSION_ID
	  AND OVERSEA_FLAG = 'G'   -- 只取全球的数据
	  AND LV0_PROD_LIST_CODE = 'GR'   -- 只取集团的数据
  UNION ALL 
  -- 研发替代指数维度数据
  SELECT GROUP_LEVEL,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV3_PROD_RND_TEAM_CODE,
         LV3_PROD_RD_TEAM_CN_NAME,
         CALIBER_FLAG,
         VIEW_FLAG
	  FROM FIN_DM_OPT_FOI.DM_FOC_REPL_VIEW_INFO_D
	  WHERE VERSION_ID = V_VERSION_ID
	  AND DATA_FLAG = 'REPLACE'
  )
  SELECT DISTINCT V_VERSION_ID AS VERSION_ID,
         GROUP_LEVEL,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV3_PROD_RND_TEAM_CODE,
         LV3_PROD_RD_TEAM_CN_NAME,
         CALIBER_FLAG,
         VIEW_FLAG,
         'STANDARD' AS DATA_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
	  FROM DIM_SAME_REPL_TMP;
	  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将标准成本指数的维度层级，插入到FIN_DM_OPT_FOI.DM_FOC_REPL_VIEW_INFO_D的表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FOC_REPL_VIEW_INFO_D';

  -- 日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集FIN_DM_OPT_FOI.DM_FOC_REPL_VIEW_INFO_D表统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

