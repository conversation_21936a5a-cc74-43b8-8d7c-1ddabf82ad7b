-- Name: f_dm_fcst_cus_annl; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_cus_annl(f_cost_type character varying, f_granularity_type character varying, f_ytd_flag character varying, f_custom_id bigint DEFAULT NULL::bigint, f_keystr character varying DEFAULT NULL::character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年9月14日
  创建人  ：唐钦
  背景描述：根据年均本和权重值，计算研发替代指数逻辑
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FCST_CUS_ANNL();
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_CUS_ANNL'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_SQL TEXT; --执行语句
  V_PARENT_AMT VARCHAR(500);
  V_GROUP_LEVEL VARCHAR(200);
  V_KEYSTR VARCHAR(50) := F_KEYSTR;
  V_CUSTOM_ID BIGINT := F_CUSTOM_ID;   -- 需要计算的组合ID
  V_LV4_CODE VARCHAR(100);
  V_LV4_NAME VARCHAR(100);
  V_IN_SOFTWARE VARCHAR(50);
  V_INTO_SOFTWARE VARCHAR(50);
  V_REL_SOFTWARE VARCHAR(200);
  V_FROM_TABLE VARCHAR(100);
  V_TO_WEIGHT_TABLE VARCHAR(100);
  V_TMP_TABLE VARCHAR(100);
  V_TMP1_TABLE VARCHAR(100);
  V_TMP2_TABLE VARCHAR(100);
  V_DIM_TABLE VARCHAR(100);
  V_YTD_TYPE VARCHAR(50);
  V_SQL_CUSTOM VARCHAR(200);
  V_SQL_CUSTOM1 VARCHAR(200);
  V_FROM_COST_TABLE VARCHAR(100);
  V_TO_COST_TABLE VARCHAR(100);
  V_FROM_AMP_TABLE VARCHAR(100);
  V_TO_AMP_TABLE VARCHAR(100);
  V_TO_STATUS_TABLE VARCHAR(100);
  V_LAST_YEAR_FLAG VARCHAR(200);
  V_YEAR_FLAG VARCHAR(200);
  V_SQL_DE_AMT VARCHAR(200);
  V_YEAR BIGINT := YEAR(CURRENT_TIMESTAMP);   -- 当前年份
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  -- 判断YTD标签，根据标签结果取年度/年度YTD数据进行计算
  IF F_YTD_FLAG = 'Y' THEN   -- 年度YTD数据
     V_YTD_TYPE := '_YTD';
  ELSIF F_YTD_FLAG = 'N' THEN   -- 年度整年数据
     V_YTD_TYPE := '';
  END IF;
  -- 判断成本类型，PSP成本需要添加软硬件标识条件，标准成本不需要；
  V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_ANNL_AVG_T';
  V_FROM_COST_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_ANNL'||V_YTD_TYPE||'_COST_T';
  V_TO_COST_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_CUS_ANNL'||V_YTD_TYPE||'_COST_T';
  V_FROM_AMP_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_ANNL'||V_YTD_TYPE||'_AMP_T';
  V_TO_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_CUS_ANNL'||V_YTD_TYPE||'_WEIGHT_T';
  V_TO_AMP_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_CUS_ANNL'||V_YTD_TYPE||'_AMP_T';
  V_TO_STATUS_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_CUS_ANNL'||V_YTD_TYPE||'_STATUS_T';
  V_TMP_TABLE := F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_COST_TMP';
  V_TMP1_TABLE := F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_DIM_TMP';
  V_TMP2_TABLE := F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_BASE_STATUS_TMP';
  V_DIM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_CUSTOM_COMB_DIM_T';
  V_REL_SOFTWARE := 'AND NVL(T1.SOFTWARE_MARK,''S2'') = NVL(T2.SOFTWARE_MARK,''S2'')';
  V_IN_SOFTWARE := 'SOFTWARE_MARK,';
  V_INTO_SOFTWARE := 'T1.SOFTWARE_MARK,';
  
  IF F_COST_TYPE = 'PSP' THEN    -- PSP成本类型
     V_SQL_DE_AMT := 'NVL(RMB_COST_AMT,0) AS RMB_COST_AMT,';
  ELSIF F_COST_TYPE = 'STD' THEN   -- 标准成本类型
     V_SQL_DE_AMT := 'NVL(GS_DECRYPT(RMB_COST_AMT, '''||V_KEYSTR||''', ''AES128'', ''CBC'', ''SHA256''),0) AS RMB_COST_AMT,';   -- 解密金额
  END IF;

  -- 判断不同目录树类型，对变量进行不同定义
  IF F_GRANULARITY_TYPE = 'IRB' THEN   -- 重量级团队目录树
     V_LV4_CODE := 'LV4_PROD_RND_TEAM_CODE';
     V_LV4_NAME := 'LV4_PROD_RD_TEAM_CN_NAME';
  ELSIF F_GRANULARITY_TYPE = 'INDUS' THEN   -- 产业目录树
     V_LV4_CODE := 'LV4_INDUSTRY_CATG_CODE';
     V_LV4_NAME := 'LV4_INDUSTRY_CATG_CN_NAME';
  ELSIF F_GRANULARITY_TYPE = 'PROD' THEN   -- 销售目录树
     V_LV4_CODE := 'LV4_PROD_LIST_CODE';
     V_LV4_NAME := 'LV4_PROD_LIST_CN_NAME';
  END IF;
  -- 判断F_CUSTOM_ID的传参，当为空值时，则为每月调度；否则为JAVA页面传参
  IF F_CUSTOM_ID IS NULL THEN 
     V_SQL_CUSTOM := '';
     V_SQL_CUSTOM1 := '';
  ELSE 
     V_SQL_CUSTOM := 'AND T2.CUSTOM_ID = '||F_CUSTOM_ID;
     V_SQL_CUSTOM1 := ' AND CUSTOM_ID = '||F_CUSTOM_ID;
  END IF;
  
  -- 取出对应的版本号
  IF F_VERSION_ID IS NULL THEN 
    SELECT VERSION_ID INTO V_VERSION_ID   -- 年度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;
  DBMS_OUTPUT.PUT_LINE('取到版本号为：'||V_VERSION_ID);
  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到最新年度版本号为：'||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   
  -- 清空结果表数据
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_WEIGHT_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||V_SQL_CUSTOM1;
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_COST_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||V_SQL_CUSTOM1;
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_AMP_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||V_SQL_CUSTOM1;
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_STATUS_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||V_SQL_CUSTOM1;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除版本号为：'||V_VERSION_ID||'，组合ID为：'||F_CUSTOM_ID||'，'||V_TO_WEIGHT_TABLE||'、'||V_TO_COST_TABLE||'、'||V_TO_AMP_TABLE||'、'||V_TO_STATUS_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  -- 创建组合维度临时表
  V_SQL := '
  DROP TABLE IF EXISTS '||V_TMP1_TABLE||';
  CREATE TEMPORARY TABLE '||V_TMP1_TABLE||' (
      CUSTOM_ID             BIGINT,
      CUSTOM_CN_NAME        VARCHAR(200),
      GROUP_LEVEL           VARCHAR(50),
      LV4_CODE              VARCHAR(50),
      LV4_CN_NAME           VARCHAR(500),
      SPART_CODE            VARCHAR(50),
      SPART_CN_NAME         VARCHAR(500),
      VIEW_FLAG             VARCHAR(50),
      REGION_CODE           VARCHAR(50),
      REGION_CN_NAME        VARCHAR(500),
      REPOFFICE_CODE        VARCHAR(50),
      REPOFFICE_CN_NAME     VARCHAR(500),
      BG_CODE               VARCHAR(50),
      BG_CN_NAME            VARCHAR(200),
      OVERSEA_FLAG          VARCHAR(10),
      MAIN_FLAG             VARCHAR(2),
      CODE_ATTRIBUTES       VARCHAR(50),
      SOFTWARE_MARK         VARCHAR(50)
  )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY HASH(SPART_CODE)';
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;
  DBMS_OUTPUT.PUT_LINE('创建'||V_TMP1_TABLE||'表成功');
  
  -- 创建MAIN_FLAG标识数据的临时表
  DROP TABLE IF EXISTS CUS_MAIN_TMP;
  CREATE TEMPORARY TABLE CUS_MAIN_TMP(
       CUSTOM_ID           BIGINT,
       MAIN_FLAG           VARCHAR(2)
  )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY REPLICATION;
   
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '创建组合维度临时表：'||V_TMP1_TABLE||'，创建完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  V_SQL := '
  INSERT INTO '||V_TMP1_TABLE||'(
         CUSTOM_ID,        
         CUSTOM_CN_NAME,   
         GROUP_LEVEL,
         LV4_CODE,         
         LV4_CN_NAME,      
         SPART_CODE,       
         SPART_CN_NAME,    
         REGION_CODE,      
         REGION_CN_NAME,   
         REPOFFICE_CODE,   
         REPOFFICE_CN_NAME,
         BG_CODE,          
         BG_CN_NAME,       
         OVERSEA_FLAG,     
         '||V_IN_SOFTWARE||'
         MAIN_FLAG,        
         CODE_ATTRIBUTES  
    )
  SELECT CUSTOM_ID,        
         CUSTOM_CN_NAME,   
         GROUP_LEVEL,
         LV4_CODE,         
         LV4_CN_NAME,       
         SPART_CODE,       
         SPART_CN_NAME,    
         REGION_CODE,      
         REGION_CN_NAME,   
         REPOFFICE_CODE,  
         REPOFFICE_CN_NAME,
         BG_CODE,          
         BG_CN_NAME,       
         OVERSEA_FLAG,     
         '||V_IN_SOFTWARE||'
         MAIN_FLAG,        
         CODE_ATTRIBUTES
      FROM '||V_DIM_TABLE||' T2
      WHERE ENABLE_FLAG = ''Y'' 
      AND USE_FLAG = ''CALC''   -- 计算时需要的组合维度
      AND PAGE_FLAG IN (''ANNUAL'',''ALL_ANNUAL'')   -- 限制年度页面逻辑
      '||V_SQL_CUSTOM;
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将组合维表的数据放进临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  ---------------------------------------------------------------------------------组合年度权重----------------------------------------------------------------------------------------
  -- 创建临时表
  V_SQL := '
  DROP TABLE IF EXISTS '||V_TMP_TABLE||';
  CREATE TEMPORARY TABLE '||V_TMP_TABLE||' (
      CUSTOM_ID             BIGINT,
      CUSTOM_CN_NAME        VARCHAR(500),
      PERIOD_YEAR           INT,
      LV4_CODE              VARCHAR(50),
      LV4_CN_NAME           VARCHAR(500),
      SPART_CODE            VARCHAR(50),
      SPART_CN_NAME         VARCHAR(500),
      RMB_COST_AMT          NUMERIC,
      PARENT_AMT            NUMERIC,
      VIEW_FLAG             VARCHAR(50),
      REGION_CODE           VARCHAR(50),
      REGION_CN_NAME        VARCHAR(500),
      REPOFFICE_CODE        VARCHAR(50),
      REPOFFICE_CN_NAME     VARCHAR(500),
      BG_CODE               VARCHAR(50),
      BG_CN_NAME            VARCHAR(200),
      OVERSEA_FLAG          VARCHAR(10),
      APPEND_FLAG           VARCHAR(2),
      MAIN_FLAG             VARCHAR(2),
      CODE_ATTRIBUTES       VARCHAR(50),
      YTD_FLAG              VARCHAR(2),
      SOFTWARE_MARK         VARCHAR(50)
  )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY HASH(SPART_CODE)';
  EXECUTE IMMEDIATE V_SQL;
  DBMS_OUTPUT.PUT_LINE('创建'||V_TMP_TABLE||'表成功');
   
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '解密临时表：'||V_TMP_TABLE||'，创建完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 提取数据到临时表
  V_SQL := '
  INSERT INTO '||V_TMP_TABLE||'(
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         PERIOD_YEAR,      
         LV4_CODE,         
         LV4_CN_NAME,      
         SPART_CODE,       
         SPART_CN_NAME,    
         RMB_COST_AMT,  
         PARENT_AMT,
         VIEW_FLAG,        
         REGION_CODE,      
         REGION_CN_NAME,   
         REPOFFICE_CODE,   
         REPOFFICE_CN_NAME,
         BG_CODE,          
         BG_CN_NAME,       
         OVERSEA_FLAG,     
         APPEND_FLAG,
         MAIN_FLAG,        
         CODE_ATTRIBUTES,  
         '||V_IN_SOFTWARE||'
         YTD_FLAG
  )
  WITH MAIN_COST_TMP AS (
  -- 当MIAN_FLAG为：N时需要的数据
  SELECT PERIOD_YEAR,      
         '||V_LV4_CODE||' AS LV4_CODE,         
         '||V_LV4_NAME||' AS LV4_CN_NAME,      
         SPART_CODE,       
         SPART_CN_NAME,    
         '||V_SQL_DE_AMT||'   -- 若金额为空，就给0  
         VIEW_FLAG,        
         REGION_CODE,      
         REGION_CN_NAME,   
         REPOFFICE_CODE,   
         REPOFFICE_CN_NAME,
         BG_CODE,          
         BG_CN_NAME,       
         OVERSEA_FLAG,     
         APPEND_FLAG,
         ''N'' AS MAIN_FLAG,      
         NULL AS CODE_ATTRIBUTES,   -- 根据变量对主力编码标识和编码属性重新打上对应标签
         '||V_IN_SOFTWARE||'
         YTD_FLAG
      FROM '||V_FROM_TABLE||'
      WHERE VERSION_ID = '||V_VERSION_ID||'
      AND YTD_FLAG = '''||F_YTD_FLAG||'''   -- 根据传参取不同数据
      AND VIEW_FLAG = ''PROD_SPART''
      AND (CODE_ATTRIBUTES <> ''全选'' OR CODE_ATTRIBUTES IS NULL)   -- 主力标识为：N时，由于主力编码维表的数据是在基础数据的基础上打标签，所以这部分数据重合，只排除全选数据即可
  UNION ALL
  -- 当MIAN_FLAG为：Y时需要的数据
  SELECT PERIOD_YEAR,      
         '||V_LV4_CODE||' AS LV4_CODE,         
         '||V_LV4_NAME||' AS LV4_CN_NAME,      
         SPART_CODE,       
         SPART_CN_NAME,    
         '||V_SQL_DE_AMT||'   -- 若金额为空，就给0  
         VIEW_FLAG,        
         REGION_CODE,      
         REGION_CN_NAME,   
         REPOFFICE_CODE,   
         REPOFFICE_CN_NAME,
         BG_CODE,          
         BG_CN_NAME,       
         OVERSEA_FLAG,     
         APPEND_FLAG,
         MAIN_FLAG,      
         CODE_ATTRIBUTES,   -- 根据变量对主力编码标识和编码属性重新打上对应标签
         '||V_IN_SOFTWARE||'
         YTD_FLAG
      FROM '||V_FROM_TABLE||'
      WHERE VERSION_ID = '||V_VERSION_ID||'
      AND YTD_FLAG = '''||F_YTD_FLAG||'''   -- 根据传参取不同数据
      AND VIEW_FLAG = ''PROD_SPART''
	  AND MAIN_FLAG = ''Y''    -- 主力标识为：Y时，包含主力编码维表数据，以及造的全选属性的数据
  )
  SELECT T2.CUSTOM_ID,
         T2.CUSTOM_CN_NAME,
         T1.PERIOD_YEAR,      
         T1.LV4_CODE,         
         T1.LV4_CN_NAME,      
         T1.SPART_CODE,       
         T1.SPART_CN_NAME,    
         T1.RMB_COST_AMT,     
         SUM(T1.RMB_COST_AMT) OVER(PARTITION BY T2.CUSTOM_ID,T1.PERIOD_YEAR,T1.REGION_CODE,T1.REPOFFICE_CODE,T1.BG_CODE,T1.OVERSEA_FLAG,'||V_INTO_SOFTWARE||'T1.MAIN_FLAG,T1.CODE_ATTRIBUTES) AS PARENT_AMT,
         T1.VIEW_FLAG,        
         T1.REGION_CODE,      
         T1.REGION_CN_NAME,   
         T1.REPOFFICE_CODE,   
         T1.REPOFFICE_CN_NAME,
         T1.BG_CODE,          
         T1.BG_CN_NAME,       
         T1.OVERSEA_FLAG,     
         T1.APPEND_FLAG,
         T1.MAIN_FLAG,        
         T1.CODE_ATTRIBUTES,
         '||V_INTO_SOFTWARE||'
         T1.YTD_FLAG
      FROM MAIN_COST_TMP T1
      INNER JOIN '||V_TMP1_TABLE||' T2
      ON T1.LV4_CODE = T2.LV4_CODE
      AND T1.SPART_CODE = T2.SPART_CODE 
      AND T1.REGION_CODE = T2.REGION_CODE
      AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
      AND T1.BG_CODE = T2.BG_CODE
      AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
      AND T1.MAIN_FLAG = T2.MAIN_FLAG
      AND NVL(T1.CODE_ATTRIBUTES,''S1'') = NVL(T2.CODE_ATTRIBUTES,''S1'')
      '||V_REL_SOFTWARE;
      
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;
  DBMS_OUTPUT.PUT_LINE('获取到版本号为：'||V_VERSION_ID||'，成本类型为：'||F_COST_TYPE||'，YTD_FLAG为：'||F_YTD_FLAG||'，组合ID为：'||V_CUSTOM_ID||'的数据，并插入到临时表');
  
  -- 计算该组合所有SPART层级的权重值
  V_SQL := '
  INSERT INTO '||V_TO_WEIGHT_TABLE||'(
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         WEIGHT_RATE,
         PARENT_CODE,
         PARENT_CN_NAME,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         '||V_IN_SOFTWARE||'
         APPEND_FLAG,
         GRANULARITY_TYPE
  )
  SELECT CUSTOM_ID,
         CUSTOM_CN_NAME,
         '||V_VERSION_ID||' AS VERSION_ID,
         PERIOD_YEAR,
         SPART_CODE AS GROUP_CODE,
         SPART_CN_NAME AS GROUP_CN_NAME,
         DECODE(RMB_COST_AMT,0,0,RMB_COST_AMT/PARENT_AMT) AS WEIGHT_RATE,
         LV4_CODE AS PARENT_CODE,
         LV4_CN_NAME AS PARENT_CN_NAME,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         '||V_IN_SOFTWARE||'
         APPEND_FLAG,
         '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
      FROM '||V_TMP_TABLE;
      
     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL;   
     DBMS_OUTPUT.PUT_LINE('权重计算成功');                
                                            
    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID||'，成本类型为：'||F_COST_TYPE||'，YTD_FLAG为：'||F_YTD_FLAG||'，组合ID为：'||V_CUSTOM_ID||'的权重数据到'||V_TO_WEIGHT_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');    
   
  -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_WEIGHT_TABLE;

  -- 日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_WEIGHT_TABLE||'统计信息完成!');
   
 ---------------------------------------------------------------------------------组合成本分布图-----------------------------------------------------------------------------------------
  -- 从年度成本分布图表取得SPART层级成本数据
  V_SQL := '
  INSERT INTO '||V_TO_COST_TABLE||'(
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         RMB_COST_AMT,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         '||V_IN_SOFTWARE||'
         GRANULARITY_TYPE
  )
  WITH BASE_ANNL_COST AS(
  SELECT T2.CUSTOM_ID,
         T2.CUSTOM_CN_NAME,
         T1.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T2.GROUP_LEVEL,
         T1.RMB_COST_AMT,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME,
         T1.OVERSEA_FLAG,
         '||V_INTO_SOFTWARE||'
         T1.VIEW_FLAG,
         T1.MAIN_FLAG,
         T1.CODE_ATTRIBUTES
      FROM '||V_FROM_COST_TABLE||' T1
      INNER JOIN '||V_TMP1_TABLE||' T2
        ON T1.GROUP_CODE = T2.LV4_CODE
        AND T1.REGION_CODE = T2.REGION_CODE
        AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
        AND T1.BG_CODE= T2.BG_CODE
        AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
        AND T1.MAIN_FLAG = T2.MAIN_FLAG
        AND NVL(T1.CODE_ATTRIBUTES,''S1'') = NVL(T2.CODE_ATTRIBUTES,''S1'')
        '||V_REL_SOFTWARE||'
      WHERE T1.GROUP_LEVEL = ''LV4''
      AND T1.VIEW_FLAG = ''PROD_SPART''
  )
  SELECT CUSTOM_ID,
         CUSTOM_CN_NAME,
         '||V_VERSION_ID||' AS VERSION_ID,
         PERIOD_YEAR,
         CUSTOM_ID AS GROUP_CODE,
         CUSTOM_CN_NAME AS GROUP_CN_NAME,
         GROUP_LEVEL,
         SUM(RMB_COST_AMT) AS RMB_COST_AMT,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         '||V_IN_SOFTWARE||'
         '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
      FROM BASE_ANNL_COST
      GROUP BY CUSTOM_ID,
               CUSTOM_CN_NAME,
               GROUP_LEVEL,
               PERIOD_YEAR,
               REGION_CODE,
               REGION_CN_NAME,
               REPOFFICE_CODE,
               REPOFFICE_CN_NAME,
               BG_CODE,
               BG_CN_NAME,
               OVERSEA_FLAG,
               '||V_IN_SOFTWARE||'
               MAIN_FLAG,
               CODE_ATTRIBUTES';
               
     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL;   
     DBMS_OUTPUT.PUT_LINE('成本分布图计算成功');                
                                            
    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID||'，成本类型为：'||F_COST_TYPE||'，YTD_FLAG为：'||F_YTD_FLAG||'，组合ID为：'||V_CUSTOM_ID||'的成本分布图数据到'||V_TO_COST_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');    
   
  -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_COST_TABLE;

  -- 日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_COST_TABLE||'统计信息完成!');
         
 ----------------------------------------------------------------------------------组合涨跌幅----------------------------------------------------------------------------------------------
 -- 从年度涨跌幅表取得SPART层级涨跌幅数据
  V_SQL := '
  INSERT INTO '||V_TO_AMP_TABLE||'(
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         '||V_IN_SOFTWARE||'
         GRANULARITY_TYPE
  )
  WITH BASE_ANNL_AMP AS(
  SELECT T2.CUSTOM_ID,
         T2.CUSTOM_CN_NAME,
         T1.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T2.GROUP_LEVEL,
         T1.ANNUAL_AMP,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME,
         T1.OVERSEA_FLAG,
         '||V_INTO_SOFTWARE||'
         T1.VIEW_FLAG,
         T1.MAIN_FLAG,
         T1.CODE_ATTRIBUTES
      FROM '||V_FROM_AMP_TABLE||' T1
      INNER JOIN '||V_TMP1_TABLE||' T2
        ON T1.GROUP_CODE = T2.SPART_CODE 
        AND T1.PARENT_CODE = T2.LV4_CODE 
        AND T1.REGION_CODE = T2.REGION_CODE
        AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
        AND T1.BG_CODE= T2.BG_CODE
        AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
        AND T1.MAIN_FLAG = T2.MAIN_FLAG
        AND NVL(T1.CODE_ATTRIBUTES,''S1'') = NVL(T2.CODE_ATTRIBUTES,''S1'')
        '||V_REL_SOFTWARE||'
      WHERE T1.GROUP_LEVEL = ''SPART''
      AND T1.VIEW_FLAG = ''PROD_SPART''
  ),
  WEIGHT_AMP_TMP AS(
  SELECT T1.CUSTOM_ID,
         T1.CUSTOM_CN_NAME,
         T1.PERIOD_YEAR,
         T1.CUSTOM_ID AS GROUP_CODE,
         T1.CUSTOM_CN_NAME AS GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         T1.ANNUAL_AMP*T2.WEIGHT_RATE AS ANNUAL_AMP_WEIGHT,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME,
         T1.OVERSEA_FLAG,
         '||V_INTO_SOFTWARE||'
         T1.MAIN_FLAG,
         T1.CODE_ATTRIBUTES
      FROM BASE_ANNL_AMP T1
      INNER JOIN '||V_TO_WEIGHT_TABLE||' T2
        ON T1.CUSTOM_ID = T2.CUSTOM_ID
        AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
        AND T1.GROUP_CODE = T2.GROUP_CODE
        AND T1.PARENT_CODE = T2.PARENT_CODE 
        AND T1.REGION_CODE = T2.REGION_CODE
        AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
        AND T1.BG_CODE = T2.BG_CODE
        AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
        AND T1.MAIN_FLAG = T2.MAIN_FLAG
        AND NVL(T1.CODE_ATTRIBUTES,''S1'') = NVL(T2.CODE_ATTRIBUTES,''S1'')
        '||V_REL_SOFTWARE||'
  )
  SELECT CUSTOM_ID,
         CUSTOM_CN_NAME,
         '||V_VERSION_ID||' AS VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         SUM(ANNUAL_AMP_WEIGHT) AS ANNUAL_AMP,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         '||V_IN_SOFTWARE||'
         '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
      FROM WEIGHT_AMP_TMP
      GROUP BY CUSTOM_ID,
               CUSTOM_CN_NAME,
               PERIOD_YEAR,
               GROUP_CODE,
               GROUP_CN_NAME,
               GROUP_LEVEL,
               REGION_CODE,
               REGION_CN_NAME,
               REPOFFICE_CODE,
               REPOFFICE_CN_NAME,
               BG_CODE,
               BG_CN_NAME,
               OVERSEA_FLAG,
               '||V_IN_SOFTWARE||'
               MAIN_FLAG,
               CODE_ATTRIBUTES';
    
     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL;   
     DBMS_OUTPUT.PUT_LINE('涨跌幅计算成功');                
                                            
    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID||'，成本类型为：'||F_COST_TYPE||'，YTD_FLAG为：'||F_YTD_FLAG||'，组合ID为：'||V_CUSTOM_ID||'的涨跌幅数据到'||V_TO_AMP_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');    
   
  -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_AMP_TABLE;

  -- 日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_AMP_TABLE||'统计信息完成!');

  -------------------------------------------------------------------------------------状态码逻辑处理---------------------------------------------------------------------------------
   -- 创建预处理状态码临时表
  V_SQL := '
  DROP TABLE IF EXISTS '||V_TMP2_TABLE||';
  CREATE TEMPORARY TABLE '||V_TMP2_TABLE||'(
      CUSTOM_ID              VARCHAR(50),
      CUSTOM_CN_NAME         VARCHAR(500),
      GROUP_CODE             VARCHAR(50), 
      GROUP_CN_NAME          VARCHAR(500),
      GROUP_LEVEL            VARCHAR(50), 
      LAST_THREE_YEAR_FLAG   BIGINT,
      LAST_THREE_APPEND_YEAR BIGINT,
      LAST_TWO_YEAR_FLAG     BIGINT,
      LAST_TWO_APPEND_YEAR   BIGINT,
      LAST_YEAR_FLAG         BIGINT,
      LAST_APPEND_YEAR       BIGINT,
      CURRENT_YEAR_FLAG      BIGINT,
      CURRENT_APPEND_YEAR    BIGINT,
      REGION_CODE            VARCHAR(50),
      REGION_CN_NAME         VARCHAR(500),
      REPOFFICE_CODE         VARCHAR(50),
      REPOFFICE_CN_NAME      VARCHAR(500),
      BG_CODE                VARCHAR(50),
      BG_CN_NAME             VARCHAR(200),
      OVERSEA_FLAG           VARCHAR(2),
      VIEW_FLAG              VARCHAR(50),
      MAIN_FLAG              VARCHAR(2),
      CODE_ATTRIBUTES        VARCHAR(50),
      SOFTWARE_MARK          VARCHAR(50)
  )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY HASH (GROUP_CODE)'; 
  EXECUTE IMMEDIATE V_SQL;
   
  -- 计算预处理的状态码数据值
  V_SQL := '
  INSERT INTO '||V_TMP2_TABLE||'(
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         LAST_THREE_YEAR_FLAG,  
         LAST_TWO_YEAR_FLAG,    
         LAST_YEAR_FLAG,        
         CURRENT_YEAR_FLAG,     
         REGION_CODE,           
         REGION_CN_NAME,        
         REPOFFICE_CODE,        
         REPOFFICE_CN_NAME,     
         BG_CODE,               
         BG_CN_NAME,            
         OVERSEA_FLAG,          
         '||V_IN_SOFTWARE||'
         MAIN_FLAG,             
         CODE_ATTRIBUTES
    )
  WITH DISINNCT_DIM_TMP AS(
  SELECT DISTINCT CUSTOM_ID,
         CUSTOM_CN_NAME,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         APPEND_FLAG,
         GRANULARITY_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         '||V_IN_SOFTWARE||'
         MAIN_FLAG,
         CODE_ATTRIBUTES
      FROM '||V_TO_WEIGHT_TABLE||'
      WHERE VERSION_ID = '||V_VERSION_ID||'
      '||V_SQL_CUSTOM1||'
      AND GRANULARITY_TYPE = '''||F_GRANULARITY_TYPE||'''
  )
  SELECT CUSTOM_ID,
         CUSTOM_CN_NAME,     
         SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-3 THEN DECODE(APPEND_FLAG,''N'',1,0) ELSE 0 END) AS LAST_THREE_YEAR_FLAG,  -- 为当年-3年时，赋予该年数据为补齐时赋0，否则实际有数则赋1
         SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-2 THEN DECODE(APPEND_FLAG,''N'',1,0) ELSE 0 END) AS LAST_TWO_YEAR_FLAG,    -- 为当年-2年时，赋予该年数据为补齐时赋0，否则实际有数则赋1
         SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-1 THEN DECODE(APPEND_FLAG,''N'',1,0) ELSE 0 END) AS LAST_YEAR_FLAG,  
         SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||' THEN DECODE(APPEND_FLAG,''N'',1,0) ELSE 0 END) AS CURRENT_YEAR_FLAG,
         REGION_CODE,           
         REGION_CN_NAME,        
         REPOFFICE_CODE,        
         REPOFFICE_CN_NAME,     
         BG_CODE,               
         BG_CN_NAME,            
         OVERSEA_FLAG,          
         '||V_IN_SOFTWARE||'     
         MAIN_FLAG,             
         CODE_ATTRIBUTES
       FROM DISINNCT_DIM_TMP
       GROUP BY CUSTOM_ID,
                CUSTOM_CN_NAME,
                GRANULARITY_TYPE,
                REGION_CODE,           
                REGION_CN_NAME,        
                REPOFFICE_CODE,        
                REPOFFICE_CN_NAME,     
                BG_CODE,               
                BG_CN_NAME,            
                OVERSEA_FLAG,          
                '||V_IN_SOFTWARE||'
                MAIN_FLAG,             
                CODE_ATTRIBUTES';
     
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;   
    DBMS_OUTPUT.PUT_LINE('状态码各年数据情况插入临时表');            
  
  -- 对ITEM层级的年份进行循环 
  FOR YEAR_FLAG IN V_YEAR-2 .. V_YEAR LOOP                      
    IF YEAR_FLAG = V_YEAR-2 THEN
       V_LAST_YEAR_FLAG := 'LAST_THREE_YEAR_FLAG';
       V_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
    ELSIF YEAR_FLAG = V_YEAR-1 THEN
       V_LAST_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
       V_YEAR_FLAG := 'LAST_YEAR_FLAG';
    ELSIF YEAR_FLAG = V_YEAR THEN
       V_LAST_YEAR_FLAG := 'LAST_YEAR_FLAG';
       V_YEAR_FLAG := 'CURRENT_YEAR_FLAG';
    ELSE NULL;
    END IF;
  
  -- ITEM层级年度涨跌幅状态码数据计算逻辑
  V_SQL := '
   INSERT INTO '||V_TO_STATUS_TABLE||'(
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         '||V_IN_SOFTWARE||'
         GRANULARITY_TYPE
      ) 
  SELECT T1.CUSTOM_ID,
         T1.CUSTOM_CN_NAME,
         '||V_VERSION_ID||' AS VERSION_ID,
         '||YEAR_FLAG||' AS PERIOD_YEAR,     -- 循环的年份即是当次计算的年份
         T1.CUSTOM_ID AS GROUP_CODE,
         T1.CUSTOM_CN_NAME AS GROUP_CN_NAME,
         T2.GROUP_LEVEL,
         CASE WHEN '||V_LAST_YEAR_FLAG||' > 0 AND '||V_YEAR_FLAG||' = 0 THEN 1
              WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' = 0 THEN 2
              WHEN '||V_LAST_YEAR_FLAG||' > 0 AND '||V_YEAR_FLAG||' > 0 THEN 3
              WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' > 0 THEN 4    -- 反向思维处理
         END AS STATUS_CODE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         '||V_IN_SOFTWARE||'
         '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
       FROM '||V_TMP2_TABLE||' T1
       LEFT JOIN (SELECT DISTINCT CUSTOM_ID,GROUP_LEVEL FROM '||V_TMP1_TABLE||') T2
       ON T1.CUSTOM_ID = T2.CUSTOM_ID';
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    DBMS_OUTPUT.PUT_LINE('状态码逻辑执行前');
    EXECUTE IMMEDIATE V_SQL;   
    DBMS_OUTPUT.PUT_LINE('状态码逻辑执行后');    
       
  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '预处理状态码数据插入结果表'||V_TO_STATUS_TABLE,
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
  END LOOP;   -- 结束循环

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

