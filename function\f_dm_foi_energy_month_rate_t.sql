-- Name: f_dm_foi_energy_month_rate_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_energy_month_rate_t(f_caliber_flag character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间：2024-02-20
创建人  ：黄心蕊 hwx1187045
最新修改时间：2024-04-26
修改人：唐钦
背景描述：月度分析-同环比表数据初始化
参数描述：参数一(F_VERSION_ID)：运行版本号
          参数二(X_SUCCESS_FLAG)：运行状态返回值 ‘1’为成功，‘0’为失败
来源表    ：FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_INFO_T --数字能源_规格品清单 版本号来源表
          FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_PRICE_INDEX_T --数字能源_价格指数表 明细数据来源表
目标表    ：FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_RATE_T --数字能源_价格指数同环比表    
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOI_ENERGY_MONTH_RATE_T(''); 数字能源一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME        VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOI_ENERGY_MONTH_RATE_T';
  V_VERSION        BIGINT; --版本号
  V_BASE_PERIOD_ID INT := TO_NUMBER((YEAR(CURRENT_DATE) - 3) || '01'); --基期会计期
  
  -- 202405版本新增
  V_FROM_TABLE VARCHAR(100);
  V_TO_TABLE VARCHAR(100);
  V_VERSION_TABLE VARCHAR(100);
  V_COLUMN VARCHAR(50);
  V_SQL TEXT;
  
BEGIN 
X_SUCCESS_FLAG := '1';

--日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
 -- 判断产业项目为ICT还是数字能源
  IF F_CALIBER_FLAG = 'I' THEN   -- ICT  
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_INFO_T';
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_PRICE_INDEX_T';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_MONTH_RATE_T';
     V_COLUMN := 'CONTINUITY_TYPE';
  ELSIF F_CALIBER_FLAG = 'E' THEN   -- 数字能源 
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_INFO_T';
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_PRICE_INDEX_T';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_RATE_T';
     V_COLUMN := 'GROUP_PUR_FLAG';
  END IF;

  --版本号入参判断，当入参为空，取规格品清单最新版本号
  IF F_VERSION_ID IS NULL THEN
  V_SQL := '
    SELECT VERSION_ID 
      FROM '||V_VERSION_TABLE||'
       WHERE LAST_UPDATE_DATE IS NOT NULL 
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1';
  EXECUTE IMMEDIATE V_SQL INTO V_VERSION;
    --入参不为空，则以入参为版本号
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;
  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
 
--1.删除同版本同口径数据 
V_SQL := 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION ;
  EXECUTE IMMEDIATE V_SQL;

  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 2,
  F_CAL_LOG_DESC => '删除结果表：'||V_TO_TABLE||',同版本数据成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
  
-- 2.同环比计算
V_SQL := '
WITH LEV_INDEX AS
 (SELECT PERIOD_ID,
         SUBSTR(PERIOD_ID, 5, 2) AS MONTH_DAY,
         L4_CEG_CODE,
         L4_CEG_SHORT_CN_NAME,
         L4_CEG_CN_NAME,
         L3_CEG_CODE,
         L3_CEG_SHORT_CN_NAME,
         L3_CEG_CN_NAME,
         L2_CEG_CODE,
         L2_CEG_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         PRICE_INDEX,
         PARENT_CODE,
         PARENT_CN_NAME,
         '||V_COLUMN||'
    FROM '||V_FROM_TABLE||'
   WHERE VERSION_ID = '||V_VERSION||'
     AND BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'),

BASE_YOY AS
 (SELECT PERIOD_ID,
         L4_CEG_CODE,
         L4_CEG_SHORT_CN_NAME,
         L4_CEG_CN_NAME,
         L3_CEG_CODE,
         L3_CEG_SHORT_CN_NAME,
         L3_CEG_CN_NAME,
         L2_CEG_CODE,
         L2_CEG_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         PRICE_INDEX,
         LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY L2_CEG_CODE, NVL(L3_CEG_CODE, ''LV3''), NVL(L4_CEG_CODE, ''LV4''), NVL(PARENT_CODE, ''PD''), GROUP_CODE,GROUP_LEVEL,'||V_COLUMN||', MONTH_DAY ORDER BY PERIOD_ID) AS YOY_PERIOD_ID,
         LAG(PRICE_INDEX, 1, NULL) OVER(PARTITION BY L2_CEG_CODE, NVL(L3_CEG_CODE, ''LV3''), NVL(L4_CEG_CODE, ''LV4''), NVL(PARENT_CODE, ''PD''), GROUP_CODE, GROUP_LEVEL,'||V_COLUMN||',MONTH_DAY ORDER BY PERIOD_ID) AS YOY_PRICE_INDEX,
         LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY L2_CEG_CODE, NVL(L3_CEG_CODE, ''LV3''), NVL(L4_CEG_CODE, ''LV4''), NVL(PARENT_CODE, ''PD''), GROUP_CODE ,GROUP_LEVEL ,'||V_COLUMN||' ORDER BY PERIOD_ID) AS POP_PERIOD_ID,
         LAG(PRICE_INDEX, 1, NULL) OVER(PARTITION BY L2_CEG_CODE, NVL(L3_CEG_CODE, ''LV3''), NVL(L4_CEG_CODE, ''LV4''), NVL(PARENT_CODE, ''PD''), GROUP_CODE ,GROUP_LEVEL,'||V_COLUMN||' ORDER BY PERIOD_ID) AS POP_PRICE_INDEX,
         PARENT_CODE,
         PARENT_CN_NAME,
         '||V_COLUMN||'
    FROM LEV_INDEX)

INSERT INTO '||V_TO_TABLE||'
  (VERSION_ID,
   PERIOD_YEAR,
   PERIOD_ID,
   BASE_PERIOD_ID,
   L4_CEG_CODE,
   L4_CEG_SHORT_CN_NAME,
   L4_CEG_CN_NAME,
   L3_CEG_CODE,
   L3_CEG_SHORT_CN_NAME,
   L3_CEG_CN_NAME,
   L2_CEG_CODE,
   L2_CEG_CN_NAME,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   RATE,
   RATE_FLAG,
   PARENT_CODE,
   PARENT_CN_NAME,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   '||V_COLUMN||')

SELECT '||V_VERSION||' AS VERSION_ID,
       LEFT(PERIOD_ID, 4) AS PERIOD_YEAR,
       PERIOD_ID,
       '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
       L4_CEG_CODE,
       L4_CEG_SHORT_CN_NAME,
       L4_CEG_CN_NAME,
       L3_CEG_CODE,
       L3_CEG_SHORT_CN_NAME,
       L3_CEG_CN_NAME,
       L2_CEG_CODE,
       L2_CEG_CN_NAME,
       GROUP_CODE,
       GROUP_CN_NAME,
       GROUP_LEVEL,
       ((PRICE_INDEX / NULLIF(YOY_PRICE_INDEX, 0)) - 1) AS RATE,
       ''YOY'' AS RATE_FLAG,
       PARENT_CODE,
       PARENT_CN_NAME,
       -1 AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       -1 AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       ''N'' AS DEL_FLAG,
       '||V_COLUMN||'
  FROM BASE_YOY
 WHERE YOY_PRICE_INDEX IS NOT NULL
UNION ALL
SELECT '||V_VERSION||' AS VERSION_ID,
       LEFT(PERIOD_ID, 4),
       PERIOD_ID,
       '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
       L4_CEG_CODE,
       L4_CEG_SHORT_CN_NAME,
       L4_CEG_CN_NAME,
       L3_CEG_CODE,
       L3_CEG_SHORT_CN_NAME,
       L3_CEG_CN_NAME,
       L2_CEG_CODE,
       L2_CEG_CN_NAME,
       GROUP_CODE,
       GROUP_CN_NAME,
       GROUP_LEVEL,
       ((PRICE_INDEX / NULLIF(POP_PRICE_INDEX, 0)) - 1) AS RATE,
       ''POP'' AS RATE_FLAG,
       PARENT_CODE,
       PARENT_CN_NAME,
       -1 AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       -1 AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       ''N'' AS DEL_FLAG,
       '||V_COLUMN||'
  FROM BASE_YOY
 WHERE POP_PRICE_INDEX IS NOT NULL';
   EXECUTE IMMEDIATE V_SQL;
   
--3.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 3,
  F_CAL_LOG_DESC => '产业标识为：'||F_CALIBER_FLAG||'的同环比表插数成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := '0';
  
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; $$
/

