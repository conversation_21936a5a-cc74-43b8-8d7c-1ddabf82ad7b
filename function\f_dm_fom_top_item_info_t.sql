-- Name: f_dm_fom_top_item_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_top_item_info_t(f_keystr character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$


/*
最后更新时间: 16点26分2024年4月12日
更新人 : 黄心蕊
更新内容 : 202405版本修改 时间范围前滚12个月
创建时间：2023/12/07
创建人  ：许灿烽
参数描述：
        参数一(f_keystr)：绝密数据解密密钥串
        参数二(x_result_status)：运行状态返回值 ‘1’为成功，‘0’为失败
		参数三(f_version_id)：前端传入的版本号
背景描述： 制造对象下规格品清单(月度分析)
备注： 这里要计算制造对象下 TOP 95% 金额的ITEM。分别算出去年的TOP ITEM,本年YTD的TOP ITEM,取两年ITEM的并集。且制造对象下ITEM数量<=8，全部都为TOP,且刚好跨越 95% 的那个ITEM也要算进去。
       使用逆向思维，计算出<=0.05 权重的ITEM(非TOP)，其余的都是TOP ITEM(好处是不用特殊处理跨越 0.95的那个ITEM)
参数描述:x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T --制造单领域数据明细表(包含自制与EMS)
目标表:FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T  --制造对象下规格品清单(月度分析)
事例：fin_dm_opt_foi.f_dm_fom_top_item_info_t()

事例: SELECT FIN_DM_OPT_FOI.F_DM_FOM_TOP_ITEM_INFO_T('密钥串','');
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOM_TOP_ITEM_INFO_T'; --存储过程名称
  V_TO_TABLE VARCHAR(50) := 'FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T'; -- 目标表
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT ; --版本号ID
  V_VERSION_NAME VARCHAR2(50) ; --版本中文名称
BEGIN
  X_RESULT_STATUS = '1';
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

--取刷新数据的版本号,如果前端传值就用前端的,没有再赋值
IF F_VERSION_ID IS NULL THEN
--查询月度版本号
  SELECT VERSION_ID,VERSION INTO V_VERSION_ID,V_VERSION_NAME
    FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T 
    WHERE DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'MONTH' AND VERSION_TYPE='AUTO'
	ORDER BY VERSION_ID DESC LIMIT 1;
	
 --20240326 更新版本表的AUTO版本号的时间
 UPDATE FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T SET LAST_UPDATE_DATE = CURRENT_TIMESTAMP WHERE VERSION_ID = V_VERSION_ID;

 ELSE V_VERSION_ID := F_VERSION_ID;
	
	SELECT VERSION INTO V_VERSION_NAME
	FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T
	WHERE VERSION_ID = V_VERSION_ID;
END IF;

--删除当前版本数据
DELETE FROM FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T
WHERE VERSION_ID = V_VERSION_ID;


--取最近2年数据并解密
WITH SUM_COST_TEMP AS (
SELECT 
 PERIOD_YEAR    --会计年
,LV0_CODE    --重量级团队LV0编码
,LV0_CN_NAME    --重量级团队LV0中文名称
,LV1_CODE    --重量级团队LV1编码
,LV1_CN_NAME    --重量级团队LV1中文名称
,BUSSINESS_OBJECT_CODE    --经营对象编码
,BUSSINESS_OBJECT_CN_NAME    --经营对象名称
,SHIPPING_OBJECT_CODE     --发货对象编码
,SHIPPING_OBJECT_CN_NAME    --发货对象名称
,MANUFACTURE_OBJECT_CODE     --制造对象编码
,MANUFACTURE_OBJECT_CN_NAME    --制造对象名称
,ITEM_CODE    --子项ITEM编码
,ITEM_CN_NAME    --子项ITEM中文名称
,CALIBER_FLAG    --业务口径（E：EMS/M：自制）
--,SUM(CASE WHEN CALIBER_FLAG = 'M' THEN CAST(RMB_MADE_AMT AS NUMERIC) ELSE RMB_EMS_AMT END) AS RMB_COST_AMT    --吸收金额
,SUM(TO_NUMBER(GS_DECRYPT(RMB_MADE_AMT,f_keystr, 'AES128', 'CBC', 'SHA256'))) AS RMB_COST_AMT--吸收金额  加密后使用
FROM FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T T
WHERE T.ONLY_ITEM_FLAG = 'N'  --剔除单ITEM 仅限自制的业务口径
AND T.CALIBER_FLAG ='M'  -- 业务口径：自制
/*
AND T.PERIOD_YEAR >= YEAR(CURRENT_TIMESTAMP) - 1
AND T.PERIOD_ID <  CAST(TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMM') AS BIGINT)*/
AND T.PERIOD_ID >= CAST(TO_CHAR(ADD_MONTHS(NOW(),-12),'YYYYMM') AS BIGINT )
AND T.PERIOD_ID < CAST(TO_CHAR(NOW(), 'YYYYMM') AS BIGINT) --202405版本修改 时间范围前滚12个月

-- 测试
/*
AND CALIBER_FLAG = 'E'
AND PERIOD_YEAR = '2023'   --会计年
--AND BUSSINESS_OBJECT_CN_NAME = 'X86'
--AND SHIPPING_OBJECT_CN_NAME = 'X86服务器'
AND MANUFACTURE_OBJECT_CODE = '硬盘背板'    --制造对象编码
AND MANUFACTURE_OBJECT_CN_NAME = '硬盘背板'   --制造对象名称
*/
GROUP BY 
 PERIOD_YEAR    --会计年
,LV0_CODE    --重量级团队LV0编码
,LV0_CN_NAME    --重量级团队LV0中文名称
,LV1_CODE    --重量级团队LV1编码
,LV1_CN_NAME    --重量级团队LV1中文名称
,BUSSINESS_OBJECT_CODE    --经营对象编码
,BUSSINESS_OBJECT_CN_NAME    --经营对象名称
,SHIPPING_OBJECT_CODE     --发货对象编码
,SHIPPING_OBJECT_CN_NAME    --发货对象名称
,MANUFACTURE_OBJECT_CODE     --制造对象编码
,MANUFACTURE_OBJECT_CN_NAME    --制造对象名称
,ITEM_CODE    --子项ITEM编码
,ITEM_CN_NAME    --子项ITEM中文名称
,CALIBER_FLAG    --业务口径（E：EMS/M：自制）

UNION ALL
SELECT 
 PERIOD_YEAR    --会计年
,LV0_CODE    --重量级团队LV0编码
,LV0_CN_NAME    --重量级团队LV0中文名称
,LV1_CODE    --重量级团队LV1编码
,LV1_CN_NAME    --重量级团队LV1中文名称
,BUSSINESS_OBJECT_CODE    --经营对象编码
,BUSSINESS_OBJECT_CN_NAME    --经营对象名称
,SHIPPING_OBJECT_CODE     --发货对象编码
,SHIPPING_OBJECT_CN_NAME    --发货对象名称
,MANUFACTURE_OBJECT_CODE     --制造对象编码
,MANUFACTURE_OBJECT_CN_NAME    --制造对象名称
,ITEM_CODE    --子项ITEM编码
,ITEM_CN_NAME    --子项ITEM中文名称
,CALIBER_FLAG    --业务口径（E：EMS/M：自制）
--,SUM(CASE WHEN CALIBER_FLAG = 'M' THEN CAST(RMB_MADE_AMT AS NUMERIC) ELSE RMB_EMS_AMT END) AS RMB_COST_AMT    --吸收金额
,SUM(RMB_EMS_AMT) AS RMB_COST_AMT--吸收金额
FROM FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T T
WHERE  T.CALIBER_FLAG = 'E'  --业务口径: EMS
/*AND T.PERIOD_YEAR >= YEAR(CURRENT_TIMESTAMP) - 1
AND T.PERIOD_ID <  CAST(TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMM') AS BIGINT)*/
AND T.PERIOD_ID >= CAST(TO_CHAR(ADD_MONTHS(NOW(),-12),'YYYYMM') AS BIGINT )
AND T.PERIOD_ID < CAST(TO_CHAR(NOW(), 'YYYYMM') AS BIGINT) --202405版本修改 时间范围前滚12个月

-- 测试
/*
AND CALIBER_FLAG = 'E'
AND PERIOD_YEAR = '2023'   --会计年
--AND BUSSINESS_OBJECT_CN_NAME = 'X86'
--AND SHIPPING_OBJECT_CN_NAME = 'X86服务器'
AND MANUFACTURE_OBJECT_CODE = '硬盘背板'    --制造对象编码
AND MANUFACTURE_OBJECT_CN_NAME = '硬盘背板'   --制造对象名称
*/
GROUP BY 
 PERIOD_YEAR    --会计年
,LV0_CODE    --重量级团队LV0编码
,LV0_CN_NAME    --重量级团队LV0中文名称
,LV1_CODE    --重量级团队LV1编码
,LV1_CN_NAME    --重量级团队LV1中文名称
,BUSSINESS_OBJECT_CODE    --经营对象编码
,BUSSINESS_OBJECT_CN_NAME    --经营对象名称
,SHIPPING_OBJECT_CODE     --发货对象编码
,SHIPPING_OBJECT_CN_NAME    --发货对象名称
,MANUFACTURE_OBJECT_CODE     --制造对象编码
,MANUFACTURE_OBJECT_CN_NAME    --制造对象名称
,ITEM_CODE    --子项ITEM编码
,ITEM_CN_NAME    --子项ITEM中文名称
,CALIBER_FLAG    --业务口径（E：EMS/M：自制）
)

--分视角开窗求出年的分组总发货额
,YEAR_AMT_TEMP AS (
SELECT PERIOD_YEAR    --会计年
,LV0_CODE    --重量级团队LV0编码
,LV0_CN_NAME    --重量级团队LV0中文名称
,LV1_CODE    --重量级团队LV1编码
,LV1_CN_NAME    --重量级团队LV1中文名称
,BUSSINESS_OBJECT_CODE    --经营对象编码
,BUSSINESS_OBJECT_CN_NAME    --经营对象名称
,SHIPPING_OBJECT_CODE     --发货对象编码
,SHIPPING_OBJECT_CN_NAME    --发货对象名称
,MANUFACTURE_OBJECT_CODE     --制造对象编码
,MANUFACTURE_OBJECT_CN_NAME    --制造对象名称
,ITEM_CODE    --子项ITEM编码
,ITEM_CN_NAME    --子项ITEM中文名称
,CALIBER_FLAG    --业务口径（E：EMS/M：自制）
,RMB_COST_AMT    --吸收金额
,SUM(A.RMB_COST_AMT) OVER(PARTITION BY PERIOD_YEAR,LV0_CODE,LV0_CN_NAME,LV1_CODE,LV1_CN_NAME,BUSSINESS_OBJECT_CODE,BUSSINESS_OBJECT_CN_NAME,SHIPPING_OBJECT_CODE,SHIPPING_OBJECT_CN_NAME,MANUFACTURE_OBJECT_CODE,MANUFACTURE_OBJECT_CN_NAME,CALIBER_FLAG ORDER BY RMB_COST_AMT) AS ORDER_YEAR_AMT  --从小到大排序累计金额
,SUM(A.RMB_COST_AMT) OVER(PARTITION BY PERIOD_YEAR,LV0_CODE,LV0_CN_NAME,LV1_CODE,LV1_CN_NAME,BUSSINESS_OBJECT_CODE,BUSSINESS_OBJECT_CN_NAME,SHIPPING_OBJECT_CODE,SHIPPING_OBJECT_CN_NAME,MANUFACTURE_OBJECT_CODE,MANUFACTURE_OBJECT_CN_NAME,CALIBER_FLAG) AS YEAR_AMT  --年金额
,COUNT(1) OVER(PARTITION BY PERIOD_YEAR,LV0_CODE,LV0_CN_NAME,LV1_CODE,LV1_CN_NAME,BUSSINESS_OBJECT_CODE,BUSSINESS_OBJECT_CN_NAME,SHIPPING_OBJECT_CODE,SHIPPING_OBJECT_CN_NAME,MANUFACTURE_OBJECT_CODE,MANUFACTURE_OBJECT_CN_NAME,CALIBER_FLAG) AS ITEM_CNT  --制造对象下ITEM数量
        FROM SUM_COST_TEMP A
)

--分别算出去年和本年YTD的TOP ITEM，打上标记 IS_TOP_FLAG
,TWO_YEAR_TOP_ITEM_TEMP AS (
SELECT PERIOD_YEAR    --会计年
,LV0_CODE    --重量级团队LV0编码
,LV0_CN_NAME    --重量级团队LV0中文名称
,LV1_CODE    --重量级团队LV1编码
,LV1_CN_NAME    --重量级团队LV1中文名称
,BUSSINESS_OBJECT_CODE    --经营对象编码
,BUSSINESS_OBJECT_CN_NAME    --经营对象名称
,SHIPPING_OBJECT_CODE     --发货对象编码
,SHIPPING_OBJECT_CN_NAME    --发货对象名称
,MANUFACTURE_OBJECT_CODE     --制造对象编码
,MANUFACTURE_OBJECT_CN_NAME    --制造对象名称
,ITEM_CODE    --子项ITEM编码
,ITEM_CN_NAME    --子项ITEM中文名称
,CALIBER_FLAG    --业务口径（E：EMS/M：自制）
,RMB_COST_AMT    --吸收金额
,ORDER_YEAR_AMT  --从小到大排序累计金额
,YEAR_AMT  --年金额
,ITEM_CNT  --制造对象下ITEM数量
,ORDER_YEAR_AMT / NULLIF(YEAR_AMT,0) AS ACCU_WEIGHT  --累计权重
,RMB_COST_AMT / NULLIF(YEAR_AMT,0) AS WEIGHT_RATE  --权重值
,CASE WHEN ITEM_CNT <= 8 THEN 1 
      WHEN ORDER_YEAR_AMT / NULLIF(YEAR_AMT,0) <= 0.05 THEN 0 ELSE 1 END AS IS_TOP_FLAG --THEN 'N' ELSE 'Y' END AS IS_TOP_FLAG  --从小到大排序算累计权重。原来应该是用Y,N，这里使用1,0方便算两年的并集。后续再转成Y,N
FROM YEAR_AMT_TEMP
)



INSERT INTO FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T
SELECT 
 V_VERSION_ID AS VERSION_ID    --版本ID
,V_VERSION_NAME AS VERSION_NAME    --版本名称
,YEAR(CURRENT_TIMESTAMP) - 1 || '-' || YEAR(CURRENT_TIMESTAMP) AS PERIOD_YEAR    --会计年(区间值, 例如:2022-2023, 2021，2022，2023)
,A.LV0_CODE    --重量级团队LV0编码
,A.LV0_CN_NAME    --重量级团队LV0中文名称
,A.LV1_CODE    --重量级团队LV1编码
,A.LV1_CN_NAME    --重量级团队LV1中文名称
,A.BUSSINESS_OBJECT_CODE    --经营对象编码
,A.BUSSINESS_OBJECT_CN_NAME    --经营对象名称
,A.SHIPPING_OBJECT_CODE     --发货对象编码
,A.SHIPPING_OBJECT_CN_NAME    --发货对象名称
,A.MANUFACTURE_OBJECT_CODE     --制造对象编码
,A.MANUFACTURE_OBJECT_CN_NAME    --制造对象名称
,A.ITEM_CODE AS TOP_ITEM_CODE    --子项ITEM编码
,A.ITEM_CN_NAME AS TOP_ITEM_CN_NAME    --子项ITEM中文名称
,NULL AS WEIGHT_RATE    --权重值
,-1 AS CREATED_BY    --创建人
,CURRENT_TIMESTAMP AS CREATION_DATE    --创建日期
,-1 AS LAST_UPDATED_BY    --最后更新人
,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE    --最后更新日期
,'N' AS DEL_FLAG    --删除标识(未删除：N，已删除：Y)
,CASE WHEN A.IS_TOP_FLAG = 1 THEN 'Y' ELSE 'N' END AS IS_TOP_FLAG    --是否为TOP类, Y:表示是, N:表示否
,A.CALIBER_FLAG    --业务口径（E：EMS/M：自制）
FROM (
--只要两年中任何一年的ITEM是TOP ，则为TOP。
SELECT 
 LV0_CODE    --重量级团队LV0编码
,LV0_CN_NAME    --重量级团队LV0中文名称
,LV1_CODE    --重量级团队LV1编码
,LV1_CN_NAME    --重量级团队LV1中文名称
,BUSSINESS_OBJECT_CODE    --经营对象编码
,BUSSINESS_OBJECT_CN_NAME    --经营对象名称
,SHIPPING_OBJECT_CODE     --发货对象编码
,SHIPPING_OBJECT_CN_NAME    --发货对象名称
,MANUFACTURE_OBJECT_CODE     --制造对象编码
,MANUFACTURE_OBJECT_CN_NAME    --制造对象名称
,ITEM_CODE    --子项ITEM编码
,ITEM_CN_NAME    --子项ITEM中文名称
,CALIBER_FLAG    --业务口径（E：EMS/M：自制）
,MAX(IS_TOP_FLAG) AS IS_TOP_FLAG   --只要有一个1,那这里就是
FROM TWO_YEAR_TOP_ITEM_TEMP
GROUP BY LV0_CODE    --重量级团队LV0编码
,LV0_CN_NAME    --重量级团队LV0中文名称
,LV1_CODE    --重量级团队LV1编码
,LV1_CN_NAME    --重量级团队LV1中文名称
,BUSSINESS_OBJECT_CODE    --经营对象编码
,BUSSINESS_OBJECT_CN_NAME    --经营对象名称
,SHIPPING_OBJECT_CODE     --发货对象编码
,SHIPPING_OBJECT_CN_NAME    --发货对象名称
,MANUFACTURE_OBJECT_CODE     --制造对象编码
,MANUFACTURE_OBJECT_CN_NAME    --制造对象名称
,ITEM_CODE    --子项ITEM编码
,ITEM_CN_NAME    --子项ITEM中文名称
,CALIBER_FLAG    --业务口径（E：EMS/M：自制）
) A 
;


  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除当月版本制造对象下规格品清单(月度分析),并重新插入新的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END
$$
/

