-- Name: f_dm_foc_made_month_rate; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_made_month_rate(f_industry_flag character varying, f_dimension_type character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/***************************************************************************************************************************************************************
最近修改时间: 2024年6月20日11点09分
修改人   ：黄心蕊 hwx1187045
修改内容： 202407版本 新增IAS部分,及通用颗粒度和量纲颗粒度IAS新增LV4层级
修改时间：2024年4月18日16点27分
修改人   ：黄心蕊 hwx1187045
修改内容： 202405版本 新增数字能源部分,及量纲增加COA层级
创建时间：2023-03-20
创建人  ：黄心蕊 hwx1187045
修改时间：2023-12-21
修改人	：黄心蕊 hwx1187045
修改内容： 202401版本量纲新增SPART层级
背景描述：月度分析-同环比表数据初始化
参数描述：参数一(F_ITEM_VERSION)：通用版本号
		  参数二(X_RESULT_STATUS)：运行状态返回值 ‘1’为成功，‘0’为失败
		  参数三(F_DIMENSION_TYPE)：入参值为'U'为通用颗粒度，入参值为'U'则为盈利颗粒度，入参值为'D'则为量纲颗粒度
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_MADE_MONTH_RATE('U'); 通用颗粒度一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_MADE_MONTH_RATE('P'); 盈利颗粒度一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_MADE_MONTH_RATE('D'); 量纲颗粒度一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                       VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_MADE_MONTH_RATE';
  V_VERSION                       BIGINT; --版本号
  V_EXCEPTION_FLAG                VARCHAR(20) := '0'; --异常定点
  V_BASE_PERIOD_ID                INT := TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '01'); --基期会计期
  V_DIMENSION_TYPE                VARCHAR(2) := F_DIMENSION_TYPE; --维度标识
  V_INDEX_TABLE                   VARCHAR(100); --指数表
  V_TARGET_TABLE                  VARCHAR(100); --结果表（同环比表）
  V_PROFITS_NAME                  VARCHAR(100); --盈利颗粒度字段
  V_L1_NAME                       VARCHAR(100); --盈利颗粒度L1名称字段
  V_L2_NAME                       VARCHAR(100); --盈利颗粒度L2名称字段
  V_MIN_PERIOD					 VARCHAR(100);
  V_MAX_PERIOD					 VARCHAR(100);
  V_SQL                           TEXT; --执行语句
  V_DIMENSION_CODE                TEXT;
  V_DIMENSION_CN_NAME             TEXT;
  V_DIMENSION_SUBCATEGORY_CODE    TEXT;
  V_DIMENSION_SUBCATEGORY_CN_NAME TEXT;
  V_DIMENSION_SUB_DETAIL_CODE     TEXT;
  V_DIMENSION_SUB_DETAIL_CN_NAME  TEXT;
  V_DMS_CODE                      TEXT;
  V_DMS_CN_NAME                   TEXT;
  V_SEQUENCE                      TEXT;
  
  --202401版本量纲新增SPART层级
  V_SPART_CODE       			  TEXT;
  V_SPART_CN_NAME                 TEXT;
  
  --202405版本 数字能源新增COA层级
  V_COA_CODE                     TEXT;
  V_COA_CN_NAME                 TEXT;
  
BEGIN 
X_RESULT_STATUS := '1';

--0.日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

V_EXCEPTION_FLAG := '1';   
--版本号入参判断，当入参为空，取TOP规格品清单最新版本号

IF F_ITEM_VERSION IS NULL THEN
/*SELECT VERSION_ID INTO V_VERSION
  FROM FIN_DM_OPT_FOI.DM_FOC_MADE_TOP_ITEM_INFO_T
 ORDER BY LAST_UPDATE_DATE DESC
 LIMIT 1;*/
 	IF F_INDUSTRY_FLAG = 'I' THEN
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
	 --20240327 修改版本号取数逻辑
	ELSIF F_INDUSTRY_FLAG = 'E' THEN  --202405版本 新增数字能源逻辑
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
	ELSIF F_INDUSTRY_FLAG = 'IAS' THEN	--202407版本 新增IAS部分
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
		 
	END IF ; 
 
--入参不为空，则以入参为版本号
ELSE V_VERSION := F_ITEM_VERSION;
END IF;
  
  --1.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '取得本次执行数据产业项目标识：'||F_INDUSTRY_FLAG||'，颗粒度：'||V_DIMENSION_TYPE||'，以及版本号：'||V_VERSION ,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

  IF V_DIMENSION_TYPE = 'U' THEN /*通用颗粒度*/
    IF F_INDUSTRY_FLAG = 'I' THEN
    V_INDEX_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_MADE_MONTH_COST_IDX_T';
    V_TARGET_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_MONTH_RATE_T';
	SELECT MIN(PERIOD_ID),MAX(PERIOD_ID) INTO V_MIN_PERIOD,V_MAX_PERIOD
	FROM FIN_DM_OPT_FOI.DM_FOC_MADE_MID_MONTH_ITEM_T;
	
    ELSIF F_INDUSTRY_FLAG = 'E' THEN
	--202405版本 新增数字能源部分
    V_INDEX_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_MONTH_COST_IDX_T';
    V_TARGET_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_MONTH_RATE_T';
	SELECT MIN(PERIOD_ID),MAX(PERIOD_ID) INTO V_MIN_PERIOD,V_MAX_PERIOD
	FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_MID_MONTH_ITEM_T;
	
    ELSIF F_INDUSTRY_FLAG = 'IAS' THEN
	--202407版本 新增IAS部分
    V_INDEX_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_MONTH_COST_IDX_T';
    V_TARGET_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_MONTH_RATE_T';
	SELECT MIN(PERIOD_ID),MAX(PERIOD_ID) INTO V_MIN_PERIOD,V_MAX_PERIOD
	FROM FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_MID_MONTH_ITEM_T;
	
    END IF;
	--V_SEQUENCE     := 'FIN_DM_OPT_FOI.DM_FOC_MADE_MONTH_RATE_S.NEXTVAL';
    V_PROFITS_NAME := '';
    V_L1_NAME      := '';
    V_L2_NAME      := '';
						
  ELSIF V_DIMENSION_TYPE = 'P' THEN
    /*盈利颗粒度表以及定义盈利字段*/
    IF F_INDUSTRY_FLAG = 'I' THEN
    V_INDEX_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_MONTH_COST_IDX_T';
    V_TARGET_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_MONTH_RATE_T';
	SELECT MIN(PERIOD_ID),MAX(PERIOD_ID) INTO V_MIN_PERIOD,V_MAX_PERIOD
	FROM FIN_DM_OPT_FOI.DM_FOC_MADE_MID_MONTH_ITEM_T;
	
    ELSIF F_INDUSTRY_FLAG = 'E' THEN
	--202405版本 新增数字能源部分
    V_INDEX_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_MONTH_COST_IDX_T';
    V_TARGET_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_MONTH_RATE_T';
	SELECT MIN(PERIOD_ID),MAX(PERIOD_ID) INTO V_MIN_PERIOD,V_MAX_PERIOD
	FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_MID_MONTH_ITEM_T;
	
    ELSIF F_INDUSTRY_FLAG = 'IAS' THEN
	--202407版本 新增IAS部分
    V_INDEX_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_MONTH_COST_IDX_T';
    V_TARGET_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_MONTH_RATE_T';
	SELECT MIN(PERIOD_ID),MAX(PERIOD_ID) INTO V_MIN_PERIOD,V_MAX_PERIOD
	FROM FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_MID_MONTH_ITEM_T;
	
    END IF;
	--V_SEQUENCE     := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_MONTH_RATE_S.NEXTVAL';
    V_PROFITS_NAME := 'PROFITS_NAME,';
    V_L1_NAME      := 'L1_NAME,';
    V_L2_NAME      := 'L2_NAME,';
	
  ELSIF V_DIMENSION_TYPE = 'D' THEN
    /*盈利颗粒度表以及定义盈利字段*/
	--V_SEQUENCE						:= 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MONTH_RATE_S.NEXTVAL';
    IF F_INDUSTRY_FLAG = 'I' THEN
    V_INDEX_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MONTH_COST_IDX_T';
    V_TARGET_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MONTH_RATE_T';
	SELECT MIN(PERIOD_ID),MAX(PERIOD_ID) INTO V_MIN_PERIOD,V_MAX_PERIOD
	FROM FIN_DM_OPT_FOI.DM_FOC_MADE_MID_MONTH_ITEM_T;
	
    ELSIF F_INDUSTRY_FLAG = 'E' THEN
	--202405版本 新增数字能源部分
    V_INDEX_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_MONTH_COST_IDX_T';
    V_TARGET_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_MONTH_RATE_T';
	
	V_COA_CODE		:= ' COA_CODE, '; 
	V_COA_CN_NAME	:= ' COA_CN_NAME,';  --202405版本 数字能源新增COA层级
	
	SELECT MIN(PERIOD_ID),MAX(PERIOD_ID) INTO V_MIN_PERIOD,V_MAX_PERIOD
	FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_MID_MONTH_ITEM_T;
	
    ELSIF F_INDUSTRY_FLAG = 'IAS' THEN
	--202405版本 新增数字能源部分
    V_INDEX_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_MONTH_COST_IDX_T';
    V_TARGET_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_MONTH_RATE_T';
	SELECT MIN(PERIOD_ID),MAX(PERIOD_ID) INTO V_MIN_PERIOD,V_MAX_PERIOD
	FROM FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_MID_MONTH_ITEM_T;
	
    END IF;
	
    V_PROFITS_NAME                  := '';
    V_L1_NAME                       := '';
    V_L2_NAME                       := '';
    V_DIMENSION_CODE                := ' DIMENSION_CODE,';
    V_DIMENSION_CN_NAME             := ' DIMENSION_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE    := ' DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME := ' DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE     := ' DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME  := ' DIMENSION_SUB_DETAIL_CN_NAME,';
	V_SPART_CODE   					:= ' SPART_CODE,';
	V_SPART_CN_NAME					:= ' SPART_CN_NAME,'; --202401版本量纲新增SPART层级
    V_DMS_CODE                      := ' DMS_CODE,';
    V_DMS_CN_NAME                   := ' DMS_CN_NAME,';
	SELECT MIN(PERIOD_ID),MAX(PERIOD_ID) INTO V_MIN_PERIOD,V_MAX_PERIOD
	FROM FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MID_MONTH_ITEM_T;
  
  END IF;
  
V_EXCEPTION_FLAG := '2';    
 V_SQL:='DELETE FROM '||V_TARGET_TABLE||' WHERE VERSION_ID ='||V_VERSION||';';
 EXECUTE IMMEDIATE V_SQL;

--2.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 2,
  F_CAL_LOG_DESC => '同环比表同版本数据删除完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

 V_EXCEPTION_FLAG := '3';  
	V_SQL:='
	WITH LEV_INDEX AS
	 (SELECT VIEW_FLAG,
			 PROD_RND_TEAM_CODE,
			 PROD_RND_TEAM_CN_NAME,
			 '||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
			  ||V_COA_CODE
			  ||V_COA_CN_NAME	--202405版本 数字能源新增COA层级
			  ||V_DIMENSION_CODE				
			  ||V_DIMENSION_CN_NAME            
			  ||V_DIMENSION_SUBCATEGORY_CODE   
			  ||V_DIMENSION_SUBCATEGORY_CN_NAME
			  ||V_DIMENSION_SUB_DETAIL_CODE    
			  ||V_DIMENSION_SUB_DETAIL_CN_NAME 
			  ||V_SPART_CODE   
			  ||V_SPART_CN_NAME
			  ||V_DMS_CODE                     
			  ||V_DMS_CN_NAME||'  --202401版本量纲新增SPART层级
			 SHIPPING_OBJECT_CODE,
			 SHIPPING_OBJECT_CN_NAME, 
			 MANUFACTURE_OBJECT_CODE, 
			 MANUFACTURE_OBJECT_CN_NAME, 
			 PERIOD_YEAR,
			 PERIOD_ID,
			 SUBSTR(PERIOD_ID, 5, 2) AS MONTH_DAY,
			 PARENT_CODE,
			 PARENT_CN_NAME,
			 GROUP_CODE,
			 GROUP_CN_NAME,
			 GROUP_LEVEL,
			 COST_INDEX,
			 CALIBER_FLAG,
			 OVERSEA_FLAG,  
			 LV0_PROD_LIST_CODE, 
			 LV0_PROD_LIST_CN_NAME
		FROM '||V_INDEX_TABLE||'
	   WHERE VERSION_ID = '||V_VERSION||'
	     AND PERIOD_ID BETWEEN '||V_MIN_PERIOD||' AND '||V_MAX_PERIOD||'
		 AND BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'),
     
	BASE_YOY AS
	 (SELECT VIEW_FLAG,
			 PERIOD_YEAR,
			 PERIOD_ID,
			 PROD_RND_TEAM_CODE,
			 PROD_RND_TEAM_CN_NAME,
			 '||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
			  ||V_COA_CODE
			  ||V_COA_CN_NAME	--202405版本 数字能源新增COA层级
			  ||V_DIMENSION_CODE				
			  ||V_DIMENSION_CN_NAME            
			  ||V_DIMENSION_SUBCATEGORY_CODE   
			  ||V_DIMENSION_SUBCATEGORY_CN_NAME
			  ||V_DIMENSION_SUB_DETAIL_CODE    
			  ||V_DIMENSION_SUB_DETAIL_CN_NAME 
			  ||V_SPART_CODE   
			  ||V_SPART_CN_NAME
			  ||V_DMS_CODE                     
			  ||V_DMS_CN_NAME||'  --202401版本量纲新增SPART层级
			 SHIPPING_OBJECT_CODE,
			 SHIPPING_OBJECT_CN_NAME,
			 MANUFACTURE_OBJECT_CODE,
			 MANUFACTURE_OBJECT_CN_NAME,
			 GROUP_CODE,
			 GROUP_CN_NAME,
			 GROUP_LEVEL,
			 COST_INDEX,
			 LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY CALIBER_FLAG,VIEW_FLAG, PROD_RND_TEAM_CODE, '||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME||V_DIMENSION_CODE
																									||V_DIMENSION_CN_NAME     
																									||V_COA_CODE
																									||V_COA_CN_NAME	--202405版本 数字能源新增COA层级
																									||V_DIMENSION_SUBCATEGORY_CODE   
																									||V_DIMENSION_SUBCATEGORY_CN_NAME
																									||V_DIMENSION_SUB_DETAIL_CODE    
																									||V_DIMENSION_SUB_DETAIL_CN_NAME 
																									||V_SPART_CODE   
																									||V_SPART_CN_NAME
																									||V_DMS_CODE                     
																									||V_DMS_CN_NAME||'   --202401版本量纲新增SPART层级
																									NVL(SHIPPING_OBJECT_CODE,''SOD''),
																									NVL(SHIPPING_OBJECT_CN_NAME,''SON''),
																									NVL(MANUFACTURE_OBJECT_CODE,''MOD''),
																									NVL(MANUFACTURE_OBJECT_CN_NAME,''MON''),
																									CALIBER_FLAG,OVERSEA_FLAG,LV0_PROD_LIST_CODE,LV0_PROD_LIST_CN_NAME,GROUP_LEVEL,
																									GROUP_CODE, MONTH_DAY ORDER BY PERIOD_ID) AS YOY_PERIOD_ID,
			 LAG(COST_INDEX, 1, NULL) OVER(PARTITION BY CALIBER_FLAG,VIEW_FLAG, PROD_RND_TEAM_CODE, '||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME||V_DIMENSION_CODE				
																									 ||V_DIMENSION_CN_NAME
																									 ||V_COA_CODE
																									 ||V_COA_CN_NAME	--202405版本 数字能源新增COA层级
																									 ||V_DIMENSION_SUBCATEGORY_CODE 
																									 ||V_DIMENSION_SUBCATEGORY_CN_NAME
																									 ||V_DIMENSION_SUB_DETAIL_CODE    
																									 ||V_DIMENSION_SUB_DETAIL_CN_NAME 
																									 ||V_SPART_CODE   
																									 ||V_SPART_CN_NAME
																									 ||V_DMS_CODE                     
																									 ||V_DMS_CN_NAME||'   --202401版本量纲新增SPART层级
																									 NVL(SHIPPING_OBJECT_CODE,''SOD''),
																									 NVL(SHIPPING_OBJECT_CN_NAME,''SON''),
																									 NVL(MANUFACTURE_OBJECT_CODE,''MOD''),
																									 NVL(MANUFACTURE_OBJECT_CN_NAME,''MON''),
																									 CALIBER_FLAG,OVERSEA_FLAG,LV0_PROD_LIST_CODE,LV0_PROD_LIST_CN_NAME,GROUP_LEVEL,
																									 GROUP_CODE, MONTH_DAY ORDER BY PERIOD_ID) AS YOY_COST_INDEX,
			 LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY CALIBER_FLAG,VIEW_FLAG, PROD_RND_TEAM_CODE, '||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME||V_DIMENSION_CODE				
																									||V_DIMENSION_CN_NAME
																									||V_COA_CODE
																									||V_COA_CN_NAME	--202405版本 数字能源新增COA层级
																									||V_DIMENSION_SUBCATEGORY_CODE   
																									||V_DIMENSION_SUBCATEGORY_CN_NAME
																									||V_DIMENSION_SUB_DETAIL_CODE    
																									||V_DIMENSION_SUB_DETAIL_CN_NAME
																									||V_SPART_CODE   
																									||V_SPART_CN_NAME
																									||V_DMS_CODE                     
																									||V_DMS_CN_NAME||'   --202401版本量纲新增SPART层级
																									NVL(SHIPPING_OBJECT_CODE,''SOD''),
																									NVL(SHIPPING_OBJECT_CN_NAME,''SON''),
																									NVL(MANUFACTURE_OBJECT_CODE,''MOD''),
																									NVL(MANUFACTURE_OBJECT_CN_NAME,''MON''),
																									CALIBER_FLAG,OVERSEA_FLAG,LV0_PROD_LIST_CODE,LV0_PROD_LIST_CN_NAME,GROUP_LEVEL,
																									GROUP_CODE ORDER BY PERIOD_ID) AS POP_PERIOD_ID,
			 LAG(COST_INDEX, 1, NULL) OVER(PARTITION BY CALIBER_FLAG,VIEW_FLAG, PROD_RND_TEAM_CODE, '||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME||V_DIMENSION_CODE				
																									 ||V_DIMENSION_CN_NAME
																									 ||V_COA_CODE
																									 ||V_COA_CN_NAME	--202405版本 数字能源新增COA层级
																									 ||V_DIMENSION_SUBCATEGORY_CODE   
																									 ||V_DIMENSION_SUBCATEGORY_CN_NAME
																									 ||V_DIMENSION_SUB_DETAIL_CODE    
																									 ||V_DIMENSION_SUB_DETAIL_CN_NAME 
																									 ||V_SPART_CODE   
																									 ||V_SPART_CN_NAME
																									 ||V_DMS_CODE                     
																									 ||V_DMS_CN_NAME||'  --202401版本量纲新增SPART层级
																									 NVL(SHIPPING_OBJECT_CODE,''SOD''),
																									 NVL(SHIPPING_OBJECT_CN_NAME,''SON''),
																									 NVL(MANUFACTURE_OBJECT_CODE,''MOD''),
																									 NVL(MANUFACTURE_OBJECT_CN_NAME,''MON''),
																									 CALIBER_FLAG,OVERSEA_FLAG,LV0_PROD_LIST_CODE,LV0_PROD_LIST_CN_NAME,GROUP_LEVEL,
																									 GROUP_CODE ORDER BY PERIOD_ID) AS POP_COST_INDEX,
			 PARENT_CODE,
			 PARENT_CN_NAME,
			 CALIBER_FLAG,
			 OVERSEA_FLAG,  
			 LV0_PROD_LIST_CODE, 
			 LV0_PROD_LIST_CN_NAME
		FROM LEV_INDEX )	
    
	INSERT INTO '||V_TARGET_TABLE||'
	  (--ID,
	   VERSION_ID,
	   PERIOD_YEAR,
	   PERIOD_ID,
	   BASE_PERIOD_ID,
	   PROD_RND_TEAM_CODE,
	   PROD_RND_TEAM_CN_NAME,
	   '||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
	    ||V_COA_CODE
	    ||V_COA_CN_NAME	--202405版本 数字能源新增COA层级
		||V_DIMENSION_CODE				
		||V_DIMENSION_CN_NAME            
		||V_DIMENSION_SUBCATEGORY_CODE   
		||V_DIMENSION_SUBCATEGORY_CN_NAME
		||V_DIMENSION_SUB_DETAIL_CODE    
		||V_DIMENSION_SUB_DETAIL_CN_NAME 
		||V_SPART_CODE   
		||V_SPART_CN_NAME
		||V_DMS_CODE                     
		||V_DMS_CN_NAME||'  --202401版本量纲新增SPART层级
		SHIPPING_OBJECT_CODE,
		SHIPPING_OBJECT_CN_NAME,
		MANUFACTURE_OBJECT_CODE,
		MANUFACTURE_OBJECT_CN_NAME,
	   GROUP_CODE,
	   GROUP_CN_NAME,
	   GROUP_LEVEL,
	   RATE,
	   RATE_FLAG,
	   PARENT_CODE,
	   PARENT_CN_NAME,
	   CREATED_BY,
	   CREATION_DATE,
	   LAST_UPDATED_BY,
	   LAST_UPDATE_DATE,
	   DEL_FLAG,
	   VIEW_FLAG,
	   CALIBER_FLAG,
	   OVERSEA_FLAG,  
	   LV0_PROD_LIST_CODE, 
	   LV0_PROD_LIST_CN_NAME)
	  SELECT -- '||V_SEQUENCE||' AS ID,
	  '||V_VERSION||' AS VERSION_ID,
			 PERIOD_YEAR,
			 PERIOD_ID,
			 '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
			 PROD_RND_TEAM_CODE,
			 PROD_RND_TEAM_CN_NAME,
			 '||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
			  ||V_COA_CODE
			  ||V_COA_CN_NAME	--202405版本 数字能源新增COA层级
			  ||V_DIMENSION_CODE				
			  ||V_DIMENSION_CN_NAME            
			  ||V_DIMENSION_SUBCATEGORY_CODE   
			  ||V_DIMENSION_SUBCATEGORY_CN_NAME
			  ||V_DIMENSION_SUB_DETAIL_CODE    
			  ||V_DIMENSION_SUB_DETAIL_CN_NAME 
			  ||V_SPART_CODE   
			  ||V_SPART_CN_NAME
			  ||V_DMS_CODE                     
			  ||V_DMS_CN_NAME||'  --202401版本量纲新增SPART层级
			  SHIPPING_OBJECT_CODE,
			  SHIPPING_OBJECT_CN_NAME,
			  MANUFACTURE_OBJECT_CODE,
			  MANUFACTURE_OBJECT_CN_NAME,
			 GROUP_CODE,
			 GROUP_CN_NAME,
			 GROUP_LEVEL,
			 ((COST_INDEX / NULLIF(YOY_COST_INDEX,0)) - 1) AS RATE,
			 ''YOY'' AS RATE_FLAG,
			 PARENT_CODE,
			 PARENT_CN_NAME,
			 ''-1'' AS CREATED_BY,
			 CURRENT_TIMESTAMP AS CREATION_DATE,
			 ''-1'' AS LAST_UPDATED_BY,
			 CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
			 ''N'' AS DEL_FLAG,
			 VIEW_FLAG,
			 CALIBER_FLAG,
			 OVERSEA_FLAG,  
			 LV0_PROD_LIST_CODE, 
			 LV0_PROD_LIST_CN_NAME
		FROM BASE_YOY  
	   WHERE YOY_COST_INDEX IS NOT NULL
	  UNION ALL
	  SELECT -- '||V_SEQUENCE||' AS ID,
	      '||V_VERSION||' AS VERSION_ID,
			 PERIOD_YEAR,
			 PERIOD_ID,
			 '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
			 PROD_RND_TEAM_CODE,
			 PROD_RND_TEAM_CN_NAME,
			 '||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
			  ||V_COA_CODE
			  ||V_COA_CN_NAME	--202405版本 数字能源新增COA层级
			  ||V_DIMENSION_CODE				
			  ||V_DIMENSION_CN_NAME            
			  ||V_DIMENSION_SUBCATEGORY_CODE   
			  ||V_DIMENSION_SUBCATEGORY_CN_NAME
			  ||V_DIMENSION_SUB_DETAIL_CODE    
			  ||V_DIMENSION_SUB_DETAIL_CN_NAME 
			  ||V_SPART_CODE   
			  ||V_SPART_CN_NAME
			  ||V_DMS_CODE                     
			  ||V_DMS_CN_NAME||'  --202401版本量纲新增SPART层级
			  SHIPPING_OBJECT_CODE,
			  SHIPPING_OBJECT_CN_NAME,
			  MANUFACTURE_OBJECT_CODE,
			  MANUFACTURE_OBJECT_CN_NAME,
			 GROUP_CODE,
			 GROUP_CN_NAME,
			 GROUP_LEVEL,
			 ((COST_INDEX / NULLIF(POP_COST_INDEX,0)) - 1) AS RATE,
			 ''POP'' AS RATE_FLAG,
			 PARENT_CODE,
			 PARENT_CN_NAME,
			 ''-1'' AS CREATED_BY,
			 CURRENT_TIMESTAMP AS CREATION_DATE,
			 ''-1'' AS LAST_UPDATED_BY,
			 CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
			 ''N'' AS DEL_FLAG,
			 VIEW_FLAG,			 
			 CALIBER_FLAG,
			 OVERSEA_FLAG,  
			 LV0_PROD_LIST_CODE, 
			 LV0_PROD_LIST_CN_NAME
		FROM BASE_YOY
	   WHERE POP_COST_INDEX IS NOT NULL;';
--   
-- 	RETURN V_SQL;
  EXECUTE IMMEDIATE V_SQL;
   
--3.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 3,
  F_CAL_LOG_DESC => '同环比表插数成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_EXCEPTION_FLAG,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; 


$$
/

