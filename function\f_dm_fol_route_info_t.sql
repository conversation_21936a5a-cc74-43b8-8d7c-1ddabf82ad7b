-- Name: f_dm_fol_route_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fol_route_info_t(p_version_id integer DEFAULT NULL::integer, p_refresh_type character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：2024-02-19
创建人  ：qwx1110218
背景描述：航线清单表的加工规则：1、包括去年、当年的所有航线
                                2、Top航线计算去年的航线量总额，排序，取前Top85%的航线清单A；
                                3、Top航线计算当年YTD的航线量总额，排序，取前Top85%的航线清单B；
                                4、Top清单为A与B的并集；
                                5、周刷新：周六晚上随调度更新；
                                6、航线量总额计算方式：a)航线是由起始港+目的港两个字段决定的；
                                                       b)一条航线的总量，需将20GP数量×1，40GP数量×2，40HQ数量×2，再加和；按年汇总后即当年的航线量总额
          p_version_id 逻辑：1、自动调度，取版本表最大版本ID+1；2、刷新（页面的刷新价格表、刷新系统）：取java传版本ID；
          p_refresh_type 逻辑：1、自动调度，直接赋值为 4_AUTO；2、刷新（页面的刷新价格表、刷新系统）：取java传的值（1_刷新价格表 或 2_刷新系统）
参数描述：参数(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_fol_route_info_t()
变更记录：2024-6-3 qwx1110218 直接从航线汇总表取“航线”字段
          2024-8-21 qwx1110218 来源表变更：由 dm_fol_route_info_sum_t （价格补录表内关联航线量集成表）变更为 dm_fol_route_price_info_sum_t （价格补录表左关联航线量集成表），
                                           且需要剔除柜型量为空的数据（即 container_qty is not null）；

*/


declare
	v_sp_name varchar(100) := 'fin_dm_opt_foi.f_dm_fol_route_info_t('||p_version_id||','''||p_refresh_type||''')';
	v_tbl_name varchar(100) := 'fin_dm_opt_foi.dm_fol_route_info_t';
	v_dml_row_count  int default 0 ;
	v_max_version_id int;
	v_route_version_code varchar(30);


begin

  set enable_force_vector_engine to on;
	x_success_flag := '1';        --1表示成功

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '航线量汇总表 '||v_tbl_name||'：开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  -- 创建 route_info_sum_tmp1 临时表
  drop table if exists route_info_sum_tmp1;
	create temporary table route_info_sum_tmp1(
         version_id       int -- 版本ID
       , year             int -- 年份
       , period_id        int -- 会计期
       , transport_mode   varchar(50) -- 运输方式（精品海运、Xeneta）
       , source_port_name varchar(50) -- 起始港
       , dest_port_name   varchar(50) -- 目的港
       , route            varchar(200) -- 航线（目的港_国家）
       , region_cn_name   varchar(50) -- 目的港区域
       , container_type   varchar(50) -- 柜型（20GP、40GP、40HQ）
       , container_qty    numeric(38,10) -- 柜型量
  )on commit preserve rows distribute by hash(period_id,container_type); /*replication;*/

  -- 创建 route_info_sum_tmp5 临时表
  drop table if exists route_info_sum_tmp5;
	create temporary table route_info_sum_tmp5(
         version_id       int -- 版本ID
       , year             int -- 年份
       , source_port_name varchar(50) -- 起始港
       , dest_port_name   varchar(50) -- 目的港
       , region_cn_name   varchar(50) -- 目的港区域
       , container_qty    numeric(38,10) -- 柜型量
       , weight           numeric(38,10) -- 权重
       , sort_num         int
       , sum_weight       numeric(38,10)
       , top_route_flag   varchar(10)
  )on commit preserve rows distribute by hash(source_port_name,dest_port_name); /*replication;*/
  
  select max(version_id) as max_version_id into v_max_version_id from fin_dm_opt_foi.dm_fol_route_price_info_sum_t where upper(del_flag) = 'N';
  
  if(p_refresh_type is null or p_refresh_type = '') then
    -- 清理已经写入版本信息表的数据
    delete from fin_dm_opt_foi.dm_fol_version_info_t
     where version_id = nvl(p_version_id,v_max_version_id)
       and source_en_name = 'f_dm_fol_route_info_t'
       and refresh_type = '4_AUTO'
       and step = 1
       and upper(del_flag) = 'N'
    ;
    
  elseif(p_refresh_type = '1_刷新价格表') then 
    -- 清理已经写入版本信息表的数据
    delete from fin_dm_opt_foi.dm_fol_version_info_t
     where upper(del_flag) = 'N'
       and version_code = to_char(current_date,'yyyymmdd') 
       and ((refresh_type = '2_刷新系统' and  source_en_name is null) or (refresh_type = '4_AUTO' and  source_en_name ='f_dm_fol_route_info_t'))
    ;
  end if;
  
  if(p_refresh_type = '' or p_refresh_type is null) then
    -- 成功数据写入到版本信息表
    insert into fin_dm_opt_foi.dm_fol_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select nvl(p_version_id,v_max_version_id)   as version_id
         , v_route_version_code as version_code
         , 2 as step
         , 'f_dm_fol_route_info_t' as source_en_name
         , '航线清单函数' as source_cn_name
         , '4_AUTO' as refresh_type
         , '' as remark
         , -1 as created_by
         , current_timestamp as creation_date
         , -1 as last_updated_by
         , current_timestamp as last_update_date
         , 'N' as del_flag
  ;
  
  elseif(p_refresh_type = '1_刷新价格表') then
    -- 成功数据写入到版本信息表
    insert into fin_dm_opt_foi.dm_fol_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select nvl(p_version_id,v_max_version_id)   as version_id
         , v_route_version_code as version_code
         , 2 as step
         , 'f_dm_fol_route_info_t' as source_en_name
         , '航线清单函数' as source_cn_name
         , '4_AUTO' as refresh_type
         , '' as remark
         , -1 as created_by
         , current_timestamp as creation_date
         , -1 as last_updated_by
         , current_timestamp as last_update_date
         , 'N' as del_flag
    ;
  
  end if;
  
  
  
  insert into route_info_sum_tmp1(
         version_id        -- 版本ID
       , year              -- 年份
       , period_id         -- 会计期
       , transport_mode    -- 运输方式（精品海运、Xeneta）
       , source_port_name  -- 起始港
       , dest_port_name    -- 目的港
       , route             -- 航线（目的港_国家）
       , region_cn_name    -- 目的港区域
       , container_type    -- 柜型（20GP、40GP、40HQ）
       , container_qty     -- 柜型量
  )
  -- 数据入到临时表
  -- 20GP数量×1，40GP数量×2，40HQ数量×2
  select version_id  -- 版本ID
       , year              -- 年份
       , period_id         -- 会计期
       , transport_mode    -- 运输方式（精品海运、Xeneta）
       , source_port_name  -- 起始港
       , dest_port_name    -- 目的港
       , route             -- 航线（目的港_国家）
       , region_cn_name    -- 目的港区域
       , container_type    -- 柜型（20GP、40GP、40HQ）
       , sum(container_qty) as container_qty     -- 柜型量
    from fin_dm_opt_foi.dm_fol_route_price_info_sum_t
   where version_id = nvl(p_version_id,v_max_version_id)  -- 取最大版本ID的数据
     and upper(del_flag) = 'N'
     and container_type is not null
   group by version_id
       , year
       , period_id
       , transport_mode
       , source_port_name
       , dest_port_name
       , route
       , region_cn_name
       , container_type
  ;

  -- 数据入到临时表
  insert into route_info_sum_tmp5(
         version_id        -- 版本ID
       , year              -- 年份
       , source_port_name  -- 起始港
       , dest_port_name    -- 目的港
       , region_cn_name    -- 目的港区域
       , container_qty     -- 柜型量
       , weight            -- 权重
       , sort_num
       , sum_weight
       , top_route_flag
  )
  -- 按年、航线汇总柜型量
  with route_info_sum_tmp2 as(
  select version_id        -- 版本ID
       , year              -- 年份
       , source_port_name  -- 起始港
       , dest_port_name    -- 目的港
       , region_cn_name    -- 目的港区域
       , sum(container_qty) as  container_qty  -- 柜型量
    from route_info_sum_tmp1
   group by version_id
       , year
       , source_port_name
       , dest_port_name
       , region_cn_name
  ),
  -- 按年汇总柜型量，用于计算权重（即货量占比）
  route_info_sum_tmp6 as(
  select version_id        -- 版本ID
       , year              -- 年份
       , sum(container_qty) as  container_qty  -- 柜型量
    from route_info_sum_tmp1
   group by version_id
       , year
  ),
  -- 权重计算=当年的各个航线的量/当年总航线的量
  route_info_sum_tmp7 as(
  select t1.version_id
       , t1.year
       , t1.source_port_name
       , t1.dest_port_name
       , t1.region_cn_name
       , round((case when t2.container_qty = 0 then 0
                     when t1.container_qty = 0 then 0
                     else t1.container_qty/t2.container_qty
                 end),10) as weight  -- 权重
    from route_info_sum_tmp2 t1  -- 航线临时表
    left join route_info_sum_tmp6 t2  -- 区域汇总表
      on t1.version_id = t2.version_id
     and t1.year = t2.year
  ),
  -- 按年倒序柜型量
  route_info_sum_tmp3 as(
  select t1.version_id        -- 版本ID
       , t1.year              -- 年份
       , t1.source_port_name  -- 起始港
       , t1.dest_port_name    -- 目的港
       , t1.region_cn_name    -- 目的港区域
       , t1.container_qty     -- 柜型量
       , t2.weight            -- 权重
       , row_number() over(partition by t1.version_id, t1.year order by t1.container_qty desc) as sort_num
    from route_info_sum_tmp2 t1
    left join route_info_sum_tmp7 t2
      on t1.version_id = t2.version_id
     and t1.year = t2.year
     and t1.source_port_name = t2.source_port_name
     and t1.dest_port_name = t2.dest_port_name
     and t1.region_cn_name = t2.region_cn_name
  ),
  route_info_sum_tmp9 as(
  select version_id        -- 版本ID
       , year              -- 年份
       , source_port_name  -- 起始港
       , dest_port_name    -- 目的港
       , region_cn_name    -- 目的港区域
       , container_qty     -- 柜型量
       , weight            -- 权重
       , sort_num
       , sum(weight) over(partition by version_id, year order by sort_num) as sum_weight
    from route_info_sum_tmp3
  ),
  -- 计算每年的85%
  route_info_sum_tmp4 as(
  select version_id        -- 版本ID
       , year              -- 年份
       , source_port_name  -- 起始港
       , dest_port_name    -- 目的港
       , region_cn_name    -- 目的港区域
       , container_qty     -- 柜型量
       , weight            -- 权重
       , sort_num
       , sum_weight
       , (case when sum_weight < 0.85 then 'Y' else 'N' end) as top_route_flag
    from route_info_sum_tmp9
  ),
  route_info_sum_tmp10 as(
  select version_id        -- 版本ID
       , year              -- 年份
       , max(sort_num) as max_sort_num
    from route_info_sum_tmp4
   where top_route_flag = 'Y'
   group by version_id
       , year
  )
  -- 取当年的85%清单
  select t1.version_id        -- 版本ID
       , t1.year              -- 年份
       , t1.source_port_name  -- 起始港
       , t1.dest_port_name    -- 目的港
       , t1.region_cn_name    -- 目的港区域
       , t1.container_qty     -- 柜型量
       , t1.weight            -- 权重
       , t1.sort_num
       , t1.sum_weight
       , (case when t1.sort_num = t2.max_sort_num+1 then 'Y' else t1.top_route_flag end) as top_route_flag
    from route_info_sum_tmp4 t1
    left join route_info_sum_tmp10 t2
      on t1.version_id = t2.version_id
     and t1.year = t2.year
   where t1.year = (to_char(current_date,'yyyy'))::int
   union all
  -- 取去年的85%清单
  select t1.version_id        -- 版本ID
       , t1.year              -- 年份
       , t1.source_port_name  -- 起始港
       , t1.dest_port_name    -- 目的港
       , t1.region_cn_name    -- 目的港区域
       , t1.container_qty     -- 柜型量
       , t1.weight            -- 权重
       , t1.sort_num
       , t1.sum_weight
       , (case when t1.sort_num = t2.max_sort_num+1 then 'Y' else t1.top_route_flag end) as top_route_flag
    from route_info_sum_tmp4 t1
    left join route_info_sum_tmp10 t2
      on t1.version_id = t2.version_id
     and t1.year = t2.year
   where t1.year = (to_char(current_date,'yyyy'))::int -1
  ;

  v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写入日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
      p_log_version_id => null,                 --版本
      p_log_sp_name => v_sp_name,    --sp名称
      p_log_para_list => '',--参数
      p_log_step_num  => 2,
      p_log_cal_log_desc => '去年与今年Top 85%数据入到临时表 route_info_sum_tmp5，数据量：'||v_dml_row_count,--日志描述
      p_log_formula_sql_txt => null,--错误信息
      p_log_row_count => v_dml_row_count,
      p_log_errbuf => null  --错误编码
  ) ;

  -- 自动调度时，如果已存在当前年月日的版本，则需要先删除
  delete from fin_dm_opt_foi.dm_fol_route_info_t where version_code = to_char(current_date,'yyyymmdd') /*and version_status = 'AUTO'*/ and upper(del_flag) = 'N';

  -- 数据入到目标表
  insert into fin_dm_opt_foi.dm_fol_route_info_t(
           version_code         -- 版本编码
         , year                 -- 年份
         , transport_mode       -- 运输方式
         , source_port_name     -- 起始港
         , dest_port_name       -- 目的港
         , region_cn_name       -- 区域
         , route                -- 航线（目的港_国家）
         , top_route_flag       -- Top航线标识（Y 是、N 否）
         , weight	              -- 权重
         , version_status       -- 版本状态（AUTO 调度生成、ADJUST 前台页面编辑、FINAL 前台页面刷新）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    with route_info_sum_tmp8 as(
    select distinct version_id        -- 版本ID
          , year              -- 年份
          , transport_mode    -- 运输方式（精品海运、Xeneta）
          , source_port_name  -- 起始港
          , dest_port_name    -- 目的港
          , route             -- 航线（目的港_国家）
          , region_cn_name    -- 目的港区域
       from route_info_sum_tmp1
    ),
    route_info_sum_tmp11 as(
    select distinct version_id        -- 版本ID
         , source_port_name
         , dest_port_name  -- 起始港
         , region_cn_name    -- 目的港区域
         , top_route_flag
     from route_info_sum_tmp5
    ),
    route_info_sum_tmp12 as(
    select version_id        -- 版本ID
         , source_port_name
         , dest_port_name  -- 起始港
         , region_cn_name    -- 目的港区域
         , top_route_flag
         , row_number() over(partition by version_id,source_port_name,dest_port_name,region_cn_name order by top_route_flag) as rn
      from route_info_sum_tmp11
    ),
    route_info_sum_tmp13 as(
    select version_id        -- 版本ID
         , source_port_name
         , dest_port_name  -- 起始港
         , region_cn_name    -- 目的港区域
         , 'Y' as top_route_flag
      from route_info_sum_tmp12
     where rn = 2
    ),
    route_info_sum_tmp14 as(
    select t1.version_id        -- 版本ID
         , t1.year              -- 年份
         , t1.source_port_name  -- 起始港
         , t1.dest_port_name    -- 目的港
         , t1.region_cn_name    -- 目的港区域
         , t1.weight            -- 权重
         , (case when t2.top_route_flag is not null then t2.top_route_flag else t1.top_route_flag end) as top_route_flag
      from route_info_sum_tmp5 t1
      left join route_info_sum_tmp13 t2
        on t1.version_id = t2.version_id
       and t1.source_port_name = t2.source_port_name
       and t1.dest_port_name = t2.dest_port_name
       and t1.region_cn_name = t2.region_cn_name
    )    
    select to_char(current_date,'yyyymmdd') as version_code  -- 版本编码
         , t1.year              -- 年份
         , t1.transport_mode    -- 运输方式
         , t1.source_port_name  -- 起始港
         , t1.dest_port_name    -- 目的港
         , t1.region_cn_name    -- 目的港区域
         , t1.route              -- 航线（目的港_国家）
         , t2.top_route_flag     -- Top航线标识（Y 是、N 否）
         , t2.weight	           -- 权重
         , 'AUTO' as version_status
         , '' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
      from route_info_sum_tmp8 t1
      join route_info_sum_tmp14 t2
        on t1.version_id = t2.version_id
       and t1.year = t2.year
       and t1.source_port_name = t2.source_port_name
       and t1.dest_port_name = t2.dest_port_name
       and t1.region_cn_name = t2.region_cn_name
  ;

  v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写入日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
      p_log_version_id => null,                 --版本
      p_log_sp_name => v_sp_name,    --sp名称
      p_log_para_list => '',--参数
      p_log_step_num  => 3,
      p_log_cal_log_desc => '数据入到目标表，数据量：'||v_dml_row_count,--日志描述
      p_log_formula_sql_txt => null,--错误信息
      p_log_row_count => v_dml_row_count,
      p_log_errbuf => null  --错误编码
  ) ;

  -- 获取Top清单表中最大版本编码
  select max(version_code) as max_version_code into v_route_version_code from fin_dm_opt_foi.dm_fol_route_info_t;

  -- 清理已经写入版本信息表的数据
  delete from fin_dm_opt_foi.dm_fol_version_info_t
   where version_id = nvl(p_version_id,v_max_version_id)
     and version_code = v_route_version_code
     and source_en_name = 'f_dm_fol_route_info_t'
     and refresh_type = nvl(p_refresh_type,'4_AUTO')
     and step = 1
     and upper(del_flag) = 'N'
  ;
  
  if(p_refresh_type = '' or p_refresh_type is null) then
    -- 成功数据写入到版本信息表，需要更新对应的“执行中”数据状态
    update fin_dm_opt_foi.dm_fol_version_info_t set step = 1, version_code = v_route_version_code
     where version_id = nvl(p_version_id,v_max_version_id)
       and source_en_name = 'f_dm_fol_route_info_t'
       and refresh_type = '4_AUTO'
       and upper(del_flag) = 'N'
    ;
    
  elseif(p_refresh_type = '1_刷新价格表') then
    -- 成功数据写入到版本信息表，需要更新对应的“执行中”数据状态
    update fin_dm_opt_foi.dm_fol_version_info_t set step = 1, version_code = v_route_version_code
     where version_id = nvl(p_version_id,v_max_version_id)
       and source_en_name = 'f_dm_fol_route_info_t'
       and refresh_type = '4_AUTO'
       and upper(del_flag) = 'N'
    ;
  end if;

  v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写入日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
    p_log_version_id => null,                 --版本
    p_log_sp_name => v_sp_name,    --sp名称
    p_log_para_list => '',--参数
    p_log_step_num  => 4,
    p_log_cal_log_desc => 'Top航线清单表的最大版本：'||v_route_version_code||'，获取的版本ID：'||nvl(p_version_id,v_max_version_id)||'，写入到版本信息表，数据量：'||v_dml_row_count||'，运行结束！',--日志描述
    p_log_formula_sql_txt => null,--错误信息
    p_log_row_count => v_dml_row_count,
    p_log_errbuf => null  --错误编码
  ) ;

  exception
  	when others then

      perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		    p_log_row_count => null,
		    p_log_errbuf => sqlstate  --错误编码
      ) ;
	x_success_flag := '2001';	         --2001表示失败

  -- 失败信息写入到版本信息表
  insert into fin_dm_opt_foi.dm_fol_version_info_t(
         version_id           -- 版本ID（自动生成）
       , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
       , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
       , source_en_name       -- 来源英文描述（可以是表名、函数名等）
       , source_cn_name       -- 来源中文描述
       , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
       , remark               -- 备注
       , created_by           -- 创建人
       , creation_date        -- 创建时间
       , last_updated_by      -- 修改人
       , last_update_date     -- 修改时间
       , del_flag             -- 是否删除
  )
  select nvl(p_version_id,v_max_version_id) as version_id
       , v_route_version_code as version_code
       , 2001 as step
       , 'f_dm_fol_route_info_t' as source_en_name
       , '航线清单函数' as source_cn_name
       , nvl(p_refresh_type,'4_AUTO') as refresh_type
       , '' as remark
	     , -1 as created_by
	     , current_timestamp as creation_date
	     , -1 as last_updated_by
	     , current_timestamp as last_update_date
	     , 'N' as del_flag
    ;

  --收集统计信息
  analyse fin_dm_opt_foi.dm_fol_route_info_t;
  analyse fin_dm_opt_foi.dm_fol_version_info_t;

end;
$$
/

