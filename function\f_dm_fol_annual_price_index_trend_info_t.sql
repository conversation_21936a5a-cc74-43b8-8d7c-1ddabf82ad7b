-- Name: f_dm_fol_annual_price_index_trend_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fol_annual_price_index_trend_info_t(p_version_id integer DEFAULT NULL::integer, p_refresh_type character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：2024-02-19
创建人  ：qwx1110218
背景描述：物流价格指数年度趋势表的加工规则：1、层级：柜型-精品海运-区域-航线-供应商（先分柜型）；
                                            2、基期：去年；
                                            3、权重：各层级	当年的货量占比，以计算2023、欧洲区域、40HQ年度涨跌幅为例，欧洲区域下各航线的货量占比即各航线在区域下的权重，
                                               此权重计算公式为，以区域下航线权重为例（香港-鹿特丹；区域为欧洲，40HQ柜型）：香港-鹿特丹40HQ下的当年的量/欧洲40HQ下当年的量；
                                            4、计算公式： P_1为区域下航线的当年年均价，P_0为为区域下航线的去年的年均价，W_X为当年航线在区域的货量占比；
                                            5、年均价计算逻辑：
                                               5.1）柜型-精品海运-区域-航线-供应商均价：（EMC供应商、香港-鹿特丹、40HQ柜型）
                                                   (1)EMC供应商年均价： 按年加和[（EMC下40HQ柜型的月燃油价格+EMC下40HQ柜型月框招价格） ×EMC下40HQ月量] / EMC下40HQ年总量
                                                   (2)EMC下40HQ柜型的202001燃油价格，由航线价格表的BAF价格字段以及40HQ字段决定
                                                   (3)EMC下40HQ柜型202001框招价格，由航线价格表的PTP价格字段以及40HQ字段决定
                                               5.2）柜型-精品海运-区域-航线均价：（香港-鹿特丹、40HQ柜型）
                                                   (1)香港-鹿特丹年均价： 按年加和{卷积至航线[（供应商下40HQ柜型的月燃油价格+供应商下40HQ柜型月框招价格） ×供应商下40HQ月量]} / 航线下所有供应商40HQ年总量
                                                   (2)EMC下40HQ柜型的月燃油价格，由航线价格表的BAF价格字段以及40HQ字段决定
                                                   (4)EMC下40HQ柜型月框招价格，由航线价格表的PTP价格字段以及40HQ字段决定
                                            6、年均价缺失逻辑的计算规则：举例：当前年=2022年，上一年=2021年；
                                               6.1）2022年无年均价，2021年有年均价，则提示：2022年无数据，无法计算涨跌幅；
                                               6.2）2022年无年均价，2021年无年均价，则提示：2021、2022年无数据，无法计算涨跌幅；
                                               6.3）2022年有年均价，2021年有年均价，无需提示；
                                               6.4）2022年有年均价，2021年无年均价，且2021年无法用历史均价补齐，则提示：2021年无数据，且无法用历史年均本补齐；
                                               6.5）2022年有年均价，2021年无年均价，且2021年已用历史均价补齐，则提示：2021年无数据，由2020年均本补齐；
          p_version_id 逻辑：1、自动调度，取版本表最大版本ID+1；2、刷新（页面的刷新价格表、刷新系统）：取java传版本ID；
          p_refresh_type 逻辑：1、自动调度，直接赋值为 4_AUTO；2、刷新（页面的刷新价格表、刷新系统）：取java传的值（1_刷新价格表 或 2_刷新系统）
参数描述：参数(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_fol_annual_price_index_trend_info_t()
变更记录：2024-6-5 qwx1110218 新增非Top航线取数逻辑、新增ALL柜型取数逻辑
变更记录：2024-8-21 qwx1110218 来源表变更：由 dm_fol_route_info_sum_t （价格补录表内关联航线量集成表）变更为 dm_fol_route_price_info_sum_t （价格补录表左关联航线量集成表），
                                           且需要剔除柜型量为空的数据（即 container_qty is not null）；无需将20GP数量×1，40GP数量×2，40HQ数量×2；
*/


declare
	v_sp_name varchar(200) := 'fin_dm_opt_foi.f_dm_fol_annual_price_index_trend_info_t('||p_version_id||','''||p_refresh_type||''')';
	v_tbl_name varchar(200) := 'fin_dm_opt_foi.dm_fol_annual_price_index_trend_info_t';
	v_dml_row_count  int default 0 ;
	v_max_version_id int;  -- 最大版本ID
	v_route_version_code  varchar(30);  -- 航线清单表的版本code
	v_price_version_code varchar(30);  -- 价格补录表的版本code
	v_current_year   int;  -- 当前年


begin

  set enable_force_vector_engine to on;
	x_success_flag := '1';        --1表示成功

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '物流价格指数年度趋势表 '||v_tbl_name||'：开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  -- 创建 all_year_tmp 临时表
  drop table if exists all_year_tmp;
	create temporary table all_year_tmp(
         year int  -- 年份
  )on commit preserve rows distribute by replication
	;

  -- 版本信息表中执行失败的版本ID，目标表中需要清理
  with version_info_2001_tmp as(
  select distinct nvl(version_id,0) as version_id
    from fin_dm_opt_foi.dm_fol_version_info_t
   where step = 2001   -- 执行失败
     and upper(del_flag) = 'N'
  )
  delete from fin_dm_opt_foi.dm_fol_annual_price_index_trend_info_t where upper(del_flag) = 'N' and nvl(version_id,0) in(select nvl(version_id,0) from version_info_2001_tmp)
  ;

  -- 从航线量汇总表取最大版本ID的数据
  select max(version_id) as max_version_id into v_max_version_id from fin_dm_opt_foi.dm_fol_route_price_info_sum_t where upper(del_flag) = 'N';

  -- 如果是自动调度，版本ID则取版本信息表的最大版本ID+1；如果是刷新按钮，则直接取版本信息表的最大版本ID；
  if((p_version_id is null or p_version_id = '') and (p_refresh_type is null or p_refresh_type = '')) then
    -- 从版本表获取航线量汇总表版本ID对应的航线信息表的版本编码
    select max(version_code) as version_code into v_route_version_code
      from fin_dm_opt_foi.dm_fol_version_info_t t1
     where version_id = v_max_version_id
       and source_en_name = 'f_dm_fol_route_info_t'
       and not exists(select distinct version_id
                        from dm_fol_version_info_t t2
                       where t1.version_id = t2.version_id
                         and t2.step in(2001,0,2)
                         and upper(t2.del_flag) = 'N'
                     )
       and upper(t1.del_flag) = 'N'
    ;

    -- 从版本表获取航线量汇总表版本ID对应的物流航线价格补录表的版本编码
    select max(version_code) as version_code into v_price_version_code
      from fin_dm_opt_foi.dm_fol_version_info_t t1
     where version_id = v_max_version_id
       and source_en_name = 'apd_fol_route_price_info_t'
       and not exists(select distinct version_id
                        from dm_fol_version_info_t t2
                       where t1.version_id = t2.version_id
                         and t2.step in(2001,0,2)
                         and upper(t2.del_flag) = 'N'
                     )
       and upper(t1.del_flag) = 'N'
    ;

    -- 清理已经写入版本信息表的数据
    delete from fin_dm_opt_foi.dm_fol_version_info_t
     where version_id = v_max_version_id
       and source_en_name = 'f_dm_fol_annual_price_index_trend_info_t'
       and refresh_type = '4_AUTO'
       and step = 1
       and upper(del_flag) = 'N'
    ;

    -- 成功数据写入到版本信息表
    insert into fin_dm_opt_foi.dm_fol_version_info_t(
         version_id           -- 版本ID（自动生成）
       , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
       , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
       , source_en_name       -- 来源英文描述（可以是表名、函数名等）
       , source_cn_name       -- 来源中文描述
       , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
       , remark               -- 备注
       , created_by           -- 创建人
       , creation_date        -- 创建时间
       , last_updated_by      -- 修改人
       , last_update_date     -- 修改时间
       , del_flag             -- 是否删除
  )
  select v_max_version_id   as version_id
       , v_route_version_code as version_code
       , 2 as step
       , 'f_dm_fol_annual_price_index_trend_info_t' as source_en_name
       , '物流价格指数年度趋势表函数'           as source_cn_name
       , '4_AUTO' as refresh_type
       , 'version_code 是航线清单表的版本编码' as remark
	     , -1 as created_by
	     , current_timestamp as creation_date
	     , -1 as last_updated_by
	     , current_timestamp as last_update_date
	     , 'N' as del_flag
	 union all
	select v_max_version_id   as version_id
       , v_price_version_code as version_code
       , 2 as step
       , 'f_dm_fol_annual_price_index_trend_info_t' as source_en_name
       , '物流价格指数年度趋势表函数'           as source_cn_name
       , '4_AUTO' as refresh_type
       , 'version_code 是物流航线价格补录表的版本编码' as remark
	     , -1 as created_by
	     , current_timestamp as creation_date
	     , -1 as last_updated_by
	     , current_timestamp as last_update_date
	     , 'N' as del_flag
  ;

   elseif((p_version_id is not null or p_version_id <> '') and (p_refresh_type = '1_刷新价格表')) then
    -- 从版本表获取航线量汇总表版本ID对应的物流航线价格补录表的版本编码
    select max(version_code) as version_code into v_price_version_code
      from fin_dm_opt_foi.dm_fol_version_info_t t1
     where version_id = p_version_id
       and source_en_name is null
       and refresh_type = '1_刷新价格表'
       and upper(del_flag) = 'N'
       --and step = 2  -- 价格表刷新时，java会更新“价格补录表”的step=2，所有表数据刷新完成后，才更新“价格补录表”的step=1
    ;

    -- 从版本表获取航线量汇总表版本ID对应的航线信息表的版本编码
    select max(version_code) as version_code into v_route_version_code
      from fin_dm_opt_foi.dm_fol_version_info_t t1
     where version_id < p_version_id
       and source_en_name = 'f_dm_fol_route_info_t'
       and not exists(select distinct version_id
                        from dm_fol_version_info_t t2
                       where t1.version_id = t2.version_id
                         and t2.step in(2001,0,2)
                         and upper(t2.del_flag) = 'N'
                     )
       and upper(t1.del_flag) = 'N'
    ;

    -- 清理已经写入版本信息表的数据
    delete from fin_dm_opt_foi.dm_fol_version_info_t
     where version_id = p_version_id
       and source_en_name = 'f_dm_fol_annual_price_index_trend_info_t'
       and refresh_type = p_refresh_type
       and step = 1
       and upper(del_flag) = 'N'
    ;

    -- 成功数据写入到版本信息表
    insert into fin_dm_opt_foi.dm_fol_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select p_version_id   as version_id
         , v_price_version_code as version_code
         , 2 as step
         , 'f_dm_fol_annual_price_index_trend_info_t' as source_en_name
         , '物流价格指数年度趋势表函数'           as source_cn_name
         , p_refresh_type as refresh_type
         , 'version_code 是物流航线价格补录表的版本编码' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
    ;

   end if;

  -- 获取2020年至当前年的年份
  select (substr(max(period_id),1,4))::int as max_year into v_current_year
    from fin_dm_opt_foi.dm_fol_route_price_info_sum_t
   where version_id = nvl(p_version_id,v_max_version_id)
     and transport_mode = '精品海运'
     and upper(del_flag) = 'N'
  ;

  for i in 2020..v_current_year loop
    insert into all_year_tmp values(i);
    i := i+1;
  end loop;



  -- 清理数据
  delete from fin_dm_opt_foi.dm_fol_annual_price_index_trend_info_t where version_id = nvl(p_version_id,v_max_version_id) and upper(del_flag) = 'N';

  -- 数据入到目标表
  insert into fin_dm_opt_foi.dm_fol_annual_price_index_trend_info_t(
         version_id              -- 版本编码（java传版本ID，即版本信息表的version_id）
       , year                    -- 年份
       , base_year               -- 基期（去年）
       , transport_mode          -- 运输方式（精品海运、Xeneta）
       , region_cn_name          -- 区域
       , route                   -- 航线（起始港_目的港）
       , source_country_name     -- 起始国家
       , dest_country_name       -- 目的国家
       , supplier_short_name     -- LST（即供应商）
       , level_code              -- 层级编码（01、02、03、04）
       , level_desc              -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
       , container_type          -- 柜型（20GP、40GP、40HQ）
       , currency                -- 币种
       , price                   -- 价格
       , container_qty           -- 柜型量
       , avg_price               -- 均价
       , weight                  -- 权重（年份的即货量占比）
       , price_index             -- 价格指数
       , year_miss_flag          -- 年份缺失标识（Y 是 、N 否）
       , apd_year                -- 补齐年份（即缺失年份用的哪年补齐）
       , year_avg_miss_code      -- 年均价缺失情况编码（1、当年无数据，无法计算涨跌幅； 2、当年、去年无数据，无法计算涨跌幅；3、正常计算；4、去年无数据，且无法用历史年均本补齐；5、去年无数据，且用历史年均本补齐；）
       , remark                  -- 备注
       , created_by              -- 创建人
       , creation_date           -- 创建时间
       , last_updated_by         -- 修改人
       , last_update_date        -- 修改时间
       , del_flag                -- 是否删除
  )
  -- 取航线量汇总表的最大版本ID数据
  with route_info_sum_tmp1 as(
  select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
       , year                     -- 年份
       , period_id                -- 会计期
       , transport_mode           -- 运输方式（精品海运、Xeneta）
       , region_cn_name           -- 目的地区域
       , route	                  -- 航线（起始港_目的港）
       , source_country_name      -- 起运地国家
       , dest_country_name        -- 目的地国家
       , supplier_short_name      -- LST（即供应商）
       , price_id
       , container_type           -- 柜型（20GP、40GP、40HQ、ALL）
       , currency                 -- 币种
       , sum(price) as price      -- 价格
       , sum(case when container_type = '20GP' then container_qty
                  when container_type = '40GP' then container_qty/2
                  when container_type = '40HQ' then container_qty/2
             end) as container_qty   -- 柜型量
    from fin_dm_opt_foi.dm_fol_route_price_info_sum_t
   where version_id = nvl(p_version_id,v_max_version_id)
     and transport_mode = '精品海运'
     and upper(del_flag) = 'N'
     and container_qty is not null
   group by version_id
       , year
       , period_id
       , transport_mode
       , region_cn_name
       , route
       , source_country_name
       , dest_country_name
       , supplier_short_name
       , price_id
       , container_type
       , currency
  ),
  route_info_sum_tmp2 as(
  select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
       , year                     -- 年份
       , period_id                -- 会计期
       , transport_mode           -- 运输方式（精品海运、Xeneta）
       , region_cn_name           -- 目的地区域
       , route	                  -- 航线（起始港_目的港）
       , source_country_name      -- 起运地国家
       , dest_country_name        -- 目的地国家
       , supplier_short_name      -- LST（即供应商）
       , container_type           -- 柜型（20GP、40GP、40HQ）
       , currency                 -- 币种
       , price                    -- 价格
       , container_qty            -- 柜型量
       , round(price*container_qty,10) as amt   -- 金额
    from route_info_sum_tmp1
   union all
  select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
       , year                     -- 年份
       , period_id                -- 会计期
       , transport_mode           -- 运输方式（精品海运、Xeneta）
       , region_cn_name           -- 目的地区域
       , route	                  -- 航线（起始港_目的港）
       , source_country_name      -- 起运地国家
       , dest_country_name        -- 目的地国家
       , supplier_short_name      -- LST（即供应商）
       , 'ALL' as container_type  -- 柜型（20GP、40GP、40HQ、ALL）
       , currency                 -- 币种
       , null::numeric price      -- 价格
       , sum(container_qty)       as container_qty  -- 柜型量
       , sum(price*container_qty) as amt            -- 金额
    from route_info_sum_tmp1
   group by version_id
       , year
       , period_id
       , transport_mode
       , region_cn_name
       , route
       , source_country_name
       , dest_country_name
       , supplier_short_name
       , currency
  ),
  -- 按供应商汇总价格、柜型量
  route_info_sum_tmp15 as(
  select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
       , year                     -- 年份
       , transport_mode           -- 运输方式（精品海运、Xeneta）
       , region_cn_name           -- 目的地区域
       , route                    -- 航线（起始港_目的港）
       , source_country_name      -- 起运地国家
       , dest_country_name        -- 目的地国家
       , supplier_short_name      -- LST（即供应商）
       , container_type           -- 柜型（20GP、40GP、40HQ）
       , currency                 -- 币种
       , sum(price)         as price          -- 价格
       , sum(container_qty) as container_qty  -- 柜型量
       , sum(amt)           as amt            -- 量*价
    from route_info_sum_tmp2
   group by version_id
       , year
       , transport_mode
       , region_cn_name
       , route
       , source_country_name
       , dest_country_name
       , supplier_short_name
       , container_type
       , currency
  ),
  -- 均价计算
  route_info_sum_tmp3 as(
  select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
       , year                     -- 年份
       , transport_mode           -- 运输方式（精品海运、Xeneta）
       , region_cn_name           -- 目的地区域
       , route                    -- 航线（起始港_目的港）
       , source_country_name      -- 起运地国家
       , dest_country_name        -- 目的地国家
       , supplier_short_name      -- LST（即供应商）
       , container_type           -- 柜型（20GP、40GP、40HQ）
       , currency                 -- 币种
       , price      -- 价格
       , container_qty  -- 柜型量
       , (case when container_qty = 0 then 0
               else amt/container_qty
          end) as avg_price  -- 均价
    from route_info_sum_tmp15
  ),
  -- 按航线汇总价格、柜型量
  route_info_sum_tmp4 as(
  select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
       , year                     -- 年份
       , transport_mode           -- 运输方式（精品海运、Xeneta）
       , region_cn_name           -- 目的地区域
       , route                    -- 航线（起始港_目的港）
       , source_country_name      -- 起运地国家
       , dest_country_name        -- 目的地国家
       , container_type           -- 柜型（20GP、40GP、40HQ）
       , currency                 -- 币种
       , sum(price) as price      -- 价格
       , sum(container_qty) as container_qty  -- 柜型量
       , (case when sum(container_qty) = 0 then 0
               else sum(amt)/sum(container_qty)
          end) as avg_price  -- 均价
    from route_info_sum_tmp2
   group by version_id
       , year
       , transport_mode
       , region_cn_name
       , route
       , source_country_name
       , dest_country_name
       , container_type
       , currency
  ),
  -- 按区域汇总价格、柜型量
  route_info_sum_tmp5 as(
  select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
       , year                     -- 年份
       , transport_mode           -- 运输方式（精品海运、Xeneta）
       , region_cn_name           -- 目的地区域
       , container_type           -- 柜型（20GP、40GP、40HQ）
       , currency                 -- 币种
       , sum(price) as price      -- 价格
       , sum(container_qty) as container_qty  -- 柜型量
       , (case when sum(container_qty) = 0 then 0
               else sum(amt)/sum(container_qty)
          end) as avg_price  -- 均价
    from route_info_sum_tmp2
   group by version_id
       , year
       , transport_mode
       , region_cn_name
       , container_type
       , currency
  ),
  -- 按运输方式汇总价格、柜型量
  route_info_sum_tmp6 as(
  select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
       , year                     -- 年份
       , transport_mode           -- 运输方式（精品海运、Xeneta）
       , container_type           -- 柜型（20GP、40GP、40HQ）
       , currency                 -- 币种
       , sum(price) as price      -- 价格
       , sum(container_qty) as container_qty  -- 柜型量
       , (case when sum(container_qty) = 0 then 0
               else sum(amt)/sum(container_qty)
          end) as avg_price  -- 均价
    from route_info_sum_tmp2
   group by version_id
       , year
       , transport_mode
       , container_type
       , currency
  ),
  -- 汇总运输方式、区域、航线、供应商层级的数据
  route_info_sum_tmp7 as(
  select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
       , t1.year                     -- 年份
       , t1.transport_mode           -- 运输方式（精品海运、Xeneta）
       , t1.region_cn_name           -- 目的地区域
       , t1.route                    -- 航线（起始港_目的港）
       , t1.source_country_name      -- 起运地国家
       , t1.dest_country_name        -- 目的地国家
       , t1.supplier_short_name      -- LST（即供应商）
       , '04'   as level_code        -- 层级编码（01、02、03、04）
       , '供应商' as level_desc      -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
       , t1.container_type           -- 柜型（20GP、40GP、40HQ）
       , t1.currency                 -- 币种
       , t1.price                    -- 价格
       , t1.container_qty            -- 柜型量
       , t1.avg_price                -- 均价
       , round((case when t2.container_qty = 0 then 0
                     else t1.container_qty/t2.container_qty
                end),10) as weight              -- 权重（即货量占比）
    from route_info_sum_tmp3 t1  -- 供应商信息临时表
    left join route_info_sum_tmp4 t2  -- 航线信息临时表
      on t1.version_id          = t2.version_id
     and t1.year                = t2.year
     and t1.transport_mode      = t2.transport_mode
     and t1.region_cn_name      = t2.region_cn_name
     and t1.route               = t2.route
     and t1.container_type      = t2.container_type
     and t1.currency            = t2.currency
   union all
  select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
       , t1.year                     -- 年份
       , t1.transport_mode           -- 运输方式（精品海运、Xeneta）
       , t1.region_cn_name           -- 目的地区域
       , t1.route                    -- 航线（起始港_目的港）
       , t1.source_country_name      -- 起运地国家
       , t1.dest_country_name        -- 目的地国家
       , '' as supplier_short_name   -- LST（即供应商）
       , '03'   as level_code        -- 层级编码（01、02、03、04）
       , '航线' as level_desc        -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
       , t1.container_type           -- 柜型（20GP、40GP、40HQ）
       , t1.currency                 -- 币种
       , t1.price                    -- 价格
       , t1.container_qty            -- 柜型量
       , t1.avg_price                -- 均价
       , round((case when t2.container_qty = 0 then 0
                     else t1.container_qty/t2.container_qty
                end),10) as weight             -- 权重（即货量占比）
    from route_info_sum_tmp4 t1  -- 航线信息临时表
    left join route_info_sum_tmp5 t2  -- 区域信息临时表
      on t1.version_id          = t2.version_id
     and t1.year                = t2.year
     and t1.transport_mode      = t2.transport_mode
     and t1.region_cn_name      = t2.region_cn_name
     and t1.container_type      = t2.container_type
     and t1.currency            = t2.currency
   union all
  select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
       , t1.year                     -- 年份
       , t1.transport_mode           -- 运输方式（精品海运、Xeneta）
       , t1.region_cn_name           -- 目的地区域
       , '' as route                 -- 航线（起始港_目的港）
       , '' as source_country_name   -- 起运地国家
       , '' as dest_country_name     -- 目的地国家
       , '' as supplier_short_name   -- LST（即供应商）
       , '02'   as level_code        -- 层级编码（01、02、03、04）
       , '区域' as level_desc        -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
       , t1.container_type           -- 柜型（20GP、40GP、40HQ）
       , t1.currency                 -- 币种
       , t1.price                    -- 价格
       , t1.container_qty            -- 柜型量
       , t1.avg_price                -- 均价
       , round(case when t2.container_qty = 0 then 0
                    else t1.container_qty/t2.container_qty
               end,10) as weight     --权重（年份的即货量占比）
    from route_info_sum_tmp5 t1  -- 区域信息临时表
    left join route_info_sum_tmp6 t2  -- 运输方式信息临时表
      on t1.version_id          = t2.version_id
     and t1.year                = t2.year
     and t1.transport_mode      = t2.transport_mode
     and t1.container_type      = t2.container_type
     and t1.currency            = t2.currency
   union all
  select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
       , t1.year                     -- 年份
       , t1.transport_mode           -- 运输方式（精品海运、Xeneta）
       , '' as region_cn_name        -- 目的地区域
       , '' as route                 -- 航线（起始港_目的港）
       , '' as source_country_name   -- 起运地国家
       , '' as dest_country_name     -- 目的地国家
       , '' as supplier_short_name   -- LST（即供应商）
       , '01'   as level_code        -- 层级编码（01、02、03、04）
       , '运输方式' as level_desc    -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
       , t1.container_type           -- 柜型（20GP、40GP、40HQ）
       , t1.currency                 -- 币种
       , t1.price                    -- 价格
       , t1.container_qty            -- 柜型量
       , t1.avg_price                -- 均价
       , 0 as weight                 -- 权重（年份的即货量占比）
    from route_info_sum_tmp6 t1  -- 运输方式信息临时表
  ),
  -- 补齐缺失年份的数据（从2020年到当前年），缺失年份打标识，记录用的哪个年份补录的
  -- 缺失年份打标识
  apd_year_info_tmp1 as(
  select nvl(p_version_id,v_max_version_id) as version_id
       , t1.year as all_year
       , t2.year as miss_year
       , (case when t1.year = t2.year then 'N' else 'Y' end) as year_miss_flag
    from all_year_tmp t1
    left join (select distinct version_id,year from route_info_sum_tmp2) t2
      on t1.year = t2.year
  ),
  apd_year_info_tmp2 as(
  select version_id, all_year, miss_year, year_miss_flag
       , count(miss_year) over(partition by version_id order by all_year, miss_year) as cn
    from apd_year_info_tmp1
  ),
  -- 往前补齐年份
  apd_year_info_tmp3 as(
  select version_id, all_year, miss_year, year_miss_flag, cn
       , (max(miss_year) over(partition by version_id, cn))::int as apd_year
    from apd_year_info_tmp2
  ),
  -- 非缺失年份数据、缺失年份数据
  route_info_sum_tmp8 as(
  select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
       , year                     -- 年份
       , transport_mode           -- 运输方式（精品海运、Xeneta）
       , region_cn_name           -- 目的地区域
       , route                    -- 航线（起始港_目的港）
       , source_country_name      -- 起运地国家
       , dest_country_name        -- 目的地国家
       , supplier_short_name      -- LST（即供应商）
       , level_code               -- 层级编码（01、02、03、04）
       , level_desc               -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
       , container_type           -- 柜型（20GP、40GP、40HQ）
       , currency                 -- 币种
       , price                    -- 价格
       , container_qty            -- 柜型量
       , avg_price                -- 均价
       , weight                   -- 权重（即货量占比）
       , 'N'  as year_miss_flag   -- 缺失年份标识
       , null as apd_year         -- 补齐年份（即缺失年份用的哪年补齐）
    from route_info_sum_tmp7
   union all
  select t1.version_id::int
       , t1.all_year as year      -- 补齐年份
       , t2.transport_mode        -- 运输方式（精品海运、Xeneta）
       , t2.region_cn_name        -- 目的地区域
       , t2.route                 -- 航线（起始港_目的港）
       , t2.source_country_name   -- 起运地国家
       , t2.dest_country_name     -- 目的地国家
       , t2.supplier_short_name   -- LST（即供应商）
       , t2.level_code            -- 层级编码（01、02、03、04）
       , t2.level_desc            -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
       , t2.container_type        -- 柜型（20GP、40GP、40HQ）
       , t2.currency              -- 币种
       , t2.price                 -- 价格
       , t2.container_qty         -- 柜型量
       , t2.avg_price             -- 均价
       , t2.weight                -- 权重（即货量占比）
       , t1.year_miss_flag        -- 缺失年份标识
       , t1.apd_year              -- 补齐年份（即缺失年份用的哪年补齐）
    from apd_year_info_tmp3 t1
    left join route_info_sum_tmp7 t2
      on t1.version_id = t2.version_id
     and t1.apd_year = t2.year
   where t1.year_miss_flag = 'Y'  -- 取缺失年份
  ),
  -- 当前年的数据
  curr_route_info_sum_tmp as(
  select version_id
       , year
       , transport_mode           -- 运输方式（精品海运、Xeneta）
       , region_cn_name           -- 目的地区域
       , route                    -- 航线（起始港_目的港）
       , source_country_name      -- 起运地国家
       , dest_country_name        -- 目的地国家
       , supplier_short_name      -- LST（即供应商）
       , level_code               -- 层级编码（01、02、03、04）
       , level_desc               -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
       , container_type           -- 柜型（20GP、40GP、40HQ）
       , currency                 -- 币种
       , price                    -- 价格
       , container_qty            -- 柜型量
       , avg_price                -- 均价
       , weight                   -- 权重（即货量占比）
       , year_miss_flag           -- 缺失年份标识
       , apd_year                 -- 补齐年份（即缺失年份用的哪年补齐）
       , 'Y' as current_year_flag -- 当年标识（Y 当年 N 上一年）
    from route_info_sum_tmp8
  ),
  -- 上年数据
  last_route_info_sum_tmp as(
  select version_id
       , year+1 as year
       , transport_mode           -- 运输方式（精品海运、Xeneta）
       , region_cn_name           -- 目的地区域
       , route                    -- 航线（起始港_目的港）
       , source_country_name      -- 起运地国家
       , dest_country_name        -- 目的地国家
       , supplier_short_name      -- LST（即供应商）
       , level_code               -- 层级编码（01、02、03、04）
       , level_desc               -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
       , container_type           -- 柜型（20GP、40GP、40HQ）
       , currency                 -- 币种
       , price                    -- 价格
       , container_qty            -- 柜型量
       , avg_price                -- 均价
       , weight                   -- 权重（即货量占比）
       , year_miss_flag           -- 缺失年份标识
       , apd_year                 -- 补齐年份（即缺失年份用的哪年补齐）
       , 'N' as current_year_flag -- 当年标识（Y 当年 N 上一年）
    from route_info_sum_tmp8
  ),
  /*年均价的计算逻辑：当前年/上一年
    年均价计算规则：举例：当前年=2022年，上一年=2021年；
                  1、2022年无年均价，2021年有年均价，则提示：2022年无数据，无法计算涨跌幅；
                  2、2022年无年均价，2021年无年均价，则提示：2021、2022年无数据，无法计算涨跌幅；
                  3、2022年有年均价，2021年有年均价，无需提示；
                  4、2022年有年均价，2021年无年均价，且2021年无法用历史均价补齐，则提示：2021年无数据，且无法用历史年均本补齐；
                  5、2022年有年均价，2021年无年均价，且2021年已用历史均价补齐，则提示：2021年无数据，由2020年均本补齐；
  */
  route_info_sum_tmp9 as(
  select t1.version_id
       , t1.year
       , t1.transport_mode           -- 运输方式（精品海运、Xeneta）
       , t1.region_cn_name           -- 目的地区域
       , t1.route                    -- 航线（起始港_目的港）
       , t1.source_country_name      -- 起运地国家
       , t1.dest_country_name        -- 目的地国家
       , t1.supplier_short_name      -- LST（即供应商）
       , t1.level_code               -- 层级编码（01、02、03、04）
       , t1.level_desc               -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
       , t1.container_type           -- 柜型（20GP、40GP、40HQ）
       , t1.currency                 -- 币种
       , t1.price                    -- 价格
       , t1.container_qty            -- 柜型量
       , t1.avg_price                -- 均价
       , t1.weight                   -- 权重（即货量占比）
       , (case when t1.level_code in('03','04')
               then (case when t2.avg_price = 0 then 0
                          else round(t1.avg_price/t2.avg_price-1,10)
                     end)
          end) as price_index        -- 价格指数
       , t1.year_miss_flag           -- 缺失年份标识
       , (case when t1.year_miss_flag = 'Y' and t1.year_miss_flag = 'Y' then null
               else t2.apd_year
          end) as apd_year                -- 补齐年份（即缺失年份用的哪年补齐）
       , (case when t1.year_miss_flag = 'Y' and t2.year_miss_flag = 'N' then 1  -- 当年无数据，上年有数，无法计算涨跌幅
               when t1.year_miss_flag = 'Y' and t2.year_miss_flag = 'Y' then 2  -- 当年、上年无数据，无法计算涨跌幅
               when t1.year_miss_flag = 'N' and t2.year_miss_flag = 'N' then 3  -- 可以计算涨跌幅
               when t1.year_miss_flag = 'N' and t2.year_miss_flag = 'Y' and t2.apd_year is null then 4  -- 当年有数，上年无数据，且上年无法用历史年均本补齐，无法计算涨跌幅
               when t1.year_miss_flag = 'N' and t2.year_miss_flag = 'Y' and t2.apd_year is not null then 5  -- 当年有数，上年无数据，且上年已用历史年均本补齐，可以计算涨跌幅
          end) as year_avg_miss_code   -- 年均价缺失情况编码
    from curr_route_info_sum_tmp t1  -- 当年数据
    left join last_route_info_sum_tmp t2  -- 上年数据
      on t1.version_id = t2.version_id
     and t1.year = t2.year
     and t1.transport_mode = t2.transport_mode
     and nvl(t1.region_cn_name,'SNULL') = nvl(t2.region_cn_name,'SNULL')
     and nvl(t1.route,'SNULL') = nvl(t2.route,'SNULL')
     and nvl(t1.supplier_short_name,'SNULL') = nvl(t2.supplier_short_name,'SNULL')
     and t1.level_code = t2.level_code
     and t1.container_type = t2.container_type
     and nvl(t1.currency,'SNULL') = nvl(t2.currency,'SNULL')
  ),
  -- 计算区域的涨跌幅=航线涨跌幅*航线权重
  route_info_sum_tmp10 as(
  select version_id
       , year
       , transport_mode           -- 运输方式（精品海运、Xeneta）
       , region_cn_name           -- 目的地区域
       , '02' as level_code       -- 层级编码（01、02、03、04）
       , container_type           -- 柜型（20GP、40GP、40HQ）
       , currency                 -- 币种
       , sum(price_index*weight) as price_index        -- 价格指数
    from route_info_sum_tmp9
   where level_code = '03'
   group by version_id
       , year
       , transport_mode
       , region_cn_name
       , container_type
       , currency
  ),
  route_info_sum_tmp11 as(
  select t1.version_id
       , t1.year
       , t1.transport_mode           -- 运输方式（精品海运、Xeneta）
       , t1.region_cn_name           -- 目的地区域
       , t1.route                    -- 航线（起始港_目的港）
       , t1.source_country_name      -- 起运地国家
       , t1.dest_country_name        -- 目的地国家
       , t1.supplier_short_name      -- LST（即供应商）
       , t1.level_code               -- 层级编码（01、02、03、04）
       , t1.level_desc               -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
       , t1.container_type           -- 柜型（20GP、40GP、40HQ）
       , t1.currency                 -- 币种
       , t1.price                    -- 价格
       , t1.container_qty            -- 柜型量
       , t1.avg_price                -- 均价
       , t1.weight                   -- 权重（即货量占比）
       , t2.price_index              -- 价格指数
       , t1.year_miss_flag           -- 缺失年份标识
       , t1.apd_year                 -- 补齐年份（即缺失年份用的哪年补齐）
       , t1.year_avg_miss_code   -- 年均价缺失情况编码
    from route_info_sum_tmp9 t1
    left join route_info_sum_tmp10 t2
      on t1.version_id = t2.version_id
     and t1.year = t2.year
     and t1.transport_mode = t2.transport_mode
     and t1.region_cn_name = t2.region_cn_name
     and t1.container_type = t2.container_type
     and t1.currency = t2.currency
   where t1.level_code = '02'
  ),
  -- 计算运输方式的涨跌幅=区域涨跌幅*区域权重
  route_info_sum_tmp12 as(
  select version_id
       , year
       , transport_mode           -- 运输方式（精品海运、Xeneta）
       , '01' as level_code       -- 层级编码（01、02、03、04）
       , container_type           -- 柜型（20GP、40GP、40HQ）
       , currency                 -- 币种
       , sum(price_index*weight) as price_index        -- 价格指数
    from route_info_sum_tmp11
   group by version_id
       , year
       , transport_mode
       , container_type
       , currency

  ),
  route_info_sum_tmp13 as(
  select t1.version_id
       , t1.year
       , t1.transport_mode           -- 运输方式（精品海运、Xeneta）
       , t1.region_cn_name           -- 目的地区域
       , t1.route                    -- 航线（起始港_目的港）
       , t1.source_country_name      -- 起运地国家
       , t1.dest_country_name        -- 目的地国家
       , t1.supplier_short_name      -- LST（即供应商）
       , t1.level_code               -- 层级编码（01、02、03、04）
       , t1.level_desc               -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
       , t1.container_type           -- 柜型（20GP、40GP、40HQ）
       , t1.currency                 -- 币种
       , t1.price                    -- 价格
       , t1.container_qty            -- 柜型量
       , t1.avg_price                -- 均价
       , t1.weight                   -- 权重（即货量占比）
       , t2.price_index              -- 价格指数
       , t1.year_miss_flag           -- 缺失年份标识
       , t1.apd_year                 -- 补齐年份（即缺失年份用的哪年补齐）
       , t1.year_avg_miss_code   -- 年均价缺失情况编码
    from route_info_sum_tmp9 t1
    left join route_info_sum_tmp12 t2
      on t1.version_id = t2.version_id
     and t1.year = t2.year
     and t1.transport_mode = t2.transport_mode
     and t1.container_type = t2.container_type
     and t1.currency = t2.currency
   where t1.level_code = '01'
  ),
  route_info_sum_tmp14 as(
  select version_id
       , year
       , transport_mode           -- 运输方式（精品海运、Xeneta）
       , region_cn_name           -- 目的地区域
       , route                    -- 航线（起始港_目的港）
       , source_country_name      -- 起运地国家
       , dest_country_name        -- 目的地国家
       , supplier_short_name      -- LST（即供应商）
       , level_code               -- 层级编码（01、02、03、04）
       , level_desc               -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
       , container_type           -- 柜型（20GP、40GP、40HQ）
       , currency                 -- 币种
       , price                    -- 价格
       , container_qty            -- 柜型量
       , avg_price                -- 均价
       , weight                   -- 权重（即货量占比）
       , price_index              -- 价格指数
       , year_miss_flag           -- 缺失年份标识
       , apd_year                 -- 补齐年份（即缺失年份用的哪年补齐）
       , year_avg_miss_code   -- 年均价缺失情况编码
    from route_info_sum_tmp9
   where level_code in('03','04')
   union all
  select version_id
       , year
       , transport_mode           -- 运输方式（精品海运、Xeneta）
       , region_cn_name           -- 目的地区域
       , route                    -- 航线（起始港_目的港）
       , source_country_name      -- 起运地国家
       , dest_country_name        -- 目的地国家
       , supplier_short_name      -- LST（即供应商）
       , level_code               -- 层级编码（01、02、03、04）
       , level_desc               -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
       , container_type           -- 柜型（20GP、40GP、40HQ）
       , currency                 -- 币种
       , price                    -- 价格
       , container_qty            -- 柜型量
       , avg_price                -- 均价
       , weight                   -- 权重（即货量占比）
       , price_index              -- 价格指数
       , year_miss_flag           -- 缺失年份标识
       , apd_year                 -- 补齐年份（即缺失年份用的哪年补齐）
       , year_avg_miss_code   -- 年均价缺失情况编码
    from route_info_sum_tmp11
   union all
  select version_id
       , year
       , transport_mode           -- 运输方式（精品海运、Xeneta）
       , region_cn_name           -- 目的地区域
       , route                    -- 航线（起始港_目的港）
       , source_country_name      -- 起运地国家
       , dest_country_name        -- 目的地国家
       , supplier_short_name      -- LST（即供应商）
       , level_code               -- 层级编码（01、02、03、04）
       , level_desc               -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
       , container_type           -- 柜型（20GP、40GP、40HQ）
       , currency                 -- 币种
       , price                    -- 价格
       , container_qty            -- 柜型量
       , avg_price                -- 均价
       , weight                   -- 权重（即货量占比）
       , price_index              -- 价格指数
       , year_miss_flag           -- 缺失年份标识
       , apd_year                 -- 补齐年份（即缺失年份用的哪年补齐）
       , year_avg_miss_code   -- 年均价缺失情况编码
    from route_info_sum_tmp13
  )
  select version_id
       , year
       , year-1 as base_year      -- 基期（去年）
       , transport_mode           -- 运输方式（精品海运、Xeneta）
       , region_cn_name           -- 目的地区域
       , route                    -- 航线（起始港_目的港）
       , source_country_name      -- 起运地国家
       , dest_country_name        -- 目的地国家
       , supplier_short_name      -- LST（即供应商）
       , level_code               -- 层级编码（01、02、03、04）
       , level_desc               -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
       , container_type           -- 柜型（20GP、40GP、40HQ）
       , currency                 -- 币种
       , price                    -- 价格
       , container_qty            -- 柜型量
       , avg_price                -- 均价
       , (case when year_avg_miss_code in(1,2) then null
               else weight
          end) as weight          -- 权重（即货量占比）
       , (case when year_avg_miss_code in(1,2) then null
               else sum(price_index)
          end) as price_index     -- 价格指数
       , year_miss_flag           -- 年份缺失标识
       , apd_year                 -- 补齐年份（即缺失年份用的哪年补齐）
       , year_avg_miss_code       -- 年均价缺失情况编码
       , '' as remark
	     , -1 as created_by
	     , current_timestamp as creation_date
	     , -1 as last_updated_by
	     , current_timestamp as last_update_date
	     , 'N' as del_flag
    from route_info_sum_tmp14
   where year_avg_miss_code is not null  -- 排除当年与去年没关联上的数据
   group by version_id
       , year
       , transport_mode
       , region_cn_name
       , route
       , source_country_name
       , dest_country_name
       , supplier_short_name
       , level_code
       , level_desc
       , container_type
       , currency
       , price
       , container_qty
       , avg_price
       , weight
       , year_miss_flag
       , apd_year
       , year_avg_miss_code
  ;

  v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写入日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
      p_log_version_id => null,                 --版本
      p_log_sp_name => v_sp_name,    --sp名称
      p_log_para_list => '',--参数
      p_log_step_num  => 2,
      p_log_cal_log_desc => '数据入到目标表，数据量：'||v_dml_row_count,--日志描述
      p_log_formula_sql_txt => null,--错误信息
      p_log_row_count => v_dml_row_count,
      p_log_errbuf => null  --错误编码
  ) ;

  -- 成功数据写入到版本信息表，需要更新对应的“执行中”数据状态
  update fin_dm_opt_foi.dm_fol_version_info_t set step = 1
   where version_id = nvl(p_version_id,v_max_version_id)
     and source_en_name = 'f_dm_fol_annual_price_index_trend_info_t'
     and refresh_type = nvl(p_refresh_type,'4_AUTO')
     and upper(del_flag) = 'N'
  ;

  v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写入日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
      p_log_version_id => null,                 --版本
      p_log_sp_name => v_sp_name,    --sp名称
      p_log_para_list => '',--参数
      p_log_step_num  => 3,
      p_log_cal_log_desc => '获取的版本ID：'||nvl(p_version_id,v_max_version_id)||'，获取Top航线的版本编码：'||v_route_version_code||'，获取价格补录表的版本编码：'||v_price_version_code||'，写入到版本信息表，数据量：'||v_dml_row_count||'，运行结束！',--日志描述
      p_log_formula_sql_txt => null,--错误信息
      p_log_row_count => v_dml_row_count,
      p_log_errbuf => null  --错误编码
  ) ;

  exception
  	when others then

      perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		    p_log_row_count => null,
		    p_log_errbuf => sqlstate  --错误编码
      ) ;
	x_success_flag := '2001';	         --2001表示失败

  -- 失败信息写入到版本信息表
  insert into fin_dm_opt_foi.dm_fol_version_info_t(
         version_id           -- 版本ID（自动生成）
       , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
       , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
       , source_en_name       -- 来源英文描述（可以是表名、函数名等）
       , source_cn_name       -- 来源中文描述
       , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
       , remark               -- 备注
       , created_by           -- 创建人
       , creation_date        -- 创建时间
       , last_updated_by      -- 修改人
       , last_update_date     -- 修改时间
       , del_flag             -- 是否删除
  )
  select nvl(p_version_id,v_max_version_id)   as version_id
       , v_route_version_code as version_code
       , 2001 as step
       , 'f_dm_fol_annual_price_index_trend_info_t' as source_en_name
       , '物流价格指数年度趋势表函数'               as source_cn_name
       , nvl(p_refresh_type,'4_AUTO')  as refresh_type
       , 'version_code 是航线清单表的' as remark
	     , -1 as created_by
	     , current_timestamp as creation_date
	     , -1 as last_updated_by
	     , current_timestamp as last_update_date
	     , 'N' as del_flag
	 union all
	select nvl(p_version_id,v_max_version_id)   as version_id
       , v_price_version_code as version_code
       , 2001 as step
       , 'f_dm_fol_annual_price_index_trend_info_t' as source_en_name
       , '物流价格指数年度趋势表函数'               as source_cn_name
       , nvl(p_refresh_type,'4_AUTO') as refresh_type
       , 'version_code 是物流航线价格补录表的' as remark
	     , -1 as created_by
	     , current_timestamp as creation_date
	     , -1 as last_updated_by
	     , current_timestamp as last_update_date
	     , 'N' as del_flag
  ;

  --收集统计信息
  analyse fin_dm_opt_foi.dm_fol_annual_price_index_trend_info_t;
  analyse fin_dm_opt_foi.dm_fol_version_info_t;

end;
$$
/

