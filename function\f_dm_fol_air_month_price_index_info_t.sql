-- Name: f_dm_fol_air_month_price_index_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fol_air_month_price_index_info_t(p_period_id bigint DEFAULT NULL::bigint, p_version_id bigint DEFAULT NULL::bigint, p_refresh_type character varying DEFAULT NULL::character varying, p_version_code character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
  	/*
创建人  ：zwx1275798
创建时间：2024年8月27日
背景描述：物流空运价格指数月度表
参数描述： p_period_id：JAVA传入会计期(年月)
           p_version_id   逻辑：1、自动调度，取航线量汇总表的最大版本ID；2、刷新（页面的刷新价格表）：取java传版本ID；
           p_version_code 逻辑：1、自动调度，取价格补录头表的最大版本code；2、刷新（页面的刷新价格表）：取java传版本code；
           p_refresh_type 逻辑：1、自动调度，直接赋值为 4_AUTO；2、刷新（页面的刷新价格表）：取java传的值（1_刷新价格表 ）
		   x_success_flag:返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_fol_air_month_price_index_info_t(202401)
变更记录：202503 zwx1275798 代码优化(代码由2286行缩减至1521行)：
                                          1、将所有with as 临时表修改为temporary会话临时表
                                          2、temporary会话临时表的表结构创建语句删除，将insert temporary语句修改为创建temporary表的逻辑语句
										  3、将最小粒度的成本和是否精品：ALL（精品+货代）的逻辑放在最开始从汇总表取数的地方，删除后面每个指标单独计算ALL的代码。
										  4、各层级的成本、货量、货量占比、指数，需多次计算的逻辑进行整合，精简逻辑，去除冗余代码
*/

declare
	v_sp_name varchar(500) := 'fin_dm_opt_foi.f_dm_fol_air_month_price_index_info_t';
	v_tbl_name varchar(500) := 'fin_dm_opt_foi.dm_fol_air_month_price_index_info_t';
	v_dml_row_count number default 0 ;
	v_apd_max_version_code varchar(30);
	v_max_version_id int;
	
	begin
	x_success_flag := '1';        --1表示成功
	
	
	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => ''||v_tbl_name||'，开始运行！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  
        --从 物流空运价格指数月度表 中，删除版本信息表中执行失败的 version_id 数据
        delete from fin_dm_opt_foi.dm_fol_air_month_price_index_info_t t1 
		 where t1.version_id in (select distinct t2.version_id from fin_dm_opt_foi.dm_fol_air_version_info_t t2 where  t2.step='2001' and t2.transport_mode = '精品海空运');
			
		-- 如果p_version_code为空，则取 物流空运价格补录表头表中 的最大版本编码，如果p_version_code不为空，则取传入的p_version_code  
		 if (p_version_code is null or p_version_code = '') then  
        select max(version_code) as max_version_code into v_apd_max_version_code 
		from fin_dm_opt_foi.apd_fol_air_route_price_heaer_t 
		where upper(status)='FINAL';
		else 
		select  p_version_code into v_apd_max_version_code ;
		end if
          ; 
		
		
        -- 如果是传version_id调函数取JAVA传入的p_version_id
        if p_version_id is not null then 
        select  p_version_id into v_max_version_id ;
		-- 如果是自动调度的则取 物流空运航线量价汇总表 的最大版本ID
        else 
        select max(version_id) as max_version_id into v_max_version_id       
		from fin_dm_opt_foi.dm_fol_air_route_info_sum_t;	
        end if 
        ;		
		
		if p_period_id is null then		
		  -- 清理已经写入版本信息表的数据
         delete from fin_dm_opt_foi.dm_fol_air_version_info_t 
		 where version_id = v_max_version_id 
		 and step = 2 
		 and source_en_name = 'f_dm_fol_air_month_price_index_info_t'
		 and refresh_type = nvl(p_refresh_type,'4_AUTO');
		 
		 -- 将执行步骤：2  执行中 插入版本信息表中的
        insert into fin_dm_opt_foi.dm_fol_air_version_info_t(
                    version_id           -- 版本ID（自动生成）
                  , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
                  , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
                  , source_en_name       -- 来源英文描述（可以是表名、函数名等）
                  , source_cn_name       -- 来源中文描述
                  , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
				  , transport_mode       -- 运输方式（精品空运、精品海运）
                  , remark               -- 备注
                  , created_by           -- 创建人
                  , creation_date        -- 创建时间
                  , last_updated_by      -- 修改人
                  , last_update_date     -- 修改时间
                  , del_flag             -- 是否删除
                  )       
             select v_max_version_id   as version_id
                  , v_apd_max_version_code as version_code
                  , 2 as step
                  , 'f_dm_fol_air_month_price_index_info_t' as source_en_name
                  , '物流空运价格指数月度函数' as source_cn_name
                  , nvl(p_refresh_type,'4_AUTO') as refresh_type
				  , '精品空运' as transport_mode
                  , 'version_code为物流空运框招及燃油价格补录表的最大版本编码' as remark
  	              , -1 as created_by
  	              , current_timestamp as creation_date
  	              , -1 as last_updated_by
  	              , current_timestamp as last_update_date
  	              , 'N' as del_flag
                 ;
				 
            end if
          ; 
		  
		  	--从 物流空运航线量价汇总表 取出 version_id 为最大版本 
			drop table if exists air_route_tmp;
		    create temporary table air_route_tmp
		      as		
			  -- 区分是否精品
		select  version_id
	           ,period_id
			   ,active_period
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name     
               ,dest_port_name       
	           ,dest_country_name
			   ,supplier_short_name
			   ,currency
			   ,sum(price*container_qty) as price
			   ,sum(container_qty) as container_qty
			   ,Huawei_group   
			   ,service_level  
			   ,is_high_quality          
		from  fin_dm_opt_foi.dm_fol_air_route_info_sum_t
		where 1=1
		  and version_id = v_max_version_id
		  and price <> 0
		group by version_id
		       ,period_id
			   ,active_period
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name     
               ,dest_port_name       
	           ,dest_country_name
			   ,supplier_short_name
			   ,currency
			   ,Huawei_group   
			   ,service_level  
			   ,is_high_quality
			   union all 
			   -- 不区分是否精品：ALL=精品+货代
			   select  version_id
	           ,period_id
			   ,active_period
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name     
               ,dest_port_name       
	           ,dest_country_name
			   ,supplier_short_name
			   ,currency
			   ,sum(price*container_qty) as price
			   ,sum(container_qty) as container_qty
			   ,Huawei_group   
			   ,service_level  
			   ,'ALL' as is_high_quality          
		from  fin_dm_opt_foi.dm_fol_air_route_info_sum_t
		where 1=1
		  and version_id = v_max_version_id
		  and price <> 0
		group by version_id
		       ,period_id
			   ,active_period
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name     
               ,dest_port_name       
	           ,dest_country_name
			   ,supplier_short_name
			   ,currency
			   ,Huawei_group   
			   ,service_level  	   
			   ;
			   
			   v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 2,
            p_log_cal_log_desc => '【 air_route_tmp 航线量临时表，数据量：'||v_dml_row_count||',version_id为'||v_max_version_id||',切换基期为'||p_period_id,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;

              -- 航线层级月度货量
            drop table if exists air_route_qty_tmp;
		    create temporary table air_route_qty_tmp
		      as
            select version_id
		      ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_port_name     
              ,dest_port_name       
	          ,dest_country_name
			  ,currency
			  ,sum(container_qty) as route_qty
			  ,Huawei_group   
			  ,service_level  
			  ,is_high_quality
    	  from air_route_tmp
         where 1=1
		   and container_qty <> 0	  
		  group by version_id
		          ,period_id
				  ,transport_mode
				  ,region_cn_name
				  ,route
				  ,source_port_name     
                  ,dest_port_name       
	              ,dest_country_name
				  ,currency		
                  ,Huawei_group   
			      ,service_level  
			      ,is_high_quality
				  ;
				  
				  -- 区域层级月度货量
            drop table if exists air_region_qty_tmp;
		    create temporary table air_region_qty_tmp
		      as
            select version_id
		      ,period_id
			  ,transport_mode
			  ,region_cn_name			  
			  ,currency
			  ,sum(container_qty) as region_qty
			  ,Huawei_group   
			  ,service_level  
			  ,is_high_quality
    	  from air_route_tmp
         where 1=1
		   and container_qty <> 0	  
		  group by version_id
		          ,period_id
				  ,transport_mode
				  ,region_cn_name				  
				  ,currency		
                  ,Huawei_group   
			      ,service_level  
			      ,is_high_quality
				  ;
				  
				  -- 运输方式层级月度货量
				  drop table if exists air_transport_qty_tmp;
		    create temporary table air_transport_qty_tmp
		      as
			select version_id
		      ,period_id
			  ,transport_mode
			  ,currency
			  ,sum(container_qty) as transport_qty
			  ,Huawei_group   
			  ,service_level  
			  ,is_high_quality
    	  from air_route_tmp		
		  where 1=1
		   and container_qty <> 0
		  group by version_id
		          ,period_id
				  ,transport_mode
				  ,currency		
                  ,Huawei_group   
			      ,service_level  
			      ,is_high_quality		
                 ;				  

             --计算货量占比 
			 drop table if exists air_qty_tmp;
		    create temporary table air_qty_tmp
		      as      
		-- 航线占区域的货量占比
		select t1.version_id
		      ,t1.period_id
			  ,t1.transport_mode
			  ,t1.region_cn_name
			  ,t1.route
			  ,t1.source_port_name     
              ,t1.dest_port_name       
	          ,t1.dest_country_name
			  ,t1.currency
			  ,'03'   as level_code
  			  ,'航线' as level_desc
			  ,t1.route_qty/t2.region_qty as W_i
			  ,null as W_x
			  ,t1.Huawei_group   
              ,t1.service_level  
			  ,t1.is_high_quality
	    from air_route_qty_tmp t1
		left join air_region_qty_tmp t2
		  on t1.version_id = t2.version_id
		 and t1.transport_mode = t2.transport_mode
		 and t1.region_cn_name = t2.region_cn_name
		 and t1.currency = t2.currency
		 and t1.period_id = t2.period_id
		 and t1.Huawei_group = t2.Huawei_group
		 and t1.service_level = t2.service_level
		 and t1.is_high_quality = t2.is_high_quality
		 union all
		 -- 区域占运输方式的货量占比
		 select t1.version_id
		      ,t1.period_id
			  ,t1.transport_mode
			  ,t1.region_cn_name
			  ,null as route
			  ,null as source_port_name     
              ,null as dest_port_name       
	          ,null as dest_country_name
			  ,t1.currency
			  ,'02'   as level_code
  			  ,'区域' as level_desc
			  ,null as W_i
			  ,t1.region_qty/t2.transport_qty as W_x
			  ,t1.Huawei_group   
			  ,t1.service_level  
			  ,t1.is_high_quality
	    from air_region_qty_tmp t1
		left join air_transport_qty_tmp t2
		  on t1.version_id = t2.version_id
		 and t1.transport_mode = t2.transport_mode
		 and t1.currency = t2.currency
		 and t1.period_id = t2.period_id
		 and t1.Huawei_group = t2.Huawei_group
		 and t1.service_level = t2.service_level
		 and t1.is_high_quality = t2.is_high_quality
		 ; 


         -- 卷积到供应商层级{卷积到月[当日的（华为PTP价+ FUL价格）×当日的货量]}
		  drop table if exists air_supplier_price_tmp;
		    create temporary table air_supplier_price_tmp
		      as
		select version_id
		      ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_port_name     
              ,dest_port_name       
	          ,dest_country_name
			  ,supplier_short_name
			  ,currency
			  ,sum(price) as price
              ,Huawei_group       
              ,service_level      
              ,is_high_quality 
    	  from air_route_tmp		
		  where 1 = 1
		    and container_qty <> 0
			and transport_mode = '精品空运'
		  group by version_id
		      ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_port_name     
              ,dest_port_name       
	          ,dest_country_name
			  ,supplier_short_name
			  ,currency	
              ,Huawei_group       
              ,service_level      
              ,is_high_quality
		         ;
				 
				 -- 精品空运供应商层级下当月的货量 
				  drop table if exists air_supplier_qty_tmp;
		    create temporary table air_supplier_qty_tmp
		      as
		select version_id
		      ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_port_name     
              ,dest_port_name       
	          ,dest_country_name
			  ,supplier_short_name
			  ,currency
			  ,sum(container_qty) as all_qty
			  ,Huawei_group       
              ,service_level      
              ,is_high_quality
    	  from air_route_tmp		
		  where 1 = 1
		   and container_qty <> 0
		   and transport_mode = '精品空运'
		  group by version_id
		          ,period_id
				  ,transport_mode
				  ,region_cn_name
				  ,route
				  ,source_port_name     
                  ,dest_port_name       
	              ,dest_country_name
			      ,supplier_short_name
				  ,currency	
                  ,Huawei_group       
                  ,service_level      
                  ,is_high_quality				  
		            ;
					
			 -- 卷积到航线层级{卷积到月[当日的（华为PTP价+ FUL价格）×当日的货量]}
		 drop table if exists air_route_price_tmp;
		    create temporary table air_route_price_tmp
		      as
		select version_id
		      ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_port_name     
              ,dest_port_name       
	          ,dest_country_name
			  ,currency
			  ,sum(price) as price
              ,Huawei_group       
              ,service_level      
              ,is_high_quality 
    	  from air_route_tmp		
		  where 1 = 1
		    and container_qty <> 0
		  group by version_id
		      ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_port_name     
              ,dest_port_name       
	          ,dest_country_name
			  ,currency	
              ,Huawei_group       
              ,service_level      
              ,is_high_quality
		     ;
			  
		 -- 计算供应商层级和航线层级的均价
		    drop table if exists air_price_tmp;
		    create temporary table air_price_tmp
		      as		
			-- 计算精品空运供应商的均价
		select t1.version_id         
		    ,t1.period_id          
            ,t1.transport_mode     
            ,t1.region_cn_name     
            ,t1.route              
            ,t1.source_port_name     
            ,t1.dest_port_name       
	        ,t1.dest_country_name 
            ,t1.supplier_short_name
            ,t1.currency           
            ,t1.price              
            ,t2.all_qty as container_qty                                    
			,'04'       as level_code
  			,'供应商'   as level_desc
			,round(t1.price/t2.all_qty,10) as avg_price
			,null as apd_period_flag
			,t1.Huawei_group       
            ,t1.service_level      
            ,t1.is_high_quality
	    from air_supplier_price_tmp t1
		left join air_supplier_qty_tmp t2
		  on t1.version_id = t2.version_id
		 and t1.period_id  = t2.period_id
		 and t1.transport_mode = t2.transport_mode
		 and t1.region_cn_name = t2.region_cn_name
		 and t1.route = t2.route
		 and t1.source_port_name  = t2.source_port_name  
         and t1.dest_port_name    = t2.dest_port_name    
	     and t1.dest_country_name = t2.dest_country_name
		 and t1.supplier_short_name = t2.supplier_short_name
		 and t1.currency = t2.currency	
         and t1.Huawei_group = t2.Huawei_group
		 and t1.service_level = t2.service_level
		 and t1.is_high_quality = t2.is_high_quality	
         union all 
		 -- 计算航线层级的均价
         select t1.version_id         
		    ,t1.period_id          
            ,t1.transport_mode     
            ,t1.region_cn_name     
            ,t1.route              
            ,t1.source_port_name     
            ,t1.dest_port_name       
	        ,t1.dest_country_name  
			,null as supplier_short_name
            ,t1.currency           
            ,t1.price              
            ,t2.route_qty as container_qty                                    
			,'03'       as level_code
  			,'航线'   as level_desc
			,round(t1.price/t2.route_qty,10) as avg_price
			,null as apd_period_flag
			,t1.Huawei_group       
            ,t1.service_level      
            ,t1.is_high_quality
	    from air_route_price_tmp t1
		left join air_route_qty_tmp t2
		  on t1.version_id = t2.version_id
		 and t1.period_id  = t2.period_id
		 and t1.transport_mode = t2.transport_mode
		 and t1.region_cn_name = t2.region_cn_name
		 and t1.route = t2.route
		 and t1.source_port_name  = t2.source_port_name  
         and t1.dest_port_name    = t2.dest_port_name    
	     and t1.dest_country_name = t2.dest_country_name
		 and t1.currency = t2.currency	
         and t1.Huawei_group = t2.Huawei_group
		 and t1.service_level = t2.service_level
		 and t1.is_high_quality = t2.is_high_quality		 
		 ;		 
		 
         v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 5,
            p_log_cal_log_desc => '【 air_price_tmp 计算供应商层级和航线层级的均价，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	
		 		 
		 -- 202001至今的所有时间年月
		 drop table if exists time_tmp;
		    create temporary table time_tmp
		      as
              select to_char(generate_series,'YYYYMM')  as period_id
              from generate_series('2020-01-01':: timestamp ,CURRENT_DATE::timestamp, '1 month')
              ;
			  
			  -- 价格表所有维度
			  drop table if exists all_route_tmp;
		    create temporary table all_route_tmp
		      as
		    select distinct               
             transport_mode     
            ,region_cn_name     
            ,route              
            ,source_port_name     
            ,dest_port_name       
	        ,dest_country_name  
            ,currency                                              
            ,Huawei_group       
            ,service_level      
            ,is_high_quality            
          from air_price_tmp
		   ;
		 
		 --  航线均价补齐，均价本月数据存在缺失时，先向之前的月份填充第一个不缺失的均价数据
		insert into air_price_tmp(
		        version_id         
			   ,period_id          
			   ,transport_mode     
			   ,region_cn_name     
			   ,route              
			   ,source_port_name     
               ,dest_port_name       
	           ,dest_country_name  
			   ,currency           
			   ,price              
			   ,container_qty      
			   ,level_code         
			   ,level_desc         
			   ,avg_price          
			   ,apd_period_flag    
			   ,Huawei_group       
			   ,service_level      
			   ,is_high_quality    	
			  )		
		   select v_max_version_id as version_id
		         ,t1.period_id
			     ,t2.transport_mode
			     ,t2.region_cn_name
			     ,t2.route
				 ,t2.source_port_name     
                 ,t2.dest_port_name       
	             ,t2.dest_country_name
			     ,t2.currency
				 ,t3.price 
			     ,t3.container_qty
			     ,t3.level_code
  			     ,t3.level_desc
			     ,t3.avg_price
				 ,'Y' AS apd_period_flag
				 ,t2.Huawei_group       
                 ,t2.service_level      
                 ,t2.is_high_quality
		     from time_tmp t1
			 left join all_route_tmp t2
			 on 1=1
		left join air_price_tmp t3
		on  t2.transport_mode=t3.transport_mode
		and t2.region_cn_name=t3.region_cn_name
		and t2.route = t3.route
		and t2.source_port_name = t3.source_port_name
		and t2.dest_port_name = t3.dest_port_name
		and t2.dest_country_name = t3.dest_country_name
		and t2.currency = t3.currency
		and t2.Huawei_group = t3.Huawei_group
		and t2.service_level = t3.service_level
		and t2.is_high_quality = t3.is_high_quality
		where t3.period_id = (select max(t3.period_id)  
		                        from air_price_tmp t3  
								where t3.period_id < t1.period_id 
								and t3.level_code = '03' 
								and t2.transport_mode=t3.transport_mode
		                        and t2.region_cn_name=t3.region_cn_name
		                        and t2.route = t3.route
								and t2.source_port_name  = t3.source_port_name  
                                and t2.dest_port_name    = t3.dest_port_name    
	                            and t2.dest_country_name = t3.dest_country_name
		                        and t2.currency = t3.currency 
                                and t2.Huawei_group = t3.Huawei_group
                                and t2.service_level = t3.service_level
                                and t2.is_high_quality = t3.is_high_quality
								)
		  and t1.period_id not in ( select distinct t3.period_id  
		                             from air_price_tmp t3 
									 where t3.level_code = '03'  
									 and t2.transport_mode=t3.transport_mode
		                             and t2.region_cn_name=t3.region_cn_name
		                             and t2.route = t3.route
									 and t2.source_port_name  = t3.source_port_name  
                                     and t2.dest_port_name    = t3.dest_port_name    
	                                 and t2.dest_country_name = t3.dest_country_name
		                             and t2.currency = t3.currency
                                     and t2.Huawei_group = t3.Huawei_group
									 and t2.service_level = t3.service_level
                                     and t2.is_high_quality = t3.is_high_quality
									 )
		  and t1.period_id <= (select max(period_id)  from fin_dm_opt_foi.dm_fol_air_route_info_sum_t )
		  and t3.level_code = '03'
		  ;
		  
		  --  航线均价补齐，若之前数据全缺失,则向后面的月份填充第一个不缺失的均本数据
        insert into air_price_tmp(
		        version_id         
			   ,period_id          
			   ,transport_mode     
			   ,region_cn_name     
			   ,route              
			   ,source_port_name     
               ,dest_port_name       
	           ,dest_country_name  
			   ,currency           
			   ,price              
			   ,container_qty      
			   ,level_code         
			   ,level_desc         
			   ,avg_price          
			   ,apd_period_flag    
			   ,Huawei_group       
			   ,service_level      
			   ,is_high_quality    	
			  )
		   select v_max_version_id as version_id
		         ,t1.period_id
			     ,t2.transport_mode
			     ,t2.region_cn_name
			     ,t2.route
			     ,t2.source_port_name     
                 ,t2.dest_port_name       
	             ,t2.dest_country_name
			     ,t2.currency
				 ,t3.price 
			     ,t3.container_qty
			     ,t3.level_code
  			     ,t3.level_desc
			     ,t3.avg_price
				 ,'Y' AS apd_period_flag
				 ,t2.Huawei_group       
                 ,t2.service_level      
                 ,t2.is_high_quality
		     from time_tmp t1
			 left join all_route_tmp t2
			 on 1=1
		left join air_price_tmp t3
		on  t2.transport_mode=t3.transport_mode
		and t2.region_cn_name=t3.region_cn_name
		and t2.route = t3.route
		and t2.source_port_name = t3.source_port_name
		and t2.dest_port_name = t3.dest_port_name
		and t2.dest_country_name = t3.dest_country_name
		and t2.currency = t3.currency
		and t2.Huawei_group = t3.Huawei_group
		and t2.service_level = t3.service_level
		and t2.is_high_quality = t3.is_high_quality
		where t3.period_id = (select min(t3.period_id)  
		                        from air_price_tmp t3  
								where t3.period_id > t1.period_id 
								and t3.level_code = '03' 
								and t2.transport_mode=t3.transport_mode
		                        and t2.region_cn_name=t3.region_cn_name
		                        and t2.route = t3.route
								and t2.source_port_name = t3.source_port_name
		                        and t2.dest_port_name = t3.dest_port_name
		                        and t2.dest_country_name = t3.dest_country_name
		                        and t2.currency = t3.currency
                                and t2.Huawei_group = t3.Huawei_group
		                        and t2.service_level = t3.service_level
		                        and t2.is_high_quality = t3.is_high_quality								
								)
		  and t1.period_id not in ( select distinct t3.period_id  
		                             from air_price_tmp t3 
									 where t3.level_code = '03'  
									 and t2.transport_mode=t3.transport_mode
		                             and t2.region_cn_name=t3.region_cn_name
		                             and t2.route = t3.route
									 and t2.source_port_name = t3.source_port_name
		                             and t2.dest_port_name = t3.dest_port_name
		                             and t2.dest_country_name = t3.dest_country_name
		                             and t2.currency = t3.currency  
									 and t2.Huawei_group = t3.Huawei_group
		                             and t2.service_level = t3.service_level
		                             and t2.is_high_quality = t3.is_high_quality									 							 
									 )
		  and t1.period_id <= (select max(period_id)  from fin_dm_opt_foi.dm_fol_air_route_info_sum_t )
		  and t3.level_code = '03'
		    ;
			
			v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 7,
            p_log_cal_log_desc => '【 air_price_tmp 航线均价补齐，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	
		 
		 
		 --  将均价信息和航线占区域的货量占比插入物流价格指数月度表
			delete from fin_dm_opt_foi.dm_fol_air_month_price_index_info_t where version_id = v_max_version_id ;
			insert into fin_dm_opt_foi.dm_fol_air_month_price_index_info_t (
			        version_id
                   ,period_id
                   ,transport_mode
                   ,region_cn_name
                   ,route
                   ,source_port_name     
                   ,dest_port_name       
	               ,dest_country_name
                   ,supplier_short_name                   
                   ,currency
                   ,price
                   ,container_qty
				   ,level_code
                   ,level_desc
                   ,avg_price
                   ,weight
                   ,apd_period_flag
                   ,Huawei_group
                   ,service_level
                   ,is_high_quality
                   ,remark
                   ,created_by
                   ,creation_date
                   ,last_updated_by
                   ,last_update_date
                   ,del_flag

				   )
			select  t1.version_id         
			       ,t1.period_id			   
			       ,t1.transport_mode     
			       ,t1.region_cn_name     
			       ,t1.route              
			       ,t1.source_port_name     
                   ,t1.dest_port_name       
	               ,t1.dest_country_name 
			       ,t1.supplier_short_name   
			       ,t1.currency           
			       ,t1.price              
			       ,t1.container_qty      
			       ,t1.level_code         
			       ,t1.level_desc         
			       ,t1.avg_price 
                   ,t2.W_i	as weight			   
			       ,case when apd_period_flag = 'Y' 
				         then apd_period_flag 
						 else 'N'  
						 end as apd_period_flag
				   ,t1.Huawei_group
                   ,t1.service_level
                   ,t1.is_high_quality
				   , '' as remark
  	               , -1 as created_by
  	               , current_timestamp as creation_date
  	               , -1 as last_updated_by
  	               , current_timestamp as last_update_date
  	               , 'N' as del_flag
			from air_price_tmp  t1
			left join air_qty_tmp t2
		  on t1.version_id = t2.version_id    
         and t1.transport_mode = t2.transport_mode
         and t1.region_cn_name = t2.region_cn_name
		 and t1.route = t2.route   
         and t1.source_port_name  = t2.source_port_name  
         and t1.dest_port_name    = t2.dest_port_name    
	     and t1.dest_country_name = t2.dest_country_name		 
		 and t1.currency = t2.currency 
	     and t1.level_code =  t2.level_code
		 and t1.period_id = t2.period_id 
		 and t1.Huawei_group = t2.Huawei_group
		 and t1.service_level = t2.service_level
		 and t1.is_high_quality = t2.is_high_quality
            ;
			
			v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 8,
            p_log_cal_log_desc => '【 dm_fol_air_month_price_index_info_t 所有均价信息包括补齐的均价，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	
		 
		 -- 取默认基期航线层级的均价
		    drop table if exists base_price_tmp;
		    create temporary table base_price_tmp
		         as                  
   		       select  version_id
		              ,period_id
			          ,transport_mode
			          ,region_cn_name
			          ,route
			          ,source_port_name     
                      ,dest_port_name       
	                  ,dest_country_name
			          ,currency
			          ,level_code
  			          ,level_desc
			          ,avg_price
					  ,Huawei_group   
                      ,service_level  
			          ,is_high_quality
			   from air_price_tmp
			   where period_id = 202001
			     and level_code = '03'
				 ;
		 
		 
		 -- 默认基期 - 区域层级的价格指数：卷积到区域层级 { 【航线的报告期均价（即202001~至今每个月的均价）/ 航线的默认基期均价（即202001的均价）】 *  航线占区域的权重 }
		
		-- 先计算 精品空运默认基期 - 区域层级的价格指数SUM前的值，航线层级：【航线的报告期均价（即202001~至今每个月的均价）/ 航线的默认基期均价（即202001的均价））】*W_i
		 drop table if exists air_price_index_tmp;
		    create temporary table air_price_index_tmp
		         as 		
	    select t1.version_id
              ,t1.period_id
              ,202001 as base_period_id
              ,202001 as change_period_id
              ,t1.transport_mode
              ,t1.region_cn_name
			  ,t1.route              
			  ,t1.source_port_name     
              ,t1.dest_port_name       
	          ,t1.dest_country_name
			  ,t1.currency
              ,'03'   as level_code
              ,'航线' as level_desc           
              ,case when t2.avg_price = 0 then 0 else (t1.avg_price/t2.avg_price)* t3.W_i end as price_index
			  ,t1.Huawei_group   
              ,t1.service_level  
			  ,t1.is_high_quality
		from air_price_tmp t1
		left join base_price_tmp  t2
		  on t1.version_id = t2.version_id
		 and t1.transport_mode = t2.transport_mode
		 and t1.currency = t2.currency
		 and t1.region_cn_name = t2.region_cn_name
		 and t1.route = t2.route
		 and t1.source_port_name  = t2.source_port_name  
         and t1.dest_port_name    = t2.dest_port_name    
	     and t1.dest_country_name = t2.dest_country_name
		 and t1.level_code = t2.level_code
		 and t1.Huawei_group = t2.Huawei_group
		 and t1.service_level = t2.service_level
		 and t1.is_high_quality = t2.is_high_quality
		left join air_qty_tmp t3
		  on t1.version_id = t3.version_id    
         and t1.transport_mode = t3.transport_mode
         and t1.region_cn_name = t3.region_cn_name
		 and t1.route = t3.route   
         and t1.source_port_name  = t3.source_port_name  
         and t1.dest_port_name    = t3.dest_port_name    
	     and t1.dest_country_name = t3.dest_country_name		 
		 and t1.currency = t3.currency 
		 and t1.period_id = t3.period_id
		 and t1.Huawei_group = t3.Huawei_group
		 and t1.service_level = t3.service_level
		 and t1.is_high_quality = t3.is_high_quality
	   where t1.level_code =  '03'
	    and  t3.level_code =  '03'
        and t3.W_i is not null	
		and t1.transport_mode = '精品空运'  
       union all 
	   -- TAC默认基期 - 区域层级的价格指数SUM前的值，航线层级：【航线的报告期TAC均价（即202001~至今每个月的均价）/ 航线的默认基期TAC均价（即202001的均价）】 *  航线占区域的权重
 select t1.version_id
              ,t1.period_id
              ,202001 as base_period_id
              ,202001 as change_period_id
              ,t1.transport_mode
              ,t1.region_cn_name
			  ,t1.route              
			  ,t1.source_port_name     
              ,t1.dest_port_name       
	          ,t1.dest_country_name
			  ,t1.currency
              ,'03'   as level_code
              ,'航线' as level_desc           
              ,case when t2.avg_price = 0 then 0 else (t1.avg_price/t2.avg_price)* t3.W_i end as price_index   
			  ,t1.Huawei_group   
              ,t1.service_level  
			  ,t1.is_high_quality
		from air_price_tmp t1
		left join base_price_tmp  t2
		  on t1.version_id = t2.version_id
		 and upper(t1.transport_mode) = 'TAC'
		 and t2.transport_mode = '精品空运'		 
		 and t1.currency = t2.currency
		 and t1.region_cn_name = t2.region_cn_name
		 and t1.route = t2.route
		 and t1.source_port_name  = t2.source_port_name  
         and t1.dest_port_name    = t2.dest_port_name    
	     and t1.dest_country_name = t2.dest_country_name
		 and t1.level_code = t2.level_code
		 and t1.Huawei_group = t2.Huawei_group
		 and t1.service_level = t2.service_level
		 and t1.is_high_quality = t2.is_high_quality
		left join air_qty_tmp t3
		  on t1.version_id = t3.version_id
         and t1.transport_mode = t3.transport_mode		  
         and t1.region_cn_name = t3.region_cn_name
		 and t1.route = t3.route     
         and t1.source_port_name  = t3.source_port_name  
         and t1.dest_port_name    = t3.dest_port_name    
	     and t1.dest_country_name = t3.dest_country_name		 
		 and t1.currency = t3.currency 
		 and t1.period_id = t3.period_id
		 and t1.Huawei_group = t3.Huawei_group
		 and t1.service_level = t3.service_level
		 and t1.is_high_quality = t3.is_high_quality
	   where t1.level_code =  '03'
	    and  t3.level_code =  '03'
		and t3.W_i is not null		
		and upper(t1.transport_mode) = 'TAC'  
		and t2.transport_mode = '精品空运'
		and upper(t3.transport_mode) = 'TAC'  	   
            ;
			
			
			v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 9,
            p_log_cal_log_desc => '【 air_price_index_tmp  默认基期-区域层级SUM前的的月度价格指数，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	
		 
		 -- 计算 默认基期 区域层级的价格指数和货量占比，插入物流价格指数月度表
		insert into fin_dm_opt_foi.dm_fol_air_month_price_index_info_t (
	           version_id
              ,period_id
              ,base_period_id
              ,change_period_id
              ,transport_mode
              ,region_cn_name
			  ,level_code
              ,level_desc                            
              ,currency              
              ,price_index
			  ,weight
              ,Huawei_group
              ,service_level
              ,is_high_quality
              ,remark
              ,created_by
              ,creation_date
              ,last_updated_by
              ,last_update_date
              ,del_flag
			  )
       select t1.version_id         
             ,t1.period_id          
             ,t1.base_period_id     
             ,t1.change_period_id   
             ,t1.transport_mode     
             ,t1.region_cn_name                     
             ,'02'   as level_code
		     ,'区域' as level_desc   
             ,t1.currency
             ,sum(t1.price_index)*100   as price_index
			 ,sum(t2.W_x) as weight
			 ,t1.Huawei_group
             ,t1.service_level
             ,t1.is_high_quality
             , '' as remark
  	         , -1 as created_by
  	         , current_timestamp as creation_date
  	         , -1 as last_updated_by
  	         , current_timestamp as last_update_date
  	         , 'N' as del_flag
		 from air_price_index_tmp t1
		 left join air_qty_tmp t2
		   on t1.version_id = t2.version_id    
          and t1.transport_mode = t2.transport_mode
          and t1.region_cn_name = t2.region_cn_name
		  and t1.currency = t2.currency 
		  and t1.period_id = t2.period_id
		  and t1.Huawei_group = t2.Huawei_group
		  and t1.service_level = t2.service_level
		  and t1.is_high_quality = t2.is_high_quality
		where t1.level_code = '03'
		  and t2.level_code = '02'
		group by t1.version_id
		        ,t1.period_id
                ,t1.base_period_id
				,t1.change_period_id
				,t1.transport_mode
				,t1.region_cn_name
				,t1.currency
				,t1.Huawei_group
                ,t1.service_level
                ,t1.is_high_quality
				;
				
				v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 10,
            p_log_cal_log_desc => '【 dm_fol_air_month_price_index_info_t  默认基期-区域层级的月度价格指数，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	
		 
		 -- 默认基期-运输方式层级的月度价格指数 C  = sum（ I默认的全部报告期每个月的价格指数  *W_x    ）
		
		-- 计算 默认基期-运输方式层级的月度价格指数
			 insert into fin_dm_opt_foi.dm_fol_air_month_price_index_info_t(
		        version_id
              ,period_id
              ,base_period_id
              ,change_period_id
              ,transport_mode			                            
              ,currency 
              ,level_code
              ,level_desc  			  
              ,price_index
              ,Huawei_group
              ,service_level
              ,is_high_quality
              ,remark
              ,created_by
              ,creation_date
              ,last_updated_by
              ,last_update_date
              ,del_flag	   
			  )		
	    select t1.version_id
              ,t1.period_id
              ,202001 as base_period_id
              ,202001 as change_period_id
              ,t1.transport_mode
			  ,t1.currency
              ,'01'   as level_code
              ,'运输方式' as level_desc           
              ,sum(t1.price_index* t2.W_x) as price_index
			  ,t1.Huawei_group
              ,t1.service_level
              ,t1.is_high_quality
			  , '' as remark
  	          , -1 as created_by
  	          , current_timestamp as creation_date
  	          , -1 as last_updated_by
  	          , current_timestamp as last_update_date
  	          , 'N' as del_flag
		from fin_dm_opt_foi.dm_fol_air_month_price_index_info_t t1		
		 left join air_qty_tmp t2
		  on t1.version_id = t2.version_id  
         and t1.transport_mode = t2.transport_mode		  
         and t1.region_cn_name = t2.region_cn_name      
		 and t1.currency = t2.currency 
		 and t1.period_id = t2.period_id
		 and t1.Huawei_group = t2.Huawei_group
		 and t1.service_level = t2.service_level
		 and t1.is_high_quality = t2.is_high_quality
	   where t1.version_id = v_max_version_id
		 and t1.level_code =  '02'
		 and t2.level_code =  '02'
		 and t2.W_x is not null	
		 group by t1.version_id
		        ,t1.period_id
				,t1.transport_mode
				,t1.currency
				,t1.Huawei_group
                ,t1.service_level
                ,t1.is_high_quality
            ;
			
				v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 11,
            p_log_cal_log_desc => '【 dm_fol_air_month_price_index_info_t  默认基期-运输方式层级的月度价格指数，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	
		 
		  if p_period_id  is not null and  p_period_id != 202001 then 
		  
		  -- 取出结果表中 区域层级 默认基期精品空运的月度价格指数
		   drop table if exists air_region_index_tmp;
		    create temporary table air_region_index_tmp
		         as 
		select version_id
              ,period_id
              ,base_period_id
              ,change_period_id
              ,transport_mode
              ,region_cn_name
              ,level_code
              ,level_desc
              ,currency
              ,price_index
			  ,Huawei_group
              ,service_level
              ,is_high_quality
		from  fin_dm_opt_foi.dm_fol_air_month_price_index_info_t
		where version_id = v_max_version_id
		  and period_id = p_period_id
		  and base_period_id = 202001
		  and level_code = '02'
		  and transport_mode = '精品空运'
		  ;
			 
        -- 计算 切换基期  精品空运区域层级的价格指数
        insert into fin_dm_opt_foi.dm_fol_air_month_price_index_info_t (
	           version_id
              ,period_id
              ,base_period_id
              ,change_period_id
              ,transport_mode
              ,region_cn_name
			  ,level_code
              ,level_desc                            
              ,currency              
              ,price_index
              ,Huawei_group
              ,service_level
              ,is_high_quality
              ,remark
              ,created_by
              ,creation_date
              ,last_updated_by
              ,last_update_date
              ,del_flag
			  )

	    select t1.version_id
              ,t1.period_id
              ,202001      as base_period_id
              ,p_period_id as change_period_id
              ,t1.transport_mode
              ,t1.region_cn_name
              ,t1.level_code
              ,t1.level_desc
              ,t1.currency
              ,case when t2.price_index = 0 then 0 else (t1.price_index/t2.price_index)*100 end as price_index
			  ,t1.Huawei_group
              ,t1.service_level
              ,t1.is_high_quality
              , '' as remark
  	          , -1 as created_by
  	          , current_timestamp as creation_date
  	          , -1 as last_updated_by
  	          , current_timestamp as last_update_date
  	          , 'N' as del_flag
		from  fin_dm_opt_foi.dm_fol_air_month_price_index_info_t t1
		left join air_region_index_tmp t2
		  on t1.version_id       = t2.version_id
         and t1.base_period_id   = t2.base_period_id
         and t1.change_period_id = t2.change_period_id
         and t1.transport_mode   = t2.transport_mode
         and t1.region_cn_name   = t2.region_cn_name
         and t1.level_code       = t2.level_code
         and t1.level_desc       = t2.level_desc
         and t1.currency         = t2.currency
		 and t1.Huawei_group = t2.Huawei_group
		 and t1.service_level = t2.service_level
		 and t1.is_high_quality = t2.is_high_quality
		where t1.version_id = v_max_version_id
		  and t1.base_period_id = 202001
		  and t1.level_code = '02'
		  and t1.transport_mode = '精品空运' 
		  union all
		  --计算 切换基期  TAC 区域层级的价格指数
		   select t1.version_id
              ,t1.period_id
              ,202001      as base_period_id
              ,p_period_id as change_period_id
              ,t1.transport_mode
              ,t1.region_cn_name
              ,t1.level_code
              ,t1.level_desc
              ,t1.currency
              ,case when t2.price_index = 0 then 0 else (t1.price_index/t2.price_index)*100 end as price_index
			  ,t1.Huawei_group
              ,t1.service_level
              ,t1.is_high_quality
              , '' as remark
  	          , -1 as created_by
  	          , current_timestamp as creation_date
  	          , -1 as last_updated_by
  	          , current_timestamp as last_update_date
  	          , 'N' as del_flag
		from  fin_dm_opt_foi.dm_fol_air_month_price_index_info_t t1
		left join air_region_index_tmp t2
		  on t1.version_id       = t2.version_id
         and t1.base_period_id   = t2.base_period_id
         and t1.change_period_id = t2.change_period_id
         and upper(t1.transport_mode) = 'TAC'   
		 and t2.transport_mode = '精品空运'
         and t1.region_cn_name   = t2.region_cn_name
         and t1.level_code       = t2.level_code
         and t1.level_desc       = t2.level_desc
         and t1.currency         = t2.currency
		 and t1.Huawei_group = t2.Huawei_group
		 and t1.service_level = t2.service_level
		 and t1.is_high_quality = t2.is_high_quality
		where t1.version_id = v_max_version_id
		  and t1.base_period_id = 202001
		  and t1.level_code = '02'
		  and upper(t1.transport_mode) = 'TAC'   
		  and t2.transport_mode = '精品空运'
         ;
		     
		 
		 v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 12,
            p_log_cal_log_desc => '【 dm_fol_air_month_price_index_info_t  切换基期-精品空运区域层级的月度价格指数，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	  
		 
	        -- 取出结果表中 运输方式层级 默认基期精品空运的月度价格指数
		   drop table if exists air_transport_index_tmp;
		    create temporary table air_transport_index_tmp
		         as 
		select version_id
              ,period_id
              ,base_period_id
              ,change_period_id
              ,transport_mode
              ,level_code
              ,level_desc
              ,currency
              ,price_index
			  ,Huawei_group
              ,service_level
              ,is_high_quality
		from  fin_dm_opt_foi.dm_fol_air_month_price_index_info_t
		where version_id = v_max_version_id
		  and period_id = p_period_id
		  and base_period_id = 202001
		  and level_code = '01'
		  and transport_mode = '精品空运'
		  ;
		  
		  
		 -- 计算 切换基期  精品空运运输方式层级的价格指数
        insert into fin_dm_opt_foi.dm_fol_air_month_price_index_info_t (
	           version_id
              ,period_id
              ,base_period_id
              ,change_period_id
              ,transport_mode			                                          
              ,level_code
              ,level_desc 
              ,currency  			  
              ,price_index
              ,Huawei_group
              ,service_level
              ,is_high_quality
              ,remark
              ,created_by
              ,creation_date
              ,last_updated_by
              ,last_update_date
              ,del_flag	   
			  )		
	    select t1.version_id
              ,t1.period_id
              ,202001      as base_period_id
              ,p_period_id as change_period_id
              ,t1.transport_mode
              ,t1.level_code
              ,t1.level_desc
              ,t1.currency
              ,case when t2.price_index = 0  then 0 else (t1.price_index/t2.price_index)*100 end as price_index
              ,t1.Huawei_group
              ,t1.service_level
              ,t1.is_high_quality
              , '' as remark
  	          , -1 as created_by
  	          , current_timestamp as creation_date
  	          , -1 as last_updated_by
  	          , current_timestamp as last_update_date
  	          , 'N' as del_flag
		from  fin_dm_opt_foi.dm_fol_air_month_price_index_info_t t1
		left join air_transport_index_tmp t2
		  on t1.version_id       = t2.version_id
         and t1.base_period_id   = t2.base_period_id
         and t1.change_period_id = t2.change_period_id
         and t1.transport_mode   = t2.transport_mode
         and t1.level_code       = t2.level_code
         and t1.level_desc       = t2.level_desc
         and t1.currency         = t2.currency
		 and t1.Huawei_group = t2.Huawei_group
		 and t1.service_level = t2.service_level
		 and t1.is_high_quality = t2.is_high_quality
		where t1.version_id = v_max_version_id
		  and t1.base_period_id = 202001
		  and t1.level_code = '01'
		  and t1.transport_mode = '精品空运'
		  union all
		    -- 计算 切换基期  TAC运输方式层级的价格指数
		  select t1.version_id
              ,t1.period_id
              ,202001      as base_period_id
              ,p_period_id as change_period_id
              ,t1.transport_mode
              ,t1.level_code
              ,t1.level_desc
              ,t1.currency
              ,case when t2.price_index = 0  then 0 else (t1.price_index/t2.price_index)*100 end as price_index
			  ,t1.Huawei_group
              ,t1.service_level
              ,t1.is_high_quality
              , '' as remark
  	          , -1 as created_by
  	          , current_timestamp as creation_date
  	          , -1 as last_updated_by
  	          , current_timestamp as last_update_date
  	          , 'N' as del_flag
		from  fin_dm_opt_foi.dm_fol_air_month_price_index_info_t t1
		left join air_transport_index_tmp t2
		  on t1.version_id       = t2.version_id
         and t1.base_period_id   = t2.base_period_id
         and t1.change_period_id = t2.change_period_id
         and upper(t1.transport_mode) = 'TAC'   
		 and t2.transport_mode = '精品空运'
         and t1.level_code       = t2.level_code
         and t1.level_desc       = t2.level_desc
         and t1.currency         = t2.currency
		 and t1.Huawei_group = t2.Huawei_group
		 and t1.service_level = t2.service_level
		 and t1.is_high_quality = t2.is_high_quality
		where t1.version_id = v_max_version_id
		  and t1.base_period_id = 202001
		  and t1.level_code = '01'
		  and upper(t1.transport_mode) = 'TAC'   
		  and t2.transport_mode = '精品空运'
          ;
		  
		  v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 14,
            p_log_cal_log_desc => '【 dm_fol_air_month_price_index_info_t  切换基期-运输方式层级的月度价格指数，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	 
		 
		 end if
          ; 
		  
		  if p_period_id is null then
		  
		  	-- 清理已经写入版本信息表的数据
         delete from fin_dm_opt_foi.dm_fol_air_version_info_t 
		 where version_id = v_max_version_id 
		 and step = 1 
		 and source_en_name = 'f_dm_fol_air_month_price_index_info_t'
		 and refresh_type = nvl(p_refresh_type,'4_AUTO');	 
       
		  
        --将版本信息表中的执行步骤改为：1 成功
		update fin_dm_opt_foi.dm_fol_air_version_info_t 
		set step = 1 
		where source_en_name = 'f_dm_fol_air_month_price_index_info_t' 
		  and version_id = v_max_version_id
		  and step = 2
		  and refresh_type = nvl(p_refresh_type,'4_AUTO');
		  
		  
		end if
          ; 
		  
		  v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 15,
            p_log_cal_log_desc => '【 ddm_fol_air_version_info_t  成功数据写入到版本信息表，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	 
		 
		   --收集统计信息
  analyse fin_dm_opt_foi.dm_fol_air_month_price_index_info_t;
  analyse fin_dm_opt_foi.dm_fol_air_version_info_t;
		 
		  exception
    when others then
       perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
         p_log_version_id => null,                 --版本
         p_log_sp_name => v_sp_name,    --sp名称
         p_log_para_list => '',--参数
         p_log_step_num  => null,
         p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
         p_log_formula_sql_txt => sqlerrm,--错误信息
	      p_log_row_count => null,
	      p_log_errbuf => sqlstate  --错误编码
       ) ;
	x_success_flag := '2001';	         --2001表示失败
	
	if p_period_id is null then
	
	 -- 失败数据写入到版本信息表
  insert into fin_dm_opt_foi.dm_fol_air_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
		 , transport_mode       -- 运输方式（精品空运、精品海运）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select v_max_version_id   as version_id
         , v_apd_max_version_code as version_code
         , 2001 as step
         , 'f_dm_fol_air_month_price_index_info_t' as source_en_name
         , '物流空运价格指数月度表' as source_cn_name
         , nvl(p_refresh_type,'4_AUTO') as refresh_type
		 , '精品空运' as transport_mode
         , 'version_code为物流价格补录表的最大版本编码' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
  ;
  
          end if
          ; 

  
end;
$$
/

