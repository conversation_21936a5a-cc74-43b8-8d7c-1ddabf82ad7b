-- Name: f_dm_foi_energy_dim; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_energy_dim(f_caliber_flag character varying, f_cate_version bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年2月18日14点38分
  修改时间：2024年4月26日10点32分
  创建人  ：唐钦
  背景描述：维度关联表（用于年度分析页面下拉框）
  参数描述：f_cate_version ：年度版本号
            x_success_flag ：是否成功
  事例    ：select FIN_DM_OPT_FOI.F_DM_FOI_ENERGY_DIM()
*/

DECLARE
  V_SP_NAME    VARCHAR(100) := 'FIN_DM_OPT_FOI.F_DM_FOI_ENERGY_DIM'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_VERSION_TABLE VARCHAR(100);
  V_FROM_TABLE VARCHAR(100);
  V_TO_TABLE VARCHAR(100);
  V_COLUMN VARCHAR(50);  -- ICT与数字能源差值字段：连续性/集团采购
  V_COLUMN_FLAG VARCHAR(50);
  V_SQL_N_COLUMN VARCHAR(100);
  V_SQL_Y_COLUMN VARCHAR(100);
  V_SQL TEXT;
  
BEGIN
  X_RESULT_STATUS = '1';
  
  -- 开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  -- 判断入参口径为ICT还是数字能源，表信息及部分变量存在差异
  IF F_CALIBER_FLAG = 'I' THEN   -- ICT
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_REPORT_T';   -- 来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_VIEW_INFO_T';   -- 结果表
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_PLAN_VERSION_T';   -- 版本信息表
     V_SQL_N_COLUMN := '''不含连续性影响'' AS CONTINUITY_TYPE';
     V_SQL_Y_COLUMN := '''含连续性影响'' AS CONTINUITY_TYPE';
     V_COLUMN := 'CONTINUITY_TYPE';
     V_COLUMN_FLAG := '不含连续性影响';
  ELSIF F_CALIBER_FLAG = 'E' THEN   -- 数字能源
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T';   -- 来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_VIEW_INFO_T';   -- 结果表
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T';   -- 版本信息表
     V_SQL_N_COLUMN := '''N'' AS GROUP_PUR_FLAG';
     V_SQL_Y_COLUMN := '''Y'' AS GROUP_PUR_FLAG';
     V_COLUMN := 'GROUP_PUR_FLAG';
     V_COLUMN_FLAG := 'ENERGY';
  END IF;

  -- 取刷新数据的版本号
  IF F_CATE_VERSION IS NULL THEN
     V_SQL := '   
         SELECT VERSION_ID
            FROM '||V_VERSION_TABLE||'
            WHERE
             DEL_FLAG = ''N''
             AND STATUS = 1
             AND UPPER(DATA_TYPE) = ''CATEGORY''
             ORDER BY LAST_UPDATE_DATE DESC
             LIMIT 1';
   EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
  ELSE V_VERSION_ID := F_CATE_VERSION;
  END IF; 

  -- 删除年度分析-维度关联表数据:
  EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_TO_TABLE;
  
  -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '清空结果表：'||V_TO_TABLE||'表的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 维表数据插入结果表
  V_SQL := '
  INSERT INTO '||V_TO_TABLE||'
      (VERSION_ID,
       GROUP_LEVEL,
       L2_CEG_CODE,
       L2_CEG_CN_NAME,
       L3_CEG_CODE,
       L3_CEG_CN_NAME,
       L3_CEG_SHORT_CN_NAME,
       L4_CEG_CODE,
       L4_CEG_SHORT_CN_NAME,
       L4_CEG_CN_NAME,
       CATEGORY_CODE,
       CATEGORY_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       SUPPLIER_CODE,
       SUPPLIER_CN_NAME,
       '||V_COLUMN||',
       CREATED_BY,
       CREATION_DATE,
       LAST_UPDATED_BY,
       LAST_UPDATE_DATE,
       DEL_FLAG)
  WITH DIS_DIM_TMP AS(
    SELECT DISTINCT T.L2_CEG_CODE,
           T.L2_CEG_CN_NAME,
           T.L3_CEG_CODE,
           T.L3_CEG_CN_NAME,
           T.L3_CEG_SHORT_CN_NAME,
           T.L4_CEG_CODE,
           T.L4_CEG_SHORT_CN_NAME,
           T.L4_CEG_CN_NAME,
           T.CATEGORY_CODE,
           T.CATEGORY_NAME,
           T.ITEM_CODE,
           T.ITEM_NAME AS ITEM_CN_NAME,
           T.SUPPLIER_CODE,
           T.SUPPLIER_CN_NAME
         FROM '||V_FROM_TABLE||' T
  )
  , LEV_DIM_TMP AS(
    SELECT DISTINCT ''SUPPLIER'' AS GROUP_LEVEL,
           T.L2_CEG_CODE,
           T.L2_CEG_CN_NAME,
           T.L3_CEG_CODE,
           T.L3_CEG_CN_NAME,
           T.L3_CEG_SHORT_CN_NAME,
           T.L4_CEG_CODE,
           T.L4_CEG_SHORT_CN_NAME,
           T.L4_CEG_CN_NAME,
           T.CATEGORY_CODE,
           T.CATEGORY_NAME,
           T.ITEM_CODE,
           T.ITEM_CN_NAME,
           T.SUPPLIER_CODE,
           T.SUPPLIER_CN_NAME,
           NULL AS '||V_COLUMN||'
        FROM DIS_DIM_TMP T
    UNION ALL
    SELECT DISTINCT ''ITEM'' AS GROUP_LEVEL,
           T.L2_CEG_CODE,
           T.L2_CEG_CN_NAME,
           T.L3_CEG_CODE,
           T.L3_CEG_CN_NAME,
           T.L3_CEG_SHORT_CN_NAME,
           T.L4_CEG_CODE,
           T.L4_CEG_SHORT_CN_NAME,
           T.L4_CEG_CN_NAME,
           T.CATEGORY_CODE,
           T.CATEGORY_NAME,
           T.ITEM_CODE,
           T.ITEM_CN_NAME,
           NULL AS SUPPLIER_CODE,
           NULL AS SUPPLIER_CN_NAME,
           NULL AS '||V_COLUMN||'
        FROM DIS_DIM_TMP T
    UNION ALL
    SELECT DISTINCT ''CATEGORY'' AS GROUP_LEVEL,
           T.L2_CEG_CODE,
           T.L2_CEG_CN_NAME,
           T.L3_CEG_CODE,
           T.L3_CEG_CN_NAME,
           T.L3_CEG_SHORT_CN_NAME,
           T.L4_CEG_CODE,
           T.L4_CEG_SHORT_CN_NAME,
           T.L4_CEG_CN_NAME,
           T.CATEGORY_CODE,
           T.CATEGORY_NAME,
           NULL AS ITEM_CODE,
           NULL AS ITEM_CN_NAME,
           NULL AS SUPPLIER_CODE,
           NULL AS SUPPLIER_CN_NAME,
           NULL AS '||V_COLUMN||'
        FROM DIS_DIM_TMP T
    UNION ALL
    SELECT DISTINCT ''LV4'' AS GROUP_LEVEL,
           T.L2_CEG_CODE,
           T.L2_CEG_CN_NAME,
           T.L3_CEG_CODE,
           T.L3_CEG_CN_NAME,
           T.L3_CEG_SHORT_CN_NAME,
           T.L4_CEG_CODE,
           T.L4_CEG_SHORT_CN_NAME,
           T.L4_CEG_CN_NAME,
           NULL AS CATEGORY_CODE,
           NULL AS CATEGORY_NAME,
           NULL AS ITEM_CODE,
           NULL AS ITEM_CN_NAME,
           NULL AS SUPPLIER_CODE,
           NULL AS SUPPLIER_CN_NAME,
           NULL AS '||V_COLUMN||'
        FROM DIS_DIM_TMP T
    UNION ALL
    SELECT DISTINCT ''LV3'' AS GROUP_LEVEL,
           T.L2_CEG_CODE,
           T.L2_CEG_CN_NAME,
           T.L3_CEG_CODE,
           T.L3_CEG_CN_NAME,
           T.L3_CEG_SHORT_CN_NAME,
           NULL AS L4_CEG_CODE,
           NULL AS L4_CEG_SHORT_CN_NAME,
           NULL AS L4_CEG_CN_NAME,
           NULL AS CATEGORY_CODE,
           NULL AS CATEGORY_NAME,
           NULL AS ITEM_CODE,
           NULL AS ITEM_CN_NAME,
           NULL AS SUPPLIER_CODE,
           NULL AS SUPPLIER_CN_NAME,
           T1.'||V_COLUMN||'
        FROM DIS_DIM_TMP T
        LEFT JOIN (
                   SELECT VALUE AS L3_CEG_CN_NAME,REL_VALUE1 AS L3_CEG_CODE,
                          '||V_SQL_N_COLUMN||'   -- 不含集团代采的专家团/不含连续性
                      FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T 
                      WHERE DEL_FLAG = ''N'' AND ENABLE_FLAG = ''Y'' AND PARA_NAME = '''||V_COLUMN_FLAG||'''
                  UNION ALL 
                  SELECT DISTINCT L3_CEG_CN_NAME,L3_CEG_CODE,
                         '||V_SQL_Y_COLUMN||'   -- 所有专家团（含集团代采）/含连续性
                      FROM DIS_DIM_TMP
                      ) T1
        ON T.L3_CEG_CODE = T1.L3_CEG_CODE  
)
-- LV2层级数据+其余层级数据
  SELECT DISTINCT '||V_VERSION_ID||' AS VERSION_ID,
         ''LV2'' AS GROUP_LEVEL,
         T.L2_CEG_CODE,
         T.L2_CEG_CN_NAME,
         NULL AS L3_CEG_CODE,
         NULL AS L3_CEG_CN_NAME,
         NULL AS L3_CEG_SHORT_CN_NAME,
         NULL AS L4_CEG_CODE,
         NULL AS L4_CEG_SHORT_CN_NAME,
         NULL AS L4_CEG_CN_NAME,
         NULL AS CATEGORY_CODE,
         NULL AS CATEGORY_NAME,
         NULL AS ITEM_CODE,
         NULL AS ITEM_CN_NAME,
         NULL AS SUPPLIER_CODE,
         NULL AS SUPPLIER_CN_NAME,
         T.'||V_COLUMN||',
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
      FROM LEV_DIM_TMP T
      WHERE GROUP_LEVEL = ''LV3''  -- 只取LV3层级数据处理
      GROUP BY T.L2_CEG_CODE,
               T.L2_CEG_CN_NAME,
               T.'||V_COLUMN||'
  UNION ALL
  SELECT '||V_VERSION_ID||' AS VERSION_ID,
         GROUP_LEVEL,
         L2_CEG_CODE,
         L2_CEG_CN_NAME,
         L3_CEG_CODE,
         L3_CEG_CN_NAME,
         L3_CEG_SHORT_CN_NAME,
         L4_CEG_CODE,
         L4_CEG_SHORT_CN_NAME,
         L4_CEG_CN_NAME,
         CATEGORY_CODE,
         CATEGORY_NAME,
         ITEM_CODE,
         ITEM_CN_NAME,
         SUPPLIER_CODE,
         SUPPLIER_CN_NAME,
         '||V_COLUMN||',
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
      FROM LEV_DIM_TMP';
     EXECUTE IMMEDIATE V_SQL;

 --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID||',的全量数据到DM_FOI_ENERGY_VIEW_INFO_T表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');       
   
   --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;

  --3.日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集DM_FOI_ENERGY_VIEW_INFO_T统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

