-- Name: f_dm_foc_clear_table_imp; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_clear_table_imp(f_industry_flag character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$



/*
创建时间:2024年2月5日20:36:20
创建人  :李志勇
最后修改时间:2024年6月2日
最后修改人:李志勇
背景描述:清空数据
参数描述:
来源表:
目标表:
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_CLEAR_TABLE_IMP('E')
*/
DECLARE
    V_SP_NAME              VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_CLEAR_TABLE_IMP'; --存储过程名称
    V_STEP_MUM             BIGINT        := 0; --步骤号
    V_SQL                  TEXT; --SQL逻辑
    V_CNT_CATEGORY         BIGINT;
    V_CNT_ITEM             BIGINT;
    V_FROM_TABLE           varchar(100)  := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T';
    V_CATEGORY_MAX_VERSION int8;
    V_ITEM_MAX_VERSION     int8;

BEGIN
    X_RESULT_STATUS = 'SUCCESS';

    --1、开始日志
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
        (F_SP_NAME => V_SP_NAME,
         F_STEP_NUM => V_STEP_MUM,
         F_CAL_LOG_DESC => V_SP_NAME || '开始执行');

    IF F_INDUSTRY_FLAG = 'I' THEN
        --删除不需要的数据
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_CD_DMS;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_RD_DMS;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T_DMS;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_ITEM_DECODE_DTL_T_DMS;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_VIEW_ANNL_COST_T_DMS;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_09CG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_09CI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_09CO;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_09RG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_09RI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_09RO;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_10CG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_10CI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_10CO;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_10RG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_10RI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_10RO;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_11CG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_11CI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_11CO;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_11RG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_11RI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_11RO;
    ELSEIF F_INDUSTRY_FLAG = 'E' THEN
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_CD_DMS;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_RD_DMS;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MID_MONTH_ITEM_T_DMS;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_ITEM_DECODE_DTL_T_DMS;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_VIEW_ANNL_COST_T_DMS;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_09CG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_09CI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_09CO;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_09RG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_09RI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_09RO;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_10CG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_10CI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_10CO;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_10RG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_10RI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_10RO;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_11CG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_11CI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_11CO;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_11RG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_11RI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_11RO;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_12CG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_12CI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_12CO;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_12RG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_12RI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_12RO;
	ELSEIF F_INDUSTRY_FLAG = 'IAS' THEN
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_CD_DMS;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_RD_DMS;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MID_MONTH_ITEM_T_DMS;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_ITEM_DECODE_DTL_T_DMS;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_VIEW_ANNL_COST_T_DMS;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_09CG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_09CI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_09CO;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_09RG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_09RI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_09RO;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_10CG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_10CI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_10CO;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_10RG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_10RI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_10RO;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_11CG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_11CI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_11CO;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_11RG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_11RI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_11RO;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_12CG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_12CI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_12CO;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_12RG;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_12RI;
        TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_12RO;
    END IF;


    --写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
        (F_SP_NAME => V_SP_NAME,
         F_STEP_NUM => V_STEP_MUM,
         F_CAL_LOG_DESC => f_industry_flag ||'清空完成',
         F_DML_ROW_COUNT => 0,
         F_RESULT_STATUS => X_RESULT_STATUS,
         F_ERRBUF => 'SUCCESS');

    RETURN 'SUCCESS';

EXCEPTION
    WHEN OTHERS THEN
        X_RESULT_STATUS := 'FAILED';

        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
            (F_SP_NAME => V_SP_NAME,
             F_CAL_LOG_DESC => V_SP_NAME || '运行失败',
             F_RESULT_STATUS => X_RESULT_STATUS,
             F_ERRBUF => SQLSTATE || ':' || SQLERRM
            );

END



$$
/

