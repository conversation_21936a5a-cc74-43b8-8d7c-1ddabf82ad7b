-- Name: f_dm_foc_item_dtl_inner_old_bak_0920; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_item_dtl_inner_old_bak_0920(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
创建时间：2023/03/21
创建人  ：刘必华
最后修改时间: 2023/05/30
最后修改人: 曹昆
背景描述：1. 贴源层的实际发货明细表关联维表带出重量级团队,采购专家团字段
          2. 将单ITEM的品类数据条目打上标识
参数描述：x_success_flag ：是否成功
事例：SELECT fin_dm_opt_foi.f_dm_foc_item_dtl_inner()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'fin_dm_opt_foi.F_DM_FOC_ITEM_DTL_INNER'; --存储过程名称
  V_VERSION_ID BIGINT ; --新版本号ID, 取自fin_dm_opt_foi.DM_FOC_VERSION_INFO_S
  V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYY-MM'); --新的版本中文名称
  V_DIM_VERSION_ID BIGINT ; --最新的品类-专家团映射维版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 存放当前是否已有版本号标识
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  --清空目标表数据:
  EXECUTE IMMEDIATE 'TRUNCATE TABLE fin_dm_opt_foi.DM_FOC_BOM_ITEM_SHIP_DTL_T';
  
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空fin_dm_opt_foi.DM_FOC_BOM_ITEM_SHIP_DTL_T数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
   -- 查询该月版本是否已存在，若存在，沿用，否则新建 
  SELECT COUNT(1) INTO V_CURRENT_FLAG
	FROM
		fin_dm_opt_foi.DM_FOC_VERSION_INFO_T 
	WHERE
		VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
		AND DEL_FLAG = 'N'
		AND STATUS = 1
		AND UPPER(DATA_TYPE) = 'CATEGORY';
	-- FLAG 不等于0，说明已有版本号，沿用		
  IF V_CURRENT_FLAG <> 0 THEN 
  SELECT VERSION_ID INTO V_VERSION_ID
	FROM
		fin_dm_opt_foi.DM_FOC_VERSION_INFO_T 
	WHERE
		VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
		AND DEL_FLAG = 'N'
		AND STATUS = 1
		AND UPPER(DATA_TYPE) = 'CATEGORY';
  ELSE
  --新版本号赋值
  SELECT NEXTVAL('fin_dm_opt_foi.DM_FOC_VERSION_INFO_S')
    INTO V_VERSION_ID
    FROM DUAL;
     
  --往版本信息表记录本次TOP品类版本号, 版本号为V_VERSION_ID, 依赖的品类专家维V_DIM_VERSION_ID
  INSERT INTO fin_dm_opt_foi.DM_FOC_VERSION_INFO_T
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG)
   VALUES
   (V_VERSION_ID,V_DIM_VERSION_ID,V_VERSION_NAME,1,'AUTO','CATEGORY',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N');
   END IF;
   
  --查询品类专家团映射关系的最新版本号; 
  SELECT MAX(T.VERSION_ID) 
    INTO V_DIM_VERSION_ID 
    FROM fin_dm_opt_foi.DM_FOC_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'DIMENSION'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1;
 
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '往版本信息表记录TOP品类版本号信息, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
  --往目标表里插数
  INSERT INTO fin_dm_opt_foi.DM_FOC_BOM_ITEM_SHIP_DTL_T
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE,
     LV2_PROD_RD_TEAM_CN_NAME,
     LV3_PROD_RND_TEAM_CODE,
     LV3_PROD_RD_TEAM_CN_NAME,
     L3_CEG_CODE,
     L3_CEG_CN_NAME,
     L3_CEG_SHORT_CN_NAME,
     CATEGORY_CODE,
     CATEGORY_CN_NAME,
     ITEM_CODE,
     ITEM_CN_NAME,
     SHIP_QUANTITY,
     RMB_COST_AMT,
     PROD_CODE,
     CN_DESCRIPTION,
     PARENTPARTNUMBER,
     PARENT_SHIP_QUANTITY,
     NON_SALE_FLAG,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     PRIMARY_ID,
     L1_NAME,
     L2_NAME
     )
SELECT     T.VERSION_ID,
           T.PERIOD_YEAR,
           T.PERIOD_ID, --年月
           T.LV0_PROD_RND_TEAM_CODE, --重量级团队LV0代码
           T.LV0_PROD_RD_TEAM_CN_NAME, --重量级团队LV0中文名称
           T.LV1_PROD_RND_TEAM_CODE, --重量级团队LV1代码
           T.LV1_PROD_RD_TEAM_CN_NAME, --重量级团队LV1中文名称
           T.LV2_PROD_RND_TEAM_CODE, --重量级团队LV2代码
           T.LV2_PROD_RD_TEAM_CN_NAME, --重量级团队LV2中文名称
           T.LV3_PROD_RND_TEAM_CODE, --重量级团队LV3代码
           T.LV3_PROD_RD_TEAM_CN_NAME, --重量级团队LV3中文名称
           T.L3_CEG_CODE, --专家团code
           T.L3_CEG_CN_NAME, --专家团名称
           T.L3_CEG_SHORT_CN_NAME, --专家团简称
           T.CATEGORY_CODE, --小类代码
           T.CATEGORY_CN_NAME, --小类名称
           T.ITEM_CODE, --子项ITEM编码
           T.ITEM_CN_NAME, --子项ITEM名称
           T.SHIP_QUANTITY, --子项ITEM发货量
           T.RMB_COST_AMT, --发货成本
           T.PROD_CODE, --产品编码
           T.CN_DESCRIPTION, --产品名称
           T.PARENTPARTNUMBER, --父项ITEM编码
           T.PARENT_SHIP_QUANTITY, --父项编码发货数量（缺少父项ITEM名称字段匹配规则）
           T.NON_SALE_FLAG, --非销售标识
           T.CREATED_BY,
           T.CREATION_DATE,
           T.LAST_UPDATED_BY,
           T.LAST_UPDATE_DATE,
           T.DEL_FLAG,
           T.PRIMARY_ID,
           T.L1_NAME, --盈利颗粒度L1层级
           CASE WHEN T.FLAG = 0 THEN NVL(L2S.L2_NAME,'其他')
                ELSE NVL(T.L2_NAME,'其他')
                END AS L2_NAME --盈利颗粒度L2层级
FROM 
(
    SELECT V_VERSION_ID AS VERSION_ID,
           CAST(SUBSTR(TO_CHAR(D.PERIOD_ID), 1, 4) AS BIGINT) AS PERIOD_YEAR,
           D.PERIOD_ID, --年月
           CP.LV0_PROD_LIST_CODE    AS LV0_PROD_RND_TEAM_CODE, --重量级团队LV0代码
           CP.LV0_PROD_LIST_CN_NAME AS LV0_PROD_RD_TEAM_CN_NAME, --重量级团队LV0中文名称
           CP.LV1_PROD_LIST_CODE    AS LV1_PROD_RND_TEAM_CODE, --重量级团队LV1代码
           CP.LV1_PROD_LIST_CN_NAME AS LV1_PROD_RD_TEAM_CN_NAME, --重量级团队LV1中文名称
           CP.LV2_PROD_LIST_CODE    AS LV2_PROD_RND_TEAM_CODE, --重量级团队LV2代码
           CP.LV2_PROD_LIST_CN_NAME AS LV2_PROD_RD_TEAM_CN_NAME, --重量级团队LV2中文名称
           CP.LV3_PROD_LIST_CODE    AS LV3_PROD_RND_TEAM_CODE, --重量级团队LV3代码
           CP.LV3_PROD_LIST_CN_NAME AS LV3_PROD_RD_TEAM_CN_NAME, --重量级团队LV3中文名称
           ZJ.L3_CEG_CODE, --专家团code
           ZJ.L3_CEG_CN_NAME, --专家团名称
           ZJ.L3_CEG_SHORT_CN_NAME, --专家团简称
           M.ITEM_SUBTYPE_CODE AS CATEGORY_CODE, --小类代码
           M.ITEM_SUBTYPE_CN_NAME AS CATEGORY_CN_NAME, --小类名称
           M.ITEM_CODE, --子项ITEM编码
           M.ITEM_NAME AS ITEM_CN_NAME, --子项ITEM名称
           D.SHIP_QUANTITY, --子项ITEM发货量
           T.RMB_COST_AMT, --发货成本
           CP.PROD_CODE, --产品编码
           CP.CN_DESCRIPTION, --产品名称
           D.PARENTPARTNUMBER, --父项ITEM编码
           D.PARENT_SHIP_QUANTITY, --父项编码发货数量（缺少父项ITEM名称字段匹配规则）
           D.NON_SALE_FLAG, --非销售标识
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG,
           T.PRIMARY_ID,
           COALESCE(L1.L1_NAME, L1B.L1_NAME, CFG.L1_NAME, CFG2.L1_NAME, '其他') AS L1_NAME, --盈利颗粒度L1层级
           L2C.L2_NAME AS L2_NAME, --COA维表盈利颗粒度L2层级
           COUNT(L2C.L2_NAME) OVER(PARTITION BY COALESCE(L1.L1_NAME, L1B.L1_NAME, CFG.L1_NAME, CFG2.L1_NAME, '其他')) AS FLAG --标识COA维表L2_NAME为空的数据为0
      FROM fin_dm_opt_foi.DWL_PROD_BOM_ITEM_SHIP_DIM_I D   ---9月版本已更改
     INNER JOIN DWRDIM.DWR_DIM_MATERIAL_CODE_D M --物料维度表
        ON D.ITEM_CODE = M.ITEM_CODE
     INNER JOIN DMDIM.DM_DIM_PRODUCT_T_D CP --重量级团队匹配维表
        ON D.PROD_KEY = CP.PROD_KEY
       AND CP.LV0_PROD_LIST_CODE = '104364'
       AND CP.PROD_POV_ID = 4
       AND D.SHIP_QUANTITY >= 0 -- 数量非负数
     INNER JOIN fin_dm_opt_foi.DM_FOC_CATG_CEG_ICT_D ZJ --品类-专家团映射维
        ON M.ITEM_SUBTYPE_CODE = ZJ.CATEGORY_CODE
       AND D.MODEL_NUM IN ('P', 'SI') --取P和SI件
       AND D.RECOGNISE_TYPE_ID = 4 --取供应中心发区域（4）
       AND ZJ.VERSION_ID = V_DIM_VERSION_ID
      LEFT JOIN fin_dm_opt_foi.DM_FOC_DATA_PRIMARY_ENCRYPT_T T --数据侧高斯加密表
        ON D.PRIMARY_ID = T.PRIMARY_ID
      LEFT JOIN FIN_DM_OPT_FOP.APD_FOP_ICT_FCST_HOLISTIC_VIEW_T L1 --ICT业务预测全景图,7月版本增加盈利颗粒度L1、L2层级
        ON CP.LV1_PROD_LIST_CODE = L1.LV1_CODE
       AND CP.LV2_PROD_LIST_CODE = L1.LV2_CODE
       AND CP.LV3_PROD_LIST_CODE = L1.LV3_CODE
       AND L1.LV3_CODE IS NOT NULL -- 先LV3_CODE有值的进行关联
       AND L1.DEL_FLAG = 'N' AND UPPER(L1.STATUS) = 'SUBMIT' 
      LEFT JOIN FIN_DM_OPT_FOP.APD_FOP_ICT_FCST_HOLISTIC_VIEW_T L1B --ICT业务预测全景图,7月版本增加盈利颗粒度L1、L2层级
        ON CP.LV1_PROD_LIST_CODE = L1B.LV1_CODE
       AND CP.LV2_PROD_LIST_CODE = L1B.LV2_CODE
       AND L1B.LV2_CODE IS NOT NULL
       AND L1B.LV3_CODE IS NULL -- LV3_CODE无值时关联到LV2_CODE取值即可
       AND L1B.DEL_FLAG = 'N' AND UPPER(L1B.STATUS) = 'SUBMIT' 
      LEFT JOIN fin_dm_opt_foi.DM_FOC_PROD_L1_CFG_T CFG -- 其他场景:重量级团队和L1_NAME关系的配置表
        ON CP.LV1_PROD_LIST_CN_NAME = CFG.LV1_PROD_RD_TEAM_CN_NAME
       AND CP.LV2_PROD_LIST_CN_NAME = CFG.LV2_PROD_RD_TEAM_CN_NAME
       AND CP.LV3_PROD_LIST_CN_NAME = CFG.LV3_PROD_RD_TEAM_CN_NAME
       AND CFG.LV3_PROD_RD_TEAM_CN_NAME IS NOT NULL -- 先LV3_PROD_RD_TEAM_CN_NAME有值的进行关联
      LEFT JOIN fin_dm_opt_foi.DM_FOC_PROD_L1_CFG_T CFG2 -- 其他场景:重量级团队和L1_NAME关系的配置表
        ON CP.LV1_PROD_LIST_CN_NAME = CFG2.LV1_PROD_RD_TEAM_CN_NAME
       AND CP.LV2_PROD_LIST_CN_NAME = CFG2.LV2_PROD_RD_TEAM_CN_NAME
       AND CFG2.LV2_PROD_RD_TEAM_CN_NAME IS NOT NULL
       AND CFG2.LV3_PROD_RD_TEAM_CN_NAME IS NULL -- LV3_PROD_RD_TEAM_CN_NAME无值时关联到LV2取值即可
      LEFT JOIN FIN_DM_OPT_FOP.APD_FOP_COA_L1_T L2C --COA维表
       ON COALESCE(L1.L1_NAME, L1B.L1_NAME, CFG.L1_NAME, CFG2.L1_NAME) = L2C.L1_NAME
      AND CP.PROD_CODE = L2C.COA_CODE
      AND L2C.DEL_FLAG = 'N' AND UPPER(L2C.STATUS) = 'SUBMIT' 
      
      ) T
      LEFT JOIN (SELECT DISTINCT ITEM_CODE,L1_NAME,L2_NAME FROM FIN_DM_OPT_FOP.DM_DIM_FOP_SPART_PROFITING_RELATION_T --Spart与L1、L2、L3关系表
                  WHERE DEL_FLAG = 'N'
                    AND UPPER(STATUS) = 'SUBMIT'  -- 状态（SAVE 保存、SUBMIT 提交）
                    AND PERIOD_ID = (SELECT MAX(PERIOD_ID) FROM FIN_DM_OPT_FOP.DM_DIM_FOP_SPART_PROFITING_RELATION_T)  --取最大的年月
                ) L2S
       ON T.L1_NAME = L2S.L1_NAME
      AND T.PARENTPARTNUMBER = L2S.ITEM_CODE
      ;
  
  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插数到fin_dm_opt_foi.DM_FOC_BOM_ITEM_SHIP_DTL_T表, 新版本号='||V_VERSION_ID||', 所依赖的品类-专家团的版本号='||V_DIM_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  
  --3.收集统计信息
  ANALYZE fin_dm_opt_foi.DM_FOC_BOM_ITEM_SHIP_DTL_T;
  
  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集fin_dm_opt_foi.DM_FOC_BOM_ITEM_SHIP_DTL_T统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
 
END$$
/

