-- Name: f_dm_foc_reverses_top_cate_info; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_reverses_top_cate_info(f_industry_flag character varying, f_dimension_type character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*

背景描述：1. 计算反向视角统计TOP品类的权重; 2.TOP标识依照已有的视角4 VIEW_FLAG=3 的数据打标识  3.增加数字能源适配 4.增加IAS适配
参数描述:f_dimension_type : 维度类型(通用颗粒度：U,仅通用的有反向视角), x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_CATE_T(通用颗粒度)+FIN_DM_OPT_FOI.DM_FOC_TOP_CATE_INFO_T(通用颗粒度)
目标表:FIN_DM_OPT_FOI.DM_FOC_TOP_CATE_INFO_T(通用颗粒度)
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_REVERSES_TOP_CATE_INFO()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_REVERSES_TOP_CATE_INFO'; --存储过程名称
  V_VERSION_ID BIGINT; --新的版本号, 取自 FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_CATE_T
  V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYY-MM'); --新的版本中文名称
  V_DIM_VERSION_ID BIGINT; --品类-专家团最新的版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_VIEW_NUM VARCHAR(2);
  V_SQL        TEXT;   --SQL逻辑
  V_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_LV4_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_L1_NAME VARCHAR(100);
  V_L2_NAME VARCHAR(100);
  V_IN_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_LV4_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_L1_NAME VARCHAR(200);
  V_IN_L2_NAME VARCHAR(200);
  V_INSERT_LV3_PROD_RND_TEAM_CODE VARCHAR(500);
  V_INSERT_LV4_PROD_RND_TEAM_CODE VARCHAR(500);
  V_INSERT_L1_NAME VARCHAR(500);
  V_INSERT_L2_NAME VARCHAR(500);
  V_FROM_TABLE VARCHAR(50); -- 来源表
  V_TO_TABLE VARCHAR(50); -- 目标表
  V_VERSION_TABLE  VARCHAR(100);
 
  
BEGIN
  X_RESULT_STATUS = '1';
     
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行,颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG);

  --对入参合法性判断
  IF F_DIMENSION_TYPE NOT IN ('U') THEN -- 仅通用颗粒度的需处理反向视角
     DBMS_OUTPUT.PUT_LINE ('入参有误，请重新输入！');
     RETURN 'SUCCESS'; -- 如果不是通用颗粒度直接成功结束
  END IF;
  
  --判断入参
  IF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'I' THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_CATE_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOP_CATE_INFO_T';--目标表

	 
  ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'E' THEN 
	V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_CATE_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOP_CATE_INFO_T';--目标表
 
  ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'IAS' THEN 
	V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_CATE_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOP_CATE_INFO_T';--目标表
	 
  ELSE
    NULL;
  END IF;
  
--判断版本表
  IF F_INDUSTRY_FLAG = 'I' THEN 
 
	
  --查询最新版本号的品类专家团映射关系; 往目标表里插数
SELECT T.VERSION_ID 
    INTO V_DIM_VERSION_ID
    FROM DM_FOC_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'DIMENSION'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1
	 ORDER BY LAST_UPDATE_DATE DESC
     LIMIT 1; 
	 
  --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 

		 
  ELSIF F_INDUSTRY_FLAG = 'E' THEN

  --查询最新版本号的品类专家团映射关系; 往目标表里插数
SELECT T.VERSION_ID 
    INTO V_DIM_VERSION_ID
    FROM DM_FOC_ENERGY_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'DIMENSION'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1
	 ORDER BY LAST_UPDATE_DATE DESC
     LIMIT 1; 
	 
  --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_ENERGY_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
		
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN

  --查询最新版本号的品类专家团映射关系; 往目标表里插数
SELECT T.VERSION_ID 
    INTO V_DIM_VERSION_ID
    FROM DM_FOC_IAS_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'DIMENSION'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1
	 ORDER BY LAST_UPDATE_DATE DESC
     LIMIT 1; 
	 
  --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_IAS_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 		
		
  ELSE 
     NULL ;
	 
  END IF;
    
  --删除当前会计期版本的TOP规格品数据, 支持单月重刷(该反向视角函数仅新增VIEW_FLAG = 6的数据)
  V_SQL := 'DELETE FROM '||V_TO_TABLE||' T WHERE T.VIEW_FLAG = 6 AND T.VERSION_ID = '''||V_VERSION_ID||''''; -- 该反向视角函数仅新增VIEW_FLAG = 6的数据，重跑需清掉VIEW_FLAG = 6的
  EXECUTE IMMEDIATE V_SQL ;
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除TOP品类清单的数据, 删除版本名称'||V_VERSION_ID||',颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  
  
  IF F_INDUSTRY_FLAG IN ('E','I') THEN 
	V_LV4_PROD_RND_TEAM_CODE := '';
    V_LV4_PROD_RD_TEAM_CN_NAME := '';
	V_IN_LV4_PROD_RND_TEAM_CODE := '';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	
	V_VIEW_NUM := '3';
	
	
   ELSIF F_INDUSTRY_FLAG = 'IAS' THEN
	V_LV4_PROD_RND_TEAM_CODE := 'LV4_PROD_RND_TEAM_CODE,';
    V_LV4_PROD_RD_TEAM_CN_NAME := 'LV4_PROD_RD_TEAM_CN_NAME,';
	V_IN_LV4_PROD_RND_TEAM_CODE := 'A.LV4_PROD_RND_TEAM_CODE,';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME := 'A.LV4_PROD_RD_TEAM_CN_NAME,';
	V_INSERT_LV4_PROD_RND_TEAM_CODE := ' AND NVL(A.LV4_PROD_RND_TEAM_CODE, 3) = NVL(B.LV4_PROD_RND_TEAM_CODE, 3)';
	
	V_VIEW_NUM := '7';
	
	ELSE 
	    NULL;
		
	END IF;
  
  --创建临时表, 用于插入权重数据--分地区(全球、国内、海外)分BG去计算TOP品类权重 (9月版本需求新增)
  DROP TABLE IF EXISTS DM_FOC_WEIGHT_CATE_INFO_TEMP;
  CREATE TEMPORARY TABLE DM_FOC_WEIGHT_CATE_INFO_TEMP
  (
    VERSION_ID BIGINT,
    VERSION_NAME VARCHAR2(50),
    VIEW_FLAG VARCHAR2(2),
    PERIOD_YEAR	VARCHAR(50),
    LV0_PROD_RND_TEAM_CODE VARCHAR(50),
    LV0_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV1_PROD_RND_TEAM_CODE VARCHAR(50),
    LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV2_PROD_RND_TEAM_CODE VARCHAR(50),
    LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV3_PROD_RND_TEAM_CODE  VARCHAR(50),
    LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
	LV4_PROD_RND_TEAM_CODE  VARCHAR(50),
    LV4_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    L3_CEG_CODE VARCHAR(50),
    L3_CEG_CN_NAME VARCHAR(200),
    L3_CEG_SHORT_CN_NAME VARCHAR(200),
    L4_CEG_CODE    VARCHAR(50), -- 9月版本需求新增
    L4_CEG_CN_NAME    VARCHAR(200),
    L4_CEG_SHORT_CN_NAME    VARCHAR(200),
    CATEGORY_CODE VARCHAR(50),
    CATEGORY_CN_NAME VARCHAR(200),
    WEIGHT_RATE NUMERIC,
    DOUBLE_FLAG VARCHAR2(2),
    CALIBER_FLAG VARCHAR2(2),
    OVERSEA_FLAG VARCHAR(2),
    LV0_PROD_LIST_CODE VARCHAR(50),
    LV0_PROD_LIST_CN_NAME VARCHAR(200),
    LV0_PROD_LIST_EN_NAME VARCHAR(200)
  )
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN;
  
    
    
  --第一步先往临时表里插数: 分视角分地区(全球、国内、海外)分BG计算前三年和今年YTD,以及去年+今年YTD的TOP品类及权重
  V_SQL :=
  'INSERT INTO DM_FOC_WEIGHT_CATE_INFO_TEMP
    (VERSION_ID,
     VERSION_NAME,
     VIEW_FLAG,
     PERIOD_YEAR,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE,
     LV2_PROD_RD_TEAM_CN_NAME,
     LV3_PROD_RND_TEAM_CODE,
     LV3_PROD_RD_TEAM_CN_NAME,'
	 ||V_LV4_PROD_RND_TEAM_CODE
	 ||V_LV4_PROD_RD_TEAM_CN_NAME||'
     L3_CEG_CODE,
     L3_CEG_CN_NAME,
     L3_CEG_SHORT_CN_NAME,
     L4_CEG_CODE,
     L4_CEG_CN_NAME,
     L4_CEG_SHORT_CN_NAME,
     CATEGORY_CODE,
     CATEGORY_CN_NAME,
     WEIGHT_RATE,
     DOUBLE_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME,
     LV0_PROD_LIST_EN_NAME)
    WITH SUM_COST_TEMP AS
     (
      --分视角, 根据LV0,专家团,品类和年字段,收敛实际发货额
      SELECT  
              T.CALIBER_FLAG,
              T.OVERSEA_FLAG,
              T.LV0_PROD_LIST_CODE,
              T.LV0_PROD_LIST_CN_NAME,
              T.LV0_PROD_LIST_EN_NAME,
              T.VIEW_FLAG,
              T.LV0_PROD_RND_TEAM_CODE,
              T.LV0_PROD_RD_TEAM_CN_NAME,
              T.LV1_PROD_RND_TEAM_CODE,
              T.LV1_PROD_RD_TEAM_CN_NAME,
              T.LV2_PROD_RND_TEAM_CODE,
              T.LV2_PROD_RD_TEAM_CN_NAME,
              T.LV3_PROD_RND_TEAM_CODE,
              T.LV3_PROD_RD_TEAM_CN_NAME,'
			  ||V_LV4_PROD_RND_TEAM_CODE
			  ||V_LV4_PROD_RD_TEAM_CN_NAME||'
              T.L3_CEG_CODE,
              T.L3_CEG_CN_NAME,
              T.L3_CEG_SHORT_CN_NAME,
              T.L4_CEG_CODE,
              T.L4_CEG_CN_NAME,
              T.L4_CEG_SHORT_CN_NAME,
              T.CATEGORY_CODE,
              T.CATEGORY_CN_NAME,
              TO_CHAR(T.PERIOD_YEAR) AS PERIOD_YEAR,
              SUM(T.RMB_COST_AMT) AS COST_AMT,
              DECODE(T.PERIOD_YEAR,
                     YEAR(CURRENT_TIMESTAMP),
                     0,
                     YEAR(CURRENT_TIMESTAMP) - 1,
                     1,
                     NULL) AS YEAR_FLAG
        FROM '||V_FROM_TABLE ||' T
       WHERE  T.VIEW_FLAG = '''||V_VIEW_NUM||''' -- 用视角4的数据去加工视角7的数据
       GROUP BY  T.CALIBER_FLAG,
                 T.OVERSEA_FLAG,
                 T.LV0_PROD_LIST_CODE,
                 T.LV0_PROD_LIST_CN_NAME,
                 T.LV0_PROD_LIST_EN_NAME,
                 T.VIEW_FLAG,
                 T.LV0_PROD_RND_TEAM_CODE,
                 T.LV0_PROD_RD_TEAM_CN_NAME,
                 T.LV1_PROD_RND_TEAM_CODE,
                 T.LV1_PROD_RD_TEAM_CN_NAME,
                 T.LV2_PROD_RND_TEAM_CODE,
                 T.LV2_PROD_RD_TEAM_CN_NAME,
                 T.LV3_PROD_RND_TEAM_CODE,
                 T.LV3_PROD_RD_TEAM_CN_NAME,'
				||V_LV4_PROD_RND_TEAM_CODE
				||V_LV4_PROD_RD_TEAM_CN_NAME||'
                 T.L3_CEG_CODE,
                 T.L3_CEG_CN_NAME,
                 T.L3_CEG_SHORT_CN_NAME,
                 T.L4_CEG_CODE,
                 T.L4_CEG_CN_NAME,
                 T.L4_CEG_SHORT_CN_NAME,
                 T.CATEGORY_CODE,
                 T.CATEGORY_CN_NAME,
                 T.PERIOD_YEAR),
    
    YEAR_AMT_TEMP AS
     ( 
      --分视角开窗求出年的分组总发货额
      SELECT  A.CALIBER_FLAG,
              A.OVERSEA_FLAG,
              A.LV0_PROD_LIST_CODE,
              A.LV0_PROD_LIST_CN_NAME,
              A.LV0_PROD_LIST_EN_NAME,
              A.VIEW_FLAG,
              A.LV0_PROD_RND_TEAM_CODE,
              A.LV0_PROD_RD_TEAM_CN_NAME,
              A.LV1_PROD_RND_TEAM_CODE,
              A.LV1_PROD_RD_TEAM_CN_NAME,
              A.LV2_PROD_RND_TEAM_CODE,
              A.LV2_PROD_RD_TEAM_CN_NAME,
              A.LV3_PROD_RND_TEAM_CODE,
              A.LV3_PROD_RD_TEAM_CN_NAME,'
			  ||V_LV4_PROD_RND_TEAM_CODE
			  ||V_LV4_PROD_RD_TEAM_CN_NAME||'
              A.L3_CEG_CODE,
              A.L3_CEG_CN_NAME,
              A.L3_CEG_SHORT_CN_NAME,
              A.L4_CEG_CODE,
              A.L4_CEG_CN_NAME,
              A.L4_CEG_SHORT_CN_NAME,
              A.CATEGORY_CODE,
              A.CATEGORY_CN_NAME,
              DECODE(A.PERIOD_YEAR,
                     YEAR(CURRENT_TIMESTAMP),
                     A.PERIOD_YEAR || ''YTD'',
                     A.PERIOD_YEAR) AS PERIOD_YEAR,
              A.COST_AMT,
              SUM(A.COST_AMT) OVER(PARTITION BY A.CALIBER_FLAG, A.OVERSEA_FLAG, A.LV0_PROD_LIST_CODE, A.VIEW_FLAG, A.L3_CEG_CODE,A. L4_CEG_CODE, A.LV0_PROD_RND_TEAM_CODE, A.LV1_PROD_RND_TEAM_CODE, A.LV2_PROD_RND_TEAM_CODE, A.LV3_PROD_RND_TEAM_CODE,'||V_LV4_PROD_RND_TEAM_CODE||' A.PERIOD_YEAR) AS YEAR_AMT
        FROM SUM_COST_TEMP A),
    
    TWO_YEAR_TEMP AS
     (
      --分视角收敛上一年到今年YTD的总金额
      SELECT  A.CALIBER_FLAG,
              A.OVERSEA_FLAG,
              A.LV0_PROD_LIST_CODE,
              A.LV0_PROD_LIST_CN_NAME,
              A.LV0_PROD_LIST_EN_NAME,
              A.VIEW_FLAG,
              A.LV0_PROD_RND_TEAM_CODE,
              A.LV0_PROD_RD_TEAM_CN_NAME,
              A.LV1_PROD_RND_TEAM_CODE,
              A.LV1_PROD_RD_TEAM_CN_NAME,
              A.LV2_PROD_RND_TEAM_CODE,
              A.LV2_PROD_RD_TEAM_CN_NAME,
              A.LV3_PROD_RND_TEAM_CODE,
              A.LV3_PROD_RD_TEAM_CN_NAME,'
			  ||V_LV4_PROD_RND_TEAM_CODE
			  ||V_LV4_PROD_RD_TEAM_CN_NAME||'
              A.L3_CEG_CODE,
              A.L3_CEG_CN_NAME,
              A.L3_CEG_SHORT_CN_NAME,
              A.L4_CEG_CODE,
              A.L4_CEG_CN_NAME,
              A.L4_CEG_SHORT_CN_NAME,
              A.CATEGORY_CODE,
              A.CATEGORY_CN_NAME,
              SUM(A.COST_AMT) AS YEAR_AMT,
              SUM(SUM(A.COST_AMT)) OVER(PARTITION BY A.CALIBER_FLAG, A.OVERSEA_FLAG, A.LV0_PROD_LIST_CODE, A.VIEW_FLAG, A.L3_CEG_CODE,A. L4_CEG_CODE, A.LV0_PROD_RND_TEAM_CODE, A.LV1_PROD_RND_TEAM_CODE, A.LV2_PROD_RND_TEAM_CODE,'||V_LV4_PROD_RND_TEAM_CODE||' A.LV3_PROD_RND_TEAM_CODE ) AS TOTAL_YEAR_AMT
        FROM SUM_COST_TEMP A
       WHERE A.YEAR_FLAG IN (0, 1)
       GROUP BY  A.OVERSEA_FLAG,
                 A.CALIBER_FLAG,
                 A.LV0_PROD_LIST_CODE,
                 A.LV0_PROD_LIST_CN_NAME,
                 A.LV0_PROD_LIST_EN_NAME,
                 A.VIEW_FLAG,
                 A.LV0_PROD_RND_TEAM_CODE,
                 A.LV0_PROD_RD_TEAM_CN_NAME,
                 A.LV1_PROD_RND_TEAM_CODE,
                 A.LV1_PROD_RD_TEAM_CN_NAME,
                 A.LV2_PROD_RND_TEAM_CODE,
                 A.LV2_PROD_RD_TEAM_CN_NAME,
                 A.LV3_PROD_RND_TEAM_CODE,
                 A.LV3_PROD_RD_TEAM_CN_NAME,
				 '||V_LV4_PROD_RND_TEAM_CODE||'
				 '||V_LV4_PROD_RD_TEAM_CN_NAME||'
                 A.L3_CEG_CODE,
                 A.L3_CEG_CN_NAME,
                 A.L3_CEG_SHORT_CN_NAME,
                 A.L4_CEG_CODE,
                 A.L4_CEG_CN_NAME,
                 A.L4_CEG_SHORT_CN_NAME,
                 A.CATEGORY_CODE,
                 A.CATEGORY_CN_NAME)
    
    --区间年的权重
   SELECT '|| V_VERSION_ID ||' AS VERSION_ID,
          '''|| V_VERSION_NAME ||''' AS VERSION_NAME,
           P.VIEW_FLAG,
           YEAR(CURRENT_TIMESTAMP) - 1 || ''+'' || YEAR(CURRENT_TIMESTAMP) ||
           ''YTD'' AS PERIOD_YEAR,
           P.LV0_PROD_RND_TEAM_CODE,
           P.LV0_PROD_RD_TEAM_CN_NAME,
           P.LV1_PROD_RND_TEAM_CODE,
           P.LV1_PROD_RD_TEAM_CN_NAME,
           P.LV2_PROD_RND_TEAM_CODE,
           P.LV2_PROD_RD_TEAM_CN_NAME,
           P.LV3_PROD_RND_TEAM_CODE,
           P.LV3_PROD_RD_TEAM_CN_NAME,'
		   ||V_LV4_PROD_RND_TEAM_CODE
		   ||V_LV4_PROD_RD_TEAM_CN_NAME||'		   
           P.L3_CEG_CODE,
           P.L3_CEG_CN_NAME,
           P.L3_CEG_SHORT_CN_NAME,
           P.L4_CEG_CODE,
           P.L4_CEG_CN_NAME,
           P.L4_CEG_SHORT_CN_NAME,
           P.CATEGORY_CODE,
           P.CATEGORY_CN_NAME,
           P.YEAR_AMT / NULLIF(P.TOTAL_YEAR_AMT,0) AS WEIGHT_RATE,
           ''Y'' AS DOUBLE_FLAG,
           P.CALIBER_FLAG,
           P.OVERSEA_FLAG,
           P.LV0_PROD_LIST_CODE,
           P.LV0_PROD_LIST_CN_NAME,
           P.LV0_PROD_LIST_EN_NAME
      FROM TWO_YEAR_TEMP P
    UNION ALL
    --单个会计年的权重
   SELECT '|| V_VERSION_ID ||' AS VERSION_ID,
          '''|| V_VERSION_NAME ||''' AS VERSION_NAME,
           Q.VIEW_FLAG,
           TO_CHAR(Q.PERIOD_YEAR) AS PERIOD_YEAR,
           Q.LV0_PROD_RND_TEAM_CODE,
           Q.LV0_PROD_RD_TEAM_CN_NAME,
           Q.LV1_PROD_RND_TEAM_CODE,
           Q.LV1_PROD_RD_TEAM_CN_NAME,
           Q.LV2_PROD_RND_TEAM_CODE,
           Q.LV2_PROD_RD_TEAM_CN_NAME,
           Q.LV3_PROD_RND_TEAM_CODE,
           Q.LV3_PROD_RD_TEAM_CN_NAME,'
		   ||V_LV4_PROD_RND_TEAM_CODE
		   ||V_LV4_PROD_RD_TEAM_CN_NAME||'
           Q.L3_CEG_CODE,
           Q.L3_CEG_CN_NAME,
           Q.L3_CEG_SHORT_CN_NAME,
           Q.L4_CEG_CODE,
           Q.L4_CEG_CN_NAME,
           Q.L4_CEG_SHORT_CN_NAME,
           Q.CATEGORY_CODE,
           Q.CATEGORY_CN_NAME,
           Q.COST_AMT / NULLIF(Q.YEAR_AMT,0) AS WEIGHT_RATE,
           ''N'' AS DOUBLE_FLAG,
           Q.CALIBER_FLAG,
           Q.OVERSEA_FLAG,
           Q.LV0_PROD_LIST_CODE,
           Q.LV0_PROD_LIST_CN_NAME,
           Q.LV0_PROD_LIST_EN_NAME
      FROM YEAR_AMT_TEMP Q';
	  
	DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '创建临时表, 并往临时表里插数: 分视角分地区(全球、国内、海外)分BG各年TOP品类及权重, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME||',颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  

  
----------------------------------------------------------------------以上第一步先处理按照分地区(全球、国内、海外)分BG的权重数据-------------

----------------------------------------------------------------------以下第二步TOP标签数据延用视角4的------------------------



--往TOP品类清单插数, 打上前95%TOP的标识
  V_SQL :=
  'INSERT INTO '||V_TO_TABLE||'
    (VERSION_ID,
     VERSION_NAME,
     PERIOD_YEAR,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE,
     LV2_PROD_RD_TEAM_CN_NAME,
     LV3_PROD_RND_TEAM_CODE,
     LV3_PROD_RD_TEAM_CN_NAME,'
	 ||V_LV4_PROD_RND_TEAM_CODE
	 ||V_LV4_PROD_RD_TEAM_CN_NAME||'
     TOP_L3_CEG_CODE,
     TOP_L3_CEG_CN_NAME,
     TOP_L3_CEG_SHORT_CN_NAME,
     TOP_L4_CEG_CODE,
     TOP_L4_CEG_CN_NAME,
     TOP_L4_CEG_SHORT_CN_NAME,
     TOP_CATEGORY_CODE,
     TOP_CATEGORY_CN_NAME,
     WEIGHT_RATE,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     VIEW_FLAG,
     IS_TOP_FLAG,
     DOUBLE_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME,
     LV0_PROD_LIST_EN_NAME
     )
    --打上是否为TOP类标识
    SELECT A.VERSION_ID,
           A.VERSION_NAME,
           A.PERIOD_YEAR,
           A.LV0_PROD_RND_TEAM_CODE,
           A.LV0_PROD_RD_TEAM_CN_NAME,
           A.LV1_PROD_RND_TEAM_CODE,
           A.LV1_PROD_RD_TEAM_CN_NAME,
           A.LV2_PROD_RND_TEAM_CODE,
           A.LV2_PROD_RD_TEAM_CN_NAME,
           A.LV3_PROD_RND_TEAM_CODE,
           A.LV3_PROD_RD_TEAM_CN_NAME,'
		  ||V_IN_LV4_PROD_RND_TEAM_CODE
		  ||V_IN_LV4_PROD_RD_TEAM_CN_NAME||'
           A.L3_CEG_CODE,
           A.L3_CEG_CN_NAME,
           A.L3_CEG_SHORT_CN_NAME,
           A.L4_CEG_CODE,
           A.L4_CEG_CN_NAME,
           A.L4_CEG_SHORT_CN_NAME,
           A.CATEGORY_CODE,
           A.CATEGORY_CN_NAME,
           A.WEIGHT_RATE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           6 AS VIEW_FLAG, -- 生成新的视角7数据
           DECODE(B.IS_TOP_FLAG, ''N'', ''N'', ''Y'') AS IS_TOP_FLAG, --延用视角4的数据打top标识
           A.DOUBLE_FLAG,
           A.CALIBER_FLAG,
           A.OVERSEA_FLAG,
           A.LV0_PROD_LIST_CODE,
           A.LV0_PROD_LIST_CN_NAME,
           A.LV0_PROD_LIST_EN_NAME
      FROM DM_FOC_WEIGHT_CATE_INFO_TEMP A
      LEFT JOIN '||V_TO_TABLE||' B
        ON A.VIEW_FLAG = B.VIEW_FLAG
       AND B.VIEW_FLAG = '''||V_VIEW_NUM||''' -- 延用视角4的数据打top标识
       AND A.PERIOD_YEAR = B.PERIOD_YEAR
	   AND A.VERSION_ID = B.VERSION_ID
	   AND B.VERSION_ID = '||V_VERSION_ID||'
       AND A.LV0_PROD_RND_TEAM_CODE = B.LV0_PROD_RND_TEAM_CODE
       AND NVL(A.LV1_PROD_RND_TEAM_CODE, 1) = NVL(B.LV1_PROD_RND_TEAM_CODE, 1)
       AND NVL(A.LV2_PROD_RND_TEAM_CODE, 2) = NVL(B.LV2_PROD_RND_TEAM_CODE, 2)
       AND NVL(A.LV3_PROD_RND_TEAM_CODE, 3) = NVL(B.LV3_PROD_RND_TEAM_CODE, 3)'
	   ||V_INSERT_LV4_PROD_RND_TEAM_CODE||'
       AND A.DOUBLE_FLAG = B.DOUBLE_FLAG
       AND A.L3_CEG_CODE = B.TOP_L3_CEG_CODE
       AND A.L4_CEG_CODE = B.TOP_L4_CEG_CODE
       AND A.CATEGORY_CODE = B.TOP_CATEGORY_CODE
       AND A.CALIBER_FLAG = B.CALIBER_FLAG
       AND A.OVERSEA_FLAG = B.OVERSEA_FLAG
       AND A.LV0_PROD_LIST_CODE = B.LV0_PROD_LIST_CODE';
	   
	DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '往TOP品类表里插入反向视角7的数据, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME||',颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');


  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成,颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG);
 
  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败,颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG, 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END$$
/

