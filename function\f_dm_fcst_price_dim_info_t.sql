-- Name: f_dm_fcst_price_dim_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_dim_info_t(f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：20241112
创建人  ：qwx1110218
背景描述：定价指数-下拉框维表，从月累计均价收敛表（dm_fcst_price_dim_info_t）取数，只取有效数据；只保留1个版本；
参数描述：参数一： f_version_id 版本号
          参数五： x_result_status 运行状态返回值 'SUCCESS'为成功，'FAILED'为失败
事例    ：select fin_dm_opt_foi.f_dm_fcst_price_dim_info_t()
变更记录：

*/


declare
	v_sp_name     varchar(200) := 'fin_dm_opt_foi.f_dm_fcst_price_dim_info_t';
	v_to_table    varchar(100) := 'fin_dm_opt_foi.dm_fcst_price_dim_info_t';  -- 目标表
	v_version_id  bigint;        -- 版本号
	v_sql         text;
	v_step_num    int;
	v_cn          int;


begin

	x_result_status := 'SUCCESS';        --1表示成功
	v_step_num = 1;

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
      f_sp_name => v_sp_name,    --sp名称
      f_step_num => v_step_num,
      f_cal_log_desc => '函数 '||v_sp_name||' 开始运行',--日志描述
      f_formula_sql_txt  => v_sql,
      f_dml_row_count => sql%rowcount,
      f_result_status => x_result_status,
      f_errbuf => 'SUCCESS'
  );


  -- 从版本表取最新版本
  if(f_version_id is null) then
    select version_id into v_version_id
      from fin_dm_opt_foi.dm_fcst_price_version_info_t
     where del_flag = 'N'
       and status = 1
       and upper(data_type) = 'ANNUAL'  -- 用年度版本号
     order by last_update_date desc
     limit 1
    ;
  else
    v_version_id := f_version_id;
  end if;

  perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
      f_sp_name => v_sp_name,    --sp名称
      f_step_num => v_step_num,
      f_cal_log_desc => '获取最新版本： '||v_version_id,--日志描述
      f_formula_sql_txt  => v_sql,
      f_dml_row_count => sql%rowcount,
      f_result_status => x_result_status,
      f_errbuf => 'SUCCESS'
  );

  -- 清理数据
  truncate table fin_dm_opt_foi.dm_fcst_price_dim_info_t ;
  
  v_step_num := v_step_num+1;
  perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
      f_sp_name => v_sp_name,    --sp名称
      f_step_num => v_step_num,
      f_cal_log_desc => '清理对应版本的数据，版本号：'||v_version_id||'，数据量：'||sql%rowcount,--日志描述
      f_formula_sql_txt  => v_sql,
      f_dml_row_count => sql%rowcount,
      f_result_status => x_result_status,
      f_errbuf => 'SUCCESS'
  );
  
  -- 数据入到目标表
  insert into fin_dm_opt_foi.dm_fcst_price_dim_info_t(
         version_id                        -- 版本号
       , group_level                       -- 层级
       , bg_code                           -- BG编码
       , bg_cn_name                        -- BG中文名称
       , lv0_prod_list_code                -- 产品LV0编码
       , lv1_prod_list_code                -- 产品LV1编码
       , lv2_prod_list_code                -- 产品LV2编码
       , lv3_prod_list_code                -- 产品LV3编码
       , lv4_prod_list_code                -- 产品LV4编码
       , lv0_prod_list_cn_name             -- 产品LV0中文名称
       , lv1_prod_list_cn_name             -- 产品LV1中文名称
       , lv2_prod_list_cn_name             -- 产品LV2中文名称
       , lv3_prod_list_cn_name             -- 产品LV3中文名称
       , lv4_prod_list_cn_name             -- 产品LV4中文名称
       , spart_code                        -- SPART编码
       , spart_cn_name                     -- SPART中文名称
       , oversea_flag                      -- 国内海外标识
       , region_code                       -- 地区部编码
       , region_cn_name                    -- 地区部名称
       , repoffice_code                    -- 代表处编码
       , repoffice_cn_name                 -- 代表处名称
       , sign_top_cust_category_code       -- 签约客户_大T系统部编码
       , sign_top_cust_category_cn_name    -- 签约客户_大T系统部名称
       , sign_subsidiary_custcatg_cn_name  -- 签约客户_子网系统部名称
       , view_flag                         -- 视角标识，用于区分不同视角下的数据()
       , created_by                        -- 创建人
       , creation_date                     -- 创建时间
       , last_updated_by                   -- 修改人
       , last_update_date                  -- 修改时间
       , del_flag                          -- 删除标识(未删除：N，已删除：Y)
  )
  with price_annl_avg_tmp as(
  select version_id
       , bg_code
       , bg_cn_name
       , lv0_prod_list_code
       , lv1_prod_list_code
       , lv2_prod_list_code
       , lv3_prod_list_code
       , lv4_prod_list_code
       , lv0_prod_list_cn_name
       , lv1_prod_list_cn_name
       , lv2_prod_list_cn_name
       , lv3_prod_list_cn_name
       , lv4_prod_list_cn_name
       , spart_code
       , spart_cn_name
       , oversea_flag
       , region_code
       , region_cn_name
       , repoffice_code
       , repoffice_cn_name
       , sign_top_cust_category_code
       , sign_top_cust_category_cn_name
       , sign_subsidiary_custcatg_cn_name
       , view_flag
    from fin_dm_opt_foi.dm_fcst_price_annl_avg_t t1  -- 年度均本补齐表
   where t1.del_flag = 'N'
     and t1.version_id = v_version_id
     and upper(enable_flag) = 'Y'  -- 只取有效数据
  )
  -- 各层级
  select version_id
       , 'SPART' as group_level
       , bg_code
       , bg_cn_name
       , lv0_prod_list_code
       , lv1_prod_list_code
       , lv2_prod_list_code
       , lv3_prod_list_code
       , lv4_prod_list_code
       , lv0_prod_list_cn_name
       , lv1_prod_list_cn_name
       , lv2_prod_list_cn_name
       , lv3_prod_list_cn_name
       , lv4_prod_list_cn_name
       , spart_code
       , spart_cn_name
       , oversea_flag
       , region_code
       , region_cn_name
       , repoffice_code
       , repoffice_cn_name
       , sign_top_cust_category_code
       , sign_top_cust_category_cn_name
       , sign_subsidiary_custcatg_cn_name
       , view_flag
       , -1 as created_by
			 , current_timestamp as creation_date
			 , -1 as last_updated_by
			 , current_timestamp as last_update_date
			 , 'N' as del_flag
    from price_annl_avg_tmp
   union all
  select distinct version_id
       , 'LV4' as group_level
       , bg_code
       , bg_cn_name
       , lv0_prod_list_code
       , lv1_prod_list_code
       , lv2_prod_list_code
       , lv3_prod_list_code
       , lv4_prod_list_code
       , lv0_prod_list_cn_name
       , lv1_prod_list_cn_name
       , lv2_prod_list_cn_name
       , lv3_prod_list_cn_name
       , lv4_prod_list_cn_name
       , '' as spart_code
       , '' as spart_cn_name
       , oversea_flag
       , region_code
       , region_cn_name
       , repoffice_code
       , repoffice_cn_name
       , sign_top_cust_category_code
       , sign_top_cust_category_cn_name
       , sign_subsidiary_custcatg_cn_name
       , view_flag
       , -1 as created_by
			 , current_timestamp as creation_date
			 , -1 as last_updated_by
			 , current_timestamp as last_update_date
			 , 'N' as del_flag
    from price_annl_avg_tmp
   union all
  select distinct version_id
       , 'LV3' as group_level
       , bg_code
       , bg_cn_name
       , lv0_prod_list_code
       , lv1_prod_list_code
       , lv2_prod_list_code
       , lv3_prod_list_code
       , '' as lv4_prod_list_code
       , lv0_prod_list_cn_name
       , lv1_prod_list_cn_name
       , lv2_prod_list_cn_name
       , lv3_prod_list_cn_name
       , '' as lv4_prod_list_cn_name
       , '' as spart_code
       , '' as spart_cn_name
       , oversea_flag
       , region_code
       , region_cn_name
       , repoffice_code
       , repoffice_cn_name
       , sign_top_cust_category_code
       , sign_top_cust_category_cn_name
       , sign_subsidiary_custcatg_cn_name
       , view_flag
       , -1 as created_by
			 , current_timestamp as creation_date
			 , -1 as last_updated_by
			 , current_timestamp as last_update_date
			 , 'N' as del_flag
    from price_annl_avg_tmp
   union all
  select distinct version_id
       , 'LV2' as group_level
       , bg_code
       , bg_cn_name
       , lv0_prod_list_code
       , lv1_prod_list_code
       , lv2_prod_list_code
       , '' as lv3_prod_list_code
       , '' as lv4_prod_list_code
       , lv0_prod_list_cn_name
       , lv1_prod_list_cn_name
       , lv2_prod_list_cn_name
       , '' as lv3_prod_list_cn_name
       , '' as lv4_prod_list_cn_name
       , '' as spart_code
       , '' as spart_cn_name
       , oversea_flag
       , region_code
       , region_cn_name
       , repoffice_code
       , repoffice_cn_name
       , sign_top_cust_category_code
       , sign_top_cust_category_cn_name
       , sign_subsidiary_custcatg_cn_name
       , view_flag
       , -1 as created_by
			 , current_timestamp as creation_date
			 , -1 as last_updated_by
			 , current_timestamp as last_update_date
			 , 'N' as del_flag
    from price_annl_avg_tmp
   union all
  select distinct version_id
       , 'LV1' as group_level
       , bg_code
       , bg_cn_name
       , lv0_prod_list_code
       , lv1_prod_list_code
       , '' as lv2_prod_list_code
       , '' as lv3_prod_list_code
       , '' as lv4_prod_list_code
       , lv0_prod_list_cn_name
       , lv1_prod_list_cn_name
       , '' as lv2_prod_list_cn_name
       , '' as lv3_prod_list_cn_name
       , '' as lv4_prod_list_cn_name
       , '' as spart_code
       , '' as spart_cn_name
       , oversea_flag
       , region_code
       , region_cn_name
       , repoffice_code
       , repoffice_cn_name
       , sign_top_cust_category_code
       , sign_top_cust_category_cn_name
       , sign_subsidiary_custcatg_cn_name
       , view_flag
       , -1 as created_by
			 , current_timestamp as creation_date
			 , -1 as last_updated_by
			 , current_timestamp as last_update_date
			 , 'N' as del_flag
    from price_annl_avg_tmp
   union all
  select distinct version_id
       , 'LV0' as group_level
       , bg_code
       , bg_cn_name
       , lv0_prod_list_code
       , '' as lv1_prod_list_code
       , '' as lv2_prod_list_code
       , '' as lv3_prod_list_code
       , '' as lv4_prod_list_code
       , lv0_prod_list_cn_name
       , '' as lv1_prod_list_cn_name
       , '' as lv2_prod_list_cn_name
       , '' as lv3_prod_list_cn_name
       , '' as lv4_prod_list_cn_name
       , '' as spart_code
       , '' as spart_cn_name
       , oversea_flag
       , region_code
       , region_cn_name
       , repoffice_code
       , repoffice_cn_name
       , sign_top_cust_category_code
       , sign_top_cust_category_cn_name
       , sign_subsidiary_custcatg_cn_name
       , view_flag
       , -1 as created_by
			 , current_timestamp as creation_date
			 , -1 as last_updated_by
			 , current_timestamp as last_update_date
			 , 'N' as del_flag
    from price_annl_avg_tmp
  ;

  v_step_num := v_step_num+1;
  perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
      f_sp_name => v_sp_name,    --sp名称
      f_step_num => v_step_num,
      f_cal_log_desc => '数据入到目标表，数据量：'||sql%rowcount,--日志描述
      f_formula_sql_txt  => v_sql,
      f_dml_row_count => sql%rowcount,
      f_result_status => x_result_status,
      f_errbuf => 'SUCCESS'
  );


  --收集统计信息
	v_sql := 'analyse '||v_to_table;
	execute v_sql;
  
  v_step_num := v_step_num+1;
  perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
      f_sp_name => v_sp_name,    --sp名称
      f_step_num => v_step_num,
      f_cal_log_desc => '目标表统计信息收集完成，运行结束！',--日志描述
      f_formula_sql_txt  => v_sql,
      f_dml_row_count => sql%rowcount,
      f_result_status => x_result_status,
      f_errbuf => 'SUCCESS'
  );

  exception
  	when others then

      perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
         f_sp_name => v_sp_name,    -- sp名称
         f_step_num => v_step_num,
         f_cal_log_desc => v_sp_name||'：运行错误',-- 日志描述
         f_formula_sql_txt  => v_sql,
         f_dml_row_count => sql%rowcount,
         f_result_status => '0',
         f_errbuf => sqlstate  -- 错误编码
      ) ;

      x_result_status := 'FAILED';

end
$$
/

