-- Name: f_dm_foi_his_actual_bak; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_his_actual_bak(OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
DECLARE
  V_SP_NAME VARCHAR2(500):= 'FIN_DM_OPT_FOI.F_DM_FOI_HIS_ACTUAL';
  V_BEGIN_DATE TIMESTAMP := TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)-3)||'01','YYYYMM'); 
  
BEGIN
  X_RESULT_STATUS:= 1;
  
  --0.日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
  
  --1.清空实际数补齐表的数据
  EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_APPEND_T';
  
  --1.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 1,
   F_CAL_LOG_DESC => '清空FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_APPEND_T实际数补齐表的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
	--2.插入新补齐的均价实际数
  INSERT INTO FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_APPEND_T
    (YEAR,
     PERIOD_ID,
     ITEM_ID,
     ITEM_CODE,
     ITEM_NAME,
     AVG_PRICE_CNY,
     CATEGORY_CODE,
     CATEGORY_NAME,
     CATEGORY_EN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     APPEND_FLAG)
    WITH CATEGORY_ITEM_TEMP AS
     (
      --历史表里出现的品类,item, 取数范围: (三年前第1月)至当前系统月(不含)  
      SELECT DISTINCT T.ITEM_CODE,
                       T.ITEM_ID,
                       T.ITEM_NAME,
                       T.CATEGORY_CODE,
                       T.CATEGORY_NAME,
                       T.CATEGORY_EN_NAME
        FROM FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_REPORT_T T
       WHERE T.YEAR >= YEAR(CURRENT_TIMESTAMP) - 3
         AND T.PERIOD_ID < TO_NUMBER(TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMM'))),
    
    PERIOD_DIM_TEMP AS
     (
      --生成连续月份, 201901至当前系统实际月, (当前系统实际月 = 当前系统月-1)
      SELECT TO_CHAR(ADD_MONTHS(V_BEGIN_DATE, NUM.VAL - 1),
                      'YYYYMM') AS PERIOD_ID
        FROM GENERATE_SERIES(1,
                              TO_NUMBER(TIMESTAMPDIFF(MONTH,
                                                      V_BEGIN_DATE,
                                                      CURRENT_TIMESTAMP)),
                              1) NUM(VAL)),
    
    CROSS_JOIN_TEMP AS
     (
      --生成连续年月的品类,ITEM维
      SELECT A.CATEGORY_CODE,
              A.ITEM_CODE,
              SUBSTR(B.PERIOD_ID, 1, 4) AS YEAR,
              B.PERIOD_ID
        FROM CATEGORY_ITEM_TEMP A, PERIOD_DIM_TEMP B),
    
    ITEM_SUM_TEMP AS
     (
      --按品类, ITEM, 会计期, 计算均价=汇总订单量和订单金额
      SELECT TT.CATEGORY_CODE,
              TT.ITEM_CODE,
              TT.PERIOD_ID,
              CASE
                WHEN TT.TOTAL_QTY = 0 THEN
                 0
                ELSE
                 TT.TOTAL_AMT / TT.TOTAL_QTY
              END AS AVG_AMT,
              'N' AS APD_FLAG
        FROM (SELECT T.CATEGORY_CODE,
                      T.ITEM_CODE,
                      T.PERIOD_ID,
                      NVL(SUM(T.RECEIVE_QTY), 0) AS TOTAL_QTY,
                      NVL(SUM(T.RECEIVE_AMT_CNY), 0) AS TOTAL_AMT
                 FROM FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_REPORT_T T
                GROUP BY T.CATEGORY_CODE, T.ITEM_CODE, T.PERIOD_ID) TT),
    
    FORWARD_FILLER_TEMP AS
     (
      --按照品类,ITEM组, 向前寻找会计期补齐均价
      SELECT SS.CATEGORY_CODE,
              SS.ITEM_CODE,
              SS.YEAR,
              SS.PERIOD_ID,
              SS.AVG_AMT,
              FIRST_VALUE(SS.AVG_AMT) OVER(PARTITION BY SS.CATEGORY_CODE, SS.ITEM_CODE, SS.AVG_AMT_FLAG ORDER BY SS.PERIOD_ID) AS AVG_AMT_2, --新补齐的均价字段
              SS.AVG_AMT_FLAG,
              SS.APD_FLAG
        FROM (SELECT S.CATEGORY_CODE,
                      S.ITEM_CODE,
                      S.YEAR,
                      S.PERIOD_ID,
                      S.AVG_AMT,
                      SUM(S.NULL_FLAG) OVER(PARTITION BY S.CATEGORY_CODE, S.ITEM_CODE ORDER BY S.PERIOD_ID) AS AVG_AMT_FLAG, --均价标识: 为空不参与累计加1
                      S.APD_FLAG
                 FROM (SELECT T1.CATEGORY_CODE,
                              T1.ITEM_CODE,
                              T1.YEAR,
                              T1.PERIOD_ID,
                              T2.AVG_AMT,
                              DECODE(T2.AVG_AMT, NULL, 0, 1) AS NULL_FLAG, --空标识, 用于sum开窗累计
                              NVL(T2.APD_FLAG, 'Y') AS APD_FLAG --补齐标识：Y为补齐，N为原始存在
                         FROM CROSS_JOIN_TEMP T1
                         LEFT JOIN ITEM_SUM_TEMP T2
                           ON T1.CATEGORY_CODE = T2.CATEGORY_CODE
                          AND T1.ITEM_CODE = T2.ITEM_CODE
                          AND T1.PERIOD_ID = T2.PERIOD_ID) S) SS)
    
    --向后补齐均价
    SELECT S.YEAR,
           S.PERIOD_ID,
           S.ITEM_ID,
           S.ITEM_CODE,
           S.ITEM_NAME,
           NVL(S.AVG_AMT_2, S.AVG_AMT_3) AS RECEIVE_AMT_CNY,
           S.CATEGORY_CODE,
           S.CATEGORY_NAME,
           S.CATEGORY_EN_NAME,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG,
           S.APD_FLAG AS APPEND_FLAG
      FROM (SELECT T1.CATEGORY_CODE,
                   T3.CATEGORY_NAME,
                   T3.CATEGORY_EN_NAME,
                   T1.ITEM_CODE,
                   T3.ITEM_ID,
                   T3.ITEM_NAME,
                   T1.YEAR,
                   T1.PERIOD_ID,
                   T1.AVG_AMT_2,
                   T2.AVG_AMT_3,
                   T1.APD_FLAG
              FROM FORWARD_FILLER_TEMP T1
              LEFT JOIN (SELECT DISTINCT S.CATEGORY_CODE,
                                        S.ITEM_CODE,
                                        FIRST_VALUE(S.PERIOD_ID) OVER(PARTITION BY S.CATEGORY_CODE, S.ITEM_CODE ORDER BY S.PERIOD_ID ASC) AS PERIOD_ID, --有均价的首条会计期
                                        FIRST_VALUE(S.AVG_AMT_2) OVER(PARTITION BY S.CATEGORY_CODE, S.ITEM_CODE ORDER BY S.PERIOD_ID ASC) AS AVG_AMT_3 --有均价的首条补齐均价
                          FROM FORWARD_FILLER_TEMP S
                         WHERE S.AVG_AMT_FLAG > 0) T2 
                ON T1.CATEGORY_CODE = T2.CATEGORY_CODE
               AND T1.ITEM_CODE = T2.ITEM_CODE
               AND T1.PERIOD_ID < T2.PERIOD_ID 
              LEFT JOIN CATEGORY_ITEM_TEMP T3 --关联带出item, 品类的名称信息
                ON T1.CATEGORY_CODE = T3.CATEGORY_CODE
               AND T1.ITEM_CODE = T3.ITEM_CODE) S;
    
     
     
  --2.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 2,
   F_CAL_LOG_DESC => '插入新补齐的均价实际数到FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_APPEND_T表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --3.收集统计信息
  ANALYZE FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_APPEND_T;
  
  --3.日志结束
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 3,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_APPEND_T统计信息完成!');
   
  
 return 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := 0;
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END$$
/

