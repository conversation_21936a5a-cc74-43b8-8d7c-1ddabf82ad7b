-- Name: f_dm_fcst_ict_raw_data_examine; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_ict_raw_data_examine(f_cost_type character varying, f_keystr character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
背景描述：	1.计算国内海外全球月卷积金额
			2.筛选单SPART
			3.关联主力编码表字段
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_ICT_RAW_DATA_EXAMINE'; --存储过程名称
  V_VERSION_ID BIGINT ; --新版本号ID, 取自 FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S
  V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYY-MM'); --新的版本中文名称
  V_DIM_VERSION BIGINT ; --最新的品类-专家团映射维版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 存放当前是否已有版本号标识
  V_SQL TEXT;
  
  V_SUM_RMB_AMT VARCHAR(200);
  V_AVG_RMB_AMT VARCHAR(200);
  V_FROM_TABLE VARCHAR(50);
  V_TO_TABLE VARCHAR(50);

BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行,成本类型:'||F_COST_TYPE);
   

   --判断PBI维度和成本类型选择来源表,目标表,关联表,金额字段
  IF F_COST_TYPE = 'PSP' THEN 
	 V_FROM_TABLE := 'DM_FCST_ICT_PSP_PROD_UNIT_T';
	 V_TO_TABLE := 'DM_FCST_ICT_PSP_RAW_DATA_EXAMINE_T'; 
  ELSIF F_COST_TYPE = 'STD'  THEN 
	 V_FROM_TABLE := 'DM_FCST_ICT_STD_PROD_UNIT_T';
	 V_TO_TABLE := 'DM_FCST_ICT_STD_RAW_DATA_EXAMINE_T'; 
  ELSE 
	RETURN '入参有误';
  END IF;

  
    
     --判断成本类型获得主力编码表
  IF F_COST_TYPE = 'PSP' THEN 
     V_SUM_RMB_AMT := 'SUM(RMB_COST_AMT) AS RMB_COST_AMT,';
	 V_AVG_RMB_AMT := ' SUM(RMB_COST_AMT) / NULLIF(SUM(PART_QTY),0),' ;
	 
	 
  ELSIF f_cost_type = 'STD'  THEN 
	 V_SUM_RMB_AMT := ' SUM(TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256''))) AS RMB_COST_AMT ,';
	 V_AVG_RMB_AMT := ' SUM(TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256'')))  / NULLIF(SUM(PART_QTY),0),' ;
	 
	 
  ELSE 
	RETURN '入参有误';
  END IF;
  
 
	
--新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 

		
   --主力编码版本号赋值
  SELECT VERSION_ID INTO V_DIM_VERSION
    FROM
       DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 		
	
	
   --清空目标表数据
 DBMS_OUTPUT.PUT_LINE('-----清空临时目标表开始------'); 
 EXECUTE IMMEDIATE 'TRUNCATE '||V_TO_TABLE ;
 DBMS_OUTPUT.PUT_LINE('-----清空临时目标表结束------'); 
  
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据,成本类型:'||F_COST_TYPE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
	
	
--向目标表插数,带上均本
V_SQL:= 'INSERT INTO 	'||V_TO_TABLE||'  (
	VERSION_ID,
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	HW_CONTRACT_NUM,
	BG_CODE ,
	BG_CN_NAME ,
	LV0_PROD_LIST_CODE ,
	LV0_PROD_LIST_CN_NAME ,
	LV1_PROD_LIST_CODE ,
	LV1_PROD_LIST_CN_NAME ,
	LV2_PROD_LIST_CODE ,
	LV2_PROD_LIST_CN_NAME ,
	LV3_PROD_LIST_CODE ,
	LV3_PROD_LIST_CN_NAME ,
	LV4_PROD_LIST_CODE ,
	LV4_PROD_LIST_CN_NAME ,
	LV0_PROD_RND_TEAM_CODE ,
	LV0_PROD_RD_TEAM_CN_NAME ,
	LV1_PROD_RND_TEAM_CODE ,
	LV1_PROD_RD_TEAM_CN_NAME ,
	LV2_PROD_RND_TEAM_CODE ,
	LV2_PROD_RD_TEAM_CN_NAME ,
	LV3_PROD_RND_TEAM_CODE ,
	LV3_PROD_RD_TEAM_CN_NAME ,
	LV4_PROD_RND_TEAM_CODE ,
	LV4_PROD_RD_TEAM_CN_NAME ,
	LV0_INDUSTRY_CATG_CODE ,
	LV0_INDUSTRY_CATG_CN_NAME ,
	LV1_INDUSTRY_CATG_CODE ,
	LV1_INDUSTRY_CATG_CN_NAME ,
	LV2_INDUSTRY_CATG_CODE ,
	LV2_INDUSTRY_CATG_CN_NAME ,
	LV3_INDUSTRY_CATG_CODE ,
	LV3_INDUSTRY_CATG_CN_NAME ,
	LV4_INDUSTRY_CATG_CODE ,
	LV4_INDUSTRY_CATG_CN_NAME ,  
	RMB_COST_AMT ,
	RMB_AVG_AMT,
	SPART_CODE,
	PROD_QTY,
	OVERSEA_FLAG ,
	CREATED_BY,
	CREATION_DATE,
	LAST_UPDATED_BY,
	LAST_UPDATE_DATE,
	DEL_FLAG
	)
	SELECT 
	'||V_VERSION_ID||',
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	HW_CONTRACT_NUM ,
	BG_CODE ,
	BG_CN_NAME ,
	LV0_PROD_LIST_CODE ,
	LV0_PROD_LIST_CN_NAME ,
	LV1_PROD_LIST_CODE ,
	LV1_PROD_LIST_CN_NAME ,
	LV2_PROD_LIST_CODE ,
	LV2_PROD_LIST_CN_NAME ,
	LV3_PROD_LIST_CODE ,
	LV3_PROD_LIST_CN_NAME ,
	LV4_PROD_LIST_CODE ,
	LV4_PROD_LIST_CN_NAME ,
	LV0_PROD_RND_TEAM_CODE ,
	LV0_PROD_RD_TEAM_CN_NAME ,
	LV1_PROD_RND_TEAM_CODE ,
	LV1_PROD_RD_TEAM_CN_NAME ,
	LV2_PROD_RND_TEAM_CODE ,
	LV2_PROD_RD_TEAM_CN_NAME ,
	LV3_PROD_RND_TEAM_CODE ,
	LV3_PROD_RD_TEAM_CN_NAME ,
	LV4_PROD_RND_TEAM_CODE ,
	LV4_PROD_RD_TEAM_CN_NAME ,
	LV0_INDUSTRY_CATG_CODE ,
	LV0_INDUSTRY_CATG_CN_NAME ,
	LV1_INDUSTRY_CATG_CODE ,
	LV1_INDUSTRY_CATG_CN_NAME ,
	LV2_INDUSTRY_CATG_CODE ,
	LV2_INDUSTRY_CATG_CN_NAME ,
	LV3_INDUSTRY_CATG_CODE ,
	LV3_INDUSTRY_CATG_CN_NAME ,
	LV4_INDUSTRY_CATG_CODE ,
	LV4_INDUSTRY_CATG_CN_NAME ,  
	NULL AS RMB_COST_AMT,
	NULL AS RMB_AVG_AMT,
    SPART_CODE,
	SUM(PART_QTY) AS PART_QTY,
	OVERSEA_FLAG,
	-1 AS CREATED_BY,
	CURRENT_TIMESTAMP AS CREATION_DATE,
	-1 AS LAST_UPDATED_BY,
	CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
	''N'' AS DEL_FLAG
	FROM '||V_FROM_TABLE||'
	GROUP BY
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	HW_CONTRACT_NUM ,
	BG_CODE ,
	BG_CN_NAME ,
 	LV0_PROD_LIST_CODE ,
	LV0_PROD_LIST_CN_NAME ,
	LV1_PROD_LIST_CODE ,
	LV1_PROD_LIST_CN_NAME ,
	LV2_PROD_LIST_CODE ,
	LV2_PROD_LIST_CN_NAME ,
	LV3_PROD_LIST_CODE ,
	LV3_PROD_LIST_CN_NAME ,
	LV4_PROD_LIST_CODE ,
	LV4_PROD_LIST_CN_NAME ,
	LV0_PROD_RND_TEAM_CODE ,
	LV0_PROD_RD_TEAM_CN_NAME ,
	LV1_PROD_RND_TEAM_CODE ,
	LV1_PROD_RD_TEAM_CN_NAME ,
	LV2_PROD_RND_TEAM_CODE ,
	LV2_PROD_RD_TEAM_CN_NAME ,
	LV3_PROD_RND_TEAM_CODE ,
	LV3_PROD_RD_TEAM_CN_NAME ,
	LV4_PROD_RND_TEAM_CODE ,
	LV4_PROD_RD_TEAM_CN_NAME ,
	LV0_INDUSTRY_CATG_CODE ,
	LV0_INDUSTRY_CATG_CN_NAME ,
	LV1_INDUSTRY_CATG_CODE ,
	LV1_INDUSTRY_CATG_CN_NAME ,
	LV2_INDUSTRY_CATG_CODE ,
	LV2_INDUSTRY_CATG_CN_NAME ,
	LV3_INDUSTRY_CATG_CODE ,
	LV3_INDUSTRY_CATG_CN_NAME ,
	LV4_INDUSTRY_CATG_CODE ,
	LV4_INDUSTRY_CATG_CN_NAME , 
	SPART_CODE,
	OVERSEA_FLAG
	'
	;
	
DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE IMMEDIATE V_SQL;	


   
  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插数到 '||V_TO_TABLE||'表',
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  
  --3.收集统计信息
  V_SQL := 'ANALYZE '||V_TO_TABLE;
   EXECUTE IMMEDIATE V_SQL;
  
  
  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集 '||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END
$$
/

