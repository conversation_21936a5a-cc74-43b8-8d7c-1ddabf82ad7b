-- Name: f_dm_foi_item_custody_per_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_item_custody_per_t(f_caliber_flag character varying, f_cate_version bigint DEFAULT NULL::bigint, f_item_version bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$ 
	/*
创建时间：2022-10-22
创建人  ：songhui swx1182801
修改时间：2024-02-26
最后修改人：杨泽宝 YWX1106160
背景描述：规格品监控数据表,然后调用该函数的版本将相对应的数据生成导入到目标表中
参数描述：参数一(f_cate_version)：top品类清单表最新版本号
		  参数二(f_item_version)：导入通用版本号（规格品清单版本号）
		  参数三(x_success_flag)  ：运行状态返回值-成功或者失败
		  参数四(f_caliber_flag):入参 I 代表ICT采购，E代表数字能源
事例    ：select FIN_DM_OPT_FOI.f_dm_foi_item_custody_per_t(202101)	--一个版本的数据
*/
declare
	V_SP_NAME varchar(50) := 'FIN_DM_OPT_FOI.F_DM_FOI_ITEM_CUSTODY_PER_T';
	v_dml_row_count  number default 0 ;
	v_version_id bigint ;
	v_exception_flag varchar(50):='1';--异常定点
    v_part1_public TEXT := NULL;   -- 配置调度的函数公共部分1
	v_execute_sql TEXT := NULL;   -- 执行SQL
	V_STEP_NUM   BIGINT := 0; --步骤号
	V_SQL        TEXT;   --SQL逻辑
	V_TYPE        VARCHAR(50);--字段1
	
	V_FROM_TABLE1 VARCHAR(100); -- 来源表1
	V_TO_TABLE   VARCHAR(100); -- 目标表
begin
	x_success_flag := '1';
	--0.日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
	
--通过F_DIMENSION_TYPE传参,确认来源表和目标表
  IF F_CALIBER_FLAG = 'I' THEN -- ICT采购
     V_FROM_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOI_LEV_REC_AMT_T';--来源表1
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ITEM_CUSTODY_PER_T';--目标表
  ELSIF F_CALIBER_FLAG = 'E' THEN -- 数字能源
     V_FROM_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_ACTUAL_COST_T';--来源表1
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_ITEM_CUSTODY_PER_T'; --目标表
  ELSE
    NULL;
  END IF; 
	
IF F_CALIBER_FLAG = 'I' THEN -- ICT采购	
	-- 将查询到的数据放到变量中的公共sql
	V_PART1_PUBLIC := '
                    SELECT VALUE 
                        FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T
                        WHERE ENABLE_FLAG = ''Y''
                        AND UPPER(PARA_NAME) = ''$PARA_NAME$'';
                      ';  	 	
	-- 无论是季调还是月调，都通过sql从变量参数表（dm_foi_plan_var_para_t）表中取出对应的规格品版本号
	IF f_cate_version IS NULL AND f_item_version IS NULL  THEN
		V_EXECUTE_SQL := REPLACE( V_PART1_PUBLIC,'$PARA_NAME$','VERSION_ID-ITEM');  -- 规格品版本号
		EXECUTE V_EXECUTE_SQL INTO V_VERSION_ID; 		
		-- 业务在前台配置页面调整规格品数据时，Java传参，优先取规格品版本号，若无，则取TOP品类版本号作为生成本次数据的版本号		
	ELSE v_version_id := nvl ( f_item_version, f_cate_version );	
	END IF;	
ELSIF F_CALIBER_FLAG = 'E' THEN -- 数字能源
	IF f_cate_version IS NULL  AND f_item_version IS NULL  THEN
		SELECT VERSION_ID INTO V_VERSION_ID
		FROM FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T 
		WHERE substr(version,1,6) = TO_CHAR(CURRENT_DATE, 'YYYYMM')
		    AND DEL_FLAG = 'N'
			AND STATUS = 1
			AND UPPER(DATA_TYPE) = 'ITEM'
			AND UPPER(VERSION_TYPE) IN ('AUTO','FINAL');
	ELSE v_version_id := nvl ( f_item_version, f_cate_version );
	END IF;	
END IF;
	
  --1.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '执行的版本号为：'||v_version_id||'，其中f_item_version 为:'||f_item_version||'，f_cate_version 为：'||f_cate_version,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => x_success_flag,
   F_ERRBUF => 'SUCCESS');
	
   
 -- 支持重跑，删除除目标表要插入的数据
  V_SQL := 'delete from '||V_TO_TABLE||' where version_id='''||v_version_id||'''';
  EXECUTE IMMEDIATE V_SQL ;
  
 --2.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除'||V_TO_TABLE||'表的数据，并取到version_id='||v_version_id,
   F_FORMULA_SQL_TXT => V_SQL,--本段执行逻辑SQL
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => x_success_flag,
   F_ERRBUF => 'SUCCESS');
   
    --取不同的字段名：目标表中的字段名不一致，所以需要再次做一下处理
	IF  F_CALIBER_FLAG = 'I' THEN -- ICT采购
		V_TYPE:='CONTINUITY_TYPE';
	ELSIF F_CALIBER_FLAG = 'E' THEN --数字能源
		V_TYPE:='GROUP_PUR_FLAG';
	END IF;
  
	---插入目标表数据（dm_foi_item_custody_per_t/dm_foi_energy_item_custody_per_t）
	V_SQL :='
	insert into '||V_TO_TABLE||' (
			/*id, --（主键）*/
            year,--会计期(年)
            period_id,--会计期
            group_code,--分层级code编码
            group_cn_name,--分层级中文名称
            group_level,--Group层级（Lv2：生产采购、Lv3：专家团、Lv4：模块、CATEGORY：品类）
            l4_ceg_code,--模块编码
            l4_ceg_short_cn_name,--模块（Group Lv4简称）
            l3_ceg_code,--专家团编码
            l3_ceg_short_cn_name,--专家团（Group Lv3简称）
            l2_ceg_code,--生产采购编码
            l2_ceg_cn_name,--生产采购
            amt_percent,--金额占比（规格品在item的占比）
            created_by,--创建人
            creation_date,--创建时间
            last_updated_by,--修改人
            last_update_date,--修改时间
            del_flag,--删除标识(未删除：N，已删除：Y)
            version_id,--版本id
            l4_ceg_cn_name,--模块中文名称
            l3_ceg_cn_name,--专家团中文名称
            '||V_TYPE||'
			)     
	 -----金额占比（规格品在item的占比)
	 select 
	     /*FIN_DM_OPT_FOI.dm_foi_item_custody_per_S.nextval id,*/
       t1.year,
       t1.period_id,
       t1.group_code,
       t1.group_cn_name,
       t1.group_level,
       t1.l4_ceg_code,
       t1.l4_ceg_short_cn_name,
       t1.l3_ceg_code,
       t1.l3_ceg_short_cn_name,
       t1.l2_ceg_code,
       t1.l2_ceg_cn_name,
       t1.amt_percent ,---金额占比（规格品在item的占比）
       -1 as created_by,
       current_timestamp as creation_date,
       -1 as last_updated_by,
       current_timestamp as last_update_date,
       ''N'' as  del_flag,
	   '''||v_version_id||''' as version_id, 
       t1.l4_ceg_cn_name,
       t1.l3_ceg_cn_name,
	   t1.'||V_TYPE||'
	from (
					select 	
							 lra.YEAR,
							 lra.period_id,
							 lra.group_code,
							 lra.group_cn_name,
							 lra.group_level,
							 lra.l4_ceg_code,
							 lra.l4_ceg_short_cn_name,
							 lra.l3_ceg_code,
							 lra.l3_ceg_short_cn_name,
							 lra.l2_ceg_code,
							 lra.l2_ceg_cn_name,
							 CASE WHEN  SUM(lra.RECEIVE_AMT_CNY) =0 then 0 else
								round(sum(CASE WHEN lra.top_flag =''Y'' THEN lra.RECEIVE_AMT_CNY else 0 END)/SUM(lra.RECEIVE_AMT_CNY),6)
								end AS amt_percent,---金额占比（规格品在item的占比）
							 lra.l4_ceg_cn_name,
							 lra.l3_ceg_cn_name,
							 lra.'||V_TYPE||'
						from   '||V_FROM_TABLE1||' lra 
						where lra.version_id='''||v_version_id||''' 
							and UPPER(lra.group_level) in (''LV2'',''LV3'',''LV4'',''CATEGORY'')    
						         GROUP BY	
							            lra.YEAR,
										lra.period_id,
										lra.group_code,
										lra.group_cn_name,
										lra.group_level,
										lra.l4_ceg_code,
										lra.l4_ceg_short_cn_name,
										lra.l3_ceg_code,
										lra.l3_ceg_short_cn_name,
										lra.l2_ceg_code,
										lra.l2_ceg_cn_name,
										lra.l4_ceg_cn_name,
										lra.l3_ceg_cn_name,
										lra.'||V_TYPE||') t1  
						 where amt_percent <> 0;';
	EXECUTE IMMEDIATE V_SQL ; 
						 
  --3.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入数据到'||V_TO_TABLE||'表',
   F_FORMULA_SQL_TXT => V_SQL,--本段执行逻辑SQL
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => x_success_flag,
   F_ERRBUF => 'SUCCESS');  
   
  --4.收集信息
  V_SQL :='ANALYSE '||V_TO_TABLE;
  EXECUTE IMMEDIATE V_SQL ; 
  --5.日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
return 'SUCCESS';
EXCEPTION
  WHEN OTHERS THEN
  x_success_flag := 0;  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => x_success_flag, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );	 
end;
$$
/

