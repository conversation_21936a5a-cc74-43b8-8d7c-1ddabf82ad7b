-- Name: f_dm_foq_raw_materials_price_trend_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foq_raw_materials_price_trend_t(OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：2023-08-10
创建人  ：朱雅欣
背景描述：原材料价格趋势表,调用该函数将相对应的数据生成导入到目标表中
参数描述：参数(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_foq_raw_materials_price_trend_t()

*/


DECLARE
	v_sp_name varchar(200) := 'fin_dm_opt_foi.f_dm_foq_raw_materials_price_trend_t';
	v_tbl_name varchar(200) := 'fin_dm_opt_foi.dm_foq_raw_materials_price_trend_t';
	v_dml_row_count number default 0 ;
  v_count number default 0 ;

BEGIN
	x_success_flag := '1';                          --1表示成功
	

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,               --sp名称
        p_log_para_list => '',                    --参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '原材料价格趋势'||v_tbl_name||'：开始运行',--日志描述
        p_log_formula_sql_txt => null,            --错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null                      --错误编码
      ) ;
						
    --判断 来源表 foq_dwk_grp_outs_act_fcst_i 是否有数据，然后truncate数据
    --支持重跑，清除目标表要插入全量数据
	SELECT count(1) INTO v_count FROM fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i;
	IF v_count > 0
    THEN 		
	TRUNCATE TABLE fin_dm_opt_foi.dm_foq_raw_materials_price_trend_t;
	
	DROP TABLE IF EXISTS dm_foq_raw_materials_price_trend_tmp;
	CREATE TEMPORARY TABLE dm_foq_raw_materials_price_trend_tmp (
	    period_id            numeric
     ,data_date            numeric
     ,data_category        varchar(100)
     ,index_code           varchar(600)
     ,index_name           varchar(1000)
     ,index_short_name     varchar(1000)
     ,source_name          varchar(1000)
	   ,source_table         varchar(600)
     ,frequency_name       varchar(1000)
     ,unit_name            varchar(1000)
     ,data_value           numeric
	) on commit preserve rows distribute by replication
	;
	INSERT INTO dm_foq_raw_materials_price_trend_tmp (
	    period_id              
     ,data_date            
     ,data_category            
     ,index_code           
     ,index_name           
     ,index_short_name     
     ,source_name          
	   ,source_table         
     ,frequency_name       
     ,unit_name            
     ,data_value 
     )
  SELECT period_id                                       
        , data_date                               
        , data_category                           
	      , index_code                                
        , index_name                                
        , index_short_name                          
	      , source_name                               
	      , source_table                               
        , frequency_name                            
        , unit_name                               
        , data_value                                
    FROM  fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i A
	 WHERE  del_flag = 'N'
		 AND  data_category = '预测数'
     AND  period_id = ( SELECT MAX ( A2.period_id ) FROM fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i A2 )
	 UNION  ALL
	SELECT	period_id                               
        , data_date                               
        , data_category                           
	      , index_code                                
        , index_name                                
        , index_short_name                          
	      , source_name                               
	      , source_table                               
        , frequency_name                            
        , unit_name                               
        , data_value                             
	 FROM	  fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i  A 
	WHERE   del_flag = 'N'
	  AND   data_category = '实际数'
	  AND   EXISTS(select 1 from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i A1 
		              where A.index_code = A1.index_code AND A1.data_category = '预测数'
                    AND A1.period_id = (select max(A2.period_id) from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i A2));
	
		--插入目标表数据
		INSERT INTO fin_dm_opt_foi.dm_foq_raw_materials_price_trend_t
    (   period_id            --会计期
      , data_date            --数据日期
      , data_type            --数据类别
      , index_code           --指标编码
      , index_name           --指标名称
      , index_short_name     --指标简称
      , metal_short_cn_name  --金属简称
      , source_name          --数据源
	    , source_table         --数据源表
      , frequency_name       --频率
      , unit_name            --单位
      , data_value           --数据值
      , data_value_yoy       --同比
      , data_value_pp_ptd    --环比
      , remark               --备注
      , created_by           --创建人
      , creation_date        --创建时间
      , last_updated_by      --修改人
      , last_update_date     --修改时间
      , del_flag             --是否删除
     )
 SELECT C.period_id                              AS period_id	            --会计期
      , A.data_date                              AS data_date	            --数据日期
      , A.data_category                          AS data_type	            --数据类别
 	    , A.index_code                             AS index_code	          --指标编码
      , A.index_name                             AS index_name	          --指标名称
      , A.index_short_name                       AS index_short_name	    --指标简称
 	    , C.index_short_name                       AS metal_short_cn_name   --金属简称
 	    , A.source_name                            AS source_name	          --数据源
 	    , A.source_table                           AS source_table          --数据源表
      , A.frequency_name                         AS frequency_name	      --频率
      , A.unit_name                              AS unit_name	            --单位
      , A.data_value                             AS data_value	          --数据值
      , (A.data_value-D.data_value)/D.data_value AS data_value_yoy        --同比
      , (A.data_value-E.data_value)/E.data_value AS data_value_pp_ptd     --环比
 	    , ''                                       AS remark                --备注
      , -1                                       AS created_by            --创建人
      , current_timestamp                        AS creation_date         --创建时间
      , -1                                       AS last_updated_by       --修改人
      , current_timestamp                        AS last_update_date      --修改时间
      , 'N'                                      AS del_flag              --是否删除
 FROM  dm_foq_raw_materials_price_trend_tmp  A
 LEFT JOIN (SELECT max(C1.period_id) AS period_id
                  ,C1.index_code
         	       ,C1.index_short_name
            FROM   fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i C1
            WHERE  C1.data_category = '预测数'
            GROUP BY C1.index_code
         	       ,C1.index_short_name) C
   ON  A.index_code=C.index_code
 LEFT JOIN dm_foq_raw_materials_price_trend_tmp  D
   ON  A.index_code=D.index_code
  AND  D.data_date=TO_CHAR(to_date(A.data_date::TEXT,'yyyyMM')- interval '12months' ,'yyyyMM')::numeric
 LEFT JOIN dm_foq_raw_materials_price_trend_tmp  E
   ON  A.index_code=E.index_code
  AND  E.data_date=TO_CHAR(to_date(A.data_date::TEXT,'yyyyMM')- interval '1months'  ,'yyyyMM')::numeric
 ;
		

		
	v_dml_row_count := nvl(sql%rowcount,0);	-- 收集数据量

  -- 写结束日志
    perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,      --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',         --参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '原材料价格趋势'||v_tbl_name||':计算后的数据量:'||v_dml_row_count||'结束运行',--日志描述
        p_log_formula_sql_txt => null, --错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null           --错误编码
      ) ;

  end if 
	;

exception
  	when others then

      perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                     --版本
        p_log_sp_name => v_sp_name,                   --sp名称
        p_log_para_list => '',                        --参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,             --错误信息
		p_log_row_count => null,
		p_log_errbuf => sqlstate                      --错误编码
      ) ;
	x_success_flag := '2001';	                      --2001表示失败

    --收集统计信息
    analyse fin_dm_opt_foi.dm_foq_raw_materials_price_trend_t;

end;
$$
/

