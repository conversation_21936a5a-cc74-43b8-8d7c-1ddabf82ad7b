-- Name: f_dm_fcst_price_mid_spart_annl_amp; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_mid_spart_annl_amp(f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年12月版本
  创建人：twx1139790
  背景描述：根据年累计均本补齐数据，计算SPART层级涨跌幅数据，再与权重表关联，计算得到上层级涨跌幅数据
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FCST_PRICE_MID_SPART_ANNL_AMP('');
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_PRICE_MID_SPART_ANNL_AMP'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_YEAR INT;
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
     if MONTH(CURRENT_TIMESTAMP) = 1
  then V_YEAR = YEAR(NOW()) -1;
  ELSE
  V_YEAR = YEAR(NOW());
  END IF ;
   
  -- 取出年度版本号
  IF F_VERSION_ID IS NULL THEN 
    SELECT VERSION_ID INTO V_VERSION_ID   -- 年度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到最新年度版本号为：'||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 删除结果表数据
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T WHERE GROUP_LEVEL IN (''SPART'')';   -- 删除涨跌幅临时表为SPART层级的其余层级数据

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除：DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T，版本号为：'||V_VERSION_ID||'的，层级为：SPART层级的表数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

 --------------------------------------------------------------------------------- SPART层级涨跌幅计算 ------------------------------------------------------------------------------------
  -- 插入SPART层级涨跌幅到临时表
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T(
         VERSION_ID,
         PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CODE,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CODE,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CODE,
         LV3_PROD_LIST_CN_NAME,
         LV4_PROD_LIST_CODE,
         LV4_PROD_LIST_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         PARENT_CODE,
         PARENT_CN_NAME,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME,
		 ENABLE_FLAG
    )
  -- 将年均本数据按年份行转列 
  WITH BY_YEAR_AVG_TMP AS(
  SELECT LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CODE,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CODE,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CODE,
         LV3_PROD_LIST_CN_NAME,
         LV4_PROD_LIST_CODE,
         LV4_PROD_LIST_CN_NAME,
         SPART_CODE AS GROUP_CODE,
         SPART_CN_NAME AS GROUP_CN_NAME,
         'SPART' AS GROUP_LEVEL,
         LV4_PROD_LIST_CODE AS PARENT_CODE,
         LV4_PROD_LIST_CN_NAME AS PARENT_CN_NAME,
         SUM(CASE WHEN PERIOD_YEAR = V_YEAR-3 THEN USD_PNP_AVG ELSE 0 END) AS LAST_THREE_YEAR_AVG,
         SUM(CASE WHEN PERIOD_YEAR = V_YEAR-2 THEN USD_PNP_AVG ELSE 0 END) AS LAST_TWO_YEAR_AVG,
         SUM(CASE WHEN PERIOD_YEAR = V_YEAR-1 THEN USD_PNP_AVG ELSE 0 END) AS LAST_YEAR_AVG,
         SUM(CASE WHEN PERIOD_YEAR = V_YEAR THEN USD_PNP_AVG ELSE 0 END) AS YEAR_AVG,
         VIEW_FLAG,                    
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         BG_CODE,                      
         BG_CN_NAME
     FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_AVG_T
     WHERE VERSION_ID = V_VERSION_ID
     GROUP BY LV0_PROD_LIST_CODE,
              LV0_PROD_LIST_CN_NAME,
              LV1_PROD_LIST_CODE,
              LV1_PROD_LIST_CN_NAME,
              LV2_PROD_LIST_CODE,
              LV2_PROD_LIST_CN_NAME,
              LV3_PROD_LIST_CODE,
              LV3_PROD_LIST_CN_NAME,
              LV4_PROD_LIST_CODE,
              LV4_PROD_LIST_CN_NAME,
              SPART_CODE,
              SPART_CN_NAME,
              VIEW_FLAG,                    
              OVERSEA_FLAG,
              REGION_CODE,
              REGION_CN_NAME,
              REPOFFICE_CODE,
              REPOFFICE_CN_NAME,
              SIGN_TOP_CUST_CATEGORY_CODE,
              SIGN_TOP_CUST_CATEGORY_CN_NAME,
              SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
              BG_CODE,                      
              BG_CN_NAME
	)
  , YEAR_ANNUAL_AMP_TMP AS(
      SELECT V_YEAR-2 AS PERIOD_YEAR,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME,
             LV1_PROD_LIST_CODE,
             LV1_PROD_LIST_CN_NAME,
             LV2_PROD_LIST_CODE,
             LV2_PROD_LIST_CN_NAME,
             LV3_PROD_LIST_CODE,
             LV3_PROD_LIST_CN_NAME,
             LV4_PROD_LIST_CODE,
             LV4_PROD_LIST_CN_NAME,
             GROUP_CODE,
             GROUP_CN_NAME,
             GROUP_LEVEL,
             ((LAST_TWO_YEAR_AVG/NULLIF(LAST_THREE_YEAR_AVG,0))-1) AS ANNUAL_AMP,
             PARENT_CODE,
             PARENT_CN_NAME,
             VIEW_FLAG,                    
             OVERSEA_FLAG,
             REGION_CODE,
             REGION_CN_NAME,
             REPOFFICE_CODE,
             REPOFFICE_CN_NAME,
             SIGN_TOP_CUST_CATEGORY_CODE,
             SIGN_TOP_CUST_CATEGORY_CN_NAME,
             SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
             BG_CODE,                      
             BG_CN_NAME
          FROM BY_YEAR_AVG_TMP
      UNION ALL
      SELECT V_YEAR-1 AS PERIOD_YEAR,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME,
             LV1_PROD_LIST_CODE,
             LV1_PROD_LIST_CN_NAME,
             LV2_PROD_LIST_CODE,
             LV2_PROD_LIST_CN_NAME,
             LV3_PROD_LIST_CODE,
             LV3_PROD_LIST_CN_NAME,
             LV4_PROD_LIST_CODE,
             LV4_PROD_LIST_CN_NAME,
             GROUP_CODE,
             GROUP_CN_NAME,
             GROUP_LEVEL,
             ((LAST_YEAR_AVG/NULLIF(LAST_TWO_YEAR_AVG,0))-1) AS ANNUAL_AMP,
             PARENT_CODE,
             PARENT_CN_NAME,
             VIEW_FLAG,                    
             OVERSEA_FLAG,
             REGION_CODE,
             REGION_CN_NAME,
             REPOFFICE_CODE,
             REPOFFICE_CN_NAME,
             SIGN_TOP_CUST_CATEGORY_CODE,
             SIGN_TOP_CUST_CATEGORY_CN_NAME,
             SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
             BG_CODE,                      
             BG_CN_NAME
         FROM BY_YEAR_AVG_TMP   
      UNION ALL
      SELECT V_YEAR AS PERIOD_YEAR,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME,
             LV1_PROD_LIST_CODE,
             LV1_PROD_LIST_CN_NAME,
             LV2_PROD_LIST_CODE,
             LV2_PROD_LIST_CN_NAME,
             LV3_PROD_LIST_CODE,
             LV3_PROD_LIST_CN_NAME,
             LV4_PROD_LIST_CODE,
             LV4_PROD_LIST_CN_NAME,
             GROUP_CODE,
             GROUP_CN_NAME,
             GROUP_LEVEL,
             ((YEAR_AVG/NULLIF(LAST_YEAR_AVG,0))-1) AS ANNUAL_AMP,
             PARENT_CODE,
             PARENT_CN_NAME,
             VIEW_FLAG,                    
             OVERSEA_FLAG,
             REGION_CODE,
             REGION_CN_NAME,
             REPOFFICE_CODE,
             REPOFFICE_CN_NAME,
             SIGN_TOP_CUST_CATEGORY_CODE,
             SIGN_TOP_CUST_CATEGORY_CN_NAME,
             SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
             BG_CODE,                      
             BG_CN_NAME
         FROM BY_YEAR_AVG_TMP 
              ),
  -- 判断涨跌幅值>1的SPART数据，所有年份都需要剔除，所以不考虑
  EXCEP_DIM_TMP AS (
  SELECT DISTINCT LV0_PROD_LIST_CODE,
         LV1_PROD_LIST_CODE,
         LV2_PROD_LIST_CODE,
         LV3_PROD_LIST_CODE,
         LV4_PROD_LIST_CODE,
         GROUP_CODE,
         PARENT_CODE,
         OVERSEA_FLAG,
         REGION_CODE,
         REPOFFICE_CODE,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         BG_CODE,
         'N' AS ENABLE_FLAG
      FROM YEAR_ANNUAL_AMP_TMP
      WHERE NVL(ANNUAL_AMP,0) > 1
  )
  -- 计算最细粒度层级的年度涨跌幅数据
       SELECT V_VERSION_ID AS VERSION_ID,
              T1.PERIOD_YEAR,
              T1.LV0_PROD_LIST_CODE,
              T1.LV0_PROD_LIST_CN_NAME,
              T1.LV1_PROD_LIST_CODE,
              T1.LV1_PROD_LIST_CN_NAME,
              T1.LV2_PROD_LIST_CODE,
              T1.LV2_PROD_LIST_CN_NAME,
              T1.LV3_PROD_LIST_CODE,
              T1.LV3_PROD_LIST_CN_NAME,
              T1.LV4_PROD_LIST_CODE,
              T1.LV4_PROD_LIST_CN_NAME,
              T1.GROUP_CODE,
              T1.GROUP_CN_NAME,
              T1.GROUP_LEVEL,
              NVL(T1.ANNUAL_AMP,0) AS ANNUAL_AMP,
              T1.PARENT_CODE,
              T1.PARENT_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.REGION_CODE,
              T1.REGION_CN_NAME,
              T1.REPOFFICE_CODE,
              T1.REPOFFICE_CN_NAME,
              T1.SIGN_TOP_CUST_CATEGORY_CODE,
              T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
              T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
              T1.VIEW_FLAG,
              -1 AS CREATED_BY,
              CURRENT_TIMESTAMP AS CREATION_DATE,
              -1 AS LAST_UPDATED_BY,
              CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
              'N' AS DEL_FLAG,
              T1.BG_CODE,
              T1.BG_CN_NAME,
			  DECODE(T2.ENABLE_FLAG,NULL,'Y',T2.ENABLE_FLAG) AS ENABLE_FLAG
           FROM YEAR_ANNUAL_AMP_TMP T1
		   LEFT JOIN EXCEP_DIM_TMP T2
		   ON T1.GROUP_CODE = T2.GROUP_CODE
          AND T1.LV4_PROD_LIST_CODE = T2.LV4_PROD_LIST_CODE
	      AND T1.LV3_PROD_LIST_CODE = T2.LV3_PROD_LIST_CODE
	      AND T1.LV2_PROD_LIST_CODE = T2.LV2_PROD_LIST_CODE
	      AND T1.LV1_PROD_LIST_CODE = T2.LV1_PROD_LIST_CODE
	      AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
	      AND T1.VIEW_FLAG = T2.VIEW_FLAG
          AND T1.BG_CODE = T2.BG_CODE
          AND NVL(T1.OVERSEA_FLAG,'S1') = NVL(T2.OVERSEA_FLAG,'S1')
          AND NVL(T1.REGION_CODE,'S2') = NVL(T2.REGION_CODE,'S2')
          AND NVL(T1.REPOFFICE_CODE,'S3') = NVL(T2.REPOFFICE_CODE,'S3')
          AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'S4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'S4')
          AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5');
  
   -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入VERSION_ID= '||V_VERSION_ID ||' 的SPART层级的数据到DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  ----------------------------------------------------------------------------- 对年均本表打上对应是否有效标识的标签 ---------------------------------------------------------------------------
  -- 创建年均本临时表
  DROP TABLE IF EXISTS FCST_PRICE_AVG_APPEND_TMP;
  CREATE TEMPORARY TABLE FCST_PRICE_AVG_APPEND_TMP(
         PERIOD_YEAR NUMERIC,
         LV0_PROD_LIST_CODE CHARACTER VARYING(50),
         LV1_PROD_LIST_CODE CHARACTER VARYING(50),
         LV2_PROD_LIST_CODE CHARACTER VARYING(50),
         LV3_PROD_LIST_CODE CHARACTER VARYING(50),
         LV4_PROD_LIST_CODE CHARACTER VARYING(50),
         LV0_PROD_LIST_CN_NAME CHARACTER VARYING(200),
         LV1_PROD_LIST_CN_NAME CHARACTER VARYING(200),
         LV2_PROD_LIST_CN_NAME CHARACTER VARYING(200),
         LV3_PROD_LIST_CN_NAME CHARACTER VARYING(200),
         LV4_PROD_LIST_CN_NAME CHARACTER VARYING(200),
         OVERSEA_FLAG CHARACTER VARYING(20),
         REGION_CODE CHARACTER VARYING(50),
         REGION_CN_NAME CHARACTER VARYING(200),
         REGION_NAME_ABBR CHARACTER VARYING(200),
         REPOFFICE_CODE CHARACTER VARYING(50),
         REPOFFICE_CN_NAME CHARACTER VARYING(200),
         REPOFFICE_NAME_ABBR CHARACTER VARYING(200),
         SIGN_TOP_CUST_CATEGORY_CODE CHARACTER VARYING(50),
         SIGN_TOP_CUST_CATEGORY_CN_NAME CHARACTER VARYING(200),
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME CHARACTER VARYING(100),
         SPART_CODE CHARACTER VARYING(40),
         SPART_CN_NAME CHARACTER VARYING(2000),
         USD_PNP_AMT NUMERIC,
         USD_PNP_AVG NUMERIC,
         VIEW_FLAG CHARACTER VARYING(20),
         APPEND_FLAG CHARACTER VARYING(5),
         APPEND_YEAR INT,
         BG_CODE CHARACTER VARYING(50),
         BG_CN_NAME CHARACTER VARYING(200),
         ENABLE_FLAG CHARACTER VARYING(2)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE, LV4_PROD_LIST_CODE, REPOFFICE_CODE);

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '创建临时表成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 将年均本数据插入临时表
  INSERT INTO FCST_PRICE_AVG_APPEND_TMP(
         PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV1_PROD_LIST_CODE,
         LV2_PROD_LIST_CODE,
         LV3_PROD_LIST_CODE,
         LV4_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CN_NAME,
         LV4_PROD_LIST_CN_NAME,
         SPART_CODE,
         SPART_CN_NAME,
         USD_PNP_AMT,
         USD_PNP_AVG,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         APPEND_FLAG,
         APPEND_YEAR,
         BG_CODE,
         BG_CN_NAME,
		 ENABLE_FLAG
  )
  SELECT PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV1_PROD_LIST_CODE,
         LV2_PROD_LIST_CODE,
         LV3_PROD_LIST_CODE,
         LV4_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CN_NAME,
         LV4_PROD_LIST_CN_NAME,
         SPART_CODE,
         SPART_CN_NAME,
         USD_PNP_AMT,
         USD_PNP_AVG,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         APPEND_FLAG,
         APPEND_YEAR,
         BG_CODE,
         BG_CN_NAME,
		 ENABLE_FLAG
	  FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_AVG_T
	  WHERE VERSION_ID = V_VERSION_ID;
	  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将版本号为：'||V_VERSION_ID||'，年均本数据插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 将年均本结果表数据清空
  EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_AVG_T ';   -- 删除涨跌幅临时表为SPART层级的其余层级数据

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '清空：DM_FCST_PRICE_ANNL_AVG_T，版本号为：'||V_VERSION_ID||'的表数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  -- 将无效数据的维度，关联年均本表，修改是否有效标识的标签，将有效改为无效
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_AVG_T(
         VERSION_ID,
		 PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV1_PROD_LIST_CODE,
         LV2_PROD_LIST_CODE,
         LV3_PROD_LIST_CODE,
         LV4_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CN_NAME,
         LV4_PROD_LIST_CN_NAME,
         SPART_CODE,
         SPART_CN_NAME,
         USD_PNP_AMT,
         USD_PNP_AVG,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         APPEND_FLAG,
         APPEND_YEAR,
         BG_CODE,
         BG_CN_NAME,
		 ENABLE_FLAG,
		 CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
  SELECT V_VERSION_ID AS VERSION_ID,
		 T1.PERIOD_YEAR,
         T1.LV0_PROD_LIST_CODE,
         T1.LV1_PROD_LIST_CODE,
         T1.LV2_PROD_LIST_CODE,
         T1.LV3_PROD_LIST_CODE,
         T1.LV4_PROD_LIST_CODE,
         T1.LV0_PROD_LIST_CN_NAME,
         T1.LV1_PROD_LIST_CN_NAME,
         T1.LV2_PROD_LIST_CN_NAME,
         T1.LV3_PROD_LIST_CN_NAME,
         T1.LV4_PROD_LIST_CN_NAME,
         T1.SPART_CODE,
         T1.SPART_CN_NAME,
         T1.USD_PNP_AMT,
         T1.USD_PNP_AVG,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T1.APPEND_FLAG,
         T1.APPEND_YEAR,
         T1.BG_CODE,
         T1.BG_CN_NAME,
		 NVL(T2.ENABLE_FLAG,T1.ENABLE_FLAG) AS ENABLE_FLAG,
		 -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
	  FROM FCST_PRICE_AVG_APPEND_TMP T1
	  LEFT JOIN (SELECT * FROM 
	               FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T
				   WHERE ENABLE_FLAG = 'N'
				   AND VERSION_ID = V_VERSION_ID
				   AND PERIOD_YEAR = V_YEAR) T2
	  ON T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
      AND T1.LV1_PROD_LIST_CODE = T2.LV1_PROD_LIST_CODE
      AND T1.LV2_PROD_LIST_CODE = T2.LV2_PROD_LIST_CODE
      AND T1.LV3_PROD_LIST_CODE = T2.LV3_PROD_LIST_CODE
      AND T1.LV4_PROD_LIST_CODE = T2.LV4_PROD_LIST_CODE
      AND T1.SPART_CODE = T2.GROUP_CODE
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.BG_CODE = T2.BG_CODE
--      AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
      AND NVL(T1.OVERSEA_FLAG,'S1') = NVL(T2.OVERSEA_FLAG,'S1')
      AND NVL(T1.REGION_CODE,'S2') = NVL(T2.REGION_CODE,'S2')
      AND NVL(T1.REPOFFICE_CODE,'S3') = NVL(T2.REPOFFICE_CODE,'S3')
      AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'S4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'S4')
      AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5');

   -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将VERSION_ID= '||V_VERSION_ID ||' 的数据的年均本值，修改为对应无效标识标签',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T';
  DBMS_OUTPUT.PUT_LINE('收集统计信息成功'); 

  -- 日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

