-- Name: f_dm_foi_cate_item_weight_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_cate_item_weight_t(f_caliber_flag character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
最近更新时间: 2024年6月25日16点20分
修改人:	黄心蕊
修改内容: 202407版本 新增华东采购与IAS
创建时间：2024-02-01
创建人  ：黄心蕊 HWX1187045
背景描述：配置页面-品类及ITEM清单权重表初始化
参数描述：参数一(F_CALIBER_FLAG)：'I'为ICT，'E'为数字能源
		  参数二(F_VERSION_ID)：运行版本号
		  参数三(X_SUCCESS_FLAG)：运行状态返回值 ‘1’为成功，‘0’为失败
--数字能源
来源表 ： FIN_DM_OPT_FOI.DM_FOI_ENERGY_BASE_ITEM_AMT_T 数字能源_ITEM基础金额表
		  FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_CATE_INFO_T 数字能源_TOP品类清单
		  FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T 数字能源_版本表
目标表 ： FIN_DM_OPT_FOI.DM_FOI_ENERGY_CATE_ITEM_WEIGHT_T 数字能源_品类及ITEM清单权重表
--采购ICT
来源表 ： FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_REPORT_T 采购ICT_历史数表
		  FIN_DM_OPT_FOI.DM_FOI_TOP_CATE_INFO_T 采购ICT_TOP品类清单
		  FIN_DM_OPT_FOI.DM_FOI_PLAN_VERSION_T 采购ICT_版本表
目标表 ： FIN_DM_OPT_FOI.DM_FOI_CATE_ITEM_WEIGHT_T 采购ICT_品类及ITEM清单权重表
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOI_CATE_ITEM_WEIGHT_T('I',''); --ICT一个版本数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOI_CATE_ITEM_WEIGHT_T('E',''); --数字能源一个版本数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOI_CATE_ITEM_WEIGHT_T('IAS',''); --IAS一个版本数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOI_CATE_ITEM_WEIGHT_T('EAST_CHINA_PQC',''); --华东采购一个版本数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME       VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOI_CATE_ITEM_WEIGHT_T';
  V_VERSION       INT; --版本号
  V_STEP_NUM      INT := 0; --函数步骤号
  V_TO_TABLE      VARCHAR(200); --目标表
  V_FROM_TABLE    VARCHAR(200); --来源表
  V_VERSION_TABLE VARCHAR(200); --版本号取数表
  V_SUM_AMT       NUMERIC; --四年到货总额
  V_SQL           TEXT;
  V_PERIOD_YEAR   VARCHAR(200) := YEAR(NOW()) - 3 || '-' || YEAR(NOW());
  V_FROM_TOP_CATE_TABLE VARCHAR(50);	--TOP品类清单表
  
  --202407版本 新增华东采购与IAS
  V_CALIBER_FLAG  TEXT;
  V_IN_CALIBER    TEXT;
  V_IAS_ECPQC_SQL TEXT; --华东采购IAS表新增CALIBER_FLAG字段
  V_SQL_TYPE_OR_FLAG TEXT;

BEGIN 

X_SUCCESS_FLAG := '1';

--日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  IF F_CALIBER_FLAG = 'I' THEN
    V_FROM_TABLE          := 'FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_REPORT_T'; --历史数表
    V_TO_TABLE            := 'FIN_DM_OPT_FOI.DM_FOI_CATE_ITEM_WEIGHT_T'; --目标表
    V_VERSION_TABLE       := 'FIN_DM_OPT_FOI.DM_FOI_PLAN_VERSION_T';	--版本号来源表
    V_FROM_TOP_CATE_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_TOP_CATE_INFO_T ';	--TOP品类清单，用于ITEM清单权重计算
  ELSIF F_CALIBER_FLAG = 'E' THEN
    V_FROM_TABLE          := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_BASE_ITEM_AMT_T';	
    V_TO_TABLE            := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_CATE_ITEM_WEIGHT_T';
    V_VERSION_TABLE       := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T';
    V_FROM_TOP_CATE_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_CATE_INFO_T ';
	
  ELSE 
	--202407版本 新增华东采购与IAS
     V_FROM_TABLE			:= 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_BASE_ITEM_AMT_T';--来源表1
     V_TO_TABLE				:= 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_CATE_ITEM_WEIGHT_T'; --目标表
	 V_FROM_TOP_CATE_TABLE	:= 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_TOP_CATE_INFO_T ';
	 V_CALIBER_FLAG			:= 'CALIBER_FLAG,';       
	 V_IN_CALIBER			:= '''' || F_CALIBER_FLAG || ''' AS CALIBER_FLAG,';
	 V_IAS_ECPQC_SQL		:= ' AND T1.CALIBER_FLAG = '''||F_CALIBER_FLAG||''' ';
	 
    IF F_CALIBER_FLAG = 'IAS' THEN
      -- IAS
      V_VERSION_TABLE	:= 'FIN_DM_OPT_FOI.DM_FOI_IAS_PLAN_VERSION_T';
    ELSIF F_CALIBER_FLAG = 'EAST_CHINA_PQC' THEN
      -- 华东采购
      V_VERSION_TABLE	:= 'FIN_DM_OPT_FOI.DM_FOI_ECPQC_PLAN_VERSION_T';
    END IF;
	
  END IF;
   
  V_STEP_NUM := V_STEP_NUM + 1;
  --版本号入参判断，当入参为空，取规格品清单最新版本号
  IF F_VERSION_ID IS NOT NULL THEN
    V_VERSION := F_VERSION_ID;
  ELSE
  V_SQL:='
  SELECT VERSION_ID 
    FROM '||V_VERSION_TABLE||'
   WHERE DEL_FLAG = ''N''
     AND STATUS = 1
     AND UPPER(DATA_TYPE) = ''CATEGORY''
     AND UPPER(VERSION_TYPE) IN (''AUTO'', ''FINAL'')
   ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;';
   EXECUTE V_SQL INTO V_VERSION;
  END IF;
  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
  
--删除结果表同版本数据
V_SQL:='DELETE FROM '||V_TO_TABLE||' T1 WHERE T1.VERSION_ID = '||V_VERSION||V_IAS_ECPQC_SQL||';';
EXECUTE V_SQL;														--202407版本 IAS与华东采购新增字段CALIBER_FLAG

  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '删除'||V_VERSION||' 数据成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

--1.基础数据准备，ITEM四年金额分年卷积  
V_STEP_NUM := V_STEP_NUM + 1;
V_SQL:= ' 
DROP TABLE IF EXISTS ITEM_YEAR_AMT_TEMP;
CREATE TEMPORARY TABLE ITEM_YEAR_AMT_TEMP(
	PERIOD_YEAR INT,
	ITEM_CODE	VARCHAR(50),
	ITEM_CN_NAME	VARCHAR(500),
	RMB_RECEIVE_AMT NUMERIC,
	CATEGORY_CODE	VARCHAR(50),
	CATEGORY_NAME  VARCHAR(200),
	L4_CEG_CODE	VARCHAR(50),
	L4_CEG_SHORT_CN_NAME	VARCHAR(200),
	L4_CEG_CN_NAME	VARCHAR(200),
	L3_CEG_CODE VARCHAR(50),
	L3_CEG_SHORT_CN_NAME VARCHAR(200),
	L3_CEG_CN_NAME VARCHAR(200),
	L2_CEG_CODE VARCHAR(50),
	L2_CEG_CN_NAME VARCHAR(200)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(ITEM_CODE,CATEGORY_CODE);

  INSERT INTO ITEM_YEAR_AMT_TEMP
    (PERIOD_YEAR,
     ITEM_CODE,
     ITEM_CN_NAME,
     RMB_RECEIVE_AMT,
     CATEGORY_CODE,
     CATEGORY_NAME,
     L4_CEG_CODE,
     L4_CEG_SHORT_CN_NAME,
     L4_CEG_CN_NAME,
     L3_CEG_CODE,
     L3_CEG_SHORT_CN_NAME,
     L3_CEG_CN_NAME,
     L2_CEG_CODE,
     L2_CEG_CN_NAME)
    SELECT LEFT(PERIOD_ID, 4) AS PERIOD_YEAR,
           ITEM_CODE,
           ITEM_NAME,
           SUM(RECEIVE_AMT_CNY) AS RMB_RECEIVE_AMT,
           CATEGORY_CODE,
           CATEGORY_NAME,
           L4_CEG_CODE,
           L4_CEG_SHORT_CN_NAME,
           L4_CEG_CN_NAME,
           L3_CEG_CODE,
           L3_CEG_SHORT_CN_NAME,
           L3_CEG_CN_NAME,
           L2_CEG_CODE,
           L2_CEG_CN_NAME
      FROM '||V_FROM_TABLE||' T1
     WHERE LEFT(PERIOD_ID, 4) BETWEEN (YEAR(NOW()) - 3) AND ( YEAR(NOW() )  ) --实际逻辑仅需要4年实际数
	   '||V_IAS_ECPQC_SQL||'
     GROUP BY ITEM_CODE,
              ITEM_NAME,
              CATEGORY_CODE,
              CATEGORY_NAME,
              L4_CEG_CODE,
              L4_CEG_SHORT_CN_NAME,
              L4_CEG_CN_NAME,
              L3_CEG_CODE,
              L3_CEG_SHORT_CN_NAME,
              L3_CEG_CN_NAME,
              L2_CEG_CODE,
              L2_CEG_CN_NAME,
              LEFT(PERIOD_ID, 4);';
			  
EXECUTE V_SQL;
			  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '临时表金额插数成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
  

--2.权重计算
----2.1 综合四年权重计算
------2.1.1 分母金额卷积
  V_STEP_NUM := V_STEP_NUM + 1;
  SELECT SUM(RMB_RECEIVE_AMT)
    INTO V_SUM_AMT
    FROM ITEM_YEAR_AMT_TEMP;
   
--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '四年到货额总额（综合四年权重分母）收敛完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
------2.1.2 综合权重计算
  V_STEP_NUM := V_STEP_NUM + 1;
  V_SQL:='
INSERT INTO '||V_TO_TABLE||'
  (VERSION_ID,
   PERIOD_YEAR,
   PERIOD_TYPE,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   WEIGHT_RATE,
   CATEGORY_CODE,
   CATEGORY_NAME,
   L4_CEG_CODE,
   L4_CEG_CN_NAME,
   L4_CEG_SHORT_CN_NAME,
   L3_CEG_CODE,
   L3_CEG_CN_NAME,
   L3_CEG_SHORT_CN_NAME,
   L2_CEG_CODE,
   L2_CEG_CN_NAME,
   '||V_CALIBER_FLAG||'		--202407版本 华东采购IAS表新增CALIBER_FLAG字段
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   TOP_TYPE)
--ITEM四年综合权重计算 计算TOP品类下所有ITEN权重
  SELECT '||V_VERSION||' AS VERSION_ID,
         '''||V_PERIOD_YEAR||''' AS PERIOD_YEAR,
         ''S'' AS PERIOD_TYPE,
         T1.ITEM_CODE AS GROUP_CODE,
         T1.ITEM_CN_NAME AS GROUP_CN_NAME,
         ''ITEM'' AS GROUP_LEVEL,
         SUM(T1.RMB_RECEIVE_AMT) / NULLIF(SUM(SUM(T1.RMB_RECEIVE_AMT))OVER(PARTITION BY T1.CATEGORY_CODE), 0) AS WEIGHT_RATE,
         T1.CATEGORY_CODE,
         T1.CATEGORY_NAME,
         T1.L4_CEG_CODE,
         T1.L4_CEG_CN_NAME,
         T1.L4_CEG_SHORT_CN_NAME,
         T1.L3_CEG_CODE,
         T1.L3_CEG_CN_NAME,
         T1.L3_CEG_SHORT_CN_NAME,
         T1.L2_CEG_CODE,
         T1.L2_CEG_CN_NAME,	
		 '||V_IN_CALIBER||'	--202407版本 华东采购IAS表新增CALIBER_FLAG字段
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
		 NULL AS TOP_TYPE
    FROM ITEM_YEAR_AMT_TEMP T1
	INNER JOIN (--关联最新TOP品类清单，内关联出TOP品类
				SELECT DISTINCT CATEGORY_CODE
				 FROM '||V_FROM_TOP_CATE_TABLE||' T1
				WHERE T1.VERSION_ID = '||V_VERSION||'
				'||V_IAS_ECPQC_SQL||'	--202407版本 华东采购IAS表新增CALIBER_FLAG字段
				) T2
		ON T1.CATEGORY_CODE = T2.CATEGORY_CODE
   GROUP BY T1.CATEGORY_CODE,
            T1.CATEGORY_NAME,
			T1.ITEM_CODE,
			T1.ITEM_CN_NAME,
            T1.L4_CEG_CODE,
            T1.L4_CEG_CN_NAME,
            T1.L4_CEG_SHORT_CN_NAME,
            T1.L3_CEG_CODE,
            T1.L3_CEG_CN_NAME,
            T1.L3_CEG_SHORT_CN_NAME,
            T1.L2_CEG_CODE,
            T1.L2_CEG_CN_NAME
UNION ALL
--品类四年综合权重计算 计算所有品类权重
  SELECT '||V_VERSION||' AS VERSION_ID,
         '''||V_PERIOD_YEAR||''' AS PERIOD_YEAR,
         ''S'' AS PERIOD_TYPE,
         T1.CATEGORY_CODE AS GROUP_CODE,
         T1.CATEGORY_NAME AS GROUP_CN_NAME,
         ''CATEGORY'' AS GROUP_LEVEL,
         T1.WEIGHT_RATE,
         T1.CATEGORY_CODE,
         T1.CATEGORY_NAME,
         T1.L4_CEG_CODE,
         T1.L4_CEG_CN_NAME,
         T1.L4_CEG_SHORT_CN_NAME,
         T1.L3_CEG_CODE,
         T1.L3_CEG_CN_NAME,
         T1.L3_CEG_SHORT_CN_NAME,
         T1.L2_CEG_CODE,
         T1.L2_CEG_CN_NAME,
		 '||V_IN_CALIBER||'	--202407版本 华东采购IAS表新增CALIBER_FLAG字段
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         CASE
           WHEN T1.TOP_TYPE_NUMBER <= 50 THEN ''TOP50''
           WHEN T1.TOP_TYPE_NUMBER <= 100 THEN ''TOP100''
           WHEN T1.TOP_TYPE_NUMBER <= 300 THEN ''TOP300''
           ELSE ''''
         END AS TOP_TYPE			----对金额占前300的品类打TOP类别标签
    FROM (SELECT SUM(RMB_RECEIVE_AMT) / NULLIF('||V_SUM_AMT||', 0) AS WEIGHT_RATE,
                 ROW_NUMBER() OVER(ORDER BY SUM(RMB_RECEIVE_AMT) DESC) AS TOP_TYPE_NUMBER,
                 CATEGORY_CODE,
                 CATEGORY_NAME,
                 L4_CEG_CODE,
                 L4_CEG_CN_NAME,
                 L4_CEG_SHORT_CN_NAME,
                 L3_CEG_CODE,
                 L3_CEG_CN_NAME,
                 L3_CEG_SHORT_CN_NAME,
                 L2_CEG_CODE,
                 L2_CEG_CN_NAME
            FROM ITEM_YEAR_AMT_TEMP
           GROUP BY CATEGORY_CODE,
                    CATEGORY_NAME,
                    L4_CEG_CODE,
                    L4_CEG_CN_NAME,
                    L4_CEG_SHORT_CN_NAME,
                    L3_CEG_CODE,
                    L3_CEG_CN_NAME,
                    L3_CEG_SHORT_CN_NAME,
                    L2_CEG_CODE,
                    L2_CEG_CN_NAME) T1
			;';
			
--DBMS_OUTPUT.PUT_LINE(V_SQL);			
EXECUTE V_SQL;

--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '四年综合权重计算完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
----2.2 四年单年权重计算

  V_STEP_NUM := V_STEP_NUM + 1;
  V_SQL:='
INSERT INTO '||V_TO_TABLE||'
  (VERSION_ID,
   PERIOD_YEAR,
   PERIOD_TYPE,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   WEIGHT_RATE,
   CATEGORY_CODE,
   CATEGORY_NAME,
   L4_CEG_CODE,
   L4_CEG_CN_NAME,
   L4_CEG_SHORT_CN_NAME,
   L3_CEG_CODE,
   L3_CEG_CN_NAME,
   L3_CEG_SHORT_CN_NAME,
   L2_CEG_CODE,
   L2_CEG_CN_NAME,
   '||V_CALIBER_FLAG||'	--202407版本 华东采购IAS表新增CALIBER_FLAG字段
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG)
--ITEM四年单年权重计算 计算TOP品类下所有ITEN权重
  SELECT '||V_VERSION||' AS VERSION_ID,
         T1.PERIOD_YEAR,
		 ''U'' AS PERIOD_TYPE,
         T1.ITEM_CODE    AS GROUP_CODE,
         T1.ITEM_CN_NAME AS GROUP_CN_NAME,
         ''ITEM'' AS GROUP_LEVEL,
         SUM(T1.RMB_RECEIVE_AMT) /
         NULLIF(SUM(SUM(T1.RMB_RECEIVE_AMT)) OVER(PARTITION BY T1.PERIOD_YEAR,T1.CATEGORY_CODE),0) AS WEIGHT_RATE,
         T1.CATEGORY_CODE,
         T1.CATEGORY_NAME,
         T1.L4_CEG_CODE,
         T1.L4_CEG_CN_NAME,
         T1.L4_CEG_SHORT_CN_NAME,
         T1.L3_CEG_CODE,
         T1.L3_CEG_CN_NAME,
         T1.L3_CEG_SHORT_CN_NAME,
         T1.L2_CEG_CODE,
         T1.L2_CEG_CN_NAME,
		 '||V_IN_CALIBER||'	--202407版本 华东采购IAS表新增CALIBER_FLAG字段
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
		 ''N'' AS DEL_FLAG
    FROM ITEM_YEAR_AMT_TEMP T1
	INNER JOIN (SELECT DISTINCT CATEGORY_CODE
				 FROM '||V_FROM_TOP_CATE_TABLE||' T1
				WHERE T1.VERSION_ID = '||V_VERSION||'
				'||V_IAS_ECPQC_SQL||' 	--202407版本 华东采购IAS表新增CALIBER_FLAG字段
				) T2
		ON T1.CATEGORY_CODE = T2.CATEGORY_CODE
   GROUP BY T1.CATEGORY_CODE,
            T1.CATEGORY_NAME,
            T1.PERIOD_YEAR,
            T1.ITEM_CODE,
            T1.ITEM_CN_NAME,
            T1.L4_CEG_CODE,
            T1.L4_CEG_CN_NAME,
            T1.L4_CEG_SHORT_CN_NAME,
            T1.L3_CEG_CODE,
            T1.L3_CEG_CN_NAME,
            T1.L3_CEG_SHORT_CN_NAME,
            T1.L2_CEG_CODE,
            T1.L2_CEG_CN_NAME
UNION ALL
--品类四年单年权重计算 所有品类
  SELECT '||V_VERSION||' AS VERSION_ID,
         PERIOD_YEAR,
		 ''U'' AS PERIOD_TYPE,
         CATEGORY_CODE AS GROUP_CODE,
         CATEGORY_NAME AS GROUP_CN_NAME,
         ''CATEGORY'' AS GROUP_LEVEL,
         SUM(RMB_RECEIVE_AMT) /
         NULLIF(SUM(SUM(RMB_RECEIVE_AMT)) OVER(PARTITION BY PERIOD_YEAR),0) AS WEIGHT_RATE,
         CATEGORY_CODE,
         CATEGORY_NAME,
         L4_CEG_CODE,
         L4_CEG_CN_NAME,
         L4_CEG_SHORT_CN_NAME,
         L3_CEG_CODE,
         L3_CEG_CN_NAME,
         L3_CEG_SHORT_CN_NAME,
         L2_CEG_CODE,
         L2_CEG_CN_NAME,
		 '||V_IN_CALIBER||'	--202407版本 华东采购IAS表新增CALIBER_FLAG字段
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
		 ''N'' AS DEL_FLAG
    FROM ITEM_YEAR_AMT_TEMP
   GROUP BY CATEGORY_CODE,
            CATEGORY_NAME,
            PERIOD_YEAR,
            L4_CEG_CODE,
            L4_CEG_CN_NAME,
            L4_CEG_SHORT_CN_NAME,
            L3_CEG_CODE,
            L3_CEG_CN_NAME,
            L3_CEG_SHORT_CN_NAME,
            L2_CEG_CODE,
            L2_CEG_CN_NAME;';
			
EXECUTE V_SQL;

--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '四年单年权重计算完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 

V_SQL:='ANALYZE '||V_TO_TABLE||';';
EXECUTE V_SQL;

RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

