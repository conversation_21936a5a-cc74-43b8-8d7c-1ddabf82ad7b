-- Name: f_dm_fol_actual_perform_alloc_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fol_actual_perform_alloc_info_t(p_version_id integer DEFAULT NULL::integer, p_refresh_type character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：2024-02-19
创建人  ：qwx1110218
背景描述：实际履行份额表的加工规则：1、供应商份额的层级：柜型-精品海运-区域-航线（先分柜型）
                                    2、按月展示每家供应商的累计份额占比
                                    3、份额计算：（如航线下供应商份额）
                                       3.1)按月汇总-柜型-精品海运-区域-航线各供应商的量总额，并累计当年YTD总额为A，如202005，则卷积202001-202005的总额
                                       3.2)按月汇总-柜型-精品海运-区域-航线量所有供应商的量总额，并累计当年YTD总额为B，如202005，则卷积202001-202005的总额
                                       3.3)A/B即为对应供应商的在此航线当年累计份额占比
          p_version_id 逻辑：1、自动调度，取版本表最大版本ID+1；2、刷新（页面的刷新价格表、刷新系统）：取java传版本ID；
          p_refresh_type 逻辑：1、自动调度，直接赋值为 4_AUTO；2、刷新（页面的刷新价格表、刷新系统）：取java传的值（1_刷新价格表 或 2_刷新系统）
参数描述：参数(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_fol_actual_perform_alloc_info_t()
变更记录：2024-6-4 qwx1110218 新增非Top航线的逻辑、新增ALL柜型的逻辑
          2024-8-21 qwx1110218 来源表变更：由 dm_fol_route_info_sum_t （价格补录表内关联航线量集成表）变更为 dm_fol_route_price_info_sum_t （价格补录表左关联航线量集成表），
                                           且需要剔除柜型量为空的数据（即 container_qty is not null）

*/


declare
	v_sp_name varchar(200) := 'fin_dm_opt_foi.f_dm_fol_actual_perform_alloc_info_t('||p_version_id||','''||p_refresh_type||''')';
	v_tbl_name varchar(200) := 'fin_dm_opt_foi.dm_fol_actual_perform_alloc_info_t';
	v_dml_row_count  int default 0 ;
	v_max_version_id int;
	v_route_version_code varchar(30);
	v_price_version_code  varchar(30);
	v_max_last_update_date timestamp;
	v_version_status      varchar(50);


begin

  set enable_force_vector_engine to on;
	x_success_flag := '1';        --1表示成功

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '实际履行份额表 '||v_tbl_name||'：开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
  ) ;
  
  -- 版本信息表中执行失败的版本ID，目标表中需要清理
  with version_info_2001_tmp as(
  select distinct nvl(version_id,0) as version_id
    from fin_dm_opt_foi.dm_fol_version_info_t
   where step = 2001   -- 执行失败
  )
  delete from fin_dm_opt_foi.dm_fol_actual_perform_alloc_info_t where upper(del_flag) = 'N' and nvl(version_id,0) in(select nvl(version_id,0) from version_info_2001_tmp)
  ;

  -- 从航线量汇总表取最大版本ID的数据
  select max(version_id) as max_version_id into v_max_version_id from fin_dm_opt_foi.dm_fol_route_price_info_sum_t where upper(del_flag) = 'N';
  
  if((p_version_id is null or p_version_id = '') and (p_refresh_type is null or p_refresh_type = '')) then  
    -- 从版本表获取航线量汇总表版本ID对应的航线信息表的版本编码
    select max(version_code) as version_code into v_route_version_code
      from fin_dm_opt_foi.dm_fol_version_info_t t1
     where version_id = v_max_version_id
       and source_en_name = 'f_dm_fol_route_info_t'
       and not exists(select distinct version_id 
                        from fin_dm_opt_foi.dm_fol_version_info_t t2 
                       where t1.version_id = t2.version_id 
                         and t2.step in(2001,0,2)
                         and upper(t2.del_flag) = 'N'
                     )
       and upper(t1.del_flag) = 'N'
    ;
    
    -- 清理已经写入版本信息表的数据
    delete from fin_dm_opt_foi.dm_fol_version_info_t 
     where version_id = v_max_version_id
       and version_code = v_route_version_code
       and source_en_name = 'f_dm_fol_actual_perform_alloc_info_t'
       and refresh_type = '4_AUTO'
       and step = 1
       and upper(del_flag) = 'N'
    ; 
    
    -- 版本信息表初始化“执行中”的数据
    insert into fin_dm_opt_foi.dm_fol_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select v_max_version_id   as version_id
         , v_route_version_code as version_code
         , 2 as step  -- 执行中
         , 'f_dm_fol_actual_perform_alloc_info_t' as source_en_name
         , '实际履行份额函数'           as source_cn_name
         , '4_AUTO' as refresh_type
         , 'version_code 是航线清单表的' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
    ;
    
    v_dml_row_count := sql%rowcount;	-- 收集数据量
  
    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '版本ID：'||v_max_version_id||'，刷新类型：4_AUTO，航线清单表的版本编码：'||v_route_version_code||'，写入到版本信息表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
  
  elseif((p_version_id is not null or p_version_id <> '') and (p_refresh_type = '1_刷新价格表')) then  
    -- 从版本表获取航线量汇总表版本ID对应的物流航线价格补录表的版本编码
    select max(version_code) as version_code into v_price_version_code
      from fin_dm_opt_foi.dm_fol_version_info_t t1
     where version_id = p_version_id
       and source_en_name is null
       and refresh_type = '1_刷新价格表'
       and upper(del_flag) = 'N'
       --and step = 2  -- 价格表刷新时，java会更新“价格补录表”的step=2，所有表数据刷新完成后，才更新“价格补录表”的step=1
    ;
    
    -- 从版本表获取航线量汇总表版本ID对应的航线信息表的版本编码
    select max(version_code) as version_code into v_route_version_code
      from fin_dm_opt_foi.dm_fol_version_info_t t1
     where version_id < p_version_id
       and source_en_name = 'f_dm_fol_route_info_t'
       and not exists(select distinct version_id
                        from dm_fol_version_info_t t2
                       where t1.version_id = t2.version_id
                         and t2.step in(2001,0,2)
                         and upper(t2.del_flag) = 'N'
                     )
       and upper(t1.del_flag) = 'N'
    ;
    
    -- 清理已经写入版本信息表的数据
    delete from fin_dm_opt_foi.dm_fol_version_info_t 
     where version_id = p_version_id
       and source_en_name = 'f_dm_fol_actual_perform_alloc_info_t'
       and refresh_type = p_refresh_type
       and step = 1
       and upper(del_flag) = 'N'
    ;
    
    -- 成功数据写入到版本信息表
    insert into fin_dm_opt_foi.dm_fol_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select p_version_id   as version_id
         , v_price_version_code as version_code
         , 2 as step
         , 'f_dm_fol_actual_perform_alloc_info_t' as source_en_name
         , '实际履行份额表'           as source_cn_name
         , p_refresh_type as refresh_type
         , 'version_code 是物流航线价格补录表的版本编码' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
    ;
    
    v_dml_row_count := sql%rowcount;	-- 收集数据量
  
    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入版本ID：'||p_version_id||'，传入刷新类型：'||p_refresh_type||'，航线清单表的版本编码：'||v_route_version_code||'，价格补录表的版本编码：'||v_price_version_code||'，写入到版本信息表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
  
  elseif((p_version_id is not null or p_version_id <> '') and p_refresh_type = '2_刷新系统') then
    -- 从版本表获取航线量汇总表版本ID对应的物流航线价格补录表的版本编码
    select max(version_code) as version_code into v_price_version_code
      from fin_dm_opt_foi.dm_fol_version_info_t t1
     where version_id < p_version_id
       and source_en_name = 'apd_fol_route_price_info_t'
       and not exists(select distinct version_id
                        from dm_fol_version_info_t t2
                       where t1.version_id = t2.version_id
                         and t2.step in(2001,0,2)
                         and upper(t2.del_flag) = 'N'
                     )
      and upper(t1.del_flag) = 'N'
    ;
  
    -- 从版本表获取航线量汇总表版本ID对应的航线信息表的版本编码
    select max(version_code) as version_code into v_route_version_code
      from fin_dm_opt_foi.dm_fol_version_info_t t1
     where version_id = p_version_id
       and source_en_name is null
       and refresh_type = '2_刷新系统'
       and upper(del_flag) = 'N'
       --and step = 2  -- 系统刷新时，java会更新“航线清单表”的step=2，所有表数据刷新完成后，才更新“航线清单表”的step=1
    ;
    
    -- 清理已经写入版本信息表的数据
    delete from fin_dm_opt_foi.dm_fol_version_info_t 
     where version_id = p_version_id
       and version_code = v_route_version_code
       and source_en_name = 'f_dm_fol_actual_perform_alloc_info_t'
       and refresh_type = p_refresh_type
       and step = 1
       and upper(del_flag) = 'N'
    ; 
    
    -- 版本信息表初始化“执行中”的数据
    insert into fin_dm_opt_foi.dm_fol_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select nvl(p_version_id,v_max_version_id)   as version_id
         , v_route_version_code as version_code
         , 2 as step  -- 执行中
         , 'f_dm_fol_actual_perform_alloc_info_t' as source_en_name
         , '实际履行份额函数'           as source_cn_name
         , nvl(p_refresh_type,'4_AUTO') as refresh_type
         , 'version_code 是航线清单表的' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
    ;
    
    v_dml_row_count := sql%rowcount;	-- 收集数据量
  
    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入版本ID：'||p_version_id||'，传入刷新类型：'||p_refresh_type||'，航线清单表的版本编码：'||v_route_version_code||'，状态：'||v_version_status||'，写入到版本信息表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
    
  end if;
  
  -- 如果获取的航线信息表的版本编码不为空值
  if(v_route_version_code is  null or v_route_version_code = '') then
    
    -- 开始记录日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入版本ID：'||p_version_id||'，传入刷新类型：'||p_refresh_type||'，获取的航线信息表的版本编码为空值： '||v_route_version_code||'，需要排查版本信息表的最大版本ID是否有2001（执行失败）、2（执行中）、0（草稿（java））',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
    x_success_flag := 2001;
    return;
    
  else
    -- 根据版本ID获取的版本编码，取最大更新时间的数据
    -- 取Top航线、非Top航线的数据
    select max(last_update_date) as max_last_update_date into v_max_last_update_date
      from fin_dm_opt_foi.dm_fol_route_info_t
     where version_code = v_route_version_code
       -- and top_route_flag = 'Y'  -- 只取是Top航线的
       and upper(version_status) <> 'ADJUST'  -- 排除ADJUST版本 
       and upper(del_flag) = 'N'
    ;
    
    -- 根据版本版本+最后更新时间获取对应的状态
    select distinct version_status into v_version_status
      from fin_dm_opt_foi.dm_fol_route_info_t
     where version_code = v_route_version_code
       -- and top_route_flag = 'Y'  -- 只取是Top航线的
       and upper(del_flag) = 'N'
       and last_update_date = v_max_last_update_date
    ;
    
    -- 清理数据
    delete from fin_dm_opt_foi.dm_fol_actual_perform_alloc_info_t where version_id = nvl(p_version_id,v_max_version_id) and upper(del_flag) = 'N';
  
    -- 数据入到目标表
    insert into fin_dm_opt_foi.dm_fol_actual_perform_alloc_info_t(
           version_id              -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                    -- 年份
         , period_id               -- 会计期
         , target_type             -- 目标时点类型（M、YTD、H1、H2等）
         , transport_mode          -- 运输方式（精品海运）
         , region_cn_name          -- 区域
         , route                   -- 航线（起始港_目的港）
         , source_country_name     -- 起始国家
         , dest_country_name       -- 目的国家
         , supplier_short_name     -- LST（即供应商）
         , level_code              -- 层级编码（01、02、03、04）
         , level_desc              -- 层级描述（01 运输方式、02 区域、03 航线、04供应商）
         , container_type          -- 柜型（20GP、40GP、40HQ）
         , percent                 -- 份额占比
         , remark                  -- 备注
         , created_by              -- 创建人
         , creation_date           -- 创建时间
         , last_updated_by         -- 修改人
         , last_update_date        -- 修改时间
         , del_flag                -- 是否删除
    )
    with route_info_sum_tmp as(
    -- 取航线量汇总表的最大版本ID数据
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , route	                  -- 航线（目的港_国家）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , supplier_short_name      -- LST（即供应商）
         , container_type           -- 柜型（20GP、40GP、40HQ）
         , sum(container_qty) as container_qty  -- 柜型量
      from fin_dm_opt_foi.dm_fol_route_price_info_sum_t
     where version_id = nvl(p_version_id,v_max_version_id)
       and transport_mode = '精品海运'
       and upper(del_flag) = 'N'
       and container_qty is not null
     group by version_id
         , year
         , period_id
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , supplier_short_name
         , container_type
    ),
    route_info_sum_tmp1 as(
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , route	                  -- 航线（目的港_国家）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , supplier_short_name      -- LST（即供应商）
         , sum(container_qty) as container_qty  -- 柜型量
      from route_info_sum_tmp
     group by version_id
         , year
         , period_id
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , supplier_short_name
    ),
    -- 取最大会计期，作为补数的截止日期
    max_period_id_tmp as(
    select max(period_id) as max_period_id
      from route_info_sum_tmp
    ),
    -- 航线、供应商层级的汇总
    act_perform_alloc_info_tmp1 as(
    select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                     -- 年份
         , t1.period_id                -- 会计期
         , t1.transport_mode           -- 运输方式（精品海运、Xeneta）
         , t1.region_cn_name           -- 目的地区域
         , t1.route	                   -- 航线（目的港_国家）
         , t1.source_country_name      -- 起运地国家
         , t1.dest_country_name        -- 目的地国家
         , t1.supplier_short_name      -- LST（即供应商）
         , t1.container_type           -- 柜型（20GP、40GP、40HQ）
         , sum(t1.container_qty) over(partition by t1.version_id,t1.year,t1.transport_mode,t1.region_cn_name,t1.route,
                                                   t1.source_country_name,t1.dest_country_name,t1.supplier_short_name,t1.container_type
                                          order by t1.period_id
                                     ) as container_qty -- 柜型量
      from route_info_sum_tmp t1
     union all
    select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                     -- 年份
         , t1.period_id                -- 会计期
         , t1.transport_mode           -- 运输方式（精品海运、Xeneta）
         , t1.region_cn_name           -- 目的地区域
         , t1.route	                   -- 航线（目的港_国家）
         , t1.source_country_name      -- 起运地国家
         , t1.dest_country_name        -- 目的地国家
         , t1.supplier_short_name      -- LST（即供应商）
         , 'ALL' as container_type     -- 柜型（20GP、40GP、40HQ、ALL）
         , sum(t1.container_qty) over(partition by t1.version_id,t1.year,t1.transport_mode,t1.region_cn_name,t1.route,
                                                   t1.source_country_name,t1.dest_country_name,t1.supplier_short_name
                                          order by t1.period_id
                                     ) as container_qty -- 柜型量
      from route_info_sum_tmp1 t1
    ),
    -- 补齐缺少的年累计数据，用于计算
    make_up_data_tmp1 as(
    select distinct t2.version_id
         , t2.year
         , t2.transport_mode
         , t2.region_cn_name
         , t2.route
         , t2.source_country_name
         , t2.dest_country_name
         , t2.supplier_short_name
         , t2.container_type
         , (t2.year||lpad(t1.month,2,'0'))::int as apd_period_id
      from act_perform_alloc_info_tmp1 t2
      left join (select unnest(array[1,2,3,4,5,6,7,8,9,10,11,12]) as month) t1
        on 1=1
    ),
    make_up_data_tmp2 as(
    select t1.version_id
         , t1.year
         , t1.transport_mode
         , t1.region_cn_name
         , t1.route
         , t1.source_country_name
         , t1.dest_country_name
         , t1.supplier_short_name
         , t1.container_type
         , t1.apd_period_id
         , t2.period_id
         , t2.container_qty
         , count(t2.period_id) over(partition by t1.year, t1.route, t1.supplier_short_name, t1.container_type order by t1.apd_period_id) as cn
      from make_up_data_tmp1 t1
      left join act_perform_alloc_info_tmp1 t2
        on t1.version_id = t2.version_id
       and t1.year = t2.year
       and t1.transport_mode = t2.transport_mode
       and t1.region_cn_name = t2.region_cn_name
       and t1.route = t2.route
       and t1.transport_mode = t2.transport_mode
       and t1.region_cn_name = t2.region_cn_name
       and t1.supplier_short_name = t2.supplier_short_name
       and t1.container_type = t2.container_type
       and t1.apd_period_id = t2.period_id
    ),
    make_up_data_tmp3 as(
    select version_id
         , year
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , supplier_short_name
         , container_type
         , apd_period_id as period_id
         , max(container_qty) over(partition by year, transport_mode, region_cn_name, route, supplier_short_name, container_type, cn) as container_qty
      from make_up_data_tmp2
     where cn <> 0
       and apd_period_id <= (select max_period_id from max_period_id_tmp)
    ),
    -- 区域、供应商层级的汇总
    act_perform_alloc_info_tmp5 as(
    select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                     -- 年份
         , t1.period_id -- 会计期
         , t1.transport_mode           -- 运输方式（精品海运、Xeneta）
         , t1.region_cn_name           -- 目的地区域
         , t1.supplier_short_name      -- LST（即供应商）
         , t1.container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , sum(t1.container_qty) as container_qty -- 柜型量
      from make_up_data_tmp3 t1
     group by t1.version_id           
         , t1.year                    
         , t1.period_id               
         , t1.transport_mode          
         , t1.region_cn_name          
         , t1.supplier_short_name     
         , t1.container_type
    ),
    -- 运输方式、供应商层级的汇总
    act_perform_alloc_info_tmp6 as(
    select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                     -- 年份
         , t1.period_id                -- 会计期
         , t1.transport_mode           -- 运输方式（精品海运、Xeneta）
         , t1.supplier_short_name      -- LST（即供应商）
         , t1.container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , sum(t1.container_qty) as container_qty -- 柜型量
      from make_up_data_tmp3 t1
     group by t1.version_id          
         , t1.year                   
         , t1.period_id              
         , t1.transport_mode         
         , t1.supplier_short_name    
         , t1.container_type
    ),
    -- 航线层级的汇总
    act_perform_alloc_info_tmp2 as(
    select version_id      -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , route	                  -- 航线（起始港_目的港）
         , container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , sum(container_qty) as container_qty -- 柜型量
      from make_up_data_tmp3
     group by version_id     
         , year              
         , period_id         
         , transport_mode    
         , region_cn_name    
         , route	           
         , container_type
    ),
    -- 区域层级的汇总
    act_perform_alloc_info_tmp3 as(
    select version_id      -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , sum(container_qty) as container_qty -- 柜型量
      from make_up_data_tmp3
     group by version_id     
         , year              
         , period_id         
         , transport_mode    
         , region_cn_name    
         , container_type
    ),
    -- 运输方式层级的汇总
    act_perform_alloc_info_tmp4 as(
    select version_id      -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , sum(container_qty) as container_qty -- 柜型量
      from make_up_data_tmp3
     group by version_id     
         , year              
         , period_id         
         , transport_mode    
         , container_type
    )
    select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                     -- 年份
         , t1.period_id                -- 会计期
         , 'YTD' as target_type        -- 目标时点类型（M、YTD、H1、H2等）
         , t1.transport_mode           -- 运输方式（精品海运、Xeneta）
         , t1.region_cn_name           -- 目的地区域
         , t1.route                    -- 航线（起始港_目的港）
         , t1.source_country_name      -- 起运地国家
         , t1.dest_country_name        -- 目的地国家
         , t1.supplier_short_name      -- LST（即供应商）
         , '03'   as level_code        -- 层级编码（01、02、03、04）
         , '航线' as level_desc        -- 层级描述（01 运输方式、02 区域、03 航线、04 供应商）
         , t1.container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , round((case when t1.container_qty = 0 then 0 when t2.container_qty = 0 then 0 else t1.container_qty/t2.container_qty end),10) as percent -- 份额占比
         , '' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
      from make_up_data_tmp3 t1
      left join act_perform_alloc_info_tmp2 t2
        on t1.version_id     = t2.version_id
       and t1.year           = t2.year
       and t1.period_id      = t2.period_id
       and t1.route          = t2.route
       and t1.container_type = t2.container_type
     union all
    select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                     -- 年份
         , t1.period_id                -- 会计期
         , 'YTD' as target_type        -- 目标时点类型（M、YTD、H1、H2等）
         , t1.transport_mode           -- 运输方式（精品海运、Xeneta）
         , t1.region_cn_name           -- 目的地区域
         , '' as route                    -- 航线（起始港_目的港）
         , '' as source_country_name      -- 起运地国家
         , '' as dest_country_name        -- 目的地国家
         , t1.supplier_short_name      -- LST（即供应商）
         , '02'   as level_code        -- 层级编码（01、02、03、04）
         , '区域' as level_desc        -- 层级描述（01 运输方式、02 区域、03 航线、04 供应商）
         , t1.container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , round((case when t1.container_qty = 0 then 0 when t2.container_qty = 0 then 0 else t1.container_qty/t2.container_qty end),10) as percent -- 份额占比
         , '' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
      from act_perform_alloc_info_tmp5 t1
      left join act_perform_alloc_info_tmp3 t2
        on t1.version_id     = t2.version_id
       and t1.year           = t2.year
       and t1.period_id      = t2.period_id
       and t1.region_cn_name = t2.region_cn_name
       and t1.container_type = t2.container_type
     union all
    select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                     -- 年份
         , t1.period_id                -- 会计期
         , 'YTD' as target_type        -- 目标时点类型（M、YTD、H1、H2等）
         , t1.transport_mode           -- 运输方式（精品海运、Xeneta）
         , '' as region_cn_name           -- 目的地区域
         , '' as route                    -- 航线（起始港_目的港）
         , '' as source_country_name      -- 起运地国家
         , '' as dest_country_name        -- 目的地国家
         , t1.supplier_short_name      -- LST（即供应商）
         , '01'       as level_code    -- 层级编码（01、02、03、04）
         , '运输方式' as level_desc    -- 层级描述（01 运输方式、02 区域、03 航线、04 供应商）
         , t1.container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , round((case when t1.container_qty = 0 then 0 when t2.container_qty = 0 then 0 else t1.container_qty/t2.container_qty end),10) as percent -- 份额占比
         , '' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
      from act_perform_alloc_info_tmp6 t1
      left join act_perform_alloc_info_tmp4 t2
        on t1.version_id     = t2.version_id
       and t1.year           = t2.year
       and t1.period_id      = t2.period_id
       and t1.transport_mode = t2.transport_mode
       and t1.container_type = t2.container_type
    ;
  
  
    v_dml_row_count := sql%rowcount;	-- 收集数据量
  
    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '数据入到目标表，数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
    
    -- 成功数据写入到版本信息表，需要更新对应的“执行中”数据状态
    update fin_dm_opt_foi.dm_fol_version_info_t set step = 1 
     where version_id = nvl(p_version_id,v_max_version_id) 
       --and version_code = v_route_version_code
       and source_en_name = 'f_dm_fol_actual_perform_alloc_info_t'
       and refresh_type = nvl(p_refresh_type,'4_AUTO')
       and upper(del_flag) = 'N'
    ;  
    
    v_dml_row_count := sql%rowcount;	-- 收集数据量
  
    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 4,
        p_log_cal_log_desc => '版本信息表中的step已更新为完成，运行结束！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;

  end if;

  exception
  	when others then

      perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		    p_log_row_count => null,
		    p_log_errbuf => sqlstate  --错误编码
      ) ;
	x_success_flag := '2001';	         --2001表示失败

  -- 失败信息写入到版本信息表
  insert into fin_dm_opt_foi.dm_fol_version_info_t(
         version_id           -- 版本ID（自动生成）
       , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
       , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
       , source_en_name       -- 来源英文描述（可以是表名、函数名等）
       , source_cn_name       -- 来源中文描述
       , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
       , remark               -- 备注
       , created_by           -- 创建人
       , creation_date        -- 创建时间
       , last_updated_by      -- 修改人
       , last_update_date     -- 修改时间
       , del_flag             -- 是否删除
  )
  select nvl(p_version_id,v_max_version_id) as version_id
       , v_route_version_code as version_code
       , 2001 as step
       , 'f_dm_fol_actual_perform_alloc_info_t' as source_en_name
       , '实际履行份额函数'           as source_cn_name
       , nvl(p_refresh_type,'4_AUTO') as refresh_type
       , (case when p_refresh_type = '1_刷新价格表' then 'version_code 是物流航线价格补录表的版本编码'
               else 'version_code 是航线清单表的'
          end) as remark
	     , -1 as created_by
	     , current_timestamp as creation_date
	     , -1 as last_updated_by
	     , current_timestamp as last_update_date
	     , 'N' as del_flag
    ;

  --收集统计信息
  analyse fin_dm_opt_foi.dm_fol_actual_perform_alloc_info_t;
  analyse fin_dm_opt_foi.dm_fol_version_info_t;

end;
$$
/

