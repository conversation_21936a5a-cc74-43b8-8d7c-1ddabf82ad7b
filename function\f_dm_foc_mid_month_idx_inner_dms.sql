-- Name: f_dm_foc_mid_month_idx_inner_dms; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_mid_month_idx_inner_dms(f_caliber_flag character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$



/***************************************************************************************************************************************************************
最后更新时间：2024年1月26日09点44分
创建时间：2023-09-25 
创建人  ：黄心蕊 HWX1187045
修改时间：2023-12-19
创建人  ：黄心蕊 HWX1187045
修改内容：202401版本新增SPART层级
背景描述：月度分析-规格品指数插中间表
参数描述：参数一(f_caliber_flag)：绝密数据解密密钥串
		  参数三(X_SUCCESS_FLAG)：运行状态返回值 ‘1’为成功，‘0’为失败
事例    ：SELECT FIN_DM_OPT_FOI.f_dm_foc_mid_month_idx_dms_inner('C'); 
          SELECT FIN_DM_OPT_FOI.f_dm_foc_mid_month_idx_dms_inner('R');
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME        VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_MID_MONTH_IDX_INNER_DMS';
  V_VERSION        BIGINT; --版本号
  V_BASE_PERIOD_ID INT := TO_NUMBER((YEAR(CURRENT_DATE) - 1)||'01'); --基期会计期
  V_SOURCE_TABLE   VARCHAR(200);
  V_STEP_NUM       INT := 0; --函数步骤号
  V_CALIBER_FLAG   TEXT := F_CALIBER_FLAG;
  V_SQL            TEXT;
BEGIN

 V_STEP_NUM:=V_STEP_NUM+1;  
  --版本号入参判断，当入参为空，取TOP规格品清单最新版本号
  IF F_ITEM_VERSION IS NULL THEN
  SELECT VERSION_ID INTO V_VERSION
    FROM FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_ITEM_INFO_T  -- 防止先跑量纲颗粒度，导致通用表没有最新版本号
   ORDER BY LAST_UPDATE_DATE DESC
   LIMIT 1;
  --入参不为空，则以入参为版本号
  ELSE 
    V_VERSION := F_ITEM_VERSION;
  END IF;

    --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
	
DELETE FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS WHERE CALIBER_FLAG = V_CALIBER_FLAG;

 V_STEP_NUM:=V_STEP_NUM+1;  
    --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '删除'||V_CALIBER_FLAG||'口径数据',
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
  
  FOR V_VIEW_FLAG IN 0 .. 11 LOOP
  
    V_SOURCE_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_'||V_CALIBER_FLAG||'_VIEW'||V_VIEW_FLAG;
  
    V_SQL := '
    INSERT INTO FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS
      (PERIOD_YEAR,
       PERIOD_ID,
       VERSION_ID,
       BASE_PERIOD_ID,
       VIEW_FLAG,
       LV0_PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME,
       LV1_PROD_RND_TEAM_CODE,
       LV1_PROD_RD_TEAM_CN_NAME,
       LV2_PROD_RND_TEAM_CODE,
       LV2_PROD_RD_TEAM_CN_NAME,
       LV3_PROD_RND_TEAM_CODE,
       LV3_PROD_RD_TEAM_CN_NAME,
       DIMENSION_CODE,
       DIMENSION_CN_NAME,
       DIMENSION_SUBCATEGORY_CODE,
       DIMENSION_SUBCATEGORY_CN_NAME,
       DIMENSION_SUB_DETAIL_CODE,
       DIMENSION_SUB_DETAIL_CN_NAME,
	   SPART_CODE,
	   SPART_CN_NAME,  --202401版本新增SPART层级
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME,
       TOP_CATEGORY_CODE,
       TOP_CATEGORY_CN_NAME,
       PROD_RND_TEAM_CODE,
       PROD_RND_TEAM_CN_NAME,
       DMS_CODE,
       DMS_CN_NAME,
       GROUP_CODE,
       GROUP_CN_NAME,
       GROUP_LEVEL,
       COST_INDEX,
       PARENT_CODE,
       SCENARIO_FLAG,
       APPEND_FLAG,
       CREATED_BY,
       CREATION_DATE,
       LAST_UPDATED_BY,
       LAST_UPDATE_DATE,
       DEL_FLAG,
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME)
      SELECT PERIOD_YEAR,
             PERIOD_ID,
             '||V_VERSION||' AS VERSION_ID,
             '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
             VIEW_FLAG,
             LV0_PROD_RND_TEAM_CODE,
             LV0_PROD_RD_TEAM_CN_NAME,
             LV1_PROD_RND_TEAM_CODE,
             LV1_PROD_RD_TEAM_CN_NAME,
             LV2_PROD_RND_TEAM_CODE,
             LV2_PROD_RD_TEAM_CN_NAME,
             LV3_PROD_RND_TEAM_CODE,
             LV3_PROD_RD_TEAM_CN_NAME,
             DIMENSION_CODE,
             DIMENSION_CN_NAME,
             DIMENSION_SUBCATEGORY_CODE,
             DIMENSION_SUBCATEGORY_CN_NAME,
             DIMENSION_SUB_DETAIL_CODE,
             DIMENSION_SUB_DETAIL_CN_NAME,
			 SPART_CODE,
			 SPART_CN_NAME,  --202401版本新增SPART层级
             TOP_L3_CEG_CODE,
             TOP_L3_CEG_SHORT_CN_NAME,
             TOP_L4_CEG_CODE,
             TOP_L4_CEG_SHORT_CN_NAME,
             TOP_CATEGORY_CODE,
             TOP_CATEGORY_CN_NAME,
             PROD_RND_TEAM_CODE,
             PROD_RND_TEAM_CN_NAME,
             DMS_CODE,
             DMS_CN_NAME,
             GROUP_CODE,
             GROUP_CN_NAME,
             ''ITEM'' AS GROUP_LEVEL,
             COST_INDEX,
             PARENT_CODE,
             SCENARIO_FLAG,
             APPEND_FLAG,
             ''-1'' AS CREATED_BY,
             CURRENT_TIMESTAMP AS CREATION_DATE,
             ''-1'' AS LAST_UPDATED_BY,
             CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
             ''N'' AS DEL_FLAG,
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME
        FROM '||V_SOURCE_TABLE||';';
  
    EXECUTE IMMEDIATE V_SQL;
	
	V_STEP_NUM:=V_STEP_NUM+1;
	--写入日志
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	(F_SP_NAME => V_SP_NAME,
	F_STEP_NUM => V_STEP_NUM,
	F_CAL_LOG_DESC => '中间表'||V_SOURCE_TABLE||'插数成功',
	F_RESULT_STATUS => X_SUCCESS_FLAG,
	F_ERRBUF => 'SUCCESS');
	
  END LOOP;
	
	
	V_STEP_NUM:=V_STEP_NUM+1;
	--写入日志
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	(F_SP_NAME => V_SP_NAME,
	F_STEP_NUM => V_STEP_NUM,
	F_CAL_LOG_DESC => '量纲规格品插数中间表'||V_CALIBER_FLAG||'口径插数成功',
	F_RESULT_STATUS => X_SUCCESS_FLAG,
	F_ERRBUF => 'SUCCESS');

RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := '0';
  
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; 

$$
/

