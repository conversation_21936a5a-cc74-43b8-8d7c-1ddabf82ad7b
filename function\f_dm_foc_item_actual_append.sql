-- Name: f_dm_foc_item_actual_append; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_item_actual_append(f_industry_flag character varying, f_dimension_type character varying, f_keystr character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
最后修改人:罗若文
背景描述：对实际数ITEM补齐3+1年连续月份的均价: 前向补齐、后项补齐
		  202403版本新逻辑，不解密直接补齐
		  增加对数字能源的适配
参数描述:f_dimension_type : 维度类型(通用颗粒度：U,盈利颗粒度：P), x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_ITEM_DECODE_DTL_T(通用颗粒度),FIN_DM_OPT_FOI.DM_FOC_PFT_ITEM_DECODE_DTL_T(盈利颗粒度),FIN_DM_OPT_FOI.DM_FOC_DMS_ITEM_DECODE_DTL_T(量纲颗粒度)
目标表:FIN_DM_OPT_FOI.DM_FOC_BASE_DETAIL_ITEM_T(通用颗粒度),FIN_DM_OPT_FOI.DM_FOC_PFT_BASE_DETAIL_ITEM_T(盈利颗粒度),FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T(量纲颗粒度)
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_ITEM_ACTUAL_APPEND()
*/

DECLARE
  V_SP_NAME    VARCHAR2(100) := 'FIN_DM_OPT_FOI.F_DM_FOC_ITEM_ACTUAL_APPEND'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --新的版本号, 取自 FIN_DM_OPT_FOI.DM_FOC_ITEM_DECODE_DTL_T
  V_BEGIN_DATE TIMESTAMP := TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)-2)||'01','YYYYMM');  --三年前首月
  
  -- 7月版本需求新增
  V_SQL  TEXT; -- SQL逻辑
  V_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_LV4_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_L1_NAME VARCHAR(100);
  V_L2_NAME VARCHAR(100);
  V_IN_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_IN_LV4_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_IN_L1_NAME VARCHAR(100);
  V_IN_L2_NAME VARCHAR(100);
  V_INSERT_LV3_PROD_RND_TEAM_CODE VARCHAR(200);
  V_INSERT_LV4_PROD_RND_TEAM_CODE VARCHAR(200);
  V_INSERT_L1_NAME VARCHAR(100);
  V_INSERT_L2_NAME VARCHAR(100);
  
  V_FROM_TABLE VARCHAR(100); --来源表
  V_TO_TABLE VARCHAR(100);   --目标表
  
  
  -- 9月版本需求新增
  V_DIMENSION_CODE VARCHAR(100);
  V_DIMENSION_CN_NAME VARCHAR(200);
  V_DIMENSION_EN_NAME VARCHAR(200);
  V_DIMENSION_SUBCATEGORY_CODE VARCHAR(100);
  V_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200);
  V_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(200);
  V_DIMENSION_SUB_DETAIL_CODE VARCHAR(100);
  V_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(200);
  V_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(200);
  V_IN_DIMENSION_CODE VARCHAR(100);
  V_IN_DIMENSION_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_EN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_CODE VARCHAR(100);
  V_IN_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_CODE VARCHAR(100);
  V_IN_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(200);
  V_INSERT_DIMENSION_CODE VARCHAR(100);
  V_INSERT_DIMENSION_SUBCATEGORY_CODE VARCHAR(200);
  V_INSERT_DIMENSION_SUB_DETAIL_CODE VARCHAR(200);
  
    --202401月版本新增spart层
  V_SPART_CODE VARCHAR(200);
  V_SPART_CN_NAME VARCHAR(200);
  V_IN_SPART_CODE VARCHAR(200);
  V_IN_SPART_CN_NAME VARCHAR(200);
  V_INSERT_SPART_CODE VARCHAR(200);
  V_INSERT_SPART_CN_NAME VARCHAR(200);
  
  
    --202405月版本新增COA层
  V_COA_CODE VARCHAR(200);
  V_COA_CN_NAME VARCHAR(200);
  V_IN_COA_CODE VARCHAR(200);
  V_IN_COA_CN_NAME VARCHAR(200);
  V_INSERT_COA_CODE VARCHAR(200);
  V_INSERT_COA_CN_NAME VARCHAR(200);
  V_VERSION_TABLE VARCHAR(100);
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行,颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG);
   
  --对入参合法性判断
  IF F_DIMENSION_TYPE NOT IN ('U','P','D') THEN
     DBMS_OUTPUT.PUT_LINE ('入参有误，请重新输入！');
     RETURN '0';
  END IF;
  

  IF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'I' THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ITEM_DECODE_DTL_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_BASE_DETAIL_ITEM_T';--目标表
	 
  ELSIF F_DIMENSION_TYPE = 'P' AND F_INDUSTRY_FLAG = 'I' THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_ITEM_DECODE_DTL_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_BASE_DETAIL_ITEM_T'; --目标表
 
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'I' THEN -- 量纲颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_ITEM_DECODE_DTL_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T'; --目标表
  
  ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'E' THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_ITEM_DECODE_DTL_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_BASE_DETAIL_ITEM_T';--目标表
	 
  ELSIF F_DIMENSION_TYPE = 'P' AND F_INDUSTRY_FLAG = 'E' THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_ITEM_DECODE_DTL_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_BASE_DETAIL_ITEM_T'; --目标表
 
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'E' THEN -- 量纲颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_ITEM_DECODE_DTL_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T'; --目标表
  
  ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_ITEM_DECODE_DTL_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_BASE_DETAIL_ITEM_T';--目标表
	 
  ELSIF F_DIMENSION_TYPE = 'P' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_ITEM_DECODE_DTL_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_BASE_DETAIL_ITEM_T'; --目标表
 
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 量纲颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_ITEM_DECODE_DTL_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T'; --目标表
    
  ELSE
    NULL;
  END IF;

--判断版本表
  IF F_INDUSTRY_FLAG = 'I' THEN 
	--新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
	
  ELSIF F_INDUSTRY_FLAG = 'E' THEN 
	 
	 --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_ENERGY_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
		
   ELSIF F_INDUSTRY_FLAG = 'IAS' THEN 
	 
	 --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_IAS_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
  
  ELSE 
     NULL ;
	 
  END IF;
 
  --1.清空目标表数据:
  EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_TO_TABLE;
  
  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据,颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
 --创建临时表
            DROP TABLE IF EXISTS ACTUAL_APD_TEMP;
            CREATE TEMPORARY TABLE ACTUAL_APD_TEMP (
                VIEW_FLAG VARCHAR(2),
                LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
                LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
                LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
                LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
                LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
                LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
                LV3_PROD_RND_TEAM_CODE    VARCHAR(50), -- 7月版本需求新增
                LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
				LV4_PROD_RND_TEAM_CODE    VARCHAR(50), -- 7月版本需求新增
                LV4_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
                L1_NAME VARCHAR(100),
                L2_NAME VARCHAR(100),
                --9月版本需求新增量纲
                DIMENSION_CODE    VARCHAR(100),
                DIMENSION_CN_NAME    VARCHAR(100),
                DIMENSION_EN_NAME    VARCHAR(100),
                DIMENSION_SUBCATEGORY_CODE    VARCHAR(100),
                DIMENSION_SUBCATEGORY_CN_NAME    VARCHAR(100),
                DIMENSION_SUBCATEGORY_EN_NAME    VARCHAR(100),
                DIMENSION_SUB_DETAIL_CODE    VARCHAR(100),
                DIMENSION_SUB_DETAIL_CN_NAME    VARCHAR(200),
                DIMENSION_SUB_DETAIL_EN_NAME    VARCHAR(200),
				--202401月版本新增spart层
				SPART_CODE VARCHAR(200),
				SPART_CN_NAME VARCHAR(200),
				--202405版本新增
				COA_CODE VARCHAR(50),
				COA_CN_NAME VARCHAR(600),
                L3_CEG_CODE    VARCHAR(50),
                L3_CEG_CN_NAME    VARCHAR(200),
                L3_CEG_SHORT_CN_NAME    VARCHAR(200),
                L4_CEG_CODE    VARCHAR(50), -- 9月版本需求新增
                L4_CEG_CN_NAME    VARCHAR(200),
                L4_CEG_SHORT_CN_NAME    VARCHAR(200),
                CATEGORY_CODE CHARACTER VARYING(50),
                CATEGORY_CN_NAME CHARACTER VARYING(200),
                ITEM_CODE CHARACTER VARYING(50),
                ITEM_CN_NAME CHARACTER VARYING(500),
                PERIOD_YEAR BIGINT,
                PERIOD_ID BIGINT,
                SHIP_QUANTITY NUMERIC,
                RMB_COST_AMT TEXT,
                AVG_AMT TEXT,
                NULL_FLAG VARCHAR(2),
                APD_FLAG VARCHAR(2),
                CALIBER_FLAG VARCHAR(2),
                OVERSEA_FLAG VARCHAR(2),
                LV0_PROD_LIST_CODE VARCHAR(50),
                LV0_PROD_LIST_CN_NAME VARCHAR(200),
                LV0_PROD_LIST_EN_NAME VARCHAR(200)
            )
            ON COMMIT PRESERVE ROWS
            DISTRIBUTE BY HASH(PERIOD_ID,ITEM_CODE);

    --2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
        F_STEP_NUM =>  V_STEP_MUM,
        F_CAL_LOG_DESC => '实际数补齐临时表创建完成,颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS');  
        
    --7月版本需求新增    
    --重置变量入参
    V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
	V_LV4_PROD_RND_TEAM_CODE := 'LV4_PROD_RND_TEAM_CODE,';
    V_LV4_PROD_RD_TEAM_CN_NAME := 'LV4_PROD_RD_TEAM_CN_NAME,';
    V_L1_NAME := 'L1_NAME,';
    V_L2_NAME := 'L2_NAME,';
    V_IN_LV3_PROD_RND_TEAM_CODE := 'T.LV3_PROD_RND_TEAM_CODE,';
    V_IN_LV3_PROD_RD_TEAM_CN_NAME :='T.LV3_PROD_RD_TEAM_CN_NAME,';
	V_IN_LV4_PROD_RND_TEAM_CODE := 'T.LV4_PROD_RND_TEAM_CODE,';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME :='T.LV4_PROD_RD_TEAM_CN_NAME,';
    V_IN_L1_NAME := 'T.L1_NAME,';
    V_IN_L2_NAME := 'T.L2_NAME,';
    V_INSERT_LV3_PROD_RND_TEAM_CODE := ' AND NVL(T.LV3_PROD_RND_TEAM_CODE,3) = NVL(T2.LV3_PROD_RND_TEAM_CODE,3)';
	V_INSERT_LV4_PROD_RND_TEAM_CODE := ' AND NVL(T.LV4_PROD_RND_TEAM_CODE,3) = NVL(T2.LV4_PROD_RND_TEAM_CODE,3)';
    V_INSERT_L1_NAME := ' AND NVL(T.L1_NAME,1) = NVL(T2.L1_NAME,1)';
    V_INSERT_L2_NAME := ' AND NVL(T.L2_NAME,2) = NVL(T2.L2_NAME,2)';
    
    --9月版本需求新增量纲
    V_DIMENSION_CODE := 'DIMENSION_CODE,';
    V_DIMENSION_CN_NAME := 'DIMENSION_CN_NAME,';
    V_DIMENSION_EN_NAME := 'DIMENSION_EN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_EN_NAME := 'DIMENSION_SUBCATEGORY_EN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME := 'DIMENSION_SUB_DETAIL_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_EN_NAME := 'DIMENSION_SUB_DETAIL_EN_NAME,';
    V_IN_DIMENSION_CODE := 'T.DIMENSION_CODE,';
    V_IN_DIMENSION_CN_NAME := 'T.DIMENSION_CN_NAME,';
    V_IN_DIMENSION_EN_NAME := 'T.DIMENSION_EN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_CODE := 'T.DIMENSION_SUBCATEGORY_CODE,';
    V_IN_DIMENSION_SUBCATEGORY_CN_NAME := 'T.DIMENSION_SUBCATEGORY_CN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_EN_NAME := 'T.DIMENSION_SUBCATEGORY_EN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_CODE := 'T.DIMENSION_SUB_DETAIL_CODE,';
    V_IN_DIMENSION_SUB_DETAIL_CN_NAME := 'T.DIMENSION_SUB_DETAIL_CN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_EN_NAME := 'T.DIMENSION_SUB_DETAIL_EN_NAME,';
    V_INSERT_DIMENSION_CODE := ' AND NVL(T.DIMENSION_CODE,1) =  NVL(T2.DIMENSION_CODE,1)';
    V_INSERT_DIMENSION_SUBCATEGORY_CODE := ' AND NVL(T.DIMENSION_SUBCATEGORY_CODE,2) =  NVL(T2.DIMENSION_SUBCATEGORY_CODE,2)';
    V_INSERT_DIMENSION_SUB_DETAIL_CODE := ' AND NVL(T.DIMENSION_SUB_DETAIL_CODE,3) =  NVL(T2.DIMENSION_SUB_DETAIL_CODE,3)';
	
	--202401月版本新增spart层
	V_SPART_CODE := 'SPART_CODE,';
	V_SPART_CN_NAME  := 'SPART_CN_NAME,';
	V_IN_SPART_CODE  := 'T.SPART_CODE,';
	V_IN_SPART_CN_NAME := 'T.SPART_CN_NAME,';
	V_INSERT_SPART_CODE  := ' AND NVL(T.SPART_CODE,2) = NVL(T2.SPART_CODE,2)'; 
    
	--202405月版本新增COA层
	V_COA_CODE := 'COA_CODE,';
	V_COA_CN_NAME  := 'COA_CN_NAME,';
	V_IN_COA_CODE  := 'T.COA_CODE,';
	V_IN_COA_CN_NAME := 'T.COA_CN_NAME,';
	V_INSERT_COA_CODE  := ' AND NVL(T.COA_CODE,2) = NVL(T2.COA_CODE,2)';
	
	
    --通用颗粒度的维度时，不需要L1、L2字段和所有量纲字段
    IF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG IN ('E','I') THEN 
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
       V_INSERT_L1_NAME := '';
       V_INSERT_L2_NAME := '';
       
       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_INSERT_DIMENSION_CODE := '';
       V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
       V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
	   V_SPART_CODE := '';
	   V_SPART_CN_NAME  := '';
	   V_IN_SPART_CODE  := '';
	   V_IN_SPART_CN_NAME := '';
	   V_INSERT_SPART_CODE := '';
	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';
	   
	   --202407需求新增
	V_LV4_PROD_RND_TEAM_CODE := '';
    V_LV4_PROD_RD_TEAM_CN_NAME := '';
    V_IN_LV4_PROD_RND_TEAM_CODE := '';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	
	ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG IN ('IAS') THEN 
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
       V_INSERT_L1_NAME := '';
       V_INSERT_L2_NAME := '';
       
       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_INSERT_DIMENSION_CODE := '';
       V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
       V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
	   V_SPART_CODE := '';
	   V_SPART_CN_NAME  := '';
	   V_IN_SPART_CODE  := '';
	   V_IN_SPART_CN_NAME := '';
	   V_INSERT_SPART_CODE := '';
	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';

	
    --盈利颗粒度的维度时，不需要LV3字段和所有量纲字段
  ELSIF F_DIMENSION_TYPE = 'P' THEN
       V_LV3_PROD_RND_TEAM_CODE := '';
       V_LV3_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV3_PROD_RND_TEAM_CODE := '';
       V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
       V_INSERT_LV3_PROD_RND_TEAM_CODE := '';
    
       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_INSERT_DIMENSION_CODE := '';
       V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
       V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
       V_SPART_CODE := '';
	   V_SPART_CN_NAME  := '';
	   V_IN_SPART_CODE  := '';
	   V_IN_SPART_CN_NAME := '';
	   V_INSERT_SPART_CODE := '';
	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';
	   
	   V_LV4_PROD_RND_TEAM_CODE := '';
    V_LV4_PROD_RD_TEAM_CN_NAME := '';
    V_IN_LV4_PROD_RND_TEAM_CODE := '';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	
    --量纲颗粒度的维度时，不需要L1、L2字段
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'I' THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
       V_INSERT_L1_NAME := '';
       V_INSERT_L2_NAME := '';
	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';
	   
	      --202407需求新增
		V_LV4_PROD_RND_TEAM_CODE := '';
		V_LV4_PROD_RD_TEAM_CN_NAME := '';
		V_IN_LV4_PROD_RND_TEAM_CODE := '';
		V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
		V_INSERT_LV4_PROD_RND_TEAM_CODE := '';

   ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'E' THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
       V_INSERT_L1_NAME := '';
       V_INSERT_L2_NAME := '';
	   
	     --202407需求新增
		V_LV4_PROD_RND_TEAM_CODE := '';
		V_LV4_PROD_RD_TEAM_CN_NAME := '';
		V_IN_LV4_PROD_RND_TEAM_CODE := '';
		V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
		V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	
	    --量纲颗粒度的维度时，不需要L1、L2字段
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'IAS' THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
       V_INSERT_L1_NAME := '';
       V_INSERT_L2_NAME := '';
	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';
	   

		
    ELSE
      NULL;
    END IF;

    V_SQL := 
       'INSERT INTO ACTUAL_APD_TEMP ( 
             VIEW_FLAG,
             LV0_PROD_RND_TEAM_CODE,
             LV0_PROD_RD_TEAM_CN_NAME,
             LV1_PROD_RND_TEAM_CODE,
             LV1_PROD_RD_TEAM_CN_NAME,
             LV2_PROD_RND_TEAM_CODE,
             LV2_PROD_RD_TEAM_CN_NAME,'||
             V_LV3_PROD_RND_TEAM_CODE ||
             V_LV3_PROD_RD_TEAM_CN_NAME ||
			 V_LV4_PROD_RND_TEAM_CODE ||
             V_LV4_PROD_RD_TEAM_CN_NAME ||
             V_L1_NAME ||
             V_L2_NAME ||
             V_DIMENSION_CODE ||
             V_DIMENSION_CN_NAME ||
             V_DIMENSION_EN_NAME||
             V_DIMENSION_SUBCATEGORY_CODE ||
             V_DIMENSION_SUBCATEGORY_CN_NAME ||
             V_DIMENSION_SUBCATEGORY_EN_NAME||
             V_DIMENSION_SUB_DETAIL_CODE ||
             V_DIMENSION_SUB_DETAIL_CN_NAME ||
             V_DIMENSION_SUB_DETAIL_EN_NAME ||
			 V_SPART_CODE ||
			 V_SPART_CN_NAME ||
			 V_COA_CODE ||
			 V_COA_CN_NAME ||'
             L3_CEG_CODE,
             L3_CEG_CN_NAME,
             L3_CEG_SHORT_CN_NAME,
             L4_CEG_CODE,
             L4_CEG_CN_NAME,
             L4_CEG_SHORT_CN_NAME,
             CATEGORY_CODE,
             CATEGORY_CN_NAME,
             ITEM_CODE,
             ITEM_CN_NAME,
             PERIOD_YEAR,
             PERIOD_ID,
             SHIP_QUANTITY,
             RMB_COST_AMT,
             AVG_AMT,
             NULL_FLAG,
             APD_FLAG,
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME,
             LV0_PROD_LIST_EN_NAME)
 
    WITH ACTUAL_ITEM_TEMP AS
     (
      --实际数历史表中出现的重量级团队、采购信息维，取数范围：三年前第1月至当前系统月(不含)2023,2022,2021,2020
      SELECT DISTINCT T.VIEW_FLAG,
                      T.LV0_PROD_RND_TEAM_CODE,
                      T.LV0_PROD_RD_TEAM_CN_NAME,
                      T.LV1_PROD_RND_TEAM_CODE,
                      T.LV1_PROD_RD_TEAM_CN_NAME,
                      T.LV2_PROD_RND_TEAM_CODE,
                      T.LV2_PROD_RD_TEAM_CN_NAME,'||
                      V_IN_LV3_PROD_RND_TEAM_CODE ||
                      V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
					  V_IN_LV4_PROD_RND_TEAM_CODE ||
                      V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
                      V_IN_L1_NAME||
                      V_IN_L2_NAME||
                      V_IN_DIMENSION_CODE ||
                      V_IN_DIMENSION_CN_NAME ||
                      V_IN_DIMENSION_EN_NAME||
                      V_IN_DIMENSION_SUBCATEGORY_CODE ||
                      V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
                      V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
                      V_IN_DIMENSION_SUB_DETAIL_CODE ||
                      V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
                      V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
					  V_IN_SPART_CODE ||
					  V_IN_SPART_CN_NAME ||
					  V_IN_COA_CODE ||
					  V_IN_COA_CN_NAME ||'
                      T.L3_CEG_CODE,
                      T.L3_CEG_CN_NAME,
                      T.L3_CEG_SHORT_CN_NAME,
                      T.L4_CEG_CODE,
                      T.L4_CEG_CN_NAME,
                      T.L4_CEG_SHORT_CN_NAME,
                      T.CATEGORY_CODE,
                      T.CATEGORY_CN_NAME,
                      T.ITEM_CODE,
                      T.ITEM_CN_NAME,
                      T.CALIBER_FLAG,
                      T.OVERSEA_FLAG,
                      T.LV0_PROD_LIST_CODE,
                      T.LV0_PROD_LIST_CN_NAME,
                      T.LV0_PROD_LIST_EN_NAME
        FROM '||V_FROM_TABLE||' T
       WHERE T.PERIOD_YEAR >= YEAR(CURRENT_TIMESTAMP) - 3
         AND T.PERIOD_ID <
             CAST(TO_CHAR(CURRENT_TIMESTAMP, ''YYYYMM'') AS BIGINT)),
    
    PERIOD_DIM_TEMP AS
     (
      --生成连续月份, 三年前第1月至当前系统月(不含)
      SELECT CAST(TO_CHAR(ADD_MONTHS('''||V_BEGIN_DATE||''',NUM.VAL - 1),''YYYYMM'') AS BIGINT)
          AS PERIOD_ID
        FROM GENERATE_SERIES(1,
                              TO_NUMBER(TIMESTAMPDIFF(MONTH,'''||
                                                      V_BEGIN_DATE||''',
                                                      CURRENT_TIMESTAMP)),
                              1) NUM(VAL)),
    
    CROSS_JOIN_TEMP AS
     (
      --生成连续年月的发散维
      SELECT CAST(SUBSTR(B.PERIOD_ID, 1, 4) AS BIGINT) AS PERIOD_YEAR,
              B.PERIOD_ID,
              T.VIEW_FLAG,
              T.LV0_PROD_RND_TEAM_CODE,
              T.LV0_PROD_RD_TEAM_CN_NAME,
              T.LV1_PROD_RND_TEAM_CODE,
              T.LV1_PROD_RD_TEAM_CN_NAME,
              T.LV2_PROD_RND_TEAM_CODE,
              T.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_IN_LV3_PROD_RND_TEAM_CODE ||
              V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
			  V_IN_LV4_PROD_RND_TEAM_CODE ||
              V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
              V_IN_L1_NAME||
              V_IN_L2_NAME||
              V_IN_DIMENSION_CODE ||
              V_IN_DIMENSION_CN_NAME ||
              V_IN_DIMENSION_EN_NAME||
              V_IN_DIMENSION_SUBCATEGORY_CODE ||
              V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
              V_IN_DIMENSION_SUB_DETAIL_CODE ||
              V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
			  V_IN_SPART_CODE ||
			  V_IN_SPART_CN_NAME ||
			  V_IN_COA_CODE ||
			  V_IN_COA_CN_NAME ||'
              T.L3_CEG_CODE,
              T.L3_CEG_CN_NAME,
              T.L3_CEG_SHORT_CN_NAME,
              T.L4_CEG_CODE,
              T.L4_CEG_CN_NAME,
              T.L4_CEG_SHORT_CN_NAME,
              T.CATEGORY_CODE,
              T.CATEGORY_CN_NAME,
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              T.CALIBER_FLAG,
              T.OVERSEA_FLAG,
              T.LV0_PROD_LIST_CODE,
              T.LV0_PROD_LIST_CN_NAME,
              T.LV0_PROD_LIST_EN_NAME
        FROM ACTUAL_ITEM_TEMP T, PERIOD_DIM_TEMP B)
                
            SELECT T.VIEW_FLAG,
             T.LV0_PROD_RND_TEAM_CODE,
             T.LV0_PROD_RD_TEAM_CN_NAME,
             T.LV1_PROD_RND_TEAM_CODE,
             T.LV1_PROD_RD_TEAM_CN_NAME,
             T.LV2_PROD_RND_TEAM_CODE,
             T.LV2_PROD_RD_TEAM_CN_NAME,'||
             V_IN_LV3_PROD_RND_TEAM_CODE ||
             V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
			 V_IN_LV4_PROD_RND_TEAM_CODE ||
             V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
             V_IN_L1_NAME||
             V_IN_L2_NAME||
             V_IN_DIMENSION_CODE ||
             V_IN_DIMENSION_CN_NAME ||
             V_IN_DIMENSION_EN_NAME||
             V_IN_DIMENSION_SUBCATEGORY_CODE ||
             V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
             V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
             V_IN_DIMENSION_SUB_DETAIL_CODE ||
             V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
             V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
			 V_IN_SPART_CODE ||
			 V_IN_SPART_CN_NAME ||
			 V_IN_COA_CODE ||
			 V_IN_COA_CN_NAME ||'
             T.L3_CEG_CODE,
             T.L3_CEG_CN_NAME,
             T.L3_CEG_SHORT_CN_NAME,
             T.L4_CEG_CODE,
             T.L4_CEG_CN_NAME,
             T.L4_CEG_SHORT_CN_NAME,
             T.CATEGORY_CODE,
             T.CATEGORY_CN_NAME,
             T.ITEM_CODE,
             T.ITEM_CN_NAME,
             T.PERIOD_YEAR,
             T.PERIOD_ID,
             T2.SHIP_QUANTITY,
             T2.RMB_COST_AMT,
			 T2.RMB_AVG_AMT,  --2403优化，密文补齐
             --GS_DECRYPT(T2.RMB_AVG_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256'') AS AVG_AMT,
             DECODE(T2.RMB_AVG_AMT, NULL, 0, 1) AS NULL_FLAG, --空标识, 用于sum开窗累计
             DECODE(T2.RMB_AVG_AMT, NULL, ''Y'', ''N'') AS APD_FLAG, --补齐标识：Y为补齐，N为原始
             T.CALIBER_FLAG,
             T.OVERSEA_FLAG,
             T.LV0_PROD_LIST_CODE,
             T.LV0_PROD_LIST_CN_NAME,
             T.LV0_PROD_LIST_EN_NAME
        FROM CROSS_JOIN_TEMP T
        LEFT JOIN '||V_FROM_TABLE||' T2
          ON T.VIEW_FLAG = T2.VIEW_FLAG
         AND NVL(T.LV0_PROD_RND_TEAM_CODE,0) = NVL(T2.LV0_PROD_RND_TEAM_CODE,0)
         AND NVL(T.LV1_PROD_RND_TEAM_CODE,1) = NVL(T2.LV1_PROD_RND_TEAM_CODE,1)
         AND NVL(T.LV2_PROD_RND_TEAM_CODE,2) = NVL(T2.LV2_PROD_RND_TEAM_CODE,2)
         AND T.L3_CEG_CODE = T2.L3_CEG_CODE
         AND T.L4_CEG_CODE = T2.L4_CEG_CODE
         AND T.CATEGORY_CODE = T2.CATEGORY_CODE
         AND T.ITEM_CODE = T2.ITEM_CODE
         AND T.PERIOD_ID = T2.PERIOD_ID
         AND T.CALIBER_FLAG = T2.CALIBER_FLAG
         AND T.OVERSEA_FLAG = T2.OVERSEA_FLAG
         AND T.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE'
         ||V_INSERT_LV3_PROD_RND_TEAM_CODE
		 ||V_INSERT_LV4_PROD_RND_TEAM_CODE
         ||V_INSERT_L1_NAME
         ||V_INSERT_L2_NAME
         ||V_INSERT_DIMENSION_CODE
         ||V_INSERT_DIMENSION_SUBCATEGORY_CODE
         ||V_INSERT_DIMENSION_SUB_DETAIL_CODE
		 ||V_INSERT_SPART_CODE
		 ||V_INSERT_COA_CODE;

         EXECUTE IMMEDIATE V_SQL;
                 
 --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入数据到实际数补齐临时表,颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');   
   

 --2.只补齐均价, 发货额和发货数量无需补齐
      V_SQL := 
           'INSERT INTO '||V_TO_TABLE||' 
                (VERSION_ID,
                PERIOD_YEAR,
                PERIOD_ID,
                LV0_PROD_RND_TEAM_CODE,
                LV0_PROD_RD_TEAM_CN_NAME,
                LV1_PROD_RND_TEAM_CODE,
                LV1_PROD_RD_TEAM_CN_NAME,
                LV2_PROD_RND_TEAM_CODE,
                LV2_PROD_RD_TEAM_CN_NAME,'||
                V_LV3_PROD_RND_TEAM_CODE ||
                V_LV3_PROD_RD_TEAM_CN_NAME ||
				V_LV4_PROD_RND_TEAM_CODE ||
                V_LV4_PROD_RD_TEAM_CN_NAME ||
                V_L1_NAME ||
                V_L2_NAME ||
                V_DIMENSION_CODE ||
                V_DIMENSION_CN_NAME ||
                V_DIMENSION_EN_NAME||
                V_DIMENSION_SUBCATEGORY_CODE ||
                V_DIMENSION_SUBCATEGORY_CN_NAME ||
                V_DIMENSION_SUBCATEGORY_EN_NAME||
                V_DIMENSION_SUB_DETAIL_CODE ||
                V_DIMENSION_SUB_DETAIL_CN_NAME ||
                V_DIMENSION_SUB_DETAIL_EN_NAME ||
				V_SPART_CODE ||
				V_SPART_CN_NAME ||
				V_COA_CODE ||
				V_COA_CN_NAME ||'
                L3_CEG_CODE,
                L3_CEG_CN_NAME,
                L3_CEG_SHORT_CN_NAME,
                L4_CEG_CODE,
                L4_CEG_CN_NAME,
                L4_CEG_SHORT_CN_NAME,
                CATEGORY_CODE,
                CATEGORY_CN_NAME,
                ITEM_CODE,
                ITEM_CN_NAME,
                SHIP_QUANTITY,
                RMB_COST_AMT,
                RMB_AVG_AMT,
                CREATED_BY,
                CREATION_DATE,
                LAST_UPDATED_BY,
                LAST_UPDATE_DATE,
                DEL_FLAG,
                APPEND_FLAG,
                SCENARIO_FLAG,
                VIEW_FLAG,
                CALIBER_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME)
                
    WITH FORWARD_FILLER_TEMP AS
     (
      --按照重量级团队, 采购信息维补齐, 前向补齐均价
      SELECT  T.VIEW_FLAG,
              T.LV0_PROD_RND_TEAM_CODE,
              T.LV0_PROD_RD_TEAM_CN_NAME,
              T.LV1_PROD_RND_TEAM_CODE,
              T.LV1_PROD_RD_TEAM_CN_NAME,
              T.LV2_PROD_RND_TEAM_CODE,
              T.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_IN_LV3_PROD_RND_TEAM_CODE ||
              V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
			  V_IN_LV4_PROD_RND_TEAM_CODE ||
              V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
              V_IN_L1_NAME||
              V_IN_L2_NAME||
              V_IN_DIMENSION_CODE ||
              V_IN_DIMENSION_CN_NAME ||
              V_IN_DIMENSION_EN_NAME||
              V_IN_DIMENSION_SUBCATEGORY_CODE ||
              V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
              V_IN_DIMENSION_SUB_DETAIL_CODE ||
              V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
			  V_IN_SPART_CODE ||
			  V_IN_SPART_CN_NAME ||
			  V_IN_COA_CODE ||
			  V_IN_COA_CN_NAME ||'
              T.L3_CEG_CODE,
              T.L3_CEG_CN_NAME,
              T.L3_CEG_SHORT_CN_NAME,
              T.L4_CEG_CODE,
              T.L4_CEG_CN_NAME,
              T.L4_CEG_SHORT_CN_NAME,
              T.CATEGORY_CODE,
              T.CATEGORY_CN_NAME,
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              T.PERIOD_YEAR,
              T.PERIOD_ID,
              T.SHIP_QUANTITY,
              T.RMB_COST_AMT,
              T.AVG_AMT,
              FIRST_VALUE(T.AVG_AMT) OVER(PARTITION BY T.LV0_PROD_LIST_CODE, T.OVERSEA_FLAG, T.CALIBER_FLAG, T.VIEW_FLAG, T.LV0_PROD_RND_TEAM_CODE, T.LV1_PROD_RND_TEAM_CODE, T.LV2_PROD_RND_TEAM_CODE,'|| V_IN_LV4_PROD_RND_TEAM_CODE||V_IN_LV3_PROD_RND_TEAM_CODE || V_IN_L1_NAME || V_IN_L2_NAME || V_IN_DIMENSION_CODE || V_IN_DIMENSION_SUBCATEGORY_CODE || V_IN_DIMENSION_SUB_DETAIL_CODE ||V_IN_SPART_CODE ||V_IN_COA_CODE||' T.L3_CEG_CODE, T.L4_CEG_CODE, T.CATEGORY_CODE, T.ITEM_CODE, T.AVG_AMT_FLAG ORDER BY T.PERIOD_ID) AS AVG_AMT_2, --新补齐的均价字段
              T.AVG_AMT_FLAG,
              T.APD_FLAG,
              T.CALIBER_FLAG,
              T.OVERSEA_FLAG,
              T.LV0_PROD_LIST_CODE,
              T.LV0_PROD_LIST_CN_NAME,
              T.LV0_PROD_LIST_EN_NAME
        FROM (SELECT  T.VIEW_FLAG,
                      T.LV0_PROD_RND_TEAM_CODE,
                      T.LV0_PROD_RD_TEAM_CN_NAME,
                      T.LV1_PROD_RND_TEAM_CODE,
                      T.LV1_PROD_RD_TEAM_CN_NAME,
                      T.LV2_PROD_RND_TEAM_CODE,
                      T.LV2_PROD_RD_TEAM_CN_NAME,'||
                      V_IN_LV3_PROD_RND_TEAM_CODE ||
                      V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
					  V_IN_LV4_PROD_RND_TEAM_CODE ||
                      V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
                      V_IN_L1_NAME||
                      V_IN_L2_NAME||
                      V_IN_DIMENSION_CODE ||
                      V_IN_DIMENSION_CN_NAME ||
                      V_IN_DIMENSION_EN_NAME||
                      V_IN_DIMENSION_SUBCATEGORY_CODE ||
                      V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
                      V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
                      V_IN_DIMENSION_SUB_DETAIL_CODE ||
                      V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
                      V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
					  V_IN_SPART_CODE ||
					  V_IN_SPART_CN_NAME ||
					  V_IN_COA_CODE ||
					  V_IN_COA_CN_NAME ||'
                      T.L3_CEG_CODE,
                      T.L3_CEG_CN_NAME,
                      T.L3_CEG_SHORT_CN_NAME,
                      T.L4_CEG_CODE,
                      T.L4_CEG_CN_NAME,
                      T.L4_CEG_SHORT_CN_NAME,
                      T.CATEGORY_CODE,
                      T.CATEGORY_CN_NAME,
                      T.ITEM_CODE,
                      T.ITEM_CN_NAME,
                      T.PERIOD_YEAR,
                      T.PERIOD_ID,
                      T.SHIP_QUANTITY,
                      T.RMB_COST_AMT,
                      T.AVG_AMT,
                      SUM(T.NULL_FLAG) OVER(PARTITION BY T.LV0_PROD_LIST_CODE, T.OVERSEA_FLAG, T.CALIBER_FLAG, T.VIEW_FLAG, T.LV0_PROD_RND_TEAM_CODE, T.LV1_PROD_RND_TEAM_CODE, T.LV2_PROD_RND_TEAM_CODE,'|| V_IN_LV4_PROD_RND_TEAM_CODE||V_IN_LV3_PROD_RND_TEAM_CODE || V_IN_L1_NAME || V_IN_L2_NAME || V_IN_DIMENSION_CODE || V_IN_DIMENSION_SUBCATEGORY_CODE || V_IN_DIMENSION_SUB_DETAIL_CODE ||V_IN_SPART_CODE  ||V_IN_COA_CODE||' T.L3_CEG_CODE, T.L4_CEG_CODE, T.CATEGORY_CODE, T.ITEM_CODE ORDER BY T.PERIOD_ID) AS AVG_AMT_FLAG, --均价标识: 为空不参与累计加1
                      T.APD_FLAG,
                      T.CALIBER_FLAG,
                      T.OVERSEA_FLAG,
                      T.LV0_PROD_LIST_CODE,
                      T.LV0_PROD_LIST_CN_NAME,
                      T.LV0_PROD_LIST_EN_NAME
                 FROM ACTUAL_APD_TEMP T) T)
    
    --向后补齐均价
    SELECT '||V_VERSION_ID||',
           T.PERIOD_YEAR,
           T.PERIOD_ID,
           T.LV0_PROD_RND_TEAM_CODE,
           T.LV0_PROD_RD_TEAM_CN_NAME,
           T.LV1_PROD_RND_TEAM_CODE,
           T.LV1_PROD_RD_TEAM_CN_NAME,
           T.LV2_PROD_RND_TEAM_CODE,
           T.LV2_PROD_RD_TEAM_CN_NAME,'||
           V_IN_LV3_PROD_RND_TEAM_CODE ||
           V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
		   V_IN_LV4_PROD_RND_TEAM_CODE ||
           V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
           V_IN_L1_NAME||
           V_IN_L2_NAME||
           V_IN_DIMENSION_CODE ||
           V_IN_DIMENSION_CN_NAME ||
           V_IN_DIMENSION_EN_NAME||
           V_IN_DIMENSION_SUBCATEGORY_CODE ||
           V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
           V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
           V_IN_DIMENSION_SUB_DETAIL_CODE ||
           V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
           V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
		   V_IN_SPART_CODE ||
		   V_IN_SPART_CN_NAME ||
		   V_IN_COA_CODE ||
		   V_IN_COA_CN_NAME ||'
           T.L3_CEG_CODE,
           T.L3_CEG_CN_NAME,
           T.L3_CEG_SHORT_CN_NAME,
           T.L4_CEG_CODE,
           T.L4_CEG_CN_NAME,
           T.L4_CEG_SHORT_CN_NAME,
           T.CATEGORY_CODE,
           T.CATEGORY_CN_NAME,
           T.ITEM_CODE,
           T.ITEM_CN_NAME,
           T.SHIP_QUANTITY,
           T.RMB_COST_AMT,
           --GS_ENCRYPT(NVL(T.AVG_AMT_2, T.AVG_AMT_3),'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256'') AS RMB_AVG_AMT,
		   (NVL(T.AVG_AMT_2, T.AVG_AMT_3)) AS RMB_AVG_AMT,-- 202403版本新逻辑，不解密直接补齐
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           T.APD_FLAG AS APPEND_FLAG,
           ''S'' AS SCENARIO_FLAG,
           T.VIEW_FLAG,
           T.CALIBER_FLAG,
           T.OVERSEA_FLAG,
           T.LV0_PROD_LIST_CODE,
           T.LV0_PROD_LIST_CN_NAME,
           T.LV0_PROD_LIST_EN_NAME
      FROM (SELECT T.VIEW_FLAG,
                   T.LV0_PROD_RND_TEAM_CODE,
                   T.LV0_PROD_RD_TEAM_CN_NAME,
                   T.LV1_PROD_RND_TEAM_CODE,
                   T.LV1_PROD_RD_TEAM_CN_NAME,
                   T.LV2_PROD_RND_TEAM_CODE,
                   T.LV2_PROD_RD_TEAM_CN_NAME,'||
                   V_IN_LV3_PROD_RND_TEAM_CODE ||
                   V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
				   V_IN_LV4_PROD_RND_TEAM_CODE ||
                   V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
                   V_IN_L1_NAME||
                   V_IN_L2_NAME||
                   V_IN_DIMENSION_CODE ||
                   V_IN_DIMENSION_CN_NAME ||
                   V_IN_DIMENSION_EN_NAME||
                   V_IN_DIMENSION_SUBCATEGORY_CODE ||
                   V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
                   V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
                   V_IN_DIMENSION_SUB_DETAIL_CODE ||
                   V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
                   V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
				   V_IN_SPART_CODE ||
				   V_IN_SPART_CN_NAME ||
				   V_IN_COA_CODE ||
				   V_IN_COA_CN_NAME ||'
                   T.L3_CEG_CODE,
                   T.L3_CEG_CN_NAME,
                   T.L3_CEG_SHORT_CN_NAME,
                   T.L4_CEG_CODE,
                   T.L4_CEG_CN_NAME,
                   T.L4_CEG_SHORT_CN_NAME,
                   T.CATEGORY_CODE,
                   T.CATEGORY_CN_NAME,
                   T.ITEM_CODE,
                   T.ITEM_CN_NAME,
                   T.PERIOD_YEAR,
                   T.PERIOD_ID,
                   T.SHIP_QUANTITY,
                   T.RMB_COST_AMT,
                   T.AVG_AMT_2,
                   T2.AVG_AMT_3,
                   T.APD_FLAG,
                   T.CALIBER_FLAG,
                   T.OVERSEA_FLAG,
                   T.LV0_PROD_LIST_CODE,
                   T.LV0_PROD_LIST_CN_NAME,
                   T.LV0_PROD_LIST_EN_NAME
              FROM FORWARD_FILLER_TEMP T
              LEFT JOIN (SELECT DISTINCT T.CALIBER_FLAG,
                                        T.OVERSEA_FLAG,
                                        T.LV0_PROD_LIST_CODE,
                                        T.VIEW_FLAG,
                                        T.LV0_PROD_RND_TEAM_CODE,
                                        T.LV1_PROD_RND_TEAM_CODE,
                                        T.LV2_PROD_RND_TEAM_CODE,'||
                                        V_IN_LV3_PROD_RND_TEAM_CODE ||
										V_IN_LV4_PROD_RND_TEAM_CODE ||
                                        V_IN_L1_NAME||
                                        V_IN_L2_NAME||
                                        V_IN_DIMENSION_CODE ||
                                        V_IN_DIMENSION_CN_NAME ||
                                        V_IN_DIMENSION_EN_NAME||
                                        V_IN_DIMENSION_SUBCATEGORY_CODE ||
                                        V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
                                        V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
                                        V_IN_DIMENSION_SUB_DETAIL_CODE ||
                                        V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
                                        V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
										V_IN_SPART_CODE ||
										V_IN_SPART_CN_NAME ||
										V_IN_COA_CODE ||
										V_IN_COA_CN_NAME ||'
                                        T.L3_CEG_CODE,
                                        T.L4_CEG_CODE,
                                        T.CATEGORY_CODE,
                                        T.ITEM_CODE,
                                        FIRST_VALUE(T.PERIOD_ID) OVER(PARTITION BY T.LV0_PROD_LIST_CODE, T.OVERSEA_FLAG, T.CALIBER_FLAG, T.VIEW_FLAG, T.LV0_PROD_RND_TEAM_CODE, T.LV1_PROD_RND_TEAM_CODE, T.LV2_PROD_RND_TEAM_CODE,'|| V_IN_LV4_PROD_RND_TEAM_CODE||V_IN_LV3_PROD_RND_TEAM_CODE || V_IN_L1_NAME || V_IN_L2_NAME || V_IN_DIMENSION_CODE || V_IN_DIMENSION_SUBCATEGORY_CODE || V_IN_DIMENSION_SUB_DETAIL_CODE ||V_IN_SPART_CODE  ||V_IN_COA_CODE||'  T.L3_CEG_CODE, T.L4_CEG_CODE, T.CATEGORY_CODE, T.ITEM_CODE ORDER BY T.PERIOD_ID ASC) AS PERIOD_ID, --有均价的首条会计期
                                        FIRST_VALUE(T.AVG_AMT_2) OVER(PARTITION BY T.LV0_PROD_LIST_CODE, T.OVERSEA_FLAG, T.CALIBER_FLAG, T.VIEW_FLAG, T.LV0_PROD_RND_TEAM_CODE, T.LV1_PROD_RND_TEAM_CODE, T.LV2_PROD_RND_TEAM_CODE,'|| V_IN_LV4_PROD_RND_TEAM_CODE||V_IN_LV3_PROD_RND_TEAM_CODE || V_IN_L1_NAME || V_IN_L2_NAME || V_IN_DIMENSION_CODE || V_IN_DIMENSION_SUBCATEGORY_CODE || V_IN_DIMENSION_SUB_DETAIL_CODE ||V_IN_SPART_CODE  ||V_IN_COA_CODE||'  T.L3_CEG_CODE, T.L4_CEG_CODE, T.CATEGORY_CODE, T.ITEM_CODE ORDER BY T.PERIOD_ID ASC) AS AVG_AMT_3 --有均价的首条补齐均价
                          FROM FORWARD_FILLER_TEMP T
                         WHERE T.AVG_AMT_FLAG > 0) T2
                ON T.VIEW_FLAG = T2.VIEW_FLAG
               AND NVL(T.LV0_PROD_RND_TEAM_CODE,0) = NVL(T2.LV0_PROD_RND_TEAM_CODE,0)
               AND NVL(T.LV1_PROD_RND_TEAM_CODE,1) = NVL(T2.LV1_PROD_RND_TEAM_CODE,1)
               AND NVL(T.LV2_PROD_RND_TEAM_CODE,2) = NVL(T2.LV2_PROD_RND_TEAM_CODE,2)
               AND T.L3_CEG_CODE = T2.L3_CEG_CODE
               AND T.L4_CEG_CODE = T2.L4_CEG_CODE
               AND T.CATEGORY_CODE = T2.CATEGORY_CODE
               AND T.ITEM_CODE = T2.ITEM_CODE
               AND T.PERIOD_ID < T2.PERIOD_ID
               AND T.CALIBER_FLAG = T2.CALIBER_FLAG
               AND T.OVERSEA_FLAG = T2.OVERSEA_FLAG
               AND T.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE'
               ||V_INSERT_LV3_PROD_RND_TEAM_CODE
			   ||V_INSERT_LV4_PROD_RND_TEAM_CODE
               ||V_INSERT_L1_NAME
               ||V_INSERT_L2_NAME
               ||V_INSERT_DIMENSION_CODE
               ||V_INSERT_DIMENSION_SUBCATEGORY_CODE
               ||V_INSERT_DIMENSION_SUB_DETAIL_CODE
			   ||V_INSERT_SPART_CODE
			   ||V_INSERT_COA_CODE 			   
               ||' ) T';

               EXECUTE IMMEDIATE V_SQL;

  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入新补齐的分视角均价实际数到'||V_TO_TABLE||'表, 版本号='||V_VERSION_ID||',颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;

  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG);

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败,颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG, 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

