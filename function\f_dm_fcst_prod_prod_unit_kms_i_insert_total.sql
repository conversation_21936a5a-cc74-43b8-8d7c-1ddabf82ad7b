-- Name: f_dm_fcst_prod_prod_unit_kms_i_insert_total; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_prod_prod_unit_kms_i_insert_total(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$


/*
背景描述：1.从贴源层的临时表插入到全量结果表, 并插入主键
参数描述：x_success_flag ：是否成功
事例：fin_dm_opt_foi.F_DM_FCST_PROD_PROD_UNIT_KMS_I_INSERT_TOTAL()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'fin_dm_opt_foi.F_DM_FCST_PROD_PROD_UNIT_KMS_I_INSERT_TOTAL'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_TIMESTAMP  VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISSFF'); --当前current_timestamp的时间戳


BEGIN
  X_RESULT_STATUS = '1';

  --0.开始日志
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');


  --【初始化使用】清空目标表数据:
   EXECUTE IMMEDIATE 'TRUNCATE TABLE fin_dm_opt_foi.DWL_PROD_PROD_UNIT_KMS_I';

  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除fin_dm_opt_foi.DWL_PROD_PROD_UNIT_KMS_I所有的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --2.从临时表增量刷数到全量表, 并生成主键
  FOR CUR IN (SELECT DISTINCT PERIOD_ID
  				 FROM fin_dm_opt_foi.FCST_DWL_PROD_PROD_UNIT_KMS_I
  				 ORDER BY PERIOD_ID ASC) LOOP
  INSERT INTO fin_dm_opt_foi.DWL_PROD_PROD_UNIT_KMS_I
    (
 	P_FLAG ,
	PERIOD_ID ,
        data_source,
	SCENARIO ,
	PROD_KEY ,
	PROD_CODE ,
	CONTRACT_KEY ,
	HW_CONTRACT_NUM ,
	GEO_PC_KEY ,
	END_CUST_KEY ,
	SPART_CODE ,
	RMB_FACT_RATE_AMT ,
	USD_FACT_RATE_AMT ,
	PART_QTY ,
	PROD_UNIT_QTY ,
	DIMENSION_KEY ,
	MAIN_DIMENSION_FLAG ,
	SELF_PROD_AND_SALES_FLAG ,
        dw_last_update_date
	--PRIMARY_ID
		 )

    SELECT
     P_FLAG ,
	PERIOD_ID ,
	data_source,
	SCENARIO ,
	PROD_KEY ,
	PROD_CODE ,
	CONTRACT_KEY ,
	HW_CONTRACT_NUM ,
	GEO_PC_KEY ,
	END_CUST_KEY ,
	SPART_CODE ,
	RMB_FACT_RATE_AMT ,
	USD_FACT_RATE_AMT ,
	PART_QTY ,
	PROD_UNIT_QTY ,
	DIMENSION_KEY ,
	MAIN_DIMENSION_FLAG ,
	SELF_PROD_AND_SALES_FLAG ,
	dw_last_update_date
	--NEXTVAL('fin_dm_opt_foi.DWL_PROD_PROD_UNIT_KMS_S') || V_TIMESTAMP --主键值=序列+时间戳
      FROM fin_dm_opt_foi.FCST_DWL_PROD_PROD_UNIT_KMS_I P
      WHERE P.PERIOD_ID = CUR.PERIOD_ID
      ;

  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '从fin_dm_opt_foi.FCST_DWL_PROD_PROD_UNIT_KMS_I临时表刷数月份: ' || CUR.PERIOD_ID ||'到全量表fin_dm_opt_foi.DWL_PROD_PROD_UNIT_KMS_I, 并生成主键',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  END LOOP;

  --收集统计信息
    EXECUTE IMMEDIATE 'ANALYZE fin_dm_opt_foi.fcst_DWL_PROD_PROD_UNIT_KMS_I';
    EXECUTE IMMEDIATE 'ANALYZE fin_dm_opt_foi.DWL_PROD_PROD_UNIT_KMS_I';

  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集fin_dm_opt_foi.DWL_PROD_PROD_UNIT_KMS_I统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';

  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败',
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END

$$
/

