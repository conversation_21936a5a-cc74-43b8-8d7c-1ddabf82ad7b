-- Name: f_dm_fcst_get_period_id; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_get_period_id(OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

    /*
    获取不同PBI维度树下的会计期起止时间
    */

DECLARE
    V_STEP_NUM int := 1;
    V_SP_NAME   VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FCST_GET_PERIOD_ID';
    V_VERSION_ID   INT ;

BEGIN
x_result_status := '1';
    SELECT VERSION_ID
	INTO V_VERSION_ID
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;

    EXECUTE  IMMEDIATE 'DELETE FROM  DM_FCST_ICT_PERIOD_ID_DIM where version_id = '||V_VERSION_ID;

	INSERT INTO DM_FCST_ICT_PERIOD_ID_DIM(
        granularityType ,
        startTime ,
        endTime ,
        version_id ,
        create_time ,
        last_update_time
    )
    SELECT
        'IRB'             AS granularityType,
        MIN( period_id )  AS startTime,
        MAX( period_id )  AS endTime,
        V_VERSION_ID AS version_id,
        CURRENT_TIMESTAMP AS create_time,
        CURRENT_TIMESTAMP AS last_update_time
    FROM
        fin_dm_opt_foi.dm_fcst_ict_std_irb_mon_cost_idx_t
        WHERE VERSION_ID = V_VERSION_ID;

    INSERT INTO DM_FCST_ICT_PERIOD_ID_DIM(
        granularityType ,
        startTime ,
        endTime ,
        version_id ,
        create_time ,
        last_update_time
    )
    SELECT
        'INDUS'           AS granularityType,
        MIN( period_id )  AS startTime,
        MAX( period_id )  AS endTime,
        V_VERSION_ID AS version_id,
        CURRENT_TIMESTAMP AS create_time,
        CURRENT_TIMESTAMP AS last_update_time
    FROM
        fin_dm_opt_foi.dm_fcst_ict_std_INDUS_mon_cost_idx_t
        WHERE VERSION_ID = V_VERSION_ID;

    INSERT INTO DM_FCST_ICT_PERIOD_ID_DIM(
        granularityType ,
        startTime ,
        endTime ,
        version_id ,
        create_time ,
        last_update_time
    )
    SELECT
        'PROD'            AS granularityType,
        MIN( period_id )  AS startTime,
        MAX( period_id )  AS endTime,
        V_VERSION_ID AS version_id,
        CURRENT_TIMESTAMP AS create_time,
        CURRENT_TIMESTAMP AS last_update_time
    FROM
        fin_dm_opt_foi.dm_fcst_ict_std_PROD_mon_cost_idx_t
        WHERE VERSION_ID = V_VERSION_ID;

    --2 执行结束
    V_STEP_NUM = V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
            ( F_SP_NAME => V_SP_NAME ,
              F_STEP_NUM => V_STEP_NUM ,
              F_CAL_LOG_DESC => '数据插入完成' ,
             -- F_DML_ROW_COUNT => SQL % ROWCOUNT ,
              F_RESULT_STATUS => X_RESULT_STATUS ,
              F_ERRBUF => 'SUCCESS' );


    --收集统计信息
  --  EXECUTE IMMEDIATE ' ANALYZE ' || V_TO_TABLE;

    --日志结束
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
            ( F_SP_NAME => V_SP_NAME ,
              F_STEP_NUM => V_STEP_NUM ,
              F_CAL_LOG_DESC => V_SP_NAME || '运行结束, 收集' );

    RETURN 'SUCCESS';

EXCEPTION
    WHEN OTHERS THEN
        X_RESULT_STATUS := 'FAILED';

        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
                ( F_SP_NAME => V_SP_NAME ,
                  F_CAL_LOG_DESC => V_SP_NAME || '运行失败' ,
                  F_RESULT_STATUS => X_RESULT_STATUS ,
                  F_ERRBUF => SQLSTATE || ':' || SQLERRM
                );

END;

$$
/

