-- Name: f_dm_foc_version_info_is_running_change; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_version_info_is_running_change(f_industry_flag character varying, x_is_running character varying DEFAULT NULL::character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$


/*
创建时间:2024年2月4日15:24:51
创建人  :李志勇
最后修改时间:2024年5月25日10:15:12 新增数字能源逻辑适配
最后修改人:李志勇
背景描述:修改版本信息表中当月生成的最大版本号（年度和月度）的刷数状态，其对应 x_is_running 设置为 Y（正在刷数） 或者N （当前未刷数）
参数描述: x_is_running :刷数目标状态  x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T(版本信息表)
目标表:FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T(版本信息表)
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_VERSION_INFO_IS_RUNNING_CHANGE('N')
*/
DECLARE
    V_SP_NAME               VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_VERSION_INFO_IS_RUNNING_CHANGE'; --存储过程名称
    V_STEP_MUM              BIGINT        := 0; --步骤号
    V_SQL                   TEXT; --SQL逻辑
    V_CNT_CATEGORY          BIGINT;
    V_CNT_ITEM              BIGINT;
    V_IS_RUNNING            VARCHAR(5)    := x_is_running;
    V_FROM_TABLE            varchar(100);
    V_CATEGORY_MAX_VERSION  int8;
    V_ITEM_MAX_VERSION      int8;
    V_CATEGORY_VERSION_TIME timestamp;
    V_ITEM_VERSION_TIME     timestamp;

BEGIN
    X_RESULT_STATUS = 'SUCCESS';

    --1、开始日志
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
            (F_SP_NAME => V_SP_NAME,
             F_STEP_NUM => V_STEP_MUM,
             F_CAL_LOG_DESC => V_SP_NAME || '开始执行');
			 
 -- 判断产业项目为ICT还是数字能源
  IF F_INDUSTRY_FLAG = 'I' THEN   -- ICT  
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T';
  ELSIF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源  
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T';
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 数字能源  
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T';
  END IF;

    /*检查是否有多个版本号*/
    IF x_is_running IS NULL THEN
        V_SQL := 'SELECT COUNT(version_id)
        FROM ' || V_FROM_TABLE || '
        WHERE DATA_TYPE = ''CATEGORY''
          AND SUBSTR(CREATION_DATE, 1, 7) = SUBSTR(CURRENT_DATE, 1, 7)
          AND del_flag = ''N''';
        EXECUTE IMMEDIATE V_SQL INTO V_CNT_CATEGORY;

        V_SQL := ' SELECT COUNT(version_id)
        FROM ' || V_FROM_TABLE || '
        WHERE DATA_TYPE = ''ITEM''
          AND SUBSTR(CREATION_DATE, 1, 7) = SUBSTR(CURRENT_DATE, 1, 7)
          AND del_flag = ''N''';
        EXECUTE IMMEDIATE V_SQL INTO V_CNT_ITEM;

        IF V_CNT_CATEGORY <> 1 OR V_CNT_ITEM <> 1 THEN
            x_result_status = 'FAILED';
            --写入日志
            V_STEP_MUM := V_STEP_MUM + 1;
            PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
                    (F_SP_NAME => V_SP_NAME,
                     F_STEP_NUM => V_STEP_MUM,
                     F_CAL_LOG_DESC => 'ERROR:当月版本号存在多个有效版本号',
                     F_DML_ROW_COUNT => 0,
                     F_RESULT_STATUS => X_RESULT_STATUS,
                     F_ERRBUF => 'ERROR');
            RETURN 'ERROR:当月版本号存在多个有效版本号.';
        END IF;

    ELSE
        /*2、获取当月最大有效版本号*/
        V_SQL := ' SELECT MAX(version_id)
        FROM ' || V_FROM_TABLE || '
        WHERE DATA_TYPE = ''CATEGORY''
          AND SUBSTR(CREATION_DATE, 1, 7) = SUBSTR(CURRENT_DATE, 1, 7) AND DEL_FLAG = ''N'' ';
        EXECUTE IMMEDIATE V_SQL INTO V_CATEGORY_MAX_VERSION;

        V_SQL := 'SELECT MAX(version_id)
        FROM ' || V_FROM_TABLE || '
        WHERE DATA_TYPE = ''ITEM''
          AND SUBSTR(CREATION_DATE, 1, 7) = SUBSTR(CURRENT_DATE, 1, 7) AND DEL_FLAG = ''N'' ';
        EXECUTE IMMEDIATE V_SQL INTO V_ITEM_MAX_VERSION;

        V_SQL := ' SELECT MAX(creation_date)
        FROM ' || V_FROM_TABLE || '
        WHERE  DATA_TYPE = ''CATEGORY'' AND DEL_FLAG = ''N'' ';
        EXECUTE IMMEDIATE V_SQL INTO V_CATEGORY_VERSION_TIME;

        V_SQL := ' SELECT MAX(creation_date)
        FROM ' || V_FROM_TABLE || '
        WHERE  DATA_TYPE = ''ITEM'' AND DEL_FLAG = ''N'' ';
        EXECUTE IMMEDIATE V_SQL INTO V_ITEM_VERSION_TIME;

        IF substr(V_CATEGORY_VERSION_TIME, 1, 7) <> SUBSTR(CURRENT_DATE, 1, 7) OR
           substr(V_ITEM_VERSION_TIME, 1, 7) <> SUBSTR(CURRENT_DATE, 1, 7) THEN
            --写入日志
            x_result_status = 'FAILED';
            V_STEP_MUM := V_STEP_MUM + 1;
            PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
                    (F_SP_NAME => V_SP_NAME,
                     F_STEP_NUM => V_STEP_MUM,
                     F_CAL_LOG_DESC => '当前月份暂无版本号生成或者版本号生成不全',
                     F_DML_ROW_COUNT => 0,
                     F_RESULT_STATUS => X_RESULT_STATUS,
                     F_ERRBUF => 'ERROR');
            RETURN 'ERROR:当前月份暂无版本号生成或生成不全.';
        END IF;

        --3、屏蔽/开放当期版本号,取最大且有效
        IF V_IS_RUNNING = 'N' THEN --放开当期版本号
            V_SQL := 'UPDATE ' || V_FROM_TABLE || '
            SET is_running         = ''' || V_IS_RUNNING || ''',
                last_update_date = current_timestamp
            WHERE VERSION_ID = ' || V_CATEGORY_MAX_VERSION || '
				AND DEL_FLAG = ''N''
              AND is_running = ''Y''';
            EXECUTE IMMEDIATE V_SQL;


            V_SQL := 'UPDATE ' || V_FROM_TABLE || '
            SET is_running         = ''' || V_IS_RUNNING || ''',
                last_update_date = current_timestamp
            WHERE VERSION_ID = ' || V_ITEM_MAX_VERSION || '
			AND DEL_FLAG = ''N''
              AND is_running = ''Y''';
            EXECUTE IMMEDIATE V_SQL;

        ELSE
            IF V_IS_RUNNING = 'Y' THEN --屏蔽当期版本号
                V_SQL := 'UPDATE ' || V_FROM_TABLE || '
            SET is_running         = ''' || V_IS_RUNNING || ''',
                last_update_date = current_timestamp
            WHERE VERSION_ID = ' || V_CATEGORY_MAX_VERSION || '
			AND DEL_FLAG = ''N''
              AND is_running = ''N''';
                EXECUTE IMMEDIATE V_SQL;

                V_SQL := 'UPDATE ' || V_FROM_TABLE || '
            SET is_running         = ''' || V_IS_RUNNING || ''',
                last_update_date = current_timestamp
            WHERE VERSION_ID = ' || V_ITEM_MAX_VERSION || '
			AND DEL_FLAG = ''N''
              AND is_running = ''N''';
                EXECUTE IMMEDIATE V_SQL;


            END IF;
        END IF;

    END IF;

    --写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
            (F_SP_NAME => V_SP_NAME,
             F_STEP_NUM => V_STEP_MUM,
             F_CAL_LOG_DESC => '版本号状态修改完成。' || 'CATEGROY本月最大版本号: ' || V_CATEGORY_MAX_VERSION ||
                               '跑数状态修改成: ' || V_IS_RUNNING || ';    ITEM本月最大版本号: ' ||
                               V_ITEM_MAX_VERSION ||
                               '跑数状态修改成: ' || V_IS_RUNNING,
             F_DML_ROW_COUNT => 0,
             F_RESULT_STATUS => X_RESULT_STATUS,
             F_ERRBUF => 'SUCCESS');

    RETURN 'SUCCESS';

EXCEPTION
    WHEN OTHERS THEN
        X_RESULT_STATUS := 'FAILED';

        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
                (F_SP_NAME => V_SP_NAME,
                 F_CAL_LOG_DESC => V_SP_NAME || '运行失败',
                 F_RESULT_STATUS => X_RESULT_STATUS,
                 F_ERRBUF => SQLSTATE || ':' || SQLERRM
                );

END




$$
/

