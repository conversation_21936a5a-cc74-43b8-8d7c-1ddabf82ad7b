-- Name: f_dm_foc_cus_item_append; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_cus_item_append(f_industry_flag character varying, f_keystr character varying, f_custom_id character varying DEFAULT NULL::character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/********************************************************************************************************************************************************************************
最近更新时间:2024年6月29日14点10分
修改人   ：黄心蕊 hwx1187045
修改内容： 202407版本 新增IAS部分 IAS新增LV4层级
最近更新时间:2024年4月25日17点07分
修改人   ：黄心蕊 hwx1187045
修改内容： 202405版本 新增数字能源部分,及量纲增加COA层级
创建时间：2023/03/21
创建人  ：刘必华
最后修改时间:20230829
最后修改人:曹琼
背景描述：对实际数ITEM补齐3+1年连续月份的均价: 前向补齐、后项补齐
参数描述:f_keystr : 密钥, x_success_flag :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_CUS_ITEM_DECODE_DTL_T 
目标表:FIN_DM_OPT_FOI.DM_FOC_CUS_BASE_DETAIL_ITEM_T 
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_CUS_ITEM_APPEND()
*********************************************************************************************************************************************************************************/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_CUS_ITEM_APPEND'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --新的版本号, 取自OPT_FCST.DM_FOC_ITEM_DECODE_DTL_T
  V_BEGIN_DATE TIMESTAMP := TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)-3)||'01','YYYYMM');  --三年前首月
  V_KEYSTR  varchar2(50) := F_KEYSTR;  -- 密钥入参
  V_FROM_TABLE VARCHAR(200); -- 来源表
  V_TO_TABLE VARCHAR(200); -- 目标表
  V_SQL  TEXT; 
  V_CUSTOM_ID TEXT; 
  --202403版本新增 预测数时间范围修改
  V_END_YEAR VARCHAR(50);
  V_COUNT_MONTH INTEGER;
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
 /*  
  --对入参合法性判断
  IF F_DIMENSION_TYPE NOT IN ('U','P','D') THEN
     DBMS_OUTPUT.PUT_LINE ('入参有误，请重新输入！');
     RETURN '0';
  END IF;*/
 /* 
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_CUS_ITEM_DECODE_DTL_T';--来源表
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_CUS_BASE_DETAIL_ITEM_T';--目标表
	*/

  --新版本号赋值
  /*SELECT T.VERSION_ID
    INTO V_VERSION_ID
    FROM FIN_DM_OPT_FOI.DM_FOC_ITEM_DECODE_DTL_T T
   LIMIT 1;*/
 /* V_SQL := 'SELECT VERSION_ID FROM FIN_DM_OPT_FOI.DM_FOC_TOP_ITEM_INFO_T T 
  ORDER BY LAST_UPDATE_DATE DESC
  LIMIT 1 ';
  EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;*/
  
  
 --取版本号
  IF F_INDUSTRY_FLAG = 'I' THEN
  	SELECT VERSION_ID
  	  INTO V_VERSION_ID
  	  FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
  	 WHERE DEL_FLAG = 'N'
  	   AND STATUS = 1
  	   AND UPPER(DATA_TYPE) = 'ITEM'
  	 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
   --20240327 修改版本号取数逻辑
   
    V_FROM_TABLE	:= 'FIN_DM_OPT_FOI.DM_FOC_CUS_ITEM_DECODE_DTL_T';--来源表
    V_TO_TABLE		:= 'FIN_DM_OPT_FOI.DM_FOC_CUS_BASE_DETAIL_ITEM_T';--目标表
  
  ELSIF F_INDUSTRY_FLAG = 'E' THEN	--202405版本 新增数字能源部分
  	SELECT VERSION_ID
  	  INTO V_VERSION_ID
  	  FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T
  	 WHERE DEL_FLAG = 'N'
  	   AND STATUS = 1
  	   AND UPPER(DATA_TYPE) = 'ITEM'
  	 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
  	 
    V_FROM_TABLE	:= 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_CUS_ITEM_DECODE_DTL_T';--来源表
    V_TO_TABLE		:= 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_CUS_BASE_DETAIL_ITEM_T';--目标表
	
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN	--202405版本 新增数字能源部分
  	SELECT VERSION_ID
  	  INTO V_VERSION_ID
  	  FROM FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T
  	 WHERE DEL_FLAG = 'N'
  	   AND STATUS = 1
  	   AND UPPER(DATA_TYPE) = 'ITEM'
  	 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
  	 
    V_FROM_TABLE	:= 'FIN_DM_OPT_FOI.DM_FOC_IAS_CUS_ITEM_DECODE_DTL_T';--来源表
    V_TO_TABLE		:= 'FIN_DM_OPT_FOI.DM_FOC_IAS_CUS_BASE_DETAIL_ITEM_T';--目标表
    
  END IF ;
  
  
  
 
  --1.清空目标表数据并复制:
  
  IF  F_CUSTOM_ID IS NULL THEN 
     V_CUSTOM_ID := ' AND 1=1 ';
	  EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_TO_TABLE;
  ELSE 
    V_CUSTOM_ID := ' AND CUSTOM_ID = '||F_CUSTOM_ID||' ' ;
    V_SQL := 'DELETE FROM '||V_TO_TABLE||' WHERE CUSTOM_ID = '||F_CUSTOM_ID||';';
    EXECUTE IMMEDIATE V_SQL;
  END  IF;
 
  
  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   
    --取预测月的最大月份 202303版本需求 
  --预测期间动态扩展从12个月到18个月。当最大实际月是1月到6月,预测月取到第二年的6月;当最大实际月是7月到12月,预测月取到第二年的12月
	SELECT (CASE WHEN MONTH(ADD_MONTHS(CURRENT_TIMESTAMP, -1)) between 1 and 6 
			THEN CAST((YEAR(CURRENT_DATE)+1)||'06' AS BIGINT)
			WHEN MONTH(ADD_MONTHS(CURRENT_TIMESTAMP, -1)) between 7 and 11 
			THEN CAST((YEAR(CURRENT_DATE)+1)||'12' AS BIGINT)
			WHEN MONTH(ADD_MONTHS(CURRENT_TIMESTAMP, -1)) = 12 
			THEN CAST(YEAR(CURRENT_DATE)||'12' AS BIGINT)
			END) AS END_YEAR, 
			CASE WHEN MONTH(ADD_MONTHS(CURRENT_TIMESTAMP, -1)) IN (1,7) THEN 16
			WHEN MONTH(ADD_MONTHS(CURRENT_TIMESTAMP, -1)) IN (2,8) THEN 15
			WHEN MONTH(ADD_MONTHS(CURRENT_TIMESTAMP, -1)) IN (3,9) THEN 14
			WHEN MONTH(ADD_MONTHS(CURRENT_TIMESTAMP, -1)) IN (4,10) THEN 13
			WHEN MONTH(ADD_MONTHS(CURRENT_TIMESTAMP, -1)) IN (5,11) THEN 12
			WHEN MONTH(ADD_MONTHS(CURRENT_TIMESTAMP, -1)) IN (6,12) THEN 11
			END AS COUNT_MONTH INTO V_END_YEAR,V_COUNT_MONTH
  FROM DUAL;
   
  
 --1.创建临时表,插入需要补齐的实际数情况
   DROP TABLE IF EXISTS ACTUAL_APD_TEMP;
   CREATE TEMPORARY TABLE ACTUAL_APD_TEMP (
       PERIOD_YEAR BIGINT,
       PERIOD_ID BIGINT,
       PARENT_CODE    VARCHAR(50),
       PARENT_CN_NAME    VARCHAR(2000),
       PARENT_LEVEL  VARCHAR(50),
       GRANULARITY_TYPE  VARCHAR(2),
       GROUP_CODE    VARCHAR(50),
       GROUP_CN_NAME    VARCHAR(2000),
       LV0_PROD_LIST_CODE    VARCHAR(50),
       LV0_PROD_LIST_CN_NAME    VARCHAR(200),
       OVERSEA_FLAG    VARCHAR(50), 
       CALIBER_FLAG    VARCHAR(200),
       CUSTOM_ID    VARCHAR(50),
       CUSTOM_CN_NAME    VARCHAR(200),
       VIEW_FLAG VARCHAR(2),
       SHIP_QUANTITY NUMERIC,
       RMB_COST_AMT TEXT,
       --AVG_AMT NUMERIC,
	   AVG_AMT TEXT,  --20240402修改 只补齐不解密
       NULL_FLAG NUMERIC,
       APD_FLAG VARCHAR(2)
   )
   ON COMMIT PRESERVE ROWS
   DISTRIBUTE BY HASH(PERIOD_ID,GROUP_CODE);

    --2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
        F_STEP_NUM =>  V_STEP_MUM,
        F_CAL_LOG_DESC => '实际数补齐临时表创建完成',
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS');  

    V_SQL := 
       'INSERT INTO ACTUAL_APD_TEMP ( 
             PERIOD_YEAR,
             PERIOD_ID,
             PARENT_CODE,
             PARENT_CN_NAME,
             PARENT_LEVEL,
             GRANULARITY_TYPE,
             GROUP_CODE,
             GROUP_CN_NAME,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME,
             OVERSEA_FLAG,
             CALIBER_FLAG,
             CUSTOM_ID,
             CUSTOM_CN_NAME,
             VIEW_FLAG,
             AVG_AMT,
             RMB_COST_AMT,
             SHIP_QUANTITY,
             NULL_FLAG,
             APD_FLAG)
 
    WITH ACTUAL_ITEM_TEMP AS
     (
      --实际数历史表中出现的重量级团队、采购信息维，取数范围：三年前第1月至当前系统月(不含)2023,2022,2021,2020
      SELECT DISTINCT T.VIEW_FLAG,
                        PARENT_CODE,
                        PARENT_CN_NAME,
                        PARENT_LEVEL,
                        GRANULARITY_TYPE,
                        GROUP_CODE,
                        GROUP_CN_NAME,
                        LV0_PROD_LIST_CODE,
                        LV0_PROD_LIST_CN_NAME,
                        OVERSEA_FLAG,
                        CALIBER_FLAG,CUSTOM_ID,CUSTOM_CN_NAME
        FROM '||V_FROM_TABLE||' T
       WHERE T.PERIOD_YEAR >= YEAR(CURRENT_TIMESTAMP) - 3
         AND T.PERIOD_ID <
             CAST(TO_CHAR(CURRENT_TIMESTAMP, ''YYYYMM'') AS BIGINT)
        AND PARENT_LEVEL IS NOT NULL
       '||V_CUSTOM_ID||'
        ),
    
    PERIOD_DIM_TEMP AS
     (
      --生成连续月份, 三年前第1月至当前系统月(不含)
      SELECT CAST(TO_CHAR(ADD_MONTHS('''||V_BEGIN_DATE||''',NUM.VAL - 1),''YYYYMM'') AS BIGINT)
          AS PERIOD_ID
        FROM GENERATE_SERIES(1,
                              TO_NUMBER(TIMESTAMPDIFF(MONTH,'''||
                                                      V_BEGIN_DATE||''',
                                                      CURRENT_TIMESTAMP)),
                              1) NUM(VAL)),
    
    CROSS_JOIN_TEMP AS
     (
      --生成连续年月的发散维
      SELECT CAST(SUBSTR(B.PERIOD_ID, 1, 4) AS BIGINT) AS PERIOD_YEAR,
              B.PERIOD_ID,
              T1.PARENT_CODE,
              T1.PARENT_CN_NAME,
              T1.PARENT_LEVEL,
              T1.GRANULARITY_TYPE,
              T1.GROUP_CODE,
              T1.GROUP_CN_NAME,
              T1.LV0_PROD_LIST_CODE,
              T1.LV0_PROD_LIST_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.CALIBER_FLAG,
              T1.VIEW_FLAG,T1.CUSTOM_ID,T1.CUSTOM_CN_NAME
        FROM ACTUAL_ITEM_TEMP T1, PERIOD_DIM_TEMP B
    )
            SELECT 
             T.PERIOD_YEAR,
             T.PERIOD_ID,
             T.PARENT_CODE,
             T.PARENT_CN_NAME,
             T.PARENT_LEVEL,
             T.GRANULARITY_TYPE,
             T.GROUP_CODE,
             T.GROUP_CN_NAME,
             T.LV0_PROD_LIST_CODE,
             T.LV0_PROD_LIST_CN_NAME,
             T.OVERSEA_FLAG,
             T.CALIBER_FLAG,
             T.CUSTOM_ID,
             T.CUSTOM_CN_NAME,
             T.VIEW_FLAG,
             --GS_DECRYPT(T2.RMB_AVG_AMT,'''||v_keystr||''', ''AES128'', ''CBC'', ''SHA256'') AS AVG_AMT,
			 T2.RMB_AVG_AMT AS AVG_AMT,  --20240402修改 只补齐不解密
             T2.RMB_COST_AMT ,
             T2.SHIP_QUANTITY , 
             DECODE(T2.RMB_AVG_AMT, NULL, 0, 1) AS NULL_FLAG, --空标识, 用于sum开窗累计
             DECODE(T2.RMB_AVG_AMT, NULL, ''Y'', ''N'') AS APD_FLAG --补齐标识：Y为补齐，N为原始
        FROM CROSS_JOIN_TEMP T
        LEFT JOIN '||V_FROM_TABLE||' T2
          ON T.VIEW_FLAG = T2.VIEW_FLAG
          AND NVL(T.PARENT_CODE,''SNULL'') = NVL(T2.PARENT_CODE,''SNULL'')
          AND NVL(T.PARENT_LEVEL,''SNULL'') = NVL(T2.PARENT_LEVEL,''SNULL'')
          AND T.GRANULARITY_TYPE = T2.GRANULARITY_TYPE
          AND T.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
          AND T.GROUP_CODE = T2.GROUP_CODE
          AND T.PERIOD_ID = T2.PERIOD_ID
          AND T.CUSTOM_ID = T2.CUSTOM_ID
          AND T.OVERSEA_FLAG = T2.OVERSEA_FLAG
          AND T.CALIBER_FLAG = T2.CALIBER_FLAG
          WHERE T.PARENT_LEVEL IS NOT NULL ;';

--DBMS_OUTPUT.PUT_LINE(V_SQL); 
  EXECUTE IMMEDIATE V_SQL;
           
 --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入数据到实际数补齐临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');   
   

 --2.只补齐均价, 发货额和发货数量无需补齐
      V_SQL := 
           'INSERT INTO '||V_TO_TABLE||' 
                (VERSION_ID,
                PERIOD_YEAR,
                PERIOD_ID,
                CUSTOM_ID,
                CUSTOM_CN_NAME,
                PARENT_CODE,
                PARENT_CN_NAME,
                PARENT_LEVEL,
                GRANULARITY_TYPE,
                GROUP_CODE,
                GROUP_CN_NAME,
                VIEW_FLAG,
                RMB_AVG_AMT,
                RMB_COST_AMT,
                SHIP_QUANTITY,
                APPEND_FLAG,
                APPEND_YEAR,
                CALIBER_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                CREATED_BY,
                CREATION_DATE,
                LAST_UPDATED_BY,
                LAST_UPDATE_DATE,
                DEL_FLAG,
                SCENARIO_FLAG
                ) 

 -- 按不同视角，补齐对应的重量级团队，以及采购信息维，补齐年均本
 WITH FILLER_TEMP AS
     (
      SELECT SS.*,
             FIRST_VALUE(SS.AVG_AMT) OVER(PARTITION BY SS.PARENT_CODE, SS.PARENT_LEVEL, SS.GRANULARITY_TYPE, SS.GROUP_CODE, SS.LV0_PROD_LIST_CODE, SS.OVERSEA_FLAG, SS.CALIBER_FLAG,SS.CUSTOM_ID,AVG_AMT_FLAG_F ORDER BY SS.PERIOD_ID) AS AVG_AMT_F, --向前新补齐的均价字段
             FIRST_VALUE(SS.PERIOD_ID) OVER(PARTITION BY SS.PARENT_CODE, SS.PARENT_LEVEL, SS.GRANULARITY_TYPE, SS.GROUP_CODE, SS.LV0_PROD_LIST_CODE, SS.OVERSEA_FLAG, SS.CALIBER_FLAG,SS.CUSTOM_ID,AVG_AMT_FLAG_F ORDER BY SS.PERIOD_ID) AS PERIOD_YEAR_F, --向前新补齐的年份字段
             FIRST_VALUE(SS.AVG_AMT) OVER(PARTITION BY SS.PARENT_CODE, SS.PARENT_LEVEL, SS.GRANULARITY_TYPE, SS.GROUP_CODE, SS.LV0_PROD_LIST_CODE, SS.OVERSEA_FLAG, SS.CALIBER_FLAG,SS.CUSTOM_ID,AVG_AMT_FLAG_B ORDER BY SS.PERIOD_ID DESC) AS AVG_AMT_B, --向后新补齐的均价字段
             FIRST_VALUE(SS.PERIOD_ID) OVER(PARTITION BY SS.PARENT_CODE, SS.PARENT_LEVEL, SS.GRANULARITY_TYPE, SS.GROUP_CODE, SS.LV0_PROD_LIST_CODE, SS.OVERSEA_FLAG, SS.CALIBER_FLAG,SS.CUSTOM_ID,AVG_AMT_FLAG_B ORDER BY SS.PERIOD_ID DESC) AS PERIOD_YEAR_B --向后新补齐的年份字段             
         FROM (SELECT S.*,
                      SUM(S.NULL_FLAG) OVER(PARTITION BY S.PARENT_CODE, S.PARENT_LEVEL, S.GRANULARITY_TYPE, S.GROUP_CODE, S.LV0_PROD_LIST_CODE, S.OVERSEA_FLAG, S.CALIBER_FLAG,S.CUSTOM_ID ORDER BY S.PERIOD_ID) AS AVG_AMT_FLAG_F, --分组累加0，1，标识；当结果值一样说明，后面的标识为0，金额是空；结果值一样的正好在分一个组，都取第一个值，即可向前补齐
                      SUM(S.NULL_FLAG) OVER(PARTITION BY S.PARENT_CODE, S.PARENT_LEVEL, S.GRANULARITY_TYPE, S.GROUP_CODE, S.LV0_PROD_LIST_CODE, S.OVERSEA_FLAG, S.CALIBER_FLAG,S.CUSTOM_ID ORDER BY S.PERIOD_ID DESC) AS AVG_AMT_FLAG_B
                  FROM ACTUAL_APD_TEMP S) SS)
                 
    SELECT '||V_VERSION_ID||' AS VERSION_ID,
           S.PERIOD_YEAR,
           S.PERIOD_ID,
           S.CUSTOM_ID,
           S.CUSTOM_CN_NAME,
           S.PARENT_CODE,
           S.PARENT_CN_NAME,
           S.PARENT_LEVEL,
           S.GRANULARITY_TYPE,
           S.GROUP_CODE,
           S.GROUP_CN_NAME,
           S.VIEW_FLAG,
           --GS_ENCRYPT(NVL(NVL(S.AVG_AMT,S.AVG_AMT_F), S.AVG_AMT_B),'''||V_KEYSTR||''',''AES128'',''CBC'',''SHA256'' ) AS RMB_AVG_AMT,
		   NVL(NVL(S.AVG_AMT,S.AVG_AMT_F), S.AVG_AMT_B) AS RMB_AVG_AMT,  --20240402修改 只补齐不解密
           S.RMB_COST_AMT,
           S.SHIP_QUANTITY , 
           S.APD_FLAG ,
           CASE  WHEN APD_FLAG=''Y'' AND AVG_AMT_F IS NOT NULL THEN PERIOD_YEAR_F 
                 WHEN APD_FLAG=''Y'' AND AVG_AMT_F IS  NULL  THEN PERIOD_YEAR_B
                ELSE '''' END AS  APPEND_YEAR,  
           S.CALIBER_FLAG,
           S.OVERSEA_FLAG,
           S.LV0_PROD_LIST_CODE,
           S.LV0_PROD_LIST_CN_NAME,           
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           ''S'' AS SCENARIO_FLAG
       FROM  FILLER_TEMP S; '; 


  EXECUTE IMMEDIATE V_SQL;

  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入新补齐的分视角均价实际数到'||V_TO_TABLE||'表, 版本号='||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
/*-------------------开始补齐预测数据------------------------------*/

  V_SQL := 
  'INSERT INTO '||V_TO_TABLE||' 
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     CUSTOM_ID,
     CUSTOM_CN_NAME,
     PARENT_CODE,
     PARENT_CN_NAME,
     PARENT_LEVEL,
     GRANULARITY_TYPE,
     GROUP_CODE,
     GROUP_CN_NAME,
     VIEW_FLAG,
     RMB_AVG_AMT,
     RMB_COST_AMT,
     SHIP_QUANTITY,
     APPEND_FLAG,
     APPEND_YEAR,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     SCENARIO_FLAG)

 WITH RECENT_ACTUAL_TEMP AS
     (
      --从实际数补齐表取最近一次实际月(即当前预测月-1)的重量级团队、采购维信息
      SELECT DISTINCT  T.VIEW_FLAG,
                       T.CUSTOM_ID,
                       T.CUSTOM_CN_NAME,
                       T.PARENT_CODE,
                       T.PARENT_CN_NAME,
                       T.PARENT_LEVEL,
                       T.GRANULARITY_TYPE,
                       T.GROUP_CODE,
                       T.GROUP_CN_NAME,
                       T.CALIBER_FLAG,
                       T.OVERSEA_FLAG,
                       T.LV0_PROD_LIST_CODE,
                       T.LV0_PROD_LIST_CN_NAME,
                       --TO_NUMBER(GS_DECRYPT(T.RMB_AVG_AMT,'''||v_keystr||''',''AES128'',''CBC'',''SHA256'')) AS RMB_AVG_AMT
					   T.RMB_AVG_AMT AS RMB_AVG_AMT  --20240402修改 只补齐不解密
        FROM '||V_TO_TABLE||' T
       WHERE T.PERIOD_ID =
             CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP, -1), ''YYYYMM'') AS BIGINT)),
    
   
    CROSS_JOIN_TEMP AS
     (
      --生成今年预测月份及预测月份之后, 所有的发散维
      SELECT  A.VIEW_FLAG,
              A.CUSTOM_ID,
              A.CUSTOM_CN_NAME,
              A.PARENT_CODE,
              A.PARENT_CN_NAME,
              A.PARENT_LEVEL,
              A.GRANULARITY_TYPE,
              A.GROUP_CODE,
              A.GROUP_CN_NAME,
              A.CALIBER_FLAG,
              A.OVERSEA_FLAG,
              A.LV0_PROD_LIST_CODE,
              A.LV0_PROD_LIST_CN_NAME,
              CAST(SUBSTR(TO_CHAR(B.PERIOD_ID), 1, 4) AS BIGINT) AS PERIOD_YEAR,
              B.PERIOD_ID,
              A.RMB_AVG_AMT
        FROM RECENT_ACTUAL_TEMP A,
              (SELECT TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP, LEVEL),''yyyymm'') AS PERIOD_ID
						FROM (SELECT GENERATE_SERIES(0,'''||V_COUNT_MONTH||''') AS LEVEL FROM DUAL)) B
						--202403版本预测数范围动态延长
		), 
    
     FCST_ITEM_TEMP AS
     (
      --取对应系统会计期的预测数
      SELECT DISTINCT I.PERIOD_ID, I.FCST_PERIOD_ID, I.ITEM_CODE,  GS_ENCRYPT(I.RMB_UNIT_PRICE ,'''||v_keystr||''', ''AES128'', ''CBC'', ''SHA256'') AS RMB_UNIT_PRICE
        FROM FIN_DM_OPT_FOI.FOI_DWK_GRP_PROCOST_PRICE_REL_I I
       WHERE I.FCST_PERIOD_ID <=
             TO_NUMBER('''||V_END_YEAR||''')
         AND I.PERIOD_ID IN (SELECT MAX(PERIOD_ID) FROM FIN_DM_OPT_FOI.FOI_DWK_GRP_PROCOST_PRICE_REL_I )
	   --202403版本预测数范围动态延长
		 )
       
    SELECT '||V_VERSION_ID||' AS VERSION_ID,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.CUSTOM_ID,
           T1.CUSTOM_CN_NAME,
           T1.PARENT_CODE,
           T1.PARENT_CN_NAME,
           T1.PARENT_LEVEL,
           T1.GRANULARITY_TYPE,
           T1.GROUP_CODE,
           T1.GROUP_CN_NAME,
           T1.VIEW_FLAG,
           --GS_ENCRYPT(DECODE(T2.ITEM_CODE, NULL, T1.RMB_AVG_AMT, T2.RMB_UNIT_PRICE),'''||v_keystr||''', ''AES128'', ''CBC'', ''SHA256'') AS RMB_AVG_AMT,
		   DECODE(T2.ITEM_CODE, NULL, T1.RMB_AVG_AMT, T2.RMB_UNIT_PRICE) AS RMB_AVG_AMT, --20240402修改 只补齐不解密
           NULL AS SHIP_QUANTITY,
           NULL AS RMB_COST_AMT,        
           DECODE(T2.ITEM_CODE, NULL, ''Y'', ''N'') AS APPEND_FLAG,
           '''',
           T1.CALIBER_FLAG,
           T1.OVERSEA_FLAG,
           T1.LV0_PROD_LIST_CODE,
           T1.LV0_PROD_LIST_CN_NAME,
          -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           ''Y'' AS SCENARIO_FLAG
      FROM CROSS_JOIN_TEMP T1
      LEFT JOIN FCST_ITEM_TEMP T2
        ON T1.GROUP_CODE = T2.ITEM_CODE
       AND T1.PERIOD_ID = T2.FCST_PERIOD_ID';
       


       EXECUTE IMMEDIATE V_SQL;   

  --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;

  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END

$$
/

