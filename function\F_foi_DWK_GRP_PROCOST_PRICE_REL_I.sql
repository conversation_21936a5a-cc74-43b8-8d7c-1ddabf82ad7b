-- Name: F_foi_DWK_GRP_PROCOST_PRICE_REL_I; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi."F_foi_DWK_GRP_PROCOST_PRICE_REL_I"(OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：2023-07-27
创建人  ：柳兴旺 l00521248 
背景描述：采购成本PROCOST预估价数据,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(p_period)：传入会计期（年月）,改成全量新增，所以不需要会计期参数
		  参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select FIN_DM_OPT_FOI.F_foi_DWK_GRP_PROCOST_PRICE_REL_I()

*/


declare
	v_sp_name varchar(50) := 'FIN_DM_OPT_FOI.F_foi_DWK_GRP_PROCOST_PRICE_REL_I';
	v_tbl_name varchar(50) := 'FIN_DM_OPT_FOI.foi_DWK_GRP_PROCOST_PRICE_REL_I';
	v_dml_row_count  number default 0 ;


begin
	x_success_flag := '1';       --1表示成功
	

	 --写日志,开始
	insert into FIN_DM_OPT_FOI.DM_FOI_LOG_T
		(log_id,
		 version_id,
		 sp_name,
		 para_list,
		 step_num,
		 cal_log_desc,
		 formula_sql_txt,
		 dml_row_count	,
		 result_status,
		 errbuf,
		 created_by,
		 creation_date)
	values
		(FIN_DM_OPT_FOI.DM_FOI_LOG_S.nextval,
		 null,
		 v_sp_name,
		 '',
		 1,                                             --第一步
		 '采购成本PROCOST预估价数据'||v_tbl_name||'：开始运行',
		 null,
		 v_dml_row_count,
		 x_success_flag,
		 null,
		 1,
		 current_timestamp);


		---支持重跑，清除目标表要插入会计期的数据
		delete from FIN_DM_OPT_FOI.foi_DWK_GRP_PROCOST_PRICE_REL_I;
		

		---插入目标表数据
		insert into FIN_DM_OPT_FOI.foi_DWK_GRP_PROCOST_PRICE_REL_I            ----采购成本PROCOST预估价数据
		(
			period_id,
			version_id,
			fcst_period_id,
			item_code,
			currency_code,
			rmb_unit_price,
			price_source,
			release_type,
			source_type,
			del_flag,
			creation_date,
			last_update_date,
			crt_cycle_id,
			dw_last_update_date
		)
			select 
			period_id,
			version_id,
			fcst_period_id,
			item_code,
			currency_code,
			rmb_unit_price,
			price_source,
			release_type,
			source_type,
			del_flag,
			creation_date,
			last_update_date,
			crt_cycle_id,
			dw_last_update_date
		from FIN_DM_OPT_FOI.foi_dwk_grp_procost_price_rel_i_temp
		  where 1=1
		  and SUBSTR(PERIOD_ID,1,4) = YEAR(CURRENT_TIMESTAMP)
  		  ;

	v_dml_row_count := sql%rowcount;          -- 收集数据量

	 -- 写结束日志
	insert into FIN_DM_OPT_FOI.DM_FOI_LOG_T
		(log_id,
		version_id,
		sp_name,
		para_list,
		step_num,
		cal_log_desc,
		formula_sql_txt,
		dml_row_count,
		result_status,
		errbuf,
		created_by,
		creation_date)
	values
		(
		FIN_DM_OPT_FOI.DM_FOI_LOG_S.nextval,
		null,
		v_sp_name,
		'',
		2,                                             --最后一步
		'采购成本PROCOST预估价数据'||v_tbl_name||'：结束运行',
		null,
		v_dml_row_count,
		'1',
		null,
		1,
		current_timestamp);
		
		--收集统计信息
    analyse FIN_DM_OPT_FOI.foi_DWK_GRP_PROCOST_PRICE_REL_I;
		

exception
  	when others then
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

	x_success_flag := '2001';       --2001表示失败
	

end;
$$
/

