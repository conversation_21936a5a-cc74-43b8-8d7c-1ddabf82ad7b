-- Name: f_dm_foc_dim_exam; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_dim_exam(f_industry_flag character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$



/*
创建时间:2024年2月5日20:59:13
创建人  :李志勇
最后修改时间:2024年6月2日
最后修改人:李志勇
背景描述:检查当月版本和上月版本维度是否有变化
参数描述:  x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_TOTAL_VIEW_INFO_D(版本信息表)
目标表:
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_DIM_EXAM('E')
*/
DECLARE
        V_SP_NAME                  VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_DIM_EXAM'; --存储过程名称
    V_STEP_MUM                     BIGINT        := 0; --步骤号
    V_SQL                          TEXT; --SQL逻辑
    V_CNT_CATEGORY                 BIGINT;
    V_CNT_ITEM                     BIGINT;
    V_FROM_TABLE                   varchar(100)  ;
    V_FRE_FIX                      varchar(50)  ;
    V_CURRENT_CATEGORY_MAX_VERSION int8;
    V_CURRENT_ITEM_MAX_VERSION     int8;
    V_LAST_ITEM_MAX_VERSION        int8;
    V_VERSION_TIME                 timestamp;
    V_CNT                          int8;

BEGIN
    X_RESULT_STATUS = 'SUCCESS';

    --1、开始日志
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
            (F_SP_NAME => V_SP_NAME,
             F_STEP_NUM => V_STEP_MUM,
             F_CAL_LOG_DESC => V_SP_NAME || '开始执行');

    IF F_INDUSTRY_FLAG = 'I' THEN
        V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T';
        V_FRE_FIX := 'DM_FOC';
    ELSE
        IF F_INDUSTRY_FLAG = 'E' THEN
            V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T';
            V_FRE_FIX := 'DM_FOC_ENERGY';
		ELSIF F_INDUSTRY_FLAG = 'IAS' THEN
            V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T';
            V_FRE_FIX := 'DM_FOC_IAS';
        END IF;
    END IF;


    /*2、获取当月最大CATEGORY&&ITEM版本号*/
    V_SQL := ' SELECT MAX(version_id)
        FROM ' || V_FROM_TABLE || '
        WHERE DATA_TYPE = ''CATEGORY''
          AND SUBSTR(CREATION_DATE, 1, 7) = SUBSTR(CURRENT_DATE, 1, 7)';
    EXECUTE IMMEDIATE V_SQL INTO V_CURRENT_CATEGORY_MAX_VERSION;

    V_SQL := 'SELECT MAX(version_id)
        FROM ' || V_FROM_TABLE || '
        WHERE DATA_TYPE = ''ITEM''
          AND SUBSTR(CREATION_DATE, 1, 7) = SUBSTR(CURRENT_DATE, 1, 7)';
    EXECUTE IMMEDIATE V_SQL INTO V_CURRENT_ITEM_MAX_VERSION;
    --V_LAST_ITEM_MAX_VERSION

    /*3、获取上月最大ITEM版本号*/
    V_SQL := 'SELECT MAX(version_id)
        FROM ' || V_FROM_TABLE || '
        WHERE DATA_TYPE = ''ITEM''
          AND SUBSTR(CREATION_DATE, 1, 7) = SUBSTR(ADD_MONTHS(SYSDATE, -1), 1, 7)';
    EXECUTE IMMEDIATE V_SQL INTO V_LAST_ITEM_MAX_VERSION;

    V_SQL := '
select count(1) from  (select * from(
            select DISTINCT  null as self_parent_dimension_value, LV0_PROD_RND_TEAM_CODE||''_LV0'' as dimension_value, LV0_PROD_RD_TEAM_CN_NAME as dimension_display_value,view_flag,''LV0'' as group_level FROM fin_dm_opt_foi.'||V_FRE_FIX||'_total_view_info_d
            where VIEW_FLAG = ''0'' AND DEL_FLAG = ''N''
            and version_id = ' || V_CURRENT_CATEGORY_MAX_VERSION || '  --当月版本号 CATEGORY
            union all
            select DISTINCT LV0_PROD_RND_TEAM_CODE||''_LV0'' as self_parent_dimension_value, LV1_PROD_RND_TEAM_CODE||''_LV1'' as dimension_value,LV1_PROD_RD_TEAM_CN_NAME as dimension_display_value, view_flag,''LV1'' as group_level  FROM fin_dm_opt_foi.'||V_FRE_FIX||'_total_view_info_d
            where VIEW_FLAG = ''1'' AND DEL_FLAG = ''N''
            and version_id = ' || V_CURRENT_CATEGORY_MAX_VERSION || '
            union all
            select DISTINCT LV1_PROD_RND_TEAM_CODE||''_LV1'' as self_parent_dimension_value, LV2_PROD_RND_TEAM_CODE||''_LV2'' as dimension_value,LV2_PROD_RD_TEAM_CN_NAME as dimension_display_value, view_flag,''LV2'' as group_level  FROM fin_dm_opt_foi.'||V_FRE_FIX||'_total_view_info_d
            where VIEW_FLAG = ''2'' AND DEL_FLAG = ''N''
            and version_id = ' || V_CURRENT_CATEGORY_MAX_VERSION || '
            union all
            select DISTINCT LV2_PROD_RND_TEAM_CODE||''_LV2'' as self_parent_dimension_value, LV3_PROD_RND_TEAM_CODE||''_LV3'' as dimension_value,LV3_PROD_RD_TEAM_CN_NAME as dimension_display_value, view_flag,''LV3'' as group_level FROM fin_dm_opt_foi.'||V_FRE_FIX||'_total_view_info_d
            where VIEW_FLAG = ''3'' AND DEL_FLAG = ''N''
            and version_id = ' || V_CURRENT_CATEGORY_MAX_VERSION || '
            )

	except		 --不返回在第一个表中出现但在第二个表中不出现的行
	select * from(
            select DISTINCT  null as self_parent_dimension_value, LV0_PROD_RND_TEAM_CODE||''_LV0'' as dimension_value, LV0_PROD_RD_TEAM_CN_NAME as dimension_display_value,view_flag,''LV0'' as group_level FROM fin_dm_opt_foi.'||V_FRE_FIX||'_top_item_info_t
            where VIEW_FLAG = ''0'' AND DEL_FLAG = ''N''
            and version_id = ' || V_LAST_ITEM_MAX_VERSION || ' --上月版本号 ITEM
            union all
            select DISTINCT LV0_PROD_RND_TEAM_CODE||''_LV0'' as self_parent_dimension_value, LV1_PROD_RND_TEAM_CODE||''_LV1'' as dimension_value,LV1_PROD_RD_TEAM_CN_NAME as dimension_display_value, view_flag,''LV1'' as group_level FROM fin_dm_opt_foi.'||V_FRE_FIX||'_top_item_info_t
            where VIEW_FLAG = ''1'' AND DEL_FLAG = ''N''
            and version_id = ' || V_LAST_ITEM_MAX_VERSION || '
            union all
            select DISTINCT LV1_PROD_RND_TEAM_CODE||''_LV1'' as self_parent_dimension_value, LV2_PROD_RND_TEAM_CODE||''_LV2'' as dimension_value,LV2_PROD_RD_TEAM_CN_NAME as dimension_display_value, view_flag,''LV2'' as group_level FROM fin_dm_opt_foi.'||V_FRE_FIX||'_top_item_info_t
            where VIEW_FLAG = ''2'' AND DEL_FLAG = ''N''
            and version_id = ' || V_LAST_ITEM_MAX_VERSION || '
            union all
            select DISTINCT LV2_PROD_RND_TEAM_CODE||''_LV2'' as self_parent_dimension_value, LV3_PROD_RND_TEAM_CODE||''_LV3'' as dimension_value,LV3_PROD_RD_TEAM_CN_NAME as dimension_display_value, view_flag,''LV3'' as group_level  FROM fin_dm_opt_foi.'||V_FRE_FIX||'_top_item_info_t
            where VIEW_FLAG = ''3'' AND DEL_FLAG = ''N''
            and version_id = ' || V_LAST_ITEM_MAX_VERSION || '
            ) )';
   --DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL INTO V_CNT;

    IF V_CNT <> 0 THEN
        V_STEP_MUM := V_STEP_MUM + 1;
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
                (F_SP_NAME => V_SP_NAME,
                 F_STEP_NUM => V_STEP_MUM,
                 F_CAL_LOG_DESC => '【维度信息有变化！】当前月份和上月版本号维表对比结果：当前月份 CATEGORY 版本号为 ： ' ||
                                   V_CURRENT_CATEGORY_MAX_VERSION || '，上月ITEM版本号为：' || V_LAST_ITEM_MAX_VERSION ||
                                   '。维度变化数量为' || V_CNT,
                 F_DML_ROW_COUNT => 0,
                 F_RESULT_STATUS => X_RESULT_STATUS,
                 F_ERRBUF => 'ERROR');
        RETURN 'ERROR: 维度信息有变化';

    ELSE
        --写入日志
        V_STEP_MUM := V_STEP_MUM + 1;
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
                (F_SP_NAME => V_SP_NAME,
                 F_STEP_NUM => V_STEP_MUM,
                 F_CAL_LOG_DESC => '维表信息对比完成,当月和上月维度信息无变化！',
                 F_DML_ROW_COUNT => 0,
                 F_RESULT_STATUS => X_RESULT_STATUS,
                 F_ERRBUF => 'SUCCESS');

        RETURN 'SUCCESS';
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        X_RESULT_STATUS := 'FAILED';

        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
                (F_SP_NAME => V_SP_NAME,
                 F_CAL_LOG_DESC => V_SP_NAME || '运行失败',
                 F_RESULT_STATUS => X_RESULT_STATUS,
                 F_ERRBUF => SQLSTATE || ':' || SQLERRM
                );

END



$$
/

