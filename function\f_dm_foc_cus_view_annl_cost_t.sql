-- Name: f_dm_foc_cus_view_annl_cost_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_cus_view_annl_cost_t(f_industry_flag character varying, f_dimension_type character varying, f_keystr character varying, f_custom_id character varying DEFAULT NULL::character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
  创建时间：2023-08-17
  创建人  ：曹琼
  修改时间：2023-12-22
  修改人：twx1139790
  背景描述：自由组合下ITEM的年均本基础表(补齐后，加密)
  参数描述：f_dimension_type : 维度类型(通用颗粒度：U,盈利颗粒度：P,量纲颗粒度：D),x_success_flag ：是否成功
  事例    ：select fin_dm_opt_foi.f_dm_foc_cus_view_annl_cost_t()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_CUS_VIEW_ANNL_COST_T'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT;
  V_KEYSTR  varchar2(50) := F_KEYSTR;  -- 密钥入参

    -- 7月版本需求新增
    V_SQL        TEXT;   --SQL逻辑
    V_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
    V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
    V_L1_NAME VARCHAR(200);
    V_L2_NAME VARCHAR(200);
    V_IN_LV3 VARCHAR(200);
    V_IN_PFT VARCHAR(200);
    V_FROM_TABLE VARCHAR(200);
    V_TO_TABLE VARCHAR(200);
    V_TMP_TABLE VARCHAR(200);
    V_SQL_LV3   TEXT;
    V_SQL_PFT   TEXT;
    V_TMP_TABLE2 VARCHAR(200);
    V_DIM_TABLE  VARCHAR(200);
    V_JOIN_SQL   TEXT;
    V_FILDER_SQL  TEXT;
    V_DIMENSION_CODE                               VARCHAR(200); --量纲颗粒度
    V_DIMENSION_CN_NAME                            VARCHAR(200);
    V_DIMENSION_EN_NAME                            VARCHAR(200);
    V_DIMENSION_SUBCATEGORY_CODE                   VARCHAR(200); --量纲子类
    V_DIMENSION_SUBCATEGORY_CN_NAME                VARCHAR(200);
    V_DIMENSION_SUBCATEGORY_EN_NAME                VARCHAR(200);
    V_DIMENSION_SUB_DETAIL_CODE                    VARCHAR(200); --量纲子类明细
    V_DIMENSION_SUB_DETAIL_CN_NAME                 VARCHAR(200);
    V_DIMENSION_SUB_DETAIL_EN_NAME                 VARCHAR(200);
    V_SQL_LV3_PROD_RND_TEAM_CODE            VARCHAR(200);
    V_SQL_LV3_PROD_RD_TEAM_CN_NAME          VARCHAR(200);
    V_SQL_L1_NAME                           VARCHAR(200);
    V_SQL_L2_NAME                           VARCHAR(200);
    V_SQL_DIMENSION_CODE                    VARCHAR(200);
    V_SQL_DIMENSION_CN_NAME                 VARCHAR(200);
    V_SQL_DIMENSION_SUBCATEGORY_CODE        VARCHAR(200);
    V_SQL_DIMENSION_SUBCATEGORY_CN_NAME     VARCHAR(200);
    V_SQL_DIMENSION_SUB_DETAIL_CODE         VARCHAR(200);
    V_SQL_DIMENSION_SUB_DETAIL_CN_NAME      VARCHAR(200);
    V_SETS_LV3_PROD VARCHAR(200);
    V_SETS_L1_NAME  VARCHAR(200);
    V_SETS_L2_NAME  VARCHAR(200);
    V_SETS_DIMENSION   VARCHAR(200);
    V_SETS_DIMENSION_SUBCATEGORY   VARCHAR(200);
    V_SETS_DIMENSION_SUB_DETAIL    VARCHAR(200);
    V_J_LV3_PROD_RND_TEAM_CODE VARCHAR(200);
    V_J_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
    V_J_L1_NAME VARCHAR(200);
    V_J_L2_NAME VARCHAR(200);
    V_J_DIMENSION_CODE VARCHAR(200);
    V_J_DIMENSION_CN_NAME  VARCHAR(200);
    V_J_DIMENSION_SUBCATEGORY_CODE VARCHAR(200);
    V_J_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200);
    V_J_DIMENSION_SUB_DETAIL_CODE     VARCHAR(200);
    V_J_DIMENSION_SUB_DETAIL_CN_NAME  VARCHAR(200);
    V_SQL_LVL  VARCHAR(500);
    V_MIN_PROD  TEXT;
    V_C_DIM_ID     VARCHAR(200);
    V_C_DIM_NAME   VARCHAR(200);
    V_C_MAX_LEVEL  VARCHAR(200);

    -- 202401版本新增
    V_SPART_TOTAL VARCHAR(200);
    V_IN_SPART VARCHAR(200);
    V_REL_SPART VARCHAR(500);
    V_J_SPART_CODE VARCHAR(200);
    V_J_SPART_NAME VARCHAR(200);
    V_SETS_SPART VARCHAR(500);

    -- 202405版本新增
    V_DIFF_COLUMN_TOTAL VARCHAR(200);
    V_J_DIFF_COLUMN_CODE VARCHAR(200);
    V_J_DIFF_COLUMN_NAME VARCHAR(200);
    V_SETS_DIFF_COLUMN VARCHAR(500);
    V_VERSION_TABLE VARCHAR(100);

  CURSOR C_DIM IS SELECT  CUSTOM_ID,CUSTOM_CN_NAME,
                           MAX(DECODE(GROUP_LEVEL,'ITEM','01ITEM','CATEGORY','02CATEGORY','MODL','03MODL','CEG','04CEG','SPART','05SPART','SUB_DETAIL','06SUB_DETAIL','SUBCATEGORY','07SUBCATEGORY','DIMENSION','08DIMENSION', 'L2','09L2','L1','10L1','LV3','11LV3','LV2','12LV2','LV1','13LV1','LV0','14LV0')) AS MAX_LEVEL
                   FROM FIN_DM_OPT_FOI.DM_FOC_CUSTOM_COMB_D 
                   WHERE DEL_FLAG='N' AND ENABLE_FLAG='Y'  
                   AND PAGE_FLAG IN ('ALL_ANNUAL','ANNUAL')
                   AND GRANULARITY_TYPE = F_DIMENSION_TYPE
                   GROUP BY CUSTOM_ID,CUSTOM_CN_NAME ;

  CURSOR C_DIM_ENERGY IS SELECT  CUSTOM_ID,CUSTOM_CN_NAME,
                           MAX(DECODE(GROUP_LEVEL,'ITEM','01ITEM','CATEGORY','02CATEGORY','MODL','03MODL','CEG','04CEG','SPART','05SPART','SUB_DETAIL','06SUB_DETAIL','SUBCATEGORY','07SUBCATEGORY','DIMENSION','08DIMENSION', 'L2','09L2','L1','10L1','COA','11COA','LV3','12LV3','LV2','13LV2','LV1','14LV1','LV0','15LV0')) AS MAX_LEVEL
                   FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_CUSTOM_COMB_D
                   WHERE DEL_FLAG='N' AND ENABLE_FLAG='Y'
                   AND PAGE_FLAG IN ('ALL_ANNUAL','ANNUAL')
                   AND GRANULARITY_TYPE = F_DIMENSION_TYPE
                   GROUP BY CUSTOM_ID,CUSTOM_CN_NAME;
                  
   CURSOR C_DIM_IAS IS SELECT  CUSTOM_ID,CUSTOM_CN_NAME,
                           MAX(DECODE(GROUP_LEVEL,'ITEM','01ITEM','CATEGORY','02CATEGORY','MODL','03MODL','CEG','04CEG','SPART','05SPART','SUB_DETAIL','06SUB_DETAIL','SUBCATEGORY','07SUBCATEGORY','DIMENSION','08DIMENSION', 'L2','09L2','L1','10L1','LV4','11LV4','LV3','12LV3','LV2','13LV2','LV1','14LV1','LV0','15LV0')) AS MAX_LEVEL
                   FROM FIN_DM_OPT_FOI.DM_FOC_IAS_CUSTOM_COMB_D 
                   WHERE DEL_FLAG='N' AND ENABLE_FLAG='Y'  
                   AND PAGE_FLAG IN ('ALL_ANNUAL','ANNUAL')
                   AND GRANULARITY_TYPE = F_DIMENSION_TYPE
                   GROUP BY CUSTOM_ID,CUSTOM_CN_NAME ;

BEGIN
  X_RESULT_STATUS = '1';

  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  -- 判断产业项目为ICT还是数字能源
  IF F_INDUSTRY_FLAG = 'I' THEN   -- ICT
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T ';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_CUS_VIEW_ANNL_COST_T'; --目标表
     V_DIM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_CUSTOM_COMB_D';       --组合维度表
     IF F_DIMENSION_TYPE = 'U' THEN -- 通用颗粒度
            V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T';--来源表
            V_TMP_TABLE := 'AVG_APD_TMP';   -- 临时表
            V_TMP_TABLE2 := 'DELETE_ZERO_TMP';
        ELSIF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
            V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_ITEM_T';--来源表
            V_TMP_TABLE := 'PFT_AVG_APD_TMP';   -- 临时表
            V_TMP_TABLE2 := 'PFT_DELETE_ZERO_TMP';
        ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
            V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T';--来源表
            V_TMP_TABLE := 'DMS_AVG_APD_TMP';   -- 临时表
            V_TMP_TABLE2 := 'DMS_DELETE_ZERO_TMP';
     ELSE
      NULL;
     END IF;
  ELSIF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T ';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_CUS_VIEW_ANNL_COST_T'; --目标表
     V_DIM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_CUSTOM_COMB_D';       --组合维度表
     IF F_DIMENSION_TYPE = 'U' THEN -- 通用颗粒度
            V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_T';--来源表
            V_TMP_TABLE := 'ENERGY_AVG_APD_TMP';   -- 临时表
            V_TMP_TABLE2 := 'ENERGY_DELETE_ZERO_TMP';
        ELSIF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
            V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_MID_MONTH_ITEM_T';--来源表
            V_TMP_TABLE := 'ENERGY_PFT_AVG_APD_TMP';   -- 临时表
            V_TMP_TABLE2 := 'ENERGY_PFT_DELETE_ZERO_TMP';
        ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
            V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MID_MONTH_ITEM_T';--来源表
            V_TMP_TABLE := 'ENERGY_DMS_AVG_APD_TMP';   -- 临时表
            V_TMP_TABLE2 := 'ENERGY_DMS_DELETE_ZERO_TMP';
     ELSE
      NULL;
     END IF;
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407版本新增IAS
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T ';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_CUS_VIEW_ANNL_COST_T'; --目标表
     V_DIM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_CUSTOM_COMB_D';       --组合维度表
     IF F_DIMENSION_TYPE = 'U' THEN -- 通用颗粒度
            V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_T';--来源表
            V_TMP_TABLE := 'IAS_AVG_APD_TMP';   -- 临时表
            V_TMP_TABLE2 := 'IAS_DELETE_ZERO_TMP';
        ELSIF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
            V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_MID_MONTH_ITEM_T';--来源表
            V_TMP_TABLE := 'IAS_PFT_AVG_APD_TMP';   -- 临时表
            V_TMP_TABLE2 := 'IAS_PFT_DELETE_ZERO_TMP';
        ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
            V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MID_MONTH_ITEM_T';--来源表
            V_TMP_TABLE := 'IAS_DMS_AVG_APD_TMP';   -- 临时表
            V_TMP_TABLE2 := 'IAS_DMS_DELETE_ZERO_TMP';
     ELSE
      NULL;
     END IF;
  END IF;

    --版本号赋值
  V_SQL := '
    SELECT VERSION_ID 
    FROM
        '||V_VERSION_TABLE||'
    WHERE
        DEL_FLAG = ''N''
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = ''CATEGORY''
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1';
    EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;

  --1.清空目标表数据:
  DBMS_OUTPUT.PUT_LINE('0:-----------');
   IF  F_CUSTOM_ID IS NULL THEN

     V_SQL := 'DELETE FROM '||V_TO_TABLE||' WHERE  GRANULARITY_TYPE = '''||F_DIMENSION_TYPE||''' ;';
     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL;
     DBMS_OUTPUT.PUT_LINE('1:-----------'||V_SQL);
  ELSE
   V_SQL := 'DELETE FROM '||V_TO_TABLE||' WHERE CUSTOM_ID = '||F_CUSTOM_ID||';';
   EXECUTE IMMEDIATE V_SQL;
   DBMS_OUTPUT.PUT_LINE('2:-----------'||V_SQL);
  END  IF;


  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'的表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');


     --重置变量入参
     V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';--7月版本需求新增
     V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
     V_L1_NAME := 'L1_NAME,';
     V_L2_NAME := 'L2_NAME,';
     V_DIMENSION_CODE                := 'DIMENSION_CODE,';
     V_DIMENSION_CN_NAME             := 'DIMENSION_CN_NAME,';
     V_DIMENSION_SUBCATEGORY_CODE    := 'DIMENSION_SUBCATEGORY_CODE,';
     V_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
     V_DIMENSION_SUB_DETAIL_CODE     := 'DIMENSION_SUB_DETAIL_CODE,';
     V_DIMENSION_SUB_DETAIL_CN_NAME  := 'DIMENSION_SUB_DETAIL_CN_NAME,';

     V_SQL_LV3_PROD_RND_TEAM_CODE :=    'T.LV3_PROD_RND_TEAM_CODE,';  --7月版本需求新增
     V_SQL_LV3_PROD_RD_TEAM_CN_NAME :=  'T.LV3_PROD_RD_TEAM_CN_NAME,';
     V_SQL_L1_NAME := 'T.L1_NAME,';
     V_SQL_L2_NAME := 'T.L2_NAME,';
     V_SQL_DIMENSION_CODE                := 'T.DIMENSION_CODE,';
     V_SQL_DIMENSION_CN_NAME             := 'T.DIMENSION_CN_NAME,';
     V_SQL_DIMENSION_SUBCATEGORY_CODE    := 'T.DIMENSION_SUBCATEGORY_CODE,';
     V_SQL_DIMENSION_SUBCATEGORY_CN_NAME := 'T.DIMENSION_SUBCATEGORY_CN_NAME,';
     V_SQL_DIMENSION_SUB_DETAIL_CODE     := 'T.DIMENSION_SUB_DETAIL_CODE,';
     V_SQL_DIMENSION_SUB_DETAIL_CN_NAME  := 'T.DIMENSION_SUB_DETAIL_CN_NAME,';

     V_SETS_LV3_PROD := '(LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME),';
     V_SETS_L1_NAME := '(L1_NAME),';
     V_SETS_L2_NAME := '(L2_NAME),';

     V_SETS_DIMENSION  := '(DIMENSION_CODE,DIMENSION_CN_NAME),';
     V_SETS_DIMENSION_SUBCATEGORY  := '(DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME),';
     V_SETS_DIMENSION_SUB_DETAIL   := '(DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME),';

     V_J_LV3_PROD_RND_TEAM_CODE :=    '||LV3_PROD_RND_TEAM_CODE';
     V_J_LV3_PROD_RD_TEAM_CN_NAME :=  '||LV3_PROD_RD_TEAM_CN_NAME';
     V_J_L1_NAME := '||L1_NAME';
     V_J_L2_NAME := '||L2_NAME';
     V_J_DIMENSION_CODE                := '||DIMENSION_CODE';
     V_J_DIMENSION_CN_NAME             := '||DIMENSION_CN_NAME';
     V_J_DIMENSION_SUBCATEGORY_CODE    := '||DIMENSION_SUBCATEGORY_CODE';
     V_J_DIMENSION_SUBCATEGORY_CN_NAME := '||DIMENSION_SUBCATEGORY_CN_NAME';
     V_J_DIMENSION_SUB_DETAIL_CODE     := '||DIMENSION_SUB_DETAIL_CODE';
     V_J_DIMENSION_SUB_DETAIL_CN_NAME  := '||DIMENSION_SUB_DETAIL_CN_NAME';

     V_IN_LV3 := 'T1.LV3_PROD_RND_TEAM_CODE,T1.LV3_PROD_RD_TEAM_CN_NAME,';
     V_IN_PFT := 'T1.L1_NAME,T1.L2_NAME,';
     V_SQL_LV3 := ' AND NVL(T1.LV3_PROD_RND_TEAM_CODE,''SNULL'') = NVL(T2.LV3_PROD_RND_TEAM_CODE,''SNULL'')';
     V_SQL_PFT := ' AND NVL(T1.L1_NAME,''SNULL'') = NVL(T2.L1_NAME,''SNULL'')
                    AND NVL(T1.L2_NAME,''SNULL'') = NVL(T2.L2_NAME,''SNULL'')';

    --通用颗粒度的维度时，不需要L1和L2字段
    IF F_DIMENSION_TYPE = 'U' THEN
         V_L1_NAME := '';
         V_L2_NAME := '';
         V_DIMENSION_CODE                := '';
         V_DIMENSION_CN_NAME             := '';
         V_DIMENSION_SUBCATEGORY_CODE    := '';
         V_DIMENSION_SUBCATEGORY_CN_NAME := '';
         V_DIMENSION_SUB_DETAIL_CODE     := '';
         V_DIMENSION_SUB_DETAIL_CN_NAME  := '';
         V_SQL_L1_NAME := '';
         V_SQL_L2_NAME := '';
         V_SQL_DIMENSION_CODE                := '';
         V_SQL_DIMENSION_CN_NAME             := '';
         V_SQL_DIMENSION_SUBCATEGORY_CODE    := '';
         V_SQL_DIMENSION_SUBCATEGORY_CN_NAME := '';
         V_SQL_DIMENSION_SUB_DETAIL_CODE     := '';
         V_SQL_DIMENSION_SUB_DETAIL_CN_NAME  := '';
         V_SETS_L1_NAME := '';
         V_SETS_L2_NAME := '';
         V_SETS_DIMENSION  := '';
         V_SETS_DIMENSION_SUBCATEGORY  := '';
         V_SETS_DIMENSION_SUB_DETAIL   := '';
         V_J_L1_NAME := '';
         V_J_L2_NAME := '';
         V_J_DIMENSION_CODE                := '';
         V_J_DIMENSION_CN_NAME             := '';
         V_J_DIMENSION_SUBCATEGORY_CODE    := '';
         V_J_DIMENSION_SUBCATEGORY_CN_NAME := '';
         V_J_DIMENSION_SUB_DETAIL_CODE     := '';
         V_J_DIMENSION_SUB_DETAIL_CN_NAME  := '';
         V_SQL_LVL := 'LV3_PROD_RND_TEAM_CODE,''LV3'',' ;
         V_IN_PFT := '';
         V_SQL_PFT := '';
      IF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407版本新增IAS
         V_DIFF_COLUMN_TOTAL := 'LV4_PROD_RND_TEAM_CODE,
                         LV4_PROD_RD_TEAM_CN_NAME,';
         V_J_DIFF_COLUMN_CODE := '||LV4_PROD_RND_TEAM_CODE';
         V_J_DIFF_COLUMN_NAME := '||LV4_PROD_RD_TEAM_CN_NAME';
         V_SETS_DIFF_COLUMN   := '(LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME),';
         V_SQL_LVL := 'LV3_PROD_RND_TEAM_CODE,''LV3'',
                       LV4_PROD_RND_TEAM_CODE,''LV4'',';
         V_JOIN_SQL := 'AND V.LV0_PROD_RND_TEAM_CODE||V.LV1_PROD_RND_TEAM_CODE||V.LV2_PROD_RND_TEAM_CODE||V.LV3_PROD_RND_TEAM_CODE||V.LV4_PROD_RND_TEAM_CODE
                         = DECODE(V.LV0_PROD_RND_TEAM_CODE,'''','''',T.LV0_PROD_RND_TEAM_CODE)||
                            DECODE(V.LV1_PROD_RND_TEAM_CODE,'''','''',T.LV1_PROD_RND_TEAM_CODE)||
                            DECODE(V.LV2_PROD_RND_TEAM_CODE,'''','''',T.LV2_PROD_RND_TEAM_CODE)||
                            DECODE(V.LV3_PROD_RND_TEAM_CODE,'''','''',T.LV3_PROD_RND_TEAM_CODE)||
                            DECODE(V.LV4_PROD_RND_TEAM_CODE,'''','''',T.LV4_PROD_RND_TEAM_CODE)';
      ELSE   -- ICT/数字能源
        V_JOIN_SQL := 'AND V.LV0_PROD_RND_TEAM_CODE||V.LV1_PROD_RND_TEAM_CODE||V.LV2_PROD_RND_TEAM_CODE||V.LV3_PROD_RND_TEAM_CODE
                         = DECODE(V.LV0_PROD_RND_TEAM_CODE,'''','''',T.LV0_PROD_RND_TEAM_CODE)||
                            DECODE(V.LV1_PROD_RND_TEAM_CODE,'''','''',T.LV1_PROD_RND_TEAM_CODE)||
                            DECODE(V.LV2_PROD_RND_TEAM_CODE,'''','''',T.LV2_PROD_RND_TEAM_CODE)||
                            DECODE(V.LV3_PROD_RND_TEAM_CODE,'''','''',T.LV3_PROD_RND_TEAM_CODE)';
      END IF;

    --盈利颗粒度的维度时，不需要LV3字段
    ELSIF F_DIMENSION_TYPE = 'P' THEN
        V_LV3_PROD_RND_TEAM_CODE := '';
        V_LV3_PROD_RD_TEAM_CN_NAME := '';
        V_DIMENSION_CODE                := '';
        V_DIMENSION_CN_NAME             := '';
        V_DIMENSION_SUBCATEGORY_CODE    := '';
        V_DIMENSION_SUBCATEGORY_CN_NAME := '';
        V_DIMENSION_SUB_DETAIL_CODE     := '';
        V_DIMENSION_SUB_DETAIL_CN_NAME  := '';

        V_SQL_LV3_PROD_RND_TEAM_CODE := '';
        V_SQL_LV3_PROD_RD_TEAM_CN_NAME := '';
        V_SQL_DIMENSION_CODE                := '';
        V_SQL_DIMENSION_CN_NAME             := '';
        V_SQL_DIMENSION_SUBCATEGORY_CODE    := '';
        V_SQL_DIMENSION_SUBCATEGORY_CN_NAME := '';
        V_SQL_DIMENSION_SUB_DETAIL_CODE     := '';
        V_SQL_DIMENSION_SUB_DETAIL_CN_NAME  := '';

         V_SETS_LV3_PROD := '';
         V_SETS_DIMENSION  := '';
         V_SETS_DIMENSION_SUBCATEGORY  := '';
         V_SETS_DIMENSION_SUB_DETAIL   := '';

         V_J_LV3_PROD_RND_TEAM_CODE  := '';
         V_J_LV3_PROD_RD_TEAM_CN_NAME  := '';
         V_J_DIMENSION_CODE                := '';
         V_J_DIMENSION_CN_NAME             := '';
         V_J_DIMENSION_SUBCATEGORY_CODE    := '';
         V_J_DIMENSION_SUBCATEGORY_CN_NAME := '';
         V_J_DIMENSION_SUB_DETAIL_CODE     := '';
         V_J_DIMENSION_SUB_DETAIL_CN_NAME  := '';
         V_SQL_LVL := 'L1_NAME,''L1'' ,
                       L2_NAME,''L2'' ,' ;
        V_IN_LV3 := '';
        V_SQL_LV3 := '';

        V_JOIN_SQL := 'AND V.LV0_PROD_RND_TEAM_CODE||V.LV1_PROD_RND_TEAM_CODE||V.LV2_PROD_RND_TEAM_CODE||
                            V.L1_NAME||V.L2_NAME
                         = DECODE(V.LV0_PROD_RND_TEAM_CODE,'''','''',T.LV0_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV1_PROD_RND_TEAM_CODE,'''','''',T.LV1_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV2_PROD_RND_TEAM_CODE,'''','''',T.LV2_PROD_RND_TEAM_CODE)||
                           DECODE(V.L1_NAME,'''','''',T.L1_NAME)||
                           DECODE(V.L2_NAME,'''','''',T.L2_NAME)';



    --量纲颗粒度的维度时，不需要L1，L2字段
    ELSIF F_DIMENSION_TYPE = 'D' THEN
        V_L1_NAME := '';
        V_L2_NAME := '';
        V_SQL_L1_NAME := '';
        V_SQL_L2_NAME := '';

        V_SETS_L1_NAME := '';
        V_SETS_L2_NAME := '';

        V_J_L1_NAME := '';
        V_J_L2_NAME := '';
        V_IN_LV3 := '';
        V_SQL_LV3 := '';
        V_SPART_TOTAL := 'SPART_CODE,
                          SPART_CN_NAME,';
        V_J_SPART_CODE := '||SPART_CODE';
        V_J_SPART_NAME := '||SPART_CN_NAME';
        V_SETS_SPART   := '(SPART_CODE,SPART_CN_NAME),';
        
      IF F_INDUSTRY_FLAG = 'E' THEN 
         V_DIFF_COLUMN_TOTAL := 'COA_CODE,
                         COA_CN_NAME,';
         V_J_DIFF_COLUMN_CODE := '||COA_CODE';
         V_J_DIFF_COLUMN_NAME := '||COA_CN_NAME';
         V_SETS_DIFF_COLUMN   := '(COA_CODE,COA_CN_NAME),';
         V_SQL_LVL := 'LV3_PROD_RND_TEAM_CODE,''LV3'',
                       DIMENSION_CODE,''DIMENSION'',
                       DIMENSION_SUBCATEGORY_CODE,''SUBCATEGORY'',
                       DIMENSION_SUB_DETAIL_CODE,''SUB_DETAIL'',
                       SPART_CODE,''SPART'',
                       COA_CODE,''COA'', ' ;
         V_JOIN_SQL := 'AND V.LV0_PROD_RND_TEAM_CODE||V.LV1_PROD_RND_TEAM_CODE||V.LV2_PROD_RND_TEAM_CODE||V.LV3_PROD_RND_TEAM_CODE||
                            V.SPART_CODE||V.DIMENSION_CODE||V.DIMENSION_SUBCATEGORY_CODE||V.DIMENSION_SUB_DETAIL_CODE
                          = DECODE(V.LV0_PROD_RND_TEAM_CODE,'''','''',T.LV0_PROD_RND_TEAM_CODE)||
                            DECODE(V.LV1_PROD_RND_TEAM_CODE,'''','''',T.LV1_PROD_RND_TEAM_CODE)||
                            DECODE(V.LV2_PROD_RND_TEAM_CODE,'''','''',T.LV2_PROD_RND_TEAM_CODE)||
                            DECODE(V.LV3_PROD_RND_TEAM_CODE,'''','''',T.LV3_PROD_RND_TEAM_CODE)||
                            DECODE(V.SPART_CODE,'''','''',T.SPART_CODE)||
                            DECODE(V.COA_CODE,'''','''',T.COA_CODE)||   -- 202405版本新增
                            DECODE(V.DIMENSION_CODE,'''','''',T.DIMENSION_CODE)||
                            DECODE(V.DIMENSION_SUBCATEGORY_CODE,'''','''',T.DIMENSION_SUBCATEGORY_CODE)||
                            DECODE(V.DIMENSION_SUB_DETAIL_CODE,'''','''',T.DIMENSION_SUB_DETAIL_CODE)';
      ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407版本新增IAS
         V_DIFF_COLUMN_TOTAL := 'LV4_PROD_RND_TEAM_CODE,
                         LV4_PROD_RD_TEAM_CN_NAME,';
         V_J_DIFF_COLUMN_CODE := '||LV4_PROD_RND_TEAM_CODE';
         V_J_DIFF_COLUMN_NAME := '||LV4_PROD_RD_TEAM_CN_NAME';
         V_SETS_DIFF_COLUMN   := '(LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME),';
         V_SQL_LVL := 'LV3_PROD_RND_TEAM_CODE,''LV3'',
                       DIMENSION_CODE,''DIMENSION'',
                       DIMENSION_SUBCATEGORY_CODE,''SUBCATEGORY'',
                       DIMENSION_SUB_DETAIL_CODE,''SUB_DETAIL'',
                       SPART_CODE,''SPART'',
                       LV4_PROD_RND_TEAM_CODE,''LV4'', ' ;
         V_JOIN_SQL := 'AND V.LV0_PROD_RND_TEAM_CODE||V.LV1_PROD_RND_TEAM_CODE||V.LV2_PROD_RND_TEAM_CODE||V.LV3_PROD_RND_TEAM_CODE||
                            V.SPART_CODE||V.LV4_PROD_RND_TEAM_CODE||V.DIMENSION_CODE||V.DIMENSION_SUBCATEGORY_CODE||V.DIMENSION_SUB_DETAIL_CODE
                          = DECODE(V.LV0_PROD_RND_TEAM_CODE,'''','''',T.LV0_PROD_RND_TEAM_CODE)||
                            DECODE(V.LV1_PROD_RND_TEAM_CODE,'''','''',T.LV1_PROD_RND_TEAM_CODE)||
                            DECODE(V.LV2_PROD_RND_TEAM_CODE,'''','''',T.LV2_PROD_RND_TEAM_CODE)||
                            DECODE(V.LV3_PROD_RND_TEAM_CODE,'''','''',T.LV3_PROD_RND_TEAM_CODE)||
                            DECODE(V.SPART_CODE,'''','''',T.SPART_CODE)||
                            DECODE(V.LV4_PROD_RND_TEAM_CODE,'''','''',T.LV4_PROD_RND_TEAM_CODE)||   -- 202405版本新增
                            DECODE(V.DIMENSION_CODE,'''','''',T.DIMENSION_CODE)||
                            DECODE(V.DIMENSION_SUBCATEGORY_CODE,'''','''',T.DIMENSION_SUBCATEGORY_CODE)||
                            DECODE(V.DIMENSION_SUB_DETAIL_CODE,'''','''',T.DIMENSION_SUB_DETAIL_CODE)';
      ELSIF F_INDUSTRY_FLAG = 'I' THEN 
         V_SQL_LVL := 'LV3_PROD_RND_TEAM_CODE,''LV3'',
                       DIMENSION_CODE,''DIMENSION'',
                       DIMENSION_SUBCATEGORY_CODE,''SUBCATEGORY'',
                       DIMENSION_SUB_DETAIL_CODE,''SUB_DETAIL'',
                       SPART_CODE,''SPART'',' ;
         V_JOIN_SQL := 'AND V.LV0_PROD_RND_TEAM_CODE||V.LV1_PROD_RND_TEAM_CODE||V.LV2_PROD_RND_TEAM_CODE||V.LV3_PROD_RND_TEAM_CODE||
                           V.SPART_CODE||V.DIMENSION_CODE||V.DIMENSION_SUBCATEGORY_CODE||V.DIMENSION_SUB_DETAIL_CODE
                         = DECODE(V.LV0_PROD_RND_TEAM_CODE,'''','''',T.LV0_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV1_PROD_RND_TEAM_CODE,'''','''',T.LV1_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV2_PROD_RND_TEAM_CODE,'''','''',T.LV2_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV3_PROD_RND_TEAM_CODE,'''','''',T.LV3_PROD_RND_TEAM_CODE)||
                           DECODE(V.SPART_CODE,'''','''',T.SPART_CODE)||
                           DECODE(V.DIMENSION_CODE,'''','''',T.DIMENSION_CODE)||
                           DECODE(V.DIMENSION_SUBCATEGORY_CODE,'''','''',T.DIMENSION_SUBCATEGORY_CODE)||
                           DECODE(V.DIMENSION_SUB_DETAIL_CODE,'''','''',T.DIMENSION_SUB_DETAIL_CODE)';
      ELSE NULL;
      END IF;
    ELSE
      NULL;
    END IF;

 --创建年均本临时表
  V_SQL := '
    DROP TABLE IF EXISTS ' || V_TMP_TABLE ||';
    CREATE TEMPORARY TABLE ' || V_TMP_TABLE ||' (
        PERIOD_YEAR BIGINT,
        PARENT_CODE    VARCHAR(200),
        PARENT_CN_NAME    VARCHAR(200),
        PARENT_LEVEL    VARCHAR(50),
        DIMENSION_TYPE    VARCHAR(50) ,
        GROUP_CODE VARCHAR(200),
        GROUP_CN_NAME VARCHAR(2000),
        LV0_PROD_LIST_CODE VARCHAR(50),
        LV0_PROD_LIST_CN_NAME VARCHAR(200)  ,
        OVERSEA_FLAG VARCHAR(2),
        CALIBER_FLAG VARCHAR(2),
        CUSTOM_ID    VARCHAR(50),
        CUSTOM_CN_NAME    VARCHAR(200),
        VIEW_FLAG VARCHAR(2),
        AVG_AMT VARCHAR(500),
        SUM_AMT VARCHAR(500),
        SUM_QTY NUMERIC,
        APD_FLAG VARCHAR(2),
        NULL_FLAG INT
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(VIEW_FLAG,GROUP_CODE)';

    EXECUTE IMMEDIATE V_SQL;
    DBMS_OUTPUT.PUT_LINE('11:-----------');

    V_SQL := '
    DROP TABLE IF EXISTS ' || V_TMP_TABLE2 ||';
    CREATE TEMPORARY TABLE ' || V_TMP_TABLE2 ||' (
        PERIOD_YEAR BIGINT,
        LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
        LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
        LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
        LV3_PROD_RND_TEAM_CODE VARCHAR(50),
        LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200),
        LV4_PROD_RND_TEAM_CODE VARCHAR(50),
        LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200),
        L1_NAME VARCHAR(200),
        L2_NAME VARCHAR(200),
        DIMENSION_CODE    VARCHAR(500), /*9月新增量纲颗粒度维度 */
        DIMENSION_CN_NAME    VARCHAR(2000) ,
        DIMENSION_SUBCATEGORY_CODE    VARCHAR(500) ,
        DIMENSION_SUBCATEGORY_CN_NAME    VARCHAR(2000) ,
        DIMENSION_SUB_DETAIL_CODE    VARCHAR(500) ,
        DIMENSION_SUB_DETAIL_CN_NAME    VARCHAR(2000) ,
        SPART_CODE VARCHAR(200),
        SPART_CN_NAME VARCHAR(200),
        COA_CODE VARCHAR(50),
        COA_CN_NAME VARCHAR(200),
        L3_CEG_CODE    VARCHAR(50),
        L3_CEG_CN_NAME    VARCHAR(200),
        L3_CEG_SHORT_CN_NAME    VARCHAR(200),
        L4_CEG_CODE    VARCHAR(50),
        L4_CEG_CN_NAME    VARCHAR(200),
        L4_CEG_SHORT_CN_NAME    VARCHAR(200),
        CATEGORY_CODE VARCHAR(50),
        CATEGORY_CN_NAME VARCHAR(200),
        ITEM_CODE VARCHAR(50),
        ITEM_CN_NAME VARCHAR(2000),
        SHIP_QUANTITY NUMERIC,
        RMB_COST_AMT NUMERIC,
        VIEW_FLAG VARCHAR(2),
        CALIBER_FLAG VARCHAR(2),
        OVERSEA_FLAG VARCHAR(2),
        LV0_PROD_LIST_CODE VARCHAR(50),
        LV0_PROD_LIST_CN_NAME VARCHAR(200)  ,
        CUSTOM_ID    VARCHAR(50),
        CUSTOM_CN_NAME    VARCHAR(200),
        CUSTOM_LEVEL    VARCHAR(50)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(CATEGORY_CODE,ITEM_CODE)';

    EXECUTE IMMEDIATE V_SQL;
    DBMS_OUTPUT.PUT_LINE('12:-----------');

    --2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_MUM,
     F_CAL_LOG_DESC => 'ITEM年均本临时表创建完成',
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS');


IF F_CUSTOM_ID IS NULL AND F_INDUSTRY_FLAG = 'I' THEN

    OPEN C_DIM;
    LOOP
    FETCH  C_DIM   INTO  V_C_DIM_ID,V_C_DIM_NAME,V_C_MAX_LEVEL ;
    EXIT WHEN C_DIM%NOTFOUND;

        V_SQL := '
        INSERT INTO '|| V_TMP_TABLE2 ||'(
                    PERIOD_YEAR,
                    LV0_PROD_RND_TEAM_CODE,
                    LV0_PROD_RD_TEAM_CN_NAME,
                    LV1_PROD_RND_TEAM_CODE,
                    LV1_PROD_RD_TEAM_CN_NAME,
                    LV2_PROD_RND_TEAM_CODE,
                    LV2_PROD_RD_TEAM_CN_NAME,'
                   ||V_LV3_PROD_RND_TEAM_CODE
                   ||V_LV3_PROD_RD_TEAM_CN_NAME
                   ||V_L1_NAME
                   ||V_L2_NAME
                   ||V_DIMENSION_CODE
                   ||V_DIMENSION_CN_NAME
                   ||V_DIMENSION_SUBCATEGORY_CODE
                   ||V_DIMENSION_SUBCATEGORY_CN_NAME
                   ||V_DIMENSION_SUB_DETAIL_CODE
                   ||V_DIMENSION_SUB_DETAIL_CN_NAME
                   ||V_SPART_TOTAL
                   ||V_DIFF_COLUMN_TOTAL||'
                   L3_CEG_CODE,
                   L3_CEG_CN_NAME,
                   L3_CEG_SHORT_CN_NAME,
                   L4_CEG_CODE,
                   L4_CEG_CN_NAME,
                   L4_CEG_SHORT_CN_NAME,
                   CATEGORY_CODE,
                   CATEGORY_CN_NAME,
                   ITEM_CODE,
                   ITEM_CN_NAME,
                   SHIP_QUANTITY,
                   RMB_COST_AMT,
                   VIEW_FLAG,
                   CALIBER_FLAG,
                   OVERSEA_FLAG  ,
                   LV0_PROD_LIST_CODE,
                   LV0_PROD_LIST_CN_NAME  ,
                  CUSTOM_ID,CUSTOM_CN_NAME,
                  CUSTOM_LEVEL
                   )
           -- 剔除发货额为0的数据
           SELECT PERIOD_YEAR,
                  LV0_PROD_RND_TEAM_CODE,
                  LV0_PROD_RD_TEAM_CN_NAME,
                  LV1_PROD_RND_TEAM_CODE,
                  LV1_PROD_RD_TEAM_CN_NAME,
                  LV2_PROD_RND_TEAM_CODE,
                  LV2_PROD_RD_TEAM_CN_NAME,'
                  ||V_LV3_PROD_RND_TEAM_CODE
                  ||V_LV3_PROD_RD_TEAM_CN_NAME
                  ||V_L1_NAME
                  ||V_L2_NAME
                  ||V_DIMENSION_CODE
                  ||V_DIMENSION_CN_NAME
                  ||V_DIMENSION_SUBCATEGORY_CODE
                  ||V_DIMENSION_SUBCATEGORY_CN_NAME
                  ||V_DIMENSION_SUB_DETAIL_CODE
                  ||V_DIMENSION_SUB_DETAIL_CN_NAME
                  ||V_SPART_TOTAL
                  ||V_DIFF_COLUMN_TOTAL||'
                  L3_CEG_CODE,
                  L3_CEG_CN_NAME,
                  L3_CEG_SHORT_CN_NAME,
                  L4_CEG_CODE,
                  L4_CEG_CN_NAME,
                  L4_CEG_SHORT_CN_NAME,
                  CATEGORY_CODE,
                  CATEGORY_CN_NAME,
                  ITEM_CODE,
                  ITEM_CN_NAME,
                  SHIP_QUANTITY,
                  RMB_COST_AMT,
                  VIEW_FLAG,
                  CALIBER_FLAG,
                  OVERSEA_FLAG  ,
                  LV0_PROD_LIST_CODE,
                  LV0_PROD_LIST_CN_NAME,
                   ''' || V_C_DIM_ID ||''',
                   ''' || V_C_DIM_NAME ||''',
                   ''' || V_C_MAX_LEVEL ||'''
              FROM(
                  SELECT T.PERIOD_YEAR,
                         T.LV0_PROD_RND_TEAM_CODE,
                         T.LV0_PROD_RD_TEAM_CN_NAME,
                         T.LV1_PROD_RND_TEAM_CODE,
                         T.LV1_PROD_RD_TEAM_CN_NAME,
                         T.LV2_PROD_RND_TEAM_CODE,
                         T.LV2_PROD_RD_TEAM_CN_NAME,'||
                         V_SQL_LV3_PROD_RND_TEAM_CODE||
                         V_SQL_LV3_PROD_RD_TEAM_CN_NAME||
                         V_SQL_L1_NAME||
                         V_SQL_L2_NAME||
                         V_SQL_DIMENSION_CODE||
                         V_SQL_DIMENSION_CN_NAME||
                         V_SQL_DIMENSION_SUBCATEGORY_CODE||
                         V_SQL_DIMENSION_SUBCATEGORY_CN_NAME||
                         V_SQL_DIMENSION_SUB_DETAIL_CODE||
                         V_SQL_DIMENSION_SUB_DETAIL_CN_NAME||
                         V_SPART_TOTAL
                         ||V_DIFF_COLUMN_TOTAL||'
                         T.L3_CEG_CODE,
                         T.L3_CEG_CN_NAME,
                         T.L3_CEG_SHORT_CN_NAME,
                         T.L4_CEG_CODE,
                         T.L4_CEG_CN_NAME,
                         T.L4_CEG_SHORT_CN_NAME,
                         T.CATEGORY_CODE,
                         T.CATEGORY_CN_NAME,
                         T.ITEM_CODE,
                         T.ITEM_CN_NAME,
                         T.SHIP_QUANTITY,
                         CAST(GS_DECRYPT( T.RMB_COST_AMT,''' || V_KEYSTR ||''',''AES128'',''CBC'',''SHA256'' ) AS NUMERIC) AS RMB_COST_AMT,
                         T.VIEW_FLAG,
                         T.CALIBER_FLAG,
                         T.OVERSEA_FLAG  ,
                         T.LV0_PROD_LIST_CODE,
                         T.LV0_PROD_LIST_CN_NAME
                        FROM  (
                        SELECT * FROM  ' || V_FROM_TABLE || ' WHERE ONLY_ITEM_FLAG = ''N''
                        AND REVIEW_ITEM_FLAG = 0   -- 不属于底层数据审视范围的数据(202401新增)
--                        AND CAST(GS_DECRYPT(RMB_COST_AMT,''' || V_KEYSTR ||''',''AES128'',''CBC'',''SHA256'' ) AS NUMERIC) > 0   -- 条件去除，减少加解密逻辑，在MID_MONTH_ITEM函数已剔除一次
                        ) T
                       WHERE 1=1
                     AND EXISTS
                           (SELECT NULL
                            FROM ' || V_DIM_TABLE || ' V
                            WHERE V.DEL_FLAG=''N''
                            AND V.ENABLE_FLAG=''Y''
                            AND V.CUSTOM_ID='||V_C_DIM_ID||'
                            AND V.VIEW_FLAG=T.VIEW_FLAG
                            AND V.CALIBER_FLAG=T.CALIBER_FLAG
                            AND V.OVERSEA_FLAG=T.OVERSEA_FLAG
                            AND V.LV0_PROD_LIST_CODE=T.LV0_PROD_LIST_CODE
                            AND NVL(V.CATEGORY_CODE||V.L4_CEG_CODE||V.L3_CEG_CODE,''AAAAA'')
                             = NVL(DECODE(V.CATEGORY_CODE,'''','''',T.CATEGORY_CODE)||DECODE(V.L4_CEG_CODE,'''','''',T.L4_CEG_CODE)||DECODE(V.L3_CEG_CODE,'''','''',T.L3_CEG_CODE),''AAAAA'')
                            '  ||V_JOIN_SQL||')
                        ); '
                    ;


    EXECUTE IMMEDIATE V_SQL;
        DBMS_OUTPUT.PUT_LINE('13:-----------');

    END LOOP ;
    CLOSE  C_DIM;
    
ELSIF F_CUSTOM_ID IS NULL AND F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源

    OPEN C_DIM_ENERGY;
    LOOP
    FETCH  C_DIM_ENERGY   INTO  V_C_DIM_ID,V_C_DIM_NAME,V_C_MAX_LEVEL ;
    EXIT WHEN C_DIM_ENERGY%NOTFOUND;

        V_SQL := '
        INSERT INTO '|| V_TMP_TABLE2 ||'(
                    PERIOD_YEAR,
                    LV0_PROD_RND_TEAM_CODE,
                    LV0_PROD_RD_TEAM_CN_NAME,
                    LV1_PROD_RND_TEAM_CODE,
                    LV1_PROD_RD_TEAM_CN_NAME,
                    LV2_PROD_RND_TEAM_CODE,
                    LV2_PROD_RD_TEAM_CN_NAME,'
                   ||V_LV3_PROD_RND_TEAM_CODE
                   ||V_LV3_PROD_RD_TEAM_CN_NAME
                   ||V_L1_NAME
                   ||V_L2_NAME
                   ||V_DIMENSION_CODE
                   ||V_DIMENSION_CN_NAME
                   ||V_DIMENSION_SUBCATEGORY_CODE
                   ||V_DIMENSION_SUBCATEGORY_CN_NAME
                   ||V_DIMENSION_SUB_DETAIL_CODE
                   ||V_DIMENSION_SUB_DETAIL_CN_NAME
                   ||V_SPART_TOTAL
                   ||V_DIFF_COLUMN_TOTAL||'
                   L3_CEG_CODE,
                   L3_CEG_CN_NAME,
                   L3_CEG_SHORT_CN_NAME,
                   L4_CEG_CODE,
                   L4_CEG_CN_NAME,
                   L4_CEG_SHORT_CN_NAME,
                   CATEGORY_CODE,
                   CATEGORY_CN_NAME,
                   ITEM_CODE,
                   ITEM_CN_NAME,
                   SHIP_QUANTITY,
                   RMB_COST_AMT,
                   VIEW_FLAG,
                   CALIBER_FLAG,
                   OVERSEA_FLAG  ,
                   LV0_PROD_LIST_CODE,
                   LV0_PROD_LIST_CN_NAME  ,
                  CUSTOM_ID,CUSTOM_CN_NAME,
                  CUSTOM_LEVEL
                   )
           -- 剔除发货额为0的数据
           SELECT PERIOD_YEAR,
                  LV0_PROD_RND_TEAM_CODE,
                  LV0_PROD_RD_TEAM_CN_NAME,
                  LV1_PROD_RND_TEAM_CODE,
                  LV1_PROD_RD_TEAM_CN_NAME,
                  LV2_PROD_RND_TEAM_CODE,
                  LV2_PROD_RD_TEAM_CN_NAME,'
                  ||V_LV3_PROD_RND_TEAM_CODE
                  ||V_LV3_PROD_RD_TEAM_CN_NAME
                  ||V_L1_NAME
                  ||V_L2_NAME
                  ||V_DIMENSION_CODE
                  ||V_DIMENSION_CN_NAME
                  ||V_DIMENSION_SUBCATEGORY_CODE
                  ||V_DIMENSION_SUBCATEGORY_CN_NAME
                  ||V_DIMENSION_SUB_DETAIL_CODE
                  ||V_DIMENSION_SUB_DETAIL_CN_NAME
                  ||V_SPART_TOTAL
                  ||V_DIFF_COLUMN_TOTAL||'
                  L3_CEG_CODE,
                  L3_CEG_CN_NAME,
                  L3_CEG_SHORT_CN_NAME,
                  L4_CEG_CODE,
                  L4_CEG_CN_NAME,
                  L4_CEG_SHORT_CN_NAME,
                  CATEGORY_CODE,
                  CATEGORY_CN_NAME,
                  ITEM_CODE,
                  ITEM_CN_NAME,
                  SHIP_QUANTITY,
                  RMB_COST_AMT,
                  VIEW_FLAG,
                  CALIBER_FLAG,
                  OVERSEA_FLAG  ,
                  LV0_PROD_LIST_CODE,
                  LV0_PROD_LIST_CN_NAME,
                   ''' || V_C_DIM_ID ||''',
                   ''' || V_C_DIM_NAME ||''',
                   ''' || V_C_MAX_LEVEL ||'''
              FROM(
                  SELECT T.PERIOD_YEAR,
                         T.LV0_PROD_RND_TEAM_CODE,
                         T.LV0_PROD_RD_TEAM_CN_NAME,
                         T.LV1_PROD_RND_TEAM_CODE,
                         T.LV1_PROD_RD_TEAM_CN_NAME,
                         T.LV2_PROD_RND_TEAM_CODE,
                         T.LV2_PROD_RD_TEAM_CN_NAME,'||
                         V_SQL_LV3_PROD_RND_TEAM_CODE||
                         V_SQL_LV3_PROD_RD_TEAM_CN_NAME||
                         V_SQL_L1_NAME||
                         V_SQL_L2_NAME||
                         V_SQL_DIMENSION_CODE||
                         V_SQL_DIMENSION_CN_NAME||
                         V_SQL_DIMENSION_SUBCATEGORY_CODE||
                         V_SQL_DIMENSION_SUBCATEGORY_CN_NAME||
                         V_SQL_DIMENSION_SUB_DETAIL_CODE||
                         V_SQL_DIMENSION_SUB_DETAIL_CN_NAME||
                         V_SPART_TOTAL
                         ||V_DIFF_COLUMN_TOTAL||'
                         T.L3_CEG_CODE,
                         T.L3_CEG_CN_NAME,
                         T.L3_CEG_SHORT_CN_NAME,
                         T.L4_CEG_CODE,
                         T.L4_CEG_CN_NAME,
                         T.L4_CEG_SHORT_CN_NAME,
                         T.CATEGORY_CODE,
                         T.CATEGORY_CN_NAME,
                         T.ITEM_CODE,
                         T.ITEM_CN_NAME,
                         T.SHIP_QUANTITY,
                         CAST(GS_DECRYPT( T.RMB_COST_AMT,''' || V_KEYSTR ||''',''AES128'',''CBC'',''SHA256'' ) AS NUMERIC) AS RMB_COST_AMT,
                         T.VIEW_FLAG,
                         T.CALIBER_FLAG,
                         T.OVERSEA_FLAG  ,
                         T.LV0_PROD_LIST_CODE,
                         T.LV0_PROD_LIST_CN_NAME
                        FROM  (
                        SELECT * FROM  ' || V_FROM_TABLE || ' WHERE ONLY_ITEM_FLAG = ''N''
                        AND REVIEW_ITEM_FLAG = 0   -- 不属于底层数据审视范围的数据(202401新增)
--                        AND CAST(GS_DECRYPT(RMB_COST_AMT,''' || V_KEYSTR ||''',''AES128'',''CBC'',''SHA256'' ) AS NUMERIC) > 0   -- 条件去除，减少加解密逻辑，在MID_MONTH_ITEM函数已剔除一次
                        ) T
                       WHERE 1=1
                     AND EXISTS
                           (SELECT NULL
                            FROM ' || V_DIM_TABLE || ' V
                            WHERE V.DEL_FLAG=''N''
                            AND V.ENABLE_FLAG=''Y''
                            AND V.CUSTOM_ID='||V_C_DIM_ID||'
                            AND V.VIEW_FLAG=T.VIEW_FLAG
                            AND V.CALIBER_FLAG=T.CALIBER_FLAG
                            AND V.OVERSEA_FLAG=T.OVERSEA_FLAG
                            AND V.LV0_PROD_LIST_CODE=T.LV0_PROD_LIST_CODE
                            AND NVL(V.CATEGORY_CODE||V.L4_CEG_CODE||V.L3_CEG_CODE,''AAAAA'')
                             = NVL(DECODE(V.CATEGORY_CODE,'''','''',T.CATEGORY_CODE)||DECODE(V.L4_CEG_CODE,'''','''',T.L4_CEG_CODE)||DECODE(V.L3_CEG_CODE,'''','''',T.L3_CEG_CODE),''AAAAA'')
                            '  ||V_JOIN_SQL||')
                        ); '
                    ;

    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
        DBMS_OUTPUT.PUT_LINE('13:-----------');

    END LOOP ;
    CLOSE  C_DIM_ENERGY;

ELSIF F_CUSTOM_ID IS NULL AND F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407版本新增IAS

    OPEN C_DIM_IAS;
    LOOP
    FETCH  C_DIM_IAS   INTO  V_C_DIM_ID,V_C_DIM_NAME,V_C_MAX_LEVEL ;
    EXIT WHEN C_DIM_IAS%NOTFOUND;

        V_SQL := '
        INSERT INTO '|| V_TMP_TABLE2 ||'(
                    PERIOD_YEAR,
                    LV0_PROD_RND_TEAM_CODE,
                    LV0_PROD_RD_TEAM_CN_NAME,
                    LV1_PROD_RND_TEAM_CODE,
                    LV1_PROD_RD_TEAM_CN_NAME,
                    LV2_PROD_RND_TEAM_CODE,
                    LV2_PROD_RD_TEAM_CN_NAME,'
                   ||V_LV3_PROD_RND_TEAM_CODE
                   ||V_LV3_PROD_RD_TEAM_CN_NAME
                   ||V_L1_NAME
                   ||V_L2_NAME
                   ||V_DIMENSION_CODE
                   ||V_DIMENSION_CN_NAME
                   ||V_DIMENSION_SUBCATEGORY_CODE
                   ||V_DIMENSION_SUBCATEGORY_CN_NAME
                   ||V_DIMENSION_SUB_DETAIL_CODE
                   ||V_DIMENSION_SUB_DETAIL_CN_NAME
                   ||V_SPART_TOTAL
                   ||V_DIFF_COLUMN_TOTAL||'
                   L3_CEG_CODE,
                   L3_CEG_CN_NAME,
                   L3_CEG_SHORT_CN_NAME,
                   L4_CEG_CODE,
                   L4_CEG_CN_NAME,
                   L4_CEG_SHORT_CN_NAME,
                   CATEGORY_CODE,
                   CATEGORY_CN_NAME,
                   ITEM_CODE,
                   ITEM_CN_NAME,
                   SHIP_QUANTITY,
                   RMB_COST_AMT,
                   VIEW_FLAG,
                   CALIBER_FLAG,
                   OVERSEA_FLAG  ,
                   LV0_PROD_LIST_CODE,
                   LV0_PROD_LIST_CN_NAME  ,
                  CUSTOM_ID,CUSTOM_CN_NAME,
                  CUSTOM_LEVEL
                   )
           -- 剔除发货额为0的数据
           SELECT PERIOD_YEAR,
                  LV0_PROD_RND_TEAM_CODE,
                  LV0_PROD_RD_TEAM_CN_NAME,
                  LV1_PROD_RND_TEAM_CODE,
                  LV1_PROD_RD_TEAM_CN_NAME,
                  LV2_PROD_RND_TEAM_CODE,
                  LV2_PROD_RD_TEAM_CN_NAME,'
                  ||V_LV3_PROD_RND_TEAM_CODE
                  ||V_LV3_PROD_RD_TEAM_CN_NAME
                  ||V_L1_NAME
                  ||V_L2_NAME
                  ||V_DIMENSION_CODE
                  ||V_DIMENSION_CN_NAME
                  ||V_DIMENSION_SUBCATEGORY_CODE
                  ||V_DIMENSION_SUBCATEGORY_CN_NAME
                  ||V_DIMENSION_SUB_DETAIL_CODE
                  ||V_DIMENSION_SUB_DETAIL_CN_NAME
                  ||V_SPART_TOTAL
                  ||V_DIFF_COLUMN_TOTAL||'
                  L3_CEG_CODE,
                  L3_CEG_CN_NAME,
                  L3_CEG_SHORT_CN_NAME,
                  L4_CEG_CODE,
                  L4_CEG_CN_NAME,
                  L4_CEG_SHORT_CN_NAME,
                  CATEGORY_CODE,
                  CATEGORY_CN_NAME,
                  ITEM_CODE,
                  ITEM_CN_NAME,
                  SHIP_QUANTITY,
                  RMB_COST_AMT,
                  VIEW_FLAG,
                  CALIBER_FLAG,
                  OVERSEA_FLAG  ,
                  LV0_PROD_LIST_CODE,
                  LV0_PROD_LIST_CN_NAME,
                   ''' || V_C_DIM_ID ||''',
                   ''' || V_C_DIM_NAME ||''',
                   ''' || V_C_MAX_LEVEL ||'''
              FROM(
                  SELECT T.PERIOD_YEAR,
                         T.LV0_PROD_RND_TEAM_CODE,
                         T.LV0_PROD_RD_TEAM_CN_NAME,
                         T.LV1_PROD_RND_TEAM_CODE,
                         T.LV1_PROD_RD_TEAM_CN_NAME,
                         T.LV2_PROD_RND_TEAM_CODE,
                         T.LV2_PROD_RD_TEAM_CN_NAME,'||
                         V_SQL_LV3_PROD_RND_TEAM_CODE||
                         V_SQL_LV3_PROD_RD_TEAM_CN_NAME||
                         V_SQL_L1_NAME||
                         V_SQL_L2_NAME||
                         V_SQL_DIMENSION_CODE||
                         V_SQL_DIMENSION_CN_NAME||
                         V_SQL_DIMENSION_SUBCATEGORY_CODE||
                         V_SQL_DIMENSION_SUBCATEGORY_CN_NAME||
                         V_SQL_DIMENSION_SUB_DETAIL_CODE||
                         V_SQL_DIMENSION_SUB_DETAIL_CN_NAME||
                         V_SPART_TOTAL
                         ||V_DIFF_COLUMN_TOTAL||'
                         T.L3_CEG_CODE,
                         T.L3_CEG_CN_NAME,
                         T.L3_CEG_SHORT_CN_NAME,
                         T.L4_CEG_CODE,
                         T.L4_CEG_CN_NAME,
                         T.L4_CEG_SHORT_CN_NAME,
                         T.CATEGORY_CODE,
                         T.CATEGORY_CN_NAME,
                         T.ITEM_CODE,
                         T.ITEM_CN_NAME,
                         T.SHIP_QUANTITY,
                         CAST(GS_DECRYPT( T.RMB_COST_AMT,''' || V_KEYSTR ||''',''AES128'',''CBC'',''SHA256'' ) AS NUMERIC) AS RMB_COST_AMT,
                         T.VIEW_FLAG,
                         T.CALIBER_FLAG,
                         T.OVERSEA_FLAG  ,
                         T.LV0_PROD_LIST_CODE,
                         T.LV0_PROD_LIST_CN_NAME
                        FROM  (
                        SELECT * FROM  ' || V_FROM_TABLE || ' WHERE ONLY_ITEM_FLAG = ''N''
                        AND REVIEW_ITEM_FLAG = 0   -- 不属于底层数据审视范围的数据(202401新增)
                        ) T
                       WHERE 1=1
                     AND EXISTS
                           (SELECT NULL
                            FROM ' || V_DIM_TABLE || ' V
                            WHERE V.DEL_FLAG=''N''
                            AND V.ENABLE_FLAG=''Y''
                            AND V.CUSTOM_ID='||V_C_DIM_ID||'
                            AND V.VIEW_FLAG=T.VIEW_FLAG
                            AND V.CALIBER_FLAG=T.CALIBER_FLAG
                            AND V.OVERSEA_FLAG=T.OVERSEA_FLAG
                            AND V.LV0_PROD_LIST_CODE=T.LV0_PROD_LIST_CODE
                            AND NVL(V.CATEGORY_CODE||V.L4_CEG_CODE||V.L3_CEG_CODE,''AAAAA'')
                             = NVL(DECODE(V.CATEGORY_CODE,'''','''',T.CATEGORY_CODE)||DECODE(V.L4_CEG_CODE,'''','''',T.L4_CEG_CODE)||DECODE(V.L3_CEG_CODE,'''','''',T.L3_CEG_CODE),''AAAAA'')
                            '  ||V_JOIN_SQL||')
                        ); '
                    ;

    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
        DBMS_OUTPUT.PUT_LINE('13:-----------');

    END LOOP ;
    CLOSE  C_DIM_IAS;
    
ELSE
    V_C_DIM_ID    := F_CUSTOM_ID;

  V_SQL := '
    SELECT NVL(MAX(CUSTOM_CN_NAME),''空集''),
           NVL(MAX(DECODE(GROUP_LEVEL,''ITEM'',''01ITEM'',''CATEGORY'',''02CATEGORY'',''MODL'',''03MODL'',''CEG'',''04CEG'',''SPART'',''05SPART'',''SUB_DETAIL'',''06SUB_DETAIL'',''SUBCATEGORY'',''07SUBCATEGORY'',''DIMENSION'',''08DIMENSION'', ''L2'',''09L2'',''L1'',''10L1'',''COA'',''11COA'',''LV4'',''12LV4'',''LV3'',''13LV3'',''LV2'',''14LV2'',''LV1'',''15LV1'',''LV0'',''16LV0'')),''空集'')
           FROM '||V_DIM_TABLE||'
           WHERE CUSTOM_ID = '||F_CUSTOM_ID||' AND GRANULARITY_TYPE = '''||F_DIMENSION_TYPE||'''
           AND PAGE_FLAG IN (''ALL_ANNUAL'',''ANNUAL'')
           AND DEL_FLAG=''N'' AND ENABLE_FLAG=''Y''' ;
     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL INTO V_C_DIM_NAME,V_C_MAX_LEVEL;

    V_SQL := '
    INSERT INTO '|| V_TMP_TABLE2 ||'(
                PERIOD_YEAR,
                LV0_PROD_RND_TEAM_CODE,
                LV0_PROD_RD_TEAM_CN_NAME,
                LV1_PROD_RND_TEAM_CODE,
                LV1_PROD_RD_TEAM_CN_NAME,
                LV2_PROD_RND_TEAM_CODE,
                LV2_PROD_RD_TEAM_CN_NAME,'
               ||V_LV3_PROD_RND_TEAM_CODE
               ||V_LV3_PROD_RD_TEAM_CN_NAME
               ||V_L1_NAME
               ||V_L2_NAME
               ||V_DIMENSION_CODE
               ||V_DIMENSION_CN_NAME
               ||V_DIMENSION_SUBCATEGORY_CODE
               ||V_DIMENSION_SUBCATEGORY_CN_NAME
               ||V_DIMENSION_SUB_DETAIL_CODE
               ||V_DIMENSION_SUB_DETAIL_CN_NAME
               ||V_SPART_TOTAL
               ||V_DIFF_COLUMN_TOTAL||'
               L3_CEG_CODE,
               L3_CEG_CN_NAME,
               L3_CEG_SHORT_CN_NAME,
               L4_CEG_CODE,
               L4_CEG_CN_NAME,
               L4_CEG_SHORT_CN_NAME,
               CATEGORY_CODE,
               CATEGORY_CN_NAME,
               ITEM_CODE,
               ITEM_CN_NAME,
               SHIP_QUANTITY,
               RMB_COST_AMT,
               VIEW_FLAG,
               CALIBER_FLAG,
               OVERSEA_FLAG  ,
               LV0_PROD_LIST_CODE,
               LV0_PROD_LIST_CN_NAME  ,
              CUSTOM_ID,CUSTOM_CN_NAME,
              CUSTOM_LEVEL
               )
       -- 剔除发货额为0的数据
       SELECT PERIOD_YEAR,
              LV0_PROD_RND_TEAM_CODE,
              LV0_PROD_RD_TEAM_CN_NAME,
              LV1_PROD_RND_TEAM_CODE,
              LV1_PROD_RD_TEAM_CN_NAME,
              LV2_PROD_RND_TEAM_CODE,
              LV2_PROD_RD_TEAM_CN_NAME,'
              ||V_LV3_PROD_RND_TEAM_CODE
              ||V_LV3_PROD_RD_TEAM_CN_NAME
              ||V_L1_NAME
              ||V_L2_NAME
              ||V_DIMENSION_CODE
              ||V_DIMENSION_CN_NAME
              ||V_DIMENSION_SUBCATEGORY_CODE
              ||V_DIMENSION_SUBCATEGORY_CN_NAME
              ||V_DIMENSION_SUB_DETAIL_CODE
              ||V_DIMENSION_SUB_DETAIL_CN_NAME
              ||V_SPART_TOTAL
              ||V_DIFF_COLUMN_TOTAL||'
              L3_CEG_CODE,
              L3_CEG_CN_NAME,
              L3_CEG_SHORT_CN_NAME,
              L4_CEG_CODE,
              L4_CEG_CN_NAME,
              L4_CEG_SHORT_CN_NAME,
              CATEGORY_CODE,
              CATEGORY_CN_NAME,
              ITEM_CODE,
              ITEM_CN_NAME,
              SHIP_QUANTITY,
              RMB_COST_AMT,
              VIEW_FLAG,
              CALIBER_FLAG,
              OVERSEA_FLAG  ,
              LV0_PROD_LIST_CODE,
              LV0_PROD_LIST_CN_NAME,
               ''' || V_C_DIM_ID ||''',
               ''' || V_C_DIM_NAME ||''',
               ''' || V_C_MAX_LEVEL ||'''
          FROM(
              SELECT T.PERIOD_YEAR,
                     T.LV0_PROD_RND_TEAM_CODE,
                     T.LV0_PROD_RD_TEAM_CN_NAME,
                     T.LV1_PROD_RND_TEAM_CODE,
                     T.LV1_PROD_RD_TEAM_CN_NAME,
                     T.LV2_PROD_RND_TEAM_CODE,
                     T.LV2_PROD_RD_TEAM_CN_NAME,'||
                     V_SQL_LV3_PROD_RND_TEAM_CODE||
                     V_SQL_LV3_PROD_RD_TEAM_CN_NAME||
                     V_SQL_L1_NAME||
                     V_SQL_L2_NAME||
                     V_SQL_DIMENSION_CODE||
                     V_SQL_DIMENSION_CN_NAME||
                     V_SQL_DIMENSION_SUBCATEGORY_CODE||
                     V_SQL_DIMENSION_SUBCATEGORY_CN_NAME||
                     V_SQL_DIMENSION_SUB_DETAIL_CODE||
                     V_SQL_DIMENSION_SUB_DETAIL_CN_NAME||
                     V_SPART_TOTAL||
                     V_DIFF_COLUMN_TOTAL||'
                     T.L3_CEG_CODE,
                     T.L3_CEG_CN_NAME,
                     T.L3_CEG_SHORT_CN_NAME,
                     T.L4_CEG_CODE,
                     T.L4_CEG_CN_NAME,
                     T.L4_CEG_SHORT_CN_NAME,
                     T.CATEGORY_CODE,
                     T.CATEGORY_CN_NAME,
                     T.ITEM_CODE,
                     T.ITEM_CN_NAME,
                     T.SHIP_QUANTITY,
                     CAST(GS_DECRYPT( T.RMB_COST_AMT,''' || V_KEYSTR ||''',''AES128'',''CBC'',''SHA256'' ) AS NUMERIC) AS RMB_COST_AMT,
                     T.VIEW_FLAG,
                     T.CALIBER_FLAG,
                     T.OVERSEA_FLAG  ,
                     T.LV0_PROD_LIST_CODE,
                     T.LV0_PROD_LIST_CN_NAME
                    FROM  (SELECT * FROM  ' || V_FROM_TABLE || ' WHERE ONLY_ITEM_FLAG = ''N''
                    AND REVIEW_ITEM_FLAG = 0   -- 不属于底层数据审视范围的数据(202401新增)
--                    AND CAST(GS_DECRYPT( RMB_COST_AMT,''' || V_KEYSTR ||''',''AES128'',''CBC'',''SHA256'' ) AS NUMERIC) >0 
                    ) T
                   WHERE 1=1
                 AND EXISTS
                       (SELECT NULL
                        FROM ' || V_DIM_TABLE || ' V
                        WHERE V.DEL_FLAG=''N''
                        AND V.ENABLE_FLAG=''Y''
                        AND V.CUSTOM_ID='||V_C_DIM_ID||'
                        AND V.VIEW_FLAG=T.VIEW_FLAG
                        AND V.CALIBER_FLAG=T.CALIBER_FLAG
                        AND V.OVERSEA_FLAG=T.OVERSEA_FLAG
                        AND V.LV0_PROD_LIST_CODE=T.LV0_PROD_LIST_CODE
                        AND NVL(V.CATEGORY_CODE||V.L4_CEG_CODE||V.L3_CEG_CODE,''AAAAA'')
                         = NVL(DECODE(V.CATEGORY_CODE,'''','''',T.CATEGORY_CODE)||DECODE(V.L4_CEG_CODE,'''','''',T.L4_CEG_CODE)||DECODE(V.L3_CEG_CODE,'''','''',T.L3_CEG_CODE),''AAAAA'')
                        '  ||V_JOIN_SQL||')
                    ); '
                ;
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
            DBMS_OUTPUT.PUT_LINE('14:-----------');

END IF;

  V_SQL := '
    INSERT INTO '|| V_TMP_TABLE ||'(
                PERIOD_YEAR,
                PARENT_CODE,
                PARENT_CN_NAME,
                PARENT_LEVEL,
                DIMENSION_TYPE,
                GROUP_CODE,
                GROUP_CN_NAME,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                OVERSEA_FLAG,
                CALIBER_FLAG,
                CUSTOM_ID,
                CUSTOM_CN_NAME,
                VIEW_FLAG,
                AVG_AMT ,
                SUM_AMT ,
                SUM_QTY ,
                APD_FLAG  ,
                NULL_FLAG
               )

-- 年均本计算
  WITH VIEW_AVG_TMP AS(
  SELECT
  T3.PERIOD_YEAR,
  T3.VIEW_FLAG,
  T3.DIMENSION_TYPE,
  T3.PARENT_CODE,
  T3.PARENT_CN_NAME,
  T3.PARENT_LEVEL,
  T3.GROUP_CODE,
  T3.GROUP_CN_NAME,
  NULLIF(SUM(T3.RMB_SUM_AMT)/NULLIF(SUM(T3.RMB_SUM_QTY),0),0) AS RMB_AVG_AMT,
  SUM(T3.RMB_SUM_AMT ) AS RMB_SUM_AMT,
  SUM(T3.RMB_SUM_QTY ) AS RMB_SUM_QTY,
  T3.LV0_PROD_LIST_CODE,
  T3.LV0_PROD_LIST_CN_NAME,
  T3.OVERSEA_FLAG,
  T3.CALIBER_FLAG,
  T3.CUSTOM_ID,
  T3.CUSTOM_CN_NAME,
  T3.CUSTOM_LEVEL
  FROM (
  SELECT
  T2.PERIOD_YEAR,
  T2.VIEW_FLAG,
  T2.DIMENSION_TYPE,
  DECODE(PARENT_LEVEL,SUBSTR(CUSTOM_LEVEL,3),T2.CUSTOM_ID,T2.PARENT_CODE) AS PARENT_CODE,
  DECODE(PARENT_LEVEL,SUBSTR(CUSTOM_LEVEL,3),T2.CUSTOM_CN_NAME,T2.PARENT_CN_NAME)  AS PARENT_CN_NAME,
  T2.PARENT_LEVEL,
  T2.GROUP_CODE,
  T2.GROUP_CN_NAME,
  T2.RMB_SUM_AMT,
  T2.RMB_SUM_QTY,
  T2.LV0_PROD_LIST_CODE,
  T2.LV0_PROD_LIST_CN_NAME,
  T2.OVERSEA_FLAG,
  T2.CALIBER_FLAG,
  T2.CUSTOM_ID,
  T2.CUSTOM_CN_NAME,
  T2.CUSTOM_LEVEL
  FROM (
  SELECT T1.PERIOD_YEAR,
         T1.VIEW_FLAG,
         '''||F_DIMENSION_TYPE||''' AS DIMENSION_TYPE,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         DECODE(NVL(T1.PARENT_CODE,''999999999999''),LV0_PROD_RND_TEAM_CODE,''ICT'',
                              LV1_PROD_RND_TEAM_CODE,''LV1'',
                              LV2_PROD_RND_TEAM_CODE,''LV2'','||
                              V_SQL_LVL||'
                              L3_CEG_CODE,''CEG'',
                              L4_CEG_CODE,''MODL'',
                              CATEGORY_CODE,''CATEGORY'',
                   '''')AS PARENT_LEVEL,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.RMB_SUM_AMT,
         T1.RMB_SUM_QTY,
         T1.LV0_PROD_LIST_CODE,
         T1.LV0_PROD_LIST_CN_NAME,
         T1.OVERSEA_FLAG,
         T1.CALIBER_FLAG,
         T1.CUSTOM_ID,
         T1.CUSTOM_CN_NAME,
         T1.CUSTOM_LEVEL
    FROM (
     SELECT T.PERIOD_YEAR,
              T.CUSTOM_ID,
              T.CUSTOM_CN_NAME,
              T.CUSTOM_LEVEL,
              T.VIEW_FLAG,
              T.ITEM_CODE AS GROUP_CODE,
              T.ITEM_CN_NAME AS GROUP_CN_NAME,
              T.LV0_PROD_LIST_CODE,
              T.LV0_PROD_LIST_CN_NAME,
              T.OVERSEA_FLAG,
              T.CALIBER_FLAG,
             T.LV0_PROD_RND_TEAM_CODE,
             T.LV1_PROD_RND_TEAM_CODE,
             T.LV2_PROD_RND_TEAM_CODE,'||
             V_LV3_PROD_RND_TEAM_CODE||
             V_L1_NAME||
             V_L2_NAME||
             V_DIMENSION_CODE||
             V_DIMENSION_SUBCATEGORY_CODE||
             V_DIMENSION_SUB_DETAIL_CODE||
             V_SPART_TOTAL||
             V_DIFF_COLUMN_TOTAL||'
             T.L3_CEG_CODE,
             T.L4_CEG_CODE,
             T.CATEGORY_CODE,
     SUM(RMB_COST_AMT) AS RMB_SUM_AMT,
     SUM(SHIP_QUANTITY) AS RMB_SUM_QTY,
     (LV0_PROD_RND_TEAM_CODE||LV1_PROD_RND_TEAM_CODE||LV2_PROD_RND_TEAM_CODE'||V_J_LV3_PROD_RND_TEAM_CODE||V_J_L1_NAME||V_J_L2_NAME||V_J_DIMENSION_CODE||V_J_DIMENSION_SUBCATEGORY_CODE||V_J_DIMENSION_SUB_DETAIL_CODE||V_J_SPART_CODE||V_J_DIFF_COLUMN_CODE||'||L3_CEG_CODE||L4_CEG_CODE||CATEGORY_CODE) AS PARENT_CODE,
     (LV0_PROD_RD_TEAM_CN_NAME||LV1_PROD_RD_TEAM_CN_NAME||LV2_PROD_RD_TEAM_CN_NAME'||V_J_LV3_PROD_RD_TEAM_CN_NAME||V_J_L1_NAME||V_J_L2_NAME||V_J_DIMENSION_CN_NAME||V_J_DIMENSION_SUBCATEGORY_CN_NAME||V_J_DIMENSION_SUB_DETAIL_CN_NAME||V_J_SPART_NAME||V_J_DIFF_COLUMN_NAME||'||L3_CEG_CN_NAME||L4_CEG_CN_NAME||CATEGORY_CN_NAME) AS PARENT_CN_NAME
     FROM '||V_TMP_TABLE2||' T
     GROUP BY PERIOD_YEAR,
              T.CUSTOM_ID,
              T.CUSTOM_LEVEL,
              T.CUSTOM_CN_NAME,
              T.VIEW_FLAG,
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              T.LV0_PROD_LIST_CODE,
              T.LV0_PROD_LIST_CN_NAME,
              T.OVERSEA_FLAG,
              T.CALIBER_FLAG,
             GROUPING SETS(
                 (LV0_PROD_RND_TEAM_CODE,LV0_PROD_RD_TEAM_CN_NAME),
                 (LV1_PROD_RND_TEAM_CODE,LV1_PROD_RD_TEAM_CN_NAME),
                 (LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME),'||
                 V_SETS_LV3_PROD||
                 V_SETS_L1_NAME||
                 V_SETS_L2_NAME||
                 V_SETS_DIMENSION||
                 V_SETS_DIMENSION_SUBCATEGORY||
                 V_SETS_DIMENSION_SUB_DETAIL||
                 V_SETS_SPART||
                 V_SETS_DIFF_COLUMN||'
                 (L3_CEG_CODE,L3_CEG_CN_NAME),
                 (L4_CEG_CODE,L4_CEG_CN_NAME),
                 (CATEGORY_CODE,CATEGORY_CN_NAME)
                  )
           ) T1
          ) T2
          WHERE DECODE(PARENT_LEVEL,''ITEM'',''01'',''CATEGORY'',''02'',''MODL'',''03'',''CEG'',''04'',''SPART'',''05'',''SUB_DETAIL'',''06'',''SUBCATEGORY'',''07'',''DIMENSION'',''08'', ''L2'',''09'',''L1'',''10'',''COA'',''11'',''LV4'',''12'',''LV3'',''13'',''LV2'',''14'',''LV1'',''15'',''LV0'',''16'')
            <= SUBSTR(CUSTOM_LEVEL,1,2)
        )  T3
   GROUP BY
  PERIOD_YEAR,
  VIEW_FLAG,
  DIMENSION_TYPE,
  PARENT_CODE,
  PARENT_CN_NAME,
  PARENT_LEVEL,
  GROUP_CODE,
  GROUP_CN_NAME,
  LV0_PROD_LIST_CODE,
  LV0_PROD_LIST_CN_NAME,
  OVERSEA_FLAG,
  CALIBER_FLAG,
  CUSTOM_ID,
  CUSTOM_CN_NAME,
  CUSTOM_LEVEL
)

 -- 实际数均价临时表中出现的重量级团队、采购信息维，取数范围：三年前至当前系统年(若当年为1月时，当年年份不含)
  , DIM_TEAM_TMP AS(
        SELECT DISTINCT PARENT_CODE,
                        PARENT_CN_NAME,
                        PARENT_LEVEL,
                        DIMENSION_TYPE,
                        GROUP_CODE,
                        GROUP_CN_NAME,
                        LV0_PROD_LIST_CODE,
                        LV0_PROD_LIST_CN_NAME,
                        OVERSEA_FLAG,
                        CALIBER_FLAG,
                        VIEW_FLAG,
                        CUSTOM_ID,
                        CUSTOM_CN_NAME
                    FROM VIEW_AVG_TMP
  )

 -- 生成连续年份, 三年前的年份+当年（若当年为1月时，当年年份不含）
  , PERIOD_YEAR_TMP AS(
           SELECT CAST(GENERATE_SERIES(YEAR(CURRENT_TIMESTAMP)-3,
                              YEAR(CURRENT_TIMESTAMP),
                              1) AS BIGINT) AS PERIOD_YEAR FROM DUAL
                   )
 -- 生成连续年的发散维
 , CONTIN_DIM_TMP AS(
       SELECT T2.PERIOD_YEAR,
              T1.PARENT_CODE,
              T1.PARENT_CN_NAME,
              T1.PARENT_LEVEL,
              T1.DIMENSION_TYPE,
              T1.GROUP_CODE,
              T1.GROUP_CN_NAME,
              T1.LV0_PROD_LIST_CODE,
              T1.LV0_PROD_LIST_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.CALIBER_FLAG,
              T1.VIEW_FLAG,
              T1.CUSTOM_ID,
              T1.CUSTOM_CN_NAME
          FROM DIM_TEAM_TMP T1,PERIOD_YEAR_TMP T2
  )
    SELECT T1.PERIOD_YEAR,
           T1.PARENT_CODE,
           T1.PARENT_CN_NAME,
           T1.PARENT_LEVEL,
           T1.DIMENSION_TYPE,
           T1.GROUP_CODE,
           T1.GROUP_CN_NAME,
           T1.LV0_PROD_LIST_CODE,
           T1.LV0_PROD_LIST_CN_NAME,
           T1.OVERSEA_FLAG,
           T1.CALIBER_FLAG,
           T1.CUSTOM_ID,
           T1.CUSTOM_CN_NAME,
           T1.VIEW_FLAG,
           GS_ENCRYPT( T2.RMB_AVG_AMT,'''||V_KEYSTR||''',''AES128'',''CBC'',''SHA256'' ) AS AVG_AMT,
           GS_ENCRYPT( T2.RMB_SUM_AMT,'''||V_KEYSTR||''',''AES128'',''CBC'',''SHA256'' ) AS SUM_AMT,
           T2.RMB_SUM_QTY AS SUM_QTY,
           DECODE(T2.RMB_AVG_AMT, NULL, ''Y'', ''N'') AS APD_FLAG, --补齐标识：Y为补齐，N为原始
           DECODE(T2.RMB_AVG_AMT, NULL, 0, 1) AS NULL_FLAG --空标识, 用于sum开窗累计
       FROM CONTIN_DIM_TMP T1
       LEFT JOIN VIEW_AVG_TMP T2
       ON T1.VIEW_FLAG = T2.VIEW_FLAG
       AND NVL(T1.PARENT_CODE,''SNULL'') = NVL(T2.PARENT_CODE,''SNULL'')
       AND NVL(T1.PARENT_LEVEL,''SNULL'') = NVL(T2.PARENT_LEVEL,''SNULL'')
       AND T1.DIMENSION_TYPE = T2.DIMENSION_TYPE
       AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
       AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
       AND T1.CUSTOM_ID = T2.CUSTOM_ID
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
       WHERE T1.PARENT_LEVEL IS NOT NULL ;';

       DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
                DBMS_OUTPUT.PUT_LINE('15:-----------');


     --写入日志
   V_STEP_MUM := V_STEP_MUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_MUM,
    F_CAL_LOG_DESC => '计算年均本数据插入到临时表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');

    -- 插入补齐后的分视角下年均本数据
        V_SQL := '
        INSERT INTO ' || V_TO_TABLE ||' (
                      VERSION_ID,
                      PERIOD_YEAR,
                      CUSTOM_ID,
                      CUSTOM_CN_NAME,
                      PARENT_CODE,
                      PARENT_CN_NAME,
                      PARENT_LEVEL,
                      GRANULARITY_TYPE,
                      GROUP_CODE,
                      GROUP_CN_NAME,
                      VIEW_FLAG,
                      RMB_AVG_AMT,
                      RMB_COST_AMT ,
                      SHIP_QUANTITY ,
                      APPEND_FLAG,
                      APPEND_YEAR,
                      CALIBER_FLAG,
                      OVERSEA_FLAG,
                      LV0_PROD_LIST_CODE,
                      LV0_PROD_LIST_CN_NAME,
                      CREATED_BY,
                      CREATION_DATE,
                      LAST_UPDATED_BY,
                      LAST_UPDATE_DATE,
                      DEL_FLAG )
 -- 按不同视角，补齐对应的重量级团队，以及采购信息维，补齐年均本
 WITH FILLER_TEMP AS
     (
      SELECT SS.*,
             FIRST_VALUE(SS.AVG_AMT) OVER(PARTITION BY SS.PARENT_CODE, SS.PARENT_LEVEL, SS.DIMENSION_TYPE, SS.GROUP_CODE, SS.LV0_PROD_LIST_CODE, SS.OVERSEA_FLAG, SS.CALIBER_FLAG,SS.CUSTOM_ID,AVG_AMT_FLAG_F ORDER BY SS.PERIOD_YEAR) AS AVG_AMT_F, --向前新补齐的均价字段
             FIRST_VALUE(SS.PERIOD_YEAR) OVER(PARTITION BY SS.PARENT_CODE, SS.PARENT_LEVEL, SS.DIMENSION_TYPE, SS.GROUP_CODE, SS.LV0_PROD_LIST_CODE, SS.OVERSEA_FLAG, SS.CALIBER_FLAG,SS.CUSTOM_ID,AVG_AMT_FLAG_F ORDER BY SS.PERIOD_YEAR) AS PERIOD_YEAR_F, --向前新补齐的年份字段
             FIRST_VALUE(SS.AVG_AMT) OVER(PARTITION BY SS.PARENT_CODE, SS.PARENT_LEVEL, SS.DIMENSION_TYPE, SS.GROUP_CODE, SS.LV0_PROD_LIST_CODE, SS.OVERSEA_FLAG, SS.CALIBER_FLAG,SS.CUSTOM_ID,AVG_AMT_FLAG_B ORDER BY SS.PERIOD_YEAR DESC) AS AVG_AMT_B, --向后新补齐的均价字段
             FIRST_VALUE(SS.PERIOD_YEAR) OVER(PARTITION BY SS.PARENT_CODE, SS.PARENT_LEVEL, SS.DIMENSION_TYPE, SS.GROUP_CODE, SS.LV0_PROD_LIST_CODE, SS.OVERSEA_FLAG, SS.CALIBER_FLAG,SS.CUSTOM_ID,AVG_AMT_FLAG_B ORDER BY SS.PERIOD_YEAR DESC) AS PERIOD_YEAR_B --向后新补齐的年份字段
         FROM (SELECT S.*,
                      SUM(S.NULL_FLAG) OVER(PARTITION BY S.PARENT_CODE, S.PARENT_LEVEL, S.DIMENSION_TYPE, S.GROUP_CODE, S.LV0_PROD_LIST_CODE, S.OVERSEA_FLAG, S.CALIBER_FLAG,S.CUSTOM_ID ORDER BY S.PERIOD_YEAR) AS AVG_AMT_FLAG_F, --分组累加0，1，标识；当结果值一样说明，后面的标识为0，金额是空；结果值一样的正好在分一个组，都取第一个值，即可向前补齐
                      SUM(S.NULL_FLAG) OVER(PARTITION BY S.PARENT_CODE, S.PARENT_LEVEL, S.DIMENSION_TYPE, S.GROUP_CODE, S.LV0_PROD_LIST_CODE, S.OVERSEA_FLAG, S.CALIBER_FLAG,S.CUSTOM_ID ORDER BY S.PERIOD_YEAR DESC) AS AVG_AMT_FLAG_B
                  FROM '||V_TMP_TABLE||' S) SS)


    SELECT '||V_VERSION_ID||' AS VERSION_ID,
           S.PERIOD_YEAR,
           S.CUSTOM_ID,
           S.CUSTOM_CN_NAME,
           S.PARENT_CODE,
           S.PARENT_CN_NAME,
           S.PARENT_LEVEL,
           S.DIMENSION_TYPE,
           S.GROUP_CODE,
           S.GROUP_CN_NAME,
           S.VIEW_FLAG,
           NVL(NVL(S.AVG_AMT,S.AVG_AMT_F), S.AVG_AMT_B) AS RMB_AVG_AMT,
           S.SUM_AMT AS SUM_AMT,
           S.SUM_QTY ,
           S.APD_FLAG ,
           CASE  WHEN APD_FLAG=''Y'' AND AVG_AMT_F IS NOT NULL THEN PERIOD_YEAR_F
                  ELSE '''' END AS  APPEND_YEAR,
           S.CALIBER_FLAG,
           S.OVERSEA_FLAG,
           S.LV0_PROD_LIST_CODE,
           S.LV0_PROD_LIST_CN_NAME,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
       FROM  FILLER_TEMP S; ';


        EXECUTE IMMEDIATE V_SQL;
        DBMS_OUTPUT.PUT_LINE('16:-----------');

 --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '补齐年均本数据，并插入版本号为：'||V_VERSION_ID||'的全量加密数据到'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

   --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;

  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION

  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';

  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败',
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

