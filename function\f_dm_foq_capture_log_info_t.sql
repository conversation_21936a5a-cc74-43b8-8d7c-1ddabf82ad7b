-- Name: f_dm_foq_capture_log_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foq_capture_log_info_t(p_log_version_id integer DEFAULT NULL::integer, p_log_sp_name character varying DEFAULT NULL::character varying, p_log_para_list character varying DEFAULT NULL::character varying, p_log_step_num integer DEFAULT NULL::integer, p_log_cal_log_desc character varying DEFAULT NULL::character varying, p_log_formula_sql_txt text DEFAULT NULL::text, p_log_row_count integer DEFAULT NULL::integer, p_log_errbuf character varying DEFAULT NULL::character varying)
 RETURNS void
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$ 

 --===============================================================
  -- package name :  f_dm_foq_capture_log_info_t
  -- purpose      :  品类建模-记录日志信息，包括异常信息
  -- parameter    :  
  -- author       :  
  -- date         :  
  -- history      :  修改记录
  --===============================================================

declare
  v_sp_name varchar(50) = 'f_dm_foq_capture_log_info_t';

begin
  insert into fin_dm_opt_foi.dm_foq_capture_log_info_t
    (log_id,
     version_id,
     sp_name,
     para_list,
     step_num,
     cal_log_desc,
     formula_sql_txt,
     dml_row_count,
     result_status,
     errbuf,
     created_by,
     creation_date)
  values
    (fin_dm_opt_foi.dm_foq_capture_log_info_s.nextval,
     p_log_version_id,
     p_log_sp_name,
     p_log_para_list,
     p_log_step_num,
     p_log_cal_log_desc,
     p_log_formula_sql_txt,
     p_log_row_count,
     '1',  -- 执行成功
     p_log_errbuf,
     - 1,
     clock_timestamp());
     
 -- 异常处理   
 exception
  when others then
  --raise notice 'fin_dm_opt_foi.f_dm_foq_capture_log_info_t 日志函数报错,请检查!';
  insert into fin_dm_opt_foi.dm_foq_capture_log_info_t
    (log_id,
     version_id,
     sp_name,
     para_list,
     step_num,
     cal_log_desc,
     formula_sql_txt,
     dml_row_count,
     result_status,
     errbuf,
     created_by,
     creation_date)
  VALUES
    (fin_dm_opt_foi.dm_foq_capture_log_info_s.nextval,
     null, --版本信息
		 v_sp_name, --sp名称
		 null, --输入参数
		 null, --执行步骤
		 v_sp_name||'：运行错误', --日志描述
		 sqlerrm, --异常信息
		 null,    --数据量
		 '2001',  -- 执行失败
		 sqlstate, --异常编码
		 -1,       --创建人
     clock_timestamp());

end;
$$
/

