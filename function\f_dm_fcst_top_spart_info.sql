-- Name: f_dm_fcst_top_spart_info; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_top_spart_info(f_cost_type character varying, f_granularity_type character varying, f_view_flag character varying, f_keystr character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
创建人：lwx1165532
最后修改人：TWX1139790
背景描述：查找TOP_SPARK并打上标记
		  删除单TOP_SPART
事例：SELECT FIN_DM_OPT_FOI.F_DM_FCST_TOP_SPART_INFO()
变更记录-202503：
限制来源表版本号，避免取到多余数据
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FCST_TOP_SPART_INFO'; --存储过程名称
  V_VERSION_ID BIGINT; --新的版本号, 取自序列:FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S
  V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYY-MM'); --新的版本中文名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 存放当前是否已有版本号标识
  
  V_DIM_VERSION BIGINT  ; --主力编码版本号
  -- 7月版本需求新增
  V_SQL        TEXT;   --SQL逻辑
  V_LV0_PROD_PARA VARCHAR(150);
  V_LV1_PROD_PARA VARCHAR(150);  
  V_LV2_PROD_PARA VARCHAR(150); 
  V_LV3_PROD_PARA VARCHAR(150); 
  V_LV4_PROD_PARA VARCHAR(150); 
  V_LV0_PROD_CODE VARCHAR(150);
  V_LV1_PROD_CODE VARCHAR(150);  
  V_LV2_PROD_CODE VARCHAR(150); 
  V_LV3_PROD_CODE VARCHAR(150); 
  V_LV4_PROD_CODE VARCHAR(150);
  V_INSERT_LV0_PROD_CODE VARCHAR(150);
  V_INSERT_LV1_PROD_CODE VARCHAR(150);  
  V_INSERT_LV2_PROD_CODE VARCHAR(150); 
  V_INSERT_LV3_PROD_CODE VARCHAR(150); 
  V_INSERT_LV4_PROD_CODE VARCHAR(150);
  
  V_DIM_INSERT_LV1_PROD_CODE VARCHAR(150);  
  V_DIM_INSERT_LV2_PROD_CODE VARCHAR(150); 
  V_DIM_INSERT_LV3_PROD_CODE VARCHAR(150); 
  V_DIM_INSERT_LV4_PROD_CODE VARCHAR(150);
  
  V_FROM_TABLE VARCHAR(50); 
  V_TO_TABLE VARCHAR(50);
  V_JOIN_TABLE VARCHAR(50);
 
  V_IN_LV0_PROD_PARA VARCHAR(150);
  V_IN_LV1_PROD_PARA VARCHAR(150);  
  V_IN_LV2_PROD_PARA VARCHAR(150); 
  V_IN_LV3_PROD_PARA VARCHAR(150); 
  V_IN_LV4_PROD_PARA VARCHAR(150);
 
  V_RMB_COST_AMT VARCHAR(200);
  V_RMB_PARA VARCHAR(200);
  V_SOFTWARE VARCHAR(200);
  
BEGIN
  X_RESULT_STATUS = '1';
     
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG
   );

  
   --判断PBI维度和成本类型选择来源表,目标表,关联表,金额字段
  IF F_COST_TYPE = 'PSP' AND F_GRANULARITY_TYPE = 'IRB' THEN 
	 V_FROM_TABLE := 'DM_FCST_ICT_PSP_IRB_MID_MON_SPART_T';
	 V_TO_TABLE := 'DM_FCST_ICT_PSP_IRB_TOP_SPART_INFO_T'; --重量级团队目录
  ELSIF F_COST_TYPE = 'PSP' AND F_GRANULARITY_TYPE = 'INDUS' THEN 
	 V_FROM_TABLE := 'DM_FCST_ICT_PSP_INDUS_MID_MON_SPART_T';
	 V_TO_TABLE := 'DM_FCST_ICT_PSP_INDUS_TOP_SPART_INFO_T'; --产业目录
  ELSIF F_COST_TYPE = 'PSP' AND F_GRANULARITY_TYPE = 'PROD' THEN 
	 V_FROM_TABLE := 'DM_FCST_ICT_PSP_PROD_MID_MON_SPART_T';
	 V_TO_TABLE := 'DM_FCST_ICT_PSP_PROD_TOP_SPART_INFO_T'; --销售目录
  ELSIF F_COST_TYPE = 'STD' AND F_GRANULARITY_TYPE = 'IRB' THEN 
	 V_FROM_TABLE := 'DM_FCST_ICT_STD_IRB_MID_MON_SPART_T';
	 V_TO_TABLE := 'DM_FCST_ICT_STD_IRB_TOP_SPART_INFO_T'; --重量级团队目录
  ELSIF F_COST_TYPE = 'STD' AND F_GRANULARITY_TYPE = 'INDUS' THEN 
	 V_FROM_TABLE := 'DM_FCST_ICT_STD_INDUS_MID_MON_SPART_T';
	 V_TO_TABLE := 'DM_FCST_ICT_STD_INDUS_TOP_SPART_INFO_T'; --产业目录
  ELSIF F_COST_TYPE = 'STD' AND F_GRANULARITY_TYPE = 'PROD' THEN 
	 V_FROM_TABLE := 'DM_FCST_ICT_STD_PROD_MID_MON_SPART_T';
	 V_TO_TABLE := 'DM_FCST_ICT_STD_PROD_TOP_SPART_INFO_T'; --销售目录
	 
  ELSE 
	RETURN '入参有误';
  END IF;
  
 
   
     --判断成本类型获得主力编码表和金额字段
  IF F_COST_TYPE = 'PSP' THEN 
	 V_JOIN_TABLE := 'DM_FCST_ICT_PSP_PBI_MAIN_CODE_DIM_T';
     V_RMB_COST_AMT := ' SUM(RMB_COST_AMT) AS RMB_COST_AMT ';
	 V_RMB_PARA :=  ' AND RMB_COST_AMT IS NOT NULL ';
	 
  ELSIF f_cost_type = 'STD'  THEN 
	 V_JOIN_TABLE := 'DM_FCST_ICT_STD_PBI_MAIN_CODE_DIM_T';
	 V_RMB_COST_AMT :=  ' SUM(TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256''))) AS RMB_COST_AMT ';
	 V_RMB_PARA :=  ' AND TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256'')) IS NOT NULL ';
	 
  ELSE 
	RETURN '入参有误';
  END IF;
  
  
--判断PBI维度选择PBI字段	
  IF  F_GRANULARITY_TYPE = 'IRB' THEN 
	V_LV0_PROD_PARA :=' LV0_PROD_RND_TEAM_CODE ,LV0_PROD_RD_TEAM_CN_NAME, ';
	V_LV1_PROD_PARA :='	LV1_PROD_RND_TEAM_CODE ,LV1_PROD_RD_TEAM_CN_NAME, ';  
	V_LV2_PROD_PARA :=' LV2_PROD_RND_TEAM_CODE ,LV2_PROD_RD_TEAM_CN_NAME, '; 
	V_LV3_PROD_PARA :=' LV3_PROD_RND_TEAM_CODE ,LV3_PROD_RD_TEAM_CN_NAME, '; 
	V_LV4_PROD_PARA :=' LV4_PROD_RND_TEAM_CODE ,LV4_PROD_RD_TEAM_CN_NAME, '; 
    V_LV0_PROD_CODE :='LV0_PROD_RND_TEAM_CODE,';
	V_LV1_PROD_CODE :='LV1_PROD_RND_TEAM_CODE,';  
	V_LV2_PROD_CODE :='LV2_PROD_RND_TEAM_CODE,'; 
	V_LV3_PROD_CODE :='LV3_PROD_RND_TEAM_CODE,'; 
	V_LV4_PROD_CODE :='LV4_PROD_RND_TEAM_CODE,';
	
	V_INSERT_LV0_PROD_CODE :=' AND A.LV0_PROD_RND_TEAM_CODE = B.LV0_PROD_RND_TEAM_CODE';
	V_INSERT_LV1_PROD_CODE :=' AND A.LV1_PROD_RND_TEAM_CODE = B.LV1_PROD_RND_TEAM_CODE';  
	V_INSERT_LV2_PROD_CODE :=' AND A.LV2_PROD_RND_TEAM_CODE = B.LV2_PROD_RND_TEAM_CODE'; 
	V_INSERT_LV3_PROD_CODE :=' AND A.LV3_PROD_RND_TEAM_CODE = B.LV3_PROD_RND_TEAM_CODE'; 
	V_INSERT_LV4_PROD_CODE :=' AND A.LV4_PROD_RND_TEAM_CODE = B.LV4_PROD_RND_TEAM_CODE';
	
	
	V_DIM_INSERT_LV1_PROD_CODE :=' AND A.LV1_PROD_RND_TEAM_CODE = M.LV1_PROD_RND_TEAM_CODE';  
	V_DIM_INSERT_LV2_PROD_CODE :=' AND A.LV2_PROD_RND_TEAM_CODE = M.LV2_PROD_RND_TEAM_CODE'; 
	V_DIM_INSERT_LV3_PROD_CODE :=' AND A.LV3_PROD_RND_TEAM_CODE = M.LV3_PROD_RND_TEAM_CODE'; 
	V_DIM_INSERT_LV4_PROD_CODE :=' AND A.LV4_PROD_RND_TEAM_CODE = M.LV4_PROD_RND_TEAM_CODE';
	
	
	V_IN_LV0_PROD_PARA :=' A.LV0_PROD_RND_TEAM_CODE ,A.LV0_PROD_RD_TEAM_CN_NAME, ';
	V_IN_LV1_PROD_PARA :=' A.LV1_PROD_RND_TEAM_CODE ,A.LV1_PROD_RD_TEAM_CN_NAME, ';  
	V_IN_LV2_PROD_PARA :=' A.LV2_PROD_RND_TEAM_CODE ,A.LV2_PROD_RD_TEAM_CN_NAME, '; 
	V_IN_LV3_PROD_PARA :=' A.LV3_PROD_RND_TEAM_CODE ,A.LV3_PROD_RD_TEAM_CN_NAME, '; 
	V_IN_LV4_PROD_PARA :=' A.LV4_PROD_RND_TEAM_CODE ,A.LV4_PROD_RD_TEAM_CN_NAME, ';
	
	
  ELSIF  F_GRANULARITY_TYPE = 'INDUS' THEN 
	V_LV0_PROD_PARA :=' LV0_INDUSTRY_CATG_CODE ,LV0_INDUSTRY_CATG_CN_NAME,';
	V_LV1_PROD_PARA :='	LV1_INDUSTRY_CATG_CODE ,LV1_INDUSTRY_CATG_CN_NAME,';  
	V_LV2_PROD_PARA :=' LV2_INDUSTRY_CATG_CODE ,LV2_INDUSTRY_CATG_CN_NAME,'; 
	V_LV3_PROD_PARA :=' LV3_INDUSTRY_CATG_CODE ,LV3_INDUSTRY_CATG_CN_NAME,'; 
	V_LV4_PROD_PARA :=' LV4_INDUSTRY_CATG_CODE ,LV4_INDUSTRY_CATG_CN_NAME,'; 
	V_LV0_PROD_CODE :='LV0_INDUSTRY_CATG_CODE,';
	V_LV1_PROD_CODE :='LV1_INDUSTRY_CATG_CODE,';  
	V_LV2_PROD_CODE :='LV2_INDUSTRY_CATG_CODE,'; 
	V_LV3_PROD_CODE :='LV3_INDUSTRY_CATG_CODE,'; 
	V_LV4_PROD_CODE :='LV4_INDUSTRY_CATG_CODE,';
	
	V_INSERT_LV0_PROD_CODE :=' AND A.LV0_INDUSTRY_CATG_CODE = B.LV0_INDUSTRY_CATG_CODE';
	V_INSERT_LV1_PROD_CODE :=' AND A.LV1_INDUSTRY_CATG_CODE = B.LV1_INDUSTRY_CATG_CODE';  
	V_INSERT_LV2_PROD_CODE :=' AND A.LV2_INDUSTRY_CATG_CODE = B.LV2_INDUSTRY_CATG_CODE'; 
	V_INSERT_LV3_PROD_CODE :=' AND A.LV3_INDUSTRY_CATG_CODE = B.LV3_INDUSTRY_CATG_CODE'; 
	V_INSERT_LV4_PROD_CODE :=' AND A.LV4_INDUSTRY_CATG_CODE = B.LV4_INDUSTRY_CATG_CODE';
	
	V_DIM_INSERT_LV1_PROD_CODE :=' AND A.LV1_INDUSTRY_CATG_CODE = M.LV1_INDUSTRY_CATG_CODE';  
	V_DIM_INSERT_LV2_PROD_CODE :=' AND A.LV2_INDUSTRY_CATG_CODE = M.LV2_INDUSTRY_CATG_CODE'; 
	V_DIM_INSERT_LV3_PROD_CODE :=' AND A.LV3_INDUSTRY_CATG_CODE = M.LV3_INDUSTRY_CATG_CODE'; 
	V_DIM_INSERT_LV4_PROD_CODE :=' AND A.LV4_INDUSTRY_CATG_CODE = M.LV4_INDUSTRY_CATG_CODE';
	
	V_IN_LV0_PROD_PARA :=' A.LV0_INDUSTRY_CATG_CODE ,A.LV0_INDUSTRY_CATG_CN_NAME,';
	V_IN_LV1_PROD_PARA :=' A.LV1_INDUSTRY_CATG_CODE ,A.LV1_INDUSTRY_CATG_CN_NAME,';  
	V_IN_LV2_PROD_PARA :=' A.LV2_INDUSTRY_CATG_CODE ,A.LV2_INDUSTRY_CATG_CN_NAME,'; 
	V_IN_LV3_PROD_PARA :=' A.LV3_INDUSTRY_CATG_CODE ,A.LV3_INDUSTRY_CATG_CN_NAME,'; 
	V_IN_LV4_PROD_PARA :=' A.LV4_INDUSTRY_CATG_CODE ,A.LV4_INDUSTRY_CATG_CN_NAME,'; 
	
  ELSIF  F_GRANULARITY_TYPE = 'PROD' THEN 
	V_LV0_PROD_PARA :=' LV0_PROD_LIST_CODE,LV0_PROD_LIST_CN_NAME,';
	V_LV1_PROD_PARA :='	LV1_PROD_LIST_CODE,LV1_PROD_LIST_CN_NAME,';  
	V_LV2_PROD_PARA :=' LV2_PROD_LIST_CODE,LV2_PROD_LIST_CN_NAME,'; 
	V_LV3_PROD_PARA :=' LV3_PROD_LIST_CODE,LV3_PROD_LIST_CN_NAME,'; 
	V_LV4_PROD_PARA :=' LV4_PROD_LIST_CODE,LV4_PROD_LIST_CN_NAME,'; 
	V_LV0_PROD_CODE :='LV0_PROD_LIST_CODE,';
	V_LV1_PROD_CODE :='LV1_PROD_LIST_CODE,';  
	V_LV2_PROD_CODE :='LV2_PROD_LIST_CODE,'; 
	V_LV3_PROD_CODE :='LV3_PROD_LIST_CODE,'; 
	V_LV4_PROD_CODE :='LV4_PROD_LIST_CODE,';
	
	V_INSERT_LV0_PROD_CODE :=' AND A.LV0_PROD_LIST_CODE = B.LV0_PROD_LIST_CODE';
	V_INSERT_LV1_PROD_CODE :=' AND A.LV1_PROD_LIST_CODE = B.LV1_PROD_LIST_CODE';  
	V_INSERT_LV2_PROD_CODE :=' AND A.LV2_PROD_LIST_CODE = B.LV2_PROD_LIST_CODE'; 
	V_INSERT_LV3_PROD_CODE :=' AND A.LV3_PROD_LIST_CODE = B.LV3_PROD_LIST_CODE'; 
	V_INSERT_LV4_PROD_CODE :=' AND A.LV4_PROD_LIST_CODE = B.LV4_PROD_LIST_CODE';
	
	
	V_DIM_INSERT_LV1_PROD_CODE :=' AND A.LV1_PROD_LIST_CODE = M.LV1_PROD_LIST_CODE';  
	V_DIM_INSERT_LV2_PROD_CODE :=' AND A.LV2_PROD_LIST_CODE = M.LV2_PROD_LIST_CODE'; 
	V_DIM_INSERT_LV3_PROD_CODE :=' AND A.LV3_PROD_LIST_CODE = M.LV3_PROD_LIST_CODE'; 
	V_DIM_INSERT_LV4_PROD_CODE :=' AND A.LV4_PROD_LIST_CODE = M.LV4_PROD_LIST_CODE';
	
	V_IN_LV0_PROD_PARA :=' A.LV0_PROD_LIST_CODE,A.LV0_PROD_LIST_CN_NAME,';
	V_IN_LV1_PROD_PARA :='	A.LV1_PROD_LIST_CODE,A.LV1_PROD_LIST_CN_NAME,';  
	V_IN_LV2_PROD_PARA :=' A.LV2_PROD_LIST_CODE,A.LV2_PROD_LIST_CN_NAME,'; 
	V_IN_LV3_PROD_PARA :=' A.LV3_PROD_LIST_CODE,A.LV3_PROD_LIST_CN_NAME,'; 
	V_IN_LV4_PROD_PARA :=' A.LV4_PROD_LIST_CODE,A.LV4_PROD_LIST_CN_NAME,'; 
	
	
  ELSE 
	NULL ;
  END IF;
  
  IF F_VIEW_FLAG = 'DIMENSION' THEN 
	RETURN '路径2不参与TOP_SPARK计算';
  
  END IF;
  
  
  --软硬件标识
  V_SOFTWARE = ' SOFTWARE_MARK,';
  
  
  --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'MONTH'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
  
   --主力编码版本号赋值
  SELECT VERSION_ID  INTO V_DIM_VERSION
    FROM
       DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '往版本信息表记录TOP_SPART版本号信息, 版本号='||V_VERSION_ID||',成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   --删除当前会计期版本的TOP_SPART数据, 支持单月重刷
  V_SQL := 'DELETE FROM '||V_TO_TABLE||' T WHERE T.VERSION_ID = '''||V_VERSION_ID||'''';
  EXECUTE IMMEDIATE V_SQL ;
  
   --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除TOP_SPART清单的数据, 删除版本='||V_VERSION_ID||',成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
--创建临时表, 用于插入权重数据(总地区(全球)总BG(集团))
  DROP TABLE IF EXISTS DM_FOC_TOP_SPART_INFO_TEMP;
  CREATE TEMPORARY TABLE DM_FOC_TOP_SPART_INFO_TEMP
  (
	VERSION_ID INTEGER,
	VERSION_NAME VARCHAR(30),
	PERIOD_YEAR VARCHAR(30),
	YEAR_FLAG VARCHAR2(2),
	LV0_PROD_RND_TEAM_CODE 	VARCHAR(50),
	LV0_PROD_RD_TEAM_CN_NAME VARCHAR(200),
	LV1_PROD_RND_TEAM_CODE 	VARCHAR(50),
	LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200),
	LV2_PROD_RND_TEAM_CODE 	VARCHAR(50),
	LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200),
	LV3_PROD_RND_TEAM_CODE 	VARCHAR(50),
	LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200),
	LV4_PROD_RND_TEAM_CODE 	VARCHAR(50),
	LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200),
	LV0_INDUSTRY_CATG_CODE VARCHAR(50),
	LV0_INDUSTRY_CATG_CN_NAME VARCHAR(200),
	LV1_INDUSTRY_CATG_CODE VARCHAR(50),
	LV1_INDUSTRY_CATG_CN_NAME VARCHAR(200),
	LV2_INDUSTRY_CATG_CODE VARCHAR(50),
	LV2_INDUSTRY_CATG_CN_NAME VARCHAR(200),
	LV3_INDUSTRY_CATG_CODE VARCHAR(50),
	LV3_INDUSTRY_CATG_CN_NAME VARCHAR(200),
	LV4_INDUSTRY_CATG_CODE VARCHAR(50),
	LV4_INDUSTRY_CATG_CN_NAME VARCHAR(200),
	LV0_PROD_LIST_CODE	VARCHAR(50),
	LV0_PROD_LIST_CN_NAME VARCHAR(200),
	LV1_PROD_LIST_CODE	VARCHAR(50),
	LV1_PROD_LIST_CN_NAME VARCHAR(200),
	LV2_PROD_LIST_CODE	VARCHAR(50),
	LV2_PROD_LIST_CN_NAME VARCHAR(200),
	LV3_PROD_LIST_CODE	VARCHAR(50),
	LV3_PROD_LIST_CN_NAME VARCHAR(200),
	LV4_PROD_LIST_CODE	VARCHAR(50),
	LV4_PROD_LIST_CN_NAME VARCHAR(200),
	REGION_CODE VARCHAR(50),
	REGION_CN_NAME VARCHAR(200),
	REPOFFICE_CODE VARCHAR(50),
	REPOFFICE_CN_NAME VARCHAR(200),
	SPART_CODE VARCHAR(50),
	SPART_CN_NAME VARCHAR(50),
	BG_CODE VARCHAR(50),
	BG_CN_NAME VARCHAR(200),
	OVERSEA_FLAG VARCHAR(10),
	VIEW_FLAG VARCHAR(50),
	WEIGHT_RATE NUMERIC,
	IS_TOP_FLAG VARCHAR(1),
	DOUBLE_FLAG VARCHAR(1),
	MAIN_FLAG VARCHAR(2),
	CODE_ATTRIBUTES VARCHAR(50),
	SOFTWARE_MARK VARCHAR(100)
  )
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN;
  
  --创建临时表, 用于插入权重数据--分地区(全球、国内、海外)分BG去计算TOP品类权重 (9月版本需求新增)
  DROP TABLE IF EXISTS DM_FOC_WEIGHT_SPART_INFO_TEMP;
  CREATE TEMPORARY TABLE DM_FOC_WEIGHT_SPART_INFO_TEMP
  (
	VERSION_ID INTEGER,
	VERSION_NAME VARCHAR(30),
	PERIOD_YEAR VARCHAR(30),
	YEAR_FLAG VARCHAR2(2),
	LV0_PROD_RND_TEAM_CODE 	VARCHAR(50),
	LV0_PROD_RD_TEAM_CN_NAME 	VARCHAR(200),
	LV1_PROD_RND_TEAM_CODE 	VARCHAR(50),
	LV1_PROD_RD_TEAM_CN_NAME 	VARCHAR(200),
	LV2_PROD_RND_TEAM_CODE 	VARCHAR(50),
	LV2_PROD_RD_TEAM_CN_NAME 	VARCHAR(200),
	LV3_PROD_RND_TEAM_CODE 	VARCHAR(50),
	LV3_PROD_RD_TEAM_CN_NAME 	VARCHAR(200),
	LV4_PROD_RND_TEAM_CODE 	VARCHAR(50),
	LV4_PROD_RD_TEAM_CN_NAME 	VARCHAR(200),
	LV0_INDUSTRY_CATG_CODE VARCHAR(50),
	LV0_INDUSTRY_CATG_CN_NAME VARCHAR(200),
	LV1_INDUSTRY_CATG_CODE VARCHAR(50),
	LV1_INDUSTRY_CATG_CN_NAME VARCHAR(200),
	LV2_INDUSTRY_CATG_CODE VARCHAR(50),
	LV2_INDUSTRY_CATG_CN_NAME VARCHAR(200),
	LV3_INDUSTRY_CATG_CODE VARCHAR(50),
	LV3_INDUSTRY_CATG_CN_NAME VARCHAR(200),
	LV4_INDUSTRY_CATG_CODE VARCHAR(50),
	LV4_INDUSTRY_CATG_CN_NAME VARCHAR(200),
	LV0_PROD_LIST_CODE	VARCHAR(50),
	LV0_PROD_LIST_CN_NAME	VARCHAR(200),
	LV1_PROD_LIST_CODE	VARCHAR(50),
	LV1_PROD_LIST_CN_NAME	VARCHAR(200),
	LV2_PROD_LIST_CODE	VARCHAR(50),
	LV2_PROD_LIST_CN_NAME	VARCHAR(200),
	LV3_PROD_LIST_CODE	VARCHAR(50),
	LV3_PROD_LIST_CN_NAME	VARCHAR(200),
	LV4_PROD_LIST_CODE	VARCHAR(50),
	LV4_PROD_LIST_CN_NAME	VARCHAR(200),
	REGION_CODE VARCHAR(50),
	REGION_CN_NAME VARCHAR(200),
	REPOFFICE_CODE VARCHAR(50),
	REPOFFICE_CN_NAME VARCHAR(200),
	SPART_CODE VARCHAR(50),
	SPART_CN_NAME VARCHAR(50),
	BG_CODE VARCHAR(50),
	BG_CN_NAME VARCHAR(200),
	OVERSEA_FLAG VARCHAR(10),
	VIEW_FLAG VARCHAR(50),
	WEIGHT_RATE NUMERIC,
	IS_TOP_FLAG VARCHAR(1),
	DOUBLE_FLAG VARCHAR(1),
	MAIN_FLAG VARCHAR(2),
	CODE_ATTRIBUTES VARCHAR(50),
	SOFTWARE_MARK VARCHAR(100)
  )
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN;
    
  --第一步先往临时表里插数: 分视角分地区(全球、国内、海外)分BG计算前三年和今年YTD,以及去年+今年YTD的TOP品类及权重
     
	 V_SQL := 
      'INSERT INTO DM_FOC_WEIGHT_SPART_INFO_TEMP
        ( 	
			VERSION_ID,
			VERSION_NAME,
			PERIOD_YEAR,
           '||V_LV0_PROD_PARA 
			 ||V_LV1_PROD_PARA  
			 ||V_LV2_PROD_PARA 
			 ||V_LV3_PROD_PARA  
			 ||V_LV4_PROD_PARA||'
			REGION_CODE,
			REGION_CN_NAME,
			REPOFFICE_CODE,
			REPOFFICE_CN_NAME,
			SPART_CODE,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			SOFTWARE_MARK,
			VIEW_FLAG,
			WEIGHT_RATE,
			DOUBLE_FLAG
        )
		WITH SUM_COST_TEMP AS  (	
      --从MID_MONTH_SPART查出各PERIOD_YEAR的金额
      SELECT  
              CASE
                WHEN T.PERIOD_YEAR  BETWEEN YEAR(CURRENT_TIMESTAMP) - 1 AND
                     YEAR(CURRENT_TIMESTAMP) THEN
                 1
                ELSE
                 0
            END AS YEAR_FLAG,
			PERIOD_YEAR,
			'||V_LV0_PROD_PARA 
			 ||V_LV1_PROD_PARA  
			 ||V_LV2_PROD_PARA 
			 ||V_LV3_PROD_PARA  
			 ||V_LV4_PROD_PARA||'
			REGION_CODE,
			REGION_CN_NAME,
			REPOFFICE_CODE,
			REPOFFICE_CN_NAME,
			SPART_CODE,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			SOFTWARE_MARK,
			'''||F_VIEW_FLAG||'''  AS VIEW_FLAG,
			'||V_RMB_COST_AMT||'
        FROM '||V_FROM_TABLE ||' T 
		WHERE VIEW_FLAG = ''PROD_SPART''
		  AND VERSION_ID = '||V_DIM_VERSION||'    -- 202503新增，加上版本号限制
		'||V_RMB_PARA||'
		GROUP BY 
		PERIOD_YEAR,
			'||V_LV0_PROD_PARA 
			 ||V_LV1_PROD_PARA  
			 ||V_LV2_PROD_PARA 
			 ||V_LV3_PROD_PARA  
			 ||V_LV4_PROD_PARA||'
			REGION_CODE,
			REGION_CN_NAME,
			REPOFFICE_CODE,
			REPOFFICE_CN_NAME,
			SPART_CODE,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			SOFTWARE_MARK,
			VIEW_FLAG),
	YEAR_AMT_TEMP AS 
	( 
      --开窗求出各年LV3.5层的总发货额
      SELECT  
			'||V_LV0_PROD_PARA 
			 ||V_LV1_PROD_PARA  
			 ||V_LV2_PROD_PARA 
			 ||V_LV3_PROD_PARA  
			 ||V_LV4_PROD_PARA||'
			REGION_CODE,
			REGION_CN_NAME,
			REPOFFICE_CODE,
			REPOFFICE_CN_NAME,
			SPART_CODE,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			SOFTWARE_MARK,
			VIEW_FLAG,
              DECODE(PERIOD_YEAR,
                     YEAR(CURRENT_TIMESTAMP),
                     PERIOD_YEAR || ''YTD'',
                     PERIOD_YEAR ::VARCHAR ) AS PERIOD_YEAR,
              RMB_COST_AMT,
              SUM(RMB_COST_AMT) OVER(PARTITION BY '||V_LV0_PROD_PARA ||V_LV1_PROD_PARA  ||V_LV2_PROD_PARA ||V_LV3_PROD_PARA  ||V_LV4_PROD_PARA||'REGION_CODE,REPOFFICE_CODE,SOFTWARE_MARK,
				BG_CODE,OVERSEA_FLAG,VIEW_FLAG,PERIOD_YEAR
			 ) AS YEAR_AMT
        FROM SUM_COST_TEMP ),
	TWO_YEAR_TEMP AS
     (
      --分视角收敛上一年到今年YTD的总金额
      SELECT  
			'||V_LV0_PROD_PARA 
			 ||V_LV1_PROD_PARA  
			 ||V_LV2_PROD_PARA 
			 ||V_LV3_PROD_PARA  
			 ||V_LV4_PROD_PARA||'
			REGION_CODE,
			REGION_CN_NAME,
			REPOFFICE_CODE,
			REPOFFICE_CN_NAME,
			SPART_CODE,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			SOFTWARE_MARK,
			VIEW_FLAG,
            SUM(A.RMB_COST_AMT) AS YEAR_AMT,
            SUM(SUM(A.RMB_COST_AMT)) OVER(PARTITION BY '||V_LV0_PROD_PARA ||V_LV1_PROD_PARA  ||V_LV2_PROD_PARA ||V_LV3_PROD_PARA  ||V_LV4_PROD_PARA||'REGION_CODE,REPOFFICE_CODE,SOFTWARE_MARK,
				BG_CODE,OVERSEA_FLAG,VIEW_FLAG ) AS TOTAL_YEAR_AMT
        FROM SUM_COST_TEMP A
       WHERE A.YEAR_FLAG IN (0, 1)
       GROUP BY  '||V_LV0_PROD_PARA 
			 ||V_LV1_PROD_PARA  
			 ||V_LV2_PROD_PARA 
			 ||V_LV3_PROD_PARA  
			 ||V_LV4_PROD_PARA||'
			REGION_CODE,
			REGION_CN_NAME,
			REPOFFICE_CODE,
			REPOFFICE_CN_NAME,
			SPART_CODE,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			SOFTWARE_MARK,
			VIEW_FLAG
			)		
    --区间年的权重
   SELECT '|| V_VERSION_ID ||' AS VERSION_ID,
          '''|| V_VERSION_NAME ||''' AS VERSION_NAME,
           YEAR(CURRENT_TIMESTAMP) - 1 || ''+'' || YEAR(CURRENT_TIMESTAMP) ||
           ''YTD'' AS PERIOD_YEAR,
           '||V_LV0_PROD_PARA 
			 ||V_LV1_PROD_PARA  
			 ||V_LV2_PROD_PARA 
			 ||V_LV3_PROD_PARA  
			 ||V_LV4_PROD_PARA||'
			REGION_CODE,
			REGION_CN_NAME,
			REPOFFICE_CODE,
			REPOFFICE_CN_NAME,
			SPART_CODE,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			SOFTWARE_MARK,
			VIEW_FLAG,
			YEAR_AMT / NULLIF(TOTAL_YEAR_AMT,0) AS WEIGHT_RATE,
           ''Y'' AS DOUBLE_FLAG
      FROM TWO_YEAR_TEMP 
    UNION ALL
    --单个会计年的权重
   SELECT '|| V_VERSION_ID ||' AS VERSION_ID,
          '''|| V_VERSION_NAME ||''' AS VERSION_NAME,
           TO_CHAR(PERIOD_YEAR) AS PERIOD_YEAR,
          '||V_LV0_PROD_PARA 
			 ||V_LV1_PROD_PARA  
			 ||V_LV2_PROD_PARA 
			 ||V_LV3_PROD_PARA  
			 ||V_LV4_PROD_PARA||'
			REGION_CODE,
			REGION_CN_NAME,
			REPOFFICE_CODE,
			REPOFFICE_CN_NAME,
			SPART_CODE,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			SOFTWARE_MARK,
			VIEW_FLAG,
			RMB_COST_AMT / NULLIF(YEAR_AMT,0) AS WEIGHT_RATE,
			''N'' AS DOUBLE_FLAG
      FROM YEAR_AMT_TEMP 
	  ';			
			
	DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;


  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '创建临时表, 并往临时表里插数, 版本号='||V_VERSION_ID||',成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

 ----------------------------------------------------------------------以上第一步先处理按照分地区(全球、国内、海外)分BG的权重数据-------------

----------------------------------------------------------------------以下第二步重新计算按照总地区(全球)总BG(集团)的角度去打TOP标签数据------------------------


       DBMS_OUTPUT.PUT_LINE('-------2   往临时表里插数: 分视角计算前三年和今年YTD,以及去年+今年YTD的TOP品类及权重 准备------------'); 
  --往临时表里插数: 分视角计算前三年和今年YTD,以及去年+今年YTD的TOP品类及权重
  V_SQL :=
  'INSERT INTO DM_FOC_TOP_SPART_INFO_TEMP
    (
		VERSION_ID,
		VERSION_NAME,
		PERIOD_YEAR,
		'||V_LV0_PROD_PARA 
		||V_LV1_PROD_PARA  
		||V_LV2_PROD_PARA 
		||V_LV3_PROD_PARA  
		||V_LV4_PROD_PARA||'
		SPART_CODE,
		VIEW_FLAG,
		WEIGHT_RATE,
		DOUBLE_FLAG
			 )
    WITH SUM_COST_TEMP AS
     (
      --分视角, 根据LV0,专家团,品类和年字段,收敛实际发货额
      SELECT  
            CASE
                WHEN T.PERIOD_YEAR :: BIGINT BETWEEN YEAR(CURRENT_TIMESTAMP) - 1 AND
                     YEAR(CURRENT_TIMESTAMP) THEN
                 1
                ELSE
                 0
            END AS YEAR_FLAG,
			PERIOD_YEAR,
			'||V_LV0_PROD_PARA 
			 ||V_LV1_PROD_PARA  
			 ||V_LV2_PROD_PARA 
			 ||V_LV3_PROD_PARA  
			 ||V_LV4_PROD_PARA||'
			REGION_CODE,
			REGION_CN_NAME,
			REPOFFICE_CODE,
			REPOFFICE_CN_NAME,
			SPART_CODE,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			SOFTWARE_MARK,
			'''||F_VIEW_FLAG||'''  AS VIEW_FLAG,
			'||V_RMB_COST_AMT||'
        FROM '||V_FROM_TABLE ||'  T
       WHERE OVERSEA_FLAG = ''G'' -- 第二步 从总地区(全球)和总BG(集团)的角度去打TOP标签
	   '||V_RMB_PARA||'
	     AND  VIEW_FLAG = ''PROD_SPART''
	     AND BG_CODE = ''GR''
		 AND REGION_CODE = ''GLOBAL''
		 AND REPOFFICE_CODE = ''ALL''
		 AND VERSION_ID = '||V_DIM_VERSION||'    -- 202503新增，加上版本号限制
       GROUP BY  PERIOD_YEAR,
				'||V_LV0_PROD_PARA 
				||V_LV1_PROD_PARA  
				||V_LV2_PROD_PARA 
				||V_LV3_PROD_PARA  
				||V_LV4_PROD_PARA||'
				SPART_CODE,
				REGION_CODE,
			REGION_CN_NAME,
			REPOFFICE_CODE,
			REPOFFICE_CN_NAME,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			SOFTWARE_MARK,
			VIEW_FLAG
				),
    
 	YEAR_AMT_TEMP AS 
	( 
      --开窗求出各年LV3.5层的总发货额
      SELECT  
			'||V_LV0_PROD_PARA 
			 ||V_LV1_PROD_PARA  
			 ||V_LV2_PROD_PARA 
			 ||V_LV3_PROD_PARA  
			 ||V_LV4_PROD_PARA||'
			SPART_CODE,
			VIEW_FLAG,
              DECODE(PERIOD_YEAR,
                     YEAR(CURRENT_TIMESTAMP),
                     PERIOD_YEAR || ''YTD'',
                     PERIOD_YEAR :: VARCHAR) AS PERIOD_YEAR,
              RMB_COST_AMT,
              SUM(RMB_COST_AMT) OVER(PARTITION BY '||V_LV0_PROD_PARA ||V_LV1_PROD_PARA  ||V_LV2_PROD_PARA ||V_LV3_PROD_PARA  ||V_LV4_PROD_PARA||'
				VIEW_FLAG,PERIOD_YEAR
			 ) AS YEAR_AMT
        FROM SUM_COST_TEMP ),
	TWO_YEAR_TEMP AS
     (
      --分视角收敛上一年到今年YTD的总金额
      SELECT  
			'||V_LV0_PROD_PARA 
			 ||V_LV1_PROD_PARA  
			 ||V_LV2_PROD_PARA 
			 ||V_LV3_PROD_PARA  
			 ||V_LV4_PROD_PARA||'
			SPART_CODE,
			VIEW_FLAG,
            SUM(A.RMB_COST_AMT) AS YEAR_AMT,
            SUM(SUM(A.RMB_COST_AMT)) OVER(PARTITION BY '||V_LV0_PROD_PARA ||V_LV1_PROD_PARA  ||V_LV2_PROD_PARA ||V_LV3_PROD_PARA  ||V_LV4_PROD_PARA||'
				VIEW_FLAG ) AS TOTAL_YEAR_AMT
        FROM SUM_COST_TEMP A
       WHERE A.YEAR_FLAG IN (0, 1)
       GROUP BY  '||V_LV0_PROD_PARA 
			 ||V_LV1_PROD_PARA  
			 ||V_LV2_PROD_PARA 
			 ||V_LV3_PROD_PARA  
			 ||V_LV4_PROD_PARA||'
			SPART_CODE,
			VIEW_FLAG
			)		
    --区间年的权重
   SELECT '|| V_VERSION_ID ||' AS VERSION_ID,
          '''|| V_VERSION_NAME ||''' AS VERSION_NAME,
           YEAR(CURRENT_TIMESTAMP) - 1 || ''+'' || YEAR(CURRENT_TIMESTAMP) ||
           ''YTD'' AS PERIOD_YEAR,
           '||V_LV0_PROD_PARA 
			 ||V_LV1_PROD_PARA  
			 ||V_LV2_PROD_PARA 
			 ||V_LV3_PROD_PARA  
			 ||V_LV4_PROD_PARA||'
			SPART_CODE,
			VIEW_FLAG,
			YEAR_AMT / NULLIF(TOTAL_YEAR_AMT,0) AS WEIGHT_RATE,
           ''Y'' AS DOUBLE_FLAG
      FROM TWO_YEAR_TEMP 
    UNION ALL
    --单个会计年的权重
   SELECT '|| V_VERSION_ID ||' AS VERSION_ID,
          '''|| V_VERSION_NAME ||''' AS VERSION_NAME,
           TO_CHAR(PERIOD_YEAR) AS PERIOD_YEAR,
          '||V_LV0_PROD_PARA 
			 ||V_LV1_PROD_PARA  
			 ||V_LV2_PROD_PARA 
			 ||V_LV3_PROD_PARA  
			 ||V_LV4_PROD_PARA||'
			SPART_CODE,
			VIEW_FLAG,
			RMB_COST_AMT / NULLIF(YEAR_AMT,0) AS WEIGHT_RATE,
			''N'' AS DOUBLE_FLAG
      FROM YEAR_AMT_TEMP 
	  ';			
			

	DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
    DBMS_OUTPUT.PUT_LINE('-------2   执行SQL2结束------------'); 

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '创建临时表, 并往临时表里插数: 分视角各年TOP品类及权重, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME||',成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
    

 --往TOP品类清单插数, 打上前95%TOP的标识
  V_SQL :=
  'INSERT INTO '||V_TO_TABLE||'
    (
		VERSION_ID,
		VERSION_NAME,
		PERIOD_YEAR,
		'||V_LV0_PROD_PARA 
	     ||V_LV1_PROD_PARA  
	     ||V_LV2_PROD_PARA 
	     ||V_LV3_PROD_PARA  
	     ||V_LV4_PROD_PARA||'
		REGION_CODE,
		REGION_CN_NAME,
		REPOFFICE_CODE,
		REPOFFICE_CN_NAME,
		TOP_SPART_CODE,
		TOP_SPART_CN_NAME,
		BG_CODE,
		BG_CN_NAME,
		OVERSEA_FLAG,
		SOFTWARE_MARK,
		VIEW_FLAG,
		WEIGHT_RATE,
		IS_TOP_FLAG,
		DOUBLE_FLAG,
		MAIN_FLAG,
		CODE_ATTRIBUTES,
		CREATED_BY,
		CREATION_DATE,
		LAST_UPDATED_BY,
		LAST_UPDATE_DATE,
		DEL_FLAG
     )
    WITH BASE_FLAG_TEMP AS
     (
      --如果品类下所有ITEM的权重值相同, 或第二位权重值到末尾权重值相同, 则最大排序值标记为1或2, 反之为一般数据情况
      SELECT 		VERSION_ID,
					PERIOD_YEAR,
					'||V_LV0_PROD_PARA 
					 ||V_LV1_PROD_PARA  
					 ||V_LV2_PROD_PARA 
					 ||V_LV3_PROD_PARA  
					 ||V_LV4_PROD_PARA||'
					SPART_CODE,
					VIEW_FLAG,
					WEIGHT_RATE,
					DOUBLE_FLAG,
					DRANK_SORT,
              MAX(DRANK_SORT) OVER(PARTITION BY '||V_LV0_PROD_PARA ||V_LV1_PROD_PARA  ||V_LV2_PROD_PARA ||V_LV3_PROD_PARA  ||V_LV4_PROD_PARA||' VIEW_FLAG  ) AS MAX_DRANK_SORT
        FROM (SELECT VERSION_ID,
					PERIOD_YEAR,
					'||V_LV0_PROD_PARA 
					 ||V_LV1_PROD_PARA  
					 ||V_LV2_PROD_PARA 
					 ||V_LV3_PROD_PARA  
					 ||V_LV4_PROD_PARA||'
					SPART_CODE,
					VIEW_FLAG,
					WEIGHT_RATE,
					DOUBLE_FLAG,
                    DENSE_RANK() OVER(PARTITION BY '||V_LV0_PROD_PARA ||V_LV1_PROD_PARA  ||V_LV2_PROD_PARA ||V_LV3_PROD_PARA  ||V_LV4_PROD_PARA||' VIEW_FLAG ORDER BY WEIGHT_RATE DESC) AS DRANK_SORT
                 FROM DM_FOC_TOP_SPART_INFO_TEMP P
                WHERE P.DOUBLE_FLAG = ''Y'') R),
    
    NORMAL_DATA_TEMP AS
     (
      --筛选一般数据情况: 最大排序值标识大于2的数据, 并带出ROW_NUMBER排序字段
      SELECT VERSION_ID,
					PERIOD_YEAR,
					'||V_LV0_PROD_PARA 
					 ||V_LV1_PROD_PARA  
					 ||V_LV2_PROD_PARA 
					 ||V_LV3_PROD_PARA  
					 ||V_LV4_PROD_PARA||'
					SPART_CODE,
					VIEW_FLAG,
					DOUBLE_FLAG,
              T.WEIGHT_RATE,
              ROW_NUMBER() OVER(PARTITION BY '||V_LV0_PROD_PARA ||V_LV1_PROD_PARA  ||V_LV2_PROD_PARA ||V_LV3_PROD_PARA  ||V_LV4_PROD_PARA||' VIEW_FLAG ORDER BY T.WEIGHT_RATE DESC) AS RRANK_SORT,
              T.DRANK_SORT,
              T.MAX_DRANK_SORT
        FROM BASE_FLAG_TEMP T
       WHERE T.MAX_DRANK_SORT > 2),
    
    FLAG_DATA_TEMP AS
     (
      --对NORMAL_DATA_TEMP临时表打上辅助列标识:(ACCU_WEIGHT->ACCU_FLAG->CAL_FLAG)
      SELECT VERSION_ID,
              PERIOD_YEAR,
					'||V_LV0_PROD_PARA 
					 ||V_LV1_PROD_PARA  
					 ||V_LV2_PROD_PARA 
					 ||V_LV3_PROD_PARA  
					 ||V_LV4_PROD_PARA||'
              SPART_CODE,
			  VIEW_FLAG,
              WEIGHT_RATE,
              ACCU_WEIGHT,
              ACCU_FLAG,
              SUM(ACCU_FLAG) OVER(PARTITION BY '||V_LV0_PROD_PARA ||V_LV1_PROD_PARA  ||V_LV2_PROD_PARA ||V_LV3_PROD_PARA  ||V_LV4_PROD_PARA||' VIEW_FLAG ORDER BY S.RRANK_SORT) AS CAL_FLAG --根据ROW_NUMBER累计求和ACCU_FLAG标识
        FROM (SELECT VERSION_ID,
					 PERIOD_YEAR,
					'||V_LV0_PROD_PARA 
					 ||V_LV1_PROD_PARA  
					 ||V_LV2_PROD_PARA 
					 ||V_LV3_PROD_PARA  
					 ||V_LV4_PROD_PARA||'
					 SPART_CODE,
					VIEW_FLAG,
                      R.WEIGHT_RATE,
                      R.ACCU_WEIGHT,
                      R.RRANK_SORT,
                      CASE
                        WHEN R.ACCU_WEIGHT >= 0.95 THEN
                         1
                        ELSE
                         0
                      END AS ACCU_FLAG --累计权重大于95,标识为1,反之为0
                 FROM (SELECT VERSION_ID,
							  PERIOD_YEAR,
							  '||V_LV0_PROD_PARA 
							  ||V_LV1_PROD_PARA  
							  ||V_LV2_PROD_PARA 
							  ||V_LV3_PROD_PARA  
							  ||V_LV4_PROD_PARA||'
							  SPART_CODE,
                              Z.WEIGHT_RATE,
                              SUM(Z.WEIGHT_RATE) OVER(PARTITION BY  '||V_LV0_PROD_PARA ||V_LV1_PROD_PARA  ||V_LV2_PROD_PARA ||V_LV3_PROD_PARA  ||V_LV4_PROD_PARA||' VIEW_FLAG  ORDER BY Z.RRANK_SORT) AS ACCU_WEIGHT, --根据ROW_NUMBER累计求和权重值
                              Z.RRANK_SORT,
							  VIEW_FLAG
                         FROM NORMAL_DATA_TEMP Z) R) S),
    TOP_DATA_TEMP AS
     ( 
      --TOP品类的数据
      SELECT 				VERSION_ID,
							  PERIOD_YEAR,
							  '||V_LV0_PROD_PARA 
							  ||V_LV1_PROD_PARA  
							  ||V_LV2_PROD_PARA 
							  ||V_LV3_PROD_PARA  
							  ||V_LV4_PROD_PARA||'
							  SPART_CODE,
							  VIEW_FLAG,
              A.WEIGHT_RATE
        FROM BASE_FLAG_TEMP A
       WHERE MAX_DRANK_SORT <= 2
      UNION ALL
      SELECT 	
			  VERSION_ID,
			  PERIOD_YEAR,
			  '||V_LV0_PROD_PARA 
			  ||V_LV1_PROD_PARA  
			  ||V_LV2_PROD_PARA 
			  ||V_LV3_PROD_PARA  
			  ||V_LV4_PROD_PARA||'
			  SPART_CODE,
			  VIEW_FLAG,
              B.WEIGHT_RATE
        FROM FLAG_DATA_TEMP B
       WHERE CAL_FLAG <= 2)
    
    --临时表左关联TOP品类临时表, 打上是否为TOP类标识
    SELECT    
	DISTINCT A.VERSION_ID,
			  A.VERSION_NAME,
			  A.PERIOD_YEAR,
			  '||V_IN_LV0_PROD_PARA 
			  ||V_IN_LV1_PROD_PARA  
			  ||V_IN_LV2_PROD_PARA 
			  ||V_IN_LV3_PROD_PARA  
			  ||V_IN_LV4_PROD_PARA||'
			A.REGION_CODE,
			A.REGION_CN_NAME,
			A.REPOFFICE_CODE,
			A.REPOFFICE_CN_NAME,
            A.SPART_CODE,
		    A.SPART_CODE AS SPART_CN_NAME,
		    A.BG_CODE,
		    A.BG_CN_NAME,
			A.OVERSEA_FLAG,
			A.SOFTWARE_MARK,
			A.VIEW_FLAG,
            A.WEIGHT_RATE,
			DECODE(B.SPART_CODE, NULL, ''N'', ''Y'') AS IS_TOP_FLAG,
			A.DOUBLE_FLAG,
			DECODE(M.SPART_CODE,NULL,''N'',''Y'') AS  MAIN_FLAG,
			M.CODE_ATTRIBUTES,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_FOC_WEIGHT_SPART_INFO_TEMP A
      LEFT JOIN TOP_DATA_TEMP B
        ON A.VIEW_FLAG = B.VIEW_FLAG
       AND A.PERIOD_YEAR = B.PERIOD_YEAR
      '||V_INSERT_LV0_PROD_CODE
	   ||V_INSERT_LV1_PROD_CODE
       ||V_INSERT_LV2_PROD_CODE
       ||V_INSERT_LV3_PROD_CODE
       ||V_INSERT_LV4_PROD_CODE||
	   ' AND A.SPART_CODE = B.SPART_CODE
	   LEFT JOIN (SELECT * FROM '||V_JOIN_TABLE||' WHERE VERSION_ID = '||V_DIM_VERSION||' ) M
	   ON A.SPART_CODE = M.SPART_CODE
	   AND A.BG_CODE = M.BG_CODE
	    '||V_DIM_INSERT_LV1_PROD_CODE
         ||V_DIM_INSERT_LV2_PROD_CODE
         ||V_DIM_INSERT_LV3_PROD_CODE
         ||V_DIM_INSERT_LV4_PROD_CODE
		;
	
	DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
    DBMS_OUTPUT.PUT_LINE('-------3  执行SQL3---结束--------------'); 

	--再向目标表插入一份主力编码的数据，转为N
	V_SQL:=  'INSERT INTO '||V_TO_TABLE||'
    (
		VERSION_ID,
		VERSION_NAME,
		PERIOD_YEAR,
		'||V_LV0_PROD_PARA 
	     ||V_LV1_PROD_PARA  
	     ||V_LV2_PROD_PARA 
	     ||V_LV3_PROD_PARA  
	     ||V_LV4_PROD_PARA||'
		REGION_CODE,
		REGION_CN_NAME,
		REPOFFICE_CODE,
		REPOFFICE_CN_NAME,
		TOP_SPART_CODE,
		TOP_SPART_CN_NAME,
		BG_CODE,
		BG_CN_NAME,
		OVERSEA_FLAG,
		SOFTWARE_MARK,
		VIEW_FLAG,
		WEIGHT_RATE,
		IS_TOP_FLAG,
		DOUBLE_FLAG,
		MAIN_FLAG,
		CODE_ATTRIBUTES,
		CREATED_BY,
		CREATION_DATE,
		LAST_UPDATED_BY,
		LAST_UPDATE_DATE,
		DEL_FLAG
     )
	 SELECT 
	  		VERSION_ID,
		VERSION_NAME,
		PERIOD_YEAR,
		'||V_LV0_PROD_PARA 
	     ||V_LV1_PROD_PARA  
	     ||V_LV2_PROD_PARA 
	     ||V_LV3_PROD_PARA  
	     ||V_LV4_PROD_PARA||'
		REGION_CODE,
		REGION_CN_NAME,
		REPOFFICE_CODE,
		REPOFFICE_CN_NAME,
		TOP_SPART_CODE,
		TOP_SPART_CN_NAME,
		BG_CODE,
		BG_CN_NAME,
		OVERSEA_FLAG,
		SOFTWARE_MARK,
		VIEW_FLAG,
		WEIGHT_RATE,
		IS_TOP_FLAG,
		DOUBLE_FLAG,
		''N'' AS MAIN_FLAG,
		NULL AS CODE_ATTRIBUTES,
		CREATED_BY,
		CREATION_DATE,
		LAST_UPDATED_BY,
		LAST_UPDATE_DATE,
		DEL_FLAG
		FROM '||V_TO_TABLE||'
		WHERE MAIN_FLAG = ''Y'' 
		AND VERSION_ID = '|| V_VERSION_ID 
		
	;
	
	DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
    DBMS_OUTPUT.PUT_LINE('主力编码转换完成'); 
	
     --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插数进目标表, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME||',成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
IF F_COST_TYPE = 'STD' THEN 
 
    DBMS_OUTPUT.PUT_LINE('------准备进行only_top_spart删除--------------'); 
   
    --创建临时表
  DROP TABLE IF EXISTS DM_FOC_ONLY_SPART_TEMP;
  CREATE TEMPORARY TABLE DM_FOC_ONLY_SPART_TEMP
  (	VERSION_ID INTEGER,
	VERSION_NAME VARCHAR(30),
	PERIOD_YEAR VARCHAR(30),
	YEAR_FLAG VARCHAR2(2),
	LV0_PROD_RND_TEAM_CODE 	VARCHAR(50),
	LV1_PROD_RND_TEAM_CODE 	VARCHAR(50),
	LV2_PROD_RND_TEAM_CODE 	VARCHAR(50),
	LV3_PROD_RND_TEAM_CODE 	VARCHAR(50),
	LV4_PROD_RND_TEAM_CODE 	VARCHAR(50),
	LV0_INDUSTRY_CATG_CODE VARCHAR(50),
	LV1_INDUSTRY_CATG_CODE VARCHAR(50),
	LV2_INDUSTRY_CATG_CODE VARCHAR(50),
	LV3_INDUSTRY_CATG_CODE VARCHAR(50),
	LV4_INDUSTRY_CATG_CODE VARCHAR(50),
	LV0_PROD_LIST_CODE	VARCHAR(50),
	LV1_PROD_LIST_CODE	VARCHAR(50),
	LV2_PROD_LIST_CODE	VARCHAR(50),
	LV3_PROD_LIST_CODE	VARCHAR(50),
	LV4_PROD_LIST_CODE	VARCHAR(50),
	REGION_CODE VARCHAR(50),
	REPOFFICE_CODE VARCHAR(50),
	SPART_CODE VARCHAR(50),
	BG_CODE VARCHAR(50),
	OVERSEA_FLAG VARCHAR(10),
	VIEW_FLAG VARCHAR(50),
	WEIGHT_RATE NUMERIC,
	IS_TOP_FLAG VARCHAR(1),
	DOUBLE_FLAG VARCHAR(1)
   )ON COMMIT PRESERVE ROWS 
   DISTRIBUTE BY REPLICATION;
   
   
   
    --往临时插入单_SPART的品类信息
  V_SQL :=
  'INSERT INTO DM_FOC_ONLY_SPART_TEMP
    (		
			VERSION_ID,
			'||V_LV0_PROD_CODE 
			 ||V_LV1_PROD_CODE  
			 ||V_LV2_PROD_CODE 
			 ||V_LV3_PROD_CODE  
			 ||V_LV4_PROD_CODE||'
			REGION_CODE,
			REPOFFICE_CODE,
			SPART_CODE,
			BG_CODE,
			OVERSEA_FLAG
			)
    SELECT VERSION_ID,
			'||V_LV0_PROD_CODE 
			 ||V_LV1_PROD_CODE  
			 ||V_LV2_PROD_CODE 
			 ||V_LV3_PROD_CODE  
			 ||V_LV4_PROD_CODE||'
			REGION_CODE,
			REPOFFICE_CODE,
			SPART_CODE,
			BG_CODE,
			OVERSEA_FLAG
      FROM (SELECT VERSION_ID,
					'||V_LV0_PROD_CODE 
					||V_LV1_PROD_CODE  
					||V_LV2_PROD_CODE 
					||V_LV3_PROD_CODE  
					||V_LV4_PROD_CODE||'
					REGION_CODE,
					REPOFFICE_CODE,
					SPART_CODE,
					BG_CODE,
					OVERSEA_FLAG,
                   COUNT(1) OVER(PARTITION BY  '||V_LV0_PROD_CODE ||V_LV1_PROD_CODE  ||V_LV2_PROD_CODE ||V_LV3_PROD_CODE  ||V_LV4_PROD_CODE||' REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG,SPART_CODE) AS ITEM_FLAG
              FROM (SELECT DISTINCT VERSION_ID,
									'||V_LV0_PROD_CODE 
									||V_LV1_PROD_CODE  
									||V_LV2_PROD_CODE 
									||V_LV3_PROD_CODE  
									||V_LV4_PROD_CODE||'
									REGION_CODE,
									REPOFFICE_CODE,
									TOP_SPART_CODE AS SPART_CODE,
									BG_CODE,
									OVERSEA_FLAG
                      FROM '||V_TO_TABLE ||' T
                     WHERE T.VERSION_ID = '||V_VERSION_ID||'
                       AND T.DOUBLE_FLAG = ''Y''
                       AND T.IS_TOP_FLAG = ''Y'') R) S
     WHERE S.ITEM_FLAG = 1';
	DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL;


  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG);

   
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '创建临时表, 并往临时插入单_SPART的品类信息, 本次获取的TOP_SPART的版本号='||V_VERSION_ID,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

   
  --删除TOP品类
  V_SQL :=
  'DELETE FROM '||V_TO_TABLE||' A
   USING DM_FOC_ONLY_SPART_TEMP B
   WHERE A.VERSION_ID = '||V_VERSION_ID||'
     AND A.VIEW_FLAG = B.VIEW_FLAG
     AND A.BG_CODE = B.BG_CODE
     AND A.OVERSEA_FLAG = B.OVERSEA_FLAG
	 AND A.REGION_CODE = B.REGION_CODE
	 AND A.REPOFFICE_CODE = B.REPOFFICE_CODE
	 AND A.TOP_SPART_CODE = B.SPART_CODE
     '||V_INSERT_LV0_PROD_CODE
	  ||V_INSERT_LV1_PROD_CODE
      ||V_INSERT_LV2_PROD_CODE
      ||V_INSERT_LV3_PROD_CODE
      ||V_INSERT_LV4_PROD_CODE 
;
	DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除TOP品类清单数据: 剔除单_SPART的品类, 版本号='||V_VERSION_ID||',成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
ELSIF F_COST_TYPE = 'PSP' THEN 
	DBMS_OUTPUT.PUT_LINE('PSP成本不做剔除逻辑');
   
ELSE NULL;
END IF ;
   
 --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;

    
  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG, 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END
$$
/

