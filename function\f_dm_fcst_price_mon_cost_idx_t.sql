-- Name: f_dm_fcst_price_mon_cost_idx_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_mon_cost_idx_t(f_version_id character varying DEFAULT NULL::character varying, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间: 20241108
创建人  : 黄心蕊 hwx1187045
背景描述: 定价指数-指数计算 T-2年到T年指数
参数描述: 参数一:  F_VERSION_ID 版本号
		  参数二:  X_RESULT_STATUS 运行状态返回值 ‘1’为成功，‘0’为失败
--来源表
均价 DM_FCST_PRICE_MTD_AVG_T

权重 DM_FCST_PRICE_MON_WEIGHT_T

--目标表
指数 DM_FCST_PRICE_MON_COST_IDX_T

SELECT FIN_DM_OPT_FOI.F_DM_FCST_PRICE_MON_COST_IDX_T('');
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                 VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FCST_PRICE_MON_COST_IDX_T';
  V_VERSION                 BIGINT; --版本号
  V_BASE_PERIOD_ID          INT ; --基期会计期
  V_EXCEPTION_FLAG			INT; --异常步骤
  V_YEAR           INT;
  
BEGIN
X_RESULT_STATUS := '1';

 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 0,
  F_CAL_LOG_DESC => '开始执行',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
     if MONTH(CURRENT_TIMESTAMP) = 1
  then  select TO_NUMBER((YEAR(CURRENT_DATE) - 2) || '12') into V_BASE_PERIOD_ID ;
        select YEAR(NOW()) -1 into V_YEAR ;
  ELSE
        select TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '12') into V_BASE_PERIOD_ID  ;
        select YEAR(NOW()) into V_YEAR ;
  END IF ;
  
 
 --版本号取值
  V_EXCEPTION_FLAG	:= 0;
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;
  
 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  
--临时表建表
V_EXCEPTION_FLAG	:= 1;
DROP TABLE IF EXISTS DM_MID_MON_COST_IDX_T;
CREATE TEMPORARY TABLE DM_MID_MON_COST_IDX_T(
PERIOD_YEAR	BIGINT,
PERIOD_ID	BIGINT,
LV0_PROD_LIST_CODE	VARCHAR(50),
LV1_PROD_LIST_CODE	VARCHAR(50),
LV2_PROD_LIST_CODE	VARCHAR(50),
LV3_PROD_LIST_CODE	VARCHAR(50),
LV4_PROD_LIST_CODE	VARCHAR(50),
LV0_PROD_LIST_CN_NAME	VARCHAR(200),
LV1_PROD_LIST_CN_NAME	VARCHAR(200),
LV2_PROD_LIST_CN_NAME	VARCHAR(200),
LV3_PROD_LIST_CN_NAME	VARCHAR(200),
LV4_PROD_LIST_CN_NAME	VARCHAR(200),
GROUP_CODE	VARCHAR(50),
GROUP_CN_NAME	VARCHAR(2000),
GROUP_LEVEL	VARCHAR(50),
COST_INDEX	NUMERIC,
OVERSEA_FLAG	VARCHAR(10),
REGION_CODE	VARCHAR(50),
REGION_CN_NAME	VARCHAR(200),
REPOFFICE_CODE	VARCHAR(50),
REPOFFICE_CN_NAME	VARCHAR(200),
SIGN_TOP_CUST_CATEGORY_CODE	VARCHAR(50),
SIGN_TOP_CUST_CATEGORY_CN_NAME	VARCHAR(200),
SIGN_SUBSIDIARY_CUSTCATG_CN_NAME	VARCHAR(200),
VIEW_FLAG	VARCHAR(50),
PARENT_CODE	VARCHAR(50),
PARENT_CN_NAME	VARCHAR(200),
BG_CODE	VARCHAR(50),
BG_CN_NAME	VARCHAR(200),
APPEND_FLAG	VARCHAR(5)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(GROUP_CODE,VIEW_FLAG,BG_CODE);
 

--SPART指数计算
  WITH BASE_AVG_TEMP AS
   (--取3年累积均价数据
    SELECT PERIOD_ID,
           LV0_PROD_LIST_CODE,
           LV1_PROD_LIST_CODE,
           LV2_PROD_LIST_CODE,
           LV3_PROD_LIST_CODE,
           LV4_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME,
           LV1_PROD_LIST_CN_NAME,
           LV2_PROD_LIST_CN_NAME,
           LV3_PROD_LIST_CN_NAME,
           LV4_PROD_LIST_CN_NAME,
           SPART_CODE,
           SPART_CN_NAME,
           USD_PNP_AVG,
           OVERSEA_FLAG,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           SIGN_TOP_CUST_CATEGORY_CODE,
           SIGN_TOP_CUST_CATEGORY_CN_NAME,
           SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
           VIEW_FLAG,
           BG_CODE,
           BG_CN_NAME,
		   APPEND_FLAG	--页面打点使用
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MTD_AVG_T
     WHERE PERIOD_YEAR >= V_YEAR - 2
	   AND VERSION_ID = V_VERSION
	   AND ENABLE_FLAG = 'Y'),
  BASE_PERIOD_ID_TEMP AS
   (--取默认基期均价数据
    SELECT LV4_PROD_LIST_CODE,
           SPART_CODE,
           USD_PNP_AVG,
           OVERSEA_FLAG,
           REGION_CODE,
           REPOFFICE_CODE,
           SIGN_TOP_CUST_CATEGORY_CODE,
           SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
           VIEW_FLAG,
           BG_CODE
      FROM BASE_AVG_TEMP
     WHERE PERIOD_ID = V_BASE_PERIOD_ID) 
   INSERT INTO DM_MID_MON_COST_IDX_T
     (PERIOD_ID,
      LV0_PROD_LIST_CODE,
      LV1_PROD_LIST_CODE,
      LV2_PROD_LIST_CODE,
      LV3_PROD_LIST_CODE,
      LV4_PROD_LIST_CODE,
      LV0_PROD_LIST_CN_NAME,
      LV1_PROD_LIST_CN_NAME,
      LV2_PROD_LIST_CN_NAME,
      LV3_PROD_LIST_CN_NAME,
      LV4_PROD_LIST_CN_NAME,
      GROUP_CODE,
      GROUP_CN_NAME,
      GROUP_LEVEL,
      COST_INDEX,
      OVERSEA_FLAG,
      REGION_CODE,
      REGION_CN_NAME,
      REPOFFICE_CODE,
      REPOFFICE_CN_NAME,
      SIGN_TOP_CUST_CATEGORY_CODE,
      SIGN_TOP_CUST_CATEGORY_CN_NAME,
      SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
      VIEW_FLAG,
      PARENT_CODE,
      PARENT_CN_NAME,
      BG_CODE,
      BG_CN_NAME,
	  APPEND_FLAG)
  SELECT T1.PERIOD_ID,
         T1.LV0_PROD_LIST_CODE,
         T1.LV1_PROD_LIST_CODE,
         T1.LV2_PROD_LIST_CODE,
         T1.LV3_PROD_LIST_CODE,
         T1.LV4_PROD_LIST_CODE,
         T1.LV0_PROD_LIST_CN_NAME,
         T1.LV1_PROD_LIST_CN_NAME,
         T1.LV2_PROD_LIST_CN_NAME,
         T1.LV3_PROD_LIST_CN_NAME,
         T1.LV4_PROD_LIST_CN_NAME,
         T1.SPART_CODE AS GROUP_CODE,
         T1.SPART_CN_NAME AS GROUP_CN_NAME,
         'SPART' AS GROUP_LEVEL,
         T1.USD_PNP_AVG / NULLIF(T2.USD_PNP_AVG, 0)*100 AS COST_INDEX,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T1.LV4_PROD_LIST_CODE AS PARENT_CODE,
         T1.LV4_PROD_LIST_CN_NAME AS PARENT_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME,
		 T1.APPEND_FLAG
    FROM BASE_AVG_TEMP T1
    LEFT JOIN BASE_PERIOD_ID_TEMP T2
      ON T1.LV4_PROD_LIST_CODE = T2.LV4_PROD_LIST_CODE
     AND T1.SPART_CODE = T2.SPART_CODE
     AND T1.VIEW_FLAG = T2.VIEW_FLAG
     AND NVL(T1.OVERSEA_FLAG, 'OF') = NVL(T2.OVERSEA_FLAG, 'OF')
     AND NVL(T1.REGION_CODE, 'RGC') = NVL(T2.REGION_CODE, 'RGC')
     AND NVL(T1.REPOFFICE_CODE, 'RPC') = NVL(T2.REPOFFICE_CODE, 'RPC')
     AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE, 'STC') =
         NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE, 'STC')
     AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME, 'SSC') =
         NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME, 'SSC')
     AND NVL(T1.BG_CODE, 'BG') = NVL(T2.BG_CODE, 'BG');
	 
 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 2,
  F_CAL_LOG_DESC => 'SPART数据集成完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

  --LV4指数
  V_EXCEPTION_FLAG := 2;
  WITH BASE_IDX AS
   (SELECT PERIOD_ID,
           LV0_PROD_LIST_CODE,
           LV1_PROD_LIST_CODE,
           LV2_PROD_LIST_CODE,
           LV3_PROD_LIST_CODE,
           LV4_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME,
           LV1_PROD_LIST_CN_NAME,
           LV2_PROD_LIST_CN_NAME,
           LV3_PROD_LIST_CN_NAME,
           LV4_PROD_LIST_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           OVERSEA_FLAG,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           SIGN_TOP_CUST_CATEGORY_CODE,
           SIGN_TOP_CUST_CATEGORY_CN_NAME,
           SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
           VIEW_FLAG,
           PARENT_CODE,
           PARENT_CN_NAME,
           BG_CODE,
           BG_CN_NAME
      FROM DM_MID_MON_COST_IDX_T
     WHERE GROUP_LEVEL = 'SPART'),
  BASE_WEIGHT AS
   (SELECT GROUP_CODE,
           WEIGHT_RATE,
		   GROUP_LEVEL,
           OVERSEA_FLAG,
           REGION_CODE,
           REPOFFICE_CODE,
           SIGN_TOP_CUST_CATEGORY_CODE,
           SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
           VIEW_FLAG,
           PARENT_CODE,
           BG_CODE
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MON_WEIGHT_T
     WHERE VERSION_ID = V_VERSION
       AND GROUP_LEVEL = 'SPART') 
   INSERT INTO DM_MID_MON_COST_IDX_T
     (PERIOD_ID,
      LV0_PROD_LIST_CODE,
      LV1_PROD_LIST_CODE,
      LV2_PROD_LIST_CODE,
      LV3_PROD_LIST_CODE,
      LV0_PROD_LIST_CN_NAME,
      LV1_PROD_LIST_CN_NAME,
      LV2_PROD_LIST_CN_NAME,
      LV3_PROD_LIST_CN_NAME,
      GROUP_CODE,
      GROUP_CN_NAME,
      GROUP_LEVEL,
      COST_INDEX,
      OVERSEA_FLAG,
      REGION_CODE,
      REGION_CN_NAME,
      REPOFFICE_CODE,
      REPOFFICE_CN_NAME,
      SIGN_TOP_CUST_CATEGORY_CODE,
      SIGN_TOP_CUST_CATEGORY_CN_NAME,
      SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
      VIEW_FLAG,
      PARENT_CODE,
      PARENT_CN_NAME,
      BG_CODE,
      BG_CN_NAME)
  SELECT T1.PERIOD_ID,
         T1.LV0_PROD_LIST_CODE,
         T1.LV1_PROD_LIST_CODE,
         T1.LV2_PROD_LIST_CODE,
         T1.LV3_PROD_LIST_CODE,
         T1.LV0_PROD_LIST_CN_NAME,
         T1.LV1_PROD_LIST_CN_NAME,
         T1.LV2_PROD_LIST_CN_NAME,
         T1.LV3_PROD_LIST_CN_NAME,
         T1.LV4_PROD_LIST_CODE AS GROUP_CODE,
         T1.LV4_PROD_LIST_CN_NAME AS GROUP_CN_NAME,
         'LV4' AS GROUP_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T1.LV3_PROD_LIST_CODE AS PARENT_CODE,
         T1.LV3_PROD_LIST_CN_NAME AS PARENT_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME
    FROM BASE_IDX T1
    LEFT JOIN BASE_WEIGHT T2
      ON T1.PARENT_CODE = T2.PARENT_CODE
     AND T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.VIEW_FLAG = T2.VIEW_FLAG
     AND NVL(T1.OVERSEA_FLAG, 'OF') = NVL(T2.OVERSEA_FLAG, 'OF')
     AND NVL(T1.REGION_CODE, 'RGC') = NVL(T2.REGION_CODE, 'RGC')
     AND NVL(T1.REPOFFICE_CODE, 'RPC') = NVL(T2.REPOFFICE_CODE, 'RPC')
     AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE, 'STC') =
         NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE, 'STC')
     AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME, 'SSC') =
         NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME, 'SSC')
     AND NVL(T1.BG_CODE, 'BG') = NVL(T2.BG_CODE, 'BG')
     AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
   GROUP BY T1.PERIOD_ID,
            T1.LV0_PROD_LIST_CODE,
            T1.LV1_PROD_LIST_CODE,
            T1.LV2_PROD_LIST_CODE,
            T1.LV3_PROD_LIST_CODE,
            T1.LV0_PROD_LIST_CN_NAME,
            T1.LV1_PROD_LIST_CN_NAME,
            T1.LV2_PROD_LIST_CN_NAME,
            T1.LV3_PROD_LIST_CN_NAME,
            T1.LV4_PROD_LIST_CODE,
            T1.LV4_PROD_LIST_CN_NAME,
            T1.OVERSEA_FLAG,
            T1.REGION_CODE,
            T1.REGION_CN_NAME,
            T1.REPOFFICE_CODE,
            T1.REPOFFICE_CN_NAME,
            T1.SIGN_TOP_CUST_CATEGORY_CODE,
            T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
            T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
            T1.VIEW_FLAG,
            T1.BG_CODE,
            T1.BG_CN_NAME;
			
 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 3,
  F_CAL_LOG_DESC => 'LV4数据集成完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  --LV3指数
  V_EXCEPTION_FLAG := 3;
  WITH BASE_IDX AS
   (SELECT PERIOD_ID,
           LV0_PROD_LIST_CODE,
           LV1_PROD_LIST_CODE,
           LV2_PROD_LIST_CODE,
           LV3_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME,
           LV1_PROD_LIST_CN_NAME,
           LV2_PROD_LIST_CN_NAME,
           LV3_PROD_LIST_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           OVERSEA_FLAG,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           SIGN_TOP_CUST_CATEGORY_CODE,
           SIGN_TOP_CUST_CATEGORY_CN_NAME,
           SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
           VIEW_FLAG,
           PARENT_CODE,
           PARENT_CN_NAME,
           BG_CODE,
           BG_CN_NAME
      FROM DM_MID_MON_COST_IDX_T
     WHERE GROUP_LEVEL = 'LV4'),
  BASE_WEIGHT AS
   (SELECT GROUP_CODE,
           WEIGHT_RATE,
		   GROUP_LEVEL,
           OVERSEA_FLAG,
           REGION_CODE,
           REPOFFICE_CODE,
           SIGN_TOP_CUST_CATEGORY_CODE,
           SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
           VIEW_FLAG,
           PARENT_CODE,
           BG_CODE
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MON_WEIGHT_T
     WHERE VERSION_ID = V_VERSION
       AND GROUP_LEVEL = 'LV4') 
   INSERT INTO DM_MID_MON_COST_IDX_T
     (PERIOD_ID,
      LV0_PROD_LIST_CODE,
      LV1_PROD_LIST_CODE,
      LV2_PROD_LIST_CODE,
      LV0_PROD_LIST_CN_NAME,
      LV1_PROD_LIST_CN_NAME,
      LV2_PROD_LIST_CN_NAME,
      GROUP_CODE,
      GROUP_CN_NAME,
      GROUP_LEVEL,
      COST_INDEX,
      OVERSEA_FLAG,
      REGION_CODE,
      REGION_CN_NAME,
      REPOFFICE_CODE,
      REPOFFICE_CN_NAME,
      SIGN_TOP_CUST_CATEGORY_CODE,
      SIGN_TOP_CUST_CATEGORY_CN_NAME,
      SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
      VIEW_FLAG,
      PARENT_CODE,
      PARENT_CN_NAME,
      BG_CODE,
      BG_CN_NAME)
  SELECT T1.PERIOD_ID,
         T1.LV0_PROD_LIST_CODE,
         T1.LV1_PROD_LIST_CODE,
         T1.LV2_PROD_LIST_CODE,
         T1.LV0_PROD_LIST_CN_NAME,
         T1.LV1_PROD_LIST_CN_NAME,
         T1.LV2_PROD_LIST_CN_NAME,
         T1.LV3_PROD_LIST_CODE AS GROUP_CODE,
         T1.LV3_PROD_LIST_CN_NAME AS GROUP_CN_NAME,
         'LV3' AS GROUP_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T1.LV2_PROD_LIST_CODE AS PARENT_CODE,
         T1.LV2_PROD_LIST_CN_NAME AS PARENT_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME
    FROM BASE_IDX T1
    LEFT JOIN BASE_WEIGHT T2
      ON T1.PARENT_CODE = T2.PARENT_CODE
     AND T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.VIEW_FLAG = T2.VIEW_FLAG
     AND NVL(T1.OVERSEA_FLAG, 'OF') = NVL(T2.OVERSEA_FLAG, 'OF')
     AND NVL(T1.REGION_CODE, 'RGC') = NVL(T2.REGION_CODE, 'RGC')
     AND NVL(T1.REPOFFICE_CODE, 'RPC') = NVL(T2.REPOFFICE_CODE, 'RPC')
     AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE, 'STC') =
         NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE, 'STC')
     AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME, 'SSC') =
         NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME, 'SSC')
     AND NVL(T1.BG_CODE, 'BG') = NVL(T2.BG_CODE, 'BG')
     AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
   GROUP BY T1.PERIOD_ID,
            T1.LV0_PROD_LIST_CODE,
            T1.LV1_PROD_LIST_CODE,
            T1.LV2_PROD_LIST_CODE,
            T1.LV3_PROD_LIST_CODE,
            T1.LV0_PROD_LIST_CN_NAME,
            T1.LV1_PROD_LIST_CN_NAME,
            T1.LV2_PROD_LIST_CN_NAME,
            T1.LV3_PROD_LIST_CN_NAME,
            T1.OVERSEA_FLAG,
            T1.REGION_CODE,
            T1.REGION_CN_NAME,
            T1.REPOFFICE_CODE,
            T1.REPOFFICE_CN_NAME,
            T1.SIGN_TOP_CUST_CATEGORY_CODE,
            T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
            T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
            T1.VIEW_FLAG,
            T1.BG_CODE,
            T1.BG_CN_NAME;
			
 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 4,
  F_CAL_LOG_DESC => 'LV3数据集成完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
			
  --LV2指数
  V_EXCEPTION_FLAG := 4;
  WITH BASE_IDX AS
   (SELECT PERIOD_ID,
           LV0_PROD_LIST_CODE,
           LV1_PROD_LIST_CODE,
           LV2_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME,
           LV1_PROD_LIST_CN_NAME,
           LV2_PROD_LIST_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           OVERSEA_FLAG,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           SIGN_TOP_CUST_CATEGORY_CODE,
           SIGN_TOP_CUST_CATEGORY_CN_NAME,
           SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
           VIEW_FLAG,
           PARENT_CODE,
           PARENT_CN_NAME,
           BG_CODE,
           BG_CN_NAME
      FROM DM_MID_MON_COST_IDX_T
     WHERE GROUP_LEVEL = 'LV3'),
  BASE_WEIGHT AS
   (SELECT GROUP_CODE,
           WEIGHT_RATE,
		   GROUP_LEVEL,
           OVERSEA_FLAG,
           REGION_CODE,
           REPOFFICE_CODE,
           SIGN_TOP_CUST_CATEGORY_CODE,
           SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
           VIEW_FLAG,
           PARENT_CODE,
           BG_CODE
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MON_WEIGHT_T
     WHERE VERSION_ID = V_VERSION
       AND GROUP_LEVEL = 'LV3') 
   INSERT INTO DM_MID_MON_COST_IDX_T
     (PERIOD_ID,
      LV0_PROD_LIST_CODE,
      LV1_PROD_LIST_CODE,
      LV0_PROD_LIST_CN_NAME,
      LV1_PROD_LIST_CN_NAME,
      GROUP_CODE,
      GROUP_CN_NAME,
      GROUP_LEVEL,
      COST_INDEX,
      OVERSEA_FLAG,
      REGION_CODE,
      REGION_CN_NAME,
      REPOFFICE_CODE,
      REPOFFICE_CN_NAME,
      SIGN_TOP_CUST_CATEGORY_CODE,
      SIGN_TOP_CUST_CATEGORY_CN_NAME,
      SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
      VIEW_FLAG,
      PARENT_CODE,
      PARENT_CN_NAME,
      BG_CODE,
      BG_CN_NAME)
  SELECT T1.PERIOD_ID,
         T1.LV0_PROD_LIST_CODE,
         T1.LV1_PROD_LIST_CODE,
         T1.LV0_PROD_LIST_CN_NAME,
         T1.LV1_PROD_LIST_CN_NAME,
         T1.LV2_PROD_LIST_CODE AS GROUP_CODE,
         T1.LV2_PROD_LIST_CN_NAME AS GROUP_CN_NAME,
         'LV2' AS GROUP_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T1.LV1_PROD_LIST_CODE AS PARENT_CODE,
         T1.LV1_PROD_LIST_CN_NAME AS PARENT_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME
    FROM BASE_IDX T1
    LEFT JOIN BASE_WEIGHT T2
      ON T1.PARENT_CODE = T2.PARENT_CODE
     AND T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.VIEW_FLAG = T2.VIEW_FLAG
     AND NVL(T1.OVERSEA_FLAG, 'OF') = NVL(T2.OVERSEA_FLAG, 'OF')
     AND NVL(T1.REGION_CODE, 'RGC') = NVL(T2.REGION_CODE, 'RGC')
     AND NVL(T1.REPOFFICE_CODE, 'RPC') = NVL(T2.REPOFFICE_CODE, 'RPC')
     AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE, 'STC') =
         NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE, 'STC')
     AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME, 'SSC') =
         NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME, 'SSC')
     AND NVL(T1.BG_CODE, 'BG') = NVL(T2.BG_CODE, 'BG')
     AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
   GROUP BY T1.PERIOD_ID,
            T1.LV0_PROD_LIST_CODE,
            T1.LV1_PROD_LIST_CODE,
            T1.LV2_PROD_LIST_CODE,
            T1.LV0_PROD_LIST_CN_NAME,
            T1.LV1_PROD_LIST_CN_NAME,
            T1.LV2_PROD_LIST_CN_NAME,
            T1.OVERSEA_FLAG,
            T1.REGION_CODE,
            T1.REGION_CN_NAME,
            T1.REPOFFICE_CODE,
            T1.REPOFFICE_CN_NAME,
            T1.SIGN_TOP_CUST_CATEGORY_CODE,
            T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
            T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
            T1.VIEW_FLAG,
            T1.BG_CODE,
            T1.BG_CN_NAME;
			
 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 5,
  F_CAL_LOG_DESC => 'LV2数据集成完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
			
  --LV1指数
  V_EXCEPTION_FLAG := 5;
  WITH BASE_IDX AS
   (SELECT PERIOD_ID,
           LV0_PROD_LIST_CODE,
           LV1_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME,
           LV1_PROD_LIST_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           OVERSEA_FLAG,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           SIGN_TOP_CUST_CATEGORY_CODE,
           SIGN_TOP_CUST_CATEGORY_CN_NAME,
           SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
           VIEW_FLAG,
           PARENT_CODE,
           PARENT_CN_NAME,
           BG_CODE,
           BG_CN_NAME
      FROM DM_MID_MON_COST_IDX_T
     WHERE GROUP_LEVEL = 'LV2'),
  BASE_WEIGHT AS
   (SELECT GROUP_CODE,
           WEIGHT_RATE,
		   GROUP_LEVEL,
           OVERSEA_FLAG,
           REGION_CODE,
           REPOFFICE_CODE,
           SIGN_TOP_CUST_CATEGORY_CODE,
           SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
           VIEW_FLAG,
           PARENT_CODE,
           BG_CODE
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MON_WEIGHT_T
     WHERE VERSION_ID = V_VERSION
       AND GROUP_LEVEL = 'LV2') 
   INSERT INTO DM_MID_MON_COST_IDX_T
     (PERIOD_ID,
      LV0_PROD_LIST_CODE,
      LV0_PROD_LIST_CN_NAME,
      GROUP_CODE,
      GROUP_CN_NAME,
      GROUP_LEVEL,
      COST_INDEX,
      OVERSEA_FLAG,
      REGION_CODE,
      REGION_CN_NAME,
      REPOFFICE_CODE,
      REPOFFICE_CN_NAME,
      SIGN_TOP_CUST_CATEGORY_CODE,
      SIGN_TOP_CUST_CATEGORY_CN_NAME,
      SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
      VIEW_FLAG,
      PARENT_CODE,
      PARENT_CN_NAME,
      BG_CODE,
      BG_CN_NAME)
  SELECT T1.PERIOD_ID,
         T1.LV0_PROD_LIST_CODE,
         T1.LV0_PROD_LIST_CN_NAME,
         T1.LV1_PROD_LIST_CODE AS GROUP_CODE,
         T1.LV1_PROD_LIST_CN_NAME AS GROUP_CN_NAME,
         'LV1' AS GROUP_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T1.LV0_PROD_LIST_CODE AS PARENT_CODE,
         T1.LV0_PROD_LIST_CN_NAME AS PARENT_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME
    FROM BASE_IDX T1
    LEFT JOIN BASE_WEIGHT T2
      ON T1.PARENT_CODE = T2.PARENT_CODE
     AND T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.VIEW_FLAG = T2.VIEW_FLAG
     AND NVL(T1.OVERSEA_FLAG, 'OF') = NVL(T2.OVERSEA_FLAG, 'OF')
     AND NVL(T1.REGION_CODE, 'RGC') = NVL(T2.REGION_CODE, 'RGC')
     AND NVL(T1.REPOFFICE_CODE, 'RPC') = NVL(T2.REPOFFICE_CODE, 'RPC')
     AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE, 'STC') =
         NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE, 'STC')
     AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME, 'SSC') =
         NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME, 'SSC')
     AND NVL(T1.BG_CODE, 'BG') = NVL(T2.BG_CODE, 'BG')
     AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
   GROUP BY T1.PERIOD_ID,
            T1.LV0_PROD_LIST_CODE,
            T1.LV1_PROD_LIST_CODE,
            T1.LV0_PROD_LIST_CN_NAME,
            T1.LV1_PROD_LIST_CN_NAME,
            T1.OVERSEA_FLAG,
            T1.REGION_CODE,
            T1.REGION_CN_NAME,
            T1.REPOFFICE_CODE,
            T1.REPOFFICE_CN_NAME,
            T1.SIGN_TOP_CUST_CATEGORY_CODE,
            T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
            T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
            T1.VIEW_FLAG,
            T1.BG_CODE,
            T1.BG_CN_NAME;
			
 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 6,
  F_CAL_LOG_DESC => 'LV1数据集成完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
			
  --LV0指数
  V_EXCEPTION_FLAG := 6;
  WITH BASE_IDX AS
   (SELECT PERIOD_ID,
           LV0_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           OVERSEA_FLAG,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           SIGN_TOP_CUST_CATEGORY_CODE,
           SIGN_TOP_CUST_CATEGORY_CN_NAME,
           SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
           VIEW_FLAG,
           PARENT_CODE,
           PARENT_CN_NAME,
           BG_CODE,
           BG_CN_NAME
      FROM DM_MID_MON_COST_IDX_T
     WHERE GROUP_LEVEL = 'LV1'),
  BASE_WEIGHT AS
   (SELECT GROUP_CODE,
           WEIGHT_RATE,
		   GROUP_LEVEL,
           OVERSEA_FLAG,
           REGION_CODE,
           REPOFFICE_CODE,
           SIGN_TOP_CUST_CATEGORY_CODE,
           SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
           VIEW_FLAG,
           PARENT_CODE,
           BG_CODE
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MON_WEIGHT_T
     WHERE VERSION_ID = V_VERSION
       AND GROUP_LEVEL = 'LV1') 
   INSERT INTO DM_MID_MON_COST_IDX_T
     (PERIOD_ID,
      GROUP_CODE,
      GROUP_CN_NAME,
      GROUP_LEVEL,
      COST_INDEX,
      OVERSEA_FLAG,
      REGION_CODE,
      REGION_CN_NAME,
      REPOFFICE_CODE,
      REPOFFICE_CN_NAME,
      SIGN_TOP_CUST_CATEGORY_CODE,
      SIGN_TOP_CUST_CATEGORY_CN_NAME,
      SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
      VIEW_FLAG,
      PARENT_CODE,
      PARENT_CN_NAME,
      BG_CODE,
      BG_CN_NAME)
  SELECT T1.PERIOD_ID,
         T1.LV0_PROD_LIST_CODE AS GROUP_CODE,
         T1.LV0_PROD_LIST_CN_NAME AS GROUP_CN_NAME,
         'LV0' AS GROUP_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         NULL AS PARENT_CODE,
         NULL AS PARENT_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME
    FROM BASE_IDX T1
    LEFT JOIN BASE_WEIGHT T2
      ON T1.PARENT_CODE = T2.PARENT_CODE
     AND T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.VIEW_FLAG = T2.VIEW_FLAG
     AND NVL(T1.OVERSEA_FLAG, 'OF') = NVL(T2.OVERSEA_FLAG, 'OF')
     AND NVL(T1.REGION_CODE, 'RGC') = NVL(T2.REGION_CODE, 'RGC')
     AND NVL(T1.REPOFFICE_CODE, 'RPC') = NVL(T2.REPOFFICE_CODE, 'RPC')
     AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE, 'STC') =
         NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE, 'STC')
     AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME, 'SSC') =
         NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME, 'SSC')
     AND NVL(T1.BG_CODE, 'BG') = NVL(T2.BG_CODE, 'BG')
     AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
   GROUP BY T1.PERIOD_ID,
            T1.LV0_PROD_LIST_CODE,
            T1.LV0_PROD_LIST_CN_NAME,
            T1.OVERSEA_FLAG,
            T1.REGION_CODE,
            T1.REGION_CN_NAME,
            T1.REPOFFICE_CODE,
            T1.REPOFFICE_CN_NAME,
            T1.SIGN_TOP_CUST_CATEGORY_CODE,
            T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
            T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
            T1.VIEW_FLAG,
            T1.BG_CODE,
            T1.BG_CN_NAME;
			
 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 7,
  F_CAL_LOG_DESC => 'LV0数据集成完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  V_EXCEPTION_FLAG := 7;
  DELETE FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MON_COST_IDX_T WHERE VERSION_ID = V_VERSION;
  
 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 7,
  F_CAL_LOG_DESC => '指数表第 '||V_VERSION||' 版本数据删除完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  V_EXCEPTION_FLAG := 8;
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_MON_COST_IDX_T
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     BASE_PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_INDEX,
     OVERSEA_FLAG,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     SIGN_TOP_CUST_CATEGORY_CODE,
     SIGN_TOP_CUST_CATEGORY_CN_NAME,
     SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
     VIEW_FLAG,
     PARENT_CODE,
     PARENT_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     BG_CODE,
     BG_CN_NAME,
	 APPEND_FLAG)
    SELECT V_VERSION AS VERSION_ID,
           LEFT(PERIOD_ID,4)::INT AS PERIOD_YEAR,
           PERIOD_ID,
           V_BASE_PERIOD_ID AS BASE_PERIOD_ID,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           OVERSEA_FLAG,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           SIGN_TOP_CUST_CATEGORY_CODE,
           SIGN_TOP_CUST_CATEGORY_CN_NAME,
           SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
           VIEW_FLAG,
           PARENT_CODE,
           PARENT_CN_NAME,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG,
           BG_CODE,
           BG_CN_NAME,
		   APPEND_FLAG
      FROM DM_MID_MON_COST_IDX_T;
	  
 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 8,
  F_CAL_LOG_DESC => '指数表第 '||V_VERSION||' 版本数据插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
	  
  RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
     PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_STEP_NUM => V_EXCEPTION_FLAG,
    F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

 END; 
$$
/

