-- Name: f_dm_foc_view_annl_cost_insert; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_view_annl_cost_insert(f_view_flag character varying, f_keystr character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2023-03-21
  创建人  ：唐钦
  背景描述：分视角下ITEM的年均本基础表(补齐后，加密)
  参数描述：f_dimension_type : 维度类型(通用颗粒度：U,盈利颗粒度：P,量纲颗粒度：D),x_success_flag ：是否成功
  事例    ：select fin_dm_opt_foi.F_DM_FOC_VIEW_ANNL_COST_INSERT()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_VIEW_ANNL_COST_INSERT'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --新的版本号, 取自OPT_FCST.DM_FOC_MID_MONTH_ITEM_T
--  V_KEYSTR  varchar2(50) := F_KEYSTR;  -- 密钥入参
  
    -- 7月版本需求新增
    V_SQL        TEXT;   --SQL逻辑
    V_FROM_TABLE VARCHAR(500);
    V_TO_TABLE VARCHAR(500);
    V_VIEW_FLAG BIGINT := F_VIEW_FLAG;
    
BEGIN
  X_RESULT_STATUS = '1';
  /*SELECT T1.VERSION_ID
    INTO V_VERSION_ID
    FROM FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T T1
   LIMIT 1;
     */
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
    --版本号赋值
   SELECT VERSION_ID INTO V_VERSION_ID FROM FIN_DM_OPT_FOI.DM_FOC_DMS_VIEW_ANNL_COST_APPEND_T LIMIT 1;

   DELETE FROM FIN_DM_OPT_FOI.DM_FOC_DMS_VIEW_ANNL_COST_T WHERE VERSION_ID = V_VERSION_ID AND VIEW_FLAG = F_VIEW_FLAG;
   --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空DM_FOC_DMS_VIEW_ANNL_COST_T的表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 创建临时表 
   DROP TABLE IF EXISTS DECRYPT_AVG_TMP;
    CREATE TEMPORARY TABLE DECRYPT_AVG_TMP (
        VERSION_ID BIGINT,
        PERIOD_YEAR BIGINT,
        LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(500),
        LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(500),
        LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(500),
        LV3_PROD_RND_TEAM_CODE VARCHAR(50),
        LV3_PROD_RD_TEAM_CN_NAME VARCHAR(500),
        DIMENSION_CODE VARCHAR(500),
        DIMENSION_CN_NAME VARCHAR(2000),
        DIMENSION_SUBCATEGORY_CODE VARCHAR(500),
        DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(2000),
        DIMENSION_SUB_DETAIL_CODE VARCHAR(500),
        DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(2000),
        L3_CEG_CODE    VARCHAR(50),
        L3_CEG_CN_NAME    VARCHAR(500),
        L3_CEG_SHORT_CN_NAME    VARCHAR(500),
        L4_CEG_CODE    VARCHAR(50),
        L4_CEG_CN_NAME    VARCHAR(500),
        L4_CEG_SHORT_CN_NAME    VARCHAR(500),
        CATEGORY_CODE VARCHAR(50),
        CATEGORY_CN_NAME VARCHAR(500),
        ITEM_CODE VARCHAR(50),
        ITEM_CN_NAME VARCHAR(2000),
        RMB_AVG_AMT NUMERIC,
        APPEND_FLAG VARCHAR(2),
        APPEND_YEAR BIGINT,
        VIEW_FLAG VARCHAR(2),
        CALIBER_FLAG VARCHAR(2),
        OVERSEA_FLAG VARCHAR(2),
        LV0_PROD_LIST_CODE VARCHAR(50),
        LV0_PROD_LIST_CN_NAME VARCHAR(500)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(ITEM_CODE,CATEGORY_CODE);
   
   INSERT INTO DECRYPT_AVG_TMP (
          VERSION_ID,
          PERIOD_YEAR,
          LV0_PROD_RND_TEAM_CODE,
          LV0_PROD_RD_TEAM_CN_NAME,
          LV1_PROD_RND_TEAM_CODE,
          LV1_PROD_RD_TEAM_CN_NAME,
          LV2_PROD_RND_TEAM_CODE,
          LV2_PROD_RD_TEAM_CN_NAME,
          LV3_PROD_RND_TEAM_CODE,
          LV3_PROD_RD_TEAM_CN_NAME,
          DIMENSION_CODE,
          DIMENSION_CN_NAME,
          DIMENSION_SUBCATEGORY_CODE,
          DIMENSION_SUBCATEGORY_CN_NAME,
          DIMENSION_SUB_DETAIL_CODE,
          DIMENSION_SUB_DETAIL_CN_NAME,
          L3_CEG_CODE,
          L3_CEG_CN_NAME,
          L3_CEG_SHORT_CN_NAME,
          L4_CEG_CODE,
          L4_CEG_CN_NAME,
          L4_CEG_SHORT_CN_NAME,
          CATEGORY_CODE,
          CATEGORY_CN_NAME,
          ITEM_CODE,
          ITEM_CN_NAME,
          RMB_AVG_AMT,
          APPEND_FLAG,
          VIEW_FLAG,
          CALIBER_FLAG,
          APPEND_YEAR,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME)
      SELECT VERSION_ID,
             PERIOD_YEAR,
             LV0_PROD_RND_TEAM_CODE,
             LV0_PROD_RD_TEAM_CN_NAME,
             LV1_PROD_RND_TEAM_CODE,
             LV1_PROD_RD_TEAM_CN_NAME,
             LV2_PROD_RND_TEAM_CODE,
             LV2_PROD_RD_TEAM_CN_NAME,
             LV3_PROD_RND_TEAM_CODE,
             LV3_PROD_RD_TEAM_CN_NAME,
             DIMENSION_CODE,
             DIMENSION_CN_NAME,
             DIMENSION_SUBCATEGORY_CODE,
             DIMENSION_SUBCATEGORY_CN_NAME,
             DIMENSION_SUB_DETAIL_CODE,
             DIMENSION_SUB_DETAIL_CN_NAME,
             L3_CEG_CODE,
             L3_CEG_CN_NAME,
             L3_CEG_SHORT_CN_NAME,
             L4_CEG_CODE,
             L4_CEG_CN_NAME,
             L4_CEG_SHORT_CN_NAME,
             CATEGORY_CODE,
             CATEGORY_CN_NAME,
             ITEM_CODE,
             ITEM_CN_NAME,
             GS_DECRYPT(RMB_AVG_AMT, F_KEYSTR, 'AES128', 'CBC', 'SHA256') ,
             APPEND_FLAG,
             VIEW_FLAG,
             CALIBER_FLAG,
             APPEND_YEAR,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME                 
          FROM FIN_DM_OPT_FOI.DM_FOC_DMS_VIEW_ANNL_COST_APPEND_T
          WHERE VIEW_FLAG = F_VIEW_FLAG;
   
        INSERT INTO FIN_DM_OPT_FOI.DM_FOC_DMS_VIEW_ANNL_COST_T (
                      PERIOD_YEAR,
                      LV0_PROD_RND_TEAM_CODE,
                      LV0_PROD_RD_TEAM_CN_NAME,
                      LV1_PROD_RND_TEAM_CODE,
                      LV1_PROD_RD_TEAM_CN_NAME,
                      LV2_PROD_RND_TEAM_CODE,
                      LV2_PROD_RD_TEAM_CN_NAME,
                      LV3_PROD_RND_TEAM_CODE,
                      LV3_PROD_RD_TEAM_CN_NAME,
                      DIMENSION_CODE,
                      DIMENSION_CN_NAME,
                      DIMENSION_SUBCATEGORY_CODE,
                      DIMENSION_SUBCATEGORY_CN_NAME,
                      DIMENSION_SUB_DETAIL_CODE,
                      DIMENSION_SUB_DETAIL_CN_NAME,
                      L3_CEG_CODE,
                      L3_CEG_CN_NAME,
                      L3_CEG_SHORT_CN_NAME,
                      L4_CEG_CODE,
                      L4_CEG_CN_NAME,
                      L4_CEG_SHORT_CN_NAME,
                      CATEGORY_CODE,
                      CATEGORY_CN_NAME,
                      ITEM_CODE,
                      ITEM_CN_NAME,
                      RMB_AVG_AMT,
                      CREATED_BY,
                      CREATION_DATE,
                      LAST_UPDATED_BY,
                      LAST_UPDATE_DATE,
                      DEL_FLAG,
                      APPEND_FLAG,
                      VIEW_FLAG,
                      VERSION_ID,
                      CALIBER_FLAG,
                      APPEND_YEAR,
                      OVERSEA_FLAG,
                      LV0_PROD_LIST_CODE,
                      LV0_PROD_LIST_CN_NAME)
       SELECT PERIOD_YEAR,
             LV0_PROD_RND_TEAM_CODE,
             LV0_PROD_RD_TEAM_CN_NAME,
             LV1_PROD_RND_TEAM_CODE,
             LV1_PROD_RD_TEAM_CN_NAME,
             LV2_PROD_RND_TEAM_CODE,
             LV2_PROD_RD_TEAM_CN_NAME,
             LV3_PROD_RND_TEAM_CODE,
             LV3_PROD_RD_TEAM_CN_NAME,
             DIMENSION_CODE,
             DIMENSION_CN_NAME,
             DIMENSION_SUBCATEGORY_CODE,
             DIMENSION_SUBCATEGORY_CN_NAME,
             DIMENSION_SUB_DETAIL_CODE,
             DIMENSION_SUB_DETAIL_CN_NAME,
             L3_CEG_CODE,
             L3_CEG_CN_NAME,
             L3_CEG_SHORT_CN_NAME,
             L4_CEG_CODE,
             L4_CEG_CN_NAME,
             L4_CEG_SHORT_CN_NAME,
             CATEGORY_CODE,
             CATEGORY_CN_NAME,
             ITEM_CODE,
             ITEM_CN_NAME,
             GS_ENCRYPT( RMB_AVG_AMT ,F_KEYSTR,'AES128','CBC','SHA256' ) AS RMB_AVG_AMT,    -- 加密金额字段
             -1 AS CREATED_BY,
             CURRENT_TIMESTAMP AS CREATION_DATE,
             -1 AS LAST_UPDATED_BY,
             CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
             'N' AS DEL_FLAG,
             APPEND_FLAG,
             VIEW_FLAG,
             VERSION_ID,
             CALIBER_FLAG,
             APPEND_YEAR,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME                 
          FROM DECRYPT_AVG_TMP;

 --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '加解密视角为：'||F_VIEW_FLAG||'的数据插入数据表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');      
   
   --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FOC_DMS_VIEW_ANNL_COST_T';

  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集DM_FOC_DMS_VIEW_ANNL_COST_T统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

