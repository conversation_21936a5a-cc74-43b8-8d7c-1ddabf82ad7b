-- Name: f_dm_fom_item_exps_dtl_t_bak; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_item_exps_dtl_t_bak(f_keystr character varying, f_caliber_flag character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
创建时间：2023/12/05
创建人  ：许灿烽
背景描述：制造单领域数据明细表(包含自制与EMS) 
参数描述:
参数描述:
        参数一(f_keystr)：绝密数据解密密钥串
        参数二(f_caliber_flag)：自制或制造
        参数三(x_result_status)：运行状态返回值, 1 为成功，0 为失败
		参数四(f_version_id)：前端传入的版本号
来源表:FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_T_SUM --标准成本吸收_总(自制)  FIN_DM_OPT_FOI.DM_FOM_EMS_PO_PRICE_T --制造EMS费用明细表
目标表:FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T  --制造单领域数据明细表(包含自制与EMS)
事例：fin_dm_opt_foi.f_dm_fom_item_exps_dtl_t()
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOM_ITEM_EXPS_DTL_T'; --存储过程名称
  V_FROM_TABLE VARCHAR(50); -- 来源表
  V_TO_TABLE VARCHAR(50) := 'FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T'; -- 目标表
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT ; --版本号ID
  V_VERSION_NAME VARCHAR2(50) ; --版本中文名称
  V_SQL        TEXT;   --SQL逻辑
BEGIN
  X_RESULT_STATUS = '1';
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  --对入参合法性判断
  IF F_CALIBER_FLAG NOT IN ('M','E') THEN
     DBMS_OUTPUT.PUT_LINE ('入参有误，请重新输入！');
     RETURN '0';
  END IF;

--查询年度版本号
--取刷新数据的版本号,如果前端传值就用前端的,没有再赋值
IF F_VERSION_ID IS NULL THEN 
  SELECT VERSION_ID,VERSION INTO V_VERSION_ID,V_VERSION_NAME
    FROM
        FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T 
    WHERE
        SUBSTR(VERSION,1,6) = TO_CHAR(CURRENT_DATE, 'YYYYMM')
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL' AND VERSION_TYPE='AUTO';
ELSE V_VERSION_ID := F_VERSION_ID;
	
	SELECT VERSION INTO V_VERSION_NAME
	FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T
	WHERE VERSION_ID = V_VERSION_ID;
END IF;
		

--只留一个版本的数据
DELETE FROM FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T
 WHERE 
--VERSION_ID = V_VERSION_ID AND 
CALIBER_FLAG = F_CALIBER_FLAG
;

--传参为M
IF f_caliber_flag = 'M' THEN 
WITH VIEW_DATA_TEMP AS (
SELECT  
 PERIOD_YEAR    --会计年
,PERIOD_ID    --会计期
,LV0_CODE    --重量级团队LV0编码
,LV0_CN_NAME    --重量级团队LV0中文名称
,LV1_CODE    --重量级团队LV1编码
,LV1_CN_NAME    --重量级团队LV1中文名称
,BUSSINESS_OBJECT_CODE    --经营对象编码
,BUSSINESS_OBJECT_CN_NAME    --经营对象名称
,SHIPPING_OBJECT_CODE     --发货对象编码
,SHIPPING_OBJECT_CN_NAME    --发货对象名称
,MANUFACTURE_OBJECT_CODE     --制造对象编码
,MANUFACTURE_OBJECT_CN_NAME    --制造对象名称
,ITEM_CODE    --子项ITEM编码
,ITEM_CN_NAME    --子项ITEM中文名称
--,TO_NUMBER(GS_DECRYPT(RMB_MADE_AMT,f_keystr, 'AES128', 'CBC', 'SHA256')) AS RMB_MADE_AMT --吸收金额  
,RMB_MADE_AMT --吸收金额  
,TRANSACTION_QUANTITY    --交易数量
FROM FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_T_SUM
 WHERE VERSION_ID = V_VERSION_ID
)

,DIST_DATA_TEMP AS(
      --筛选出单item制造对象的维度信息
      SELECT LV0_CODE ,LV1_CODE,BUSSINESS_OBJECT_CODE,SHIPPING_OBJECT_CODE,MANUFACTURE_OBJECT_CODE,ITEM_CODE
        FROM (SELECT LV0_CODE ,LV1_CODE,BUSSINESS_OBJECT_CODE,SHIPPING_OBJECT_CODE,MANUFACTURE_OBJECT_CODE,ITEM_CODE,
                     COUNT(1) OVER(PARTITION BY LV0_CODE ,LV1_CODE,BUSSINESS_OBJECT_CODE,SHIPPING_OBJECT_CODE,MANUFACTURE_OBJECT_CODE) AS ITEM_FLAG
                  FROM (SELECT DISTINCT LV0_CODE ,LV1_CODE,BUSSINESS_OBJECT_CODE,SHIPPING_OBJECT_CODE,MANUFACTURE_OBJECT_CODE,ITEM_CODE
                          FROM VIEW_DATA_TEMP T) A) B
       WHERE ITEM_FLAG = 1)

--制造单领域数据明细表(包含自制与EMS) 
INSERT INTO FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T  
SELECT 
 V_VERSION_ID AS VERSION_ID    --版本ID
,V_VERSION_NAME AS VERSION_NAME    --版本名称
,A.PERIOD_YEAR    --会计年
,A.PERIOD_ID    --会计期
,A.LV0_CODE    --重量级团队LV0编码
,A.LV0_CN_NAME    --重量级团队LV0中文名称
,A.LV1_CODE    --重量级团队LV1编码
,A.LV1_CN_NAME    --重量级团队LV1中文名称
,A.BUSSINESS_OBJECT_CODE    --经营对象编码
,A.BUSSINESS_OBJECT_CN_NAME    --经营对象名称
,A.SHIPPING_OBJECT_CODE     --发货对象编码
,A.SHIPPING_OBJECT_CN_NAME    --发货对象名称
,A.MANUFACTURE_OBJECT_CODE     --制造对象编码
,A.MANUFACTURE_OBJECT_CN_NAME    --制造对象名称
,A.ITEM_CODE    --子项ITEM编码
,A.ITEM_CN_NAME    --子项ITEM中文名称
,-1 AS CREATED_BY    --创建人
,CURRENT_TIMESTAMP AS CREATION_DATE    --创建日期
,-1 AS LAST_UPDATED_BY    --最后更新人
,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE    --最后更新日期
,'N' AS DEL_FLAG    --删除标识(未删除：N，已删除：Y)
,F_CALIBER_FLAG AS CALIBER_FLAG    --业务口径（E：EMS/M：自制）
,A.RMB_MADE_AMT    --吸收金额  
,NULL AS RMB_EMS_AMT  --EMS吸收金额(自制加密，EMS金额不加密)
,A.TRANSACTION_QUANTITY    --交易数量
,CASE WHEN B.ITEM_CODE IS NULL THEN 'N' WHEN B.ITEM_CODE = '' THEN 'N' ELSE 'Y' END AS ONLY_ITEM_FLAG  --Y：单一ITEM品类数据，N：多ITEM品类数据
FROM VIEW_DATA_TEMP A 
LEFT JOIN DIST_DATA_TEMP B 
ON( A.LV0_CODE = B.LV0_CODE
AND A.LV1_CODE=B.LV1_CODE
AND A.BUSSINESS_OBJECT_CODE=B.BUSSINESS_OBJECT_CODE
AND A.SHIPPING_OBJECT_CODE=B.SHIPPING_OBJECT_CODE
AND A.MANUFACTURE_OBJECT_CODE=B.MANUFACTURE_OBJECT_CODE
AND A.ITEM_CODE = B.ITEM_CODE
)
;

--传参为E
ELSIF f_caliber_flag = 'E' THEN 
WITH VIEW_DATA_TEMP AS (
SELECT  
 PERIOD_YEAR    --会计年
,PERIOD_ID    --会计期
,LV0_CODE    --重量级团队LV0编码
,LV0_CN_NAME    --重量级团队LV0中文名称
,LV1_CODE    --重量级团队LV1编码
,LV1_CN_NAME    --重量级团队LV1中文名称
,BUSSINESS_OBJECT_CODE    --经营对象编码
,BUSSINESS_OBJECT_CN_NAME    --经营对象名称
,SHIPPING_OBJECT_CODE     --发货对象编码
,SHIPPING_OBJECT_CN_NAME    --发货对象名称
,MANUFACTURE_OBJECT_CODE     --制造对象编码
,MANUFACTURE_OBJECT_CN_NAME    --制造对象名称
,ITEM_CODE    --子项ITEM编码
,ITEM_CN_NAME    --子项ITEM中文名称
,RMB_EMS_AMT  --EMS吸收金额(自制加密，EMS金额不加密)
,TRANSACTION_QUANTITY    --交易数量
FROM FIN_DM_OPT_FOI.DM_FOM_EMS_PO_PRICE_T
 WHERE VERSION_ID = V_VERSION_ID
)

,DIST_DATA_TEMP AS(
      --筛选出单item制造对象的维度信息
      SELECT LV0_CODE ,LV1_CODE,BUSSINESS_OBJECT_CODE,SHIPPING_OBJECT_CODE,MANUFACTURE_OBJECT_CODE,ITEM_CODE
        FROM (SELECT LV0_CODE ,LV1_CODE,BUSSINESS_OBJECT_CODE,SHIPPING_OBJECT_CODE,MANUFACTURE_OBJECT_CODE,ITEM_CODE,
                     COUNT(1) OVER(PARTITION BY LV0_CODE ,LV1_CODE,BUSSINESS_OBJECT_CODE,SHIPPING_OBJECT_CODE,MANUFACTURE_OBJECT_CODE) AS ITEM_FLAG
                  FROM (SELECT DISTINCT LV0_CODE ,LV1_CODE,BUSSINESS_OBJECT_CODE,SHIPPING_OBJECT_CODE,MANUFACTURE_OBJECT_CODE,ITEM_CODE
                          FROM VIEW_DATA_TEMP T) A) B
       WHERE ITEM_FLAG = 1)

--制造单领域数据明细表(包含自制与EMS) 
INSERT INTO FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T  
SELECT 
 V_VERSION_ID AS VERSION_ID    --版本ID
,V_VERSION_NAME AS VERSION_NAME    --版本名称
,A.PERIOD_YEAR    --会计年
,A.PERIOD_ID    --会计期
,A.LV0_CODE    --重量级团队LV0编码
,A.LV0_CN_NAME    --重量级团队LV0中文名称
,A.LV1_CODE    --重量级团队LV1编码
,A.LV1_CN_NAME    --重量级团队LV1中文名称
,A.BUSSINESS_OBJECT_CODE    --经营对象编码
,A.BUSSINESS_OBJECT_CN_NAME    --经营对象名称
,A.SHIPPING_OBJECT_CODE     --发货对象编码
,A.SHIPPING_OBJECT_CN_NAME    --发货对象名称
,A.MANUFACTURE_OBJECT_CODE     --制造对象编码
,A.MANUFACTURE_OBJECT_CN_NAME    --制造对象名称
,A.ITEM_CODE    --子项ITEM编码
,A.ITEM_CN_NAME    --子项ITEM中文名称
,-1 AS CREATED_BY    --创建人
,CURRENT_TIMESTAMP AS CREATION_DATE    --创建日期
,-1 AS LAST_UPDATED_BY    --最后更新人
,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE    --最后更新日期
,'N' AS DEL_FLAG    --删除标识(未删除：N，已删除：Y)
,F_CALIBER_FLAG AS CALIBER_FLAG    --业务口径（E：EMS/M：自制）
,NULL AS RMB_MADE_AMT    --吸收金额  
,A.RMB_EMS_AMT  --EMS吸收金额(自制加密，EMS金额不加密)
,A.TRANSACTION_QUANTITY    --交易数量
,CASE WHEN B.ITEM_CODE IS NULL THEN 'N' WHEN B.ITEM_CODE = '' THEN 'N' ELSE 'Y' END AS ONLY_ITEM_FLAG  --Y：单一ITEM品类数据，N：多ITEM品类数据
FROM VIEW_DATA_TEMP A 
LEFT JOIN DIST_DATA_TEMP B 
ON( A.LV0_CODE = B.LV0_CODE
AND A.LV1_CODE=B.LV1_CODE
AND A.BUSSINESS_OBJECT_CODE=B.BUSSINESS_OBJECT_CODE
AND A.SHIPPING_OBJECT_CODE=B.SHIPPING_OBJECT_CODE
AND A.MANUFACTURE_OBJECT_CODE=B.MANUFACTURE_OBJECT_CODE
AND A.ITEM_CODE = B.ITEM_CODE
)
;

  END IF;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '清空制造单领域数据明细表(包含自制与EMS),并重新插入新的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END$$
/

