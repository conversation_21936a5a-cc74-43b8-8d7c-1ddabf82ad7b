-- Name: f_foc_dwl_prod_bom_item_rev_detail_i; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_foc_dwl_prod_bom_item_rev_detail_i(OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：2023-07-29  0819修改
创建人  ：李志勇 l00808731
背景描述：收入时点明细实际数数据,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_foc_dwl_prod_bom_item_rev_detail_i()

*/


declare
	v_sp_name varchar(100) := 'fin_dm_opt_foi.f_foc_dwl_prod_bom_item_rev_detail_i';
	v_tbl_name varchar(100) := 'fin_dm_opt_foi.foc_dwl_prod_bom_item_rev_detail_i';
	v_dml_row_count  number default 0 ;


begin
	x_result_status := 'SUCCESS';       --1表示成功
	

	 --写日志,开始
	insert into fin_dm_opt_foi.dm_foi_log_t
		(log_id,
		 version_id,
		 sp_name,
		 para_list,
		 step_num,
		 cal_log_desc,
		 formula_sql_txt,
		 dml_row_count	,
		 result_status,
		 errbuf,
		 created_by,
		 creation_date)
	values
		(fin_dm_opt_foi.dm_foi_log_s.nextval,
		 null,
		 v_sp_name,
		 '',
		 1,                                             --第一步
		 '收入时点明细实际数数据'||v_tbl_name||'：开始运行',
		 null,
		 v_dml_row_count,
		 x_result_status,
		 null,
		 1,
		 current_timestamp);


		---支持重跑，清除目标表要插入会计期的数据
		delete from fin_dm_opt_foi.foc_dwl_prod_bom_item_rev_detail_i 
		where period_id in (select distinct period_id from fin_dm_opt_foi.foc_dwl_prod_bom_item_rev_detail_i_temp);    ---temp表的日期 

		---插入目标表数据
		insert into fin_dm_opt_foi.foc_dwl_prod_bom_item_rev_detail_i            ----收入时点明细实际数
		(
			period_id,
			item_code,
			item_cn_name,
			quantity,
			rmb_item_unit_cost_amt,
			per_unit_qty,
			parentpartnumber,
			parent_quantity,
			rmb_fact_rate_gc_amt,
			non_child_flag,
			level_rel,
			non_sale_flag,
			consignment_type_code,
			consignment_type,
			model_num,
			item_subtype_code,
			item_subtype_cn_name,
			prod_key,
			geo_pc_key,
			sign_cust_key,
			end_cust_key,
			agent_distribution_cust_key,
			enterprise_cust_key,
			account_dept_cust_key,
			cust_key,
			contract_key,
			hw_contract_num,
			proj_key,
			src_sys_name,
			crt_cycle_id,
			last_upd_cycle_id,
			dw_last_update_date,
			del_flag,
			is_resale_flag,
            main_dimension_key
		)
		select 
			period_id,
			item_code,
			item_cn_name,
			quantity,
			rmb_item_unit_cost_amt,
			per_unit_qty,
			parentpartnumber,
			parent_quantity,
			rmb_fact_rate_gc_amt,
			non_child_flag,
			level_rel,
			non_sale_flag,
			consignment_type_code,
			consignment_type,
			model_num,
			item_subtype_code,
			item_subtype_cn_name,
			prod_key,
			geo_pc_key,
			sign_cust_key,
			end_cust_key,
			agent_distribution_cust_key,
			enterprise_cust_key,
			account_dept_cust_key,
			cust_key,
			contract_key,
			hw_contract_num,
			proj_key,
			src_sys_name,
			crt_cycle_id,
			last_upd_cycle_id,
			dw_last_update_date,
			del_flag,
			is_resale_flag,
            main_dimension_key
	   from fin_dm_opt_foi.foc_dwl_prod_bom_item_rev_detail_i_temp
  		  ;

	v_dml_row_count := sql%rowcount;          -- 收集数据量

	 -- 写结束日志
	insert into fin_dm_opt_foi.dm_foi_log_t
		(log_id,
		version_id,
		sp_name,
		para_list,
		step_num,
		cal_log_desc,
		formula_sql_txt,
		dml_row_count,
		result_status,
		errbuf,
		created_by,
		creation_date)
	values
		(
		fin_dm_opt_foi.dm_foi_log_s.nextval,
		null,
		v_sp_name,
		'',
		2,                                             --最后一步
		'收入时点明细实际数数据'||v_tbl_name||'：结束运行',
		null,
		v_dml_row_count,
		'1',
		null,
		1,
		current_timestamp);
		
    --收集统计信息
    analyse fin_dm_opt_foi.foc_dwl_prod_bom_item_rev_detail_i;	 	
			
 RETURN 'SUCCESS';
		
exception
  	when others then
	 x_result_status := 'FAILED';       --2001表示失败
	
	  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
	  (
		   F_SP_NAME => V_SP_NAME, 
		   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
		   F_RESULT_STATUS => x_result_status, 
		   F_ERRBUF => SQLSTATE||':'||SQLERRM
	   );


end;
$$
/

