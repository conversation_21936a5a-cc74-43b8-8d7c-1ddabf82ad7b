-- Name: f_dm_foc_actual_cost_dms; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_actual_cost_dms(f_industry_flag character varying, f_dimension_type character varying, f_keystr character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/***************************************************************************************************************************************************************
最近修改时间: 2024年6月3日16点24分
修改人   ：黄心蕊 hwx1187045
修改内容： 202407版本 新增IAS部分,及通用颗粒度和量纲颗粒度IAS新增LV4层级
修改时间: 2024年4月16日16点52分
修改人   ：黄心蕊 hwx1187045
修改内容： 202405版本 新增数字能源部分,及量纲增加COA层级
创建时间：2023-03-20
创建人  ：黄心蕊 hwx1187045
修改时间：2023-12-18
修改人   ：黄心蕊 hwx1187045
修改时间：2024-03-07
修改人   ：黄心蕊 hwx1187045
修改内容： 202403版本 新增PARENT_CN_NAME
修改时间：2024-03-27
修改人   ：黄心蕊 hwx1187045
修改内容： 20240327 修改版本号取数逻辑
背景描述：热力图表数据初始化
参数描述：参数一(f_keystr)：绝密数据解密密钥串
          参数二(f_item_version)：通用版本号
          参数三(F_DIMENSION_TYPE)：维度类型（U：通用/P：盈利颗粒度/D:量纲颗粒度）
          参数四(X_RESULT_STATUS)：运行状态返回值 ‘1’为成功，‘0’为失败
		  参数五(F_INDUSTRY_FLAG): 产业项目标识 'I'为ICT ‘E’为数字能源
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_ACTUAL_COST('I','U','密钥串'); --ICT-通用颗粒度一个版本的数据
          SELECT FIN_DM_OPT_FOI.F_DM_FOC_ACTUAL_COST('I','F','密钥串'); --ICT-盈利颗粒度一个版本的数据
          SELECT FIN_DM_OPT_FOI.F_DM_FOC_ACTUAL_COST('I','D','密钥串'); --ICT-量纲颗粒度一个版本的数据
		  
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_ACTUAL_COST('E','U','密钥串'); --数字能源-通用颗粒度一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_ACTUAL_COST('E','F','密钥串'); --数字能源-盈利颗粒度一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_ACTUAL_COST('E','D','密钥串'); --数字能源-量纲颗粒度一个版本的数据
		  
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_ACTUAL_COST('IAS','U','密钥串'); --数字能源-通用颗粒度一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_ACTUAL_COST('IAS','F','密钥串'); --数字能源-盈利颗粒度一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_ACTUAL_COST('IAS','D','密钥串'); --数字能源-量纲颗粒度一个版本的数据

****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                      VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_ACTUAL_COST_DMS';	--DMS修改
  V_VERSION                      BIGINT;--版本号
  V_EXCEPTION_FLAG               VARCHAR(20) := '0'; --异常定点
  V_KEYSTR                       VARCHAR(100) := F_KEYSTR;--密钥串
  V_DIMENSION_TYPE               VARCHAR(2) := F_DIMENSION_TYPE;--维度标识
  V_BASE_AMT_TEMP                TEXT :=''; --会话级临时表表名
  V_DECRYP_AMT_TEMP              TEXT :=''; --金额解密临时表表名
  V_LV3_PROD_RND_TEAM_CODE       TEXT :=''; --LV3_CODE字段
  V_LV3_PROD_RD_TEAM_CN_NAME     TEXT :=''; --LV3_NAME字段
  V_LV2_VIEW                     TEXT :=''; --LV2所在视角条件
  V_LV1_VIEW                     TEXT :=''; --LV1所在视角条件
  V_LV0_VIEW                     TEXT :=''; --LV0所在视角条件
  V_CEG_PARENT_CODE              TEXT :=''; --专家团上层级CODE判断
  V_MID_TABLE                    TEXT :=''; --中间表表名
  V_TARGET_TABLE                 TEXT :=''; --目标表表名
  V_PROFITS_NAME                 TEXT :=''; --盈利颗粒度
  V_PROD_PROFITS_NAME            TEXT :=''; --重量级团队盈利颗粒度字段
  V_TOP_ITEM_INFO_T              TEXT :=''; --规格品维表
  V_MONTH_ITEM_AMT_T             TEXT :=''; --ITEM金额取数表
  V_L1_NAME                      TEXT :=''; --盈利颗粒度L1NAME
  V_L2_NAME                      TEXT :=''; --盈利颗粒度L2NAME
  V_PROD_L1_NAME                 TEXT :=''; --重量级团队盈利颗粒度L1NAME
  V_PROD_L2_NAME                 TEXT :=''; --重量级团队盈利颗粒度L2NAME
  V_SQL_LV3_PROD_RND_TEAM_CODE   TEXT :=''; --LV3_CODE查询表字段
  V_SQL_LV3_PROD_RD_TEAM_CN_NAME TEXT :=''; --LV3_NAME查询表字段
  V_SQL_L1_NAME                  TEXT :=''; --盈利颗粒度L1NAME查询表字段
  V_SQL_L2_NAME                  TEXT :=''; --盈利颗粒度L2NAME查询表字段
  V_JION_LV3_PROD_RND_TEAM_CODE  TEXT :=''; --LV3关联条件
  V_JOIN_L1_NAME                 TEXT :=''; --盈利颗粒度L1NAME关联条件
  V_JOIN_L2_NAME                 TEXT :=''; --盈利颗粒度L2NAME关联条件
  V_SQL_PROFITS_NAME             TEXT :=''; --盈利颗粒度查询字段
  V_SQL_PROD_RND_TEAM_CODE       TEXT :=''; --重量级团队CODE_CASE
  V_SQL_PROD_RND_TEAM_CN_NAME    TEXT :=''; --重量级团队NAME_CASE
  V_SQL_NEW                      TEXT :=''; --七月版本新增卷积逻辑（通用LV3，盈利L1L2）
  V_SEQUENCE                     TEXT :=''; --序列
  V_SQL                          TEXT :=''; --执行SQL
  
  V_DIMENSION_CODE                               TEXT :=''; --量纲颗粒度
  V_DIMENSION_CN_NAME                            TEXT :=''; 
  V_DIMENSION_EN_NAME                            TEXT :=''; 
  V_DIMENSION_SUBCATEGORY_CODE                   TEXT :=''; --量纲子类
  V_DIMENSION_SUBCATEGORY_CN_NAME                TEXT :=''; 
  V_DIMENSION_SUBCATEGORY_EN_NAME                TEXT :=''; 
  V_DIMENSION_SUB_DETAIL_CODE                    TEXT :=''; --量纲子类明细
  V_DIMENSION_SUB_DETAIL_CN_NAME                 TEXT :=''; 
  V_DIMENSION_SUB_DETAIL_EN_NAME                 TEXT :=''; 
  V_DMS_CODE                                     TEXT :=''; --量纲粒度
  V_DMS_CN_NAME                                  TEXT :=''; --量纲粒度
                                                      
  V_JOIN_DIMENSION_CODE                          TEXT :=''; --量纲关联条件
  V_JOIN_DIMENSION_SUBCATEGORY_CODE              TEXT :=''; --量纲子类关联条件
  V_JOIN_DIMENSION_SUB_DETAIL_CODE               TEXT :=''; --子类明细关联条件
                                                      
  V_SQL_DIMENSION_CODE                           TEXT :=''; --量纲查询表字段
  V_SQL_DIMENSION_CN_NAME                        TEXT :=''; --量纲查询表字段
  V_SQL_DIMENSION_EN_NAME                        TEXT :=''; --量纲查询表字段
  V_SQL_DIMENSION_SUBCATEGORY_CODE               TEXT :=''; --量纲子类查询表字段
  V_SQL_DIMENSION_SUBCATEGORY_CN_NAME            TEXT :=''; --量纲子类查询表字段
  V_SQL_DIMENSION_SUBCATEGORY_EN_NAME            TEXT :=''; --量纲子类查询表字段
  V_SQL_DIMENSION_SUB_DETAIL_CODE                TEXT :=''; --子类明细查询表字段
  V_SQL_DIMENSION_SUB_DETAIL_CN_NAME             TEXT :=''; --子类明细查询表字段
  V_SQL_DIMENSION_SUB_DETAIL_EN_NAME             TEXT :=''; --子类明细查询表字段
  V_SQL_DMS_CODE                                 TEXT :='';
  V_SQL_DMS_CN_NAME                              TEXT :=''; 
  V_SQL_L2_NEW                                   TEXT :=''; 
  V_SQL_L1_NEW                                   TEXT :=''; 
  V_SQL_L0_NEW                                   TEXT :=''; 
  
  V_PROD_DMS_CODE                       TEXT :='';
  V_PROD_DMS_CN_NAME                    TEXT :='';
  V_PROD_DIMENSION_CODE                 TEXT :='';
  V_PROD_DIMENSION_CN_NAME              TEXT :='';
  V_PROD_DIMENSION_SUBCATEGORY_CODE     TEXT :='';
  V_PROD_DIMENSION_SUBCATEGORY_CN_NAME  TEXT :='';
  V_PROD_DIMENSION_SUB_DETAIL_CODE      TEXT :='';
  V_PROD_DIMENSION_SUB_DETAIL_CN_NAME   TEXT :='';
  
  --202401版本新增SPART层级
  V_SPART_CODE                     	   TEXT :=''; --量纲颗粒度
  V_SPART_CN_NAME                  	   TEXT :=''; 
  V_JOIN_SPART_CODE                	   TEXT :=''; --量纲关联条件
  V_SQL_SPART_CODE                 	   TEXT :=''; --量纲查询表字段
  V_SQL_SPART_CN_NAME				   TEXT :=''; --量纲查询表字段
  V_PROD_SPART_CODE                    TEXT :=''; 
  V_PROD_SPART_CN_NAME                 TEXT :=''; 
  
  --202405版本 数字能源新增COA层级
  V_COA_PART		  TEXT :='';
  V_LV4_PART		 TEXT :='';
  V_SQL_DIFF_COLUMN_PART      TEXT :='';
  V_PROD_DIFF_COLUMN_PART     TEXT :='';
  V_JOIN_DIFF_COLUMN_CODE     TEXT :='';
  V_DIFF_COLUMN_SQL_NEW       TEXT :='';
  V_DIMENSION_PARENT	TEXT :='';
  
BEGIN
X_RESULT_STATUS := '1';

--0.日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

V_EXCEPTION_FLAG := '1';   
--版本号入参判断，当入参为空，取TOP规格品清单最新版本号
IF F_ITEM_VERSION IS NULL THEN
	IF F_INDUSTRY_FLAG = 'I' THEN
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
	 
	ELSIF F_INDUSTRY_FLAG = 'E' THEN	--202405版本 新增数字能源部分
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
		 
	ELSIF F_INDUSTRY_FLAG = 'IAS' THEN	--202407版本 新增IAS部分
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
	END IF ;
		
--入参不为空，则以入参为版本号
ELSE V_VERSION := F_ITEM_VERSION;
END IF;
  
  --1.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '取得本次执行数据产业项目标识：'||F_INDUSTRY_FLAG||'，颗粒度：'||V_DIMENSION_TYPE||'，以及版本号：'||V_VERSION ,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 


V_EXCEPTION_FLAG := '2';   
  IF V_DIMENSION_TYPE = 'U' THEN
	
	V_SQL_PROD_RND_TEAM_CODE       := '         
		 CASE T2.VIEW_FLAG
		   WHEN ''0'' THEN T2.LV0_PROD_RND_TEAM_CODE
		   WHEN ''1'' THEN T2.LV1_PROD_RND_TEAM_CODE
		   WHEN ''2'' THEN T2.LV2_PROD_RND_TEAM_CODE
		 ELSE T2.LV3_PROD_RND_TEAM_CODE
		 END AS PROD_RND_TEAM_CODE,';
	V_SQL_PROD_RND_TEAM_CN_NAME    := '
	   CASE T2.VIEW_FLAG
		   WHEN ''0'' THEN T2.LV0_PROD_RD_TEAM_CN_NAME
		   WHEN ''1'' THEN T2.LV1_PROD_RD_TEAM_CN_NAME
		   WHEN ''2'' THEN T2.LV2_PROD_RD_TEAM_CN_NAME
	   ELSE T2.LV3_PROD_RD_TEAM_CN_NAME
	   END AS PROD_RND_TEAM_CN_NAME,';
	   
	  IF F_INDUSTRY_FLAG = 'I' THEN 
	  /*表定义*/
		V_BASE_AMT_TEMP    := 'DM_FOC_BASE_UNI_TEMP';
		V_DECRYP_AMT_TEMP  := 'DM_FOC_UNI_DECRYP_TEMP';
		V_MID_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_MIDDLE_TOP_AMT_T';
		V_TARGET_TABLE     := 'FIN_DM_OPT_FOI.DM_FOC_ACTUAL_COST_T';
		V_TOP_ITEM_INFO_T  := 'FIN_DM_OPT_FOI.DM_FOC_TOP_ITEM_INFO_T';
		V_MONTH_ITEM_AMT_T := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T';
		
	  ELSIF F_INDUSTRY_FLAG = 'E' THEN  --202405版本 新增数字能源部分来源数据
	  /*表定义*/
		V_BASE_AMT_TEMP    := 'DM_FOC_ENERGY_BASE_UNI_TEMP';
		V_DECRYP_AMT_TEMP  := 'DM_FOC_ENERGY_UNI_DECRYP_TEMP';
		V_MID_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MIDDLE_TOP_AMT_T';
		V_TARGET_TABLE     := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_ACTUAL_COST_T';
		V_TOP_ITEM_INFO_T  := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOP_ITEM_INFO_T';
		V_MONTH_ITEM_AMT_T := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_T';
		
	  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN  --202407版本 新增IAS部分来源数据
	  /*表定义*/
		V_BASE_AMT_TEMP    := 'DM_FOC_IAS_BASE_UNI_TEMP';
		V_DECRYP_AMT_TEMP  := 'DM_FOC_IAS_UNI_DECRYP_TEMP';
		V_MID_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MIDDLE_TOP_AMT_T';
		V_TARGET_TABLE     := 'FIN_DM_OPT_FOI.DM_FOC_IAS_ACTUAL_COST_T';
		V_TOP_ITEM_INFO_T  := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOP_ITEM_INFO_T';
		V_MONTH_ITEM_AMT_T := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_T';
		
		V_SQL_PROD_RND_TEAM_CODE       := '         
			 CASE T2.VIEW_FLAG
			   WHEN ''0'' THEN T2.LV0_PROD_RND_TEAM_CODE
			   WHEN ''1'' THEN T2.LV1_PROD_RND_TEAM_CODE
			   WHEN ''2'' THEN T2.LV2_PROD_RND_TEAM_CODE
			   WHEN ''7'' THEN T2.LV4_PROD_RND_TEAM_CODE		--202407版本 IAS新增视角7 IAS新增LV4层级
			 ELSE T2.LV3_PROD_RND_TEAM_CODE
			 END AS PROD_RND_TEAM_CODE,';
		V_SQL_PROD_RND_TEAM_CN_NAME    := '
		   CASE T2.VIEW_FLAG
			   WHEN ''0'' THEN T2.LV0_PROD_RD_TEAM_CN_NAME
			   WHEN ''1'' THEN T2.LV1_PROD_RD_TEAM_CN_NAME
			   WHEN ''2'' THEN T2.LV2_PROD_RD_TEAM_CN_NAME
			   WHEN ''7'' THEN T2.LV4_PROD_RD_TEAM_CN_NAME		--202407版本 IAS新增视角7 IAS新增LV4层级
		   ELSE T2.LV3_PROD_RD_TEAM_CN_NAME
		   END AS PROD_RND_TEAM_CN_NAME,';
		
		V_LV4_PART			:= 'LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME,';		--202407版本 IAS新增LV4层级
		V_SQL_DIFF_COLUMN_PART		:= 'T2.LV4_PROD_RND_TEAM_CODE,T2.LV4_PROD_RD_TEAM_CN_NAME,';
		V_JOIN_DIFF_COLUMN_CODE		:= 'AND NVL(T1.LV4_PROD_RND_TEAM_CODE,4) = NVL(T2.LV4_PROD_RND_TEAM_CODE,4)';
		
		V_DIFF_COLUMN_SQL_NEW		:= '
/*LV4层级收敛*/
SELECT ' || V_VERSION || '  AS VERSION_ID,
       PERIOD_YEAR,
       PERIOD_ID,
       VIEW_FLAG,
       LV4_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
       LV4_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
       LV4_PROD_RND_TEAM_CODE AS GROUP_CODE,
       LV4_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
       ''LV4'' AS GROUP_LEVEL,
       SUM(ACTUAL_COST_AMT) AS ACTUAL_COST_AMT,
       LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,
	   LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,  
       ''-1'' AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       ''-1'' AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       ''N'' AS DEL_FLAG,
        CALIBER_FLAG,
        OVERSEA_FLAG,
        LV0_PROD_LIST_CODE,
        LV0_PROD_LIST_CN_NAME
  FROM ' || V_BASE_AMT_TEMP || '
 WHERE VIEW_FLAG = 7		--202407版本 IAS新增视角7
 GROUP BY VERSION_ID,
          PERIOD_YEAR,
          PERIOD_ID,
          VIEW_FLAG,
          LV4_PROD_RND_TEAM_CODE,
		  LV4_PROD_RD_TEAM_CN_NAME,
          LV3_PROD_RND_TEAM_CODE,
          LV3_PROD_RD_TEAM_CN_NAME,		  
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
UNION ALL
		';

	  END IF;
	
    
  /*字段值定义*/
    V_LV3_PROD_RND_TEAM_CODE       := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME     := 'LV3_PROD_RD_TEAM_CN_NAME,';
    V_SQL_LV3_PROD_RND_TEAM_CODE   := 'T2.LV3_PROD_RND_TEAM_CODE,';
    V_SQL_LV3_PROD_RD_TEAM_CN_NAME := 'T2.LV3_PROD_RD_TEAM_CN_NAME,';
       
    V_CEG_PARENT_CODE              := ' PROD_RND_TEAM_CODE AS PARENT_CODE,
										PROD_RND_TEAM_CN_NAME AS PARENT_CN_NAME,';   --202403版本 新增PARENT_CN_NAME
       
  /*条件定义*/
    V_JION_LV3_PROD_RND_TEAM_CODE := 'AND NVL(T1.LV3_PROD_RND_TEAM_CODE,3) = NVL(T2.LV3_PROD_RND_TEAM_CODE,3)';
    V_LV2_VIEW                    := ' WHERE VIEW_FLAG IN(2,3,7)';		--202407版本 IAS新增视角7
    V_LV1_VIEW                    := ' WHERE VIEW_FLAG IN(1,2,3,7)';
    V_LV0_VIEW                    := ' WHERE VIEW_FLAG IN(0,1,2,3,7)';
  /*版本新加入逻辑定义*/
    V_SQL_NEW := V_DIFF_COLUMN_SQL_NEW 		--202407版本 IAS新增LV4层级
	||'
/*LV3层级收敛*/
SELECT ' || V_VERSION || '  AS VERSION_ID,
       PERIOD_YEAR,
       PERIOD_ID,
       VIEW_FLAG,
       LV3_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
       LV3_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
       LV3_PROD_RND_TEAM_CODE AS GROUP_CODE,
       LV3_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
       ''LV3'' AS GROUP_LEVEL,
       SUM(ACTUAL_COST_AMT) AS ACTUAL_COST_AMT,
       LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
	   LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,  --202403版本 新增PARENT_CN_NAME
       ''-1'' AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       ''-1'' AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       ''N'' AS DEL_FLAG,
        CALIBER_FLAG,
        OVERSEA_FLAG,
        LV0_PROD_LIST_CODE,
        LV0_PROD_LIST_CN_NAME
  FROM ' || V_BASE_AMT_TEMP || '
 WHERE VIEW_FLAG IN(3 ,7)		--202407版本 IAS新增视角7
 GROUP BY VERSION_ID,
          PERIOD_YEAR,
          PERIOD_ID,
          VIEW_FLAG,
          LV2_PROD_RND_TEAM_CODE,
		  LV2_PROD_RD_TEAM_CN_NAME,
          LV3_PROD_RND_TEAM_CODE,
          LV3_PROD_RD_TEAM_CN_NAME,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
UNION ALL
';

  ELSIF V_DIMENSION_TYPE = 'P' THEN
	/*表定义*/
  IF F_INDUSTRY_FLAG = 'I' THEN 

    V_BASE_AMT_TEMP                := 'DM_FOC_BASE_PFT_TEMP';
    V_DECRYP_AMT_TEMP              := 'DM_FOC_PFT_DECRYP_TEMP';
    V_MID_TABLE                    := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MIDDLE_TOP_AMT_T';
    V_TARGET_TABLE                 := 'FIN_DM_OPT_FOI.DM_FOC_PFT_ACTUAL_COST_T';
    V_TOP_ITEM_INFO_T              := 'FIN_DM_OPT_FOI.DM_FOC_PFT_TOP_ITEM_INFO_T';
    V_MONTH_ITEM_AMT_T             := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_ITEM_T';
	
  ELSIF F_INDUSTRY_FLAG = 'E' THEN  --202405版本 新增数字能源部分来源数据
  
    V_BASE_AMT_TEMP                := 'DM_FOC_ENERGY_BASE_PFT_TEMP';
    V_DECRYP_AMT_TEMP              := 'DM_FOC_ENERGY_PFT_DECRYP_TEMP';
    V_MID_TABLE                    := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_MIDDLE_TOP_AMT_T';
    V_TARGET_TABLE                 := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_ACTUAL_COST_T';
    V_TOP_ITEM_INFO_T              := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_TOP_ITEM_INFO_T';
    V_MONTH_ITEM_AMT_T             := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_MID_MONTH_ITEM_T';
	
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN  --202407版本 新增IAS部分来源数据
  
    V_BASE_AMT_TEMP                := 'DM_FOC_IAS_BASE_PFT_TEMP';
    V_DECRYP_AMT_TEMP              := 'DM_FOC_IAS_PFT_DECRYP_TEMP';
    V_MID_TABLE                    := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_MIDDLE_TOP_AMT_T';
    V_TARGET_TABLE                 := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_ACTUAL_COST_T';
    V_TOP_ITEM_INFO_T              := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_TOP_ITEM_INFO_T';
    V_MONTH_ITEM_AMT_T             := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_MID_MONTH_ITEM_T';
	
  END IF;
  
  /*字段值定义*/
    V_L1_NAME                      := 'L1_NAME,';
    V_L2_NAME                      := 'L2_NAME,';
    V_PROFITS_NAME                 := 'PROFITS_NAME,';
    V_SQL_L1_NAME                  := 'T2.L1_NAME,';
    V_SQL_L2_NAME                  := 'T2.L2_NAME,';
    V_SQL_PROFITS_NAME          := '
           CASE T2.VIEW_FLAG 
            WHEN ''3'' THEN T2.L1_NAME 
            WHEN ''4'' THEN T2.L2_NAME 
            ELSE ''''
           END AS PROFITS_NAME,';
    V_SQL_PROD_RND_TEAM_CODE    := '         
      CASE T2.VIEW_FLAG
       WHEN ''0'' THEN T2.LV0_PROD_RND_TEAM_CODE
       WHEN ''1'' THEN T2.LV1_PROD_RND_TEAM_CODE
       ELSE T2.LV2_PROD_RND_TEAM_CODE
      END AS PROD_RND_TEAM_CODE,';
    V_SQL_PROD_RND_TEAM_CN_NAME := '
     CASE T2.VIEW_FLAG
       WHEN ''0'' THEN T2.LV0_PROD_RD_TEAM_CN_NAME
       WHEN ''1'' THEN T2.LV1_PROD_RD_TEAM_CN_NAME
       ELSE T2.LV2_PROD_RD_TEAM_CN_NAME
     END AS PROD_RND_TEAM_CN_NAME,';
    
    --V_SEQUENCE                     := 'FIN_DM_OPT_FOI.DM_FOC_PFT_ACTUAL_COST_S.NEXTVAL';
    V_CEG_PARENT_CODE              := '
        CASE 
         WHEN VIEW_FLAG IN (0,1,2) THEN PROD_RND_TEAM_CODE
         ELSE PROFITS_NAME
        END AS PARENT_CODE ,
		CASE 
         WHEN VIEW_FLAG IN (0,1,2) THEN PROD_RND_TEAM_CN_NAME
         ELSE PROFITS_NAME
        END AS PARENT_CN_NAME ,   --202403版本 新增PARENT_CN_NAME
		';
        
    V_PROD_L1_NAME                 := ' '''' AS L1_NAME,';
    V_PROD_L2_NAME                 := ' '''' AS L2_NAME,';
    V_PROD_PROFITS_NAME            := ' '''' AS PROFITS_NAME,';
    
  
  /*条件定义*/
    V_JOIN_L1_NAME := ' AND NVL(T1.L1_NAME,3) = NVL(T2.L1_NAME,3)';
    V_JOIN_L2_NAME := ' AND NVL(T1.L2_NAME,4) = NVL(T2.L2_NAME,4)';
    V_LV2_VIEW     := ' WHERE VIEW_FLAG IN(3,4)';
    V_LV1_VIEW     := ' WHERE VIEW_FLAG IN(3,4)';
    V_LV0_VIEW     := ' WHERE VIEW_FLAG IN(3,4)';
  /*版本新加逻辑定义*/
    V_SQL_NEW := '
 SELECT ' || V_VERSION || '  AS VERSION_ID,
       PERIOD_YEAR,
       PERIOD_ID,
       VIEW_FLAG,
       LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
       LV2_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
       L2_NAME AS PROFITS_NAME,
       L1_NAME AS L1_NAME,
       L2_NAME AS L2_NAME,
       L2_NAME AS GROUP_CODE,
       L2_NAME AS GROUP_CN_NAME,
       ''L2'' AS GROUP_LEVEL,
       SUM(ACTUAL_COST_AMT) AS ACTUAL_COST_AMT,
       L1_NAME AS PARENT_CODE,
	   L1_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       ''-1'' AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       ''-1'' AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       ''N'' AS DEL_FLAG,
        CALIBER_FLAG,
        OVERSEA_FLAG,
        LV0_PROD_LIST_CODE,
        LV0_PROD_LIST_CN_NAME
  FROM ' || V_BASE_AMT_TEMP || '
 WHERE VIEW_FLAG = 4  
 GROUP BY VERSION_ID,
          PERIOD_YEAR,
          PERIOD_ID,
          VIEW_FLAG,
          LV2_PROD_RND_TEAM_CODE,
          LV2_PROD_RD_TEAM_CN_NAME,
          L1_NAME,
          L2_NAME,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
UNION ALL
SELECT ' || V_VERSION || '  AS VERSION_ID,
       PERIOD_YEAR,
       PERIOD_ID,
       VIEW_FLAG,
       LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
       LV2_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
       L1_NAME AS PROFITS_NAME,
       L1_NAME AS L1_NAME,
       '''' AS L2_NAME,
       L1_NAME AS GROUP_CODE,
       L1_NAME AS GROUP_CN_NAME,
       ''L1'' AS GROUP_LEVEL,
       SUM(ACTUAL_COST_AMT) AS ACTUAL_COST_AMT,
       LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
	   LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,  --202403版本 新增PARENT_CN_NAME
       ''-1'' AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       ''-1'' AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       ''N'' AS DEL_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME
  FROM ' || V_BASE_AMT_TEMP || '
 WHERE VIEW_FLAG IN (3, 4)  
 GROUP BY VERSION_ID,
          PERIOD_YEAR,
          PERIOD_ID,
          VIEW_FLAG,
          LV2_PROD_RND_TEAM_CODE,
          LV2_PROD_RD_TEAM_CN_NAME,
          L1_NAME,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
UNION ALL';

  ELSIF V_DIMENSION_TYPE = 'D' THEN
    
	/*表定义*/
  IF F_INDUSTRY_FLAG = 'I' THEN 

    V_BASE_AMT_TEMP                := 'DM_FOC_BASE_DMS_TEMP';
    V_DECRYP_AMT_TEMP              := 'DM_FOC_DMS_DECRYP_TEMP';
	/*
    V_MID_TABLE                    := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MIDDLE_TOP_AMT_T';
    V_TARGET_TABLE                 := 'FIN_DM_OPT_FOI.DM_FOC_DMS_ACTUAL_COST_T';
    V_TOP_ITEM_INFO_T              := 'FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_ITEM_INFO_T';
    V_MONTH_ITEM_AMT_T             := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T';
	*/
	
	V_MID_TABLE                    := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MIDDLE_TOP_AMT_T_DMS'; --DMS修改
	V_TARGET_TABLE                 := 'FIN_DM_OPT_FOI.DM_FOC_DMS_ACTUAL_COST_T';
	V_TOP_ITEM_INFO_T              := 'FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_ITEM_INFO_T';
	V_MONTH_ITEM_AMT_T             := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T_DMS'; --DMS修改

	V_DIMENSION_PARENT:=' PROD_RND_TEAM_CODE AS PARENT_CODE,
						  PROD_RND_TEAM_CN_NAME AS PARENT_CN_NAME,
	';	--202405版本 数字能源下12视角量纲父级为COA，其他视角均为其上重量级团队
	
    V_SQL_PROD_RND_TEAM_CODE       := '         
     CASE  WHEN T2.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T2.LV1_PROD_RND_TEAM_CODE
           WHEN T2.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T2.LV2_PROD_RND_TEAM_CODE
         ELSE T2.LV3_PROD_RND_TEAM_CODE
         END AS PROD_RND_TEAM_CODE,';--202401版本新增SPART层级 加入9，10，11三个视角

    V_SQL_PROD_RND_TEAM_CN_NAME    := '
       CASE WHEN T2.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T2.LV1_PROD_RD_TEAM_CN_NAME
            WHEN T2.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T2.LV2_PROD_RD_TEAM_CN_NAME
       ELSE T2.LV3_PROD_RD_TEAM_CN_NAME
       END AS PROD_RND_TEAM_CN_NAME,';--202401版本新增SPART层级 加入9，10，11三个视角
	
	
  ELSIF F_INDUSTRY_FLAG = 'E' THEN  --202405版本 新增数字能源部分来源数据
  
    V_BASE_AMT_TEMP                := 'DM_FOC_ENERGY_BASE_DMS_TEMP';
    V_DECRYP_AMT_TEMP              := 'DM_FOC_ENERGY_DMS_DECRYP_TEMP';
	/*
    V_MID_TABLE                    := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MIDDLE_TOP_AMT_T';
    V_TARGET_TABLE                 := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_ACTUAL_COST_T';
    V_TOP_ITEM_INFO_T              := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_TOP_ITEM_INFO_T';
    V_MONTH_ITEM_AMT_T             := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MID_MONTH_ITEM_T';*/
	
	V_MID_TABLE                    := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MIDDLE_TOP_AMT_T_DMS';	--DMS修改
	V_TARGET_TABLE                 := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_ACTUAL_COST_T';
	V_TOP_ITEM_INFO_T              := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_TOP_ITEM_INFO_T';
	V_MONTH_ITEM_AMT_T             := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MID_MONTH_ITEM_T_DMS';	--DMS修改
	
	V_COA_PART	:='COA_CODE,COA_CN_NAME,'; 
	V_SQL_DIFF_COLUMN_PART	:='T2.COA_CODE,T2.COA_CN_NAME,'; 
	   
	V_PROD_DIFF_COLUMN_PART    				:= ' '''' AS COA_CODE,'''' AS COA_CODE ,';
	
	V_JOIN_DIFF_COLUMN_CODE :='AND NVL(T1.COA_CODE,''D5'') = NVL(T2.COA_CODE,''D5'') ';
	
	V_DIMENSION_PARENT:='
	DECODE(VIEW_FLAG,''12'',COA_CODE,PROD_RND_TEAM_CODE) AS PARENT_CODE,
	DECODE(VIEW_FLAG,''12'',COA_CN_NAME,PROD_RND_TEAM_CN_NAME) AS PARENT_CN_NAME,
	';	--202405版本 数字能源下12视角量纲父级为COA，其他视角均为其上重量级团队
	
    V_SQL_PROD_RND_TEAM_CODE       := '         
     CASE  WHEN T2.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T2.LV1_PROD_RND_TEAM_CODE
           WHEN T2.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T2.LV2_PROD_RND_TEAM_CODE
         ELSE T2.LV3_PROD_RND_TEAM_CODE
         END AS PROD_RND_TEAM_CODE,';--202401版本新增SPART层级 加入9，10，11三个视角
									 --202405版本 数字能源加入COA层级 12视角 重量级团队CODE与11视角一致 无需修改
    V_SQL_PROD_RND_TEAM_CN_NAME    := '
       CASE WHEN T2.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T2.LV1_PROD_RD_TEAM_CN_NAME
            WHEN T2.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T2.LV2_PROD_RD_TEAM_CN_NAME
       ELSE T2.LV3_PROD_RD_TEAM_CN_NAME
       END AS PROD_RND_TEAM_CN_NAME,';--202401版本新增SPART层级 加入9，10，11三个视角
									  --202405版本 数字能源加入COA层级 12视角 重量级团队CODE与11视角一致 
	
	V_DIFF_COLUMN_SQL_NEW:= '
	/*COA层级收敛 */
  SELECT '||V_VERSION||'  AS VERSION_ID,		/*202401版本加入SPART层级*/
         PERIOD_YEAR,
         PERIOD_ID,
         VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
		 COA_CODE AS DMS_CODE,
		 COA_CN_NAME AS DMS_CN_NAME,
		 COA_CODE,
		 COA_CN_NAME,
         '''',
         '''',
         '''',
         '''',
         '''',
         '''',
		 '''',
		 '''',
         COA_CODE AS GROUP_CODE,
         COA_CN_NAME AS GROUP_CN_NAME,
         ''COA'' AS GROUP_LEVEL,
         SUM(ACTUAL_COST_AMT) AS ACTUAL_COST_AMT,
         PROD_RND_TEAM_CODE AS PARENT_CODE,  		 
		 PROD_RND_TEAM_CN_NAME AS PARENT_CN_NAME, 
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM '||V_BASE_AMT_TEMP||'
    WHERE VIEW_FLAG = 12
   GROUP BY PERIOD_YEAR,
            PERIOD_ID,
            VIEW_FLAG,
            PROD_RND_TEAM_CODE,
            PROD_RND_TEAM_CN_NAME,
			COA_CODE,
			COA_CN_NAME,
            CALIBER_FLAG,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME
	UNION ALL
	
	';
	
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN  --202407版本 新增IAS部分
  
    V_BASE_AMT_TEMP                := 'DM_FOC_IAS_BASE_DMS_TEMP';
    V_DECRYP_AMT_TEMP              := 'DM_FOC_IAS_DMS_DECRYP_TEMP';
	/*
    V_MID_TABLE                    := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MIDDLE_TOP_AMT_T';
    V_TARGET_TABLE                 := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_ACTUAL_COST_T';
    V_TOP_ITEM_INFO_T              := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_TOP_ITEM_INFO_T';
    V_MONTH_ITEM_AMT_T             := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MID_MONTH_ITEM_T';
	*/
	
	V_MID_TABLE                    := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MIDDLE_TOP_AMT_T_DMS';	--DMS修改
	V_TARGET_TABLE                 := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_ACTUAL_COST_T';
	V_TOP_ITEM_INFO_T              := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_TOP_ITEM_INFO_T';
	V_MONTH_ITEM_AMT_T             := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MID_MONTH_ITEM_T_DMS';
	
	--202407版本 IAS新增LV4层级
	V_LV4_PART						:= 'LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME,';		
	V_SQL_DIFF_COLUMN_PART			:= 'T2.LV4_PROD_RND_TEAM_CODE,T2.LV4_PROD_RD_TEAM_CN_NAME,';
	--V_PROD_DIFF_COLUMN_PART		:= '';
	V_JOIN_DIFF_COLUMN_CODE		:= 'AND NVL(T1.LV4_PROD_RND_TEAM_CODE,4) = NVL(T2.LV4_PROD_RND_TEAM_CODE,4)';
	
	V_DIMENSION_PARENT:=' PROD_RND_TEAM_CODE AS PARENT_CODE,
						  PROD_RND_TEAM_CN_NAME AS PARENT_CN_NAME,
	';	--202407版本 IAS新增视角12 新增LV4重量级团队
	
    V_SQL_PROD_RND_TEAM_CODE       := '         
     CASE  WHEN T2.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T2.LV1_PROD_RND_TEAM_CODE
           WHEN T2.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T2.LV2_PROD_RND_TEAM_CODE
		   WHEN T2.VIEW_FLAG = ''12'' THEN T2.LV4_PROD_RND_TEAM_CODE
         ELSE T2.LV3_PROD_RND_TEAM_CODE
         END AS PROD_RND_TEAM_CODE,';--202401版本新增SPART层级 加入9，10，11三个视角
									 --202407版本 IAS新增视角12 新增LV4重量级团队
									 
    V_SQL_PROD_RND_TEAM_CN_NAME    := '
       CASE WHEN T2.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T2.LV1_PROD_RD_TEAM_CN_NAME
            WHEN T2.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T2.LV2_PROD_RD_TEAM_CN_NAME
			WHEN T2.VIEW_FLAG = ''12'' THEN T2.LV4_PROD_RD_TEAM_CN_NAME
       ELSE T2.LV3_PROD_RD_TEAM_CN_NAME
       END AS PROD_RND_TEAM_CN_NAME,';--202401版本新增SPART层级 加入9，10，11三个视角
									  --202407版本 IAS新增视角12 新增LV4重量级团队
	
	V_DIFF_COLUMN_SQL_NEW:= '
	/*LV4层级收敛*/
  SELECT '||V_VERSION||'  AS VERSION_ID,		
         PERIOD_YEAR,
         PERIOD_ID,
         VIEW_FLAG,
         LV4_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
         LV4_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
		 '''',
		 '''',
         '''',
         '''',
         '''',
         '''',
         '''',
         '''',
		 '''',
		 '''',
         LV4_PROD_RND_TEAM_CODE AS GROUP_CODE,
         LV4_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
         ''LV4'' AS GROUP_LEVEL,
         SUM(ACTUAL_COST_AMT) AS ACTUAL_COST_AMT,
         LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,  		 
		 LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, 
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM '||V_BASE_AMT_TEMP||'
    WHERE VIEW_FLAG = 12
   GROUP BY PERIOD_YEAR,
            PERIOD_ID,
            VIEW_FLAG,
            CALIBER_FLAG,
            OVERSEA_FLAG,
			LV3_PROD_RND_TEAM_CODE,
			LV3_PROD_RD_TEAM_CN_NAME,
			LV4_PROD_RND_TEAM_CODE,
			LV4_PROD_RD_TEAM_CN_NAME,
			LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME
	UNION ALL
	
	';
	
  END IF;
	

  
  /*字段值定义*/
	V_LV3_PROD_RND_TEAM_CODE       := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME     := 'LV3_PROD_RD_TEAM_CN_NAME,';
    V_DIMENSION_CODE                := 'DIMENSION_CODE,';
    V_DIMENSION_CN_NAME             := 'DIMENSION_CN_NAME,';
    V_DIMENSION_EN_NAME             := 'DIMENSION_EN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE    := 'DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_EN_NAME := 'DIMENSION_SUBCATEGORY_EN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE     := 'DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME  := 'DIMENSION_SUB_DETAIL_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_EN_NAME	:= 'DIMENSION_SUB_DETAIL_EN_NAME,';
    V_DMS_CODE                      := 'DMS_CODE,';
    V_DMS_CN_NAME                   := 'DMS_CN_NAME,'; 
  
  
	--202401版本新增SPART层级
	V_SPART_CODE	:='SPART_CODE,'; 
	V_SPART_CN_NAME :='SPART_CN_NAME,';
	
	
    V_SQL_LV3_PROD_RND_TEAM_CODE                := 'T2.LV3_PROD_RND_TEAM_CODE,';
    V_SQL_LV3_PROD_RD_TEAM_CN_NAME             := 'T2.LV3_PROD_RD_TEAM_CN_NAME,';  
    V_SQL_DIMENSION_CODE                := 'T2.DIMENSION_CODE,';
    V_SQL_DIMENSION_CN_NAME             := 'T2.DIMENSION_CN_NAME,';
    V_SQL_DIMENSION_EN_NAME             := 'T2.DIMENSION_EN_NAME,';
    V_SQL_DIMENSION_SUBCATEGORY_CODE    := 'T2.DIMENSION_SUBCATEGORY_CODE,';
    V_SQL_DIMENSION_SUBCATEGORY_CN_NAME := 'T2.DIMENSION_SUBCATEGORY_CN_NAME,';
    V_SQL_DIMENSION_SUBCATEGORY_EN_NAME := 'T2.DIMENSION_SUBCATEGORY_EN_NAME,';
    V_SQL_DIMENSION_SUB_DETAIL_CODE     := 'T2.DIMENSION_SUB_DETAIL_CODE,';
    V_SQL_DIMENSION_SUB_DETAIL_CN_NAME  := 'T2.DIMENSION_SUB_DETAIL_CN_NAME,';
    V_SQL_DIMENSION_SUB_DETAIL_EN_NAME    := 'T2.DIMENSION_SUB_DETAIL_EN_NAME,';
	
	--202401版本新增SPART层级
	V_SQL_SPART_CODE	:='T2.SPART_CODE,'; 
	V_SQL_SPART_CN_NAME :='T2.SPART_CN_NAME,';
									  
    V_SQL_DMS_CODE   := '         
      CASE WHEN T2.VIEW_FLAG IN (''0'',''3'',''6'') THEN T2.DIMENSION_CODE
           WHEN T2.VIEW_FLAG IN (''1'',''4'',''7'') THEN T2.DIMENSION_SUBCATEGORY_CODE
		   WHEN T2.VIEW_FLAG IN (''2'',''5'',''8'') THEN T2.DIMENSION_SUB_DETAIL_CODE
		   ELSE T2.SPART_CODE
         END AS DMS_CODE,';--202401版本新增SPART层级 加入9，10，11三个视角
    V_SQL_DMS_CN_NAME   := '
      CASE WHEN T2.VIEW_FLAG IN (''0'',''3'',''6'') THEN T2.DIMENSION_CN_NAME
           WHEN T2.VIEW_FLAG IN (''1'',''4'',''7'') THEN T2.DIMENSION_SUBCATEGORY_CN_NAME
		   WHEN T2.VIEW_FLAG IN (''2'',''5'',''8'') THEN T2.DIMENSION_SUB_DETAIL_CN_NAME
		   ELSE T2.SPART_CN_NAME
       END AS DMS_CN_NAME,';   --202401版本新增SPART层级 加入9，10，11三个视角
       
       
    V_CEG_PARENT_CODE              := 'DMS_CODE AS PARENT_CODE,
									   DMS_CN_NAME AS PARENT_CN_NAME,';  --202403版本 新增PARENT_CN_NAME
    
    V_PROD_DMS_CODE                       := ' '''' AS PROD_DMS_CODE,';
    V_PROD_DMS_CN_NAME                    := ' '''' AS PROD_DMS_CN_NAME,';
    V_PROD_DIMENSION_CODE                 := ' '''' AS PROD_DIMENSION_CODE,';
    V_PROD_DIMENSION_CN_NAME              := ' '''' AS PROD_DIMENSION_CN_NAME,';
    V_PROD_DIMENSION_SUBCATEGORY_CODE     := ' '''' AS PROD_DIMENSION_SUBCATEGORY_CODE,';
    V_PROD_DIMENSION_SUBCATEGORY_CN_NAME  := ' '''' AS PROD_DIMENSION_SUBCATEGORY_CN_NAME,';
    V_PROD_DIMENSION_SUB_DETAIL_CODE      := ' '''' AS PROD_DIMENSION_SUB_DETAIL_CODE,';
    V_PROD_DIMENSION_SUB_DETAIL_CN_NAME   := ' '''' AS PROD_DIMENSION_SUB_DETAIL_CN_NAME,';
  
	--202401版本新增SPART层级 
	V_PROD_SPART_CODE    				:= ' '''' AS SPART_CODE,';
	V_PROD_SPART_CN_NAME 				:= ' '''' AS SPART_CN_NAME ,';
	
  /*条件定义*/
    V_JION_LV3_PROD_RND_TEAM_CODE := 'AND NVL(T1.LV3_PROD_RND_TEAM_CODE,3) = NVL(T2.LV3_PROD_RND_TEAM_CODE,3)';
    V_JOIN_DIMENSION_CODE := 'AND NVL(T1.DIMENSION_CODE,''D1'') = NVL(T2.DIMENSION_CODE,''D1'')';
    V_JOIN_DIMENSION_SUBCATEGORY_CODE := 'AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''D2'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''D2'')';
    V_JOIN_DIMENSION_SUB_DETAIL_CODE := 'AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''D3'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''D3'')';
  --202401版本新增SPART层级
	V_JOIN_SPART_CODE :='AND NVL(T1.SPART_CODE,''D4'') = NVL(T2.SPART_CODE,''D4'') ';
  
    V_LV2_VIEW     := ' WHERE VIEW_FLAG NOT IN(0,1,2,9)'; --202401版本新增SPART层级 视角10重量级团队打到LV2层级
    V_LV1_VIEW     := ' WHERE 1=1';
    V_LV0_VIEW     := ' WHERE 1=1';
 
  /*版本新加入逻辑定义*/
    V_SQL_NEW := V_DIFF_COLUMN_SQL_NEW||'
/*SPART层级收敛 */
  SELECT '||V_VERSION||'  AS VERSION_ID,		/*202401版本加入SPART层级*/
         PERIOD_YEAR,
         PERIOD_ID,
         VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
		 SPART_CODE AS DMS_CODE,
		 SPART_CN_NAME AS DMS_CN_NAME,
         '||V_COA_PART				--202405版本 数字能源来源新增COA层级 
		 ||V_DIMENSION_CODE
         ||V_DIMENSION_CN_NAME
         ||V_DIMENSION_SUBCATEGORY_CODE
         ||V_DIMENSION_SUBCATEGORY_CN_NAME
         ||V_DIMENSION_SUB_DETAIL_CODE
         ||V_DIMENSION_SUB_DETAIL_CN_NAME||'
		 SPART_CODE,
		 SPART_CN_NAME,
         SPART_CODE AS GROUP_CODE,
         SPART_CN_NAME AS GROUP_CN_NAME,
         ''SPART'' AS GROUP_LEVEL,
         SUM(ACTUAL_COST_AMT) AS ACTUAL_COST_AMT,
         DIMENSION_SUB_DETAIL_CODE AS PARENT_CODE,  		 
		 DIMENSION_SUB_DETAIL_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM '||V_BASE_AMT_TEMP||'
    WHERE  1=1
    AND TO_NUMBER(VIEW_FLAG) > 8	--202405版本 数字能源加入12视角 202407版本 IAS新增视角12
   GROUP BY PERIOD_YEAR,
            PERIOD_ID,
            VIEW_FLAG,
         '||V_COA_PART				--202405版本 数字能源来源新增COA层级  
		 ||V_DIMENSION_CODE
             ||V_DIMENSION_CN_NAME
             ||V_DIMENSION_SUBCATEGORY_CODE
             ||V_DIMENSION_SUBCATEGORY_CN_NAME
             ||V_DIMENSION_SUB_DETAIL_CODE
             ||V_DIMENSION_SUB_DETAIL_CN_NAME||' 
            PROD_RND_TEAM_CODE,
            PROD_RND_TEAM_CN_NAME,
			SPART_CODE,
			SPART_CN_NAME,
            CALIBER_FLAG,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME
UNION ALL

    
/*子类明细收敛 */
    
  SELECT '||V_VERSION||'  AS VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
         DIMENSION_SUB_DETAIL_CODE AS DMS_CODE     ,            
         DIMENSION_SUB_DETAIL_CN_NAME AS DMS_CN_NAME,
         '||V_COA_PART				--202405版本 数字能源来源新增COA层级 
		 ||V_DIMENSION_CODE
         ||V_DIMENSION_CN_NAME
         ||V_DIMENSION_SUBCATEGORY_CODE
         ||V_DIMENSION_SUBCATEGORY_CN_NAME
         ||V_DIMENSION_SUB_DETAIL_CODE
         ||V_DIMENSION_SUB_DETAIL_CN_NAME||' 
		 '''' AS SPART_CODE,
		 '''' AS SPART_CN_NAME,
         DIMENSION_SUB_DETAIL_CODE AS GROUP_CODE,
         DIMENSION_SUB_DETAIL_CN_NAME AS GROUP_CN_NAME,
         ''SUB_DETAIL'' AS GROUP_LEVEL,
         SUM(ACTUAL_COST_AMT) AS ACTUAL_COST_AMT,
         DIMENSION_SUBCATEGORY_CODE AS PARENT_CODE, 
	     DIMENSION_SUBCATEGORY_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME		 
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM '||V_BASE_AMT_TEMP||'
    WHERE  1=1
   AND VIEW_FLAG IN (2,5,8,9,10,11,12) --202405版本 数字能源加入12视角
										--202407版本 IAS新增视角12
   GROUP BY PERIOD_YEAR,
            PERIOD_ID,
            VIEW_FLAG,
         '||V_COA_PART				--202405版本 数字能源来源新增COA层级  
		 ||V_DIMENSION_CODE
             ||V_DIMENSION_CN_NAME
             ||V_DIMENSION_SUBCATEGORY_CODE
             ||V_DIMENSION_SUBCATEGORY_CN_NAME
             ||V_DIMENSION_SUB_DETAIL_CODE
             ||V_DIMENSION_SUB_DETAIL_CN_NAME||' 
            PROD_RND_TEAM_CODE,
            PROD_RND_TEAM_CN_NAME,
            CALIBER_FLAG,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME
UNION ALL

/*量纲子类收敛 */       
  SELECT '||V_VERSION||'  AS VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
         DIMENSION_SUBCATEGORY_CODE DMS_CODE,              
         DIMENSION_SUBCATEGORY_CN_NAME AS DMS_CN_NAME,
         '||V_COA_PART				--202405版本 数字能源来源新增COA层级 
		 ||V_DIMENSION_CODE
         ||V_DIMENSION_CN_NAME
         ||V_DIMENSION_SUBCATEGORY_CODE
         ||V_DIMENSION_SUBCATEGORY_CN_NAME||'
         '''',
         '''',
		 '''' AS SPART_CODE,
		 '''' AS SPART_CN_NAME,
         DIMENSION_SUBCATEGORY_CODE AS GROUP_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME AS GROUP_CN_NAME,
         ''SUBCATEGORY'' AS GROUP_LEVEL,
         SUM(ACTUAL_COST_AMT) AS ACTUAL_COST_AMT,
         DIMENSION_CODE AS PARENT_CODE,
		 DIMENSION_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME		 
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM '||V_BASE_AMT_TEMP||'
	WHERE  1=1
	AND VIEW_FLAG NOT IN (0,3,6)
   GROUP BY PERIOD_YEAR,
            PERIOD_ID,
            VIEW_FLAG,
         '||V_COA_PART				--202405版本 数字能源来源新增COA层级 
		 ||V_DIMENSION_CODE
             ||V_DIMENSION_CN_NAME
             ||V_DIMENSION_SUBCATEGORY_CODE
             ||V_DIMENSION_SUBCATEGORY_CN_NAME||' 
            PROD_RND_TEAM_CODE,
            PROD_RND_TEAM_CN_NAME,
            CALIBER_FLAG,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME
UNION ALL  

/*量纲收敛 */           
  SELECT '||V_VERSION||'  AS VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
         DIMENSION_CODE DMS_CODE,                       
         DIMENSION_CN_NAME AS DMS_CN_NAME,
         '||V_COA_PART				--202405版本 数字能源来源新增COA层级  
		 ||V_DIMENSION_CODE
         ||V_DIMENSION_CN_NAME||' 
         '''',
         '''',
         '''',
         '''',
		 '''' AS SPART_CODE,
		 '''' AS SPART_CN_NAME,
         DIMENSION_CODE AS GROUP_CODE,
         DIMENSION_CN_NAME AS GROUP_CN_NAME,
         ''DIMENSION'' AS GROUP_LEVEL,
         SUM(ACTUAL_COST_AMT) AS ACTUAL_COST_AMT,
         '||V_DIMENSION_PARENT||'	--量纲父级需判断
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM '||V_BASE_AMT_TEMP||'
   GROUP BY PERIOD_YEAR,
            PERIOD_ID,
            VIEW_FLAG,
         '||V_COA_PART				--202405版本 数字能源来源新增COA层级  
		 ||V_DIMENSION_CODE
             ||V_DIMENSION_CN_NAME||' 
            PROD_RND_TEAM_CODE,
            PROD_RND_TEAM_CN_NAME,
            CALIBER_FLAG,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME

UNION ALL   
          
/*LV3层级收敛 */  
 
SELECT ' || V_VERSION || '  AS VERSION_ID,
       PERIOD_YEAR,
       PERIOD_ID,
       VIEW_FLAG,
       LV3_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
       LV3_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
       '||V_PROD_DMS_CODE                
        ||V_PROD_DMS_CN_NAME
		||V_PROD_DIFF_COLUMN_PART||' 	--202405版本 数字能源来源新增COA层级 仅在数字能源来源时有效
		'''',
		'''',
		'''',
		'''',
		'''',
		'''',
		'''' AS SPART_CODE,
		'''' AS SPART_CN_NAME,
       LV3_PROD_RND_TEAM_CODE AS GROUP_CODE,
       LV3_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
       ''LV3'' AS GROUP_LEVEL,
       SUM(ACTUAL_COST_AMT) AS ACTUAL_COST_AMT,
       LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
	   LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,  --202403版本 新增PARENT_CN_NAME
       ''-1'' AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       ''-1'' AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       ''N'' AS DEL_FLAG,
        CALIBER_FLAG,
        OVERSEA_FLAG,
        LV0_PROD_LIST_CODE,
        LV0_PROD_LIST_CN_NAME
  FROM ' || V_BASE_AMT_TEMP || '
 WHERE  VIEW_FLAG IN (6,7,8,11,12)  --202401版本加入SPART层级,视角11重量级团队为LV3
		--202405版本 数字能源加入12视角 202407版本 IAS新增视角12
 GROUP BY PERIOD_YEAR,
          PERIOD_ID,
          VIEW_FLAG,
          LV2_PROD_RND_TEAM_CODE,
		  LV2_PROD_RD_TEAM_CN_NAME,
          LV3_PROD_RND_TEAM_CODE,
          LV3_PROD_RD_TEAM_CN_NAME,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
UNION ALL                        
';

  
  END IF;

  
  --2.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 2,
  F_CAL_LOG_DESC => '变量定义完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');    
 
/*前置加解密数据*/ 
V_EXCEPTION_FLAG := '3'; 

V_SQL := '
DROP TABLE IF EXISTS '||V_DECRYP_AMT_TEMP||';
CREATE TEMPORARY TABLE '||V_DECRYP_AMT_TEMP||'(
    PERIOD_YEAR BIGINT,
    PERIOD_ID BIGINT,
    LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
    LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
    LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
    LV3_PROD_RND_TEAM_CODE  VARCHAR(50),
	LV4_PROD_RND_TEAM_CODE  VARCHAR(50),		--202407版本 IAS新增LV4层级
	LV4_PROD_RD_TEAM_CN_NAME	VARCHAR(200),
    L1_NAME    VARCHAR(200),/*7月新增盈利颗粒度维度*/
    L2_NAME    VARCHAR(200),
    DIMENSION_CODE VARCHAR(50), 
    DIMENSION_SUBCATEGORY_CODE VARCHAR(50), 
    DIMENSION_SUB_DETAIL_CODE VARCHAR(50), 
	SPART_CODE  VARCHAR(500),					--202401版本加入SPART层级
	SPART_CN_NAME  VARCHAR(500),
	COA_CODE  VARCHAR(500),			--202405版本 数字能源加入COA层级
	COA_CN_NAME  VARCHAR(500),
    L3_CEG_CODE    VARCHAR(50),
    L4_CEG_CODE    VARCHAR(50),  
    CATEGORY_CODE VARCHAR(50),
    ITEM_CODE  VARCHAR(50),
    RMB_COST_AMT NUMERIC,
    VIEW_FLAG  VARCHAR(2),
    CALIBER_FLAG  VARCHAR(2), /*7月新增业务口径字段*/
    OVERSEA_FLAG  VARCHAR(2),  
    LV0_PROD_LIST_CODE VARCHAR(50)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY ROUNDROBIN;

INSERT INTO '||V_DECRYP_AMT_TEMP||'
  (VIEW_FLAG,
   PERIOD_YEAR,
   PERIOD_ID,
   LV0_PROD_RND_TEAM_CODE,
   LV1_PROD_RND_TEAM_CODE,
   LV2_PROD_RND_TEAM_CODE,
   '||V_LV3_PROD_RND_TEAM_CODE
   ||V_LV4_PART		--202407版本 IAS新增LV4层级
   ||V_L1_NAME
   ||V_L2_NAME
   ||V_COA_PART 	--202405版本 数字能源来源新增COA层级  
   ||V_DIMENSION_CODE
   ||V_DIMENSION_SUBCATEGORY_CODE
   ||V_DIMENSION_SUB_DETAIL_CODE
   ||V_SPART_CODE
   ||V_SPART_CN_NAME||'
   L3_CEG_CODE,
   L4_CEG_CODE,
   CATEGORY_CODE,
   ITEM_CODE,
   RMB_COST_AMT,
   LV0_PROD_LIST_CODE,
   OVERSEA_FLAG,
   CALIBER_FLAG)
  SELECT VIEW_FLAG,
         PERIOD_YEAR,
         PERIOD_ID,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,
         '||V_LV3_PROD_RND_TEAM_CODE
		  ||V_LV4_PART	--202407版本 IAS新增LV4层级
          ||V_L1_NAME
          ||V_L2_NAME
		  ||V_COA_PART 	--202405版本 数字能源来源新增COA层级
          ||V_DIMENSION_CODE
          ||V_DIMENSION_SUBCATEGORY_CODE
          ||V_DIMENSION_SUB_DETAIL_CODE
		  ||V_SPART_CODE
		  ||V_SPART_CN_NAME||'	
         L3_CEG_CODE,
         L4_CEG_CODE,
         CATEGORY_CODE,
         ITEM_CODE,
         --TO_NUMBER(GS_DECRYPT(RMB_COST_AMT, '''||V_KEYSTR||''',''aes128'',''cbc'',''sha256'')) AS RMB_COST_AMT,
		 RMB_COST_AMT,	--DMS修改
         LV0_PROD_LIST_CODE,
         OVERSEA_FLAG,
         CALIBER_FLAG
    FROM '||V_MONTH_ITEM_AMT_T||'
   WHERE PERIOD_YEAR BETWEEN (YEAR(CURRENT_DATE) - 2) AND YEAR(CURRENT_DATE)
   AND ONLY_ITEM_FLAG = ''N'' 
   AND REVIEW_ITEM_FLAG = 0
     ;';

 
EXECUTE IMMEDIATE V_SQL;

  --3.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 3,
  F_CAL_LOG_DESC => '数据解密完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');    

/*ITEM金额临时表创建*/
V_EXCEPTION_FLAG := '4'; 

V_SQL:='
DROP TABLE IF EXISTS '||V_BASE_AMT_TEMP||';
CREATE TEMPORARY TABLE '||V_BASE_AMT_TEMP||'(
    VERSION_ID BIGINT,
    PERIOD_YEAR BIGINT,
    PERIOD_ID BIGINT,
    LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
    LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
    LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
    LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    LV3_PROD_RND_TEAM_CODE    VARCHAR(50),  /*7月新增LV3重量级团队*/
    LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
	LV4_PROD_RND_TEAM_CODE		VARCHAR(50),  --202407版本 IAS新增LV4层级
	LV4_PROD_RD_TEAM_CN_NAME      VARCHAR(200),
    L1_NAME    VARCHAR(200),/*7月新增盈利颗粒度维度*/
    L2_NAME    VARCHAR(200),
    DIMENSION_CODE    VARCHAR(500), /*9月新增量纲颗粒度维度 */
    DIMENSION_CN_NAME    VARCHAR(2000) ,
    DIMENSION_EN_NAME    VARCHAR(2000) ,
    DIMENSION_SUBCATEGORY_CODE    VARCHAR(500) ,
    DIMENSION_SUBCATEGORY_CN_NAME    VARCHAR(2000) ,
    DIMENSION_SUBCATEGORY_EN_NAME    VARCHAR(2000) ,
    DIMENSION_SUB_DETAIL_CODE    VARCHAR(500) ,
    DIMENSION_SUB_DETAIL_CN_NAME    VARCHAR(2000) ,
    DIMENSION_SUB_DETAIL_EN_NAME    VARCHAR(2000) ,
	SPART_CODE  VARCHAR(500),					--202401版本加入SPART层级
	SPART_CN_NAME  VARCHAR(500),
	COA_CODE  VARCHAR(500),					--202405版本 数字能源加入COA层级
	COA_CN_NAME  VARCHAR(500),
    TOP_L3_CEG_CODE     VARCHAR(50), 
    TOP_L3_CEG_SHORT_CN_NAME    VARCHAR(200),
    TOP_L4_CEG_CODE    VARCHAR(50),
    TOP_L4_CEG_SHORT_CN_NAME VARCHAR(200),    
    PROD_RND_TEAM_CODE  VARCHAR(50),
    PROD_RND_TEAM_CN_NAME  VARCHAR(200),
    PROFITS_NAME  VARCHAR(200),  /*7月新增盈利颗粒度字段*/
    DMS_CODE    VARCHAR(50),     /*9月新增量纲颗粒度字段*/            
    DMS_CN_NAME      VARCHAR(200),
    GROUP_CODE VARCHAR(50),
    GROUP_CN_NAME VARCHAR(1000),
    GROUP_LEVEL VARCHAR(50),
    ACTUAL_COST_AMT NUMERIC,
    PARENT_CODE VARCHAR(50),
	PARENT_CN_NAME VARCHAR(200), --202403版本 新增PARENT_CN_NAME
    CREATED_BY VARCHAR(200),
    CREATION_DATE TIMESTAMP WITHOUT TIME ZONE,
    LAST_UPDATED_BY VARCHAR(200),
    LAST_UPDATE_DATE TIMESTAMP WITHOUT TIME ZONE,
    DEL_FLAG VARCHAR(2),
    VIEW_FLAG VARCHAR(2),
    CALIBER_FLAG VARCHAR(2), /*7月新增业务口径字段*/
    OVERSEA_FLAG VARCHAR(2),  
    LV0_PROD_LIST_CODE VARCHAR(50),  
    LV0_PROD_LIST_CN_NAME VARCHAR(200)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY ROUNDROBIN;';

EXECUTE IMMEDIATE V_SQL;

  --4.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 4,
  F_FORMULA_SQL_TXT => V_SQL,
  F_CAL_LOG_DESC => 'BASE金额临时表创建完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');  

/*TOP品类金额收敛*/
V_EXCEPTION_FLAG := '5'; 
V_SQL := '
/*取出规格品及所有维度字段*/
WITH TOP_ITEM AS
 (SELECT VIEW_FLAG,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         '||V_LV3_PROD_RND_TEAM_CODE||
         V_LV3_PROD_RD_TEAM_CN_NAME||
		 V_LV4_PART||			--202407版本 IAS新增LV4层级
         V_L1_NAME||
         V_L2_NAME||
         V_DIMENSION_CODE||
         V_DIMENSION_CN_NAME||
         V_DIMENSION_EN_NAME||
         V_DIMENSION_SUBCATEGORY_CODE||
         V_DIMENSION_SUBCATEGORY_CN_NAME||
         V_DIMENSION_SUBCATEGORY_EN_NAME||
         V_DIMENSION_SUB_DETAIL_CODE||
         V_DIMENSION_SUB_DETAIL_CN_NAME||
         V_DIMENSION_SUB_DETAIL_EN_NAME||
		 V_SPART_CODE||
		 V_SPART_CN_NAME||   --202401版本加入SPART层级      
		 V_COA_PART||'       --202405版本 数字能源来源新增COA层级 
         TOP_L3_CEG_CODE,
         TOP_L3_CEG_SHORT_CN_NAME,
         TOP_L4_CEG_CODE,
         TOP_L4_CEG_SHORT_CN_NAME,         
         TOP_CATEGORY_CODE,
         TOP_CATEGORY_CN_NAME,
         TOP_ITEM_CODE,
         TOP_ITEM_CN_NAME,
         CALIBER_FLAG, /*加入业务口径 --20230606*/
         OVERSEA_FLAG,  
         LV0_PROD_LIST_CODE  , 
         LV0_PROD_LIST_CN_NAME
    FROM '||V_TOP_ITEM_INFO_T||'
   WHERE VERSION_ID = '||V_VERSION||'
     AND IS_TOP_FLAG =''Y''/*取规格品*/
     AND DOUBLE_FLAG =''Y'') /*取全量规格品*/
     
/*品类层级实际数收敛*/
INSERT INTO '||V_BASE_AMT_TEMP||'
  (VERSION_ID,
   PERIOD_YEAR,
   PERIOD_ID,
   LV0_PROD_RND_TEAM_CODE,
   LV0_PROD_RD_TEAM_CN_NAME,
   LV1_PROD_RND_TEAM_CODE,
   LV1_PROD_RD_TEAM_CN_NAME,
   LV2_PROD_RND_TEAM_CODE,
   LV2_PROD_RD_TEAM_CN_NAME,
   '||V_LV3_PROD_RND_TEAM_CODE
   ||V_LV3_PROD_RD_TEAM_CN_NAME
   ||V_LV4_PART		--202407版本 IAS新增LV4层级
   ||V_L1_NAME
   ||V_L2_NAME
   ||V_COA_PART 	--202405版本 数字能源来源新增COA层级  
   ||V_DIMENSION_CODE
   ||V_DIMENSION_CN_NAME
   ||V_DIMENSION_EN_NAME
   ||V_DIMENSION_SUBCATEGORY_CODE
   ||V_DIMENSION_SUBCATEGORY_CN_NAME
   ||V_DIMENSION_SUBCATEGORY_EN_NAME
   ||V_DIMENSION_SUB_DETAIL_CODE
   ||V_DIMENSION_SUB_DETAIL_CN_NAME
   ||V_DIMENSION_SUB_DETAIL_EN_NAME||
   V_SPART_CODE||
   V_SPART_CN_NAME||'   --202401版本加入SPART层级 
   TOP_L3_CEG_CODE    ,
   TOP_L3_CEG_SHORT_CN_NAME    ,
   TOP_L4_CEG_CODE    ,
   TOP_L4_CEG_SHORT_CN_NAME    ,   
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
   '||V_PROFITS_NAME||
   V_DMS_CODE||
   V_DMS_CN_NAME||' 
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   ACTUAL_COST_AMT,
   PARENT_CODE,
   PARENT_CN_NAME , --202403版本 新增PARENT_CN_NAME
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   VIEW_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG ,
   LV0_PROD_LIST_CODE ,
   LV0_PROD_LIST_CN_NAME ) 

/*分视角后TOP品类每月实际发货额*/
SELECT '||V_VERSION||' AS VERSION_ID,
       T1.PERIOD_YEAR,
       T1.PERIOD_ID,
       T2.LV0_PROD_RND_TEAM_CODE,
       T2.LV0_PROD_RD_TEAM_CN_NAME,
       T2.LV1_PROD_RND_TEAM_CODE,
       T2.LV1_PROD_RD_TEAM_CN_NAME,
       T2.LV2_PROD_RND_TEAM_CODE,
       T2.LV2_PROD_RD_TEAM_CN_NAME,
      '||V_SQL_LV3_PROD_RND_TEAM_CODE
       ||V_SQL_LV3_PROD_RD_TEAM_CN_NAME
       ||V_SQL_L1_NAME
       ||V_SQL_L2_NAME
	   ||V_SQL_DIFF_COLUMN_PART  	--202405版本 数字能源来源新增COA层级  202407版本 IAS新增LV4层级
       ||V_SQL_DIMENSION_CODE
       ||V_SQL_DIMENSION_CN_NAME
       ||V_SQL_DIMENSION_EN_NAME
       ||V_SQL_DIMENSION_SUBCATEGORY_CODE
       ||V_SQL_DIMENSION_SUBCATEGORY_CN_NAME
       ||V_SQL_DIMENSION_SUBCATEGORY_EN_NAME
       ||V_SQL_DIMENSION_SUB_DETAIL_CODE
       ||V_SQL_DIMENSION_SUB_DETAIL_CN_NAME
       ||V_SQL_DIMENSION_SUB_DETAIL_EN_NAME
	   ||V_SQL_SPART_CODE
	   ||V_SQL_SPART_CN_NAME||'	--202401版本加入SPART层级
       T2.TOP_L3_CEG_CODE,
       T2.TOP_L3_CEG_SHORT_CN_NAME,
       T2.TOP_L4_CEG_CODE,
       T2.TOP_L4_CEG_SHORT_CN_NAME,       
       '||V_SQL_PROD_RND_TEAM_CODE||
         V_SQL_PROD_RND_TEAM_CN_NAME||
         V_SQL_PROFITS_NAME||
         V_SQL_DMS_CODE||
         V_SQL_DMS_CN_NAME||' 
       T2.TOP_CATEGORY_CODE AS GROUP_CODE,
       T2.TOP_CATEGORY_CN_NAME AS GROUP_CN_NAME,
       ''CATEGORY'' AS GROUP_LEVEL,
       SUM(T1.RMB_COST_AMT) AS ACTUAL_COST_AMT, /*解密后收敛*/
       T2.TOP_L4_CEG_CODE AS PARENT_CODE,
	   T2.TOP_L4_CEG_SHORT_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
       ''-1'' AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       ''-1'' AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       ''N'' AS DEL_FLAG,
       T2.VIEW_FLAG,
       T2.CALIBER_FLAG, /*202307新增收入口径*/
       T2.OVERSEA_FLAG , /*202309新增*/
       T2.LV0_PROD_LIST_CODE  , /*202309新增*/
       T2.LV0_PROD_LIST_CN_NAME 
  FROM '||V_DECRYP_AMT_TEMP||' T1
 INNER JOIN TOP_ITEM T2
    ON T1.VIEW_FLAG = T2.VIEW_FLAG
   AND NVL( T1.LV0_PROD_RND_TEAM_CODE,0)= /*赋空值字段后再关联*/
       NVL(T2.LV0_PROD_RND_TEAM_CODE, 0) 
   AND NVL(T1.LV1_PROD_RND_TEAM_CODE, 1) = /*赋空值字段后再关联*/
       NVL(T2.LV1_PROD_RND_TEAM_CODE, 1)
   AND NVL(T1.LV2_PROD_RND_TEAM_CODE, 2) =
       NVL(T2.LV2_PROD_RND_TEAM_CODE, 2)
       '||V_JION_LV3_PROD_RND_TEAM_CODE
       ||V_JOIN_L1_NAME
       ||V_JOIN_L2_NAME
       ||V_JOIN_DIMENSION_CODE
       ||V_JOIN_DIMENSION_SUBCATEGORY_CODE
       ||V_JOIN_DIMENSION_SUB_DETAIL_CODE
	   ||V_JOIN_SPART_CODE --202401版本加入SPART层级
	   ||V_JOIN_DIFF_COLUMN_CODE||' --202405版本 数字能源来源新增COA层级  202407版本 IAS新增LV4层级
   AND T1.L3_CEG_CODE = T2.TOP_L3_CEG_CODE
   AND T1.L4_CEG_CODE = T2.TOP_L4_CEG_CODE
   AND T1.CATEGORY_CODE = T2.TOP_CATEGORY_CODE
   AND T1.ITEM_CODE = T2.TOP_ITEM_CODE
   AND T1.CALIBER_FLAG = T2.CALIBER_FLAG /*加入业务口径 --202307版本*/
   AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG  
   AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE  
 GROUP BY T2.VIEW_FLAG,
          T1.PERIOD_YEAR,
          T1.PERIOD_ID,
          T2.LV0_PROD_RND_TEAM_CODE,
          T2.LV0_PROD_RD_TEAM_CN_NAME,
          T2.LV1_PROD_RND_TEAM_CODE,
          T2.LV1_PROD_RD_TEAM_CN_NAME,
          T2.LV2_PROD_RND_TEAM_CODE,
          T2.LV2_PROD_RD_TEAM_CN_NAME,
          '||V_SQL_LV3_PROD_RND_TEAM_CODE
          ||V_SQL_LV3_PROD_RD_TEAM_CN_NAME
          ||V_SQL_L1_NAME
          ||V_SQL_L2_NAME
          ||V_SQL_DIMENSION_CODE
          ||V_SQL_DIMENSION_CN_NAME
          ||V_SQL_DIMENSION_EN_NAME
          ||V_SQL_DIMENSION_SUBCATEGORY_CODE
          ||V_SQL_DIMENSION_SUBCATEGORY_CN_NAME
          ||V_SQL_DIMENSION_SUBCATEGORY_EN_NAME
          ||V_SQL_DIMENSION_SUB_DETAIL_CODE
          ||V_SQL_DIMENSION_SUB_DETAIL_CN_NAME
          ||V_SQL_DIMENSION_SUB_DETAIL_EN_NAME
		  ||V_SQL_SPART_CODE
		  ||V_SQL_SPART_CN_NAME  --202401版本加入SPART层级
		  ||V_SQL_DIFF_COLUMN_PART||'	--202405版本 数字能源来源新增COA层级  202407版本 IAS新增LV4层级
          T2.TOP_L3_CEG_CODE,
          T2.TOP_L3_CEG_SHORT_CN_NAME,
          T2.TOP_L4_CEG_CODE, /*202309新增*/
          T2.TOP_L4_CEG_SHORT_CN_NAME,          
          T2.TOP_CATEGORY_CODE,
          T2.TOP_CATEGORY_CN_NAME,
          T2.CALIBER_FLAG ,
          T2.OVERSEA_FLAG , /*202309新增*/
          T2.LV0_PROD_LIST_CODE  , /*202309新增*/
          T2.LV0_PROD_LIST_CN_NAME 
          ;
            '; 

EXECUTE IMMEDIATE V_SQL;
            
--5.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 5,
  F_FORMULA_SQL_TXT => V_SQL,
  F_CAL_LOG_DESC => 'TOP品类层级发货额收敛完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
DBMS_OUTPUT.PUT_LINE('TOP品类层级发货额收敛完成'); 
/*********************************************分视角后其他层级每月发货额收敛*********************************************/
V_EXCEPTION_FLAG := '6';   
V_SQL := 'TRUNCATE TABLE '||V_MID_TABLE||';
/* 中间表收敛插数 */ 
INSERT INTO '||V_MID_TABLE||'
  (VERSION_ID,
   PERIOD_YEAR,
   PERIOD_ID,
   VIEW_FLAG,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
   '||V_PROFITS_NAME
    ||V_L1_NAME
    ||V_L2_NAME
    ||V_DMS_CODE
    ||V_DMS_CN_NAME
	||V_COA_PART		--202405版本 数字能源来源新增COA层级  
    ||V_DIMENSION_CODE
    ||V_DIMENSION_CN_NAME
    ||V_DIMENSION_SUBCATEGORY_CODE
    ||V_DIMENSION_SUBCATEGORY_CN_NAME
    ||V_DIMENSION_SUB_DETAIL_CODE
    ||V_DIMENSION_SUB_DETAIL_CN_NAME
	||V_SPART_CODE
	||V_SPART_CN_NAME||' --202401版本加入SPART层级
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   ACTUAL_COST_AMT,
   PARENT_CODE,
   PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG , /*202309新增*/
   LV0_PROD_LIST_CODE  , /*202309新增*/
   LV0_PROD_LIST_CN_NAME 
   )
 
/* 品类层级 */  
  SELECT '||V_VERSION||'  AS VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         VIEW_FLAG,
         PROD_RND_TEAM_CODE,  
         PROD_RND_TEAM_CN_NAME,  
         '||V_PROFITS_NAME      
         ||V_L1_NAME            
         ||V_L2_NAME
         ||V_DMS_CODE                  
         ||V_DMS_CN_NAME
		 ||V_COA_PART		--202405版本 数字能源来源新增COA层级 
         ||V_DIMENSION_CODE
         ||V_DIMENSION_CN_NAME
         ||V_DIMENSION_SUBCATEGORY_CODE
         ||V_DIMENSION_SUBCATEGORY_CN_NAME
         ||V_DIMENSION_SUB_DETAIL_CODE
         ||V_DIMENSION_SUB_DETAIL_CN_NAME
		 ||V_SPART_CODE
		 ||V_SPART_CN_NAME||' --202401版本加入SPART层级
         GROUP_CODE,          
         GROUP_CN_NAME,
         ''CATEGORY'' AS GROUP_LEVEL,
         ACTUAL_COST_AMT,
         PARENT_CODE,
		 PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM '||V_BASE_AMT_TEMP||'

UNION ALL
/* MODL模块层级收敛 */  
  SELECT '||V_VERSION||'  AS VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
         '||V_PROFITS_NAME
         ||V_L1_NAME
         ||V_L2_NAME
         ||V_DMS_CODE                 
         ||V_DMS_CN_NAME
		 ||V_COA_PART		--202405版本 数字能源来源新增COA层级 
         ||V_DIMENSION_CODE
         ||V_DIMENSION_CN_NAME
         ||V_DIMENSION_SUBCATEGORY_CODE
         ||V_DIMENSION_SUBCATEGORY_CN_NAME
         ||V_DIMENSION_SUB_DETAIL_CODE
         ||V_DIMENSION_SUB_DETAIL_CN_NAME
		 ||V_SPART_CODE
		 ||V_SPART_CN_NAME||' --202401版本加入SPART层级
         TOP_L4_CEG_CODE AS GROUP_CODE,
         TOP_L4_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,
         ''MODL'' AS GROUP_LEVEL,
         SUM(ACTUAL_COST_AMT) AS ACTUAL_COST_AMT,
         TOP_L3_CEG_CODE AS PARENT_CODE, 
		 TOP_L3_CEG_SHORT_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME		 
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM '||V_BASE_AMT_TEMP||'
   GROUP BY VERSION_ID,
            PERIOD_YEAR,
            PERIOD_ID,
            VIEW_FLAG,
            '||V_PROFITS_NAME
             ||V_L1_NAME
             ||V_L2_NAME
             ||V_DMS_CODE                 
             ||V_DMS_CN_NAME
             ||V_DIMENSION_CODE
             ||V_DIMENSION_CN_NAME
             ||V_DIMENSION_SUBCATEGORY_CODE
             ||V_DIMENSION_SUBCATEGORY_CN_NAME
             ||V_DIMENSION_SUB_DETAIL_CODE
             ||V_DIMENSION_SUB_DETAIL_CN_NAME
			 ||V_SPART_CODE
			 ||V_SPART_CN_NAME --202401版本加入SPART层级
			 ||V_COA_PART||'		--202405版本 数字能源来源新增COA层级  
            PROD_RND_TEAM_CODE,
            PROD_RND_TEAM_CN_NAME,
            TOP_L3_CEG_CODE,
            TOP_L3_CEG_SHORT_CN_NAME,
            TOP_L4_CEG_CODE,
            TOP_L4_CEG_SHORT_CN_NAME,            
            CALIBER_FLAG,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME
 
  
UNION ALL
/* CEG专家团层级收敛 */ 
  SELECT '||V_VERSION||'  AS VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         VIEW_FLAG,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
         '||V_PROFITS_NAME
         ||V_L1_NAME
         ||V_L2_NAME
         ||V_DMS_CODE                 
         ||V_DMS_CN_NAME
		 ||V_COA_PART		--202405版本 数字能源来源新增COA层级 
         ||V_DIMENSION_CODE
         ||V_DIMENSION_CN_NAME
         ||V_DIMENSION_SUBCATEGORY_CODE
         ||V_DIMENSION_SUBCATEGORY_CN_NAME
         ||V_DIMENSION_SUB_DETAIL_CODE
         ||V_DIMENSION_SUB_DETAIL_CN_NAME
		 ||V_SPART_CODE
		 ||V_SPART_CN_NAME ||'--202401版本加入SPART层级
         TOP_L3_CEG_CODE AS GROUP_CODE,
         TOP_L3_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,
         ''CEG'' AS GROUP_LEVEL,
         SUM(ACTUAL_COST_AMT) AS ACTUAL_COST_AMT,
         '||V_CEG_PARENT_CODE||'   --202403版本 新增PARENT_CN_NAME
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM '||V_BASE_AMT_TEMP||'
   GROUP BY VERSION_ID,
            PERIOD_YEAR,
            PERIOD_ID,
            VIEW_FLAG,
            '||V_PROFITS_NAME
             ||V_L1_NAME
             ||V_L2_NAME
             ||V_DMS_CODE                 
             ||V_DMS_CN_NAME
             ||V_DIMENSION_CODE
             ||V_DIMENSION_CN_NAME
             ||V_DIMENSION_SUBCATEGORY_CODE
             ||V_DIMENSION_SUBCATEGORY_CN_NAME
             ||V_DIMENSION_SUB_DETAIL_CODE
             ||V_DIMENSION_SUB_DETAIL_CN_NAME
			 ||V_SPART_CODE
			 ||V_SPART_CN_NAME --202401版本加入SPART层级
			 ||V_COA_PART||'		--202405版本 数字能源加入COA层级
            PROD_RND_TEAM_CODE,
            PROD_RND_TEAM_CN_NAME,
            TOP_L3_CEG_CODE,
            TOP_L3_CEG_SHORT_CN_NAME,
            CALIBER_FLAG,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME
  UNION ALL 
  
  /*新增维度收敛*/
  '||V_SQL_NEW||'
  
/* LV2层级收敛 */  
  SELECT '||V_VERSION||'  AS VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         VIEW_FLAG,
         LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
         '||V_PROD_PROFITS_NAME
          ||V_PROD_L1_NAME
          ||V_PROD_L2_NAME
          ||V_PROD_DMS_CODE                 
          ||V_PROD_DMS_CN_NAME
		  ||V_PROD_DIFF_COLUMN_PART		--202405版本 数字能源加入COA层级
          ||V_PROD_DIMENSION_CODE
          ||V_PROD_DIMENSION_CN_NAME
          ||V_PROD_DIMENSION_SUBCATEGORY_CODE
          ||V_PROD_DIMENSION_SUBCATEGORY_CN_NAME
          ||V_PROD_DIMENSION_SUB_DETAIL_CODE
          ||V_PROD_DIMENSION_SUB_DETAIL_CN_NAME
		  ||V_PROD_SPART_CODE
		  ||V_PROD_SPART_CN_NAME||'  --202401版本加入SPART层级
         LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,
         LV2_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
         ''LV2'' AS GROUP_LEVEL,
         SUM(ACTUAL_COST_AMT) AS ACTUAL_COST_AMT,
         LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,
		 LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
    FROM '||V_BASE_AMT_TEMP||V_LV2_VIEW||' /*增加视角3--202307版本*/ 
   GROUP BY VERSION_ID,
            PERIOD_YEAR,
            PERIOD_ID,
            VIEW_FLAG,
            LV1_PROD_RND_TEAM_CODE,
			LV1_PROD_RD_TEAM_CN_NAME,
            LV2_PROD_RND_TEAM_CODE,
            LV2_PROD_RD_TEAM_CN_NAME,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
  UNION ALL
  
/* LV1层级收敛 */    
  SELECT '||V_VERSION||'  AS VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         VIEW_FLAG,
         LV1_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
         '||V_PROD_PROFITS_NAME
         ||V_PROD_L1_NAME
         ||V_PROD_L2_NAME
         ||V_PROD_DMS_CODE                 
         ||V_PROD_DMS_CN_NAME
		  ||V_PROD_DIFF_COLUMN_PART		--202405版本 数字能源加入COA层级
          ||V_PROD_DIMENSION_CODE
          ||V_PROD_DIMENSION_CN_NAME
          ||V_PROD_DIMENSION_SUBCATEGORY_CODE
          ||V_PROD_DIMENSION_SUBCATEGORY_CN_NAME
          ||V_PROD_DIMENSION_SUB_DETAIL_CODE
          ||V_PROD_DIMENSION_SUB_DETAIL_CN_NAME
		  ||V_PROD_SPART_CODE
		  ||V_PROD_SPART_CN_NAME||'  --202401版本加入SPART层级
         LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,
         LV1_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
         ''LV1'' AS GROUP_LEVEL,
         SUM(ACTUAL_COST_AMT) AS ACTUAL_COST_AMT,
         LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
		 LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME
    FROM '||V_BASE_AMT_TEMP||V_LV1_VIEW||'
   GROUP BY VERSION_ID,
            PERIOD_YEAR,
            PERIOD_ID,
            VIEW_FLAG,
            LV0_PROD_RND_TEAM_CODE,
			LV0_PROD_RD_TEAM_CN_NAME,
            LV1_PROD_RND_TEAM_CODE,
            LV1_PROD_RD_TEAM_CN_NAME,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME
  UNION ALL
  
/* LV0层级收敛 */    
  SELECT '||V_VERSION||'  AS VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         VIEW_FLAG,
         LV0_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME AS PROD_RND_TEAM_CN_NAME,
         '||V_PROD_PROFITS_NAME
         ||V_PROD_L1_NAME
         ||V_PROD_L2_NAME
         ||V_PROD_DMS_CODE                 
         ||V_PROD_DMS_CN_NAME
		  ||V_PROD_DIFF_COLUMN_PART		--202405版本 数字能源加入COA层级
          ||V_PROD_DIMENSION_CODE
          ||V_PROD_DIMENSION_CN_NAME
          ||V_PROD_DIMENSION_SUBCATEGORY_CODE
          ||V_PROD_DIMENSION_SUBCATEGORY_CN_NAME
          ||V_PROD_DIMENSION_SUB_DETAIL_CODE
          ||V_PROD_DIMENSION_SUB_DETAIL_CN_NAME
		  ||V_PROD_SPART_CODE
		  ||V_PROD_SPART_CN_NAME||'  --202401版本加入SPART层级
         LV0_PROD_RND_TEAM_CODE AS GROUP_CODE,
         LV0_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
         ''LV0'' AS GROUP_LEVEL,		--202405版本 统一LV0的层级值为LV0
         SUM(ACTUAL_COST_AMT) AS ACTUAL_COST_AMT,
         NULL AS PARENT_CODE,
		 NULL AS PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME
    FROM '||V_BASE_AMT_TEMP||V_LV0_VIEW||'
   GROUP BY PERIOD_YEAR,
            PERIOD_ID,
            VIEW_FLAG,
            LV0_PROD_RND_TEAM_CODE,
            LV0_PROD_RD_TEAM_CN_NAME,
           CALIBER_FLAG,
           OVERSEA_FLAG,
           LV0_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME ;';
		   

EXECUTE IMMEDIATE V_SQL;
            
--6.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 6,
  F_CAL_LOG_DESC => '所有层级金额卷积完成',
  F_FORMULA_SQL_TXT => V_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

/*删除同版本数据*/  
V_EXCEPTION_FLAG := '7'; 
V_SQL := 'DELETE FROM '||V_TARGET_TABLE||' WHERE VERSION_ID = '||V_VERSION||';';

EXECUTE IMMEDIATE V_SQL;

--7.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 7,
  F_FORMULA_SQL_TXT => V_SQL,--本段执行逻辑SQL
  F_CAL_LOG_DESC => '热力图表同版本数据删除完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

/*热力图结果表插数*/
V_EXCEPTION_FLAG := '8';  
V_SQL:='
INSERT INTO '||V_TARGET_TABLE||'
  (VERSION_ID,
   VIEW_FLAG,
   PERIOD_YEAR,
   PERIOD_ID,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
  '||V_PROFITS_NAME
   ||V_L1_NAME
   ||V_L2_NAME
   ||V_DMS_CODE                 
   ||V_DMS_CN_NAME
   ||V_COA_PART		--202405版本 数字能源来源新增COA层级 
   ||V_DIMENSION_CODE
   ||V_DIMENSION_CN_NAME
   ||V_DIMENSION_SUBCATEGORY_CODE
   ||V_DIMENSION_SUBCATEGORY_CN_NAME
   ||V_DIMENSION_SUB_DETAIL_CODE
   ||V_DIMENSION_SUB_DETAIL_CN_NAME
   ||V_SPART_CODE
   ||V_SPART_CN_NAME||'
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   ACTUAL_COST_AMT,
   PARENT_CODE,
   PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG,
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME)
  SELECT --'||V_SEQUENCE||' AS ID,
         '||V_VERSION||',
         VIEW_FLAG,
         PERIOD_YEAR,
         PERIOD_ID,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
         '||V_PROFITS_NAME
          ||V_L1_NAME
          ||V_L2_NAME
          ||V_DMS_CODE                 
          ||V_DMS_CN_NAME
		  ||V_COA_PART		--202405版本 数字能源来源新增COA层级  
          ||V_DIMENSION_CODE
          ||V_DIMENSION_CN_NAME
          ||V_DIMENSION_SUBCATEGORY_CODE
          ||V_DIMENSION_SUBCATEGORY_CN_NAME
          ||V_DIMENSION_SUB_DETAIL_CODE
          ||V_DIMENSION_SUB_DETAIL_CN_NAME
		  ||V_SPART_CODE
		  ||V_SPART_CN_NAME||'
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ACTUAL_COST_AMT,
         PARENT_CODE,
		 PARENT_CN_NAME, --202403版本 新增PARENT_CN_NAME
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME 
    FROM '||V_MID_TABLE||';';
   
EXECUTE IMMEDIATE V_SQL;

--8.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 8,
  F_FORMULA_SQL_TXT => V_SQL,--本段执行逻辑SQL
  F_CAL_LOG_DESC => '热力图表插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
    
RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '2001';
   
     PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_STEP_NUM => V_EXCEPTION_FLAG,
    F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );
 END; 
$$
/

