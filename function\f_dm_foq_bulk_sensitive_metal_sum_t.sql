-- Name: f_dm_foq_bulk_sensitive_metal_sum_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foq_bulk_sensitive_metal_sum_t(p_version_id integer DEFAULT NULL::integer, p_benchmark_version_code character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：2024-11-20
创建人  ：朱雅欣
背景描述：大宗敏感类金属量价汇总表,调用该函数将相对应的数据生成导入到目标表中
参数描述：参数(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_foq_bulk_sensitive_metal_sum_t(p_version_id,p_benchmark_version_code)

*/


DECLARE
	v_sp_name varchar(200) := 'fin_dm_opt_foi.f_dm_foq_bulk_sensitive_metal_sum_t';
	v_tbl_name varchar(200) := 'fin_dm_opt_foi.dm_foq_bulk_sensitive_metal_sum_t';
	v_dml_row_count number default 0 ;
	v_max_version_code varchar(30);
	v_metal_cn_name varchar(30);
	v_taxes number := 1.13 ;


BEGIN
	x_success_flag := '1';                          --1表示成功
	

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,               --sp名称
        p_log_para_list => '',                    --参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '大宗敏感类金属量价汇总表'||v_tbl_name||'：开始运行',--日志描述
        p_log_formula_sql_txt => null,            --错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null                      --错误编码
      ) ;
						
     --从 大宗敏感类金属量价汇总表 中，删除版本信息表中执行失败的 version_id 数据
        delete from fin_dm_opt_foi.dm_foq_bulk_sensitive_metal_sum_t t1 
		 where t1.version_id in (select distinct t2.version_id from fin_dm_opt_foi.dm_foq_version_info_t t2 where  t2.step='2001' and t2.module_type = '大宗敏感类');		 		 
		 

		
		-- 如果 p_benchmark_version_code 为空，则取 补录表版本状态信息表 的最大版本编码，如果 p_benchmark_version_code 不为空，则取传入的 p_benchmark_version_code   
		if (p_benchmark_version_code is null or p_benchmark_version_code = '') then
        select max(version_code) as max_version_code into v_max_version_code 
		from fin_dm_opt_foi.apd_foq_version_code_status_info_t 
		where upper(status)='FINAL'
		and upper(source_name) = 'APD_FOQ_COMMODITY_BENCHMARK_PRICE_T';
		else 
		select  p_benchmark_version_code into v_max_version_code ;
		end if
          ; 
		  	  
		 
		 -- 创建 生产采购物料下单量 临时表
		 drop table if exists bulk_commodity_tmp;
		   create temporary table bulk_commodity_tmp
		   as 
		   select t1.item_subtype_code     as category_code    /*品类编码*/
				 ,t1.item_subtype_cn_name  as category_name    /*品类名称*/
				 ,t2.l3_ceg_code                               /*专家团编码*/
				 ,t2.l3_ceg_cn_name                            /*专家团中文名称*/
				 ,t2.l4_ceg_code                               /*模块编码*/
				 ,t2.l4_ceg_cn_name                            /*模块中文名称*/
				 ,t1.currency_code as currency                 /*币种*/
				 ,sum(t1.actual_order_copper_weight_ton) as quantity/*铜下单量*/
				 ,substr(t1.po_shipment_create_date_ymd,1,4)||substr(t1.po_shipment_create_date_ymd,6,2)||substr(t1.po_shipment_create_date_ymd,9,2) as data_date /*采购订单发运行创建时间_年月日*/
           from fin_dm_opt_foi.dwl_pro_pp_po_bulk_commodity_i t1
		   left join dmdim.dm_dim_ceg_d  t2 
		   on t1.ceg_key = t2.ceg_key
		   where  instr(t1.supplier_site_code,'转售') = 0
		   and t1.item_subtype_code = '2503A'
		   AND t1.currency_code = 'CNY'
		   and UPPER(t1.business_type) = 'PP'
		     AND t2.l2_ceg_code = '12229'
		   AND t2.l3_ceg_code = '16349'
		   group by t1.item_subtype_code       
				 ,t1.item_subtype_cn_name    
				 ,t2.l3_ceg_code         
				 ,t2.l3_ceg_cn_name      
				 ,t2.l4_ceg_code         
				 ,t2.l4_ceg_cn_name      
				 ,t1.currency_code    
                 ,t1.po_shipment_create_date_ymd				 
		   ;


		  -- 创建 大宗商品基准价（补录）表 临时表 
        drop table if exists benchmark_price_tmp;
	    create temporary table benchmark_price_tmp
	    as 
	    select version_code
              ,metal_cn_name
              ,data_date
              ,benchmark_price/v_taxes as benchmark_price
	    from  fin_dm_opt_foi.apd_foq_commodity_benchmark_price_t
	    where version_code = v_max_version_code
           ;
	   	   
	    -- 创建 大宗商品日价格数据信息 临时表 
        drop table if exists market_price_tmp;
	    create temporary table market_price_tmp
	    as 
		select substr(data_date,1,4)||substr(data_date,6,2)||substr(data_date,9,2) as data_date
	    ,index_code
	    ,index_name
	    ,unit_code
	    ,unit_name
	    ,data_value::numeric/v_taxes as data_value
		from (
	    select data_date
	    ,index_code
	    ,index_name
	    ,unit_code
	    ,unit_name
	    ,data_value
		,row_number() over(partition by data_date, index_code, index_name, frequency_code, frequency_name, unit_code, unit_name order by dwi_last_modified_date desc) as row_num 
	    from  fin_dm_opt_foi.dwb_ed_indi_dime_detail_infor_gs_i
	    where 1=1
	    and index_code = 'FU00015615' /*取铜的指标编码*/
	    and frequency_code = 'FQ-0000000002' /*频度取日度*/
	    and substr(data_date,1,4) >= 2023
		and upper(data_value) <>'SNULL'
		) t 
		where row_num = 1
          ;
		  		 
		  
		  -- 创建 基准价补齐表 临时表
         drop table if exists apd_benchmark_price_tmp;
         create temporary table apd_benchmark_price_tmp(
         version_code        varchar(100) /*版本编码*/
        ,metal_cn_name       varchar(600) /*金属类别*/
        ,data_date           varchar(100) /*时间*/
        ,benchmark_price     numeric      /*基准价*/
        ,apd_benchmark_flag  varchar(100) /*基准价补齐标识（Y 是、N 否），优先往前补齐*/
         )on commit preserve rows distribute by hash(data_date)
         ;
		 
		 
		 -- 创建 市场价补齐表 临时表 
        drop table if exists apd_market_price_tmp;
	    create temporary table apd_market_price_tmp
	   ( data_date           varchar(100) /*数据日期*/
	    ,index_code          varchar(600) /*指标编码*/
	    ,index_name          varchar(600) /*指标名称*/
	    ,unit_code           varchar(600) /*单位编码*/
	    ,unit_name           varchar(600) /*单位名称*/
	    ,data_value          numeric      /*数据值*/
		,apd_market_flag     varchar(100) /*市场价补齐标识（Y 是、N 否），优先往前补齐*/
	    )on commit preserve rows distribute by hash(data_date)
          ;
         	  -- 从2023年至今的所有日期，日粒度
			  drop table if exists time_tmp;
			   create temporary table time_tmp 
			   as 
              select to_char(generate_series,'YYYYMMDD')  as data_date
              from generate_series('2023-01-01':: timestamp ,CURRENT_DATE::timestamp, '1 day')
			  where  to_char(generate_series,'YYYYMMDD') <=(select max(data_date) from bulk_commodity_tmp)
            ;
			  
			  -- 市场价的所有维度字段
			    drop table if exists market_price_tmp1;
			  create temporary table market_price_tmp1 
			  as 
				select distinct index_code      /*指标编码*/
	                  ,index_name      /*指标名称*/
	                  ,unit_code       /*单位编码*/
	                  ,unit_name       /*单位名称*/
				from  market_price_tmp
				;
				
			  	   -- 从基准价表中取金属名称
	           select distinct metal_cn_name into v_metal_cn_name
	           from benchmark_price_tmp
	           ;    
	   	   
			  -- 基准价缺失数据补齐
			 insert into  apd_benchmark_price_tmp(
			   version_code      
			  ,metal_cn_name     
			  ,data_date         
			  ,benchmark_price   
			  ,apd_benchmark_flag			  			  			  
			 )
			  --基准价补录表中的维度（除去时间和金额）
	      with benchmark_price_tmp1 as(
			  select distinct metal_cn_name
			  from benchmark_price_tmp
			  ),			  
		  apd_benchmark_front_tmp as (
		    -- 若存在当日无数据，应使用历史铜的价格数据进行补齐(先向前补齐)
			  SELECT  v_max_version_code as version_code /*版本编码*/
                     ,t2.metal_cn_name                      /*类别*/
                     ,t1.data_date                          /*时间*/
                     ,t3.benchmark_price                    /*基准价*/
                     ,'Y' as apd_benchmark_flag          /*基准价补齐标识（Y 是、N 否），优先往前补齐*/
			    from time_tmp t1
				left join benchmark_price_tmp1 t2
				on 1=1
				left join benchmark_price_tmp t3
				on  t2.metal_cn_name = t3.metal_cn_name
				where t3.data_date  = ( select max(t3.data_date)
				                         from benchmark_price_tmp t3
										 where t3.data_date < t1.data_date
										  and t2.metal_cn_name = t3.metal_cn_name
										  )
				and t1.data_date not in (select distinct t3.data_date
				                          from benchmark_price_tmp t3)
			    union all
				-- 非补齐的数据
				SELECT  version_code            /*版本编码*/
                     ,metal_cn_name             /*类别*/
                     ,data_date                 /*时间*/
                     ,benchmark_price           /*基准价*/
                     ,'N' as apd_benchmark_flag /*基准价补齐标识（Y 是、N 否），优先往前补齐*/
			    from benchmark_price_tmp 							
		          )
				  -- 再向后补齐			  
			 SELECT  v_max_version_code as version_code /*版本编码*/
                     ,t2.metal_cn_name                     /*类别*/
                     ,t1.data_date                         /*时间*/
                     ,t3.benchmark_price                   /*基准价*/
                     ,'Y' as apd_benchmark_flag         /*基准价补齐标识（Y 是、N 否），优先往前补齐*/
			    from time_tmp t1
				left join benchmark_price_tmp1 t2
				on 1=1
				left join apd_benchmark_front_tmp t3
				on t2.metal_cn_name = t3.metal_cn_name
				where t3.data_date  = (select min(t3.data_date)
				                         from apd_benchmark_front_tmp t3
										 where t3.data_date > t1.data_date
										  and t2.metal_cn_name = t3.metal_cn_name
										  )
				and t1.data_date not in (select distinct t3.data_date
				                          from apd_benchmark_front_tmp t3)
				UNION ALL
				-- 非补齐的数据和向前补齐的数据
				 SELECT  version_code    /*版本编码*/
                     ,metal_cn_name      /*类别*/
                     ,data_date          /*时间*/
                     ,benchmark_price    /*基准价*/
                     ,apd_benchmark_flag /*基准价补齐标识（Y 是、N 否），优先往前补齐*/
			    from apd_benchmark_front_tmp 				  
		          ;
				  
				   v_dml_row_count := sql%rowcount;	-- 收集数据量
		  -- 开始记录日志
  perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,               --sp名称
        p_log_para_list => '',                    --参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '基准价数据补齐完毕，version_id为'||p_version_id||',基准价补录表版本为'||v_max_version_code||',数据量'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,            --错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null                      --错误编码
      ) ;
		  
		  
		        -- 市场价缺失数据补齐
				  -- 若存在当日无数据，应使用历史铜的价格数据进行补齐(先向前补齐)
				insert into  apd_market_price_tmp
	                       ( data_date       /*数据日期*/
	                        ,index_code      /*指标编码*/
	                        ,index_name      /*指标名称*/
	                        ,unit_code       /*单位编码*/
	                        ,unit_name       /*单位名称*/
	                        ,data_value      /*数据值*/
		                    ,apd_market_flag /*市场价补齐标识（Y 是、N 否），优先往前补齐*/
	                        )
				  select t1.data_date           /*数据日期*/
	                    ,t2.index_code          /*指标编码*/
	                    ,t2.index_name          /*指标名称*/
	                    ,t2.unit_code           /*单位编码*/
	                    ,t2.unit_name           /*单位名称*/
	                    ,t3.data_value          /*数据值*/
		                ,'Y' as apd_market_flag /*市场价补齐标识（Y 是、N 否），优先往前补齐*/
				  from time_tmp               t1
				  left join market_price_tmp1 t2
				  on 1=1
				  left join market_price_tmp  t3
				   on t2.index_code     = t3.index_code
				  and t2.unit_code      = t3.unit_code
				  where t3.data_date = (select  max(t3.data_date) as data_date
				                          from  market_price_tmp t3
										  where t3.data_date < t1.data_date
										    and t2.index_code     = t3.index_code
				                            and t2.unit_code      = t3.unit_code 
											)
				  and t1.data_date not in (select distinct t3.data_date
				                          from market_price_tmp t3)
				  union all
				  -- 非补齐的数据
				  select data_date              /*数据日期*/
	                    ,index_code             /*指标编码*/
	                    ,index_name             /*指标名称*/
	                    ,unit_code              /*单位编码*/
	                    ,unit_name              /*单位名称*/
	                    ,data_value             /*数据值*/
		                ,'N' as apd_market_flag /*市场价补齐标识（Y 是、N 否），优先往前补齐*/
				   from market_price_tmp
				;
				
				
				-- 再向后补齐
				insert into  apd_market_price_tmp
	                       ( data_date       /*数据日期*/
	                        ,index_code      /*指标编码*/
	                        ,index_name      /*指标名称*/
	                        ,unit_code       /*单位编码*/
	                        ,unit_name       /*单位名称*/
	                        ,data_value      /*数据值*/
		                    ,apd_market_flag /*市场价补齐标识（Y 是、N 否），优先往前补齐*/
	                        )
				   select t1.data_date           /*数据日期*/
	                    ,t2.index_code          /*指标编码*/
	                    ,t2.index_name          /*指标名称*/
	                    ,t2.unit_code           /*单位编码*/
	                    ,t2.unit_name           /*单位名称*/
	                    ,t3.data_value          /*数据值*/
		                ,'Y' as apd_market_flag /*市场价补齐标识（Y 是、N 否），优先往前补齐*/
				  from time_tmp               t1
				  left join market_price_tmp1 t2
				  on 1=1
				  left join apd_market_price_tmp  t3
				   on t2.index_code     = t3.index_code
				  and t2.unit_code      = t3.unit_code
				  where t3.data_date = (select  min(t3.data_date) as data_date
				                          from  apd_market_price_tmp t3
										  where t3.data_date > t1.data_date
										    and t2.index_code     = t3.index_code
				                            and t2.unit_code      = t3.unit_code 
											)
				  and t1.data_date not in (select distinct t3.data_date
				                          from apd_market_price_tmp t3)				 
				   ;
				   
				   
				   v_dml_row_count := sql%rowcount;	-- 收集数据量
		  -- 开始记录日志
  perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,               --sp名称
        p_log_para_list => '',                    --参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '市场价数据补齐完毕，数据量为'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,            --错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null                      --错误编码
      ) ;
				   
				   delete from fin_dm_opt_foi.dm_foq_bulk_sensitive_metal_sum_t where  version_id = p_version_id;
		 
		    -- 下单量表为主表，关联 华为基准价  和 市场价
               insert into fin_dm_opt_foi.dm_foq_bulk_sensitive_metal_sum_t(
			   version_id
              ,period_id
              ,data_date
              ,category_code
              ,category_name
              ,l3_ceg_code
              ,l3_ceg_cn_name
              ,l4_ceg_code
              ,l4_ceg_cn_name
              ,currency
              ,quantity
              ,index_code
              ,index_name
              ,metal_cn_name
              ,unit_code
              ,unit_name
              ,market_price
              ,apd_market_flag
              ,benchmark_price
              ,apd_benchmark_flag
              ,remark
              ,created_by
              ,creation_date
              ,last_updated_by
              ,last_update_date
              ,del_flag
               )
               select p_version_id      as version_id
                     ,substr(t1.data_date,1,6) as period_id
                     ,t1.data_date
                     ,t1.category_code
                     ,t1.category_name
                     ,t1.l3_ceg_code
                     ,t1.l3_ceg_cn_name
                     ,t1.l4_ceg_code
                     ,t1.l4_ceg_cn_name
                     ,t1.currency
                     ,t1.quantity
                     ,t3.index_code
                     ,t3.index_name
                     ,v_metal_cn_name as metal_cn_name
                     ,t3.unit_code
                     ,t3.unit_name
                     ,t3.data_value as market_price
                     ,t3.apd_market_flag
                     ,t2.benchmark_price
                     ,t2.apd_benchmark_flag
					 ,'' as remark
 	                 , -1 as created_by
 	                 , current_timestamp as creation_date
 	                 , -1 as last_updated_by
 	                 , current_timestamp as last_update_date
 	                 , 'N' as del_flag
			   from bulk_commodity_tmp        t1
			   left join apd_benchmark_price_tmp   t2
			   on t1.data_date = t2.data_date
			   left join apd_market_price_tmp t3
			   on t1.data_date = t3.data_date
			   ;
			   
			   -- 删掉市场价与基准价都没有的行
			   delete from fin_dm_opt_foi.dm_foq_bulk_sensitive_metal_sum_t where  market_price is null and benchmark_price is null;
			   

                  v_dml_row_count := sql%rowcount;	-- 收集数据量
		  -- 开始记录日志
            perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
                  p_log_version_id => null,                 --版本
                  p_log_sp_name => v_sp_name,               --sp名称
                  p_log_para_list => '',                    --参数
                  p_log_step_num  => 4,
                  p_log_cal_log_desc => '将数据插入结果表，数据量为'||v_dml_row_count,--日志描述
                  p_log_formula_sql_txt => null,            --错误信息
                  p_log_row_count => v_dml_row_count,
                  p_log_errbuf => null                      --错误编码
                ) ;

    --收集统计信息
    analyse fin_dm_opt_foi.dm_foq_bulk_sensitive_metal_sum_t;

exception
  	when others then

      perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                     --版本
        p_log_sp_name => v_sp_name,                   --sp名称
        p_log_para_list => '',                        --参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,             --错误信息
		p_log_row_count => null,
		p_log_errbuf => sqlstate                      --错误编码
      ) ;
	x_success_flag := '2001';	                      --2001表示失败



 end;
 $$
/

