-- Name: f_dm_fcst_price_annl_status; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_annl_status(f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年12月版本

  背景描述：根据年累计均本数据的补齐标识，判断SPART层级的补齐情况，再按照状态码处理逻辑对编码进行处理；往上层级的状态码逻辑根据SPART层级的状态码进行处理
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FCST_PRICE_ANNL_STATUS('');
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_PRICE_ANNL_STATUS'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_YEAR INT ;
  V_LAST_YEAR_FLAG VARCHAR(200);
  V_YEAR_FLAG VARCHAR(200);
  V_YEAR_APPEND VARCHAR(200);
  V_SQL TEXT;
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
 if MONTH(CURRENT_TIMESTAMP) = 1
  then V_YEAR = YEAR(NOW()) -1;
  ELSE
  V_YEAR = YEAR(NOW());
  END IF ;
   
  -- 取出年度版本号
  IF F_VERSION_ID IS NULL THEN 
    SELECT VERSION_ID INTO V_VERSION_ID   -- 年度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到最新年度版本号为：'||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 删除结果表数据
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_STATUS_T WHERE VERSION_ID = '||V_VERSION_ID;
  EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FCST_PRICE_MID_BASE_STATUS_T';

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除：DM_FCST_PRICE_ANNL_STATUS_T，版本号为：'||V_VERSION_ID||'的表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
    -- 创建临时表
     DROP TABLE IF EXISTS FCST_PRICE_SPART_TMP;
     CREATE TEMPORARY TABLE FCST_PRICE_SPART_TMP (
         LV0_PROD_LIST_CODE                 VARCHAR(50),
         LV0_PROD_LIST_CN_NAME              VARCHAR(200),
         LV1_PROD_LIST_CODE                 VARCHAR(50),
         LV1_PROD_LIST_CN_NAME              VARCHAR(200),
         LV2_PROD_LIST_CODE                 VARCHAR(50),
         LV2_PROD_LIST_CN_NAME              VARCHAR(200),
         LV3_PROD_LIST_CODE                 VARCHAR(50),
         LV3_PROD_LIST_CN_NAME              VARCHAR(200),
         LV4_PROD_LIST_CODE                 VARCHAR(50),
         LV4_PROD_LIST_CN_NAME              VARCHAR(200),
         SPART_CODE                         VARCHAR(50),
         SPART_CN_NAME                      VARCHAR(2000),
         SIGN_TOP_CUST_CATEGORY_CODE        VARCHAR(50),
         SIGN_TOP_CUST_CATEGORY_CN_NAME     VARCHAR(500),
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME   VARCHAR(500),
         LAST_THREE_YEAR_FLAG               VARCHAR(50),
         LAST_THREE_APPEND_YEAR             VARCHAR(50),
         LAST_TWO_YEAR_FLAG                 VARCHAR(50),
         LAST_TWO_APPEND_YEAR               VARCHAR(50),
         LAST_YEAR_FLAG                     VARCHAR(50),
         LAST_APPEND_YEAR                   VARCHAR(50),
         CURRENT_YEAR_FLAG                  VARCHAR(50),
         CURRENT_APPEND_YEAR                VARCHAR(50),
         VIEW_FLAG                          VARCHAR(50),
         REGION_CODE                        VARCHAR(50),
         REGION_CN_NAME                     VARCHAR(200),
         REPOFFICE_CODE                     VARCHAR(50),
         REPOFFICE_CN_NAME                  VARCHAR(200),
         BG_CODE                            VARCHAR(50),
         BG_CN_NAME                         VARCHAR(200),
         OVERSEA_FLAG                       VARCHAR(10)
     )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(SPART_CODE);
 
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '最细粒度层级缺失情况临时表创建完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  ------------------------------------------------------------------------ SPART层级状态码逻辑 -----------------------------------------------------------------------------------------
  -- 最细粒度层级缺失数据情况插入临时表
     INSERT INTO FCST_PRICE_SPART_TMP(            
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME,
            LV1_PROD_LIST_CODE,
            LV1_PROD_LIST_CN_NAME,
            LV2_PROD_LIST_CODE,
            LV2_PROD_LIST_CN_NAME,
            LV3_PROD_LIST_CODE,
            LV3_PROD_LIST_CN_NAME,
            LV4_PROD_LIST_CODE,
            LV4_PROD_LIST_CN_NAME,
            SPART_CODE,                   
            SPART_CN_NAME,
            SIGN_TOP_CUST_CATEGORY_CODE,
            SIGN_TOP_CUST_CATEGORY_CN_NAME,
            SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
            LAST_THREE_YEAR_FLAG,
            LAST_THREE_APPEND_YEAR,
            LAST_TWO_YEAR_FLAG,    
            LAST_TWO_APPEND_YEAR,  
            LAST_YEAR_FLAG,        
            LAST_APPEND_YEAR,      
            CURRENT_YEAR_FLAG,     
            CURRENT_APPEND_YEAR,   
            VIEW_FLAG,             
            REGION_CODE,           
            REGION_CN_NAME,        
            REPOFFICE_CODE,        
            REPOFFICE_CN_NAME,     
            BG_CODE,               
            BG_CN_NAME,            
            OVERSEA_FLAG
                 )
     SELECT LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME,
            LV1_PROD_LIST_CODE,
            LV1_PROD_LIST_CN_NAME,
            LV2_PROD_LIST_CODE,
            LV2_PROD_LIST_CN_NAME,
            LV3_PROD_LIST_CODE,
            LV3_PROD_LIST_CN_NAME,
            LV4_PROD_LIST_CODE,
            LV4_PROD_LIST_CN_NAME,
            SPART_CODE,                   
            SPART_CN_NAME,
            SIGN_TOP_CUST_CATEGORY_CODE,
            SIGN_TOP_CUST_CATEGORY_CN_NAME,
            SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
            SUM( CASE WHEN PERIOD_YEAR = V_YEAR-3 THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS LAST_THREE_YEAR_FLAG,  -- 为当年-3年时，赋予该年数据为补齐时赋1，否则赋0
            SUM( CASE WHEN PERIOD_YEAR = V_YEAR-3 THEN APPEND_YEAR ELSE 0 END) AS LAST_THREE_APPEND_YEAR,                                  -- 为当年-3年时，取到对应向前补齐数据的补齐年份，若为原始数据或是向后补齐的，则为NULL
            SUM( CASE WHEN PERIOD_YEAR = V_YEAR-2 THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS LAST_TWO_YEAR_FLAG,       -- 为当年-2年时，赋予该年数据为补齐时赋1，否则赋0
            SUM( CASE WHEN PERIOD_YEAR = V_YEAR-2 THEN APPEND_YEAR ELSE 0 END) AS LAST_TWO_APPEND_YEAR,                                      -- 为当年-2年时，取到对应向前补齐数据的补齐年份，若为原始数据或是向后补齐的，则为NULL
            SUM( CASE WHEN PERIOD_YEAR = V_YEAR-1 THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS LAST_YEAR_FLAG,  
            SUM( CASE WHEN PERIOD_YEAR = V_YEAR-1 THEN APPEND_YEAR ELSE 0 END) AS LAST_APPEND_YEAR, 
            SUM( CASE WHEN PERIOD_YEAR = V_YEAR THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS CURRENT_YEAR_FLAG,  
            SUM( CASE WHEN PERIOD_YEAR = V_YEAR THEN APPEND_YEAR ELSE 0 END) AS CURRENT_APPEND_YEAR,
            VIEW_FLAG,        
            REGION_CODE,      
            REGION_CN_NAME,   
            REPOFFICE_CODE,   
            REPOFFICE_CN_NAME,
            BG_CODE,          
            BG_CN_NAME,       
            OVERSEA_FLAG
          FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_AVG_T
          WHERE ENABLE_FLAG = 'Y'   -- 取年均本值为有效的数据
          GROUP BY LV0_PROD_LIST_CODE,
                   LV0_PROD_LIST_CN_NAME,
                   LV1_PROD_LIST_CODE,
                   LV1_PROD_LIST_CN_NAME,
                   LV2_PROD_LIST_CODE,
                   LV2_PROD_LIST_CN_NAME,
                   LV3_PROD_LIST_CODE,
                   LV3_PROD_LIST_CN_NAME,
                   LV4_PROD_LIST_CODE,
                   LV4_PROD_LIST_CN_NAME,
                   SPART_CODE,                   
                   SPART_CN_NAME,
                   SIGN_TOP_CUST_CATEGORY_CODE,
                   SIGN_TOP_CUST_CATEGORY_CN_NAME,
                   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
                   VIEW_FLAG,        
                   REGION_CODE,      
                   REGION_CN_NAME,   
                   REPOFFICE_CODE,   
                   REPOFFICE_CN_NAME,
                   BG_CODE,          
                   BG_CN_NAME,       
                   OVERSEA_FLAG;
         
     --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '最细粒度层级缺失数据情况插入临时表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');        

  -- 对SPART层级的年份进行循环 
  FOR YEAR_FLAG IN V_YEAR-2 .. V_YEAR LOOP                      
        
    IF YEAR_FLAG = V_YEAR-2 THEN
        V_LAST_YEAR_FLAG := 'LAST_THREE_YEAR_FLAG';
        V_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
        V_YEAR_APPEND := 'LAST_THREE_APPEND_YEAR';
    
    ELSIF YEAR_FLAG = V_YEAR-1 THEN
        V_LAST_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
        V_YEAR_FLAG := 'LAST_YEAR_FLAG';
        V_YEAR_APPEND := 'LAST_TWO_APPEND_YEAR';
        
    ELSIF YEAR_FLAG = V_YEAR THEN
        V_LAST_YEAR_FLAG := 'LAST_YEAR_FLAG';
        V_YEAR_FLAG := 'CURRENT_YEAR_FLAG';
        V_YEAR_APPEND := 'LAST_APPEND_YEAR';    
    ELSE NULL;
    END IF;
    
  V_SQL := '
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_MID_BASE_STATUS_T (
         VERSION_ID,
         PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CODE,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CODE,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CODE,
         LV3_PROD_LIST_CN_NAME,
         LV4_PROD_LIST_CODE,
         LV4_PROD_LIST_CN_NAME,
         SPART_CODE,                   
         SPART_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,        
         REGION_CODE,      
         REGION_CN_NAME,   
         REPOFFICE_CODE,   
         REPOFFICE_CN_NAME,
         BG_CODE,          
         BG_CN_NAME,       
         OVERSEA_FLAG,
         STATUS_CODE,
         APPEND_YEAR,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG)
  SELECT '||V_VERSION_ID||' AS VERSION_ID,
         '||YEAR_FLAG||' AS PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CODE,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CODE,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CODE,
         LV3_PROD_LIST_CN_NAME,
         LV4_PROD_LIST_CODE,
         LV4_PROD_LIST_CN_NAME,
         SPART_CODE,                   
         SPART_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,        
         REGION_CODE,      
         REGION_CN_NAME,   
         REPOFFICE_CODE,   
         REPOFFICE_CN_NAME,
         BG_CODE,          
         BG_CN_NAME,       
         OVERSEA_FLAG,
         CASE WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 1 THEN 2      -- T-1年、T年都无值，赋值：2
              WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' = 0 THEN 3      -- T-1年、T年有值，赋值：3
              WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 0 THEN 4      -- T-1年无值、T年有值，赋值：4
              WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' = 1 THEN 5      -- T-1年有值，T年无值，赋值：5
         END AS STATUS_CODE,
         '||V_YEAR_APPEND||' AS APPEND_YEAR,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
      FROM FCST_PRICE_SPART_TMP T1';
    EXECUTE IMMEDIATE V_SQL; 

  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '最细粒度层级全维度缺失状态码插入FCST_PRICE_BASE_STATUS_TMP表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');         
    END LOOP;

  -- 把SPART层级状态码数据插入状态码表中
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_STATUS_T(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         APPEND_YEAR,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME
  )
  SELECT V_VERSION_ID AS VERSION_ID,
         PERIOD_YEAR,
         SPART_CODE AS GROUP_CODE,
         SPART_CN_NAME AS GROUP_CN_NAME,
         'SPART' AS GROUP_LEVEL,
         STATUS_CODE,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         APPEND_YEAR,
         LV4_PROD_LIST_CODE AS PARENT_CODE,
         LV4_PROD_LIST_CN_NAME AS PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         BG_CODE,
         BG_CN_NAME
	  FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MID_BASE_STATUS_T;
  
  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '将版本号为：'||V_VERSION_ID||'，最细粒度层级的状态码插入结果表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');  

  -------------------------------------------------------------------------------------- 其余层级的状态码逻辑处理 -------------------------------------------------------------------------------
  -- LV4层级状态码
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_STATUS_T(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         APPEND_YEAR,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME
  )
  WITH BASEUP_STATUS_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T1.LV4_PROD_LIST_CODE AS GROUP_CODE,    -- 取T1表中的上一层级CODE作为GROUP_CODE
         T1.LV3_PROD_LIST_CODE AS PARENT_CODE,
         T1.REGION_CODE,
         T1.REPOFFICE_CODE,
         T1.BG_CODE,
         T1.OVERSEA_FLAG,
         T1.VIEW_FLAG,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         SUM(CASE WHEN T1.STATUS_CODE IN (3,4,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋1
         SUM(DECODE(T1.STATUS_CODE,2,0,1)) AS STATUS_2     -- 值=0，说明子级都为2，赋2
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MID_BASE_STATUS_T T1
      GROUP BY T1.LV4_PROD_LIST_CODE,
               T1.LV3_PROD_LIST_CODE,
               T1.LV2_PROD_LIST_CODE,
               T1.LV1_PROD_LIST_CODE,
               T1.LV0_PROD_LIST_CODE,
               T1.REGION_CODE,
               T1.REPOFFICE_CODE,
               T1.BG_CODE,
               T1.OVERSEA_FLAG,
               T1.VIEW_FLAG,
               T1.SIGN_TOP_CUST_CATEGORY_CODE,
               T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
               T1.PERIOD_YEAR
    )
  SELECT V_VERSION_ID AS VERSION_ID,
         T1.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         CASE WHEN T2.STATUS_NORMAL > 0 THEN 1
              WHEN T2.STATUS_2 = 0 THEN 2
         END AS STATUS_CODE,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         NULL AS APPEND_YEAR,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         T1.BG_CODE,
         T1.BG_CN_NAME
     FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_AMP_T T1
     LEFT JOIN BASEUP_STATUS_TMP T2
     ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
     AND T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.PARENT_CODE = T2.PARENT_CODE
     AND T1.VIEW_FLAG = T2.VIEW_FLAG
     AND T1.BG_CODE = T2.BG_CODE
     AND NVL(T1.REGION_CODE,'SNULL1') = NVL(T2.REGION_CODE,'SNULL1')
     AND NVL(T1.REPOFFICE_CODE,'SNULL2') = NVL(T2.REPOFFICE_CODE,'SNULL2')
     AND NVL(T1.OVERSEA_FLAG,'SNULL3') = NVL(T2.OVERSEA_FLAG,'SNULL3')
     AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'SNULL4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'SNULL4')
     AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'SNULL5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'SNULL5')
     WHERE T1.GROUP_LEVEL = 'LV4'            -- 取LV4层级的涨跌幅数据
     AND T1.VERSION_ID = V_VERSION_ID ;  

  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '将版本号为：'||V_VERSION_ID||'，LV4层级的状态码插入结果表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 

  -- LV3层级状态码
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_STATUS_T(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         APPEND_YEAR,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME
  )
  WITH BASEUP_STATUS_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T1.LV3_PROD_LIST_CODE AS GROUP_CODE,    -- 取T1表中的上一层级CODE作为GROUP_CODE
         T1.LV2_PROD_LIST_CODE AS PARENT_CODE,
         T1.REGION_CODE,
         T1.REPOFFICE_CODE,
         T1.BG_CODE,
         T1.OVERSEA_FLAG,
         T1.VIEW_FLAG,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         SUM(CASE WHEN T1.STATUS_CODE IN (3,4,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋1
         SUM(DECODE(T1.STATUS_CODE,2,0,1)) AS STATUS_2     -- 值=0，说明子级都为2，赋2
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MID_BASE_STATUS_T T1
      GROUP BY T1.LV3_PROD_LIST_CODE,
               T1.LV2_PROD_LIST_CODE,
               T1.LV1_PROD_LIST_CODE,
               T1.LV0_PROD_LIST_CODE,
               T1.REGION_CODE,
               T1.REPOFFICE_CODE,
               T1.BG_CODE,
               T1.OVERSEA_FLAG,
               T1.VIEW_FLAG,
               T1.SIGN_TOP_CUST_CATEGORY_CODE,
               T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
               T1.PERIOD_YEAR
    )
  SELECT V_VERSION_ID AS VERSION_ID,
         T1.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         CASE WHEN T2.STATUS_NORMAL > 0 THEN 1
              WHEN T2.STATUS_2 = 0 THEN 2
         END AS STATUS_CODE,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         NULL AS APPEND_YEAR,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         T1.BG_CODE,
         T1.BG_CN_NAME
     FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_AMP_T T1
     LEFT JOIN BASEUP_STATUS_TMP T2
     ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
     AND T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.PARENT_CODE = T2.PARENT_CODE
     AND T1.VIEW_FLAG = T2.VIEW_FLAG
     AND T1.BG_CODE = T2.BG_CODE
     AND NVL(T1.REGION_CODE,'SNULL1') = NVL(T2.REGION_CODE,'SNULL1')
     AND NVL(T1.REPOFFICE_CODE,'SNULL2') = NVL(T2.REPOFFICE_CODE,'SNULL2')
     AND NVL(T1.OVERSEA_FLAG,'SNULL3') = NVL(T2.OVERSEA_FLAG,'SNULL3')
     AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'SNULL4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'SNULL4')
     AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'SNULL5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'SNULL5')
     WHERE T1.GROUP_LEVEL = 'LV3'            -- 取LV3层级的涨跌幅数据
     AND T1.VERSION_ID = V_VERSION_ID ;  

  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '将版本号为：'||V_VERSION_ID||'，LV3层级的状态码插入结果表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 

  -- LV2层级状态码
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_STATUS_T(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         APPEND_YEAR,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME
  )
  WITH BASEUP_STATUS_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T1.LV2_PROD_LIST_CODE AS GROUP_CODE,    -- 取T1表中的上一层级CODE作为GROUP_CODE
         T1.LV1_PROD_LIST_CODE AS PARENT_CODE,
         T1.REGION_CODE,
         T1.REPOFFICE_CODE,
         T1.BG_CODE,
         T1.OVERSEA_FLAG,
         T1.VIEW_FLAG,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         SUM(CASE WHEN T1.STATUS_CODE IN (3,4,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋1
         SUM(DECODE(T1.STATUS_CODE,2,0,1)) AS STATUS_2     -- 值=0，说明子级都为2，赋2
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MID_BASE_STATUS_T T1
      GROUP BY T1.LV2_PROD_LIST_CODE,
               T1.LV1_PROD_LIST_CODE,
               T1.LV0_PROD_LIST_CODE,
               T1.REGION_CODE,
               T1.REPOFFICE_CODE,
               T1.BG_CODE,
               T1.OVERSEA_FLAG,
               T1.VIEW_FLAG,
               T1.SIGN_TOP_CUST_CATEGORY_CODE,
               T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
               T1.PERIOD_YEAR
    )
  SELECT V_VERSION_ID AS VERSION_ID,
         T1.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         CASE WHEN T2.STATUS_NORMAL > 0 THEN 1
              WHEN T2.STATUS_2 = 0 THEN 2
         END AS STATUS_CODE,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         NULL AS APPEND_YEAR,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         T1.BG_CODE,
         T1.BG_CN_NAME
     FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_AMP_T T1
     LEFT JOIN BASEUP_STATUS_TMP T2
     ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
     AND T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.PARENT_CODE = T2.PARENT_CODE
     AND T1.VIEW_FLAG = T2.VIEW_FLAG
     AND T1.BG_CODE = T2.BG_CODE
     AND NVL(T1.REGION_CODE,'SNULL1') = NVL(T2.REGION_CODE,'SNULL1')
     AND NVL(T1.REPOFFICE_CODE,'SNULL2') = NVL(T2.REPOFFICE_CODE,'SNULL2')
     AND NVL(T1.OVERSEA_FLAG,'SNULL3') = NVL(T2.OVERSEA_FLAG,'SNULL3')
     AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'SNULL4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'SNULL4')
     AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'SNULL5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'SNULL5')
     WHERE T1.GROUP_LEVEL = 'LV2'            -- 取LV2层级的涨跌幅数据
     AND T1.VERSION_ID = V_VERSION_ID ;  

  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '将版本号为：'||V_VERSION_ID||'，LV2层级的状态码插入结果表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 

  -- LV1层级状态码
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_STATUS_T(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         APPEND_YEAR,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME
  )
  WITH BASEUP_STATUS_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T1.LV1_PROD_LIST_CODE AS GROUP_CODE,    -- 取T1表中的上一层级CODE作为GROUP_CODE
         T1.LV0_PROD_LIST_CODE AS PARENT_CODE,
         T1.REGION_CODE,
         T1.REPOFFICE_CODE,
         T1.BG_CODE,
         T1.OVERSEA_FLAG,
         T1.VIEW_FLAG,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         SUM(CASE WHEN T1.STATUS_CODE IN (3,4,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋1
         SUM(DECODE(T1.STATUS_CODE,2,0,1)) AS STATUS_2     -- 值=0，说明子级都为2，赋2
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MID_BASE_STATUS_T T1
      GROUP BY T1.LV1_PROD_LIST_CODE,
               T1.LV0_PROD_LIST_CODE,
               T1.REGION_CODE,
               T1.REPOFFICE_CODE,
               T1.BG_CODE,
               T1.OVERSEA_FLAG,
               T1.VIEW_FLAG,
               T1.SIGN_TOP_CUST_CATEGORY_CODE,
               T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
               T1.PERIOD_YEAR
    )
  SELECT V_VERSION_ID AS VERSION_ID,
         T1.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         CASE WHEN T2.STATUS_NORMAL > 0 THEN 1
              WHEN T2.STATUS_2 = 0 THEN 2
         END AS STATUS_CODE,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         NULL AS APPEND_YEAR,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         T1.BG_CODE,
         T1.BG_CN_NAME
     FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_AMP_T T1
     LEFT JOIN BASEUP_STATUS_TMP T2
     ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
     AND T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.PARENT_CODE = T2.PARENT_CODE
     AND T1.VIEW_FLAG = T2.VIEW_FLAG
     AND T1.BG_CODE = T2.BG_CODE
     AND NVL(T1.REGION_CODE,'SNULL1') = NVL(T2.REGION_CODE,'SNULL1')
     AND NVL(T1.REPOFFICE_CODE,'SNULL2') = NVL(T2.REPOFFICE_CODE,'SNULL2')
     AND NVL(T1.OVERSEA_FLAG,'SNULL3') = NVL(T2.OVERSEA_FLAG,'SNULL3')
     AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'SNULL4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'SNULL4')
     AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'SNULL5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'SNULL5')
     WHERE T1.GROUP_LEVEL = 'LV1'            -- 取LV1层级的涨跌幅数据
     AND T1.VERSION_ID = V_VERSION_ID ;  

  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '将版本号为：'||V_VERSION_ID||'，LV2层级的状态码插入结果表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 

  -- LV0层级状态码
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_STATUS_T(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         APPEND_YEAR,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME
  )
  WITH BASEUP_STATUS_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T1.LV0_PROD_LIST_CODE AS GROUP_CODE,    -- 取T1表中的上一层级CODE作为GROUP_CODE
         T1.LV0_PROD_LIST_CODE AS PARENT_CODE,
         T1.REGION_CODE,
         T1.REPOFFICE_CODE,
         T1.BG_CODE,
         T1.OVERSEA_FLAG,
         T1.VIEW_FLAG,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         SUM(CASE WHEN T1.STATUS_CODE IN (3,4,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋1
         SUM(DECODE(T1.STATUS_CODE,2,0,1)) AS STATUS_2     -- 值=0，说明子级都为2，赋2
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MID_BASE_STATUS_T T1
      GROUP BY T1.LV0_PROD_LIST_CODE,
               T1.REGION_CODE,
               T1.REPOFFICE_CODE,
               T1.BG_CODE,
               T1.OVERSEA_FLAG,
               T1.VIEW_FLAG,
               T1.SIGN_TOP_CUST_CATEGORY_CODE,
               T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
               T1.PERIOD_YEAR
    )
  SELECT V_VERSION_ID AS VERSION_ID,
         T1.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         CASE WHEN T2.STATUS_NORMAL > 0 THEN 1
              WHEN T2.STATUS_2 = 0 THEN 2
         END AS STATUS_CODE,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         NULL AS APPEND_YEAR,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         T1.BG_CODE,
         T1.BG_CN_NAME
     FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_AMP_T T1
     LEFT JOIN BASEUP_STATUS_TMP T2
     ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
     AND T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.PARENT_CODE = T2.PARENT_CODE
     AND T1.VIEW_FLAG = T2.VIEW_FLAG
     AND T1.BG_CODE = T2.BG_CODE
     AND NVL(T1.REGION_CODE,'SNULL1') = NVL(T2.REGION_CODE,'SNULL1')
     AND NVL(T1.REPOFFICE_CODE,'SNULL2') = NVL(T2.REPOFFICE_CODE,'SNULL2')
     AND NVL(T1.OVERSEA_FLAG,'SNULL3') = NVL(T2.OVERSEA_FLAG,'SNULL3')
     AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'SNULL4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'SNULL4')
     AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'SNULL5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'SNULL5')
     WHERE T1.GROUP_LEVEL = 'LV0'            -- 取LV0层级的涨跌幅数据
     AND T1.VERSION_ID = V_VERSION_ID ;  

  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '将版本号为：'||V_VERSION_ID||'，LV0层级的状态码插入结果表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 

        
    -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_STATUS_T';
  
  --3.日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集DM_FCST_PRICE_ANNL_STATUS_T统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

