-- Name: f_dm_fom_annual_status; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_annual_status(f_caliber_flag character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2023-12-11
  创建人  ：唐钦
  背景描述：年度涨跌幅状态码表
  参数描述：x_success_flag ：是否成功
  事例    ：select FIN_DM_OPT_FOI.f_dm_fom_annual_status()
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOM_ANNUAL_STATUS'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_YEAR BIGINT := YEAR(CURRENT_TIMESTAMP);   -- 当前年份
  V_LAST_YEAR_FLAG VARCHAR2(500);
  V_YEAR_FLAG VARCHAR2(500);
  V_YEAR_APPEND VARCHAR2(500);
  V_SQL TEXT;
  
BEGIN
  X_RESULT_STATUS = '1';
  -- 取刷新数据的版本号
  IF F_VERSION_ID IS NULL THEN
     SELECT VERSION_ID INTO V_VERSION_ID
         FROM FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T 
         ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;
  
  -- 开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  -- 删除年度分析中间表数据:
  DELETE FROM FIN_DM_OPT_FOI.DM_FOM_ANNUAL_STATUS_CODE_T WHERE VERSION_ID = V_VERSION_ID AND CALIBER_FLAG = F_CALIBER_FLAG;
  
  -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除FIN_DM_OPT_FOI.DM_FOM_ANNUAL_STATUS_CODE_T表，版本号为：'||V_VERSION_ID||'，数据口径为：'||F_CALIBER_FLAG||'，的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
 -- 创建ITEM层级缺失情况临时表
    DROP TABLE IF EXISTS ITEM_LACK_STATUS_TMP;
    CREATE TEMPORARY TABLE ITEM_LACK_STATUS_TMP (
        LV0_CODE CHARACTER VARYING(50),
        LV0_CN_NAME CHARACTER VARYING(200),
        LV1_CODE CHARACTER VARYING(50),
        LV1_CN_NAME CHARACTER VARYING(200),
        BUSSINESS_OBJECT_CODE CHARACTER VARYING(200),
        BUSSINESS_OBJECT_CN_NAME CHARACTER VARYING(200),
        SHIPPING_OBJECT_CODE CHARACTER VARYING(200),
        SHIPPING_OBJECT_CN_NAME CHARACTER VARYING(200),
        MANUFACTURE_OBJECT_CODE CHARACTER VARYING(200),
        MANUFACTURE_OBJECT_CN_NAME CHARACTER VARYING(200),
        ITEM_CODE CHARACTER VARYING(50),
        ITEM_CN_NAME CHARACTER VARYING(2000),
        LAST_THREE_YEAR_FLAG CHARACTER VARYING(50),
        LAST_THREE_APPEND_YEAR CHARACTER VARYING(50),
        LAST_TWO_YEAR_FLAG CHARACTER VARYING(50),
        LAST_TWO_APPEND_YEAR CHARACTER VARYING(50),
        LAST_YEAR_FLAG CHARACTER VARYING(50),
        LAST_APPEND_YEAR CHARACTER VARYING(50),
        CURRENT_YEAR_FLAG CHARACTER VARYING(50),
        CURRENT_APPEND_YEAR CHARACTER VARYING(50),
        CALIBER_FLAG CHARACTER VARYING(2),
        PARENT_CODE CHARACTER VARYING(200),
        PARENT_CN_NAME CHARACTER VARYING(200)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(ITEM_CODE);
    
    -- 写入日志
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_NUM,
     F_CAL_LOG_DESC => 'ITEM层级缺失情况临时表创建完成',
     F_RESULT_STATUS => X_RESULT_STATUS, 
     F_ERRBUF => 'SUCCESS');
     
  -- ITEM层级缺失数据情况插入临时表
  INSERT INTO ITEM_LACK_STATUS_TMP(            
              LV0_CODE,
              LV0_CN_NAME,
              LV1_CODE,
              LV1_CN_NAME,
              BUSSINESS_OBJECT_CODE,
              BUSSINESS_OBJECT_CN_NAME,
              SHIPPING_OBJECT_CODE,
              SHIPPING_OBJECT_CN_NAME,
              MANUFACTURE_OBJECT_CODE,
              MANUFACTURE_OBJECT_CN_NAME,
              ITEM_CODE,
              ITEM_CN_NAME,
              LAST_THREE_YEAR_FLAG,
              LAST_THREE_APPEND_YEAR,
              LAST_TWO_YEAR_FLAG,
              LAST_TWO_APPEND_YEAR,
              LAST_YEAR_FLAG,
              LAST_APPEND_YEAR,
              CURRENT_YEAR_FLAG,
              CURRENT_APPEND_YEAR,
              CALIBER_FLAG,
              PARENT_CODE,
              PARENT_CN_NAME
              )
   SELECT LV0_CODE,
          LV0_CN_NAME,
          LV1_CODE,
          LV1_CN_NAME,
          BUSSINESS_OBJECT_CODE,
          BUSSINESS_OBJECT_CN_NAME,
          SHIPPING_OBJECT_CODE,
          SHIPPING_OBJECT_CN_NAME,
          MANUFACTURE_OBJECT_CODE,
          MANUFACTURE_OBJECT_CN_NAME,
          ITEM_CODE,
          ITEM_CN_NAME,
          SUM( CASE WHEN PERIOD_YEAR = V_YEAR-3 THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS LAST_THREE_YEAR_FLAG,  -- 为当年-3年时，赋予该年数据为补齐时赋1，否则赋0
          SUM( CASE WHEN PERIOD_YEAR = V_YEAR-3 THEN APPEND_YEAR ELSE 0 END) AS LAST_THREE_APPEND_YEAR,                -- 为当年-3年时，取到对应向前补齐数据的补齐年份，若为原始数据或是向后补齐的，则为NULL
          SUM( CASE WHEN PERIOD_YEAR = V_YEAR-2 THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS LAST_TWO_YEAR_FLAG,    -- 为当年-2年时，赋予该年数据为补齐时赋1，否则赋0
          SUM( CASE WHEN PERIOD_YEAR = V_YEAR-2 THEN APPEND_YEAR ELSE 0 END) AS LAST_TWO_APPEND_YEAR,                  -- 为当年-2年时，取到对应向前补齐数据的补齐年份，若为原始数据或是向后补齐的，则为NULL
          SUM( CASE WHEN PERIOD_YEAR = V_YEAR-1 THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS LAST_YEAR_FLAG,  
          SUM( CASE WHEN PERIOD_YEAR = V_YEAR-1 THEN APPEND_YEAR ELSE 0 END) AS LAST_APPEND_YEAR, 
          SUM( CASE WHEN PERIOD_YEAR = V_YEAR THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS CURRENT_YEAR_FLAG,  
          SUM( CASE WHEN PERIOD_YEAR = V_YEAR THEN APPEND_YEAR ELSE 0 END) AS CURRENT_APPEND_YEAR,
          CALIBER_FLAG,
          MANUFACTURE_OBJECT_CODE AS PARENT_CODE,
          MANUFACTURE_OBJECT_CN_NAME AS PARENT_CN_NAME
       FROM FIN_DM_OPT_FOI.DM_FOM_ANNL_COST_T
       WHERE VERSION_ID = V_VERSION_ID
       AND CALIBER_FLAG = F_CALIBER_FLAG
       AND (SHIPPING_OBJECT_CODE IS NOT NULL AND MANUFACTURE_OBJECT_CODE IS NOT NULL)
       GROUP BY LV0_CODE,
                LV0_CN_NAME,
                LV1_CODE,
                LV1_CN_NAME,
                BUSSINESS_OBJECT_CODE,
                BUSSINESS_OBJECT_CN_NAME,
                SHIPPING_OBJECT_CODE,
                SHIPPING_OBJECT_CN_NAME,
                MANUFACTURE_OBJECT_CODE,
                MANUFACTURE_OBJECT_CN_NAME,
                ITEM_CODE,
                ITEM_CN_NAME,
                CALIBER_FLAG
    UNION ALL   -- 特殊LV0/LV1下，没有制造对象/发货对象数据
   SELECT LV0_CODE,
          LV0_CN_NAME,
          LV1_CODE,
          LV1_CN_NAME,
          BUSSINESS_OBJECT_CODE,
          BUSSINESS_OBJECT_CN_NAME,
          NULL AS SHIPPING_OBJECT_CODE,
          NULL AS SHIPPING_OBJECT_CN_NAME,
          NULL AS MANUFACTURE_OBJECT_CODE,
          NULL AS MANUFACTURE_OBJECT_CN_NAME,
          ITEM_CODE,
          ITEM_CN_NAME,
          SUM( CASE WHEN PERIOD_YEAR = V_YEAR-3 THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS LAST_THREE_YEAR_FLAG,  -- 为当年-3年时，赋予该年数据为补齐时赋1，否则赋0
          SUM( CASE WHEN PERIOD_YEAR = V_YEAR-3 THEN APPEND_YEAR ELSE 0 END) AS LAST_THREE_APPEND_YEAR,                -- 为当年-3年时，取到对应向前补齐数据的补齐年份，若为原始数据或是向后补齐的，则为NULL
          SUM( CASE WHEN PERIOD_YEAR = V_YEAR-2 THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS LAST_TWO_YEAR_FLAG,    -- 为当年-2年时，赋予该年数据为补齐时赋1，否则赋0
          SUM( CASE WHEN PERIOD_YEAR = V_YEAR-2 THEN APPEND_YEAR ELSE 0 END) AS LAST_TWO_APPEND_YEAR,                  -- 为当年-2年时，取到对应向前补齐数据的补齐年份，若为原始数据或是向后补齐的，则为NULL
          SUM( CASE WHEN PERIOD_YEAR = V_YEAR-1 THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS LAST_YEAR_FLAG,  
          SUM( CASE WHEN PERIOD_YEAR = V_YEAR-1 THEN APPEND_YEAR ELSE 0 END) AS LAST_APPEND_YEAR, 
          SUM( CASE WHEN PERIOD_YEAR = V_YEAR THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS CURRENT_YEAR_FLAG,  
          SUM( CASE WHEN PERIOD_YEAR = V_YEAR THEN APPEND_YEAR ELSE 0 END) AS CURRENT_APPEND_YEAR,
          CALIBER_FLAG,
          BUSSINESS_OBJECT_CODE AS PARENT_CODE,
          BUSSINESS_OBJECT_CN_NAME AS PARENT_CN_NAME
       FROM FIN_DM_OPT_FOI.DM_FOM_ANNL_COST_T
       WHERE VERSION_ID = V_VERSION_ID
       AND CALIBER_FLAG = F_CALIBER_FLAG
       AND (SHIPPING_OBJECT_CODE IS NULL AND MANUFACTURE_OBJECT_CODE IS NULL)   -- RS文档特殊情况，制造对象/发货对象置空处理
       GROUP BY LV0_CODE,
                LV0_CN_NAME,
                LV1_CODE,
                LV1_CN_NAME,
                BUSSINESS_OBJECT_CODE,
                BUSSINESS_OBJECT_CN_NAME,
                ITEM_CODE,
                ITEM_CN_NAME,
                CALIBER_FLAG;
  
  -- 写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => 'ITEM层级缺失数据情况插入临时表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');
    
  -- ITEM层级状态码逻辑
  -- 对ITEM层级的年份进行循环 
  FOR YEAR_FLAG IN V_YEAR-2 .. V_YEAR LOOP                      
  IF YEAR_FLAG = V_YEAR-2 THEN
      V_LAST_YEAR_FLAG := 'LAST_THREE_YEAR_FLAG';
      V_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
      V_YEAR_APPEND := 'LAST_THREE_APPEND_YEAR';
  
  ELSIF YEAR_FLAG = V_YEAR-1 THEN
      V_LAST_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
      V_YEAR_FLAG := 'LAST_YEAR_FLAG';
      V_YEAR_APPEND := 'LAST_TWO_APPEND_YEAR';
      
  ELSIF YEAR_FLAG = V_YEAR THEN
      V_LAST_YEAR_FLAG := 'LAST_YEAR_FLAG';
      V_YEAR_FLAG := 'CURRENT_YEAR_FLAG';
      V_YEAR_APPEND := 'LAST_APPEND_YEAR';    
  ELSE NULL;
  END IF;
     
  V_SQL := '
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_ANNUAL_STATUS_CODE_T (
--              ID,
              PERIOD_YEAR,
              VERSION_ID,
              LV0_CODE,
              LV0_CN_NAME,
              LV1_CODE,
              LV1_CN_NAME,
              BUSSINESS_OBJECT_CODE,
              BUSSINESS_OBJECT_CN_NAME,
              SHIPPING_OBJECT_CODE,
              SHIPPING_OBJECT_CN_NAME,
              MANUFACTURE_OBJECT_CODE,
              MANUFACTURE_OBJECT_CN_NAME,
              GROUP_CODE,
              GROUP_CN_NAME,
              GROUP_LEVEL,
              STATUS_CODE,
              PARENT_CODE,
              PARENT_CN_NAME,
              CREATED_BY,
              CREATION_DATE,
              LAST_UPDATED_BY,
              LAST_UPDATE_DATE,
              DEL_FLAG,
              APPEND_YEAR,
              CALIBER_FLAG
)
  SELECT '||YEAR_FLAG||' AS PERIOD_YEAR,
         '||V_VERSION_ID||' AS VERSION_ID,
         LV0_CODE,
         LV0_CN_NAME,
         LV1_CODE,
         LV1_CN_NAME,
         BUSSINESS_OBJECT_CODE,
         BUSSINESS_OBJECT_CN_NAME,
         SHIPPING_OBJECT_CODE,
         SHIPPING_OBJECT_CN_NAME,
         MANUFACTURE_OBJECT_CODE,
         MANUFACTURE_OBJECT_CN_NAME,
         ITEM_CODE AS GROUP_CODE,
         ITEM_CN_NAME AS GROUP_CN_NAME,
         ''ITEM'' AS GROUP_LEVEL,
         CASE WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' = 1 THEN 1
              WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 1 THEN 2
              WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' = 0 THEN 3
              WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 0 AND '||V_YEAR_APPEND||' = 0 THEN 4
              WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 0 AND '||V_YEAR_APPEND||' <> 0 THEN 5
         END AS STATUS_CODE,
         PARENT_CODE,
         PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         '||V_YEAR_APPEND||' AS APPEND_YEAR,
         CALIBER_FLAG
      FROM ITEM_LACK_STATUS_TMP T1';
         
  EXECUTE IMMEDIATE V_SQL;  
  DBMS_OUTPUT.PUT_LINE(V_SQL);
    
  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '版本号为：'||V_VERSION_ID||'，数据口径为：'||F_CALIBER_FLAG||'，ITEM层级全维度缺失状态码插入结果表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');         
  END LOOP;
  
  -- 其余层级涨跌幅状态码插入结果表
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_ANNUAL_STATUS_CODE_T (
--         ID,
         PERIOD_YEAR,
         VERSION_ID,
         LV0_CODE,
         LV0_CN_NAME,
         LV1_CODE,
         LV1_CN_NAME,
         BUSSINESS_OBJECT_CODE,
         BUSSINESS_OBJECT_CN_NAME,
         SHIPPING_OBJECT_CODE,
         SHIPPING_OBJECT_CN_NAME,
         MANUFACTURE_OBJECT_CODE,
         MANUFACTURE_OBJECT_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         APPEND_YEAR,
         CALIBER_FLAG
)
  WITH ITEM_STATUS_TMP AS(
       SELECT T1.PERIOD_YEAR,
              T1.LV0_CODE,
              T1.LV1_CODE,
              T1.BUSSINESS_OBJECT_CODE,
              T1.SHIPPING_OBJECT_CODE,
              T1.MANUFACTURE_OBJECT_CODE,
              T1.MANUFACTURE_OBJECT_CODE AS GROUP_CODE,    -- 取T1表中的上一层级CODE作为GROUP_CODE
              'MANUFACTURE_OBJECT' AS GROUP_LEVEL,
              T1.SHIPPING_OBJECT_CODE AS PARENT_CODE,
              SUM(CASE WHEN T1.STATUS_CODE IN (3,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋3
              SUM(DECODE(T1.STATUS_CODE,1,0,1)) AS STATUS_1,     -- 值=0，说明子级都为1，赋1
              SUM(DECODE(T1.STATUS_CODE,4,0,1)) AS STATUS_4    -- 值=0，说明子级都为4，赋2
           FROM FIN_DM_OPT_FOI.DM_FOM_ANNUAL_STATUS_CODE_T T1
           WHERE GROUP_LEVEL = 'ITEM'
           AND CALIBER_FLAG = F_CALIBER_FLAG
           AND VERSION_ID = V_VERSION_ID
           AND (SHIPPING_OBJECT_CODE IS NOT NULL AND MANUFACTURE_OBJECT_CODE IS NOT NULL)
           GROUP BY T1.PERIOD_YEAR,
                    T1.LV0_CODE,
                    T1.LV1_CODE,
                    T1.BUSSINESS_OBJECT_CODE,
                    T1.SHIPPING_OBJECT_CODE,
                    T1.MANUFACTURE_OBJECT_CODE
       UNION ALL 
       SELECT T1.PERIOD_YEAR,
              T1.LV0_CODE,
              T1.LV1_CODE,
              T1.BUSSINESS_OBJECT_CODE,
              T1.SHIPPING_OBJECT_CODE,
              NULL AS MANUFACTURE_OBJECT_CODE,
              T1.SHIPPING_OBJECT_CODE AS GROUP_CODE,    -- 取T1表中的上一层级CODE作为GROUP_CODE
              'SHIPPING_OBJECT' AS GROUP_LEVEL,
              T1.BUSSINESS_OBJECT_CODE AS PARENT_CODE,
              SUM(CASE WHEN T1.STATUS_CODE IN (3,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋3
              SUM(DECODE(T1.STATUS_CODE,1,0,1)) AS STATUS_1,     -- 值=0，说明子级都为1，赋1
              SUM(DECODE(T1.STATUS_CODE,4,0,1)) AS STATUS_4    -- 值=0，说明子级都为4，赋2
           FROM FIN_DM_OPT_FOI.DM_FOM_ANNUAL_STATUS_CODE_T T1
           WHERE GROUP_LEVEL = 'ITEM'
           AND CALIBER_FLAG = F_CALIBER_FLAG
           AND VERSION_ID = V_VERSION_ID
           AND (SHIPPING_OBJECT_CODE IS NOT NULL AND MANUFACTURE_OBJECT_CODE IS NOT NULL)
           GROUP BY T1.PERIOD_YEAR,
                    T1.LV0_CODE,
                    T1.LV1_CODE,
                    T1.BUSSINESS_OBJECT_CODE,
                    T1.SHIPPING_OBJECT_CODE
       UNION ALL 
       SELECT T1.PERIOD_YEAR,
              T1.LV0_CODE,
              T1.LV1_CODE,
              T1.BUSSINESS_OBJECT_CODE,
              NULL AS SHIPPING_OBJECT_CODE,
              NULL AS MANUFACTURE_OBJECT_CODE,
              T1.BUSSINESS_OBJECT_CODE AS GROUP_CODE,    -- 取T1表中的上一层级CODE作为GROUP_CODE
              'BUSSINESS_OBJECT' AS GROUP_LEVEL,
              T1.LV1_CODE AS PARENT_CODE,
              SUM(CASE WHEN T1.STATUS_CODE IN (3,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋3
              SUM(DECODE(T1.STATUS_CODE,1,0,1)) AS STATUS_1,     -- 值=0，说明子级都为1，赋1
              SUM(DECODE(T1.STATUS_CODE,4,0,1)) AS STATUS_4    -- 值=0，说明子级都为4，赋2
           FROM FIN_DM_OPT_FOI.DM_FOM_ANNUAL_STATUS_CODE_T T1
           WHERE GROUP_LEVEL = 'ITEM'
           AND CALIBER_FLAG = F_CALIBER_FLAG
           AND VERSION_ID = V_VERSION_ID
           GROUP BY T1.PERIOD_YEAR,
                    T1.LV0_CODE,
                    T1.LV1_CODE,
                    T1.BUSSINESS_OBJECT_CODE
       UNION ALL 
       SELECT T1.PERIOD_YEAR,
              T1.LV0_CODE,
              T1.LV1_CODE,
              NULL AS BUSSINESS_OBJECT_CODE,
              NULL AS SHIPPING_OBJECT_CODE,
              NULL AS MANUFACTURE_OBJECT_CODE,
              T1.LV1_CODE AS GROUP_CODE,    -- 取T1表中的上一层级CODE作为GROUP_CODE
              'LV1' AS GROUP_LEVEL,
              T1.LV0_CODE AS PARENT_CODE,
              SUM(CASE WHEN T1.STATUS_CODE IN (3,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋3
              SUM(DECODE(T1.STATUS_CODE,1,0,1)) AS STATUS_1,     -- 值=0，说明子级都为1，赋1
              SUM(DECODE(T1.STATUS_CODE,4,0,1)) AS STATUS_4    -- 值=0，说明子级都为4，赋2
           FROM FIN_DM_OPT_FOI.DM_FOM_ANNUAL_STATUS_CODE_T T1
           WHERE GROUP_LEVEL = 'ITEM'
           AND CALIBER_FLAG = F_CALIBER_FLAG
           AND VERSION_ID = V_VERSION_ID
           GROUP BY T1.PERIOD_YEAR,
                    T1.LV0_CODE,
                    T1.LV1_CODE
       UNION ALL 
       SELECT T1.PERIOD_YEAR,
              T1.LV0_CODE,
              NULL AS LV1_CODE,
              NULL AS BUSSINESS_OBJECT_CODE,
              NULL AS SHIPPING_OBJECT_CODE,
              NULL AS MANUFACTURE_OBJECT_CODE,
              T1.LV0_CODE AS GROUP_CODE,    -- 取T1表中的上一层级CODE作为GROUP_CODE
              'LV0' AS GROUP_LEVEL,
              T1.LV0_CODE AS PARENT_CODE,
              SUM(CASE WHEN T1.STATUS_CODE IN (3,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋3
              SUM(DECODE(T1.STATUS_CODE,1,0,1)) AS STATUS_1,     -- 值=0，说明子级都为1，赋1
              SUM(DECODE(T1.STATUS_CODE,4,0,1)) AS STATUS_4    -- 值=0，说明子级都为4，赋2
           FROM FIN_DM_OPT_FOI.DM_FOM_ANNUAL_STATUS_CODE_T T1
           WHERE GROUP_LEVEL = 'ITEM'
           AND CALIBER_FLAG = F_CALIBER_FLAG
           AND VERSION_ID = V_VERSION_ID
           GROUP BY T1.PERIOD_YEAR,
                    T1.LV0_CODE
                    )
               SELECT T1.PERIOD_YEAR,
               V_VERSION_ID AS VERSION_ID,
               T1.LV0_CODE,
               T1.LV0_CN_NAME,
               T1.LV1_CODE,
               T1.LV1_CN_NAME,
               T1.BUSSINESS_OBJECT_CODE,
               T1.BUSSINESS_OBJECT_CN_NAME,
               T1.SHIPPING_OBJECT_CODE,
               T1.SHIPPING_OBJECT_CN_NAME,
               T1.MANUFACTURE_OBJECT_CODE,
               T1.MANUFACTURE_OBJECT_CN_NAME,
               T1.GROUP_CODE,
               T1.GROUP_CN_NAME,
               T1.GROUP_LEVEL,
               CASE WHEN T2.STATUS_NORMAL > 0 THEN 3
                    WHEN T2.STATUS_1 = 0 THEN 1
                    WHEN T2.STATUS_4 = 0 THEN 2
               ELSE 4 END AS STATUS_CODE,
               T1.PARENT_CODE,
               T1.PARENT_CN_NAME,
               -1 AS CREATED_BY,
               CURRENT_TIMESTAMP AS CREATION_DATE,
               -1 AS LAST_UPDATED_BY,
               CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
               'N' AS DEL_FLAG, 
               NULL AS APPEND_YEAR,
               T1.CALIBER_FLAG
            FROM FIN_DM_OPT_FOI.DM_FOM_ANNUAL_AMP_T T1
            LEFT JOIN ITEM_STATUS_TMP T2
            ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
            AND T1.GROUP_CODE = T2.GROUP_CODE   -- 最细层级：MANUFACTURE_OBJECT
            AND T1.PARENT_CODE = T2.PARENT_CODE
            AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
            AND NVL(T1.LV0_CODE,'SNULL0') = NVL(T2.LV0_CODE,'SNULL0')
            AND NVL(T1.LV1_CODE,'SNULL1') = NVL(T2.LV1_CODE,'SNULL1')
            AND NVL(T1.BUSSINESS_OBJECT_CODE,'SNULL2') = NVL(T2.BUSSINESS_OBJECT_CODE,'SNULL2')
            AND NVL(T1.SHIPPING_OBJECT_CODE,'SNULL3') = NVL(T2.SHIPPING_OBJECT_CODE,'SNULL3')
            WHERE T1.GROUP_LEVEL <> 'ITEM'
            AND T1.VERSION_ID = V_VERSION_ID
            AND T1.CALIBER_FLAG = F_CALIBER_FLAG;
        
  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '版本号为：'||V_VERSION_ID||'，数据口径为：'||F_CALIBER_FLAG||'的其余层级缺失状态码插入结果表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');   

     -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FOM_ANNUAL_STATUS_CODE_T';

  -- 日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集DM_FOM_ANNUAL_STATUS_CODE_T统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

