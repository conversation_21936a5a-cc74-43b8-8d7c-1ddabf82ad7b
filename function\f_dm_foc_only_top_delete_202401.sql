-- Name: f_dm_foc_only_top_delete_202401; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_only_top_delete_202401(f_dimension_type character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
创建时间：2023/03/21
创建人  ：刘必华
最后修改时间:2023/06/12
最后修改人:曹昆
背景描述：剔除单规格品的TOP品类和TOP规格品
参数描述:f_dimension_type : 维度类型(通用颗粒度：U,盈利颗粒度：P), x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_TOP_ITEM_INFO_T(通用颗粒度),FIN_DM_OPT_FOI.DM_FOC_PFT_TOP_ITEM_INFO_T(盈利颗粒度)
DELETE表:
FIN_DM_OPT_FOI.DM_FOC_TOP_ITEM_INFO_T+FIN_DM_OPT_FOI.DM_FOC_TOP_CATE_INFO_T(通用颗粒度),
FIN_DM_OPT_FOI.DM_FOC_PFT_TOP_ITEM_INFO_T+FIN_DM_OPT_FOI.DM_FOC_PFT_TOP_CATE_INFO_T(盈利颗粒度),
FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_ITEM_INFO_T+FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_CATE_INFO_T(量纲颗粒度),
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_ONLY_TOP_DELETE()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_ONLY_TOP_DELETE_202401'; --存储过程名称
  V_VERSION_CATE BIGINT; --版本信息表中TOP品类最新的版本号
  V_VERSION_ITEM BIGINT; --版本信息表中TOP规格品最新的版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  
  -- 7月版本需求新增
  V_SQL        TEXT;   --SQL逻辑
  V_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_L1_NAME VARCHAR(200);
  V_L2_NAME VARCHAR(200);
  V_IN_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_L1_NAME VARCHAR(200);
  V_IN_L2_NAME VARCHAR(200);
  V_INSERT_LV3_PROD_RND_TEAM_CODE VARCHAR(500);
  V_INSERT_L1_NAME VARCHAR(500);
  V_INSERT_L2_NAME VARCHAR(500);
  V_FROM_TABLE VARCHAR(50); -- 来源表
  V_TO_TABLE1 VARCHAR(50); -- 目标表1
  V_TO_TABLE2 VARCHAR(50); -- 目标表2
  
  -- 9月版本需求新增
  V_DIMENSION_CODE VARCHAR(500);
  V_DIMENSION_CN_NAME VARCHAR(2000);
  V_DIMENSION_EN_NAME VARCHAR(2000);
  V_DIMENSION_SUBCATEGORY_CODE VARCHAR(500);
  V_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(2000);
  V_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(2000);
  V_DIMENSION_SUB_DETAIL_CODE VARCHAR(500);
  V_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(2000);
  V_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(2000);
  V_IN_DIMENSION_CODE VARCHAR(200);
  V_IN_DIMENSION_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_EN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_CODE VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_CODE VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(200);
  V_INSERT_DIMENSION_CODE VARCHAR(500);
  V_INSERT_DIMENSION_SUBCATEGORY_CODE VARCHAR(500);
  V_INSERT_DIMENSION_SUB_DETAIL_CODE VARCHAR(500);
  
    --12月版本新增spart层
  V_SPART_CODE VARCHAR(200);
  V_SPART_CN_NAME VARCHAR(200);
  V_IN_SPART_CODE VARCHAR(200);
  V_IN_SPART_CN_NAME VARCHAR(200);
  V_INSERT_SPART_CODE VARCHAR(200);
  V_INSERT_SPART_CN_NAME VARCHAR(200);
  
BEGIN
  X_RESULT_STATUS = '1';
     
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  --对入参合法性判断
  IF F_DIMENSION_TYPE NOT IN ('U','P','D') THEN
     DBMS_OUTPUT.PUT_LINE ('入参有误，请重新输入！');
     RETURN '0';
  END IF;
  
  IF F_DIMENSION_TYPE = 'U' THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOP_ITEM_INFO_T';--来源表
     V_TO_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOC_TOP_ITEM_INFO_T';--目标表1
     V_TO_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOC_TOP_CATE_INFO_T';--目标表2
  ELSIF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_TOP_ITEM_INFO_T';--来源表
     V_TO_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOC_PFT_TOP_ITEM_INFO_T'; --目标表1
     V_TO_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOC_PFT_TOP_CATE_INFO_T';--目标表2
  ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_ITEM_INFO_T_202401';--来源表
     V_TO_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_ITEM_INFO_T_202401'; --目标表1
     V_TO_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_CATE_INFO_T_202401';--目标表2
  ELSE
    NULL;
  END IF;
  
  --V_VERSION_CATE赋值
  SELECT MAX(VERSION_ID)
    INTO V_VERSION_CATE
    FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
   WHERE UPPER(VERSION_TYPE) = 'AUTO'
     AND UPPER(DATA_TYPE) = 'CATEGORY'
     AND DEL_FLAG = 'N';
     
  --V_VERSION_ITEM赋值
  SELECT MAX(VERSION_ID)
    INTO V_VERSION_ITEM
    FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
    WHERE UPPER(VERSION_TYPE) = 'AUTO'
     AND UPPER(DATA_TYPE) = 'ITEM'      
		 AND DEL_FLAG = 'N';
  
  --创建临时表
  DROP TABLE IF EXISTS DM_FOC_ONLY_ITEM_TEMP;
  CREATE TEMPORARY TABLE DM_FOC_ONLY_ITEM_TEMP
  (VERSION_ID BIGINT,
   VIEW_FLAG VARCHAR2(2),
   LV0_PROD_RND_TEAM_CODE VARCHAR(50),
   LV1_PROD_RND_TEAM_CODE VARCHAR(50),
   LV2_PROD_RND_TEAM_CODE VARCHAR(50),
   LV3_PROD_RND_TEAM_CODE VARCHAR(50),
   L1_NAME VARCHAR(200),
   L2_NAME VARCHAR(200),
   --9月版本需求新增量纲
   DIMENSION_CODE VARCHAR(500),
   DIMENSION_CN_NAME VARCHAR(2000),
   DIMENSION_EN_NAME VARCHAR(2000),
   DIMENSION_SUBCATEGORY_CODE VARCHAR(500),
   DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(2000),
   DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(2000),
   DIMENSION_SUB_DETAIL_CODE VARCHAR(500),
   DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(2000),
   DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(2000),
   SPART_CODE VARCHAR(200),
   SPART_CN_NAME VARCHAR(200),
   L3_CEG_CODE VARCHAR(50),
   L4_CEG_CODE VARCHAR(50),
   CATEGORY_CODE VARCHAR(50),
   CALIBER_FLAG VARCHAR2(2),
   OVERSEA_FLAG VARCHAR(2),
   LV0_PROD_LIST_CODE VARCHAR(50)
   )ON COMMIT PRESERVE ROWS 
   DISTRIBUTE BY REPLICATION;
   
    --7月版本需求新增
    --重置变量入参
    V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
    V_L1_NAME := 'L1_NAME,';
    V_L2_NAME := 'L2_NAME,';
    V_IN_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';
    V_IN_LV3_PROD_RD_TEAM_CN_NAME :='LV3_PROD_RD_TEAM_CN_NAME,';
    V_IN_L1_NAME := 'L1_NAME,';
    V_IN_L2_NAME := 'L2_NAME,';
    V_INSERT_LV3_PROD_RND_TEAM_CODE := ' AND NVL(A.LV3_PROD_RND_TEAM_CODE, 3) = NVL(B.LV3_PROD_RND_TEAM_CODE, 3)';
    V_INSERT_L1_NAME := ' AND NVL(A.L1_NAME, 1) = NVL(B.L1_NAME, 1)';
    V_INSERT_L2_NAME := ' AND NVL(A.L2_NAME, 2) = NVL(B.L2_NAME, 2)';
    
    --9月版本需求新增量纲
    V_DIMENSION_CODE := 'DIMENSION_CODE,';
    V_DIMENSION_CN_NAME := 'DIMENSION_CN_NAME,';
    V_DIMENSION_EN_NAME := 'DIMENSION_EN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_EN_NAME := 'DIMENSION_SUBCATEGORY_EN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME := 'DIMENSION_SUB_DETAIL_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_EN_NAME := 'DIMENSION_SUB_DETAIL_EN_NAME,';
    V_IN_DIMENSION_CODE := 'DIMENSION_CODE,';
    V_IN_DIMENSION_CN_NAME := 'DIMENSION_CN_NAME,';
    V_IN_DIMENSION_EN_NAME := 'DIMENSION_EN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE,';
    V_IN_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_EN_NAME := 'DIMENSION_SUBCATEGORY_EN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE,';
    V_IN_DIMENSION_SUB_DETAIL_CN_NAME := 'DIMENSION_SUB_DETAIL_CN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_EN_NAME := 'DIMENSION_SUB_DETAIL_EN_NAME,';
    V_INSERT_DIMENSION_CODE := ' AND NVL(A.DIMENSION_CODE, 1) = NVL(B.DIMENSION_CODE, 1)';
    V_INSERT_DIMENSION_SUBCATEGORY_CODE := ' AND NVL(A.DIMENSION_SUBCATEGORY_CODE, 2) = NVL(B.DIMENSION_SUBCATEGORY_CODE, 2)';
    V_INSERT_DIMENSION_SUB_DETAIL_CODE := ' AND NVL(A.DIMENSION_SUB_DETAIL_CODE, 3) = NVL(B.DIMENSION_SUB_DETAIL_CODE, 3)';
    
	--12月版本新增spart层
	V_SPART_CODE := 'SPART_CODE,';
	V_SPART_CN_NAME  := 'SPART_CN_NAME,';
	V_IN_SPART_CODE  := 'SPART_CODE,';
	V_IN_SPART_CN_NAME := 'SPART_CN_NAME,';
	V_INSERT_SPART_CODE  := ' AND NVL(A.SPART_CODE,2) = NVL(B.SPART_CODE,2)'; 
	
    --通用颗粒度的维度时，不需要L1、L2字段和所有量纲字段
    IF F_DIMENSION_TYPE = 'U' THEN 
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
       V_INSERT_L1_NAME := '';
       V_INSERT_L2_NAME := '';
       
       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_INSERT_DIMENSION_CODE := '';
       V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
       V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
       V_SPART_CODE := '';
	   V_SPART_CN_NAME  := '';
	   V_IN_SPART_CODE  := '';
	   V_IN_SPART_CN_NAME := '';
	   V_INSERT_SPART_CODE  := '';
	   
    --盈利颗粒度的维度时，不需要LV3字段和所有量纲字段
   ELSIF F_DIMENSION_TYPE = 'P' THEN
       V_LV3_PROD_RND_TEAM_CODE := '';
       V_LV3_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV3_PROD_RND_TEAM_CODE := '';
       V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
       V_INSERT_LV3_PROD_RND_TEAM_CODE := '';
        
       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_INSERT_DIMENSION_CODE := '';
       V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
       V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
       V_SPART_CODE := '';
	   V_SPART_CN_NAME  := '';
	   V_IN_SPART_CODE  := '';
	   V_IN_SPART_CN_NAME := '';
	   V_INSERT_SPART_CODE  := '';
	   
	   
    --量纲颗粒度的维度时，不需要L1、L2字段
   ELSIF F_DIMENSION_TYPE = 'D' THEN
        V_L1_NAME := '';
        V_L2_NAME := '';
        V_IN_L1_NAME := '';
        V_IN_L2_NAME := '';
        V_INSERT_L1_NAME := '';
        V_INSERT_L2_NAME := '';
        
    ELSE
      NULL;
    END IF;
  
  --往临时插入单规格品的品类信息
  V_SQL :=
  'INSERT INTO DM_FOC_ONLY_ITEM_TEMP
    (VERSION_ID,
     VIEW_FLAG,
     LV0_PROD_RND_TEAM_CODE,
     LV1_PROD_RND_TEAM_CODE,
     LV2_PROD_RND_TEAM_CODE,'||
     V_LV3_PROD_RND_TEAM_CODE ||
     V_L1_NAME ||
     V_L2_NAME ||
     V_DIMENSION_CODE ||
     V_DIMENSION_CN_NAME ||
     V_DIMENSION_EN_NAME||
     V_DIMENSION_SUBCATEGORY_CODE ||
     V_DIMENSION_SUBCATEGORY_CN_NAME ||
     V_DIMENSION_SUBCATEGORY_EN_NAME||
     V_DIMENSION_SUB_DETAIL_CODE ||
     V_DIMENSION_SUB_DETAIL_CN_NAME ||
     V_DIMENSION_SUB_DETAIL_EN_NAME ||
	 V_SPART_CODE ||
	 V_SPART_CN_NAME ||'
     L3_CEG_CODE,
     L4_CEG_CODE,
     CATEGORY_CODE,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE)
    SELECT S.VERSION_ID,
           S.VIEW_FLAG,
           S.LV0_PROD_RND_TEAM_CODE,
           S.LV1_PROD_RND_TEAM_CODE,
           S.LV2_PROD_RND_TEAM_CODE,'||
           V_IN_LV3_PROD_RND_TEAM_CODE ||
           V_IN_L1_NAME ||
           V_IN_L2_NAME ||
           V_IN_DIMENSION_CODE ||
           V_IN_DIMENSION_CN_NAME ||
           V_IN_DIMENSION_EN_NAME||
           V_IN_DIMENSION_SUBCATEGORY_CODE ||
           V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
           V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
           V_IN_DIMENSION_SUB_DETAIL_CODE ||
           V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
           V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
		   V_IN_SPART_CODE ||
		   V_IN_SPART_CN_NAME ||'
           S.L3_CEG_CODE,
           S.L4_CEG_CODE,
           S.CATEGORY_CODE,
           S.CALIBER_FLAG,
           S.OVERSEA_FLAG,
           S.LV0_PROD_LIST_CODE
      FROM (SELECT R.VERSION_ID,
                   R.VIEW_FLAG,
                   R.LV0_PROD_RND_TEAM_CODE,
                   R.LV1_PROD_RND_TEAM_CODE,
                   R.LV2_PROD_RND_TEAM_CODE,'||
                   V_IN_LV3_PROD_RND_TEAM_CODE ||
                   V_IN_L1_NAME ||
                   V_IN_L2_NAME ||
                   V_IN_DIMENSION_CODE ||
                   V_IN_DIMENSION_CN_NAME ||
                   V_IN_DIMENSION_EN_NAME||
                   V_IN_DIMENSION_SUBCATEGORY_CODE ||
                   V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
                   V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
                   V_IN_DIMENSION_SUB_DETAIL_CODE ||
                   V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
                   V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
				   V_IN_SPART_CODE ||
				   V_IN_SPART_CN_NAME ||'
                   R.L3_CEG_CODE,
                   R.L4_CEG_CODE,
                   R.CATEGORY_CODE,
                   R.CALIBER_FLAG,
                   R.OVERSEA_FLAG,
                   R.LV0_PROD_LIST_CODE,
                   COUNT(1) OVER(PARTITION BY R.CALIBER_FLAG, R.OVERSEA_FLAG, R.LV0_PROD_LIST_CODE, R.VIEW_FLAG, R.LV0_PROD_RND_TEAM_CODE, R.LV1_PROD_RND_TEAM_CODE, R.LV2_PROD_RND_TEAM_CODE,'|| V_IN_LV3_PROD_RND_TEAM_CODE || V_IN_L1_NAME || V_IN_L2_NAME || V_DIMENSION_CODE || V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_SUB_DETAIL_CODE ||V_SPART_CODE||V_SPART_CN_NAME||' R.L3_CEG_CODE, R.L4_CEG_CODE, R.CATEGORY_CODE) AS ITEM_FLAG
              FROM (SELECT DISTINCT T.LV0_PROD_LIST_CODE,
                                    T.OVERSEA_FLAG,
                                    T.CALIBER_FLAG,
                                    T.VERSION_ID,
                                    T.VIEW_FLAG,
                                    T.LV0_PROD_RND_TEAM_CODE,
                                    T.LV1_PROD_RND_TEAM_CODE,
                                    T.LV2_PROD_RND_TEAM_CODE,'||
                                    V_IN_LV3_PROD_RND_TEAM_CODE ||
                                    V_IN_L1_NAME ||
                                    V_IN_L2_NAME ||
                                    V_IN_DIMENSION_CODE ||
                                    V_IN_DIMENSION_CN_NAME ||
                                    V_IN_DIMENSION_EN_NAME||
                                    V_IN_DIMENSION_SUBCATEGORY_CODE ||
                                    V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
                                    V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
                                    V_IN_DIMENSION_SUB_DETAIL_CODE ||
                                    V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
                                    V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
									V_IN_SPART_CODE ||
									V_IN_SPART_CN_NAME ||'
                                    T.TOP_L3_CEG_CODE AS L3_CEG_CODE,
                                    T.TOP_L4_CEG_CODE AS L4_CEG_CODE,
                                    T.TOP_CATEGORY_CODE AS CATEGORY_CODE,
                                    T.TOP_ITEM_CODE
                      FROM '||V_FROM_TABLE ||' T
                     WHERE T.VERSION_ID = '||V_VERSION_ITEM||'
                       AND T.DOUBLE_FLAG = ''Y''
                       AND T.IS_TOP_FLAG = ''Y'') R) S
     WHERE S.ITEM_FLAG = 1';

     EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '创建临时表, 并往临时插入单规格品的品类信息, 本次获取的TOP规格品的版本号='||V_VERSION_ITEM,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --删除TOP品类
  V_SQL :=
  'DELETE FROM '||V_TO_TABLE2||' A
   USING DM_FOC_ONLY_ITEM_TEMP B
   WHERE A.VERSION_ID = '||V_VERSION_CATE||'
     AND A.VIEW_FLAG = B.VIEW_FLAG
     AND A.CALIBER_FLAG = B.CALIBER_FLAG
     AND A.OVERSEA_FLAG = B.OVERSEA_FLAG
     AND A.LV0_PROD_LIST_CODE = B.LV0_PROD_LIST_CODE
     AND A.LV0_PROD_RND_TEAM_CODE = B.LV0_PROD_RND_TEAM_CODE
     AND NVL(A.LV1_PROD_RND_TEAM_CODE, 1) = NVL(B.LV1_PROD_RND_TEAM_CODE, 1)
     AND NVL(A.LV2_PROD_RND_TEAM_CODE, 2) = NVL(B.LV2_PROD_RND_TEAM_CODE, 2)'
     ||V_INSERT_LV3_PROD_RND_TEAM_CODE
     ||V_INSERT_L1_NAME
     ||V_INSERT_L2_NAME
     ||V_INSERT_DIMENSION_CODE
     ||V_INSERT_DIMENSION_SUBCATEGORY_CODE
     ||V_INSERT_DIMENSION_SUB_DETAIL_CODE
	 ||V_INSERT_SPART_CODE 
	 ||V_INSERT_SPART_CN_NAME ||'
     AND A.TOP_L3_CEG_CODE = B.L3_CEG_CODE
     AND A.TOP_L4_CEG_CODE = B.L4_CEG_CODE
     AND A.TOP_CATEGORY_CODE = B.CATEGORY_CODE';

     EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除TOP品类清单数据: 剔除单规格品的品类, 所剔除的品类版本号='||V_VERSION_CATE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  --删除TOP规格品
    V_SQL :=
  'DELETE FROM '||V_TO_TABLE1||' A
   USING DM_FOC_ONLY_ITEM_TEMP B
   WHERE A.VERSION_ID = B.VERSION_ID
     AND A.VIEW_FLAG = B.VIEW_FLAG
     AND A.CALIBER_FLAG = B.CALIBER_FLAG
     AND A.OVERSEA_FLAG = B.OVERSEA_FLAG
     AND A.LV0_PROD_LIST_CODE = B.LV0_PROD_LIST_CODE
     AND A.LV0_PROD_RND_TEAM_CODE = B.LV0_PROD_RND_TEAM_CODE
     AND NVL(A.LV1_PROD_RND_TEAM_CODE, 1) = NVL(B.LV1_PROD_RND_TEAM_CODE, 1)
     AND NVL(A.LV2_PROD_RND_TEAM_CODE, 2) = NVL(B.LV2_PROD_RND_TEAM_CODE, 2)'
     ||V_INSERT_LV3_PROD_RND_TEAM_CODE
     ||V_INSERT_L1_NAME
     ||V_INSERT_L2_NAME
     ||V_INSERT_DIMENSION_CODE
     ||V_INSERT_DIMENSION_SUBCATEGORY_CODE
     ||V_INSERT_DIMENSION_SUB_DETAIL_CODE
	 ||V_INSERT_SPART_CODE 
	 ||V_INSERT_SPART_CN_NAME ||'
     AND A.TOP_L3_CEG_CODE = B.L3_CEG_CODE
     AND A.TOP_L4_CEG_CODE = B.L4_CEG_CODE
     AND A.TOP_CATEGORY_CODE = B.CATEGORY_CODE';

     EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除TOP规格品清单数据: 剔除单规格品, 所剔除的规格品版本号='||V_VERSION_ITEM,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

   
  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE1;
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE2;
  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE1||'+'||V_TO_TABLE2||'统计信息完成!');
 
  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END$$
/

