-- Name: f_sync_dim_purchar; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_sync_dim_purchar(f_industry_flag character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2023-08-22
  创建人  ：唐钦
  最新修改时间：2024年5月11日
  修改人：唐钦
  背景描述：采购价格指数配置页面的映射维表数据，同步至产业成本指数映射维表
            202405版本：新增同步至产业成本-数字能源映射表；（来源表不变）
  参数描述：x_success_flag ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_SYNC_DIM_PURCHAR()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_SYNC_DIM_PURCHAR'; -- 存储过程名称
  V_STEP_NUM   BIGINT := 0; -- 步骤号
  V_FOI_LAST_VERSION VARCHAR(50); -- 采购价格指数上次同步数据的版本号ID
  V_FOI_CURR_VERSION VARCHAR(50); -- 采购价格指数当前需要同步的版本号ID
  V_FOC_VERSION_ID BIGINT; -- 产业成本指数当前版本号ID
  V_FOC_ADD_VERSION BIGINT; -- 产业成本指数新增的版本号ID
  V_FOC_VERSION_NAME VARCHAR2(200);  -- 产业成本指数新增的版本号名称
  V_VERSION_NUM BIGINT;
  V_VERSION_NUM1 VARCHAR(50);
  
 -- 202405版本新增
  V_FOC_VERSION_TABLE VARCHAR(200);
  V_FOI_VERSION_TABLE VARCHAR(200);
  V_FOC_VERSION_SEQUENCE VARCHAR(200);
  V_FROM_TABLE VARCHAR(200);
  V_TO_TABLE VARCHAR(200);
  V_PARA_NAME VARCHAR(100);
  V_PARA_DESCRIPTION VARCHAR(100);
  V_SQL TEXT;
 -- 202407版本新增
  V_FROM1_TABLE VARCHAR(50);
  V_FOI_ICT_VER_TAB VARCHAR(50);
  V_FOI_ECPQC_VER_TAB VARCHAR(50);
  V_FOI_ICT_VERSION BIGINT;
  V_FOI_ECPQC_VERSION BIGINT;
  V_LAST_FLAG INT;
    
BEGIN
  X_RESULT_STATUS = '1';
    
-- 开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
 -- 判断产业项目为ICT还是数字能源
  IF F_INDUSTRY_FLAG = 'I' THEN   -- ICT  
     V_FOC_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T';
     V_FOI_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_PLAN_VERSION_T';
     V_FOC_VERSION_SEQUENCE := '''FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S''';
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_DIM_CATG_MODL_CEG_ICT_T';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_CATG_CEG_ICT_D';
     V_PARA_DESCRIPTION := '''采购ICT-映射维表最新同步版本号''';
     V_PARA_NAME := '''SYNC_DIM''';
  ELSIF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源 
     V_FOC_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T';
     V_FOI_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T';
     V_FOC_VERSION_SEQUENCE := '''FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_S''';
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_DIM_CATG_MODL_CEG_ENERGY_T';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_CATG_CEG_ICT_D';
     V_PARA_DESCRIPTION := '''采购数字能源-映射维表最新同步版本号''';
     V_PARA_NAME := '''SYNC_DIM_ENERGY''';
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407版本新增IAS
     V_FOC_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T';
     V_FOI_ICT_VER_TAB := 'FIN_DM_OPT_FOI.DM_FOI_PLAN_VERSION_T';
     V_FOI_ECPQC_VER_TAB := 'FIN_DM_OPT_FOI.DM_FOI_ECPQC_PLAN_VERSION_T';
     V_FOC_VERSION_SEQUENCE := '''FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_S''';
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_DIM_CATG_MODL_CEG_ICT_T';
     V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_DIM_CATG_MODL_CEG_ECPQC_T';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_CATG_CEG_ICT_D';
     V_PARA_DESCRIPTION := '''采购IAS-映射维表最新同步版本号''';
     V_PARA_NAME := '''SYNC_DIM_IAS''';
  END IF;
     
  -- 查询是否有初始化同步数据值
  V_SQL := '
  SELECT COUNT(1) 
     FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T 
     WHERE ENABLE_FLAG = ''Y''
     AND DEL_FLAG = ''N''
     AND UPPER(PARA_NAME) = '||V_PARA_NAME;
  EXECUTE IMMEDIATE V_SQL INTO V_LAST_FLAG;    -- 制造量纲维表上次同步数据的版本号ID
  
-- 产业成本指数当前版本号ID(同步结果表版本信息)
  V_SQL := '
  SELECT VERSION_ID       
     FROM '||V_FOC_VERSION_TABLE||' 
     WHERE DEL_FLAG = ''N''
     AND UPPER(DATA_TYPE) = ''DIMENSION''
     AND STATUS = ''1''
     ORDER BY LAST_UPDATE_DATE DESC
     LIMIT 1';      
  EXECUTE IMMEDIATE V_SQL INTO V_FOC_VERSION_ID;
  
 -- 判断除了初始化的维表数据之外，是否有对采购维表做修改
  IF V_LAST_FLAG <> 0 THEN   -- 采购维表不只有初始化数据
-- 取到对应的3个版本号的值(同步来源表版本信息：来源一致)
  V_SQL := '
  SELECT VALUE 
     FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T 
     WHERE ENABLE_FLAG = ''Y''
     AND DEL_FLAG = ''N''
     AND UPPER(PARA_NAME) = '||V_PARA_NAME;
  EXECUTE IMMEDIATE V_SQL INTO V_FOI_LAST_VERSION;   
  ELSE V_FOI_LAST_VERSION := V_FOC_VERSION_ID;
  END IF;
  
-- 采购当前指数版本号
  IF F_INDUSTRY_FLAG IN ('I','E') THEN   -- 产业领域为：ICT、数字能源
  V_SQL := '       
  SELECT VERSION_ID
     FROM '||V_FOI_VERSION_TABLE||' 
     WHERE DEL_FLAG = ''N''
     AND UPPER(DATA_TYPE) = ''DIMENSION''
     AND STATUS = ''1''
     ORDER BY LAST_UPDATE_DATE DESC
     LIMIT 1';         
  EXECUTE IMMEDIATE V_SQL INTO V_FOI_CURR_VERSION;    
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407新增IAS
  V_SQL := '       
  SELECT VERSION_ID
     FROM '||V_FOI_ICT_VER_TAB||' 
     WHERE DEL_FLAG = ''N''
     AND UPPER(DATA_TYPE) = ''DIMENSION''
     AND STATUS = ''1''
     ORDER BY LAST_UPDATE_DATE DESC
     LIMIT 1';         
  EXECUTE IMMEDIATE V_SQL INTO V_FOI_ICT_VERSION;    -- ICT维表版本号
  V_SQL := '       
  SELECT VERSION_ID
     FROM '||V_FOI_ECPQC_VER_TAB||' 
     WHERE DEL_FLAG = ''N''
     AND UPPER(DATA_TYPE) = ''DIMENSION''
     AND STATUS = ''1''
     ORDER BY LAST_UPDATE_DATE DESC
     LIMIT 1';         
  EXECUTE IMMEDIATE V_SQL INTO V_FOI_ECPQC_VERSION;    -- 华东采购维表版本号
  V_FOI_CURR_VERSION := V_FOI_ICT_VERSION||','||V_FOI_ECPQC_VERSION;   -- 当前版本号组合保存
  END IF;
         
    -- 判断采购映射维表是否有做修改，若版本号相同，直接返回，否则执行同步逻辑     
  IF V_FOI_CURR_VERSION = V_FOI_LAST_VERSION THEN
       RETURN '采购映射维表未修改，维表最新版本号数据与上次同步时的数据一致';
  ELSE NULL;
  END IF;
        
    -- 产业成本指数新版本号赋值(同步结果表版本信息)
  V_SQL := '
  SELECT NEXTVAL('||V_FOC_VERSION_SEQUENCE||')
    FROM DUAL';
    EXECUTE IMMEDIATE V_SQL INTO V_FOC_ADD_VERSION;
    
  -- 判断同步维表当天，是否有对产业映射维表做修改，在版本名称后缀做次数区分(同步结果表版本信息)
  V_SQL := '
  SELECT COUNT(1) 
      FROM '||V_FOC_VERSION_TABLE||' 
      WHERE DEL_FLAG = ''N'' AND UPPER(DATA_TYPE) = ''DIMENSION''
      AND STATUS = ''1'' AND SUBSTR(VERSION,1,8) = REPLACE(CURRENT_DATE,''-'',NULL)';
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL INTO V_VERSION_NUM;
      
  IF V_VERSION_NUM > 0 THEN
  V_SQL := '
  SELECT MAX(SUBSTR(VERSION,10,3))
      FROM '||V_FOC_VERSION_TABLE||'
      WHERE DEL_FLAG = ''N'' AND UPPER(DATA_TYPE) = ''DIMENSION''
      AND STATUS = ''1'' AND SUBSTR(VERSION,1,8) = REPLACE(CURRENT_DATE,''-'',NULL)';
    EXECUTE IMMEDIATE V_SQL INTO V_VERSION_NUM;
  ELSE V_VERSION_NUM := '';
  END IF;    
  
  IF V_VERSION_NUM IS NULL THEN  
     V_VERSION_NUM := '1'; 
     V_VERSION_NUM1 := '-00';
  ELSE V_VERSION_NUM := V_VERSION_NUM + 1;
    IF V_VERSION_NUM >= 10 THEN 
       V_VERSION_NUM1 := '-0';
    ELSE V_VERSION_NUM1 := '-00';
    END IF;
  END IF;  
  -- 拼接版本名称
  V_FOC_VERSION_NAME := REPLACE(CURRENT_DATE,'-','')||V_VERSION_NUM1||V_VERSION_NUM;    
    DBMS_OUTPUT.PUT_LINE(V_FOC_VERSION_NAME);
  
    -- 采购映射维表刷新后，往产业成本指数版本信息表插入新的维表版本号   
  V_SQL := '
  INSERT INTO '||V_FOC_VERSION_TABLE||'
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   IS_RUNNING)
   VALUES
   ('||V_FOC_ADD_VERSION||','||V_FOC_VERSION_ID||','''||V_FOC_VERSION_NAME||''',1,''ADJUST'',''DIMENSION'',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,''N'',''N'')';    
   EXECUTE IMMEDIATE V_SQL;
   
     -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '产业成本指数映射维表新增版本号：'||V_FOC_ADD_VERSION||'插入版本信息表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');   
   
  IF F_INDUSTRY_FLAG IN ('I','E') THEN
  -- 将 上个版本的数据新增一个版本插入产业映射维表
  V_SQL := '
  INSERT INTO '||V_TO_TABLE||'(
         VERSION_ID,
         CATEGORY_CODE,
         CATEGORY_CN_NAME,
         L4_CEG_SHORT_CN_NAME,
         L4_CEG_CN_NAME,
         L3_CEG_SHORT_CN_NAME,
         L3_CEG_CN_NAME,
         L4_CEG_CODE,
         L3_CEG_CODE,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATED_DATE,
         DEL_FLAG)

  SELECT '||V_FOC_ADD_VERSION||' AS VERSION_ID,
         T1.CATEGORY_CODE,
         T1.CATEGORY_CN_NAME,
         T1.L4_CEG_SHORT_CN_NAME,
         T1.L4_CEG_CN_NAME,
         T1.L3_CEG_SHORT_CN_NAME,
         T1.L3_CEG_CN_NAME,
         T1.L4_CEG_CODE,
         T1.L3_CEG_CODE,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
       FROM '||V_TO_TABLE||' T1
       WHERE VERSION_ID = '||V_FOC_VERSION_ID;  
   EXECUTE IMMEDIATE V_SQL;
       
     -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将产业成本指数映射维表的数据，存放进新增的版本号',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');          
         
  --创建临时表
    DROP TABLE IF EXISTS DIM_ADD_DEL_TMP;
    CREATE TEMPORARY TABLE DIM_ADD_DEL_TMP (
         CATEGORY_CODE VARCHAR(50),
         CATEGORY_NAME VARCHAR(200),
         L4_CEG_SHORT_CN_NAME VARCHAR(200),
         L4_CEG_CN_NAME VARCHAR(200),
         L3_CEG_SHORT_CN_NAME VARCHAR(200),
         L3_CEG_CN_NAME VARCHAR(200),
         MODFIY_STATUS VARCHAR(50)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY REPLICATION;         

  V_SQL := '
-- 把有新增/删除的数据放到临时表
  INSERT INTO DIM_ADD_DEL_TMP(
         CATEGORY_CODE,
         CATEGORY_NAME,
         L4_CEG_SHORT_CN_NAME,
         L4_CEG_CN_NAME,
         L3_CEG_SHORT_CN_NAME,
         L3_CEG_CN_NAME,
         MODFIY_STATUS)
  SELECT CATEGORY_CODE,
         CATEGORY_NAME,
         L4_CEG_SHORT_CN_NAME,
         L4_CEG_CN_NAME,
         L3_CEG_SHORT_CN_NAME,
         L3_CEG_CN_NAME,
         ''ADD'' AS MODFIY_STATUS
     FROM '||V_FROM_TABLE||'
     WHERE VERSION_ID = '''||V_FOI_CURR_VERSION||'''
  MINUS
  SELECT CATEGORY_CODE,
         CATEGORY_NAME,
         L4_CEG_SHORT_CN_NAME,
         L4_CEG_CN_NAME,
         L3_CEG_SHORT_CN_NAME,
         L3_CEG_CN_NAME,
         ''ADD'' AS MODFIY_STATUS
     FROM '||V_FROM_TABLE||'
     WHERE VERSION_ID = '''||V_FOI_LAST_VERSION||'''
  UNION ALL    
  SELECT CATEGORY_CODE,
         CATEGORY_NAME,
         L4_CEG_SHORT_CN_NAME,
         L4_CEG_CN_NAME,
         L3_CEG_SHORT_CN_NAME,
         L3_CEG_CN_NAME,
         ''DEL'' AS MODFIY_STATUS
     FROM '||V_FROM_TABLE||'
     WHERE VERSION_ID = '''||V_FOI_LAST_VERSION||'''
  MINUS
  SELECT CATEGORY_CODE,
         CATEGORY_NAME,
         L4_CEG_SHORT_CN_NAME,
         L4_CEG_CN_NAME,
         L3_CEG_SHORT_CN_NAME,
         L3_CEG_CN_NAME,
         ''DEL'' AS MODFIY_STATUS
     FROM '||V_FROM_TABLE||'
     WHERE VERSION_ID = '''||V_FOI_CURR_VERSION||'''';                
   EXECUTE IMMEDIATE V_SQL;
         
-- 先对产业成本的维表做删除操作
  V_SQL := '
  DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_FOC_ADD_VERSION||' 
         AND CATEGORY_CODE IN ( SELECT CATEGORY_CODE FROM DIM_ADD_DEL_TMP WHERE MODFIY_STATUS = ''DEL'' )';    
   EXECUTE IMMEDIATE V_SQL;
         
    -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将期间采购用户已删除的数据在产业的映射维表中先进行删除',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');      
         
-- 采购指数增加的维度数据同步至产业指数映射维表
  V_SQL := '
  INSERT INTO '||V_TO_TABLE||'(
         VERSION_ID,
         CATEGORY_CODE,
         CATEGORY_CN_NAME,
         L4_CEG_SHORT_CN_NAME,
         L4_CEG_CN_NAME,
         L3_CEG_SHORT_CN_NAME,
         L3_CEG_CN_NAME,
         L4_CEG_CODE,
         L3_CEG_CODE,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATED_DATE,
         DEL_FLAG)

  SELECT '||V_FOC_ADD_VERSION||' AS VERSION_ID,
         T2.CATEGORY_CODE,
         T2.CATEGORY_NAME,
         T2.L4_CEG_SHORT_CN_NAME,
         T2.L4_CEG_CN_NAME,
         T2.L3_CEG_SHORT_CN_NAME,
         T2.L3_CEG_CN_NAME,
         T1.L4_CEG_CODE,
         T1.L3_CEG_CODE,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
       FROM (SELECT DISTINCT L3_CEG_CODE,
                             L3_CEG_CN_NAME,
                             L4_CEG_CODE,
                             L4_CEG_CN_NAME
                FROM DMDIM.DM_DIM_CEG_D
                             WHERE DEL_FLAG = ''N'') T1
       INNER JOIN DIM_ADD_DEL_TMP T2
       ON T1.L4_CEG_CN_NAME = T2.L4_CEG_CN_NAME
       AND T1.L3_CEG_CN_NAME = T2.L3_CEG_CN_NAME
       WHERE T2.MODFIY_STATUS = ''ADD''
       AND NOT EXISTS (SELECT 1 FROM '||V_TO_TABLE||' T3 
                             WHERE T2.CATEGORY_CODE = T3.CATEGORY_CODE
                             AND T3.VERSION_ID = '||V_FOC_ADD_VERSION||' )';
   EXECUTE IMMEDIATE V_SQL;

-- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将期间采购用户新增/修改的数据插入到产业的映射维表中',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN
  -- ICT和华东采购的映射维表数据拼接并去重后得到IAS映射维表数据，并将数据插入临时表
  V_SQL := '
    INSERT INTO '||V_TO_TABLE||' (
           VERSION_ID,
           CATEGORY_CODE,
           CATEGORY_CN_NAME,
           L3_CEG_CN_NAME,
           L3_CEG_SHORT_CN_NAME,
           CREATED_BY,
           CREATION_DATE,
           LAST_UPDATED_BY,
           LAST_UPDATED_DATE,
           DEL_FLAG,
           L3_CEG_CODE,
           L4_CEG_CODE,
           L4_CEG_CN_NAME,
           L4_CEG_SHORT_CN_NAME
    )
    SELECT DISTINCT '||V_FOC_ADD_VERSION||' AS VERSION_ID,
           T1.CATEGORY_CODE,
           T1.CATEGORY_NAME,
           T1.L3_CEG_CN_NAME,
           T1.L3_CEG_SHORT_CN_NAME,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATED_DATE,
           ''N'' AS DEL_FLAG,
           T2.L3_CEG_CODE,
           T2.L4_CEG_CODE,
           T1.L4_CEG_CN_NAME,
           T1.L4_CEG_SHORT_CN_NAME
        FROM
       ( SELECT DISTINCT CATEGORY_CODE,CATEGORY_NAME,L3_CEG_CN_NAME,L3_CEG_SHORT_CN_NAME,
        L4_CEG_CN_NAME,L4_CEG_SHORT_CN_NAME ,ROW_NUMBER() OVER(PARTITION BY CATEGORY_CODE ORDER BY LABLE ASC) AS RANK  -- 按品类编码去重，优先取华东采购维度数据
        FROM (
            SELECT ''ICT'' AS LABLE,CATEGORY_CODE,CATEGORY_NAME,
                   L4_CEG_CN_NAME,L4_CEG_SHORT_CN_NAME,
                   L3_CEG_CN_NAME,L3_CEG_SHORT_CN_NAME
               FROM '||V_FROM_TABLE||'
               WHERE VERSION_ID = '||V_FOI_ICT_VERSION||'
            UNION ALL 
            SELECT ''EAST_CHINA_PQC'' AS LABLE,CATEGORY_CODE,CATEGORY_NAME,
                   L4_CEG_CN_NAME,L4_CEG_SHORT_CN_NAME,
                   L3_CEG_CN_NAME,L3_CEG_SHORT_CN_NAME
               FROM '||V_FROM1_TABLE||'
               WHERE VERSION_ID = '||V_FOI_ECPQC_VERSION||'
    )) T1
    LEFT JOIN DMDIM.DM_DIM_CEG_D T2
    ON T1.L4_CEG_CN_NAME = T2.L4_CEG_CN_NAME
    AND T1.L3_CEG_CN_NAME = T2.L3_CEG_CN_NAME
    WHERE T1.RANK = 1
    AND T2.L2_CEG_CODE IN (''51132'',''12229'')  -- 限制ICT及华东采购L2层级的编码
    AND T2.L3_CEG_CODE IN (''12256'',''12257'',''12251'',''12849'',''12253'',''16449'',''19319'',''12252'',''51135'',''13610'',''19484'',''19382'',''51133'',''16349'')';  -- IAS共14个专家团数据
    EXECUTE IMMEDIATE V_SQL;
  END IF;

-- 删除变量参数表上次保存的采购维表同步版本号数据     
EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T WHERE PARA_NAME = '||V_PARA_NAME||' AND ENABLE_FLAG = ''Y''';
     
-- 将采购本次取得的最新版本号插入到变量参数表
V_SQL := '
INSERT INTO FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T (
       ID,
       PARA_NAME,
       PARA_DESCRIPTION,
       VALUE,
       CREATED_BY,
       CREATION_DATE,
       LAST_UPDATED_BY,
       LAST_UPDATE_DATE,
       DEL_FLAG,
       ENABLE_FLAG)
VALUES( FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_S.NEXTVAL,
       '||V_PARA_NAME||',
       '||V_PARA_DESCRIPTION||',
       '''||V_FOI_CURR_VERSION||''',
       -1,
       CURRENT_TIMESTAMP,
       -1,
       CURRENT_TIMESTAMP,
       ''N'',
       ''Y'')';
   EXECUTE IMMEDIATE V_SQL;

 -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将采购本次取得的最新版本号：'||V_FOI_CURR_VERSION||'插入到变量参数表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  ANALYSE FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T;
  ANALYSE FIN_DM_OPT_FOI.DM_FOC_CATG_CEG_ICT_D;
  ANALYSE FIN_DM_OPT_FOI.DM_DIM_CATG_MODL_CEG_ICT_T;    
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集统计信息完成!');
 
  RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );
  
END$$
/
