-- Name: f_dm_foc_repl_same_mid_mtd_index_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_repl_same_mid_mtd_index_t(f_cost_type character varying, f_keystr text DEFAULT NULL::text, f_version_id integer DEFAULT NULL::integer, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
----同编码指数中间表
F_COST_TYPE 数据类型 : PUR 采购 , MADE 制造

---来源表
--累积均本表
采购 DM_FOC_REPL_SAME_PUR_MTD_AVG_T
制造 DM_FOC_REPL_SAME_MADE_MTD_AVG_T

--年均本
采购年均本 DM_FOC_VIEW_ANNL_COST_T
制造年均本 DM_FOC_MADE_VIEW_ANNL_COST_T

--目标表
采购 DM_FOC_REPL_SAME_MID_PUR_MTD_INDEX_T
制造 DM_FOC_REPL_SAME_MID_MADE_MTD_INDEX_T

SELECT FIN_DM_OPT_FOI.F_DM_FOC_REPL_SAME_MID_MTD_INDEX_T ('PUR','密钥','');	--采购一个版本数据
SELECT FIN_DM_OPT_FOI.F_DM_FOC_REPL_SAME_MID_MTD_INDEX_T ('MADE','','');	--制造一个版本数据

****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                 VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FOC_REPL_SAME_MID_MTD_INDEX_T';
  V_EXCEPTION_FLAG          INT;
  V_VERSION					INT;
  V_BASE_PERIOD_ID			INT := TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '01');
  V_NOW_PERIOD              INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYYMM') AS INT);
  V_BEFORE_YEAR             INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-13),'YYYY') AS INT);     -- 取去年年份，用于分母值取数（若当月为1月时，即取前年年份）
  
BEGIN

X_RESULT_STATUS :='1';

 --写入日志
 V_EXCEPTION_FLAG	:= 0;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 0,
  F_CAL_LOG_DESC => '开始执行',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

--版本号取数
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'ITEM'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '取得本次执行数据成本类型：'||F_COST_TYPE||'，版本号：'||V_VERSION,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

--月均本临时表建表
DROP TABLE IF EXISTS DM_BASE_MONTH_AMT_TEMP;
CREATE TEMPORARY TABLE DM_BASE_MONTH_AMT_TEMP(
PERIOD_YEAR	BIGINT,
PERIOD_ID	BIGINT,
LV0_PROD_RND_TEAM_CODE	VARCHAR(50),
LV0_PROD_RD_TEAM_CN_NAME	VARCHAR(200),
LV1_PROD_RND_TEAM_CODE	VARCHAR(50),
LV1_PROD_RD_TEAM_CN_NAME	VARCHAR(200),
LV2_PROD_RND_TEAM_CODE	VARCHAR(50),
LV2_PROD_RD_TEAM_CN_NAME	VARCHAR(200),
LV3_PROD_RND_TEAM_CODE	VARCHAR(50),
LV3_PROD_RD_TEAM_CN_NAME	VARCHAR(200),
L3_CEG_CODE	VARCHAR(50),
L3_CEG_CN_NAME	VARCHAR(200),
L3_CEG_SHORT_CN_NAME	VARCHAR(200),
L4_CEG_CODE	VARCHAR(50),
L4_CEG_CN_NAME	VARCHAR(200),
L4_CEG_SHORT_CN_NAME	VARCHAR(200),
CATEGORY_CODE	VARCHAR(50),
CATEGORY_CN_NAME	VARCHAR(200),
SHIPPING_OBJECT_CODE 		VARCHAR(200),
SHIPPING_OBJECT_CN_NAME		VARCHAR(200),
MANUFACTURE_OBJECT_CODE 	VARCHAR(200),
MANUFACTURE_OBJECT_CN_NAME	VARCHAR(200),
ITEM_CODE	VARCHAR(50),
ITEM_CN_NAME	VARCHAR(1000),
RMB_AVG_AMT		NUMERIC,
CALIBER_FLAG	VARCHAR(2),
VIEW_FLAG	VARCHAR(50)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(ITEM_CODE,MANUFACTURE_OBJECT_CODE);

--月均本落表
  IF F_COST_TYPE = 'PUR' THEN
    INSERT INTO DM_BASE_MONTH_AMT_TEMP
      (PERIOD_YEAR,
       PERIOD_ID,
       LV0_PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME,
       LV1_PROD_RND_TEAM_CODE,
       LV1_PROD_RD_TEAM_CN_NAME,
       LV2_PROD_RND_TEAM_CODE,
       LV2_PROD_RD_TEAM_CN_NAME,
       LV3_PROD_RND_TEAM_CODE,
       LV3_PROD_RD_TEAM_CN_NAME,
       L3_CEG_CODE,
       L3_CEG_CN_NAME,
       L3_CEG_SHORT_CN_NAME,
       L4_CEG_CODE,
       L4_CEG_CN_NAME,
       L4_CEG_SHORT_CN_NAME,
       CATEGORY_CODE,
       CATEGORY_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       RMB_AVG_AMT,
       CALIBER_FLAG,
       VIEW_FLAG)
      SELECT PERIOD_YEAR,
             PERIOD_ID,
             LV0_PROD_RND_TEAM_CODE,
             LV0_PROD_RD_TEAM_CN_NAME,
             LV1_PROD_RND_TEAM_CODE,
             LV1_PROD_RD_TEAM_CN_NAME,
             LV2_PROD_RND_TEAM_CODE,
             LV2_PROD_RD_TEAM_CN_NAME,
             LV3_PROD_RND_TEAM_CODE,
             LV3_PROD_RD_TEAM_CN_NAME,
             L3_CEG_CODE,
             L3_CEG_CN_NAME,
             L3_CEG_SHORT_CN_NAME,
             L4_CEG_CODE,
             L4_CEG_CN_NAME,
             L4_CEG_SHORT_CN_NAME,
             CATEGORY_CODE,
             CATEGORY_CN_NAME,
             ITEM_CODE,
             ITEM_CN_NAME,
             TO_NUMBER(GS_DECRYPT(RMB_AVG_AMT,
                                  F_KEYSTR,
                                  'aes128',
                                  'cbc',
                                  'sha256')) AS RMB_AVG_AMT,
             CALIBER_FLAG,
             VIEW_FLAG
        FROM FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_PUR_MTD_AVG_T
        WHERE PERIOD_ID = V_NOW_PERIOD;      -- 取最新月份-1月的数据
  ELSIF F_COST_TYPE = 'MADE' THEN
    INSERT INTO DM_BASE_MONTH_AMT_TEMP
      (PERIOD_YEAR,
       PERIOD_ID,
       LV0_PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME,
       LV1_PROD_RND_TEAM_CODE,
       LV1_PROD_RD_TEAM_CN_NAME,
       LV2_PROD_RND_TEAM_CODE,
       LV2_PROD_RD_TEAM_CN_NAME,
       LV3_PROD_RND_TEAM_CODE,
       LV3_PROD_RD_TEAM_CN_NAME,
       SHIPPING_OBJECT_CODE,
       SHIPPING_OBJECT_CN_NAME,
       MANUFACTURE_OBJECT_CODE,
       MANUFACTURE_OBJECT_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       RMB_AVG_AMT,
       CALIBER_FLAG,
       VIEW_FLAG)
      SELECT PERIOD_YEAR,
             PERIOD_ID,
             LV0_PROD_RND_TEAM_CODE,
             LV0_PROD_RD_TEAM_CN_NAME,
             LV1_PROD_RND_TEAM_CODE,
             LV1_PROD_RD_TEAM_CN_NAME,
             LV2_PROD_RND_TEAM_CODE,
             LV2_PROD_RD_TEAM_CN_NAME,
             LV3_PROD_RND_TEAM_CODE,
             LV3_PROD_RD_TEAM_CN_NAME,
             SHIPPING_OBJECT_CODE,
             SHIPPING_OBJECT_CN_NAME,
             MANUFACTURE_OBJECT_CODE,
             MANUFACTURE_OBJECT_CN_NAME,
             ITEM_CODE,
             ITEM_CN_NAME,
             RMB_AVG_AMT,
             CALIBER_FLAG,
             VIEW_FLAG
        FROM FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MADE_MTD_AVG_T
        WHERE PERIOD_ID = V_NOW_PERIOD;      -- 取最新月份-1月的数据
  END IF;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 2;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 2,
  F_CAL_LOG_DESC => '月份为：'||V_NOW_PERIOD||'的月均本表落表完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
--年均本临时表建表
DROP TABLE IF EXISTS DM_BASE_ANNL_AMT_TEMP;
CREATE TEMPORARY TABLE DM_BASE_ANNL_AMT_TEMP(
PERIOD_YEAR	BIGINT,
LV0_PROD_RND_TEAM_CODE	VARCHAR(50),
LV0_PROD_RD_TEAM_CN_NAME	VARCHAR(200),
LV1_PROD_RND_TEAM_CODE	VARCHAR(50),
LV1_PROD_RD_TEAM_CN_NAME	VARCHAR(200),
LV2_PROD_RND_TEAM_CODE	VARCHAR(50),
LV2_PROD_RD_TEAM_CN_NAME	VARCHAR(200),
LV3_PROD_RND_TEAM_CODE	VARCHAR(50),
LV3_PROD_RD_TEAM_CN_NAME	VARCHAR(200),
L3_CEG_CODE	VARCHAR(50),
L3_CEG_CN_NAME	VARCHAR(200),
L3_CEG_SHORT_CN_NAME	VARCHAR(200),
L4_CEG_CODE	VARCHAR(50),
L4_CEG_CN_NAME	VARCHAR(200),
L4_CEG_SHORT_CN_NAME	VARCHAR(200),
CATEGORY_CODE	VARCHAR(50),
CATEGORY_CN_NAME	VARCHAR(200),
SHIPPING_OBJECT_CODE 	VARCHAR(200),
SHIPPING_OBJECT_CN_NAME	VARCHAR(200),
MANUFACTURE_OBJECT_CODE 	VARCHAR(200),
MANUFACTURE_OBJECT_CN_NAME	VARCHAR(200),
ITEM_CODE	VARCHAR(50),
ITEM_CN_NAME	VARCHAR(1000),
RMB_AVG_AMT		NUMERIC,
CALIBER_FLAG	VARCHAR(2),
VIEW_FLAG	VARCHAR(50)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(ITEM_CODE,MANUFACTURE_OBJECT_CODE);

  IF F_COST_TYPE = 'PUR' THEN
  --采购
    INSERT INTO DM_BASE_ANNL_AMT_TEMP
      (PERIOD_YEAR,
       LV0_PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME,
       LV1_PROD_RND_TEAM_CODE,
       LV1_PROD_RD_TEAM_CN_NAME,
       LV2_PROD_RND_TEAM_CODE,
       LV2_PROD_RD_TEAM_CN_NAME,
       LV3_PROD_RND_TEAM_CODE,
       LV3_PROD_RD_TEAM_CN_NAME,
       L3_CEG_CODE,
       L3_CEG_CN_NAME,
       L3_CEG_SHORT_CN_NAME,
       L4_CEG_CODE,
       L4_CEG_CN_NAME,
       L4_CEG_SHORT_CN_NAME,
       CATEGORY_CODE,
       CATEGORY_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       RMB_AVG_AMT,
       CALIBER_FLAG,
       VIEW_FLAG)
      SELECT PERIOD_YEAR,
             LV0_PROD_RND_TEAM_CODE,
             LV0_PROD_RD_TEAM_CN_NAME,
             LV1_PROD_RND_TEAM_CODE,
             LV1_PROD_RD_TEAM_CN_NAME,
             LV2_PROD_RND_TEAM_CODE,
             LV2_PROD_RD_TEAM_CN_NAME,
             LV3_PROD_RND_TEAM_CODE,
             LV3_PROD_RD_TEAM_CN_NAME,
             L3_CEG_CODE,
             L3_CEG_CN_NAME,
             L3_CEG_SHORT_CN_NAME,
             L4_CEG_CODE,
             L4_CEG_CN_NAME,
             L4_CEG_SHORT_CN_NAME,
             CATEGORY_CODE,
             CATEGORY_CN_NAME,
             ITEM_CODE,
             ITEM_CN_NAME,
             TO_NUMBER(GS_DECRYPT(RMB_AVG_AMT,
                                  F_KEYSTR,
                                  'aes128',
                                  'cbc',
                                  'sha256')) AS RMB_AVG_AMT,
             CALIBER_FLAG,
             VIEW_FLAG
        FROM FIN_DM_OPT_FOI.DM_FOC_VIEW_ANNL_COST_T
       WHERE PERIOD_YEAR = V_BEFORE_YEAR   -- 取前一年数据作为分母
         AND OVERSEA_FLAG = 'G'
         AND LV0_PROD_LIST_CODE = 'GR'; --同编码指数只取全球和集团的数据
		 
	 --写入日志
	 V_EXCEPTION_FLAG	:= 3;
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => 3,
	  F_CAL_LOG_DESC => '年均本表落表完成',
	  F_DML_ROW_COUNT => SQL%ROWCOUNT,
	  F_RESULT_STATUS => X_RESULT_STATUS,
	  F_ERRBUF => 'SUCCESS'); 
  
  TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MID_PUR_MTD_INDEX_T;
  
    --指数计算
    INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MID_PUR_MTD_INDEX_T
      (VERSION_ID,
	   BASE_PERIOD_ID,
	   PERIOD_YEAR,
	   PERIOD_ID,
       LV0_PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME,
       LV1_PROD_RND_TEAM_CODE,
       LV1_PROD_RD_TEAM_CN_NAME,
       LV2_PROD_RND_TEAM_CODE,
       LV2_PROD_RD_TEAM_CN_NAME,
       LV3_PROD_RND_TEAM_CODE,
       LV3_PROD_RD_TEAM_CN_NAME,
	   PROD_RND_TEAM_CODE,
	   PROD_RND_TEAM_CN_NAME,
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_CN_NAME,
       TOP_L3_CEG_SHORT_CN_NAME,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME,
	   TOP_CATEGORY_CODE,
	   TOP_CATEGORY_CN_NAME,
       GROUP_CODE,
	   GROUP_CN_NAME,
       COST_INDEX,
	   GROUP_LEVEL,
	   PARENT_CODE,
	   PARENT_CN_NAME,
       CALIBER_FLAG,
       VIEW_FLAG,
       CREATED_BY,
       CREATION_DATE,
       LAST_UPDATED_BY,
       LAST_UPDATE_DATE,
       DEL_FLAG)
      SELECT V_VERSION AS VERSION_ID,
			 V_BASE_PERIOD_ID AS BASE_PERIOD_ID,
			 T1.PERIOD_YEAR,
             T1.PERIOD_ID,
             T1.LV0_PROD_RND_TEAM_CODE,
             T1.LV0_PROD_RD_TEAM_CN_NAME,
             T1.LV1_PROD_RND_TEAM_CODE,
             T1.LV1_PROD_RD_TEAM_CN_NAME,
             T1.LV2_PROD_RND_TEAM_CODE,
             T1.LV2_PROD_RD_TEAM_CN_NAME,
             T1.LV3_PROD_RND_TEAM_CODE,
             T1.LV3_PROD_RD_TEAM_CN_NAME,
			 CASE T1.VIEW_FLAG 
			   WHEN '0' THEN T1.LV0_PROD_RND_TEAM_CODE
			   WHEN '1' THEN T1.LV1_PROD_RND_TEAM_CODE
			   WHEN '2' THEN T1.LV2_PROD_RND_TEAM_CODE
			   WHEN '3' THEN T1.LV3_PROD_RND_TEAM_CODE
			 END AS PROD_RND_TEAM_CODE,
			 CASE T1.VIEW_FLAG 
			   WHEN '0' THEN T1.LV0_PROD_RD_TEAM_CN_NAME
			   WHEN '1' THEN T1.LV1_PROD_RD_TEAM_CN_NAME
			   WHEN '2' THEN T1.LV2_PROD_RD_TEAM_CN_NAME
			   WHEN '3' THEN T1.LV3_PROD_RD_TEAM_CN_NAME
			 END AS PROD_RND_TEAM_CN_NAME,
             T1.L3_CEG_CODE,
             T1.L3_CEG_CN_NAME,
             T1.L3_CEG_SHORT_CN_NAME,
             T1.L4_CEG_CODE,
             T1.L4_CEG_SHORT_CN_NAME,
			 T1.CATEGORY_CODE,
			 T1.CATEGORY_CN_NAME,
             T1.ITEM_CODE AS GROUP_CODE,
             T1.ITEM_CN_NAME AS GROUP_CN_NAME,
             (T1.RMB_AVG_AMT / NULLIF(T2.RMB_AVG_AMT, 0)) - 1 AS COST_INDEX,
			 'ITEM' AS GROUP_LEVEL,
			 T1.CATEGORY_CODE AS PARENT_CODE,
			 T1.CATEGORY_CN_NAME AS PARENT_CN_NAME,
             T1.CALIBER_FLAG,
             T1.VIEW_FLAG,
             -1 AS CREATED_BY,
             CURRENT_TIMESTAMP AS CREATION_DATE,
             -1 AS LAST_UPDATED_BY,
             CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
             'N' AS DEL_FLAG
        FROM DM_BASE_MONTH_AMT_TEMP T1
        LEFT JOIN DM_BASE_ANNL_AMT_TEMP T2
          ON  NVL(T1.LV3_PROD_RND_TEAM_CODE,'A') = NVL(T2.LV3_PROD_RND_TEAM_CODE,'A')
		  AND NVL(T1.LV2_PROD_RND_TEAM_CODE,'A') = NVL(T2.LV2_PROD_RND_TEAM_CODE,'A')
		  AND NVL(T1.LV1_PROD_RND_TEAM_CODE,'A') = NVL(T2.LV1_PROD_RND_TEAM_CODE,'A')
		  AND NVL(T1.LV0_PROD_RND_TEAM_CODE,'A') = NVL(T2.LV0_PROD_RND_TEAM_CODE,'A')
	      AND NVL(T1.L3_CEG_CODE,'A') = NVL(T2.L3_CEG_CODE,'A')
	      AND NVL(T1.L4_CEG_CODE,'A') = NVL(T2.L4_CEG_CODE,'A')
	      AND NVL(T1.CATEGORY_CODE,'A') = NVL(T2.CATEGORY_CODE,'A')
	      AND NVL(T1.ITEM_CODE,'A') = NVL(T2.ITEM_CODE,'A')				  
         AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
         AND T1.VIEW_FLAG = T2.VIEW_FLAG;
		
	 --写入日志
	 V_EXCEPTION_FLAG	:= 4;
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => 4,
	  F_CAL_LOG_DESC => 'ITEM同编码指数计算完成',
	  F_DML_ROW_COUNT => SQL%ROWCOUNT,
	  F_RESULT_STATUS => X_RESULT_STATUS,
	  F_ERRBUF => 'SUCCESS'); 
  
  ELSIF F_COST_TYPE = 'MADE' THEN
  --制造
    INSERT INTO DM_BASE_ANNL_AMT_TEMP
      (PERIOD_YEAR,
       LV0_PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME,
       LV1_PROD_RND_TEAM_CODE,
       LV1_PROD_RD_TEAM_CN_NAME,
       LV2_PROD_RND_TEAM_CODE,
       LV2_PROD_RD_TEAM_CN_NAME,
       LV3_PROD_RND_TEAM_CODE,
       LV3_PROD_RD_TEAM_CN_NAME,
       SHIPPING_OBJECT_CODE,
       SHIPPING_OBJECT_CN_NAME,
       MANUFACTURE_OBJECT_CODE,
       MANUFACTURE_OBJECT_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       RMB_AVG_AMT,
       CALIBER_FLAG,
       VIEW_FLAG)
      SELECT PERIOD_YEAR,
             LV0_PROD_RND_TEAM_CODE,
             LV0_PROD_RD_TEAM_CN_NAME,
             LV1_PROD_RND_TEAM_CODE,
             LV1_PROD_RD_TEAM_CN_NAME,
             LV2_PROD_RND_TEAM_CODE,
             LV2_PROD_RD_TEAM_CN_NAME,
             LV3_PROD_RND_TEAM_CODE,
             LV3_PROD_RD_TEAM_CN_NAME,
             SHIPPING_OBJECT_CODE,
             SHIPPING_OBJECT_CN_NAME,
             MANUFACTURE_OBJECT_CODE,
             MANUFACTURE_OBJECT_CN_NAME,
             ITEM_CODE,
             ITEM_CN_NAME,
             RMB_AVG_AMT,
             CALIBER_FLAG,
             VIEW_FLAG
        FROM FIN_DM_OPT_FOI.DM_FOC_MADE_VIEW_ANNL_COST_T
       WHERE PERIOD_YEAR = V_BEFORE_YEAR   -- 取前一年数据作为分母
         AND OVERSEA_FLAG = 'G'
         AND LV0_PROD_LIST_CODE = 'GR'; --同编码指数只取全球和集团的数据
		 
	 --写入日志
	 V_EXCEPTION_FLAG	:= 3;
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => 3,
	  F_CAL_LOG_DESC => '年均本表落表完成',
	  F_DML_ROW_COUNT => SQL%ROWCOUNT,
	  F_RESULT_STATUS => X_RESULT_STATUS,
	  F_ERRBUF => 'SUCCESS');
  
    --指数计算
	
	TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MID_MADE_MTD_INDEX_T;
	
    INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MID_MADE_MTD_INDEX_T
      (VERSION_ID,
	   BASE_PERIOD_ID,
	   PERIOD_YEAR,
	   PERIOD_ID,
       LV0_PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME,
       LV1_PROD_RND_TEAM_CODE,
       LV1_PROD_RD_TEAM_CN_NAME,
       LV2_PROD_RND_TEAM_CODE,
       LV2_PROD_RD_TEAM_CN_NAME,
       LV3_PROD_RND_TEAM_CODE,
       LV3_PROD_RD_TEAM_CN_NAME,
	   PROD_RND_TEAM_CODE,
	   PROD_RND_TEAM_CN_NAME,
       SHIPPING_OBJECT_CODE,
       SHIPPING_OBJECT_CN_NAME,
       MANUFACTURE_OBJECT_CODE,
       MANUFACTURE_OBJECT_CN_NAME,
       GROUP_CODE,
       GROUP_CN_NAME,
       COST_INDEX,
	   GROUP_LEVEL,
	   PARENT_CODE,
	   PARENT_CN_NAME,
       CALIBER_FLAG,
       VIEW_FLAG,
       CREATED_BY,
       CREATION_DATE,
       LAST_UPDATED_BY,
       LAST_UPDATE_DATE,
       DEL_FLAG)
      SELECT V_VERSION AS VERSION_ID,
			 V_BASE_PERIOD_ID AS BASE_PERIOD_ID,
			 T1.PERIOD_YEAR,
			 T1.PERIOD_ID,
             T1.LV0_PROD_RND_TEAM_CODE,
             T1.LV0_PROD_RD_TEAM_CN_NAME,
             T1.LV1_PROD_RND_TEAM_CODE,
             T1.LV1_PROD_RD_TEAM_CN_NAME,
             T1.LV2_PROD_RND_TEAM_CODE,
             T1.LV2_PROD_RD_TEAM_CN_NAME,
             T1.LV3_PROD_RND_TEAM_CODE,
             T1.LV3_PROD_RD_TEAM_CN_NAME,
			 CASE T1.VIEW_FLAG 
			   WHEN '0' THEN T1.LV0_PROD_RND_TEAM_CODE
			   WHEN '1' THEN T1.LV1_PROD_RND_TEAM_CODE
			   WHEN '2' THEN T1.LV2_PROD_RND_TEAM_CODE
			   WHEN '3' THEN T1.LV3_PROD_RND_TEAM_CODE
			 END AS PROD_RND_TEAM_CODE,
			 CASE T1.VIEW_FLAG 
			   WHEN '0' THEN T1.LV0_PROD_RD_TEAM_CN_NAME
			   WHEN '1' THEN T1.LV1_PROD_RD_TEAM_CN_NAME
			   WHEN '2' THEN T1.LV2_PROD_RD_TEAM_CN_NAME
			   WHEN '3' THEN T1.LV3_PROD_RD_TEAM_CN_NAME
			 END AS PROD_RND_TEAM_CN_NAME,
             T1.SHIPPING_OBJECT_CODE,
             T1.SHIPPING_OBJECT_CN_NAME,
             T1.MANUFACTURE_OBJECT_CODE,
             T1.MANUFACTURE_OBJECT_CN_NAME,
             T1.ITEM_CODE AS GROUP_CODE,
             T1.ITEM_CN_NAME AS GROUP_CN_NAME,
             (T1.RMB_AVG_AMT / NULLIF(T2.RMB_AVG_AMT, 0)) - 1 AS COST_INDEX,
			 'ITEM' AS GROUP_LEVEL,
			 T1.MANUFACTURE_OBJECT_CODE AS PARENT_CODE,
			 T1.MANUFACTURE_OBJECT_CN_NAME AS PARENT_CN_NAME,
             T1.CALIBER_FLAG,
             T1.VIEW_FLAG,
             -1 AS CREATED_BY,
             CURRENT_TIMESTAMP AS CREATION_DATE,
             -1 AS LAST_UPDATED_BY,
             CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
             'N' AS DEL_FLAG
        FROM DM_BASE_MONTH_AMT_TEMP T1
        LEFT JOIN DM_BASE_ANNL_AMT_TEMP T2
          ON  NVL(T1.LV3_PROD_RND_TEAM_CODE,'A') = NVL(T2.LV3_PROD_RND_TEAM_CODE,'A')
		  AND NVL(T1.LV2_PROD_RND_TEAM_CODE,'A') = NVL(T2.LV2_PROD_RND_TEAM_CODE,'A')
		  AND NVL(T1.LV1_PROD_RND_TEAM_CODE,'A') = NVL(T2.LV1_PROD_RND_TEAM_CODE,'A')
		  AND NVL(T1.LV0_PROD_RND_TEAM_CODE,'A') = NVL(T2.LV0_PROD_RND_TEAM_CODE,'A')	
		  AND NVL(T1.SHIPPING_OBJECT_CODE,'A') = NVL(T2.SHIPPING_OBJECT_CODE,'A')
		  AND NVL(T1.MANUFACTURE_OBJECT_CODE,'A') = NVL(T2.MANUFACTURE_OBJECT_CODE,'A')
		  AND NVL(T1.ITEM_CODE,'A') = NVL(T2.ITEM_CODE,'A')
         AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
         AND T1.VIEW_FLAG = T2.VIEW_FLAG;
		 
	 --写入日志
	 V_EXCEPTION_FLAG	:= 4;
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => 4,
	  F_CAL_LOG_DESC => 'ITEM同编码指数计算完成',
	  F_DML_ROW_COUNT => SQL%ROWCOUNT,
	  F_RESULT_STATUS => X_RESULT_STATUS,
	  F_ERRBUF => 'SUCCESS'); 
	  
  END IF;

	 --写入日志
	 V_EXCEPTION_FLAG	:= 5;
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => 5,
	  F_CAL_LOG_DESC => '同编码指数中间表计算完成',
	  F_RESULT_STATUS => X_RESULT_STATUS,
	  F_ERRBUF => 'SUCCESS'); 
	  
  RETURN 'SUCCESS';
  

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
     PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_STEP_NUM => V_EXCEPTION_FLAG,
    F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

 END; 
$$
/

