-- Name: f_dm_foc_made_mid_month_item; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_made_mid_month_item(f_industry_flag character varying, f_caliber_flag character varying, f_dimension_type character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
创建时间:2023/10/18
创建人  :罗若文
最后修改时间:2024/01/30
最后修改人:杨泽宝
背景描述:分视角统计ITEM的月卷积发货额
修改： 量纲增加 SPART_CODE 和 SPART_CN_NAME 字段,增加视角 VIEW_FLAG 9-11, REVIEW_ITEM_FLAG 是否底层数据审视的逻辑 许灿烽 20231222
       针对盈利颗粒度原1-3视角对应后台结果表和取数逻辑需进行移除，以保证清洁数据和逻辑 杨泽宝 202403版本 
参数描述:f_caliber_flag : 业务口径(R：收入时点,C：发货成本), f_dimension_type : 维度类型(通用颗粒度：U,盈利颗粒度：P), x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_MADE_BOM_ITEM_SHIP_DTL_T(制造对象-发货成本),FIN_DM_OPT_FOI.DM_FOC_MADE_REVENUE_ITEM_SHIP_DTL_T(制造对象-收入时点)
目标表:	FIN_DM_OPT_FOI.DM_FOC_MADE_MID_MONTH_ITEM_T(制造对象-通用颗粒度),
		FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_MID_MONTH_ITEM_T(制造对象-盈利颗粒度),
		FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MID_MONTH_ITEM_T(制造对象-量纲颗粒度)
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_MADE_MID_MONTH_ITEM()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_MADE_MID_MONTH_ITEM'; --存储过程名称
  V_VERSION_ID BIGINT; --版本号, 取自 FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T的版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_BEGIN_YEAR BIGINT := YEAR(CURRENT_TIMESTAMP)-3;  -- 系统年-3的年份
  V_END_YEAR BIGINT := YEAR(CURRENT_TIMESTAMP);  -- 系统年
  V_SQL        TEXT;   --SQL逻辑
  V_LV1_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_LV2_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_LV3_PROD_RND_TEAM_CODE VARCHAR(50); -- 7月版本需求新增
  V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_LV4_PROD_RND_TEAM_CODE VARCHAR(50); -- 7月版本需求新增
  V_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_L1_NAME VARCHAR(200);
  V_L2_NAME VARCHAR(200);
  V_IN_LV1_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_LV2_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_LV4_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_L1_NAME VARCHAR(200);
  V_IN_L2_NAME VARCHAR(200);
  V_INSERT_LV1_PROD_RND_TEAM_CODE VARCHAR(200);
  V_INSERT_LV2_PROD_RND_TEAM_CODE VARCHAR(200);
  V_INSERT_LV3_PROD_RND_TEAM_CODE VARCHAR(200);
  V_INSERT_LV4_PROD_RND_TEAM_CODE VARCHAR(200);
  V_INSERT_L1_NAME VARCHAR(500);
  V_INSERT_L2_NAME VARCHAR(500);
  V_JOIN_TABLE VARCHAR(500);
  
  -- 7月版本需求新增
  V_FROM_TABLE VARCHAR(100); -- 来源表
  V_TO_TABLE VARCHAR(100); -- 目标表
  V_VIEW_CNT BIGINT; -- 处理通用颗粒度和盈利颗粒度视角数目不同

  -- 9月版本需求新增
  V_DIMENSION_CODE VARCHAR(100);
  V_DIMENSION_CN_NAME VARCHAR(100);
  V_DIMENSION_EN_NAME VARCHAR(100);
  V_DIMENSION_SUBCATEGORY_CODE VARCHAR(500);
  V_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(100);
  V_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(100);
  V_DIMENSION_SUB_DETAIL_CODE VARCHAR(100);
  V_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(100);
  V_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(100);
  V_IN_DIMENSION_CODE VARCHAR(200);
  V_IN_DIMENSION_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_EN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_CODE VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_CODE VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(200);
  V_INSERT_DIMENSION_CODE VARCHAR(500);
  V_INSERT_DIMENSION_SUBCATEGORY_CODE VARCHAR(500);
  V_INSERT_DIMENSION_SUB_DETAIL_CODE VARCHAR(500);
  
  -- 11月版本需求
  V_SHIPPING_OBJECT_CODE VARCHAR(50);
  V_SHIPPING_OBJECT_CN_NAME VARCHAR(50);
  V_MANUFACTURE_OBJECT_CODE VARCHAR(50);
  V_MANUFACTURE_OBJECT_CN_NAME VARCHAR(50);
  V_IN_SHIPPING_OBJECT_CODE VARCHAR(500);
  V_IN_SHIPPING_OBJECT_CN_NAME VARCHAR(500);
  V_IN_MANUFACTURE_OBJECT_CODE VARCHAR(500);
  V_IN_MANUFACTURE_OBJECT_CN_NAME VARCHAR(500);
  V_INSERT_SHIPPING_OBJECT_CODE VARCHAR(500);
  V_INSERT_MANUFACTURE_OBJECT_CODE VARCHAR(500);
  
--202401月版本需求新增量纲
  V_PARENTPARTNUMBER VARCHAR(50);
  V_SPART_CODE VARCHAR(50);
  V_SPART_CN_NAME VARCHAR(50);
  V_IN_PARENTPARTNUMBER VARCHAR(50);
  V_IN_SPART_CODE VARCHAR(50);
  V_IN_SPART_CN_NAME VARCHAR(50);
  V_INSERT_SPART_CODE VARCHAR(100);
  V_VERSION_REVIEW_MADE_ID BIGINT; --制造成本-底层数据审视  版本号

  --202403月版本需求 杨泽宝
  V_VIEW_BEGIN BIGINT; /*新增(视角)开始值*/
  
      --202405月版本需求
  V_PROD_CODE VARCHAR(50);
  V_PROD_CN_NAME VARCHAR(50);
  V_COA_CODE VARCHAR(50);
  V_COA_CN_NAME VARCHAR(50);
  V_IN_PROD_CODE VARCHAR(50);
  V_IN_PROD_CN_NAME VARCHAR(50);
  V_IN_COA_CODE VARCHAR(50);
  V_IN_COA_CN_NAME VARCHAR(50);
  V_INSERT_COA_CODE VARCHAR(100);
  V_REVERSE_VIEW_FLAG BIGINT;
BEGIN
  X_RESULT_STATUS = '1';
  
  --开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  --对入参合法性判断
  IF F_DIMENSION_TYPE NOT IN ('U','P','D') OR F_CALIBER_FLAG NOT IN ('C','R') THEN
     DBMS_OUTPUT.PUT_LINE ('入参有误，请重新输入！');
     RETURN '0';
  END IF;
  
  IF F_CALIBER_FLAG = 'C' AND F_INDUSTRY_FLAG = 'I' THEN -- 发货成本
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_BOM_ITEM_SHIP_DTL_T';--来源表
     --V_JOIN_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DATA_PRIMARY_ENCRYPT_T ';--关联加密表   -- 11月版本 制造对象不用加密
	
  ELSIF F_CALIBER_FLAG = 'R' AND F_INDUSTRY_FLAG = 'I' THEN -- 收入时点
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_REVENUE_ITEM_SHIP_DTL_T'; --来源表 
     --V_JOIN_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_REVENUE_DATA_PRIMARY_ENCRYPT_T ';--关联加密表  -- 11月版本 制造对象不用加密
	 
  ELSIF F_CALIBER_FLAG = 'C' AND F_INDUSTRY_FLAG = 'E' THEN -- 发货成本
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_BOM_ITEM_SHIP_DTL_T';--来源表
     --V_JOIN_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DATA_PRIMARY_ENCRYPT_T ';--关联加密表   -- 11月版本 制造对象不用加密
	
  ELSIF F_CALIBER_FLAG = 'R' AND F_INDUSTRY_FLAG = 'E' THEN -- 收入时点
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_REVENUE_ITEM_SHIP_DTL_T'; --来源表 
     --V_JOIN_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_REVENUE_DATA_PRIMARY_ENCRYPT_T ';--关联加密表  -- 11月版本 制造对象不用加密
	 
  ELSIF F_CALIBER_FLAG = 'C' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 发货成本
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_BOM_ITEM_SHIP_DTL_T';--来源表
     --V_JOIN_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DATA_PRIMARY_ENCRYPT_T ';--关联加密表   -- 11月版本 制造对象不用加密
	
  ELSIF F_CALIBER_FLAG = 'R' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 收入时点
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_REVENUE_ITEM_SHIP_DTL_T'; --来源表 
     --V_JOIN_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_REVENUE_DATA_PRIMARY_ENCRYPT_T ';--关联加密表  -- 11月版本 制造对象不用加密	 

  ELSE
    NULL;
  END IF;
    
  IF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'I' THEN -- 通用颗粒度
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_MID_MONTH_ITEM_T';--目标表
     V_VIEW_CNT := 3; --通用颗粒度视角从0到3
	 V_VIEW_BEGIN := 0; --通用颗粒度视角从0开始 杨泽宝 20240130
  ELSIF F_DIMENSION_TYPE = 'P' AND F_INDUSTRY_FLAG = 'I' THEN -- 盈利颗粒度
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_MID_MONTH_ITEM_T'; --目标表 
     V_VIEW_CNT := 4; --盈利颗粒度视角从0到4
	 V_VIEW_BEGIN := 3;--盈利颗粒度视角从3开始 杨泽宝 20240130
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'I'  THEN -- 量纲颗粒度
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MID_MONTH_ITEM_T'; --目标表 
     V_VIEW_CNT := 11; --量纲颗粒度视角从0到8
	 V_VIEW_BEGIN := 0;--量纲颗粒度视角从0开始 杨泽宝 20240130
	 
  ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'E' THEN -- 通用颗粒度
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_MID_MONTH_ITEM_T';--目标表
     V_VIEW_CNT := 3; --通用颗粒度视角从0到3
	 V_VIEW_BEGIN := 0; --通用颗粒度视角从0开始 杨泽宝 20240130
  ELSIF F_DIMENSION_TYPE = 'P' AND F_INDUSTRY_FLAG = 'E' THEN -- 盈利颗粒度
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_MID_MONTH_ITEM_T'; --目标表 
     V_VIEW_CNT := 4; --盈利颗粒度视角从0到4
	 V_VIEW_BEGIN := 3;--盈利颗粒度视角从3开始 杨泽宝 20240130
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'E'  THEN -- 量纲颗粒度
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_MID_MONTH_ITEM_T'; --目标表 
     V_VIEW_CNT := 12; --量纲颗粒度视角从0到8
	 V_VIEW_BEGIN := 0;--量纲颗粒度视角从0开始 杨泽宝 20240130 
	 
  ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 通用颗粒度
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_MID_MONTH_ITEM_T';--目标表
     V_VIEW_CNT := 4; --通用颗粒度视角从0到4 最后一个视角为7
	 V_VIEW_BEGIN := 0; --通用颗粒度视角从0开始 杨泽宝 20240130
  ELSIF F_DIMENSION_TYPE = 'P' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 盈利颗粒度
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_MID_MONTH_ITEM_T'; --目标表 
     V_VIEW_CNT := 4; --盈利颗粒度视角从0到4
	 V_VIEW_BEGIN := 3;--盈利颗粒度视角从3开始 杨泽宝 20240130
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'IAS'  THEN -- 量纲颗粒度
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_MID_MONTH_ITEM_T'; --目标表 
     V_VIEW_CNT := 12; --量纲颗粒度视角从0到8
	 V_VIEW_BEGIN := 0;--量纲颗粒度视角从0开始 杨泽宝 20240130  

  
  ELSE
    NULL;
  END IF;


  --清空目标表数据
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' T WHERE T.CALIBER_FLAG = '''||F_CALIBER_FLAG||'''';
  
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据,颗粒度：'||F_DIMENSION_TYPE||',时点：'||F_CALIBER_FLAG||',产业类型：'||F_INDUSTRY_FLAG,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --创建ITEM月卷积发货额临时表
    DROP TABLE IF EXISTS BASE_DATA_TEMP;
    CREATE TEMPORARY TABLE BASE_DATA_TEMP (
        VERSION_ID BIGINT,
        PERIOD_YEAR BIGINT,
        PERIOD_ID BIGINT,
        LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
        LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
        LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
        --7月版本需求
        LV3_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
		LV4_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV4_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
        L1_NAME VARCHAR(200),
        L2_NAME VARCHAR(200),
        --9月版本需求新增量纲
        DIMENSION_CODE    VARCHAR(500),
        DIMENSION_CN_NAME    VARCHAR(100),
        DIMENSION_EN_NAME    VARCHAR(100),
        DIMENSION_SUBCATEGORY_CODE    VARCHAR(500),
        DIMENSION_SUBCATEGORY_CN_NAME    VARCHAR(100),
        DIMENSION_SUBCATEGORY_EN_NAME    VARCHAR(100),
        DIMENSION_SUB_DETAIL_CODE    VARCHAR(500),
        DIMENSION_SUB_DETAIL_CN_NAME    VARCHAR(100),
        DIMENSION_SUB_DETAIL_EN_NAME    VARCHAR(100),
        --202401月版本需求新增量纲
        SPART_CODE VARCHAR(200),
        SPART_CN_NAME VARCHAR(200),
		COA_CODE VARCHAR(50),
		COA_CN_NAME VARCHAR(600) , --202405版本新增
		--11月版本需求新增制造对象
		SHIPPING_OBJECT_CODE VARCHAR(500),
		SHIPPING_OBJECT_CN_NAME VARCHAR(500),
		MANUFACTURE_OBJECT_CODE VARCHAR(500),
		MANUFACTURE_OBJECT_CN_NAME VARCHAR(500),
        ITEM_CODE CHARACTER VARYING(50),
        ITEM_CN_NAME CHARACTER VARYING(2000),
        SHIP_QUANTITY NUMERIC,
        AVG_AMT NUMERIC,
		MANUFACTURE_AMT NUMERIC,
        OVERSEA_FLAG VARCHAR(2),-- 9月版本需求新增
        LV0_PROD_LIST_CODE VARCHAR(50),
        LV0_PROD_LIST_CN_NAME VARCHAR(200),
        LV0_PROD_LIST_EN_NAME VARCHAR(200)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(PERIOD_ID,ITEM_CODE);
    
    --2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
        F_STEP_NUM =>  V_STEP_MUM,
        F_CAL_LOG_DESC => 'ITEM月卷积发货额临时表创建完成,f_caliber_flag:'||f_caliber_flag||',f_dimension_type:'||f_dimension_type||',颗粒度：'||F_DIMENSION_TYPE||',时点：'||F_CALIBER_FLAG||',产业类型：'||F_INDUSTRY_FLAG,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS');  
        
    --7月版本需求新增
    --重置变量入参
    V_LV2_PROD_RND_TEAM_CODE := 'LV2_PROD_RND_TEAM_CODE,';
    V_LV2_PROD_RD_TEAM_CN_NAME := 'LV2_PROD_RD_TEAM_CN_NAME,';
    V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
	V_LV4_PROD_RND_TEAM_CODE := 'LV4_PROD_RND_TEAM_CODE,';
    V_LV4_PROD_RD_TEAM_CN_NAME := 'LV4_PROD_RD_TEAM_CN_NAME,';
    V_L1_NAME := 'L1_NAME,';
    V_L2_NAME := 'L2_NAME,';
    
    V_IN_LV2_PROD_RND_TEAM_CODE := 'T.LV2_PROD_RND_TEAM_CODE,';
    V_IN_LV2_PROD_RD_TEAM_CN_NAME :='T.LV2_PROD_RD_TEAM_CN_NAME,';
    V_IN_LV3_PROD_RND_TEAM_CODE := 'T.LV3_PROD_RND_TEAM_CODE,';
    V_IN_LV3_PROD_RD_TEAM_CN_NAME := 'T.LV3_PROD_RD_TEAM_CN_NAME,';
	V_IN_LV4_PROD_RND_TEAM_CODE := 'T.LV4_PROD_RND_TEAM_CODE,';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME := 'T.LV4_PROD_RD_TEAM_CN_NAME,';
    V_IN_L1_NAME := 'T.L1_NAME,';
    V_IN_L2_NAME := 'T.L2_NAME,';

    --9月版本需求新增量纲
    V_DIMENSION_CODE := 'DIMENSION_CODE,';
    V_DIMENSION_CN_NAME := 'DIMENSION_CN_NAME,';
    V_DIMENSION_EN_NAME := 'DIMENSION_EN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_EN_NAME := 'DIMENSION_SUBCATEGORY_EN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME := 'DIMENSION_SUB_DETAIL_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_EN_NAME := 'DIMENSION_SUB_DETAIL_EN_NAME,';
    V_IN_DIMENSION_CODE := 'T.DIMENSION_CODE,';
    V_IN_DIMENSION_CN_NAME := 'T.DIMENSION_CN_NAME,';
    V_IN_DIMENSION_EN_NAME := 'T.DIMENSION_EN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_CODE := 'T.DIMENSION_SUBCATEGORY_CODE,';
    V_IN_DIMENSION_SUBCATEGORY_CN_NAME := 'T.DIMENSION_SUBCATEGORY_CN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_EN_NAME := 'T.DIMENSION_SUBCATEGORY_EN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_CODE := 'T.DIMENSION_SUB_DETAIL_CODE,';
    V_IN_DIMENSION_SUB_DETAIL_CN_NAME := 'T.DIMENSION_SUB_DETAIL_CN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_EN_NAME := 'T.DIMENSION_SUB_DETAIL_EN_NAME,';
	
	--11月版本需求新增制造对象
	V_SHIPPING_OBJECT_CODE := 'SHIPPING_OBJECT_CODE,' ;
	V_SHIPPING_OBJECT_CN_NAME := 'SHIPPING_OBJECT_CN_NAME,';
	V_MANUFACTURE_OBJECT_CODE := 'MANUFACTURE_OBJECT_CODE,';
    V_MANUFACTURE_OBJECT_CN_NAME := 'MANUFACTURE_OBJECT_CN_NAME,';
	V_IN_SHIPPING_OBJECT_CODE := 'T.SHIPPING_OBJECT_CODE,' ;
	V_IN_SHIPPING_OBJECT_CN_NAME := 'T.SHIPPING_OBJECT_CN_NAME,';
	V_IN_MANUFACTURE_OBJECT_CODE := 'T.MANUFACTURE_OBJECT_CODE,';
    V_IN_MANUFACTURE_OBJECT_CN_NAME := 'T.MANUFACTURE_OBJECT_CN_NAME,';

--202401月版本需求新增量纲
  V_PARENTPARTNUMBER := 'PARENTPARTNUMBER,';
  V_SPART_CODE :='SPART_CODE,';
  V_SPART_CN_NAME  := 'SPART_CN_NAME,';
  V_IN_PARENTPARTNUMBER := 'T.PARENTPARTNUMBER,';
  V_IN_SPART_CODE  := 'T.SPART_CODE,';
  V_IN_SPART_CN_NAME := 'T.SPART_CN_NAME,';
  
    
  --202405月版本需求新增
    V_PROD_CODE := 'PROD_CODE,';
	V_PROD_CN_NAME := 'PROD_CN_NAME,';
    V_COA_CODE :='COA_CODE,';
    V_COA_CN_NAME :='COA_CN_NAME,';
    V_IN_PROD_CODE := 'T.PROD_CODE,';
	V_IN_PROD_CN_NAME := 'T.PROD_CN_NAME,';
    V_IN_COA_CODE := 'T.COA_CODE,';
    V_IN_COA_CN_NAME := 'T.COA_CN_NAME,';

 --通用颗粒度的维度时，不需要L1、L2字段和所有量纲字段
	--数字能源和ICT不要LV3.5
    IF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG IN ('E','I') THEN 
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
	   
	   
	--202401月版本需求新增
    V_PARENTPARTNUMBER := '';
    V_SPART_CODE :='';
    V_SPART_CN_NAME := '';
    V_IN_PARENTPARTNUMBER := '';
    V_IN_SPART_CODE := '';
    V_IN_SPART_CN_NAME := '';
	
	--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
    V_COA_CODE := '';
    V_COA_CN_NAME :='';
    V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
    V_IN_COA_CODE := '';
    V_IN_COA_CN_NAME := '';
	
	--202407需求新增
	V_LV4_PROD_RND_TEAM_CODE := '';
    V_LV4_PROD_RD_TEAM_CN_NAME := '';
    V_IN_LV4_PROD_RND_TEAM_CODE := '';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	
	--通用颗粒度的维度时，不需要L1、L2字段和所有量纲字段
	--IAS要LV3.5
    ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'IAS' THEN 
	V_L1_NAME := '';
    V_L2_NAME := '';
    V_IN_L1_NAME := '';
    V_IN_L2_NAME := '';
    V_DIMENSION_CODE := '';
    V_DIMENSION_CN_NAME := '';
    V_DIMENSION_EN_NAME := '';
    V_DIMENSION_SUBCATEGORY_CODE := '';
    V_DIMENSION_SUBCATEGORY_CN_NAME := '';
    V_DIMENSION_SUBCATEGORY_EN_NAME := '';
    V_DIMENSION_SUB_DETAIL_CODE := '';
    V_DIMENSION_SUB_DETAIL_CN_NAME := '';
    V_DIMENSION_SUB_DETAIL_EN_NAME := '';
    V_IN_DIMENSION_CODE := '';
    V_IN_DIMENSION_CN_NAME := '';
    V_IN_DIMENSION_EN_NAME := '';
    V_IN_DIMENSION_SUBCATEGORY_CODE := '';
    V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
    V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
    V_IN_DIMENSION_SUB_DETAIL_CODE := '';
    V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
    V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
	   
	   
	--202401月版本需求新增
    V_PARENTPARTNUMBER := '';
    V_SPART_CODE :='';
    V_SPART_CN_NAME := '';
    V_IN_PARENTPARTNUMBER := '';
    V_IN_SPART_CODE := '';
    V_IN_SPART_CN_NAME := '';
	
	--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
    V_COA_CODE := '';
    V_COA_CN_NAME :='';
    V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
    V_IN_COA_CODE := '';
    V_IN_COA_CN_NAME := '';
	
    --盈利颗粒度的维度时，不需要LV3字段和所有量纲字段
	--IAS，数字能源,ICT不要LV3.5
    ELSIF F_DIMENSION_TYPE = 'P' THEN
       V_LV3_PROD_RND_TEAM_CODE := '';
       V_LV3_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV3_PROD_RND_TEAM_CODE := '';
       V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
       --202407需求新增
	   V_LV4_PROD_RND_TEAM_CODE := '';
       V_LV4_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV4_PROD_RND_TEAM_CODE := '';
       V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	   
       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
	   
	   --202401月版本需求新增
       V_PARENTPARTNUMBER := '';
       V_SPART_CODE :='';
       V_SPART_CN_NAME := '';
       V_IN_PARENTPARTNUMBER := '';
       V_IN_SPART_CODE := '';
       V_IN_SPART_CN_NAME := '';
	
	--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
    V_COA_CODE := '';
    V_COA_CN_NAME :='';
    V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
    V_IN_COA_CODE := '';
    V_IN_COA_CN_NAME := '';
	
    --量纲颗粒度的维度时，不需要L1、L2字段
	--ICT不要COA和LV3.5
    ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'I' THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
	   
	   --202405版本新增
	   V_PROD_CODE := '';
	   V_PROD_CN_NAME := '';
	   V_COA_CODE := '';
	   V_COA_CN_NAME :='';
	   V_IN_PROD_CODE := '';
	   V_IN_PROD_CN_NAME := '';
	   V_IN_COA_CODE := '';
	   V_IN_COA_CN_NAME := '';
	   
	   --202407需求新增
	   V_LV4_PROD_RND_TEAM_CODE := '';
       V_LV4_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV4_PROD_RND_TEAM_CODE := '';
       V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	  
	  --数字能源不要COA和LV3.5
	ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'E' THEN	
		
	   V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
	   
	   --202407需求新增
	   V_LV4_PROD_RND_TEAM_CODE := '';
       V_LV4_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV4_PROD_RND_TEAM_CODE := '';
       V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	  
	   --IAS不要COA
	ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'IAS' THEN	
		
	   V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
	   
	   --202405版本新增
	   V_PROD_CODE := '';
	   V_PROD_CN_NAME := '';
	   V_COA_CODE := '';
	   V_COA_CN_NAME :='';
	   V_IN_PROD_CODE := '';
	   V_IN_PROD_CN_NAME := '';
	   V_IN_COA_CODE := '';
	   V_IN_COA_CN_NAME := '';
	   
	   
    ELSE
      NULL;
    END IF;
	
      --该数据区分国内和海外
      V_SQL := 
        'INSERT INTO BASE_DATA_TEMP(
                VERSION_ID,
                PERIOD_YEAR,
                PERIOD_ID,
                LV0_PROD_RND_TEAM_CODE,
                LV0_PROD_RD_TEAM_CN_NAME,
                LV1_PROD_RND_TEAM_CODE,
                LV1_PROD_RD_TEAM_CN_NAME,'||
                V_LV2_PROD_RND_TEAM_CODE ||
                V_LV2_PROD_RD_TEAM_CN_NAME ||
                V_LV3_PROD_RND_TEAM_CODE ||
                V_LV3_PROD_RD_TEAM_CN_NAME ||
				V_LV4_PROD_RND_TEAM_CODE ||
                V_LV4_PROD_RD_TEAM_CN_NAME ||
                V_L1_NAME ||
                V_L2_NAME ||
                V_DIMENSION_CODE ||
                V_DIMENSION_CN_NAME ||
                V_DIMENSION_EN_NAME||
                V_DIMENSION_SUBCATEGORY_CODE ||
                V_DIMENSION_SUBCATEGORY_CN_NAME ||
                V_DIMENSION_SUBCATEGORY_EN_NAME||
                V_DIMENSION_SUB_DETAIL_CODE ||
                V_DIMENSION_SUB_DETAIL_CN_NAME ||
                V_DIMENSION_SUB_DETAIL_EN_NAME ||
                V_SPART_CODE ||
                V_SPART_CN_NAME ||
				V_COA_CODE ||
                V_COA_CN_NAME ||
				V_SHIPPING_OBJECT_CODE ||
				V_SHIPPING_OBJECT_CN_NAME ||
				V_MANUFACTURE_OBJECT_CODE ||
				V_MANUFACTURE_OBJECT_CN_NAME ||
                'ITEM_CODE,
                ITEM_CN_NAME,
                SHIP_QUANTITY,
                AVG_AMT,
				MANUFACTURE_AMT,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME
                )
            SELECT T.VERSION_ID,
             T.PERIOD_YEAR,
             T.PERIOD_ID,
             T.LV0_PROD_RND_TEAM_CODE,
             T.LV0_PROD_RD_TEAM_CN_NAME,
             T.LV1_PROD_RND_TEAM_CODE,
             T.LV1_PROD_RD_TEAM_CN_NAME,'||
             V_IN_LV2_PROD_RND_TEAM_CODE ||
             V_IN_LV2_PROD_RD_TEAM_CN_NAME ||
             V_IN_LV3_PROD_RND_TEAM_CODE ||
             V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
			 V_IN_LV4_PROD_RND_TEAM_CODE ||
             V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
             V_IN_L1_NAME ||
             V_IN_L2_NAME ||
             V_IN_DIMENSION_CODE ||
             V_IN_DIMENSION_CN_NAME ||
             V_IN_DIMENSION_EN_NAME||
             V_IN_DIMENSION_SUBCATEGORY_CODE ||
             V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
             V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
             V_IN_DIMENSION_SUB_DETAIL_CODE ||
             V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
             V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
             V_PARENTPARTNUMBER ||     --SPART_CODE 和 SPART_CN_NAME 都用 SPART_CODE
             V_PARENTPARTNUMBER ||
			 V_IN_PROD_CODE||  --202405版本新增
			 V_IN_PROD_CN_NAME||
			 V_IN_SHIPPING_OBJECT_CODE ||
			 V_IN_SHIPPING_OBJECT_CN_NAME ||
			 V_IN_MANUFACTURE_OBJECT_CODE ||
			 V_IN_MANUFACTURE_OBJECT_CN_NAME ||
             'T.ITEM_CODE,
             T.ITEM_CN_NAME,
             T.SHIP_QUANTITY,
             T.RMB_AVG_AMT ,
			 T.rmb_cost_amt,
             T.OVERSEA_FLAG, -- 含国内和海外两种数据
             T.LV0_PROD_LIST_CODE,
             T.LV0_PROD_LIST_CN_NAME,
             T.LV0_PROD_LIST_EN_NAME
          FROM '||V_FROM_TABLE ||' T
          --LEFT JOIN '||V_JOIN_TABLE||' T2  -- 11月版本 制造对象不用加密
          --ON T.PRIMARY_ID = T2.PRIMARY_ID
          WHERE T.PERIOD_YEAR >= '||V_BEGIN_YEAR||'
            AND T.PERIOD_YEAR <= '||V_END_YEAR||'
			AND DECODE('''||f_dimension_type||''',''D'',DIMENSION_CODE,''SNULL'') IS NOT NULL 
          ' 
          ;
     DBMS_OUTPUT.PUT_LINE (V_SQL);
     EXECUTE IMMEDIATE V_SQL;

     
       --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '数据插入会话级临时表,颗粒度：'||F_DIMENSION_TYPE||',时点：'||F_CALIBER_FLAG||',产业类型：'||F_INDUSTRY_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');  
   
   
      --再插入一份全球的数据
      V_SQL := 
        'INSERT INTO BASE_DATA_TEMP(
                VERSION_ID,
                PERIOD_YEAR,
                PERIOD_ID,
                LV0_PROD_RND_TEAM_CODE,
                LV0_PROD_RD_TEAM_CN_NAME,
                LV1_PROD_RND_TEAM_CODE,
                LV1_PROD_RD_TEAM_CN_NAME,'||
                V_LV2_PROD_RND_TEAM_CODE ||
                V_LV2_PROD_RD_TEAM_CN_NAME ||
                V_LV3_PROD_RND_TEAM_CODE ||
                V_LV3_PROD_RD_TEAM_CN_NAME ||
				V_LV4_PROD_RND_TEAM_CODE ||
                V_LV4_PROD_RD_TEAM_CN_NAME ||
                V_L1_NAME ||
                V_L2_NAME ||
                V_DIMENSION_CODE ||
                V_DIMENSION_CN_NAME ||
                V_DIMENSION_EN_NAME||
                V_DIMENSION_SUBCATEGORY_CODE ||
                V_DIMENSION_SUBCATEGORY_CN_NAME ||
                V_DIMENSION_SUBCATEGORY_EN_NAME||
                V_DIMENSION_SUB_DETAIL_CODE ||
                V_DIMENSION_SUB_DETAIL_CN_NAME ||
                V_DIMENSION_SUB_DETAIL_EN_NAME ||
                V_SPART_CODE ||
                V_SPART_CN_NAME ||
				V_COA_CODE ||
                V_COA_CN_NAME ||
				V_SHIPPING_OBJECT_CODE ||
			    V_SHIPPING_OBJECT_CN_NAME ||
				V_MANUFACTURE_OBJECT_CODE ||
				V_MANUFACTURE_OBJECT_CN_NAME ||
                'ITEM_CODE,
                ITEM_CN_NAME,
                SHIP_QUANTITY,
                AVG_AMT,
				MANUFACTURE_AMT,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME)
         SELECT T.VERSION_ID,
             T.PERIOD_YEAR,
             T.PERIOD_ID,
             T.LV0_PROD_RND_TEAM_CODE,
             T.LV0_PROD_RD_TEAM_CN_NAME,
             T.LV1_PROD_RND_TEAM_CODE,
             T.LV1_PROD_RD_TEAM_CN_NAME,'||
             V_IN_LV2_PROD_RND_TEAM_CODE ||
             V_IN_LV2_PROD_RD_TEAM_CN_NAME ||
             V_IN_LV3_PROD_RND_TEAM_CODE ||
             V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
			 V_IN_LV4_PROD_RND_TEAM_CODE ||
             V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
             V_IN_L1_NAME ||
             V_IN_L2_NAME ||
             V_IN_DIMENSION_CODE ||
             V_IN_DIMENSION_CN_NAME ||
             V_IN_DIMENSION_EN_NAME||
             V_IN_DIMENSION_SUBCATEGORY_CODE ||
             V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
             V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
             V_IN_DIMENSION_SUB_DETAIL_CODE ||
             V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
             V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
             V_IN_SPART_CODE ||
             V_IN_SPART_CN_NAME ||
			 V_IN_COA_CODE ||
             V_IN_COA_CN_NAME ||
			 V_IN_SHIPPING_OBJECT_CODE ||
			 V_IN_SHIPPING_OBJECT_CN_NAME ||
			 V_IN_MANUFACTURE_OBJECT_CODE ||
			 V_IN_MANUFACTURE_OBJECT_CN_NAME ||
             'T.ITEM_CODE,
             T.ITEM_CN_NAME,
             T.SHIP_QUANTITY,
             T.AVG_AMT,
			 T.MANUFACTURE_AMT,
             ''G'' AS OVERSEA_FLAG, -- 造一份全球数据
             T.LV0_PROD_LIST_CODE,
             T.LV0_PROD_LIST_CN_NAME,
             T.LV0_PROD_LIST_EN_NAME
          FROM BASE_DATA_TEMP T';
     DBMS_OUTPUT.PUT_LINE (V_SQL);
     EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '生成全球的数据插入会话级临时表,颗粒度：'||F_DIMENSION_TYPE||',时点：'||F_CALIBER_FLAG||',产业类型：'||F_INDUSTRY_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');  
   
   
   
   --再插入一份BG为'集团'的数据
      V_SQL := 
        'INSERT INTO BASE_DATA_TEMP(
                VERSION_ID,
                PERIOD_YEAR,
                PERIOD_ID,
                LV0_PROD_RND_TEAM_CODE,
                LV0_PROD_RD_TEAM_CN_NAME,
                LV1_PROD_RND_TEAM_CODE,
                LV1_PROD_RD_TEAM_CN_NAME,'||
                V_LV2_PROD_RND_TEAM_CODE ||
                V_LV2_PROD_RD_TEAM_CN_NAME ||
                V_LV3_PROD_RND_TEAM_CODE ||
                V_LV3_PROD_RD_TEAM_CN_NAME ||
				V_LV4_PROD_RND_TEAM_CODE ||
                V_LV4_PROD_RD_TEAM_CN_NAME ||
                V_L1_NAME ||
                V_L2_NAME ||
                V_DIMENSION_CODE ||
                V_DIMENSION_CN_NAME ||
                V_DIMENSION_EN_NAME||
                V_DIMENSION_SUBCATEGORY_CODE ||
                V_DIMENSION_SUBCATEGORY_CN_NAME ||
                V_DIMENSION_SUBCATEGORY_EN_NAME||
                V_DIMENSION_SUB_DETAIL_CODE ||
                V_DIMENSION_SUB_DETAIL_CN_NAME ||
                V_DIMENSION_SUB_DETAIL_EN_NAME ||
                V_SPART_CODE ||
                V_SPART_CN_NAME ||
				V_COA_CODE ||
				V_COA_CN_NAME ||
				V_SHIPPING_OBJECT_CODE ||
				V_SHIPPING_OBJECT_CN_NAME ||
				V_MANUFACTURE_OBJECT_CODE ||
				V_MANUFACTURE_OBJECT_CN_NAME ||
                'ITEM_CODE,
                ITEM_CN_NAME,
                SHIP_QUANTITY,
                AVG_AMT,
				MANUFACTURE_AMT,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME)
         SELECT T.VERSION_ID,
             T.PERIOD_YEAR,
             T.PERIOD_ID,
             T.LV0_PROD_RND_TEAM_CODE,
             T.LV0_PROD_RD_TEAM_CN_NAME,
             T.LV1_PROD_RND_TEAM_CODE,
             T.LV1_PROD_RD_TEAM_CN_NAME,'||
             V_IN_LV2_PROD_RND_TEAM_CODE ||
             V_IN_LV2_PROD_RD_TEAM_CN_NAME ||
             V_IN_LV3_PROD_RND_TEAM_CODE ||
             V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
			 V_IN_LV4_PROD_RND_TEAM_CODE ||
             V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
             V_IN_L1_NAME ||
             V_IN_L2_NAME ||
             V_IN_DIMENSION_CODE ||
             V_IN_DIMENSION_CN_NAME ||
             V_IN_DIMENSION_EN_NAME||
             V_IN_DIMENSION_SUBCATEGORY_CODE ||
             V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
             V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
             V_IN_DIMENSION_SUB_DETAIL_CODE ||
             V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
             V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
             V_IN_SPART_CODE ||
             V_IN_SPART_CN_NAME ||
			 V_IN_COA_CODE ||
             V_IN_COA_CN_NAME ||
			 V_IN_SHIPPING_OBJECT_CODE ||
			 V_IN_SHIPPING_OBJECT_CN_NAME ||
			 V_IN_MANUFACTURE_OBJECT_CODE ||
			 V_IN_MANUFACTURE_OBJECT_CN_NAME ||
             'T.ITEM_CODE,
             T.ITEM_CN_NAME,
             T.SHIP_QUANTITY,
             T.AVG_AMT,
			 T.MANUFACTURE_AMT,
             T.OVERSEA_FLAG,
             ''GR'' AS LV0_PROD_LIST_CODE,           -- 再生成一份BG为集团的数据
             ''集团'' AS LV0_PROD_LIST_CN_NAME,
             ''GROUP'' AS LV0_PROD_LIST_EN_NAME
          FROM BASE_DATA_TEMP T';
     DBMS_OUTPUT.PUT_LINE (V_SQL);      
     EXECUTE IMMEDIATE V_SQL;
       
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '生成BG为集团的数据插入会话级临时表',
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');  
   
--判断版本表
  IF F_INDUSTRY_FLAG = 'I' THEN 
	--新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
	
  ELSIF F_INDUSTRY_FLAG = 'E' THEN 
	 
	 --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_ENERGY_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 

		
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN 
	 
	 --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_IAS_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
		
  ELSE 
     NULL ;
	 
  END IF;

  --根据不同视角表示, 循环收敛均价, 并且插数
  FOR V_VIEW_NUM IN V_VIEW_BEGIN..V_VIEW_CNT LOOP
  
    --重置变量入参
    V_LV1_PROD_RND_TEAM_CODE := 'LV1_PROD_RND_TEAM_CODE,';
    V_LV1_PROD_RD_TEAM_CN_NAME := 'LV1_PROD_RD_TEAM_CN_NAME,';
    V_LV2_PROD_RND_TEAM_CODE := 'LV2_PROD_RND_TEAM_CODE,';
    V_LV2_PROD_RD_TEAM_CN_NAME := 'LV2_PROD_RD_TEAM_CN_NAME,';
    V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';--7月版本需求新增
    V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
	V_LV4_PROD_RND_TEAM_CODE := 'LV4_PROD_RND_TEAM_CODE,';--7月版本需求新增
    V_LV4_PROD_RD_TEAM_CN_NAME := 'LV4_PROD_RD_TEAM_CN_NAME,';
    V_L1_NAME := 'L1_NAME,';
    V_L2_NAME := 'L2_NAME,';
    V_INSERT_LV1_PROD_RND_TEAM_CODE := ' AND A.LV1_PROD_RND_TEAM_CODE = B.LV1_PROD_RND_TEAM_CODE';
    V_INSERT_LV2_PROD_RND_TEAM_CODE := ' AND A.LV2_PROD_RND_TEAM_CODE = B.LV2_PROD_RND_TEAM_CODE';
    V_INSERT_LV3_PROD_RND_TEAM_CODE := ' AND A.LV3_PROD_RND_TEAM_CODE = B.LV3_PROD_RND_TEAM_CODE';--7月版本需求新增
	V_INSERT_LV4_PROD_RND_TEAM_CODE := ' AND A.LV4_PROD_RND_TEAM_CODE = B.LV4_PROD_RND_TEAM_CODE';--7月版本需求新增
    V_INSERT_L1_NAME := ' AND A.L1_NAME = B.L1_NAME';
    V_INSERT_L2_NAME := ' AND A.L2_NAME = B.L2_NAME';
    V_IN_LV1_PROD_RND_TEAM_CODE := 'A.LV1_PROD_RND_TEAM_CODE,';
    V_IN_LV1_PROD_RD_TEAM_CN_NAME :='A.LV1_PROD_RD_TEAM_CN_NAME,';
    V_IN_LV2_PROD_RND_TEAM_CODE := 'A.LV2_PROD_RND_TEAM_CODE,';
    V_IN_LV2_PROD_RD_TEAM_CN_NAME :='A.LV2_PROD_RD_TEAM_CN_NAME,';
    V_IN_LV3_PROD_RND_TEAM_CODE := 'A.LV3_PROD_RND_TEAM_CODE,';--7月版本需求新增
    V_IN_LV3_PROD_RD_TEAM_CN_NAME :='A.LV3_PROD_RD_TEAM_CN_NAME,';
	V_IN_LV4_PROD_RND_TEAM_CODE := 'A.LV4_PROD_RND_TEAM_CODE,';--7月版本需求新增
    V_IN_LV4_PROD_RD_TEAM_CN_NAME :='A.LV4_PROD_RD_TEAM_CN_NAME,';
    V_IN_L1_NAME := 'A.L1_NAME,';
    V_IN_L2_NAME := 'A.L2_NAME,';
    
    --9月版本需求新增量纲
    V_DIMENSION_CODE := 'DIMENSION_CODE,';
    V_DIMENSION_CN_NAME := 'DIMENSION_CN_NAME,';
    V_DIMENSION_EN_NAME := 'DIMENSION_EN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_EN_NAME := 'DIMENSION_SUBCATEGORY_EN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME := 'DIMENSION_SUB_DETAIL_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_EN_NAME := 'DIMENSION_SUB_DETAIL_EN_NAME,';
    V_IN_DIMENSION_CODE := 'A.DIMENSION_CODE,';
    V_IN_DIMENSION_CN_NAME := 'A.DIMENSION_CN_NAME,';
    V_IN_DIMENSION_EN_NAME := 'A.DIMENSION_EN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_CODE := 'A.DIMENSION_SUBCATEGORY_CODE,';
    V_IN_DIMENSION_SUBCATEGORY_CN_NAME := 'A.DIMENSION_SUBCATEGORY_CN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_EN_NAME := 'A.DIMENSION_SUBCATEGORY_EN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_CODE := 'A.DIMENSION_SUB_DETAIL_CODE,';
    V_IN_DIMENSION_SUB_DETAIL_CN_NAME := 'A.DIMENSION_SUB_DETAIL_CN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_EN_NAME := 'A.DIMENSION_SUB_DETAIL_EN_NAME,';
    V_INSERT_DIMENSION_CODE := ' AND A.DIMENSION_CODE = B.DIMENSION_CODE';
    V_INSERT_DIMENSION_SUBCATEGORY_CODE := ' AND A.DIMENSION_SUBCATEGORY_CODE = B.DIMENSION_SUBCATEGORY_CODE';
    V_INSERT_DIMENSION_SUB_DETAIL_CODE := ' AND A.DIMENSION_SUB_DETAIL_CODE = B.DIMENSION_SUB_DETAIL_CODE';

	
	--11月版本新增制造对象
		V_SHIPPING_OBJECT_CODE := 'SHIPPING_OBJECT_CODE,';
		V_SHIPPING_OBJECT_CN_NAME := 'SHIPPING_OBJECT_CN_NAME,' ;
		V_MANUFACTURE_OBJECT_CODE  := 'MANUFACTURE_OBJECT_CODE,';
		V_MANUFACTURE_OBJECT_CN_NAME  := 'MANUFACTURE_OBJECT_CN_NAME,';
		V_IN_SHIPPING_OBJECT_CODE  := 'A.SHIPPING_OBJECT_CODE,';
		V_IN_SHIPPING_OBJECT_CN_NAME := 'A.SHIPPING_OBJECT_CN_NAME,';
		V_IN_MANUFACTURE_OBJECT_CODE := 'A.MANUFACTURE_OBJECT_CODE,';
		V_IN_MANUFACTURE_OBJECT_CN_NAME := 'A.MANUFACTURE_OBJECT_CN_NAME,';
		V_INSERT_SHIPPING_OBJECT_CODE := ' AND A.SHIPPING_OBJECT_CODE = B.SHIPPING_OBJECT_CODE';
		V_INSERT_MANUFACTURE_OBJECT_CODE := ' AND A.MANUFACTURE_OBJECT_CODE = B.MANUFACTURE_OBJECT_CODE';

    --202401月版本需求新增量纲
        V_SPART_CODE := ' SPART_CODE,';
        V_SPART_CN_NAME := 'SPART_CN_NAME,';
        V_IN_SPART_CODE := 'A.SPART_CODE ,';
        V_IN_SPART_CN_NAME := 'A.SPART_CN_NAME ,';
        V_INSERT_SPART_CODE := ' AND A.SPART_CODE = B.SPART_CODE ';
		
				
	 --202405版本新增
    V_PROD_CODE := 'PROD_CODE,';
	V_PROD_CN_NAME := 'PROD_CN_NAME,';
    V_COA_CODE :='COA_CODE,';
    V_COA_CN_NAME :='COA_CN_NAME,';
    V_IN_PROD_CODE := 'A.PROD_CODE,';
	V_IN_PROD_CN_NAME := 'A.PROD_CN_NAME,';
    V_IN_COA_CODE := 'A.COA_CODE,';
    V_IN_COA_CN_NAME := 'A.COA_CN_NAME,';
	V_INSERT_COA_CODE := ' AND A.COA_CODE = B.COA_CODE';
 
    /*视角1时,如果是通用颗粒度和盈利颗粒度,不需要LV1、LV2、LV3、L1_NAME、L2_NAME和所有量纲字段
    IF (V_VIEW_NUM = 0 AND F_DIMENSION_TYPE IN ('U','P')) THEN*/
	/*视角1时,如果是通用颗粒度,不需要LV1、LV2、LV3、L1_NAME、L2_NAME和所有量纲字段*/
	IF (V_VIEW_NUM = 0 AND F_DIMENSION_TYPE = 'U' ) THEN 
      V_LV1_PROD_RND_TEAM_CODE := '';
      V_LV1_PROD_RD_TEAM_CN_NAME := '';
      V_LV2_PROD_RND_TEAM_CODE := '';
      V_LV2_PROD_RD_TEAM_CN_NAME := '';
      V_LV3_PROD_RND_TEAM_CODE := '';--7月版本需求新增
      V_LV3_PROD_RD_TEAM_CN_NAME := '';
	  V_LV4_PROD_RND_TEAM_CODE := '';--7月版本需求新增
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_LV1_PROD_RND_TEAM_CODE := '';
      V_IN_LV1_PROD_RD_TEAM_CN_NAME := '';
      V_IN_LV2_PROD_RND_TEAM_CODE := '';
      V_IN_LV2_PROD_RD_TEAM_CN_NAME := '';
      V_IN_LV3_PROD_RND_TEAM_CODE := '';
      V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
	  V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_LV1_PROD_RND_TEAM_CODE := '';
      V_INSERT_LV2_PROD_RND_TEAM_CODE := '';
      V_INSERT_LV3_PROD_RND_TEAM_CODE := '';
	  V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
      
      --9月版本需求新增
      V_DIMENSION_CODE := '';
      V_DIMENSION_CN_NAME := '';
      V_DIMENSION_EN_NAME := '';
      V_DIMENSION_SUBCATEGORY_CODE := '';
      V_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_DIMENSION_SUB_DETAIL_CODE := '';
      V_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_IN_DIMENSION_CODE := '';
      V_IN_DIMENSION_CN_NAME := '';
      V_IN_DIMENSION_EN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_CODE := '';
      V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_CODE := '';
      V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_INSERT_DIMENSION_CODE := '';
      V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
      V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';

    --202401月版本需求新增量纲
        V_SPART_CODE := '';
        V_SPART_CN_NAME := '';
        V_IN_SPART_CODE := '';
        V_IN_SPART_CN_NAME := '';
        V_INSERT_SPART_CODE := '';
		
	--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';	


	
	

    --视角1时,如果是量纲颗粒度,不需要LV2、LV3、L1_NAME、L2_NAME和量纲子类、量纲子类明细字段
    ELSIF (V_VIEW_NUM = 0 AND F_DIMENSION_TYPE IN ('D') ) THEN
      V_LV2_PROD_RND_TEAM_CODE := '';
      V_LV2_PROD_RD_TEAM_CN_NAME := '';
      V_LV3_PROD_RND_TEAM_CODE := '';
      V_LV3_PROD_RD_TEAM_CN_NAME := '';
	  V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_LV2_PROD_RND_TEAM_CODE := '';
      V_IN_LV2_PROD_RD_TEAM_CN_NAME := '';
      V_IN_LV3_PROD_RND_TEAM_CODE := '';
      V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
	  V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_LV2_PROD_RND_TEAM_CODE := '';
      V_INSERT_LV3_PROD_RND_TEAM_CODE := '';
      V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
      
      V_DIMENSION_SUBCATEGORY_CODE := '';
      V_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_DIMENSION_SUB_DETAIL_CODE := '';
      V_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_CODE := '';
      V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_CODE := '';
      V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
      V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
    --202401月版本需求新增量纲
        V_SPART_CODE := '';
        V_SPART_CN_NAME := '';
        V_IN_SPART_CODE := '';
        V_IN_SPART_CN_NAME := '';
        V_INSERT_SPART_CODE := '';
		
	--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';	


	
    /*视角2时,如果是通用颗粒度和盈利颗粒度,不需要LV2、LV3、L1_NAME、L2_NAME和所有量纲字段
    ELSIF (V_VIEW_NUM = 1 AND F_DIMENSION_TYPE IN ('U','P')) THEN*/
	/*视角2时,如果是通用颗粒度,不需要LV2、LV3、L1_NAME、L2_NAME和所有量纲字段*/
	ELSIF (V_VIEW_NUM = 1 AND F_DIMENSION_TYPE ='U' ) THEN /*剔除盈利视角1、2、3 杨泽宝 20240130*/
      V_LV2_PROD_RND_TEAM_CODE := '';
      V_LV2_PROD_RD_TEAM_CN_NAME := '';
      V_LV3_PROD_RND_TEAM_CODE := '';
      V_LV3_PROD_RD_TEAM_CN_NAME := '';
	  V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_LV2_PROD_RND_TEAM_CODE := '';
      V_IN_LV2_PROD_RD_TEAM_CN_NAME := '';
      V_IN_LV3_PROD_RND_TEAM_CODE := '';
      V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
      V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_LV2_PROD_RND_TEAM_CODE := '';
      V_INSERT_LV3_PROD_RND_TEAM_CODE := '';
      V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
      
      V_DIMENSION_CODE := '';
      V_DIMENSION_CN_NAME := '';
      V_DIMENSION_EN_NAME := '';
      V_DIMENSION_SUBCATEGORY_CODE := '';
      V_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_DIMENSION_SUB_DETAIL_CODE := '';
      V_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_IN_DIMENSION_CODE := '';
      V_IN_DIMENSION_CN_NAME := '';
      V_IN_DIMENSION_EN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_CODE := '';
      V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_CODE := '';
      V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_INSERT_DIMENSION_CODE := '';
      V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
      V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
    --202401月版本需求新增量纲
        V_SPART_CODE := '';
        V_SPART_CN_NAME := '';
        V_IN_SPART_CODE := '';
        V_IN_SPART_CN_NAME := '';
        V_INSERT_SPART_CODE := '';
		
	--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';	

	
	
    --视角2时,如果是量纲颗粒度,不需要LV2、LV3、L1_NAME、L2_NAME和量纲子类明细字段
    ELSIF (V_VIEW_NUM = 1 AND F_DIMENSION_TYPE IN ('D') ) THEN
      V_LV2_PROD_RND_TEAM_CODE := '';
      V_LV2_PROD_RD_TEAM_CN_NAME := '';
      V_LV3_PROD_RND_TEAM_CODE := '';
      V_LV3_PROD_RD_TEAM_CN_NAME := '';
	  V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_LV2_PROD_RND_TEAM_CODE := '';
      V_IN_LV2_PROD_RD_TEAM_CN_NAME := '';
      V_IN_LV3_PROD_RND_TEAM_CODE := '';
      V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
      V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_LV2_PROD_RND_TEAM_CODE := '';
      V_INSERT_LV3_PROD_RND_TEAM_CODE := '';
      V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
      
      V_DIMENSION_SUB_DETAIL_CODE := '';
      V_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_CODE := '';
      V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
    --202401月版本需求新增量纲
        V_SPART_CODE := '';
        V_SPART_CN_NAME := '';
        V_IN_SPART_CODE := '';
        V_IN_SPART_CN_NAME := '';
        V_INSERT_SPART_CODE := '';
		
	--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';	

	
	
	
    /*视角3时,如果是通用颗粒度和盈利颗粒度, 不需要LV3、L1_NAME、L2_NAME和所有量纲字段
    ELSIF (V_VIEW_NUM = 2 AND F_DIMENSION_TYPE IN ('U','P')) THEN */
	/*视角3时,如果是通用颗粒度, 不需要LV3、L1_NAME、L2_NAME和所有量纲字段*/
	ELSIF (V_VIEW_NUM = 2 AND F_DIMENSION_TYPE = 'U' ) THEN /*剔除盈利视角1、2、3 杨泽宝 20240130*/
      V_LV3_PROD_RND_TEAM_CODE := '';
      V_LV3_PROD_RD_TEAM_CN_NAME := '';
	  V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_LV3_PROD_RND_TEAM_CODE := '';
      V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
	  V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_LV3_PROD_RND_TEAM_CODE := '';
	  V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
      
      V_DIMENSION_CODE := '';
      V_DIMENSION_CN_NAME := '';
      V_DIMENSION_EN_NAME := '';
      V_DIMENSION_SUBCATEGORY_CODE := '';
      V_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_DIMENSION_SUB_DETAIL_CODE := '';
      V_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_IN_DIMENSION_CODE := '';
      V_IN_DIMENSION_CN_NAME := '';
      V_IN_DIMENSION_EN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_CODE := '';
      V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_CODE := '';
      V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_INSERT_DIMENSION_CODE := '';
      V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
      V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
    --202401月版本需求新增量纲
        V_SPART_CODE := '';
        V_SPART_CN_NAME := '';
        V_IN_SPART_CODE := '';
        V_IN_SPART_CN_NAME := '';
        V_INSERT_SPART_CODE := '';
		
	--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';	


	
	
    --视角3时,如果是量纲颗粒度,不需要LV2、LV3、L1_NAME、L2_NAME字段
    ELSIF (V_VIEW_NUM = 2 AND F_DIMENSION_TYPE IN ('D')) THEN
      V_LV2_PROD_RND_TEAM_CODE := '';
      V_LV2_PROD_RD_TEAM_CN_NAME := '';
      V_LV3_PROD_RND_TEAM_CODE := '';
      V_LV3_PROD_RD_TEAM_CN_NAME := '';
	   V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_LV2_PROD_RND_TEAM_CODE := '';
      V_IN_LV2_PROD_RD_TEAM_CN_NAME := '';
      V_IN_LV3_PROD_RND_TEAM_CODE := '';
      V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
	  V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_LV2_PROD_RND_TEAM_CODE := '';
      V_INSERT_LV3_PROD_RND_TEAM_CODE := '';
	  V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
    --202401月版本需求新增量纲
        V_SPART_CODE := '';
        V_SPART_CN_NAME := '';
        V_IN_SPART_CODE := '';
        V_IN_SPART_CN_NAME := '';
        V_INSERT_SPART_CODE := '';
		
	--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';	

    --视角4时, 如果是通用颗粒度不需要L1_NAME、L2_NAME和所有量纲字段
    ELSIF (V_VIEW_NUM = 3 AND F_DIMENSION_TYPE = 'U') THEN
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
      V_DIMENSION_CODE := '';
      V_DIMENSION_CN_NAME := '';
      V_DIMENSION_EN_NAME := '';
      V_DIMENSION_SUBCATEGORY_CODE := '';
      V_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_DIMENSION_SUB_DETAIL_CODE := '';
      V_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_IN_DIMENSION_CODE := '';
      V_IN_DIMENSION_CN_NAME := '';
      V_IN_DIMENSION_EN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_CODE := '';
      V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_CODE := '';
      V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_INSERT_DIMENSION_CODE := '';
      V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
      V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
    --202401月版本需求新增量纲
        V_SPART_CODE := '';
        V_SPART_CN_NAME := '';
        V_IN_SPART_CODE := '';
        V_IN_SPART_CN_NAME := '';
        V_INSERT_SPART_CODE := '';
		
	--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';	
	
	V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_INSERT_LV4_PROD_RND_TEAM_CODE := '';

    --视角4时, 如果是盈利颗粒度不需要LV3、L2_NAME和所有量纲字段
    ELSIF (V_VIEW_NUM = 3 AND F_DIMENSION_TYPE = 'P') THEN
      V_LV3_PROD_RND_TEAM_CODE := '';
      V_LV3_PROD_RD_TEAM_CN_NAME := '';
      V_L2_NAME := '';
      V_IN_LV3_PROD_RND_TEAM_CODE := '';
      V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_LV3_PROD_RND_TEAM_CODE := '';
      V_INSERT_L2_NAME := '';
      
      V_DIMENSION_CODE := '';
      V_DIMENSION_CN_NAME := '';
      V_DIMENSION_EN_NAME := '';
      V_DIMENSION_SUBCATEGORY_CODE := '';
      V_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_DIMENSION_SUB_DETAIL_CODE := '';
      V_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_IN_DIMENSION_CODE := '';
      V_IN_DIMENSION_CN_NAME := '';
      V_IN_DIMENSION_EN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_CODE := '';
      V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_CODE := '';
      V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_INSERT_DIMENSION_CODE := '';
      V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
      V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
    --202401月版本需求新增量纲
        V_SPART_CODE := '';
        V_SPART_CN_NAME := '';
        V_IN_SPART_CODE := '';
        V_IN_SPART_CN_NAME := '';
        V_INSERT_SPART_CODE := '';
		
	--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';	

		V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	
    --视角4时,如果是量纲颗粒度,不需要LV3、L1_NAME、L2_NAME和量纲子类、量纲子类明细字段
    ELSIF (V_VIEW_NUM = 3 AND F_DIMENSION_TYPE IN ('D')) THEN
      V_LV3_PROD_RND_TEAM_CODE := '';
      V_LV3_PROD_RD_TEAM_CN_NAME := '';
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_LV3_PROD_RND_TEAM_CODE := '';
      V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_LV3_PROD_RND_TEAM_CODE := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
      
      V_DIMENSION_SUBCATEGORY_CODE := '';
      V_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_DIMENSION_SUB_DETAIL_CODE := '';
      V_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_CODE := '';
      V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_CODE := '';
      V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
      V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
    --202401月版本需求新增量纲
        V_SPART_CODE := '';
        V_SPART_CN_NAME := '';
        V_IN_SPART_CODE := '';
        V_IN_SPART_CN_NAME := '';
        V_INSERT_SPART_CODE := '';
		
	--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';	
	
		V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	
    --视角5时, 如果是盈利颗粒度不需要LV3和所有量纲字段
    ELSIF (V_VIEW_NUM = 4 AND F_DIMENSION_TYPE = 'P') THEN
      V_LV3_PROD_RND_TEAM_CODE := '';
      V_LV3_PROD_RD_TEAM_CN_NAME := '';
      V_IN_LV3_PROD_RND_TEAM_CODE := '';
      V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
      V_INSERT_LV3_PROD_RND_TEAM_CODE := '';
      
      V_DIMENSION_CODE := '';
      V_DIMENSION_CN_NAME := '';
      V_DIMENSION_EN_NAME := '';
      V_DIMENSION_SUBCATEGORY_CODE := '';
      V_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_DIMENSION_SUB_DETAIL_CODE := '';
      V_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_IN_DIMENSION_CODE := '';
      V_IN_DIMENSION_CN_NAME := '';
      V_IN_DIMENSION_EN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_CODE := '';
      V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_CODE := '';
      V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_INSERT_DIMENSION_CODE := '';
      V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
      V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
    --202401月版本需求新增量纲
        V_SPART_CODE := '';
        V_SPART_CN_NAME := '';
        V_IN_SPART_CODE := '';
        V_IN_SPART_CN_NAME := '';
        V_INSERT_SPART_CODE := '';
		
		--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';	
	
		V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	
	    --视角4时, 如果是通用颗粒度不需要L1_NAME、L2_NAME和所有量纲字段
    ELSIF (V_VIEW_NUM = 4 AND F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'IAS') THEN
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
      V_DIMENSION_CODE := '';
      V_DIMENSION_CN_NAME := '';
      V_DIMENSION_EN_NAME := '';
      V_DIMENSION_SUBCATEGORY_CODE := '';
      V_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_DIMENSION_SUB_DETAIL_CODE := '';
      V_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_IN_DIMENSION_CODE := '';
      V_IN_DIMENSION_CN_NAME := '';
      V_IN_DIMENSION_EN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_CODE := '';
      V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_CODE := '';
      V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_INSERT_DIMENSION_CODE := '';
      V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
      V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
    --202401月版本需求新增量纲
        V_SPART_CODE := '';
        V_SPART_CN_NAME := '';
        V_IN_SPART_CODE := '';
        V_IN_SPART_CN_NAME := '';
        V_INSERT_SPART_CODE := '';
		
	--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';	
	

	
	
    --视角5时,如果是量纲颗粒度,不需要LV3、L1_NAME、L2_NAME和量纲子类明细字段
    ELSIF (V_VIEW_NUM = 4 AND F_DIMENSION_TYPE IN ('D')) THEN
      V_LV3_PROD_RND_TEAM_CODE := '';
      V_LV3_PROD_RD_TEAM_CN_NAME := '';
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_LV3_PROD_RND_TEAM_CODE := '';
      V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_LV3_PROD_RND_TEAM_CODE := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
      
      V_DIMENSION_SUB_DETAIL_CODE := '';
      V_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_CODE := '';
      V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
    --202401月版本需求新增量纲
        V_SPART_CODE := '';
        V_SPART_CN_NAME := '';
        V_IN_SPART_CODE := '';
        V_IN_SPART_CN_NAME := '';
        V_INSERT_SPART_CODE := '';
		
		--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';	
	
		V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_INSERT_LV4_PROD_RND_TEAM_CODE := '';

    --视角6时,如果是量纲颗粒度,不需要LV3、L1_NAME、L2_NAME字段
    ELSIF (V_VIEW_NUM = 5 AND F_DIMENSION_TYPE IN ('D')) THEN
      V_LV3_PROD_RND_TEAM_CODE := '';
      V_LV3_PROD_RD_TEAM_CN_NAME := '';
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_LV3_PROD_RND_TEAM_CODE := '';
      V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_LV3_PROD_RND_TEAM_CODE := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
    --202401月版本需求新增量纲
        V_SPART_CODE := '';
        V_SPART_CN_NAME := '';
        V_IN_SPART_CODE := '';
        V_IN_SPART_CN_NAME := '';
        V_INSERT_SPART_CODE := '';
		
		--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';	
	
		V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_INSERT_LV4_PROD_RND_TEAM_CODE := '';

    --视角7时,如果是量纲颗粒度,不需要L1_NAME、L2_NAME和量纲子类、量纲子类明细字段
    ELSIF (V_VIEW_NUM = 6 AND F_DIMENSION_TYPE IN ('D')) THEN
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
      
      V_DIMENSION_SUBCATEGORY_CODE := '';
      V_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_DIMENSION_SUB_DETAIL_CODE := '';
      V_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_CODE := '';
      V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_CODE := '';
      V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
      V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
    --202401月版本需求新增量纲
        V_SPART_CODE := '';
        V_SPART_CN_NAME := '';
        V_IN_SPART_CODE := '';
        V_IN_SPART_CN_NAME := '';
        V_INSERT_SPART_CODE := '';
		
		--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';	
	
		V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_INSERT_LV4_PROD_RND_TEAM_CODE := '';

    --视角8时,如果是量纲颗粒度,不需要L1_NAME、L2_NAME和量纲子类明细字段
    ELSIF (V_VIEW_NUM = 7 AND F_DIMENSION_TYPE IN ('D')) THEN
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
      
      V_DIMENSION_SUB_DETAIL_CODE := '';
      V_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_CODE := '';
      V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
      V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
    --202401月版本需求新增量纲
        V_SPART_CODE := '';
        V_SPART_CN_NAME := '';
        V_IN_SPART_CODE := '';
        V_IN_SPART_CN_NAME := '';
        V_INSERT_SPART_CODE := '';
		
		--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';
	
		V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	
    --视角9时,如果是量纲颗粒度,不需要L1_NAME、L2_NAME字段
    ELSIF (V_VIEW_NUM = 8 AND F_DIMENSION_TYPE IN ('D')) THEN
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
    --202401月版本需求新增量纲
        V_SPART_CODE := '';
        V_SPART_CN_NAME := '';
        V_IN_SPART_CODE := '';
        V_IN_SPART_CN_NAME := '';
        V_INSERT_SPART_CODE := '';
		
		--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';
	

    --202401月版本需求新增量纲视角
	
		V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	  
	  
    ELSIF (V_VIEW_NUM = 9 AND F_DIMENSION_TYPE IN ('D')) THEN   --参考 V_VIEW_NUM = 2
      V_LV2_PROD_RND_TEAM_CODE := '';
      V_LV2_PROD_RD_TEAM_CN_NAME := '';
      V_LV3_PROD_RND_TEAM_CODE := '';
      V_LV3_PROD_RD_TEAM_CN_NAME := '';
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_LV2_PROD_RND_TEAM_CODE := '';
      V_IN_LV2_PROD_RD_TEAM_CN_NAME := '';
      V_IN_LV3_PROD_RND_TEAM_CODE := '';
      V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_LV2_PROD_RND_TEAM_CODE := '';
      V_INSERT_LV3_PROD_RND_TEAM_CODE := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
	  
	  	--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';
	
		V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	  
    ELSIF (V_VIEW_NUM = 10 AND F_DIMENSION_TYPE IN ('D')) THEN  --参考 V_VIEW_NUM = 5
      V_LV3_PROD_RND_TEAM_CODE := '';
      V_LV3_PROD_RD_TEAM_CN_NAME := '';
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_LV3_PROD_RND_TEAM_CODE := '';
      V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_LV3_PROD_RND_TEAM_CODE := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
	  
	  	--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';
	
		V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	
    ELSIF (V_VIEW_NUM = 11 AND F_DIMENSION_TYPE IN ('D') AND F_INDUSTRY_FLAG IN ('I','E') ) THEN   --参考 V_VIEW_NUM = 8
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
	  
	--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';
	
		V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	  
	    ELSIF (V_VIEW_NUM = 11 AND F_DIMENSION_TYPE IN ('D') AND F_INDUSTRY_FLAG IN ('IAS') ) THEN   --参考 V_VIEW_NUM = 8
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
	  
	--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';
	
	V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	  
	 
    ELSIF (V_VIEW_NUM = 12 AND F_DIMENSION_TYPE IN ('D') AND F_INDUSTRY_FLAG = 'E' ) THEN   --参考 V_VIEW_NUM = 8
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
	  
	  V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	  
	      ELSIF (V_VIEW_NUM = 12 AND F_DIMENSION_TYPE IN ('D') AND F_INDUSTRY_FLAG = 'E' ) THEN   --参考 V_VIEW_NUM = 8
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
	  
	  V_LV4_PROD_RND_TEAM_CODE := '';
      V_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_IN_LV4_PROD_RND_TEAM_CODE := '';
      V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	  V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	
	    ELSIF (V_VIEW_NUM = 12 AND F_DIMENSION_TYPE IN ('D') AND F_INDUSTRY_FLAG = 'IAS' ) THEN   --参考 V_VIEW_NUM = 8
      V_L1_NAME := '';
      V_L2_NAME := '';
      V_IN_L1_NAME := '';
      V_IN_L2_NAME := '';
      V_INSERT_L1_NAME := '';
      V_INSERT_L2_NAME := '';
	  
	  	--202405版本新增
	V_PROD_CODE := '';
	V_PROD_CN_NAME := '';
	V_COA_CODE := '';
	V_COA_CN_NAME :='';
	V_IN_PROD_CODE := '';
	V_IN_PROD_CN_NAME := '';
	V_IN_COA_CODE := '';
	V_IN_COA_CN_NAME := '';
	V_INSERT_COA_CODE := '';
	 
	 ELSE 
	  NULL;
    END IF;
	


     --创建ITEM月卷积发货额临时表
    DROP TABLE IF EXISTS VIEW_DATA_TEMP;
    CREATE TEMPORARY TABLE VIEW_DATA_TEMP (
        VERSION_ID BIGINT,
        PERIOD_YEAR BIGINT,
        PERIOD_ID BIGINT,
        LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
        LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
        LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
        --7月版本需求
        LV3_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
        --202407月版本需求
        LV4_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV4_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
        L1_NAME VARCHAR(100),
        L2_NAME VARCHAR(100),
        --9月版本需求新增量纲
        DIMENSION_CODE    VARCHAR(100),
        DIMENSION_CN_NAME    VARCHAR(100),
        DIMENSION_EN_NAME    VARCHAR(100),
        DIMENSION_SUBCATEGORY_CODE    VARCHAR(100),
        DIMENSION_SUBCATEGORY_CN_NAME    VARCHAR(100),
        DIMENSION_SUBCATEGORY_EN_NAME    VARCHAR(100),
        DIMENSION_SUB_DETAIL_CODE    VARCHAR(100),
        DIMENSION_SUB_DETAIL_CN_NAME    VARCHAR(200),
        DIMENSION_SUB_DETAIL_EN_NAME    VARCHAR(200),
        SPART_CODE VARCHAR(200),
        SPART_CN_NAME VARCHAR(200),
		COA_CODE VARCHAR(200),
        COA_CN_NAME VARCHAR(200),
		SHIPPING_OBJECT_CODE VARCHAR(200),
		SHIPPING_OBJECT_CN_NAME VARCHAR(200),
		MANUFACTURE_OBJECT_CODE VARCHAR(200),
		MANUFACTURE_OBJECT_CN_NAME VARCHAR(200),
        ITEM_CODE CHARACTER VARYING(50),
        ITEM_CN_NAME CHARACTER VARYING(500),
        SHIP_QUANTITY NUMERIC,
        AVG_AMT NUMERIC,
		MANUFACTURE_AMT NUMERIC,
        VIEW_FLAG VARCHAR(2),
        OVERSEA_FLAG VARCHAR(2),
        LV0_PROD_LIST_CODE VARCHAR(50),
        LV0_PROD_LIST_CN_NAME VARCHAR(200),
        LV0_PROD_LIST_EN_NAME VARCHAR(200)
    )
    ON COMMIT PRESERVE ROWS 
    DISTRIBUTE BY ROUNDROBIN;

    
    --插入分视角下计算ITEM的月卷积额临时表
    V_SQL := 
    'INSERT INTO VIEW_DATA_TEMP
      (VERSION_ID,
       PERIOD_YEAR,
       PERIOD_ID,
       LV0_PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME,'||
       V_LV1_PROD_RND_TEAM_CODE ||
       V_LV1_PROD_RD_TEAM_CN_NAME ||
       V_LV2_PROD_RND_TEAM_CODE ||
       V_LV2_PROD_RD_TEAM_CN_NAME ||
       V_LV3_PROD_RND_TEAM_CODE ||
       V_LV3_PROD_RD_TEAM_CN_NAME ||
	   V_LV4_PROD_RND_TEAM_CODE ||
       V_LV4_PROD_RD_TEAM_CN_NAME ||
       V_L1_NAME ||
       V_L2_NAME ||
       V_DIMENSION_CODE ||
       V_DIMENSION_CN_NAME ||
       V_DIMENSION_EN_NAME ||
       V_DIMENSION_SUBCATEGORY_CODE ||
       V_DIMENSION_SUBCATEGORY_CN_NAME ||
       V_DIMENSION_SUBCATEGORY_EN_NAME ||
       V_DIMENSION_SUB_DETAIL_CODE ||
       V_DIMENSION_SUB_DETAIL_CN_NAME ||
       V_DIMENSION_SUB_DETAIL_EN_NAME ||
       V_SPART_CODE ||
       V_SPART_CN_NAME ||
	   V_COA_CODE ||
       V_COA_CN_NAME ||
	   V_SHIPPING_OBJECT_CODE ||
	   V_SHIPPING_OBJECT_CN_NAME ||
	   V_MANUFACTURE_OBJECT_CODE ||
	   V_MANUFACTURE_OBJECT_CN_NAME ||
       'ITEM_CODE,
       ITEM_CN_NAME,
       SHIP_QUANTITY,
       AVG_AMT,
	   MANUFACTURE_AMT,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME,
       LV0_PROD_LIST_EN_NAME,
       VIEW_FLAG)
      SELECT A.VERSION_ID,
             A.PERIOD_YEAR,
             A.PERIOD_ID,
             A.LV0_PROD_RND_TEAM_CODE,
             A.LV0_PROD_RD_TEAM_CN_NAME,'||
             V_IN_LV1_PROD_RND_TEAM_CODE ||
             V_IN_LV1_PROD_RD_TEAM_CN_NAME ||
             V_IN_LV2_PROD_RND_TEAM_CODE ||
             V_IN_LV2_PROD_RD_TEAM_CN_NAME ||
             V_IN_LV3_PROD_RND_TEAM_CODE ||
             V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
             V_IN_LV4_PROD_RND_TEAM_CODE ||
             V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
             V_IN_L1_NAME ||
             V_IN_L2_NAME ||
             V_IN_DIMENSION_CODE ||
             V_IN_DIMENSION_CN_NAME ||
             V_IN_DIMENSION_EN_NAME ||
             V_IN_DIMENSION_SUBCATEGORY_CODE ||
             V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
             V_IN_DIMENSION_SUBCATEGORY_EN_NAME ||
             V_IN_DIMENSION_SUB_DETAIL_CODE ||
             V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
             V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
             V_IN_SPART_CODE ||
             V_IN_SPART_CN_NAME ||
			 V_IN_COA_CODE ||
             V_IN_COA_CN_NAME ||
			V_IN_SHIPPING_OBJECT_CODE ||
			V_IN_SHIPPING_OBJECT_CN_NAME ||
			V_IN_MANUFACTURE_OBJECT_CODE ||
			V_IN_MANUFACTURE_OBJECT_CN_NAME ||
             'A.ITEM_CODE,
             A.ITEM_CN_NAME,
             SUM(A.SHIP_QUANTITY) AS SHIP_QUANTITY,
             A.AVG_AMT ,
			 SUM(A.MANUFACTURE_AMT),
             A.OVERSEA_FLAG,
             A.LV0_PROD_LIST_CODE,
             A.LV0_PROD_LIST_CN_NAME,
             A.LV0_PROD_LIST_EN_NAME,
			'||V_VIEW_NUM ||'  AS VIEW_FLAG
        FROM BASE_DATA_TEMP A 
       WHERE A.MANUFACTURE_AMT > 0 AND A.SHIP_QUANTITY > 0  -- 取金额为非负数的数据
         AND LV1_PROD_RND_TEAM_CODE NOT IN (''101764'',''100005'',''135741'',''104237'',''133341'') -- 删除LV1重量级团队是：NP&S公共、ICT服务与软件、IRB直管团队、OPMT、行业咨询与应用集成的数据
       GROUP BY A.VERSION_ID,
                A.LV0_PROD_RND_TEAM_CODE,
                A.LV0_PROD_RD_TEAM_CN_NAME,' ||
                V_IN_LV1_PROD_RND_TEAM_CODE ||
                V_IN_LV1_PROD_RD_TEAM_CN_NAME ||
                V_IN_LV2_PROD_RND_TEAM_CODE ||
                V_IN_LV2_PROD_RD_TEAM_CN_NAME ||
                V_IN_LV3_PROD_RND_TEAM_CODE ||
                V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
				V_IN_LV4_PROD_RND_TEAM_CODE ||
                V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
                V_IN_L1_NAME ||
                V_IN_L2_NAME ||
                
                V_IN_DIMENSION_CODE ||
                V_IN_DIMENSION_CN_NAME ||
                V_IN_DIMENSION_EN_NAME ||
                V_IN_DIMENSION_SUBCATEGORY_CODE ||
                V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
                V_IN_DIMENSION_SUBCATEGORY_EN_NAME ||
                V_IN_DIMENSION_SUB_DETAIL_CODE ||
                V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
                V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
				V_IN_SPART_CODE ||
				V_IN_SPART_CN_NAME ||
				V_IN_COA_CODE ||
				V_IN_COA_CN_NAME ||
				V_IN_SHIPPING_OBJECT_CODE ||
				V_IN_SHIPPING_OBJECT_CN_NAME ||
				V_IN_MANUFACTURE_OBJECT_CODE ||
				V_IN_MANUFACTURE_OBJECT_CN_NAME ||
								
                'AVG_AMT,
				A.ITEM_CODE,
                A.ITEM_CN_NAME,
                A.PERIOD_YEAR,
                A.PERIOD_ID,
                A.OVERSEA_FLAG,
                A.LV0_PROD_LIST_CODE,
                A.LV0_PROD_LIST_CN_NAME,
                A.LV0_PROD_LIST_EN_NAME';


  DBMS_OUTPUT.PUT_LINE (V_SQL);
  EXECUTE IMMEDIATE V_SQL;


  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '视角标识:'||V_VIEW_NUM||', 计算ITEM的月卷积发货额写入临时表, 版本号='||V_VERSION_ID||',颗粒度：'||F_DIMENSION_TYPE||',时点：'||F_CALIBER_FLAG||',产业类型：'||F_INDUSTRY_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS'); 

   
   
  
    --分视角下计算ITEM的月卷积额
    V_SQL := 
    'INSERT INTO '|| V_TO_TABLE ||' 
      (VERSION_ID,
       PERIOD_YEAR,
       PERIOD_ID,
       LV0_PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME,'||
       V_LV1_PROD_RND_TEAM_CODE ||
       V_LV1_PROD_RD_TEAM_CN_NAME ||
       V_LV2_PROD_RND_TEAM_CODE ||
       V_LV2_PROD_RD_TEAM_CN_NAME ||
       V_LV3_PROD_RND_TEAM_CODE ||
       V_LV3_PROD_RD_TEAM_CN_NAME ||
       V_LV4_PROD_RND_TEAM_CODE ||
       V_LV4_PROD_RD_TEAM_CN_NAME ||
       V_L1_NAME ||
       V_L2_NAME ||
       
       V_DIMENSION_CODE ||
       V_DIMENSION_CN_NAME ||
       V_DIMENSION_EN_NAME||
       V_DIMENSION_SUBCATEGORY_CODE ||
       V_DIMENSION_SUBCATEGORY_CN_NAME ||
       V_DIMENSION_SUBCATEGORY_EN_NAME||
       V_DIMENSION_SUB_DETAIL_CODE ||
       V_DIMENSION_SUB_DETAIL_CN_NAME ||
       V_DIMENSION_SUB_DETAIL_EN_NAME ||
       V_SPART_CODE ||
       V_SPART_CN_NAME ||
	   V_COA_CODE ||
	   V_COA_CN_NAME ||
			 V_SHIPPING_OBJECT_CODE ||
			 V_SHIPPING_OBJECT_CN_NAME ||
			 V_MANUFACTURE_OBJECT_CODE ||
			 V_MANUFACTURE_OBJECT_CN_NAME ||
       'ITEM_CODE,
       ITEM_CN_NAME,
       SHIP_QUANTITY,
       RMB_AVG_AMT,
	   rmb_cost_amt,
       CREATED_BY,
       CREATION_DATE,
       LAST_UPDATED_BY,
       LAST_UPDATE_DATE,
       DEL_FLAG,
       VIEW_FLAG,
       ONLY_ITEM_FLAG,
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME,
       LV0_PROD_LIST_EN_NAME,
       REVIEW_ITEM_FLAG)
    WITH DIST_DATA_TEMP AS
     (
      --筛选出单item品类的维度信息
      SELECT B.OVERSEA_FLAG,B.LV0_PROD_LIST_CODE, B.ITEM_CODE ,'||V_COA_CODE||  V_SPART_CODE || V_DIMENSION_SUB_DETAIL_CODE ||V_SHIPPING_OBJECT_CODE || V_MANUFACTURE_OBJECT_CODE  || V_DIMENSION_SUBCATEGORY_CODE ||V_DIMENSION_CODE || V_L1_NAME || V_L2_NAME || V_LV4_PROD_RND_TEAM_CODE || V_LV3_PROD_RND_TEAM_CODE || V_LV2_PROD_RND_TEAM_CODE || V_LV1_PROD_RND_TEAM_CODE ||' B.LV0_PROD_RND_TEAM_CODE
        FROM (SELECT A.LV0_PROD_RND_TEAM_CODE,'||
                     V_LV1_PROD_RND_TEAM_CODE ||
                     V_LV2_PROD_RND_TEAM_CODE ||
                     V_LV3_PROD_RND_TEAM_CODE ||
					 V_LV4_PROD_RND_TEAM_CODE ||
                     V_L1_NAME || 
                     V_L2_NAME ||
                     V_DIMENSION_CODE ||
                     V_DIMENSION_SUBCATEGORY_CODE ||
                     V_DIMENSION_SUB_DETAIL_CODE ||
                     V_SPART_CODE ||
					 V_COA_CODE||
					 V_SHIPPING_OBJECT_CODE || 
					 V_MANUFACTURE_OBJECT_CODE ||
                     'A.ITEM_CODE,
                     A.OVERSEA_FLAG,
                     A.LV0_PROD_LIST_CODE,
                     COUNT(1) OVER(PARTITION BY '||V_COA_CODE||  V_SPART_CODE || V_DIMENSION_SUB_DETAIL_CODE ||V_SHIPPING_OBJECT_CODE || V_MANUFACTURE_OBJECT_CODE ||V_DIMENSION_SUBCATEGORY_CODE||V_DIMENSION_CODE||V_L1_NAME||V_L2_NAME||V_LV4_PROD_RND_TEAM_CODE||V_LV3_PROD_RND_TEAM_CODE||V_LV2_PROD_RND_TEAM_CODE||V_LV1_PROD_RND_TEAM_CODE||'A.LV0_PROD_RND_TEAM_CODE,A.OVERSEA_FLAG,A.LV0_PROD_LIST_CODE) AS ITEM_FLAG
                  FROM (SELECT DISTINCT OVERSEA_FLAG,LV0_PROD_LIST_CODE,ITEM_CODE,
                               '||V_COA_CODE||  V_SPART_CODE || V_DIMENSION_SUB_DETAIL_CODE ||V_SHIPPING_OBJECT_CODE || V_MANUFACTURE_OBJECT_CODE || V_DIMENSION_SUBCATEGORY_CODE||V_DIMENSION_CODE||V_L1_NAME||V_L2_NAME||V_LV4_PROD_RND_TEAM_CODE||V_LV3_PROD_RND_TEAM_CODE||V_LV2_PROD_RND_TEAM_CODE||V_LV1_PROD_RND_TEAM_CODE||' LV0_PROD_RND_TEAM_CODE
                          FROM VIEW_DATA_TEMP T) A) B
       WHERE ITEM_FLAG = 1)

      SELECT A.VERSION_ID,
                        A.PERIOD_YEAR,
                        A.PERIOD_ID,
                        A.LV0_PROD_RND_TEAM_CODE,
                        A.LV0_PROD_RD_TEAM_CN_NAME,' ||
                        V_IN_LV1_PROD_RND_TEAM_CODE ||
                        V_IN_LV1_PROD_RD_TEAM_CN_NAME ||
                        V_IN_LV2_PROD_RND_TEAM_CODE ||
                        V_IN_LV2_PROD_RD_TEAM_CN_NAME ||
                        V_IN_LV3_PROD_RND_TEAM_CODE ||
                        V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
						V_IN_LV4_PROD_RND_TEAM_CODE ||
                        V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
                        V_IN_L1_NAME ||
                        V_IN_L2_NAME ||
                        V_IN_DIMENSION_CODE ||
                        V_IN_DIMENSION_CN_NAME ||
                        V_IN_DIMENSION_EN_NAME||
                        V_IN_DIMENSION_SUBCATEGORY_CODE ||
                        V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
                        V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
                        V_IN_DIMENSION_SUB_DETAIL_CODE ||
                        V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
                        V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
                        V_IN_SPART_CODE ||
                        V_IN_SPART_CN_NAME ||
						V_IN_COA_CODE ||
                        V_IN_COA_CN_NAME ||
						V_IN_SHIPPING_OBJECT_CODE ||
						V_IN_SHIPPING_OBJECT_CN_NAME ||
						V_IN_MANUFACTURE_OBJECT_CODE ||
						V_IN_MANUFACTURE_OBJECT_CN_NAME ||
                        'A.ITEM_CODE,
                        A.ITEM_CN_NAME,
                        A.SHIP_QUANTITY,
                        A.AVG_AMT ,
						A.MANUFACTURE_AMT,
                        -1 AS CREATED_BY,
                        CURRENT_TIMESTAMP AS CREATION_DATE,
                        -1 AS LAST_UPDATED_BY,
                        CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                        ''N'' AS DEL_FLAG,
                        A.VIEW_FLAG,
                        DECODE(B.ITEM_CODE, NULL, ''N'', ''Y'') AS ONLY_ITEM_FLAG,
                        '''||F_CALIBER_FLAG||''',
                        A.OVERSEA_FLAG,
                        A.LV0_PROD_LIST_CODE,
                        A.LV0_PROD_LIST_CN_NAME,
                        A.LV0_PROD_LIST_EN_NAME,
                        CASE WHEN A.PERIOD_ID BETWEEN C.START_PERIOD AND C.END_PERIOD THEN 1 ELSE 0 END AS REVIEW_ITEM_FLAG
                    FROM VIEW_DATA_TEMP A
               LEFT JOIN DIST_DATA_TEMP B
                    ON A.ITEM_CODE = B.ITEM_CODE
                    AND A.OVERSEA_FLAG = B.OVERSEA_FLAG
                    AND A.LV0_PROD_LIST_CODE = B.LV0_PROD_LIST_CODE
                    AND A.LV0_PROD_RND_TEAM_CODE = B.LV0_PROD_RND_TEAM_CODE'
					||V_INSERT_LV1_PROD_RND_TEAM_CODE 
                    ||V_INSERT_LV2_PROD_RND_TEAM_CODE
                    ||V_INSERT_LV3_PROD_RND_TEAM_CODE
					||V_INSERT_LV4_PROD_RND_TEAM_CODE
                    ||V_INSERT_L1_NAME
                    ||V_INSERT_L2_NAME
                    ||V_INSERT_DIMENSION_CODE
                    ||V_INSERT_DIMENSION_SUBCATEGORY_CODE
                    ||V_INSERT_DIMENSION_SUB_DETAIL_CODE
                    ||V_INSERT_SPART_CODE
					||V_INSERT_COA_CODE
					||V_INSERT_SHIPPING_OBJECT_CODE
					||V_INSERT_MANUFACTURE_OBJECT_CODE ||'
                  LEFT JOIN (SELECT  DISTINCT ITEM_CODE,
							FIRST_VALUE(START_PERIOD)OVER(PARTITION BY ITEM_CODE ORDER BY START_PERIOD) AS START_PERIOD,
							FIRST_VALUE(END_PERIOD)OVER(PARTITION BY ITEM_CODE ORDER BY END_PERIOD DESC) AS END_PERIOD
							FROM(SELECT ITEM_CODE,START_PERIOD,END_PERIOD
							FROM					
								(SELECT ITEM_CODE,START_PERIOD,END_PERIOD,DEL_FLAG,MODIFY_TYPE,
								ROW_NUMBER()OVER(PARTITION BY ITEM_CODE,START_PERIOD,END_PERIOD ORDER BY LAST_UPDATE_DATE DESC) AS RN
								FROM FIN_DM_OPT_FOI.DM_FOC_MADE_DATA_REVIEW_INFO_T WHERE CALIBER_FLAG = '''||F_CALIBER_FLAG||''') 
							WHERE RN=1 AND DEL_FLAG = ''N'' AND MODIFY_TYPE <> ''REVOKE'')) C
                 ON A.ITEM_CODE = C.ITEM_CODE '
                    ;
  DBMS_OUTPUT.PUT_LINE (V_SQL);
  EXECUTE IMMEDIATE V_SQL;                

  

  
  
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '视角标识:'||V_VIEW_NUM||', 计算ITEM的月卷积发货额, 版本号='||V_VERSION_ID||', 并给单ITEM的制造对象打上标识,颗粒度：'||F_DIMENSION_TYPE||',时点：'||F_CALIBER_FLAG||',产业类型：'||F_INDUSTRY_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS'); 
  END LOOP;

    IF F_INDUSTRY_FLAG = 'IAS' AND F_DIMENSION_TYPE = 'U' THEN 
    EXECUTE IMMEDIATE 'UPDATE '||V_TO_TABLE||' SET VIEW_FLAG = 7 WHERE VIEW_FLAG = 4';
    END IF;
	
  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成,颗粒度：'||F_DIMENSION_TYPE||',时点：'||F_CALIBER_FLAG||',产业类型：'||F_INDUSTRY_FLAG);
  
  RETURN 'SUCCESS';
    
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败,颗粒度：'||F_DIMENSION_TYPE||',时点：'||F_CALIBER_FLAG||',产业类型：'||F_INDUSTRY_FLAG, 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END
$$
/

