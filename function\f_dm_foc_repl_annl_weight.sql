-- Name: f_dm_foc_repl_annl_weight; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_repl_annl_weight(f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年10月4日
  创建人  ：唐钦
  背景描述：根据年均本计算得到替代指数的所有层级权重值
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_REPL_ANNL_WEIGHT();
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_REPL_ANNL_WEIGHT'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_SQL TEXT; --执行语句
  V_PARENT_AMT VARCHAR(500);
  V_GROUP_LEVEL VARCHAR(200);
  V_BEFORE_TWO_YEAR INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY') AS INT)-2||12;
  V_BEFORE_YEAR INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY') AS INT)-1||12;
  V_CURR_YEAR INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYYMM') AS INT);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  -- 取出对应的版本号
  IF F_VERSION_ID IS NULL THEN 
    SELECT VERSION_ID INTO V_VERSION_ID   -- 年度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到最新年度版本号为：'||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 删除对应版本的结果表数据
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_WEIGHT_T WHERE VERSION_ID = '||V_VERSION_ID;
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_COST_PER_T WHERE VERSION_ID = '||V_VERSION_ID;
  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除版本号为：'||V_VERSION_ID||'的FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_WEIGHT_T的表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 创建临时表
     DROP TABLE IF EXISTS FOC_REPL_ANNL_BIND_COST_TMP;
     CREATE TEMPORARY TABLE FOC_REPL_ANNL_BIND_COST_TMP (
         PERIOD_YEAR          INT,
         GROUP_CODE           VARCHAR(50),
         GROUP_CN_NAME        VARCHAR(500),
         GROUP_LEVEL          VARCHAR(50),
         PARENT_CODE          VARCHAR(50),
         PARENT_CN_NAME       VARCHAR(500),
         SHIP_QTY             NUMERIC,
         COST_AMT             NUMERIC,
         VIEW_FLAG            VARCHAR(50),
         CALIBER_FLAG         VARCHAR(2)
     )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(GROUP_CODE);

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '创建临时表成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 取出BINDING组层级的金额数据，进行逻辑处理
  INSERT INTO FOC_REPL_ANNL_BIND_COST_TMP(
         PERIOD_YEAR,   
         GROUP_CODE,    
         GROUP_CN_NAME, 
         GROUP_LEVEL,   
         PARENT_CODE,   
         PARENT_CN_NAME,
--         SHIP_QTY,
         COST_AMT,
         VIEW_FLAG,
         CALIBER_FLAG
    )
  -- 替代ID层级
/*  WITH PERIOD_DIM_TMP AS(
  SELECT DISTINCT VERSION_ID,
         PERIOD_YEAR, 
         MAX(PERIOD_ID) OVER(PARTITION BY T1.PERIOD_YEAR,T1.LV0_PROD_RND_TEAM_CODE,T1.LV1_PROD_RND_TEAM_CODE,T1.LV2_PROD_RND_TEAM_CODE,T1.LV3_PROD_RND_TEAM_CODE,T1.REPLACEMENT_GROUP_ID,T1.CALIBER_FLAG,T1.VIEW_FLAG) AS PERIOD_ID,
         REPLACEMENT_GROUP_ID,
         VIEW_FLAG,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,
         LV3_PROD_RND_TEAM_CODE,
         CALIBER_FLAG
  FROM FIN_DM_OPT_FOI.DM_FOC_REPL_DECODE_T T1
  WHERE DEL_FLAG = 'N'
    ),
  DECODE_AMT_TMP AS(
  SELECT T1.VERSION_ID,
         T1.PERIOD_YEAR,   
         T1.REPLACEMENT_GROUP_ID,
         T1.REPLACEMENT_DESCRIPTION,
         T1.VIEW_FLAG,
         T1.LV0_PROD_RND_TEAM_CODE,
         T1.LV1_PROD_RND_TEAM_CODE,
         T1.LV2_PROD_RND_TEAM_CODE,
         T1.LV3_PROD_RND_TEAM_CODE,
         T1.LV0_PROD_RD_TEAM_CN_NAME,
         T1.LV1_PROD_RD_TEAM_CN_NAME,
         T1.LV2_PROD_RD_TEAM_CN_NAME,
         T1.LV3_PROD_RD_TEAM_CN_NAME,
         T1.REPLACING_CY_SHIP_QTY,
         T1.RMB_AAA_BINDING_CUR_COST_AMT,
         T1.CALIBER_FLAG,
         T1.DEL_FLAG
  FROM FIN_DM_OPT_FOI.DM_FOC_REPL_DECODE_T T1
  INNER JOIN PERIOD_DIM_TMP T2
  ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
  AND T1.PERIOD_ID = T2.PERIOD_ID
  AND T1.REPLACEMENT_GROUP_ID = T2.REPLACEMENT_GROUP_ID
  AND T1.VIEW_FLAG = T2.VIEW_FLAG
  AND NVL(T1.LV0_PROD_RND_TEAM_CODE,'LV0') = NVL(T2.LV0_PROD_RND_TEAM_CODE,'LV0')
  AND NVL(T1.LV1_PROD_RND_TEAM_CODE,'LV1') = NVL(T2.LV1_PROD_RND_TEAM_CODE,'LV1')
  AND NVL(T1.LV2_PROD_RND_TEAM_CODE,'LV2') = NVL(T2.LV2_PROD_RND_TEAM_CODE,'LV2')
  AND NVL(T1.LV3_PROD_RND_TEAM_CODE,'LV3') = NVL(T2.LV3_PROD_RND_TEAM_CODE,'LV3')
  AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
  WHERE T1.DEL_FLAG = 'N'
    ),
*/

  WITH DECODE_AMT_TMP AS(
  SELECT T1.VERSION_ID,
         T1.PERIOD_YEAR,   
         T1.REPLACEMENT_GROUP_ID,
         T1.REPLACEMENT_DESCRIPTION,
         T1.VIEW_FLAG,
         T1.LV0_PROD_RND_TEAM_CODE,
         T1.LV1_PROD_RND_TEAM_CODE,
         T1.LV2_PROD_RND_TEAM_CODE,
         T1.LV3_PROD_RND_TEAM_CODE,
         T1.LV0_PROD_RD_TEAM_CN_NAME,
         T1.LV1_PROD_RD_TEAM_CN_NAME,
         T1.LV2_PROD_RD_TEAM_CN_NAME,
         T1.LV3_PROD_RD_TEAM_CN_NAME,
--         T1.REPLACING_CY_SHIP_QTY,
--         T1.RMB_AAA_BINDING_CUR_COST_AMT,
		 T1.RMB_COST_AMT,
         T1.CALIBER_FLAG,
         T1.DEL_FLAG
  FROM FIN_DM_OPT_FOI.DM_FOC_REPL_DECODE_T T1
  WHERE T1.DEL_FLAG = 'N'
  AND PERIOD_ID IN (V_BEFORE_TWO_YEAR,V_BEFORE_YEAR,V_CURR_YEAR)
  ),
  FOC_GROUP_AMT_TMP AS(
  SELECT PERIOD_YEAR,   
         REPLACEMENT_GROUP_ID AS GROUP_CODE,    
         REPLACEMENT_DESCRIPTION AS GROUP_CN_NAME, 
         'BIND' AS GROUP_LEVEL,   
         DECODE(VIEW_FLAG,0,LV0_PROD_RND_TEAM_CODE,1,LV1_PROD_RND_TEAM_CODE,2,LV2_PROD_RND_TEAM_CODE,3,LV3_PROD_RND_TEAM_CODE) AS PARENT_CODE,   
         DECODE(VIEW_FLAG,0,LV0_PROD_RD_TEAM_CN_NAME,1,LV1_PROD_RD_TEAM_CN_NAME,2,LV2_PROD_RD_TEAM_CN_NAME,3,LV3_PROD_RD_TEAM_CN_NAME) AS PARENT_CN_NAME,
--         REPLACING_CY_SHIP_QTY AS SHIP_QTY,
--         REPLACING_CY_SHIP_QTY * RMB_AAA_BINDING_CUR_COST_AMT AS COST_AMT,
         RMB_COST_AMT AS COST_AMT,
         VIEW_FLAG,
         CALIBER_FLAG
      FROM DECODE_AMT_TMP
      WHERE DEL_FLAG = 'N'
  UNION ALL 
  -- LV3层级
  SELECT PERIOD_YEAR,   
         LV3_PROD_RND_TEAM_CODE AS GROUP_CODE,    
         LV3_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME, 
         'LV3' AS GROUP_LEVEL,   
         LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,   
         LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,
--         REPLACING_CY_SHIP_QTY AS SHIP_QTY,
--         REPLACING_CY_SHIP_QTY * RMB_AAA_BINDING_CUR_COST_AMT AS COST_AMT,
         RMB_COST_AMT AS COST_AMT,
         VIEW_FLAG,
         CALIBER_FLAG
      FROM DECODE_AMT_TMP
      WHERE DEL_FLAG = 'N'
	  AND VIEW_FLAG IN ('3')   -- 视角4时，才需要LV3层级数据
  UNION ALL 
  -- LV2层级
  SELECT PERIOD_YEAR,   
         LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,    
         LV2_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME, 
         'LV2' AS GROUP_LEVEL,   
         LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,   
         LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,
--         REPLACING_CY_SHIP_QTY AS SHIP_QTY,
--         REPLACING_CY_SHIP_QTY * RMB_AAA_BINDING_CUR_COST_AMT AS COST_AMT,
         RMB_COST_AMT AS COST_AMT,
         VIEW_FLAG,
         CALIBER_FLAG
      FROM DECODE_AMT_TMP
      WHERE DEL_FLAG = 'N'
	  AND VIEW_FLAG IN ('3','2')   -- 视角4/3时，才需要LV2层级数据
  UNION ALL 
  -- LV1层级
  SELECT PERIOD_YEAR,   
         LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,    
         LV1_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME, 
         'LV1' AS GROUP_LEVEL,   
         LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,   
         LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,
--         REPLACING_CY_SHIP_QTY AS SHIP_QTY,
--         REPLACING_CY_SHIP_QTY * RMB_AAA_BINDING_CUR_COST_AMT AS COST_AMT,
         RMB_COST_AMT AS COST_AMT,
         VIEW_FLAG,
         CALIBER_FLAG
      FROM DECODE_AMT_TMP
      WHERE DEL_FLAG = 'N'
	  AND VIEW_FLAG IN ('3','2','1')   -- 视角4/3/2时，才需要LV1层级数据
  UNION ALL 
  -- LV0层级
  SELECT PERIOD_YEAR,   
         LV0_PROD_RND_TEAM_CODE AS GROUP_CODE,    
         LV0_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME, 
         'LV0' AS GROUP_LEVEL,   
         LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,   
         LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,
--         REPLACING_CY_SHIP_QTY AS SHIP_QTY,
--         REPLACING_CY_SHIP_QTY * RMB_AAA_BINDING_CUR_COST_AMT AS COST_AMT,
         RMB_COST_AMT AS COST_AMT,
         VIEW_FLAG,
         CALIBER_FLAG
      FROM DECODE_AMT_TMP
      WHERE DEL_FLAG = 'N')
  SELECT PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL, 
         PARENT_CODE,
         PARENT_CN_NAME,
--         SUM(SHIP_QTY) AS SHIP_QTY,
         SUM(COST_AMT) AS COST_AMT,
         VIEW_FLAG,
         CALIBER_FLAG
     FROM FOC_GROUP_AMT_TMP
     GROUP BY PERIOD_YEAR,
              GROUP_CODE,
              GROUP_CN_NAME,
              GROUP_LEVEL, 
              PARENT_CODE,
              PARENT_CN_NAME,
              VIEW_FLAG,
              CALIBER_FLAG;
      
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将来源表数据按层级卷积汇总后，保存到临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 分为2类不同层级，进行循环：1：（'BIND'）、2：（'LV3','LV2','LV1','LV0'）
  FOR LOOP_NUM IN 0 .. 1 LOOP
  IF LOOP_NUM = 0 THEN
      V_PARENT_AMT := '
      SUM(COST_AMT) OVER(PARTITION BY PERIOD_YEAR,GROUP_LEVEL,PARENT_CODE,VIEW_FLAG,CALIBER_FLAG) AS PARENT_AMT, ';
      V_GROUP_LEVEL := ' 
      WHERE GROUP_LEVEL IN (''BIND'') ';  
  ELSIF LOOP_NUM = 1 THEN
      V_PARENT_AMT := '
      SUM(COST_AMT) OVER(PARTITION BY PERIOD_YEAR,GROUP_LEVEL,PARENT_CODE,VIEW_FLAG,CALIBER_FLAG) AS PARENT_AMT, ';
      V_GROUP_LEVEL := ' 
      WHERE GROUP_LEVEL IN (''LV3'',''LV2'',''LV1'',''LV0'') ';   
  END IF;     
  
  -- 计算权重数据
  V_SQL := '
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_WEIGHT_T(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         WEIGHT_RATE,
         RMB_COST_AMT,
         ABSOLUTE_WEIGHT,
         ABSOLUTE_PARENT_AMT,
         PARENT_CODE,
         PARENT_CN_NAME,
         CALIBER_FLAG,
         VIEW_FLAG,
         APPEND_FLAG,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
  WITH PARENT_AMT_TMP AS(
  SELECT PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL, 
         PARENT_CODE,
         PARENT_CN_NAME,
         COST_AMT,
         '||V_PARENT_AMT||'
         SUM(COST_AMT) OVER(PARTITION BY PERIOD_YEAR,GROUP_LEVEL,VIEW_FLAG,CALIBER_FLAG) AS ABSOLUTE_PARENT_AMT,
         VIEW_FLAG,
         CALIBER_FLAG
      FROM FOC_REPL_ANNL_BIND_COST_TMP
      '||V_GROUP_LEVEL||'
  ),
  -- 生成连续年份, 三年前的年份+当年（若当年为1月时，当年年份不含）   
  PERIOD_YEAR_TMP AS(     
           SELECT CAST(GENERATE_SERIES(YEAR(TO_DATE(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),''YYYY''),''YYYY''))-2,
                              YEAR(TO_DATE(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),''YYYY''),''YYYY'')),
                              1) AS BIGINT) AS PERIOD_YEAR FROM DUAL
                   ),    
  -- 生成连续年的发散维
  CONTIN_DIM_TMP AS(
  SELECT DISTINCT T2.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL, 
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         T1.VIEW_FLAG,
         T1.CALIBER_FLAG
      FROM PARENT_AMT_TMP T1,PERIOD_YEAR_TMP T2
  )
  SELECT '||V_VERSION_ID||' AS VERSION_ID,
         T1.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         DECODE(T2.COST_AMT,NULL,0,T2.COST_AMT / NULLIF(T2.PARENT_AMT,0)) AS WEIGHT_RATE,
         T2.COST_AMT AS RMB_COST_AMT,
         DECODE(T2.COST_AMT,NULL,0,T2.COST_AMT / NULLIF(T2.ABSOLUTE_PARENT_AMT,0)) AS ABSOLUTE_WEIGHT,
         T2.ABSOLUTE_PARENT_AMT,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         T1.CALIBER_FLAG,
         T1.VIEW_FLAG,
         DECODE(T2.COST_AMT,NULL,''Y'',''N'') AS APPEND_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
      FROM CONTIN_DIM_TMP T1
      LEFT JOIN PARENT_AMT_TMP T2
      ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
      AND T1.GROUP_CODE = T2.GROUP_CODE
      AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
      AND T1.PARENT_CODE = T2.PARENT_CODE
      AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      ';

     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL;   
     DBMS_OUTPUT.PUT_LINE('第'||LOOP_NUM||'次循环，权重计算成功');                
                                            
    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '第'||LOOP_NUM||'次循环，插入版本号为：'||V_VERSION_ID||' 的权重数据到FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_WEIGHT_T表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');    
   END LOOP;
   
  -------------------------------------------------------------------------替代指数金额占比逻辑------------------------------------------------------------------------------------------
  -- 创建临时表
     DROP TABLE IF EXISTS FOC_REPL_SAME_ANNL_COST_TMP;
     CREATE TEMPORARY TABLE FOC_REPL_SAME_ANNL_COST_TMP (
         PERIOD_YEAR          INT,
         GROUP_CODE           VARCHAR(50),
         GROUP_CN_NAME        VARCHAR(500),
         GROUP_LEVEL          VARCHAR(50),
         PARENT_CODE          VARCHAR(50),
         PARENT_CN_NAME       VARCHAR(500),
         SAME_COST_AMT        NUMERIC,
         REPL_COST_AMT        NUMERIC,
         VIEW_FLAG            VARCHAR(50),
         CALIBER_FLAG         VARCHAR(2)
     )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(GROUP_CODE);

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '创建金额占比临时表成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 取出同编码金额的数据和替代指数金额的数据
  INSERT INTO FOC_REPL_SAME_ANNL_COST_TMP(
         PERIOD_YEAR,   
         GROUP_CODE,    
         GROUP_CN_NAME, 
         GROUP_LEVEL,   
         PARENT_CODE,   
         PARENT_CN_NAME,
         SAME_COST_AMT,  
         REPL_COST_AMT,
         VIEW_FLAG,     
         CALIBER_FLAG
    )
  WITH SAME_COST_TMP AS(
  -- 取同编码金额整年数据
  SELECT CAST(PERIOD_YEAR AS BIGINT) AS PERIOD_YEAR,   
         GROUP_CODE,    
         GROUP_CN_NAME, 
         GROUP_LEVEL,   
         PARENT_CODE,   
         PARENT_CN_NAME,
         RMB_COST_AMT,  
         VIEW_FLAG,     
         CALIBER_FLAG
	  FROM FIN_DM_OPT_FOI.DM_FOC_TOTAL_ANNUAL_WEIGHT_T
	  WHERE VERSION_ID = V_VERSION_ID
	  AND OVERSEA_FLAG = 'G'  -- 只取全球数据
	  AND LV0_PROD_LIST_CODE = 'GR'  -- 只取集团数据
	  AND COST_TYPE = 'T'   -- 只取总成本的数据
	  AND GROUP_LEVEL IN ('LV3','LV2','LV1','LV0')
	),   -- 限制层级
  REPL_COST_TMP AS(
  -- 取替代指数金额整年数据
  SELECT PERIOD_YEAR,   
         GROUP_CODE,    
         GROUP_CN_NAME, 
         GROUP_LEVEL,   
         PARENT_CODE,   
         PARENT_CN_NAME,
         RMB_COST_AMT,  
         VIEW_FLAG,     
         CALIBER_FLAG
	  FROM DM_FOC_REPL_ANNL_WEIGHT_T T2  -- 替代指数金额
	  WHERE VERSION_ID = V_VERSION_ID
	  AND GROUP_LEVEL IN ('LV3','LV2','LV1','LV0')   -- 限制层级
  )
  SELECT DECODE(T1.PERIOD_YEAR,NULL,T2.PERIOD_YEAR,T1.PERIOD_YEAR) AS PERIOD_YEAR,   
         DECODE(T1.GROUP_CODE,NULL,T2.GROUP_CODE,T1.GROUP_CODE) AS GROUP_CODE,    
         DECODE(T1.GROUP_CN_NAME,NULL,T2.GROUP_CN_NAME,T1.GROUP_CN_NAME) AS GROUP_CN_NAME, 
         DECODE(T1.GROUP_LEVEL,NULL,T2.GROUP_LEVEL,T1.GROUP_LEVEL) AS GROUP_LEVEL,   
         DECODE(T1.PARENT_CODE,NULL,T2.PARENT_CODE,T1.PARENT_CODE) AS PARENT_CODE,   
         DECODE(T1.PARENT_CN_NAME,NULL,T2.PARENT_CN_NAME,T1.PARENT_CN_NAME) AS PARENT_CN_NAME,
         T1.RMB_COST_AMT AS SAME_COST_AMT,
		 T2.RMB_COST_AMT AS REPL_COST_AMT,
         DECODE(T1.VIEW_FLAG,NULL,T2.VIEW_FLAG,T1.VIEW_FLAG) AS VIEW_FLAG,     
         DECODE(T1.CALIBER_FLAG,NULL,T2.CALIBER_FLAG,T1.CALIBER_FLAG) AS CALIBER_FLAG 
	  FROM SAME_COST_TMP T1  -- 同编码金额
	  FULL JOIN REPL_COST_TMP T2  -- 替代指数金额
	  ON  T1.GROUP_LEVEL = T2.GROUP_LEVEL
	  AND T1.GROUP_CODE = T2.GROUP_CODE
	  AND T1.PARENT_CODE = T2.PARENT_CODE
	  AND T1.VIEW_FLAG = T2.VIEW_FLAG
	  AND T1.CALIBER_FLAG =T2.CALIBER_FLAG
	  AND T1.PERIOD_YEAR = T2.PERIOD_YEAR;
	  
 --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取出同编码金额的数据和替代指数金额的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
	 
  -- 计算金额占比数据
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_COST_PER_T(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         SAME_COST_AMT,
         REPL_COST_AMT,
         RMB_COST_PER,
         CALIBER_FLAG,
         VIEW_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
    )
  SELECT V_VERSION_ID AS VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         NVL(SAME_COST_AMT,0) AS SAME_COST_AMT,
         NVL(REPL_COST_AMT,0) AS REPL_COST_AMT,
         NVL(DECODE(SAME_COST_AMT,0,0,REPL_COST_AMT/SAME_COST_AMT),0) AS RMB_COST_PER,
         CALIBER_FLAG,
         VIEW_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
	  FROM FOC_REPL_SAME_ANNL_COST_TMP;
	  
 --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '计算得到对应编码的金额及金额占比，插入数据到结果表：FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_COST_PER_T',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_WEIGHT_T';
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_COST_PER_T';

  -- 日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_WEIGHT_T表统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

