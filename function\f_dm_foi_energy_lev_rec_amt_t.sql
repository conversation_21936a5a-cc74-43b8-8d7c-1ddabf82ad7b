-- Name: f_dm_foi_energy_lev_rec_amt_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_energy_lev_rec_amt_t(f_caliber_flag character varying, f_version_id bigint DEFAULT NULL::bigint, f_item_version bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$ 
/***************************************************************************************************************************************************************
创建人  ：罗若文
修改人：唐钦
背景描述：到货总金额数据表,然后调用该函数的版本将相对应的数据生成导入到目标表中
参数描述：            参数一(f_caliber_flag)：区分数字能源和ICT
                    参数二(f_version_id)：top品类清单表最新版本号
                    参数三(f_item_version)：导入通用版本号（规格品清单版本号）
                    参数四(x_success_flag)  ：运行状态返回值-成功或者失败
事例    ：select FIN_DM_OPT_FOI.f_dm_foi_energy_lev_rec_amt_t('E','','')    --一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                  VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOI_ENERGY_LEV_REC_AMT_T';
  V_VERSION_ID               BIGINT; --执行版本号
  V_STEP_NUM                 BIGINT := 0; --函数步骤数及异常定点
  V_PART1_PUBLIC             TEXT := NULL; -- 函数逻辑公共部分
  V_BEGIN_DATE TIMESTAMP := TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)-3)||'01','YYYYMM');
  V_FCST_PERIOD NUMBER := TO_NUMBER(TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMM')); --当前系统预测月  
  V_CATEGORY                 TEXT := NULL; --品类部分字段
  V_LV4                      TEXT := NULL; --模块部分字段
  V_LV3                      TEXT := NULL; --专家团部分字段
  V_LV2                      TEXT := NULL; --ICT部分字段
  V_L3_CEG_SHORT_CN_NAME     TEXT := NULL; --专家团短名字段
  V_L4_CEG_SHORT_CN_NAME     TEXT := NULL; --模块短名字段
  V_CONTINUITY_TYPE          TEXT := NULL; --含连续性影响字段
  V_SQL_GROUP_LEVEL          TEXT := NULL; --卷积层级
  V_SQL_GEOUP_CODE           TEXT := NULL; --查询层级CODE
  V_SQL_GROUP_CN_NAME        TEXT := NULL; --查询层级NAME
  V_SQL_PARENT_CODE          TEXT := NULL; --上层级CODE
  V_SQL_ITEM                 TEXT := NULL; --查询ITEM部分字段
  V_SQL_CATEGORY             TEXT := NULL; --查询品类部分字段
  V_SQL_LV4                  TEXT := NULL; --查询模块部分字段
  V_SQL_LV3                  TEXT := NULL; --查询专家团部分字段
  V_SQL_LV2                  TEXT := NULL; --查询ICT部分字段
  V_SQL_L3_CEG_SHORT_CN_NAME TEXT := NULL; --查询专家团短名字段
  V_SQL_L4_CEG_SHORT_CN_NAME TEXT := NULL; --查询模块短名字段
  V_SQL_CONTINUITY_TYPE      TEXT := NULL; --查询含连续性影响字段
  V_GROUP_CATEGORY           TEXT := NULL; --品类卷积字段
  V_GROUP_CONTINUITY_TYPE    TEXT := NULL; --含连续性影响字段卷积字段
  V_JOIN_CONTINUITY_TYPE     TEXT := NULL; --含连续性影响字段关联字段
  V_FILTER1                  TEXT := NULL; --筛选条件
  V_CHILD_LEVEL              TEXT := NULL; --子层级值
  V_SQL1                     TEXT := NULL; --公共部分逻辑1
  V_SQL2                     TEXT := NULL; --公共部分逻辑2
  V_SQL3                     TEXT := NULL; --逻辑3
  
  V_DM_LEV_AMT_TEMP         TEXT;
  V_SQL                   TEXT;

  V_ACTUAL_TABLE           VARCHAR(50);
  V_FCST_TABLE              VARCHAR(50);
  V_CEG_TABLE              VARCHAR(50);
  V_TO_TABLE             VARCHAR(50);
  V_TOP_AMT_TABLE        VARCHAR(100);
  V_TOP_TABLE              VARCHAR(50);
  V_SCENARIO_FLAG1         VARCHAR(100);
  V_SCENARIO_FLAG2         VARCHAR(100);
  V_INSERT_ID             VARCHAR(10);
  V_ID                     VARCHAR(100);
  V_AMT_PARA             VARCHAR(100);
  V_PARAMETER             VARCHAR(30);
  V_CURRENT_FLAG         INT;
BEGIN

  --判断入参是ICT还是数字能源
  IF f_caliber_flag = 'I' THEN 
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_LEV_REC_AMT_T';
    V_TOP_AMT_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_AMT_T';
    V_TOP_TABLE     := 'FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_INFO_T';
    V_FCST_TABLE    := 'FIN_DM_OPT_FOI.DM_FOI_FCST_SUM_T';
    V_CEG_TABLE     := 'FIN_DM_OPT_FOI.DM_DIM_FOI_ITEM_CATG_MODL_CEG_T';
    
    
      -- 将查询到的数据放到变量中的公共sql
      V_PART1_PUBLIC := '
                        SELECT VALUE 
                            FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T
                            WHERE ENABLE_FLAG = ''Y''
                            AND UPPER(PARA_NAME) = ''$PARA_NAME$'';
                      ';

      -- 无论是季调还是月调，都通过sql从变量参数表（dm_foi_plan_var_para_t）表中取出对应的规格品版本号
      IF f_version_id IS NULL AND f_item_version IS NULL THEN
        V_SQL := REPLACE(V_PART1_PUBLIC,
                                 '$PARA_NAME$',
                                 'VERSION_ID-ITEM'); -- 规格品版本号
        EXECUTE V_SQL
          INTO V_VERSION_ID;
      
        -- 业务在前台配置页面调整规格品数据时，Java传参，优先取规格品版本号，若无，则取TOP品类版本号作为生成本次数据的版本号   
      ELSE
        V_VERSION_ID := NVL(f_item_version, f_version_id);
      END IF;
  
  
  --判断入参是ICT还是数字能源
  ELSIF f_caliber_flag = 'E' THEN 
    V_TO_TABLE         := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_ACTUAL_COST_T';
    V_TOP_AMT_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_AMT_T';
    V_TOP_TABLE     := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_INFO_T';
    V_FCST_TABLE    := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_FCST_SUM_T';
    V_CEG_TABLE     := 'FIN_DM_OPT_FOI.DM_DIM_ITEM_CATG_MODL_CEG_ENERGY_T';
    
--判断数字能源版本号
    IF f_version_id IS  NULL AND f_item_version IS NULL THEN 
      SELECT COUNT(1) INTO V_CURRENT_FLAG
    FROM
        FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T 
    WHERE
        VERSION = TO_CHAR(CURRENT_DATE, 'YYYYMM')||'-ITEM-Auto'
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ITEM';
        
    -- FLAG 不等于0，说明已有版本号，沿用        
        IF V_CURRENT_FLAG <> 0 THEN 
            SELECT VERSION_ID INTO V_VERSION_ID
            FROM
                FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T 
                WHERE
                    VERSION = TO_CHAR(CURRENT_DATE, 'YYYYMM')||'-ITEM-Auto'
                    AND DEL_FLAG = 'N'
                    AND STATUS = 1
                    AND UPPER(DATA_TYPE) = 'ITEM';
                        
        ELSE 
            RETURN '没有找到版本号';
        END IF;
      ELSE  
                V_VERSION_ID := NVL(f_item_version, f_version_id);
        END IF;
    
ELSE 
    RETURN '入参错误';    
END IF;
    --0.日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  DROP TABLE IF EXISTS DM_LEV_AMT_TEMP;
  CREATE TEMPORARY TABLE DM_LEV_AMT_TEMP(
   YEAR    INT,
   PERIOD_ID    INT,
   GROUP_CODE    VARCHAR(50),
   GROUP_CN_NAME    VARCHAR(500),
   GROUP_LEVEL    VARCHAR(50),
   RECEIVE_QTY    BIGINT,
   RECEIVE_AMT_USD    NUMERIC,
   RECEIVE_AMT_CNY    NUMERIC,
   ITEM_CODE    VARCHAR(50),
   ITEM_NAME    VARCHAR(500),
   CATEGORY_CODE    VARCHAR(50),
   CATEGORY_NAME    VARCHAR(500),
   L4_CEG_CODE    VARCHAR(500),
   L4_CEG_SHORT_CN_NAME    VARCHAR(500),
   L3_CEG_CODE    VARCHAR(500),
   L3_CEG_SHORT_CN_NAME    VARCHAR(500),
   L2_CEG_CODE    VARCHAR(500),
   L2_CEG_CN_NAME    VARCHAR(500),
   TOP_FLAG    VARCHAR(3),
   L4_CEG_CN_NAME    VARCHAR(500),
   L3_CEG_CN_NAME    VARCHAR(500),
   PARENT_CODE    VARCHAR(50),
   PARENT_CN_NAME VARCHAR(500),
   APPEND_FLAG    VARCHAR(2),
   CONTINUITY_TYPE    VARCHAR(50)
  )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY REPLICATION;
   

 

--1， 取到供应商层级每月金额(TOP_FLAG区分是否为规格品下供应商)
--DM_FOI_ENERGY_TOP_ITEM_AMT_T (加入缺失月份补0规则) ==》供应商实际月金额

V_SQL := 'INSERT INTO DM_LEV_AMT_TEMP
    (YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     RECEIVE_QTY,
     RECEIVE_AMT_USD,
     RECEIVE_AMT_CNY,
     ITEM_CODE,
     ITEM_NAME,
     CATEGORY_CODE,
     CATEGORY_NAME,
     L2_CEG_CN_NAME,
     L3_CEG_SHORT_CN_NAME,
     L4_CEG_SHORT_CN_NAME,
     L2_CEG_CODE,
     L3_CEG_CODE,
     L4_CEG_CODE,
     L4_CEG_CN_NAME,
     L3_CEG_CN_NAME,
     TOP_FLAG,
     PARENT_CODE,
     PARENT_CN_NAME,
     APPEND_FLAG)
        
        SELECT T1.period_year,
           T1.PERIOD_ID,
           T1.SUPPLIER_CODE AS GROUP_CODE,
           T1.SUPPLIER_CN_NAME AS GROUP_CN_NAME,
           ''SUPPLIER'' AS GROUP_LEVEL,
           T1.RECEIVE_QTY,
           T1.RECEIVE_AMT_USD,
           T1.RECEIVE_AMT_CNY,
           T1.ITEM_CODE,
           T1.ITEM_NAME,
           T1.CATEGORY_CODE,
           T1.CATEGORY_NAME,
           T1.L2_CEG_CN_NAME,
           T1.L3_CEG_SHORT_CN_NAME,
           T1.L4_CEG_SHORT_CN_NAME,
           T1.L2_CEG_CODE,
           T1.L3_CEG_CODE,
           T1.L4_CEG_CODE,
           T1.L4_CEG_CN_NAME,
           T1.L3_CEG_CN_NAME,
           T1.TOP_FLAG,
           T1.ITEM_CODE AS PARENT_CODE,
           T1.ITEM_NAME AS PARENT_CN_NAME,
           ''N'' AS APPEND_FLAG
      FROM '||V_TOP_AMT_TABLE||' T1
     WHERE UPPER(T1.APPEND_FLAG) = ''N''
     AND VERSION_ID = '||V_VERSION_ID||'
     ';            
    EXECUTE IMMEDIATE V_SQL;    

                    
 --写入日志
  V_STEP_NUM := V_STEP_NUM+1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '供应商实际月金额插数至临时表完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');
   
   V_SQL := 'INSERT INTO DM_LEV_AMT_TEMP
    (YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     RECEIVE_QTY,
     RECEIVE_AMT_USD,
     RECEIVE_AMT_CNY,
     CATEGORY_CODE,
     CATEGORY_NAME,
     L2_CEG_CN_NAME,
     L3_CEG_SHORT_CN_NAME,
     L4_CEG_SHORT_CN_NAME,
     L2_CEG_CODE,
     L3_CEG_CODE,
     L4_CEG_CODE,
     L4_CEG_CN_NAME,
     L3_CEG_CN_NAME,
     TOP_FLAG,
     PARENT_CODE,
     PARENT_CN_NAME,
     APPEND_FLAG) 
     
WITH ITEM_AMT_TMP AS(
   --将供应商金额卷积至ITEM层级 ==》ITEM实际月金额 
SELECT        YEAR,
           PERIOD_ID,
           ITEM_CODE AS GROUP_CODE,
           ITEM_NAME AS GROUP_CN_NAME,
           ''ITEM'' AS GROUP_LEVEL,
           SUM(RECEIVE_QTY) AS RECEIVE_QTY,
           SUM(RECEIVE_AMT_USD) AS RECEIVE_AMT_USD,
           SUM(RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY,
           CATEGORY_CODE,
           CATEGORY_NAME,
           L2_CEG_CN_NAME,
           L3_CEG_SHORT_CN_NAME,
           L4_CEG_SHORT_CN_NAME,
           L2_CEG_CODE,
           L3_CEG_CODE,
           L4_CEG_CODE,
           L4_CEG_CN_NAME,
           L3_CEG_CN_NAME,
           TOP_FLAG,
           CATEGORY_CODE AS PARENT_CODE,
           CATEGORY_NAME AS PARENT_CN_NAME,
           APPEND_FLAG
      FROM DM_LEV_AMT_TEMP 
      GROUP BY 
      YEAR,
           PERIOD_ID,
           ITEM_CODE,
           ITEM_NAME,
           CATEGORY_CODE,
           CATEGORY_NAME,
           L2_CEG_CN_NAME,
           L3_CEG_SHORT_CN_NAME,
           L4_CEG_SHORT_CN_NAME,
           L2_CEG_CODE,
           L3_CEG_CODE,
           L4_CEG_CODE,
           L4_CEG_CN_NAME,
           L3_CEG_CN_NAME,
           TOP_FLAG,
           APPEND_FLAG
  UNION ALL
 -- （取预测数汇总表，量*价拿到额，缺失月份补0，关联规格品清单（TOP_ITEM）打好TOP_FLAG标签）==》ITEM预测月金额
  SELECT     T1.YEAR,
           T1.PERIOD_ID,
           T1.ITEM_CODE AS GROUP_CODE,
           T1.ITEM_NAME AS GROUP_CN_NAME,
           ''ITEM'' AS GROUP_LEVEL,
           T1.RECEIVE_QTY,
           T1.RECEIVE_AMT_USD,
           T1.RECEIVE_AMT_CNY,
           T1.CATEGORY_CODE,
           T1.CATEGORY_NAME,
           T1.L2_CEG_CN_NAME,
           T1.L3_CEG_SHORT_CN_NAME,
           T1.L4_CEG_SHORT_CN_NAME,
           T1.L2_CEG_CODE,
           T1.L3_CEG_CODE,
           T1.L4_CEG_CODE,
           T1.L4_CEG_CN_NAME,
           T1.L3_CEG_CN_NAME,
           CASE WHEN T1.ITEM_CODE = T2.ITEM_CODE THEN ''Y''
           ELSE ''N'' END AS TOP_FLAG,
           T1.CATEGORY_CODE AS PARENT_CODE,
           T1.CATEGORY_NAME AS PARENT_CN_NAME,
           T1.APPEND_FLAG
        FROM '||V_FCST_TABLE||' T1
        LEFT JOIN (SELECT DISTINCT ITEM_CODE FROM '||V_TOP_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||') T2
         ON T1.ITEM_CODE = T2.ITEM_CODE
         WHERE UPPER(T1.APPEND_FLAG) = ''N''
      ),
    CROSS_JOIN_DIM_TEMP AS
     (
      --生成当年-3年1月份至当年12月份,不存在的品类和item维
     SELECT TO_CHAR(ADD_MONTHS('''||V_BEGIN_DATE||''', NUM.VAL - 1),
                      ''YYYYMM'') AS PERIOD_ID
        FROM GENERATE_SERIES(1,
                              TO_NUMBER(TIMESTAMPDIFF(MONTH,'''||V_BEGIN_DATE||''',
                              TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)+1)||''01'',''YYYYMM''))),
                              1) NUM(VAL)
                 ), 
    PERIOD_DIM_TEMP AS(
    SELECT DISTINCT SUBSTR(T2.PERIOD_ID,1,4) AS PERIOD_YEAR,
           T2.PERIOD_ID,
           T1.GROUP_CODE,
           T1.GROUP_CN_NAME,
           T1.GROUP_LEVEL,
           T1.CATEGORY_CODE,
           T1.CATEGORY_NAME,
           T1.L2_CEG_CN_NAME,
           T1.L3_CEG_SHORT_CN_NAME,
           T1.L4_CEG_SHORT_CN_NAME,
           T1.L2_CEG_CODE,
           T1.L3_CEG_CODE,
           T1.L4_CEG_CODE,
           T1.L4_CEG_CN_NAME,
           T1.L3_CEG_CN_NAME,
           T1.TOP_FLAG,
           T1.PARENT_CODE,
           T1.PARENT_CN_NAME
        FROM ITEM_AMT_TMP T1,CROSS_JOIN_DIM_TEMP T2
         )
                     
   SELECT 
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.GROUP_CODE,
           T1.GROUP_CN_NAME,
           T1.GROUP_LEVEL,
           DECODE( T2.RECEIVE_QTY, NULL, 0, T2.RECEIVE_QTY ) AS RECEIVE_QTY,
           DECODE( T2.RECEIVE_AMT_USD, NULL, 0, T2.RECEIVE_AMT_USD ) AS RECEIVE_AMT_USD,
           DECODE( T2.RECEIVE_AMT_CNY, NULL, 0, T2.RECEIVE_AMT_CNY ) AS RECEIVE_AMT_CNY,
           T1.CATEGORY_CODE,
           T1.CATEGORY_NAME,
           T1.L2_CEG_CN_NAME,
           T1.L3_CEG_SHORT_CN_NAME,
           T1.L4_CEG_SHORT_CN_NAME,
           T1.L2_CEG_CODE,
           T1.L3_CEG_CODE,
           T1.L4_CEG_CODE,
           T1.L4_CEG_CN_NAME,
           T1.L3_CEG_CN_NAME,
           T1.TOP_FLAG,
           T1.PARENT_CODE,
           T1.PARENT_CN_NAME,
           T2.APPEND_FLAG
      FROM PERIOD_DIM_TEMP T1
      LEFT JOIN ITEM_AMT_TMP T2
      ON T1.PERIOD_ID = T2.PERIOD_ID
      AND T1.GROUP_CODE = T2.GROUP_CODE
      AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
      AND T1.PARENT_CODE = T2.PARENT_CODE
      ';

    EXECUTE IMMEDIATE V_SQL;
    

 --写入日志
  V_STEP_NUM := V_STEP_NUM+1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => 'ITEM金额插数至临时表完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_FORMULA_SQL_TXT => V_SQL,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');
   
--4， 分层卷积至CATEGORY , LV4,LV3,LV2(含集团代采),LV2(不含集团代采) 
--ITEM以上层级取数时,只取非补齐数据

 /*字段值定义*/
        
  V_LV4                      := 'L4_CEG_CODE , L4_CEG_CN_NAME , ';          
  V_LV3                      := 'L3_CEG_CODE , L3_CEG_CN_NAME , ';          
  V_LV2                      := 'L2_CEG_CODE , L2_CEG_CN_NAME , ';          
  V_L3_CEG_SHORT_CN_NAME     := 'L3_CEG_SHORT_CN_NAME , ';                  
  V_L4_CEG_SHORT_CN_NAME     := 'L4_CEG_SHORT_CN_NAME , ';                  
  V_CONTINUITY_TYPE          := '';                                         

  
  V_CATEGORY                 := '';
  V_SQL_GROUP_LEVEL          := '''CATEGORY''';
  V_SQL_GEOUP_CODE           := 'T1.CATEGORY_CODE AS GROUP_CODE,';
  V_SQL_GROUP_CN_NAME        := 'T1.CATEGORY_NAME AS GROUP_CN_NAME,'; --20240306

  V_SQL_PARENT_CODE          := 'T1.L4_CEG_CODE AS PARENT_CODE ,
                               T1.L4_CEG_CN_NAME AS PARENT_CN_NAME, ';  --20240306
                                          
  V_SQL_ITEM                 := ''; --20240306
 
  V_SQL_CATEGORY             := '';  --20240306
  V_SQL_LV4                  := 'T1.L4_CEG_CODE , T1.L4_CEG_CN_NAME , ';
  V_SQL_LV3                  := 'T1.L3_CEG_CODE , T1.L3_CEG_CN_NAME , ';
  V_SQL_LV2                  := 'T1.L2_CEG_CODE , T1.L2_CEG_CN_NAME , ';
  V_SQL_L3_CEG_SHORT_CN_NAME := 'T1.L3_CEG_SHORT_CN_NAME , ';
  V_SQL_L4_CEG_SHORT_CN_NAME := 'T1.L4_CEG_SHORT_CN_NAME , ';
  V_SQL_CONTINUITY_TYPE      := '';  

  /*卷积字段定义*/
  V_GROUP_CATEGORY := 'T1.CATEGORY_CODE , T1.CATEGORY_NAME ,';
  V_GROUP_CONTINUITY_TYPE:='';

  /*条件定义*/
  V_JOIN_CONTINUITY_TYPE := '';
  V_CHILD_LEVEL          := '''ITEM'''; --20240306

  V_SQL1 := '
  INSERT INTO DM_LEV_AMT_TEMP
  (YEAR,
   PERIOD_ID,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   RECEIVE_QTY,
   RECEIVE_AMT_USD,
   RECEIVE_AMT_CNY,
   '||V_CATEGORY
   ||V_LV2
   ||V_LV3
   ||V_LV4
   ||V_L3_CEG_SHORT_CN_NAME
   ||V_L4_CEG_SHORT_CN_NAME
   ||V_CONTINUITY_TYPE||'
   PARENT_CODE,
   PARENT_CN_NAME,
   APPEND_FLAG,
   TOP_FLAG
   ) ' ;
   
  V_SQL2 := '
    SELECT T1.YEAR,
         T1.PERIOD_ID,
         '||V_SQL_GEOUP_CODE 
         ||V_SQL_GROUP_CN_NAME 
         ||V_SQL_GROUP_LEVEL||' AS GROUP_LEVEL,
         SUM(T1.RECEIVE_QTY) AS RECEIVE_QTY,
         SUM(T1.RECEIVE_AMT_USD) AS RECEIVE_AMT_USD,
         SUM(T1.RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY,
         '||V_SQL_CATEGORY
         ||V_SQL_LV2
         ||V_SQL_LV3
         ||V_SQL_LV4
         ||V_SQL_L3_CEG_SHORT_CN_NAME
         ||V_SQL_L4_CEG_SHORT_CN_NAME
         ||V_SQL_CONTINUITY_TYPE
         ||V_SQL_PARENT_CODE||'
         ''N'' AS APPEND_FLAG,
         T1.TOP_FLAG
    FROM DM_LEV_AMT_TEMP T1 '||V_JOIN_CONTINUITY_TYPE||'
   WHERE T1.GROUP_LEVEL = '||V_CHILD_LEVEL||'
     AND T1.APPEND_FLAG = ''N'' 
   GROUP BY '||V_GROUP_CATEGORY
            ||V_SQL_LV2
            ||V_SQL_LV3
            ||V_SQL_LV4
            ||V_SQL_ITEM
            ||V_SQL_L3_CEG_SHORT_CN_NAME
            ||V_SQL_L4_CEG_SHORT_CN_NAME
            ||V_GROUP_CONTINUITY_TYPE||'
            T1.YEAR,
            T1.PERIOD_ID,
            T1.TOP_FLAG';
            
  V_SQL3:='';
  
   --写入日志
  V_STEP_NUM := V_STEP_NUM+1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '变量定义完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');
  
FOR LEVEL_FLAG IN 1 .. 4 LOOP
      
      /*品类卷积*/
      IF LEVEL_FLAG = 1 THEN
      NULL;
          
     /*LV4卷积*/
     ELSIF LEVEL_FLAG = 2 THEN
      /*字段值定义*/
      V_L4_CEG_SHORT_CN_NAME     := '';
      V_SQL_GROUP_LEVEL          := '''LV4''';
      V_SQL_GEOUP_CODE           := 'T1.L4_CEG_CODE AS GROUP_CODE,';
      V_SQL_GROUP_CN_NAME        := 'T1.L4_CEG_CN_NAME AS GROUP_CN_NAME,';
      V_SQL_PARENT_CODE          := 'T1.L3_CEG_CODE AS PARENT_CODE ,
                                     T1.L3_CEG_CN_NAME AS PARENT_CN_NAME ,';
      V_SQL_L4_CEG_SHORT_CN_NAME := '';

      /*卷积字段定义*/
      V_GROUP_CATEGORY := '';
      
      
      /*条件定义*/
      V_CHILD_LEVEL          := '''CATEGORY''';
      
      /*LV3卷积*/
      ELSIF LEVEL_FLAG = 3 THEN
      /*字段值定义*/
      V_LV4                      := '';
      V_L3_CEG_SHORT_CN_NAME     := '';
      V_CONTINUITY_TYPE          := 'CONTINUITY_TYPE,';
      
      V_SQL_GROUP_LEVEL          := '''LV3''';
      V_SQL_GEOUP_CODE           := 'T1.L3_CEG_CODE AS GROUP_CODE,';
      V_SQL_GROUP_CN_NAME        := 'T1.L3_CEG_CN_NAME AS GROUP_CN_NAME,';
      V_SQL_PARENT_CODE          := 'T1.L2_CEG_CODE AS PARENT_CODE ,
                                     T1.L2_CEG_CN_NAME AS PARENT_CN_NAME ,';
      V_SQL_LV4                  := '';
      V_SQL_L3_CEG_SHORT_CN_NAME := '';
      
      V_GROUP_CONTINUITY_TYPE     := 'T2.CONTINUITY_TYPE , ';

      /*条件定义*/
      V_CHILD_LEVEL          := '''LV4''';
      
      IF f_caliber_flag = 'I' THEN 
        V_SQL_CONTINUITY_TYPE      := 'T2.CONTINUITY_TYPE , ';
        V_JOIN_CONTINUITY_TYPE := '
                                  LEFT JOIN (SELECT VALUE, PARA_NAME AS CONTINUITY_TYPE
                                               FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T
                                              WHERE ENABLE_FLAG = ''Y''
                                                AND PARA_NAME = ''不含连续性影响'') T2
                                    ON T1.L3_CEG_CN_NAME = T2.VALUE ';
      ELSIF f_caliber_flag = 'E' THEN
      
        V_SQL_CONTINUITY_TYPE      := 'T2.CONTINUITY_TYPE , ';
        
        V_JOIN_CONTINUITY_TYPE := '
                                  LEFT JOIN (SELECT VALUE, ''N'' AS CONTINUITY_TYPE
                                               FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T
                                              WHERE ENABLE_FLAG = ''Y''
                                                AND PARA_NAME = ''ENERGY'') T2
                                    ON T1.L3_CEG_CN_NAME = T2.VALUE ';
        END IF ;
                                           
      /*LV2卷积*/
      ELSIF LEVEL_FLAG = 4 THEN
      /*字段值定义*/
      V_LV3 := '';

      V_SQL_GROUP_LEVEL       := '''LV2''';
      V_SQL_GEOUP_CODE        := 'T1.L2_CEG_CODE AS GROUP_CODE,';
      V_SQL_GROUP_CN_NAME     := 'T1.L2_CEG_CN_NAME AS GROUP_CN_NAME,';
      V_SQL_PARENT_CODE       := ''''' AS PARENT_CODE ,
                                  '''' AS PARENT_CN_NAME ,';
      V_SQL_LV3               := '';
      IF f_caliber_flag = 'I' THEN 
          V_SQL_CONTINUITY_TYPE   := 'T1.CONTINUITY_TYPE , ';
          V_GROUP_CONTINUITY_TYPE := 'T1.CONTINUITY_TYPE , ';
          
          
      ELSIF f_caliber_flag = 'E' THEN
          V_SQL_CONTINUITY_TYPE   := 'T1.CONTINUITY_TYPE  , ';
          V_GROUP_CONTINUITY_TYPE := 'T1.CONTINUITY_TYPE  , ';
      END IF;

      /*条件定义*/
      V_CHILD_LEVEL := '''LV3''';
      V_JOIN_CONTINUITY_TYPE:='';
      V_FILTER1     := ' AND T1.CONTINUITY_TYPE IS NOT NULL';
      
      V_SQL3:='
      UNION ALL
          SELECT T1.YEAR,
             T1.PERIOD_ID,
             '||V_SQL_GEOUP_CODE
             ||V_SQL_GROUP_CN_NAME
             ||V_SQL_GROUP_LEVEL||' AS GROUP_LEVEL,
             SUM(T1.RECEIVE_QTY) AS RECEIVE_QTY,
             SUM(T1.RECEIVE_AMT_USD) AS RECEIVE_AMT_USD,
             SUM(T1.RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY,
             '
             ||V_SQL_LV2
             ||V_SQL_CONTINUITY_TYPE
             ||V_SQL_PARENT_CODE||'
             ''N'' AS APPEND_FLAG,
             T1.TOP_FLAG
        FROM DM_LEV_AMT_TEMP T1 '||V_JOIN_CONTINUITY_TYPE||'
       WHERE T1.GROUP_LEVEL = '||V_CHILD_LEVEL|| V_FILTER1 ||' 
       GROUP BY '||V_SQL_LV2
                ||V_GROUP_CONTINUITY_TYPE||'
                T1.YEAR,
                T1.PERIOD_ID,
                T1.TOP_FLAG';
      
      
      IF f_caliber_flag = 'I' THEN 
        V_SQL_CONTINUITY_TYPE   := '''含连续性影响'' AS CONTINUITY_TYPE ,';
      ELSIF f_caliber_flag = 'E' THEN
        V_SQL_CONTINUITY_TYPE   := ' ''Y'' AS CONTINUITY_TYPE ,';
      END IF;
      V_GROUP_CONTINUITY_TYPE := ' ';
      V_FILTER1               := ' ';
    
    

        END IF;
        
        V_SQL1 := '
      INSERT INTO DM_LEV_AMT_TEMP
      (YEAR,
       PERIOD_ID,
       GROUP_CODE,
       GROUP_CN_NAME,
       GROUP_LEVEL,
       RECEIVE_QTY,
       RECEIVE_AMT_USD,
       RECEIVE_AMT_CNY,
       '||V_CATEGORY
       ||V_LV2
       ||V_LV3
       ||V_LV4
       ||V_L3_CEG_SHORT_CN_NAME
       ||V_L4_CEG_SHORT_CN_NAME
       ||V_CONTINUITY_TYPE||'
       PARENT_CODE,
       PARENT_CN_NAME,
       APPEND_FLAG,
       TOP_FLAG
       ) ' ;
       
      V_SQL2 := '
        SELECT T1.YEAR,
             T1.PERIOD_ID,
             '||V_SQL_GEOUP_CODE 
             ||V_SQL_GROUP_CN_NAME 
             ||V_SQL_GROUP_LEVEL||' AS GROUP_LEVEL,
             SUM(T1.RECEIVE_QTY) AS RECEIVE_QTY,
             SUM(T1.RECEIVE_AMT_USD) AS RECEIVE_AMT_USD,
             SUM(T1.RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY,
             '||V_SQL_CATEGORY
             ||V_SQL_LV2
             ||V_SQL_LV3
             ||V_SQL_LV4
             ||V_SQL_L3_CEG_SHORT_CN_NAME
             ||V_SQL_L4_CEG_SHORT_CN_NAME
             ||V_SQL_CONTINUITY_TYPE
             ||V_SQL_PARENT_CODE||'
             ''N'' AS APPEND_FLAG,
             T1.TOP_FLAG
        FROM DM_LEV_AMT_TEMP T1 '||V_JOIN_CONTINUITY_TYPE||'
       WHERE T1.GROUP_LEVEL = '||V_CHILD_LEVEL|| V_FILTER1 ||' 
       GROUP BY '||V_GROUP_CATEGORY
                ||V_SQL_LV2
                ||V_SQL_LV3
                ||V_SQL_LV4
                ||V_SQL_ITEM
                ||V_SQL_L3_CEG_SHORT_CN_NAME
                ||V_SQL_L4_CEG_SHORT_CN_NAME
                ||V_GROUP_CONTINUITY_TYPE||'
                T1.YEAR,
                T1.PERIOD_ID,
                T1.TOP_FLAG';

      V_SQL := V_SQL1|| V_SQL2 ||V_SQL3;
    
    
      EXECUTE IMMEDIATE V_SQL; 
   
     --写入日志
  V_STEP_NUM := V_STEP_NUM+1;
       PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
      (F_SP_NAME => V_SP_NAME,
       F_STEP_NUM => V_STEP_NUM,
       F_CAL_LOG_DESC => '第'||LEVEL_FLAG||'次循环完成，'||V_SQL_GROUP_LEVEL||'层级金额收敛完成',
       F_DML_ROW_COUNT => SQL%ROWCOUNT,
       F_RESULT_STATUS => X_SUCCESS_FLAG,
       F_ERRBUF => 'SUCCESS');
       
    END LOOP;
    
--按版本删除目标表数据    
    V_SQL :=  'DELETE FROM '||V_TO_TABLE||'  WHERE VERSION_ID = '||V_VERSION_ID||' ';

    EXECUTE IMMEDIATE V_SQL;


 --写入日志
  V_STEP_NUM := V_STEP_NUM+1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '金额表同版本'||V_VERSION_ID||'数据删除完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');
        
--插入结果表
IF f_caliber_flag = 'I' THEN 
    V_ID := ' FIN_DM_OPT_FOI.DM_FOI_LEV_REC_AMT_S.NEXTVAL AS ID, ';
    V_INSERT_ID := ' ID, ';
    V_PARAMETER := ' CONTINUITY_TYPE ';
    V_AMT_PARA  := ' RECEIVE_AMT_CNY, 
                     RECEIVE_AMT_USD, ';
          
    ELSIF f_caliber_flag = 'E' THEN 
    V_ID := '';
    V_INSERT_ID := '';
    V_PARAMETER := ' GROUP_PUR_FLAG ';
    V_AMT_PARA  := ' RMB_RECEIVE_AMT,
                     USD_RECEIVE_AMT, ';
      
    END IF;
    
     V_SQL := '
    INSERT INTO '||V_TO_TABLE||'
    (
     VERSION_ID,
     YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     RECEIVE_QTY,
     RECEIVE_AMT_CNY,
     RECEIVE_AMT_USD,
     PARENT_CODE,
     PARENT_CN_NAME,
     ITEM_CODE,
     ITEM_NAME,
     CATEGORY_CODE,
     CATEGORY_NAME,
     L4_CEG_CODE,
     L4_CEG_SHORT_CN_NAME,
     L4_CEG_CN_NAME,
     L3_CEG_CODE,
     L3_CEG_SHORT_CN_NAME,
     L3_CEG_CN_NAME,
     L2_CEG_CODE,
     L2_CEG_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     TOP_FLAG,
     APPEND_FLAG,
     '||V_PARAMETER||')
    SELECT '||V_VERSION_ID||' AS VERSION_ID,
           YEAR,
           PERIOD_ID,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           RECEIVE_QTY,
           RECEIVE_AMT_CNY,
           RECEIVE_AMT_USD,
           PARENT_CODE,
           PARENT_CN_NAME,
           ITEM_CODE,
           ITEM_NAME,
           CATEGORY_CODE,
           CATEGORY_NAME,
           L4_CEG_CODE,
           L4_CEG_SHORT_CN_NAME,
           L4_CEG_CN_NAME,
           L3_CEG_CODE,
           L3_CEG_SHORT_CN_NAME,
           L3_CEG_CN_NAME,
           L2_CEG_CODE,
           L2_CEG_CN_NAME,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           TOP_FLAG,
           ''N'' AS APPEND_FLAG,
           CONTINUITY_TYPE
      FROM DM_LEV_AMT_TEMP ';
  EXECUTE IMMEDIATE V_SQL;

 --写入日志
  V_STEP_NUM := V_STEP_NUM+1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '金额表'||V_TO_TABLE||'同版本'||V_VERSION_ID||'数据插入完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');
   
  --收集信息
 V_SQL:= ' ANALYSE '||V_TO_TABLE;
  EXECUTE IMMEDIATE V_SQL;
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
 
RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := 0;
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END;
$$
/

