-- Name: f_dm_fcst_mid_month_spart; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_mid_month_spart(f_year character varying, f_cost_type character varying, f_granularity_type character varying, f_view_flag character varying, f_keystr character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
创建人  ：lwx1165532
修改人：TWX1139790
背景描述：	1.计算国内海外全球月卷积金额
			2.筛选单SPART
			3.关联主力编码表字段
变更记录-202503：
①限制来源表取数时间范围，控制在T-3年至T年
②每次跑数删除非本版本数据，保证结果表始终只保存一版数据
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_MID_MONTH_SPART'; --存储过程名称
  V_VERSION_ID BIGINT ; --新版本号ID, 取自 FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S
  V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYY-MM'); --新的版本中文名称
  V_DIM_VERSION BIGINT ; --最新的品类-专家团映射维版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 存放当前是否已有版本号标识
  V_SQL TEXT;
  V_LV0_PROD_PARA VARCHAR(150);
  V_LV1_PROD_PARA VARCHAR(150);  
  V_LV2_PROD_PARA VARCHAR(150); 
  V_LV3_PROD_PARA VARCHAR(150); 
  V_LV4_PROD_PARA VARCHAR(150); 
  V_IN_LV0_PROD_PARA VARCHAR(150);
  V_IN_LV1_PROD_PARA VARCHAR(150);  
  V_IN_LV2_PROD_PARA VARCHAR(150); 
  V_IN_LV3_PROD_PARA VARCHAR(150); 
  V_IN_LV4_PROD_PARA VARCHAR(150);
  V_LV0_PROD_CODE VARCHAR(150);
  V_LV1_PROD_CODE VARCHAR(150);  
  V_LV2_PROD_CODE VARCHAR(150); 
  V_LV3_PROD_CODE VARCHAR(150); 
  V_LV4_PROD_CODE VARCHAR(150);
  V_INSERT_LV0_PROD_CODE VARCHAR(150);
  V_INSERT_LV1_PROD_CODE VARCHAR(150);  
  V_INSERT_LV2_PROD_CODE VARCHAR(150); 
  V_INSERT_LV3_PROD_CODE VARCHAR(150); 
  V_INSERT_LV4_PROD_CODE VARCHAR(150);
  V_DIMENSION_PARA VARCHAR(200);
  V_DIMENSION_SUBCATEGORY_PARA VARCHAR(200); 
  V_DIMENSION_SUB_DETAIL_PARA VARCHAR(200); 
  V_SPART_CODE VARCHAR(20);
  V_IN_SPART_CODE VARCHAR(20);
  V_SPART_CN_NAME VARCHAR(20);
  V_SPART_CN_NAME2 VARCHAR(40);
  V_IN_SPART_CN_NAME2 VARCHAR(40);
  V_AMT_PARA VARCHAR(20);
  V_WHERE_PARA TEXT ;
  V_HAVING_PARA TEXT;
  V_RMB_COST_AMT VARCHAR(100);
  V_SUM_RMB_AMT VARCHAR(200);
  V_FROM_TABLE VARCHAR(50);
  V_TO_TABLE VARCHAR(50);
  V_JOIN_TABLE VARCHAR(50);   --加解密表
  V_DIM_JOIN_TABLE VARCHAR(50); --主力编码表
  V_EXAMINE_TABLE  VARCHAR(50); --底层数据审视表
  V_SOFTWARE VARCHAR(200);
  V_FINAL_SOFTWARE  VARCHAR(200);
  V_SOFTWARE_DETAIL VARCHAR(500);
  V_BEGIN_DATE INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY') AS INT)-3;   -- 当前月份为1月时，取T-4年，否则取T-3年
  V_END_DATE INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY') AS INT);   -- 当前月份为1月时，取T-1年，否则取T年
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG);
   
   --判断PBI维度和成本类型选择来源表,目标表,关联表,金额字段
  IF F_COST_TYPE = 'PSP' AND F_GRANULARITY_TYPE = 'IRB' THEN 
	 V_FROM_TABLE := 'DM_FCST_ICT_PSP_PROD_UNIT_T';
	 V_TO_TABLE := 'DM_FCST_ICT_PSP_IRB_MID_MON_SPART_T'; --重量级团队目录
	 V_JOIN_TABLE := '';
  ELSIF F_COST_TYPE = 'PSP' AND F_GRANULARITY_TYPE = 'INDUS' THEN 
	 V_FROM_TABLE := 'DM_FCST_ICT_PSP_PROD_UNIT_T';
	 V_TO_TABLE := 'DM_FCST_ICT_PSP_INDUS_MID_MON_SPART_T'; --产业目录
	 V_JOIN_TABLE := '';
  ELSIF F_COST_TYPE = 'PSP' AND F_GRANULARITY_TYPE = 'PROD' THEN 
	 V_FROM_TABLE := 'DM_FCST_ICT_PSP_PROD_UNIT_T';
	 V_TO_TABLE := 'DM_FCST_ICT_PSP_PROD_MID_MON_SPART_T'; --销售目录
	 V_JOIN_TABLE := '';
  ELSIF F_COST_TYPE = 'STD' AND F_GRANULARITY_TYPE = 'IRB' THEN 
	 V_FROM_TABLE := 'DM_FCST_ICT_STD_PROD_UNIT_T';
	 V_TO_TABLE := 'DM_FCST_ICT_STD_IRB_MID_MON_SPART_T'; --重量级团队目录
	 V_JOIN_TABLE := 'DM_FCST_DATA_PRIMARY_ENCRYPT_T';
  ELSIF F_COST_TYPE = 'STD' AND F_GRANULARITY_TYPE = 'INDUS' THEN 
	 V_FROM_TABLE := 'DM_FCST_ICT_STD_PROD_UNIT_T';
	 V_TO_TABLE := 'DM_FCST_ICT_STD_INDUS_MID_MON_SPART_T'; --产业目录
	 V_JOIN_TABLE := 'DM_FCST_DATA_PRIMARY_ENCRYPT_T';
  ELSIF F_COST_TYPE = 'STD' AND F_GRANULARITY_TYPE = 'PROD' THEN 
	 V_FROM_TABLE := 'DM_FCST_ICT_STD_PROD_UNIT_T';
	 V_TO_TABLE := 'DM_FCST_ICT_STD_PROD_MID_MON_SPART_T'; --销售目录
	 V_JOIN_TABLE := 'DM_FCST_DATA_PRIMARY_ENCRYPT_T';
  ELSE 
	RETURN '入参有误';
  END IF;

  --判断成本类型获得主力编码表
  IF F_COST_TYPE = 'PSP' THEN 
	 V_DIM_JOIN_TABLE := 'DM_FCST_ICT_PSP_PBI_MAIN_CODE_DIM_T';
     V_SUM_RMB_AMT := 'SUM(RMB_COST_AMT) AS RMB_COST_AMT,';
	 V_RMB_COST_AMT := ' RMB_COST_AMT, ';
  ELSIF f_cost_type = 'STD'  THEN 
	 V_DIM_JOIN_TABLE := 'DM_FCST_ICT_STD_PBI_MAIN_CODE_DIM_T';
	 V_SUM_RMB_AMT := ' SUM(TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256''))) AS RMB_COST_AMT ,';
	 V_RMB_COST_AMT := ' GS_ENCRYPT(RMB_COST_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256'') ,';
  ELSE 
	RETURN '入参有误';
  END IF;
  
--判断PBI维度选择PBI字段	
  IF F_GRANULARITY_TYPE = 'IRB' THEN 
	  V_LV0_PROD_PARA :=' LV0_PROD_RND_TEAM_CODE ,LV0_PROD_RD_TEAM_CN_NAME, ';
	  V_LV1_PROD_PARA :='	LV1_PROD_RND_TEAM_CODE ,LV1_PROD_RD_TEAM_CN_NAME, ';  
	  V_LV2_PROD_PARA :=' LV2_PROD_RND_TEAM_CODE ,LV2_PROD_RD_TEAM_CN_NAME, '; 
	  V_LV3_PROD_PARA :=' LV3_PROD_RND_TEAM_CODE ,LV3_PROD_RD_TEAM_CN_NAME, '; 
	  V_LV4_PROD_PARA :=' LV4_PROD_RND_TEAM_CODE ,LV4_PROD_RD_TEAM_CN_NAME, '; 
	  V_IN_LV0_PROD_PARA :=' A.LV0_PROD_RND_TEAM_CODE ,A.LV0_PROD_RD_TEAM_CN_NAME, ';
	  V_IN_LV1_PROD_PARA :=' A.LV1_PROD_RND_TEAM_CODE ,A.LV1_PROD_RD_TEAM_CN_NAME, ';  
	  V_IN_LV2_PROD_PARA :=' A.LV2_PROD_RND_TEAM_CODE ,A.LV2_PROD_RD_TEAM_CN_NAME, '; 
	  V_IN_LV3_PROD_PARA :=' A.LV3_PROD_RND_TEAM_CODE ,A.LV3_PROD_RD_TEAM_CN_NAME, '; 
	  V_IN_LV4_PROD_PARA :=' A.LV4_PROD_RND_TEAM_CODE ,A.LV4_PROD_RD_TEAM_CN_NAME, '; 
      V_LV0_PROD_CODE :='LV0_PROD_RND_TEAM_CODE,';
	  V_LV1_PROD_CODE :='LV1_PROD_RND_TEAM_CODE,';  
	  V_LV2_PROD_CODE :='LV2_PROD_RND_TEAM_CODE,'; 
	  V_LV3_PROD_CODE :='LV3_PROD_RND_TEAM_CODE,'; 
	  V_LV4_PROD_CODE :='LV4_PROD_RND_TEAM_CODE,';
	  V_INSERT_LV0_PROD_CODE :=' AND A.LV0_PROD_RND_TEAM_CODE = B.LV0_PROD_RND_TEAM_CODE';
	  V_INSERT_LV1_PROD_CODE :=' AND A.LV1_PROD_RND_TEAM_CODE = B.LV1_PROD_RND_TEAM_CODE';  
	  V_INSERT_LV2_PROD_CODE :=' AND A.LV2_PROD_RND_TEAM_CODE = B.LV2_PROD_RND_TEAM_CODE'; 
	  V_INSERT_LV3_PROD_CODE :=' AND A.LV3_PROD_RND_TEAM_CODE = B.LV3_PROD_RND_TEAM_CODE'; 
	  V_INSERT_LV4_PROD_CODE :=' AND A.LV4_PROD_RND_TEAM_CODE = B.LV4_PROD_RND_TEAM_CODE';
  ELSIF  F_GRANULARITY_TYPE = 'INDUS' THEN 
	  V_LV0_PROD_PARA :=' LV0_INDUSTRY_CATG_CODE ,LV0_INDUSTRY_CATG_CN_NAME,';
	  V_LV1_PROD_PARA :='	LV1_INDUSTRY_CATG_CODE ,LV1_INDUSTRY_CATG_CN_NAME,';  
	  V_LV2_PROD_PARA :=' LV2_INDUSTRY_CATG_CODE ,LV2_INDUSTRY_CATG_CN_NAME,'; 
	  V_LV3_PROD_PARA :=' LV3_INDUSTRY_CATG_CODE ,LV3_INDUSTRY_CATG_CN_NAME,'; 
	  V_LV4_PROD_PARA :=' LV4_INDUSTRY_CATG_CODE ,LV4_INDUSTRY_CATG_CN_NAME,'; 
	  V_IN_LV0_PROD_PARA :=' A.LV0_INDUSTRY_CATG_CODE ,A.LV0_INDUSTRY_CATG_CN_NAME,';
	  V_IN_LV1_PROD_PARA :=' A.LV1_INDUSTRY_CATG_CODE ,A.LV1_INDUSTRY_CATG_CN_NAME,';  
	  V_IN_LV2_PROD_PARA :=' A.LV2_INDUSTRY_CATG_CODE ,A.LV2_INDUSTRY_CATG_CN_NAME,'; 
	  V_IN_LV3_PROD_PARA :=' A.LV3_INDUSTRY_CATG_CODE ,A.LV3_INDUSTRY_CATG_CN_NAME,'; 
	  V_IN_LV4_PROD_PARA :=' A.LV4_INDUSTRY_CATG_CODE ,A.LV4_INDUSTRY_CATG_CN_NAME,'; 
	  V_LV0_PROD_CODE :='LV0_INDUSTRY_CATG_CODE,';
	  V_LV1_PROD_CODE :='LV1_INDUSTRY_CATG_CODE,';  
	  V_LV2_PROD_CODE :='LV2_INDUSTRY_CATG_CODE,'; 
	  V_LV3_PROD_CODE :='LV3_INDUSTRY_CATG_CODE,'; 
	  V_LV4_PROD_CODE :='LV4_INDUSTRY_CATG_CODE,';
	  V_INSERT_LV0_PROD_CODE :=' AND A.LV0_INDUSTRY_CATG_CODE = B.LV0_INDUSTRY_CATG_CODE';
	  V_INSERT_LV1_PROD_CODE :=' AND A.LV1_INDUSTRY_CATG_CODE = B.LV1_INDUSTRY_CATG_CODE';  
	  V_INSERT_LV2_PROD_CODE :=' AND A.LV2_INDUSTRY_CATG_CODE = B.LV2_INDUSTRY_CATG_CODE'; 
	  V_INSERT_LV3_PROD_CODE :=' AND A.LV3_INDUSTRY_CATG_CODE = B.LV3_INDUSTRY_CATG_CODE'; 
	  V_INSERT_LV4_PROD_CODE :=' AND A.LV4_INDUSTRY_CATG_CODE = B.LV4_INDUSTRY_CATG_CODE';
  ELSIF  F_GRANULARITY_TYPE = 'PROD' THEN 
	  V_LV0_PROD_PARA :=' LV0_PROD_LIST_CODE,LV0_PROD_LIST_CN_NAME,';
	  V_LV1_PROD_PARA :='	LV1_PROD_LIST_CODE,LV1_PROD_LIST_CN_NAME,';  
	  V_LV2_PROD_PARA :=' LV2_PROD_LIST_CODE,LV2_PROD_LIST_CN_NAME,'; 
	  V_LV3_PROD_PARA :=' LV3_PROD_LIST_CODE,LV3_PROD_LIST_CN_NAME,'; 
	  V_LV4_PROD_PARA :=' LV4_PROD_LIST_CODE,LV4_PROD_LIST_CN_NAME,'; 
	  V_IN_LV0_PROD_PARA :=' A.LV0_PROD_LIST_CODE,A.LV0_PROD_LIST_CN_NAME,';
	  V_IN_LV1_PROD_PARA :=' A.LV1_PROD_LIST_CODE,A.LV1_PROD_LIST_CN_NAME,';  
	  V_IN_LV2_PROD_PARA :=' A.LV2_PROD_LIST_CODE,A.LV2_PROD_LIST_CN_NAME,'; 
	  V_IN_LV3_PROD_PARA :=' A.LV3_PROD_LIST_CODE,A.LV3_PROD_LIST_CN_NAME,'; 
	  V_IN_LV4_PROD_PARA :=' A.LV4_PROD_LIST_CODE,A.LV4_PROD_LIST_CN_NAME,'; 
	  V_LV0_PROD_CODE :='LV0_PROD_LIST_CODE,';
	  V_LV1_PROD_CODE :='LV1_PROD_LIST_CODE,';  
	  V_LV2_PROD_CODE :='LV2_PROD_LIST_CODE,'; 
	  V_LV3_PROD_CODE :='LV3_PROD_LIST_CODE,'; 
	  V_LV4_PROD_CODE :='LV4_PROD_LIST_CODE,';
	  V_INSERT_LV0_PROD_CODE :=' AND A.LV0_PROD_LIST_CODE = B.LV0_PROD_LIST_CODE';
	  V_INSERT_LV1_PROD_CODE :=' AND A.LV1_PROD_LIST_CODE = B.LV1_PROD_LIST_CODE';  
	  V_INSERT_LV2_PROD_CODE :=' AND A.LV2_PROD_LIST_CODE = B.LV2_PROD_LIST_CODE'; 
	  V_INSERT_LV3_PROD_CODE :=' AND A.LV3_PROD_LIST_CODE = B.LV3_PROD_LIST_CODE'; 
	  V_INSERT_LV4_PROD_CODE :=' AND A.LV4_PROD_LIST_CODE = B.LV4_PROD_LIST_CODE';
  ELSE 
	NULL ;
  END IF;
  
  --判断路径，选择量纲字段
  IF F_VIEW_FLAG = 'DIMENSION' THEN 
     V_DIMENSION_PARA := 'DIMENSION_CODE,DIMENSION_CN_NAME,';
     V_DIMENSION_SUBCATEGORY_PARA := 'DIMENSION_SUBCATEGORY_CODE ,DIMENSION_SUBCATEGORY_CN_NAME,'; 
     V_DIMENSION_SUB_DETAIL_PARA := 'DIMENSION_SUB_DETAIL_CODE ,DIMENSION_SUB_DETAIL_CN_NAME,';  
     V_SPART_CODE := '';
     V_SPART_CN_NAME := '';
     V_SPART_CN_NAME2 := '';
     V_IN_SPART_CODE := '';
     V_IN_SPART_CN_NAME2 := '';
     --软硬件标识
     V_SOFTWARE := 'SOFTWARE_MARK,';
     V_FINAL_SOFTWARE := 'A.SOFTWARE_MARK,';
     V_SOFTWARE_DETAIL := ' CASE WHEN SOFTWARE_MARK IS NULL THEN ''HARDWARE''
			                     WHEN SOFTWARE_MARK IN (''Hardware License'',''Software'') THEN ''SOFTWARE''
			                     WHEN SOFTWARE_MARK = ''Hardware'' THEN ''HARDWARE''
			                ELSE SOFTWARE_MARK END AS SOFTWARE_MARK ,';
   IF F_COST_TYPE = 'PSP' THEN 
	   V_HAVING_PARA := ' HAVING (SUM(PROD_UNIT_QTY) IS NOT NULL 
							AND SUM(RMB_COST_AMT) IS NOT NULL  
							AND SUM(PROD_UNIT_QTY) * SUM(RMB_COST_AMT) >= 0 
							AND ABS(SUM(RMB_COST_AMT)) > 1)	--剔除极小值
							AND ABS(SUM(PROD_UNIT_QTY)) >= 1 
							';
  ELSIF F_COST_TYPE = 'STD'  THEN 
	V_HAVING_PARA := ' HAVING  (SUM(PROD_UNIT_QTY) IS NOT NULL 
						AND SUM(TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256''))) IS NOT NULL
							AND ABS(SUM(TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256'')))) > 1
							AND SUM(PROD_UNIT_QTY) * SUM(TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256''))) >= 0
							)
							AND ABS(SUM(PROD_UNIT_QTY)) >= 1 
							';
  END IF;
   V_WHERE_PARA := ' WHERE PROD_UNIT_QTY IS NOT NULL 
							AND RMB_COST_AMT IS NOT NULL 
							AND ABS(RMB_COST_AMT) > 1
							AND PROD_UNIT_QTY * RMB_COST_AMT >= 0
								--剔除极小值
							AND ABS(PROD_UNIT_QTY) >= 1 
							';						
  ELSIF F_VIEW_FLAG = 'PROD_SPART' THEN 
   V_DIMENSION_PARA := '';
   V_DIMENSION_SUBCATEGORY_PARA := ''; 
   V_DIMENSION_SUB_DETAIL_PARA := '';  
   V_SPART_CODE := 'SPART_CODE,';
   V_SPART_CN_NAME := 'SPART_CN_NAME ,';
   V_SPART_CN_NAME2 := 'SPART_CODE AS SPART_CN_NAME ,';
   V_IN_SPART_CODE := 'A.SPART_CODE,';
   V_IN_SPART_CN_NAME2 := 'A.SPART_CODE AS SPART_CN_NAME,';
   V_SOFTWARE := 'SOFTWARE_MARK,';
   V_FINAL_SOFTWARE := 'A.SOFTWARE_MARK,';
   V_SOFTWARE_DETAIL := ' CASE WHEN SOFTWARE_MARK IS NULL THEN ''HARDWARE''
			                   WHEN SOFTWARE_MARK IN (''Hardware License'',''Software'') THEN ''SOFTWARE''
			                   WHEN SOFTWARE_MARK = ''Hardware'' THEN ''HARDWARE''
			              ELSE SOFTWARE_MARK END AS SOFTWARE_MARK ,';

  IF F_COST_TYPE = 'PSP' THEN 
	   V_HAVING_PARA := ' HAVING (SUM(PART_QTY) IS NOT NULL AND SUM(RMB_COST_AMT) IS NOT NULL 
							AND ABS(SUM(RMB_COST_AMT)) > 1	
							AND SUM(PART_QTY) * SUM(RMB_COST_AMT) >= 0
							)--剔除极小值
						  AND SPART_CODE != ''SNULL''
						  AND ABS(SUM(PART_QTY))  >= 1';
  ELSIF f_cost_type = 'STD'  THEN 
	  V_HAVING_PARA := ' HAVING  (SUM(PART_QTY) IS NOT NULL 
						   AND SUM(TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256''))) IS NOT NULL
							AND ABS(SUM(TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256''))))  > 1
							AND SUM(PART_QTY) * SUM(TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256''))) >= 0
							)	--剔除极小值
							AND SPART_CODE != ''SNULL''
							AND ABS(SUM(PART_QTY))  >= 1
							';
  END IF;

	V_WHERE_PARA := ' WHERE 
							PART_QTY IS NOT NULL AND 
							RMB_COST_AMT IS NOT NULL
							AND ABS(RMB_COST_AMT) > 1
							AND PART_QTY * RMB_COST_AMT >= 0
							AND SPART_CODE != ''SNULL''
							AND ABS(PART_QTY)  >= 1
							';
							
  END IF;
	
--新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
		
   --主力编码版本号赋值
  SELECT VERSION_ID INTO V_DIM_VERSION
    FROM
       DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 		
		
   --清空目标表数据
 DBMS_OUTPUT.PUT_LINE('-----清空临时目标表开始------'); 
 EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' T WHERE SUBSTR(T.PERIOD_ID,0,4) = '''||F_YEAR||''' AND VIEW_FLAG = '''||F_VIEW_FLAG||''' '  ;
 DBMS_OUTPUT.PUT_LINE('-----清空临时目标表结束------'); 
  
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

--202409新增底层数据审视
--先UPDATE源表将异常标识都置为空
V_SQL := 'UPDATE '||V_FROM_TABLE||' A SET EXAMINE_RESULT = NULL 
		  WHERE EXAMINE_RESULT = ''Y''  ';
  DBMS_OUTPUT.PUT_LINE('异常标识已重置');		
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;
V_EXAMINE_TABLE := ' DM_FCST_ICT_EXAMINE_RESULT_T ';

IF DAY(CURRENT_TIMESTAMP) != 8 THEN 	  
--不为8号时再按选定版本更新异常标识
V_SQL := 'UPDATE '||V_FROM_TABLE||' A SET EXAMINE_RESULT = ''Y''
 WHERE EXISTS (SELECT 1 FROM '||V_EXAMINE_TABLE||' B 
 WHERE A.SPART_CODE = B.SPART_CODE
 '||V_INSERT_LV1_PROD_CODE
  ||V_INSERT_LV2_PROD_CODE
  ||V_INSERT_LV3_PROD_CODE
  ||V_INSERT_LV4_PROD_CODE||'
	AND A.PERIOD_ID BETWEEN B.BEGIN_DATE  AND B.END_DATE
  AND A.REGION_CODE = B.REGION_CODE
  AND A.REPOFFICE_CODE = B.REPOFFICE_CODE
  AND A.OVERSEA_FLAG = B.OVERSEA_FLAG
  AND A.BG_CODE = B.BG_CODE
  AND NVL(A.HW_CONTRACT_NUM,1) =  NVL(B.HW_CONTRACT_NUM,1)
  AND B.PAGE_FLAG = ''abnormal''
  AND B.COST_TYPE = '''||F_COST_TYPE||'''   --成本类型
  AND B.GTS_TYPE = '''||F_GRANULARITY_TYPE||'''  --PBI维度
  AND B.VERSION_ID = (SELECT VERSION_ID  
    FROM
       DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = ''N''
        AND STATUS = 0
		 AND IS_RUNNING = ''Y''
        AND UPPER(DATA_TYPE) = ''DATA_REVIEW''
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1))';

  DBMS_OUTPUT.PUT_LINE('开始更新异常标识');		
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;
ELSEIF 	DAY(CURRENT_TIMESTAMP) = 8 THEN 
--为8号时按最新版本更新异常标识
V_SQL := 'UPDATE '||V_FROM_TABLE||' A SET EXAMINE_RESULT = ''Y''
 WHERE EXISTS (SELECT 1 FROM '||V_EXAMINE_TABLE||' B 
 WHERE A.SPART_CODE = B.SPART_CODE
 '||V_INSERT_LV1_PROD_CODE
  ||V_INSERT_LV2_PROD_CODE
  ||V_INSERT_LV3_PROD_CODE
  ||V_INSERT_LV4_PROD_CODE||'
  AND A.PERIOD_ID BETWEEN B.BEGIN_DATE  AND B.END_DATE
  AND A.REGION_CODE = B.REGION_CODE
  AND A.REPOFFICE_CODE = B.REPOFFICE_CODE
  AND A.OVERSEA_FLAG = B.OVERSEA_FLAG
  AND A.BG_CODE = B.BG_CODE
  AND NVL(A.HW_CONTRACT_NUM,1) =  NVL(B.HW_CONTRACT_NUM,1)
  AND B.PAGE_FLAG = ''abnormal''
  AND B.COST_TYPE = '''||F_COST_TYPE||'''   --成本类型
  AND B.GTS_TYPE = '''||F_GRANULARITY_TYPE||'''  --PBI维度
  AND B.VERSION_ID = (SELECT VERSION_ID  
    FROM
       DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = ''N''
        AND UPPER(DATA_TYPE) = ''DATA_REVIEW''
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1))';
  DBMS_OUTPUT.PUT_LINE('开始更新异常标识');		
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL; 
END IF;
   DBMS_OUTPUT.PUT_LINE('1:-----创建ITEM月卷积发货额临时表------');   
   
  --创建月卷积发货额临时表
    DROP TABLE IF EXISTS BASE_DATA_TEMP;
    CREATE TEMPORARY TABLE BASE_DATA_TEMP (
	PERIOD_ID NUMERIC,
	PERIOD_YEAR NUMERIC,
	REGION_CODE CHARACTER VARYING(50),
	REGION_CN_NAME CHARACTER VARYING(200),
	REGION_EN_NAME CHARACTER VARYING(200),
	REPOFFICE_CODE CHARACTER VARYING(50),
	REPOFFICE_CN_NAME CHARACTER VARYING(200),
	REPOFFICE_EN_NAME CHARACTER VARYING(200),
	BG_CODE CHARACTER VARYING(50),
	BG_CN_NAME CHARACTER VARYING(200),
	LV0_PROD_LIST_CODE CHARACTER VARYING(50),
	LV0_PROD_LIST_CN_NAME CHARACTER VARYING(200),
	LV1_PROD_LIST_CODE CHARACTER VARYING(50),
	LV1_PROD_LIST_CN_NAME CHARACTER VARYING(200),
	LV2_PROD_LIST_CODE CHARACTER VARYING(50),
	LV2_PROD_LIST_CN_NAME CHARACTER VARYING(200),
	LV3_PROD_LIST_CODE CHARACTER VARYING(50),
	LV3_PROD_LIST_CN_NAME CHARACTER VARYING(200),
	LV4_PROD_LIST_CODE CHARACTER VARYING(50),
	LV4_PROD_LIST_CN_NAME CHARACTER VARYING(200),
	LV0_PROD_RND_TEAM_CODE CHARACTER VARYING(100),
	LV0_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(200),
	LV1_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
	LV1_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
	LV2_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
	LV2_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
	LV3_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
	LV3_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
	LV4_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
	LV4_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
	LV0_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
	LV0_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
	LV1_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
	LV1_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
	LV2_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
	LV2_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
	LV3_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
	LV3_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
	LV4_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
	LV4_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
	DIMENSION_CODE CHARACTER VARYING(500),
	DIMENSION_CN_NAME CHARACTER VARYING(200),
	DIMENSION_EN_NAME CHARACTER VARYING(200),
	DIMENSION_SUBCATEGORY_CODE CHARACTER VARYING(500),
	DIMENSION_SUBCATEGORY_CN_NAME CHARACTER VARYING(200),
	DIMENSION_SUB_DETAIL_CODE CHARACTER VARYING(500),
	DIMENSION_SUB_DETAIL_CN_NAME CHARACTER VARYING(200),
	PROD_UNIT_QTY NUMERIC,
	RMB_COST_AMT NUMERIC,
	SPART_CODE CHARACTER VARYING(188),
	PART_QTY NUMERIC,
	OVERSEA_FLAG CHARACTER VARYING(1),
	VIEW_FLAG VARCHAR(10),
	ONLY_SPART_FLAG VARCHAR(1),
	EXAMINE_RESULT VARCHAR(20),
	SOFTWARE_MARK VARCHAR(100)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(PERIOD_ID,SPART_CODE);
	
--向月卷积发货额临时表插数,去掉华为合同号等字段
V_SQL:= 'INSERT INTO 	BASE_DATA_TEMP  (
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REGION_EN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	REPOFFICE_EN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	PROD_UNIT_QTY,
	RMB_COST_AMT ,
	'||V_SPART_CODE||'
	PART_QTY,
	OVERSEA_FLAG ,
	'||V_SOFTWARE||'
	VIEW_FLAG
	)
	SELECT 
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REGION_EN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	REPOFFICE_EN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	 PROD_UNIT_QTY,
	 RMB_COST_AMT ,
	'||V_SPART_CODE||'
	 PART_QTY,
	OVERSEA_FLAG,
	'||V_SOFTWARE||'
	VIEW_FLAG
	FROM
	(
SELECT 
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REGION_EN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	REPOFFICE_EN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	SUM(PROD_UNIT_QTY) AS PROD_UNIT_QTY,
	SUM(RMB_COST_AMT) AS RMB_COST_AMT ,
	'||V_SPART_CODE||'
	SUM(PART_QTY) AS PART_QTY,
	OVERSEA_FLAG,
	'||V_SOFTWARE||'
	VIEW_FLAG
FROM
	(
SELECT 
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REGION_EN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	REPOFFICE_EN_NAME ,
	HW_CONTRACT_NUM,
	BG_CODE ,
	BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	 SUM(PROD_UNIT_QTY) AS PROD_UNIT_QTY,
	'||V_SUM_RMB_AMT
     ||V_SPART_CODE||'
	SUM(PART_QTY) AS PART_QTY,
	OVERSEA_FLAG ,
	'||V_SOFTWARE_DETAIL||'
	'''||F_VIEW_FLAG||'''  AS VIEW_FLAG
FROM '||V_FROM_TABLE||'
WHERE  SUBSTR(PERIOD_ID,0,4) = '||f_year||'
	AND EXAMINE_RESULT IS NULL  --只要非异常的数据
	AND PERIOD_YEAR BETWEEN '||V_BEGIN_DATE||' AND '||V_END_DATE||'    -- 202503新增限制取数范围在需要的时间范围内
GROUP BY
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REGION_EN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	REPOFFICE_EN_NAME ,
	HW_CONTRACT_NUM,
	BG_CODE ,
	BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	'||V_SPART_CODE||'
	'||V_SOFTWARE||'
	OVERSEA_FLAG
	'||V_HAVING_PARA||'
	)
	GROUP BY 
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REGION_EN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	REPOFFICE_EN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	'||V_SPART_CODE||'
	OVERSEA_FLAG,
	'||V_SOFTWARE||'
	VIEW_FLAG
	)
	'||V_WHERE_PARA
	;
	
DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE IMMEDIATE V_SQL;	
	
--.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '第一次插数进月卷积发货额临时表完成,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
 --针对路径1，先做单SPART剔除逻辑
IF F_VIEW_FLAG = 'PROD_SPART' THEN 
V_SQL := 'WITH ONLY_SPART_TEMP AS (
    SELECT REGION_CODE ,REPOFFICE_CODE ,BG_CODE ,'||V_LV0_PROD_CODE ||V_LV1_PROD_CODE||V_LV2_PROD_CODE  ||V_LV3_PROD_CODE   ||V_LV4_PROD_CODE ||V_SPART_CODE||'OVERSEA_FLAG ,VIEW_FLAG				 
        FROM (SELECT REGION_CODE ,
					 REPOFFICE_CODE ,
					 BG_CODE ,
					 '||V_LV0_PROD_CODE 
					 ||V_LV1_PROD_CODE  
					 ||V_LV2_PROD_CODE  
					 ||V_LV3_PROD_CODE   
					 ||V_LV4_PROD_CODE 
					 ||V_SPART_CODE||'
					 OVERSEA_FLAG ,
					 VIEW_FLAG,
                     COUNT(1) OVER(PARTITION BY REGION_CODE ,REPOFFICE_CODE ,BG_CODE ,'||V_LV0_PROD_CODE ||V_LV1_PROD_CODE  ||V_LV2_PROD_CODE  ||V_LV3_PROD_CODE   ||V_LV4_PROD_CODE ||'OVERSEA_FLAG ,VIEW_FLAG ) AS SPARK_FLAG
                  FROM (SELECT DISTINCT  
	                           REGION_CODE ,
	                           REPOFFICE_CODE ,
	                           BG_CODE ,
	                           '||V_LV0_PROD_CODE 
	                           ||V_LV1_PROD_CODE  
	                           ||V_LV2_PROD_CODE  
	                           ||V_LV3_PROD_CODE   
	                           ||V_LV4_PROD_CODE 
	                           ||V_SPART_CODE||'
	                           OVERSEA_FLAG ,
	                           VIEW_FLAG
                            FROM BASE_DATA_TEMP T
		) A
) B
       WHERE SPARK_FLAG = 1
	   )
	   UPDATE BASE_DATA_TEMP A SET ONLY_SPART_FLAG = ''Y''
		WHERE EXISTS (SELECT 1 FROM ONLY_SPART_TEMP B WHERE A.SPART_CODE = B.SPART_CODE 
		AND A.REGION_CODE  = B.REGION_CODE
	AND A.REPOFFICE_CODE = B.REPOFFICE_CODE
	AND A.BG_CODE = B.REPOFFICE_CODE
	AND A.OVERSEA_FLAG = B.OVERSEA_FLAG 
	AND A.VIEW_FLAG = B.VIEW_FLAG
    '||V_INSERT_LV0_PROD_CODE
	 ||V_INSERT_LV1_PROD_CODE
	 ||V_INSERT_LV2_PROD_CODE
	 ||V_INSERT_LV3_PROD_CODE
	 ||V_INSERT_LV4_PROD_CODE||')'
;

DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE IMMEDIATE V_SQL;	 
DBMS_OUTPUT.PUT_LINE('ONLY_SPARK标识已更新');

--针对路径2，剔除没有量纲信息的数据
ELSIF  F_VIEW_FLAG = 'DIMENSION' THEN
V_SQL := 'DELETE FROM BASE_DATA_TEMP 
WHERE DIMENSION_CODE IS NULL 
AND DIMENSION_SUBCATEGORY_CODE IS NULL 
AND DIMENSION_SUB_DETAIL_CODE IS NULL
AND VIEW_FLAG = ''DIMENSION''
';
DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE IMMEDIATE V_SQL;	 
DBMS_OUTPUT.PUT_LINE('已剔除没有量纲信息的数据');
END IF;

--针对路径1，先造一份软硬件的数据
IF F_COST_TYPE = 'PSP' THEN 
V_SQL := '
INSERT INTO 	BASE_DATA_TEMP  (
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REGION_EN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	REPOFFICE_EN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	PROD_UNIT_QTY,
	RMB_COST_AMT ,
	'||V_SPART_CODE||'
	PART_QTY,
	OVERSEA_FLAG ,
	VIEW_FLAG,
	ONLY_SPART_FLAG,
	SOFTWARE_MARK
	)
SELECT 
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REGION_EN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	REPOFFICE_EN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	PROD_UNIT_QTY,
	RMB_COST_AMT ,
	'||V_SPART_CODE||'
	PART_QTY,
	OVERSEA_FLAG ,
	VIEW_FLAG,
	ONLY_SPART_FLAG,
	''ALL'' AS SOFTWARE_MARK
FROM  BASE_DATA_TEMP
   ';
   DBMS_OUTPUT.PUT_LINE(V_SQL);
   EXECUTE IMMEDIATE V_SQL;
END IF ;

--造一份代表处全球的数据
V_SQL:= '
	 --拿到某明细地区部下全代表处的合集
	WITH REPOFFICE_GLOBAL AS  (
SELECT 
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REGION_EN_NAME ,
	''ALL'' REPOFFICE_CODE ,
	''全选''   REPOFFICE_CN_NAME ,
	''all'' REPOFFICE_EN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	SUM(PROD_UNIT_QTY) PROD_UNIT_QTY,
	SUM(RMB_COST_AMT ) RMB_COST_AMT,
	'||V_SPART_CODE||'
	SUM(PART_QTY) PART_QTY,
	OVERSEA_FLAG ,
	VIEW_FLAG,
	'||V_SOFTWARE||'
	ONLY_SPART_FLAG
	
FROM BASE_DATA_TEMP
GROUP BY 
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REGION_EN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	'||V_SPART_CODE||'
	OVERSEA_FLAG ,
	VIEW_FLAG,
	'||V_SOFTWARE||'
	ONLY_SPART_FLAG
	),
	 --拿到全地区部全代表处的全球数据
	REGION_GLOBAL AS (
SELECT 
	PERIOD_ID ,
	PERIOD_YEAR ,
	''GLOBAL'' REGION_CODE ,
	''全球''   REGION_CN_NAME ,
	''global'' REGION_EN_NAME ,
	''ALL'' REPOFFICE_CODE ,
	''全选''   REPOFFICE_CN_NAME ,
	''all'' REPOFFICE_EN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	SUM(PROD_UNIT_QTY) PROD_UNIT_QTY,
	SUM(RMB_COST_AMT ) RMB_COST_AMT,
	'||V_SPART_CODE||'
	SUM(PART_QTY) PART_QTY,
	OVERSEA_FLAG  ,
	VIEW_FLAG,
	'||V_SOFTWARE||'
	ONLY_SPART_FLAG
FROM BASE_DATA_TEMP
GROUP BY 
	PERIOD_ID ,
	PERIOD_YEAR ,
	BG_CODE ,
	BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	'||V_SPART_CODE||'
	VIEW_FLAG,
	OVERSEA_FLAG,
	'||V_SOFTWARE||'
	ONLY_SPART_FLAG
	)
INSERT INTO 	BASE_DATA_TEMP  (
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REGION_EN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	REPOFFICE_EN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	PROD_UNIT_QTY,
	RMB_COST_AMT ,
	'||V_SPART_CODE||'
	PART_QTY,
	OVERSEA_FLAG ,
	VIEW_FLAG,
	'||V_SOFTWARE||'
	ONLY_SPART_FLAG
	)
SELECT 
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REGION_EN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	REPOFFICE_EN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	PROD_UNIT_QTY,
	RMB_COST_AMT ,
	'||V_SPART_CODE||'
	PART_QTY,
	OVERSEA_FLAG ,
	VIEW_FLAG,
	'||V_SOFTWARE||'
	ONLY_SPART_FLAG
FROM  REPOFFICE_GLOBAL 
	UNION ALL 
SELECT 
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REGION_EN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	REPOFFICE_EN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	PROD_UNIT_QTY,
	RMB_COST_AMT ,
	'||V_SPART_CODE||'
	PART_QTY,
	OVERSEA_FLAG ,
	VIEW_FLAG,
	'||V_SOFTWARE||'
	ONLY_SPART_FLAG
FROM REGION_GLOBAL
	';
DBMS_OUTPUT.PUT_LINE(V_SQL);
	EXECUTE IMMEDIATE V_SQL;

--.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '第二次插数进月卷积发货额临时表完成,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
	
--造一份集团的数据
   V_SQL:= 'INSERT INTO 	BASE_DATA_TEMP  (
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REGION_EN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	REPOFFICE_EN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	PROD_UNIT_QTY,
	RMB_COST_AMT ,
	'||V_SPART_CODE||'
	PART_QTY,
	OVERSEA_FLAG ,
	VIEW_FLAG,
	'||V_SOFTWARE||'
	ONLY_SPART_FLAG
	)
SELECT 
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REGION_EN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	REPOFFICE_EN_NAME ,
	''GR''   AS BG_CODE ,
	''集团'' AS BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||' 
	--PROD_UNIT_QTY,
	--RMB_COST_AMT,
	SUM(PROD_UNIT_QTY) AS PROD_UNIT_QTY,
	SUM(RMB_COST_AMT) AS RMB_COST_AMT,
	'||V_SPART_CODE||'
	SUM(PART_QTY) AS PART_QTY,
	--PART_QTY,
	OVERSEA_FLAG ,
	VIEW_FLAG,
	'||V_SOFTWARE||'
	ONLY_SPART_FLAG
FROM BASE_DATA_TEMP
	GROUP BY  
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REGION_EN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	REPOFFICE_EN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA
	||V_SPART_CODE||'
	OVERSEA_FLAG ,
	VIEW_FLAG,
	'||V_SOFTWARE||'
	ONLY_SPART_FLAG
	'
	;
DBMS_OUTPUT.PUT_LINE(V_SQL);
	EXECUTE IMMEDIATE V_SQL;
   
--.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '第三次插数进月卷积发货额临时表完成,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
--造一份全球的数据
   V_SQL:= 'INSERT INTO 	BASE_DATA_TEMP  (
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REGION_EN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	REPOFFICE_EN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	PROD_UNIT_QTY,
	RMB_COST_AMT ,
	'||V_SPART_CODE||'
	PART_QTY,
	OVERSEA_FLAG ,
	VIEW_FLAG,
	'||V_SOFTWARE||'
	ONLY_SPART_FLAG
	)
	SELECT 
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REGION_EN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	REPOFFICE_EN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	PROD_UNIT_QTY,
	RMB_COST_AMT ,
	'||V_SPART_CODE||'
	PART_QTY,
	''G'' OVERSEA_FLAG ,
	VIEW_FLAG,
	'||V_SOFTWARE||'
	ONLY_SPART_FLAG
	FROM BASE_DATA_TEMP
	';
DBMS_OUTPUT.PUT_LINE(V_SQL);
	EXECUTE IMMEDIATE V_SQL;
   
--.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '第四次插数进月卷积发货额临时表完成,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --向目标表插数
  V_SQL := '
  INSERT INTO '||V_TO_TABLE||' (
  VERSION_ID,
  PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	'||V_LV0_PROD_PARA 
	||V_LV1_PROD_PARA  
	||V_LV2_PROD_PARA 
	||V_LV3_PROD_PARA  
	||V_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	PROD_QTY,
	RMB_COST_AMT ,
	'||V_SPART_CODE||'
	'||V_SPART_CN_NAME||'
	OVERSEA_FLAG ,
	VIEW_FLAG,
	ONLY_SPART_FLAG,
	MAIN_FLAG,
	CODE_ATTRIBUTES,
	CREATED_BY,
	CREATION_DATE,
	LAST_UPDATED_BY,
	LAST_UPDATE_DATE,
	'||V_SOFTWARE||'
	DEL_FLAG
	)
	WITH FINAL_TEMP AS ( 
	SELECT DISTINCT 
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	A.BG_CODE ,
	A.BG_CN_NAME ,
	'||V_IN_LV0_PROD_PARA 
	||V_IN_LV1_PROD_PARA  
	||V_IN_LV2_PROD_PARA 
	||V_IN_LV3_PROD_PARA  
	||V_IN_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	PROD_UNIT_QTY,
	RMB_COST_AMT ,
	A.SPART_CODE,
	PART_QTY,
	OVERSEA_FLAG ,
	VIEW_FLAG,
	CASE WHEN ONLY_SPART_FLAG = ''Y'' THEN ''Y''
	WHEN ONLY_SPART_FLAG IS NULL THEN ''N''
	ELSE NULL END AS ONLY_SPART_FLAG,
	DECODE(B.SPART_CODE,NULL,''N'',''Y'') AS  MAIN_FLAG,
	'||V_FINAL_SOFTWARE||'
    B.CODE_ATTRIBUTES
	FROM BASE_DATA_TEMP A
	    LEFT JOIN (SELECT * FROM '||V_DIM_JOIN_TABLE||' WHERE VERSION_ID = '||V_DIM_VERSION||' ) B
	   ON A.SPART_CODE = B.SPART_CODE
	   AND A.BG_CODE = B.BG_CODE
	    '||V_INSERT_LV1_PROD_CODE
         ||V_INSERT_LV2_PROD_CODE
         ||V_INSERT_LV3_PROD_CODE
         ||V_INSERT_LV4_PROD_CODE
		 ||'
	)
	SELECT 
	DISTINCT 
	'||V_VERSION_ID||',
	A.PERIOD_ID ,
	A.PERIOD_YEAR ,
	A.REGION_CODE ,
	A.REGION_CN_NAME ,
	A.REPOFFICE_CODE ,
	A.REPOFFICE_CN_NAME ,
	A.BG_CODE ,
	A.BG_CN_NAME ,
	'||V_IN_LV0_PROD_PARA 
	||V_IN_LV1_PROD_PARA  
	||V_IN_LV2_PROD_PARA 
	||V_IN_LV3_PROD_PARA  
	||V_IN_LV4_PROD_PARA 
	||V_DIMENSION_PARA 
	||V_DIMENSION_SUBCATEGORY_PARA  
	||V_DIMENSION_SUB_DETAIL_PARA||'  
	CASE WHEN '''||F_VIEW_FLAG||''' = ''DIMENSION'' THEN PROD_UNIT_QTY
	WHEN '''||F_VIEW_FLAG||''' = ''PROD_SPART'' THEN PART_QTY
	END AS PROD_QTY,
	'||V_RMB_COST_AMT
	||V_IN_SPART_CODE
	||V_IN_SPART_CN_NAME2||'
	OVERSEA_FLAG ,
	VIEW_FLAG,
	ONLY_SPART_FLAG,
	MAIN_FLAG,
    CODE_ATTRIBUTES,
	-1 AS CREATED_BY,
	CURRENT_TIMESTAMP AS CREATION_DATE,
	-1 AS LAST_UPDATED_BY,
	CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
	'||V_SOFTWARE||'
	''N'' AS DEL_FLAG
	FROM FINAL_TEMP A'
	;
 DBMS_OUTPUT.PUT_LINE(V_SQL);
 EXECUTE IMMEDIATE V_SQL;
  
  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插数到 '||V_TO_TABLE||'表',
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  --3.收集统计信息
  V_SQL := 'ANALYZE '||V_TO_TABLE;
   EXECUTE IMMEDIATE V_SQL;
  
--202409新增底层数据审视
--所有逻辑结束后再一次置空异常标识
V_SQL := 'UPDATE '||V_FROM_TABLE||' A SET EXAMINE_RESULT = NULL 
		  WHERE EXAMINE_RESULT = ''Y''  ';
  DBMS_OUTPUT.PUT_LINE('异常标识已重置');		
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;
  
  -- 跨年时，由于本函数按分年入参进行逻辑计算，可能存留历史版本T-3年之前的数据，此处做逻辑删除
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID NOT IN ('||V_VERSION_ID||')';
  
  -- 写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除表：'||V_TO_TABLE||'里存留的不为当前版本号的数据',
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集 '||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END
$$
/

