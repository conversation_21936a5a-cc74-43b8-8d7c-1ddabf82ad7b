-- Name: f_dm_dim_item_catg_modl_ceg_energy_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_dim_item_catg_modl_ceg_energy_t(f_dim_version bigint DEFAULT NULL::bigint, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$                 
  /*
  创建人  ：罗若文
  背景描述：专家团维度映射表
  参数描述：x_success_flag ：是否成功
  事例    ：select FIN_DM_OPT_FOI.f_dm_dim_item_catg_modl_ceg_energy_t()
  */  


declare 
  
V_SP_NAME VARCHAR(500) := 'FIN_DM_OPT_FOI.F_DM_DIM_ITEM_CATG_MODL_CEG_ENERGY_T';
V_DML_ROW_COUNT    NUMBER DEFAULT 0;
V_DIM_VERSION BIGINT;-- 映射表最新的版本号  
V_STEP_MUM   BIGINT := 0; --步骤号

BEGIN
  X_RESULT_STATUS := '1';
 IF F_DIM_VERSION IS NULL THEN
   SELECT
        VERSION_ID INTO V_DIM_VERSION
    FROM
        FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T 
    WHERE
        CREATION_DATE =
         (SELECT MAX(CREATION_DATE)
            FROM FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T
           WHERE UPPER(DATA_TYPE) = 'DIMENSION'
             AND STATUS =1
        AND UPPER( DEL_FLAG ) = 'N');  
  ELSE V_DIM_VERSION := F_DIM_VERSION;
  END IF;
  
 --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
                                      
                                      
  TRUNCATE TABLE FIN_DM_OPT_FOI.DM_DIM_ITEM_CATG_MODL_CEG_ENERGY_T;
  
  --写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM + 1,
   F_CAL_LOG_DESC => '清空 FIN_DM_OPT_FOI.DM_DIM_ITEM_CATG_MODL_CEG_ENERGY_T数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   
-- 取出l2层级是生产采购认证部和数字能源采购认证部的采购组织编码、名称及专家团的编码、名称
with dm_foi_l2_l3_tmp as
 (SELECT DISTINCT L2_CEG_CODE,
                  L2_CEG_CN_NAME,
                  CASE WHEN L3_CEG_CODE IN ('12274','19462') THEN  '12274 + 19462' ELSE L3_CEG_CODE END  AS L3_CEG_CODE,
                  CASE WHEN L3_CEG_CN_NAME IN ('产品及技术合作采购认证部PP','产品及技术合作采购认证部GP') THEN  '产品及技术合作采购认证部' ELSE L3_CEG_CN_NAME END  AS L3_CEG_CN_NAME,
                  L4_CEG_CODE,
                  L4_CEG_CN_NAME
    FROM DMDIM.DM_DIM_CEG_D -- 采购专家团维表
   WHERE L2_CEG_CODE IN ('50047','12229')  --50047-数字能源采购认证部  ,12229-生产采购认证部
	 AND L3_CEG_CODE IN ('12251','12252','12253','12254','12256','12257','12274','16349','19382','16449','14029','19462','19484',
												'50050','50051','50053','50873','50874','15809','12321','13289')
AND DEL_FLAG = 'N'
  ),
  OPT_DIM_ITEM_TMP AS (
    SELECT
        * 
    FROM
        ( SELECT ITEM_CODE, ITEM_NAME, ITEM_SUBTYPE_CODE,
                 ROW_NUMBER ( ) OVER ( PARTITION BY ITEM_CODE ORDER BY SRC_LAST_UPDATE_DATE ) RN 
             FROM DWRDIM.DWR_DIM_MATERIAL_CODE_D ) 
    WHERE RN = 1 
    ),        
OPT_DIM_ICT_TMP as(
  SELECT *
  FROM FIN_DM_OPT_FOI.DM_DIM_CATG_MODL_CEG_ENERGY_T
  WHERE VERSION_ID = V_DIM_VERSION
  ),
-- 依据补录表为基础，取出l2-品类的编码及名称

DM_FOI_CEG_CATG_ITEM_TMP AS
 (SELECT DISTINCT T2.ITEM_CODE,
                  T2.ITEM_NAME,
                  A.CATEGORY_CODE,
                  A.CATEGORY_NAME,
                  B.L4_CEG_CODE,
                  A.L4_CEG_SHORT_CN_NAME,
                  A.L4_CEG_CN_NAME,
                  B.L3_CEG_CODE,
                  A.L3_CEG_SHORT_CN_NAME,
                  A.L3_CEG_CN_NAME,
                  B.L2_CEG_CODE,
                  B.L2_CEG_CN_NAME
    FROM OPT_DIM_ICT_TMP A -- 品类-模块-专家团映射表
    LEFT JOIN DM_FOI_L2_L3_TMP B
      ON A.L3_CEG_CN_NAME = B.L3_CEG_CN_NAME
     AND A.L4_CEG_CN_NAME = B.L4_CEG_CN_NAME
     LEFT JOIN OPT_DIM_ITEM_TMP T2 -- 物料编码临时表
      ON A.CATEGORY_CODE = T2.ITEM_SUBTYPE_CODE

   )   
-- 数据插入维度关联表

INSERT INTO FIN_DM_OPT_FOI.DM_DIM_ITEM_CATG_MODL_CEG_ENERGY_T
 (ITEM_CODE, -- ITEM编码
  ITEM_NAME, -- ITEM中文名称
  CATEGORY_CODE, -- 品类编码
  CATEGORY_NAME, -- 品类中文名称
  L2_CEG_CODE, -- 采购组织编码
  L2_CEG_CN_NAME, -- 采购组织中文名称
  L3_CEG_CODE, -- 专家团编码
  L3_CEG_CN_NAME, -- 专家团中文名称
  L3_CEG_SHORT_CN_NAME, -- 专家团简称
  L4_CEG_CODE, -- 模块编码
  L4_CEG_CN_NAME, -- 模块中文名称
  L4_CEG_SHORT_CN_NAME, -- 模块简称
  CREATED_BY, 
  CREATION_DATE, 
  LAST_UPDATED_BY, 
  LAST_UPDATE_DATE, 
  DEL_FLAG)
SELECT ITEM_CODE,
       ITEM_NAME,
       CATEGORY_CODE,
       CATEGORY_NAME,
       '50047' AS L2_CEG_CODE,
		'数字能源采购认证部' AS L2_CEG_CN_NAME,
       L3_CEG_CODE,
       L3_CEG_CN_NAME,
       L3_CEG_SHORT_CN_NAME,
       L4_CEG_CODE,
       L4_CEG_CN_NAME,
       L4_CEG_SHORT_CN_NAME,
       -1 AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       -1 AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       'N' AS DEL_FLAG
  FROM DM_FOI_CEG_CATG_ITEM_TMP;    
  
  

  
  
  
  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插数到 FIN_DM_OPT_FOI.DM_DIM_ITEM_CATG_MODL_CEG_ENERGY_T表,DIMENSION版本号 = '||V_DIM_VERSION,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   
-- 收集信息
ANALYSE FIN_DM_OPT_FOI.dm_dim_foi_item_catg_modl_ceg_t;
  --3.日志结束
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME      => V_SP_NAME,
  F_STEP_NUM     => V_STEP_MUM + 1,
  F_CAL_LOG_DESC => V_SP_NAME || '运行结束');

    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM + 1,
   F_CAL_LOG_DESC => '清空 FIN_DM_OPT_FOI.DM_DIM_ITEM_CATG_MODL_CEG_ENERGY_T数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
end; 
$$
/

