-- Name: f_dm_lev_public_idx; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_lev_public_idx(f_buss_dim_key character varying DEFAULT NULL::character varying, f_user_id character varying DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间：2023-07-26
修改人  ：黄心蕊 hwx1187045
背景描述：公共指数表数据更新
参数描述：参数一(F_BUSS_DIM_KEY)：业务KEY
		  参数二(F_USER_ID)：用户ID
		  参数三(X_SUCCESS_FLAG)：运行状态返回值 ‘1’为成功，‘0’为失败
****************************************************************************************************************************************************************/


DECLARE
  V_SP_NAME        VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_LEV_PUBLIC_IDX';
  V_VERSION        BIGINT;
  V_STEP_NUM       BIGINT := 0; --函数步骤号
  V_EXCEPTION_FLAG BIGINT := 0; --异常定点
  V_BASE_PERIOD_ID INT := TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '01');
  V_SQL            TEXT; --执行语句
  V_USER_ID        VARCHAR(100) := F_USER_ID;
  V_BUSS_DIM_KEY   VARCHAR(50) := F_BUSS_DIM_KEY;
  V_CHILD_LEVEL    VARCHAR(50);
  V_CHILD_CODE     VARCHAR(50);
  V_CHILD_NAME     VARCHAR(50);
  V_GROUP_CODE     VARCHAR(50);
  V_GROUP_NAME     VARCHAR(50);
  V_GROUP_LEVEL    VARCHAR(50);
  V_PARENT_CODE    TEXT; --上级CODE字段
  V_LEVEL_NUM      INT;
  V_L1             TEXT;
  V_SQL_L1         TEXT;
  V_L2             TEXT;
  V_SQL_L2         TEXT;
  V_L3             TEXT;
  V_SQL_L3         TEXT;
  V_L4             TEXT;
  V_SQL_L4         TEXT;
  V_L5             TEXT;
  V_SQL_L5         TEXT;
  V_L6             TEXT;
  V_SQL_L6         TEXT;
  V_L7             TEXT;
  V_SQL_L7         TEXT;
  V_L8             TEXT;
  V_SQL_L8         TEXT;
  V_L9             TEXT;
  V_SQL_L9         TEXT;
  V_L10            TEXT;
  V_SQL_L10        TEXT;
  
BEGIN

  X_SUCCESS_FLAG:='1';
  
  --日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  --取最新版本号
SELECT VERSION_ID INTO V_VERSION
  FROM FIN_DM_OPT_FOI.DM_PUBLIC_IDX_BASE_DETAIL_T
 WHERE USER_ID = V_USER_ID
 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  
  --写入日志
  V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
  /*字段值定义*/
  V_CHILD_LEVEL:='''L1''';
  V_GROUP_LEVEL:='''L2''';
  V_PARENT_CODE:=' T1.L3_CODE AS PARENT_CODE, ';
  V_L1		:=' L1_CODE, L1_CN_NAME, ';
  V_SQL_L1  :=' T1.L1_CODE , T1.L1_CN_NAME, ';
  V_L2      :=' L2_CODE, L2_CN_NAME, ';
  V_SQL_L2  :=' T1.L2_CODE , T1.L2_CN_NAME, ';
  V_L3      :=' L3_CODE, L3_CN_NAME, ';
  V_SQL_L3  :=' T1.L3_CODE , T1.L3_CN_NAME, ';
  V_L4     	:=' L4_CODE, L4_CN_NAME, ';
  V_SQL_L4  :=' T1.L4_CODE , T1.L4_CN_NAME, ';
  V_L5     	:=' L5_CODE, L5_CN_NAME, ';
  V_SQL_L5  :=' T1.L5_CODE , T1.L5_CN_NAME, ';
  V_L6     	:=' L6_CODE, L6_CN_NAME, ';
  V_SQL_L6  :=' T1.L6_CODE , T1.L6_CN_NAME, ';
  V_L7     	:=' L7_CODE, L7_CN_NAME, ';
  V_SQL_L7  :=' T1.L7_CODE , T1.L7_CN_NAME, ';
  V_L8     	:=' L8_CODE, L8_CN_NAME, ';
  V_SQL_L8  :=' T1.L8_CODE , T1.L8_CN_NAME, ';
  V_L9     	:=' L9_CODE, L9_CN_NAME, ';
  V_SQL_L9  :=' T1.L9_CODE , T1.L9_CN_NAME, ';
  V_L10    	:=' L10_CODE, L10_CN_NAME, ';
  V_SQL_L10 :=' T1.L10_CODE , T1.L10_CN_NAME, ';
  V_CHILD_CODE:=' L1_CODE AS GROUP_CODE, ';
  V_CHILD_NAME:=' L1_CN_NAME AS GROUP_CN_NAME, ';
  V_GROUP_CODE:=' L2_CODE AS PARENT_CODE, ';
  V_GROUP_NAME:=' L2_CN_NAME AS PARENT_NAME, ';
  
SELECT MAX(LEVEL_NUMBER) - 1 
  INTO V_LEVEL_NUM
  FROM FIN_DM_OPT_FOI.DM_PUBLIC_IDX_BASE_DETAIL_T
 WHERE USER_ID = V_USER_ID
   AND BUSS_DIM_KEY = V_BUSS_DIM_KEY;

V_SQL:='
DROP TABLE IF EXISTS DM_BASE_AMT_TEMP;
CREATE TEMPORARY TABLE DM_BASE_AMT_TEMP (
VERSION_ID,
   BASE_PERIOD_ID INT,
   PERIOD_YEAR INT,
   PERIOD_ID INT,
L1_CODE	varchar(50),
L1_CN_NAME	varchar(200),
L2_CODE	varchar(50),
L2_CN_NAME	varchar(200),
L3_CODE	varchar(50),
L3_CN_NAME	varchar(200),
L4_CODE	varchar(50),
L4_CN_NAME	varchar(200),
L5_CODE	varchar(50),
L5_CN_NAME	varchar(200),
L6_CODE	varchar(50),
L6_CN_NAME	varchar(200),
L7_CODE	varchar(50),
L7_CN_NAME	varchar(200),
L8_CODE	varchar(50),
L8_CN_NAME	varchar(200),
L9_CODE	varchar(50),
L9_CN_NAME	varchar(200),
L10_CODE	varchar(50),
L10_CN_NAME	varchar(200),
VIEW_FLAG	varchar(2),
MAPPING_NO1_CODE	varchar(50),
MAPPING_NO2_CODE	varchar(200),
MAPPING_NO3_CODE	varchar(200),
BUSS_DIM_KEY	varchar(50),
INDEX_RATE	numeric,
   PARENT_CODE varchar(200),
   SCENARIO_FLAG varchar(2),
CREATED_BY	varchar(200),
CREATION_DATE	timestamp,
LAST_UPDATED_BY	varchar(200),
LAST_UPDATE_DATE	timestamp,
DEL_FLAG	varchar(2),
USER_ID	varchar(200)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY ROUNDROBIN;';
--EXECUTE IMMEDIATE V_SQL;   
   
V_SQL := '
WITH BASE_INDEX AS
 (SELECT VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         '||V_GROUP_CODE||V_GROUP_NAME||V_PARENT_CODE||V_PARENT_NAME
		 ||V_L3||V_L4||V_L5||V_L6||V_L7||V_L8||V_L9||V_L10||' 
		 VIEW_FLAG,
         '||V_CHILD_LEVEL||' AS GROUP_LEVEL,
         MAPPING_NO1_CODE,
         MAPPING_NO2_CODE,
         MAPPING_NO3_CODE,
         BUSS_DIM_KEY,
         BASE_INDEX AS INDEX_RATE,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         USER_ID
    FROM FIN_DM_OPT_FOI.DM_PUBLIC_IDX_BASE_DETAIL_T
   WHERE VERSION_ID = '||V_VERSION||'
     AND UPPER(GROUP_LEVEL) = '||V_CHILD_LEVEL||'
     AND BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'
     AND USER_ID = '||V_USER_ID||'
     AND BUSS_DIM_KEY = '||V_BUSS_DIM_KEY||'),

LEV_WEIGHT AS
 (SELECT VERSION_ID,
         PERIOD_YEAR,
         VIEW_FLAG,
         MAPPING_NO1_CODE,
         MAPPING_NO2_CODE,
         MAPPING_NO3_CODE,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         WEIGHT_RATE,
         PARENT_CODE,
         BUSS_DIM_KEY,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         USER_ID
    FROM FIN_DM_OPT_FOI.DM_LEV_PUBLIC_IDX_WEIGHT_T
   WHERE VERSION_ID = '||V_VERSION||'
     AND USER_ID = '||V_USER_ID||'
     AND BUSS_DIM_KEY = '||V_BUSS_DIM_KEY||'
     AND UPPER(GROUP_LEVEL) = '||V_CHILD_LEVEL||') 
INSERT INTO DM_BASE_AMT_TEMP
  (VERSION_ID,
   BASE_PERIOD_ID,
   PERIOD_YEAR,
   PERIOD_ID,
   '||V_L3||V_L4||V_L5||V_L6||V_L7||V_L8||V_L9||V_L10||' VIEW_FLAG,
   MAPPING_NO1_CODE,
   MAPPING_NO2_CODE,
   MAPPING_NO3_CODE,
   BUSS_DIM_KEY,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   INDEX_RATE,
   PARENT_CODE,
   SCENARIO_FLAG,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   USER_ID)
SELECT '||V_VERSION||' AS VERSION_ID,
       '||V_BASE_PERIOD_ID||' SA BASE_PERIOD_ID,
       T1.PERIOD_YEAR,
       T1.PERIOD_ID,
       '||V_SQL_L3||V_SQL_L4||V_SQL_L5||V_SQL_L6||V_SQL_L7||V_SQL_L8||V_SQL_L9||V_SQL_L10||' T1.VIEW_FLAG,
       T1.MAPPING_NO1_CODE,
       T1.MAPPING_NO2_CODE,
       T1.MAPPING_NO3_CODE,
       T1.BUSS_DIM_KEY,
       T1.PARENT_CODE AS GROUP_CODE,
       T1.PARENT_NAME AS GROUP_CN_NAME,
       '||V_GROUP_LEVEL||' AS GROUP_LEVEL,
       SUM(INDEX_RATE),
       '||V_PARENT_CODE||' SCENARIO_FLAG,
      ''- 1''AS CREATED_BY,
       CURRENT_DATE AS CREATION_DATE,
      ''- 1''AS LAST_UPDATED_BY,
       CURRENT_DATE AS LAST_UPDATE_DATE,
      ''N''AS DEL_FLAG,
       '||V_USER_ID||' AS USER_ID
  FROM BASE_INDEX T1
  JOIN LEV_WEIGHT T2
    ON T1.GROUP_CODE = T2.GROUP_CODE
   AND T1.VIEW_FLAG = T2.VIEW_FLAG
   AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
   AND T1.PARENT_CODE = T2.PARENT_CODE
   AND T1.USER_ID = T2.T1.USER_ID
   AND T1.BUSS_DIM_KEY = T2.BUSS_DIM_KEY
   AND NVL(T1.MAPPING_NO1_CODE, 1) = NVL(T2.MAPPING_NO1_CODE, 1)
   AND NVL(T1.MAPPING_NO2_CODE, 2) = NVL(T2.MAPPING_NO2_CODE, 2)
   AND NVL(T1.MAPPING_NO3_CODE, 3) = NVL(T2.MAPPING_NO3_CODE, 3)
 GROUP BY T1.PERIOD_YEAR,
          T1.PERIOD_ID,
          T1.L3_CODE,
          '||V_SQL_L3||V_SQL_L4||V_SQL_L5||V_SQL_L6||V_SQL_L7||V_SQL_L8||V_SQL_L9||V_SQL_L10||'
          T1.VIEW_FLAG,
          T1.MAPPING_NO1_CODE,
          T1.MAPPING_NO2_CODE,
          T1.MAPPING_NO3_CODE,
          T1.BUSS_DIM_KEY,
          T1.PARENT_CODE,
          T2.PARENT_NAME;
';
			
  FOR LEVEL_FLAG IN 1 .. V_LEVEL_NUM LOOP
  
    IF LEVEL_FLAG = 1 THEN
      NULL;
    ELSIF LEVEL_FLAG = 2 THEN
      V_L3          :='';
      V_SQL_L3      :='';
      V_CHILD_LEVEL :='''L2''';
      V_GROUP_LEVEL :='''L3''';
      V_PARENT_CODE := ' T1.L4_CODE AS PARENT_CODE ';
      V_CHILD_CODE  := ' L2_CODE AS GROUP_CODE, ';
      V_CHILD_NAME  := ' L2_CN_NAME AS GROUP_CN_NAME, ';
      V_GROUP_CODE  := ' L3_CODE AS PARENT_CODE, ';
      V_GROUP_NAME  := ' L3_CN_NAME AS PARENT_NAME, ';
    ELSIF LEVEL_FLAG = 3 AND V_DIMENSION_TYPE = 'U' THEN
      V_L4          :='';
      V_SQL_L4      :='';
      V_CHILD_LEVEL :='''L3''';
      V_GROUP_LEVEL :='''L4''';
      V_PARENT_CODE := ' T1.L5_CODE AS PARENT_CODE ';
      V_CHILD_CODE  := ' L3_CODE AS GROUP_CODE, ';
      V_CHILD_NAME  := ' L3_CN_NAME AS GROUP_CN_NAME, ';
      V_GROUP_CODE  := ' L4_CODE AS PARENT_CODE, ';
      V_GROUP_NAME  := ' L4_CN_NAME AS PARENT_NAME, ';
    ELSIF LEVEL_FLAG = 4 AND V_DIMENSION_TYPE = 'U' THEN
      V_L5          :='';
      V_SQL_L5      :='';
      V_CHILD_LEVEL :='''L4''';
      V_GROUP_LEVEL :='''L5''';
      V_PARENT_CODE := ' T1.L6_CODE AS PARENT_CODE ';
      V_CHILD_CODE  := ' L4_CODE AS GROUP_CODE, ';
      V_CHILD_NAME  := ' L4_CN_NAME AS GROUP_CN_NAME, ';
      V_GROUP_CODE  := ' L5_CODE AS PARENT_CODE, ';
      V_GROUP_NAME  := ' L5_CN_NAME AS PARENT_NAME, ';
    ELSIF LEVEL_FLAG = 5 AND V_DIMENSION_TYPE = 'U' THEN
      V_L6          :='';
      V_SQL_L6      :='';
      V_CHILD_LEVEL :='''L5''';
      V_GROUP_LEVEL :='''L6''';
      V_PARENT_CODE := ' T1.L7_CODE AS PARENT_CODE ';
      V_CHILD_CODE  := ' L5_CODE AS GROUP_CODE, ';
      V_CHILD_NAME  := ' L5_CN_NAME AS GROUP_CN_NAME, ';
      V_GROUP_CODE  := ' L6_CODE AS PARENT_CODE, ';
      V_GROUP_NAME  := ' L6_CN_NAME AS PARENT_NAME, ';
    ELSIF LEVEL_FLAG = 6 AND V_DIMENSION_TYPE = 'U' THEN
      V_L7          :='';
      V_SQL_L7      :='';
      V_CHILD_LEVEL :='''L6''';
      V_GROUP_LEVEL :='''L7''';
      V_PARENT_CODE := ' T1.L8_CODE AS PARENT_CODE ';
      V_CHILD_CODE  := ' L6_CODE AS GROUP_CODE, ';
      V_CHILD_NAME  := ' L6_CN_NAME AS GROUP_CN_NAME, ';
      V_GROUP_CODE  := ' L7_CODE AS PARENT_CODE, ';
      V_GROUP_NAME  := ' L7_CN_NAME AS PARENT_NAME, ';
    ELSIF LEVEL_FLAG = 7 AND V_DIMENSION_TYPE = 'P' THEN
      V_L8          :='';
      V_SQL_L8      :='';
      V_CHILD_LEVEL :='''L7''';
      V_GROUP_LEVEL :='''L8''';
      V_PARENT_CODE := ' T1.L9_CODE AS PARENT_CODE ';
      V_CHILD_CODE  := ' L7_CODE AS GROUP_CODE, ';
      V_CHILD_NAME  := ' L7_CN_NAME AS GROUP_CN_NAME, ';
      V_GROUP_CODE  := ' L8_CODE AS PARENT_CODE, ';
      V_GROUP_NAME  := ' L8_CN_NAME AS PARENT_NAME, ';
    ELSIF LEVEL_FLAG = 8 AND V_DIMENSION_TYPE = 'P' THEN
      V_L9          :='';
      V_SQL_L9      :='';
      V_CHILD_LEVEL :='''L8''';
      V_GROUP_LEVEL :='''L9''';
      V_PARENT_CODE := ' T1.L10_CODE AS PARENT_CODE ';
      V_CHILD_CODE  := ' L8_CODE AS GROUP_CODE, ';
      V_CHILD_NAME  := ' L8_CN_NAME AS GROUP_CN_NAME, ';
      V_GROUP_CODE  := ' L9_CODE AS PARENT_CODE, ';
      V_GROUP_NAME  := ' L9_CN_NAME AS PARENT_NAME, ';
    ELSIF LEVEL_FLAG = 9 AND V_DIMENSION_TYPE = 'P' THEN
      V_L10         :='';
      V_SQL_L10     :='';
      V_CHILD_LEVEL :='''L9''';
      V_GROUP_LEVEL :='''L10''';
      V_PARENT_CODE :='';
      V_CHILD_CODE  := ' L9_CODE AS GROUP_CODE, ';
      V_CHILD_NAME  := ' L9_CN_NAME AS GROUP_CN_NAME, ';
      V_GROUP_CODE  := ' L10_CODE AS PARENT_CODE, ';
      V_GROUP_NAME  := ' L10_CN_NAME AS PARENT_NAME, ';
    END IF;
    
  V_SQL := '
WITH BASE_INDEX AS
 (SELECT VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         '||V_GROUP_CODE||V_GROUP_NAME||V_PARENT_CODE||V_PARENT_NAME
		 ||V_L3||V_L4||V_L5||V_L6||V_L7||V_L8||V_L9||V_L10||' 
		 VIEW_FLAG,
         '||V_CHILD_LEVEL||' AS GROUP_LEVEL,
         MAPPING_NO1_CODE,
         MAPPING_NO2_CODE,
         MAPPING_NO3_CODE,
         BUSS_DIM_KEY,
         BASE_INDEX AS INDEX_RATE,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         USER_ID
    FROM FIN_DM_OPT_FOI.DM_PUBLIC_IDX_BASE_DETAIL_T
   WHERE VERSION_ID = '||V_VERSION||'
     AND UPPER(GROUP_LEVEL) = '||V_CHILD_LEVEL||'
     AND BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'
     AND USER_ID = '||V_USER_ID||'
     AND BUSS_DIM_KEY = '||V_BUSS_DIM_KEY||'),

LEV_WEIGHT AS
 (SELECT VERSION_ID,
         PERIOD_YEAR,
         VIEW_FLAG,
         MAPPING_NO1_CODE,
         MAPPING_NO2_CODE,
         MAPPING_NO3_CODE,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         WEIGHT_RATE,
         PARENT_CODE,
         BUSS_DIM_KEY,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         USER_ID
    FROM FIN_DM_OPT_FOI.DM_LEV_PUBLIC_IDX_WEIGHT_T
   WHERE VERSION_ID = '||V_VERSION||'
     AND USER_ID = '||V_USER_ID||'
     AND BUSS_DIM_KEY = '||V_BUSS_DIM_KEY||'
     AND UPPER(GROUP_LEVEL) = '||V_CHILD_LEVEL||') 
INSERT INTO DM_BASE_AMT_TEMP
  (VERSION_ID,
   BASE_PERIOD_ID,
   PERIOD_YEAR,
   PERIOD_ID,
   '||V_L3||V_L4||V_L5||V_L6||V_L7||V_L8||V_L9||V_L10||' VIEW_FLAG,
   MAPPING_NO1_CODE,
   MAPPING_NO2_CODE,
   MAPPING_NO3_CODE,
   BUSS_DIM_KEY,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   INDEX_RATE,
   PARENT_CODE,
   SCENARIO_FLAG,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   USER_ID)
	 SELECT '||V_VERSION||' AS VERSION_ID,
       '||V_BASE_PERIOD_ID||' SA BASE_PERIOD_ID,
       T1.PERIOD_YEAR,
       T1.PERIOD_ID,
       '||V_SQL_L3||V_SQL_L4||V_SQL_L5||V_SQL_L6||V_SQL_L7||V_SQL_L8||V_SQL_L9||V_SQL_L10||' T1.VIEW_FLAG,
       T1.MAPPING_NO1_CODE,
       T1.MAPPING_NO2_CODE,
       T1.MAPPING_NO3_CODE,
       T1.BUSS_DIM_KEY,
       T1.PARENT_CODE AS GROUP_CODE,
       T1.PARENT_NAME AS GROUP_CN_NAME,
       '||V_GROUP_LEVEL||' AS GROUP_LEVEL,
       SUM(INDEX_RATE),
       '||V_PARENT_CODE||' SCENARIO_FLAG,
      ''- 1''AS CREATED_BY,
       CURRENT_DATE AS CREATION_DATE,
      ''- 1''AS LAST_UPDATED_BY,
       CURRENT_DATE AS LAST_UPDATE_DATE,
      ''N''AS DEL_FLAG,
       '||V_USER_ID||' AS USER_ID
  FROM BASE_INDEX T1
  JOIN LEV_WEIGHT T2
    ON T1.GROUP_CODE = T2.GROUP_CODE
   AND T1.VIEW_FLAG = T2.VIEW_FLAG
   AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
   AND T1.PARENT_CODE = T2.PARENT_CODE
   AND T1.USER_ID = T2.T1.USER_ID
   AND T1.BUSS_DIM_KEY = T2.BUSS_DIM_KEY
   AND NVL(T1.MAPPING_NO1_CODE, 1) = NVL(T2.MAPPING_NO1_CODE, 1)
   AND NVL(T1.MAPPING_NO2_CODE, 2) = NVL(T2.MAPPING_NO2_CODE, 2)
   AND NVL(T1.MAPPING_NO3_CODE, 3) = NVL(T2.MAPPING_NO3_CODE, 3)
 GROUP BY T1.PERIOD_YEAR,
          T1.PERIOD_ID,
          T1.L3_CODE,
          '||V_SQL_L3||V_SQL_L4||V_SQL_L5||V_SQL_L6||V_SQL_L7||V_SQL_L8||V_SQL_L9||V_SQL_L10||'
          T1.VIEW_FLAG,
          T1.MAPPING_NO1_CODE,
          T1.MAPPING_NO2_CODE,
          T1.MAPPING_NO3_CODE,
          T1.BUSS_DIM_KEY,
          T1.PARENT_CODE,
          T2.PARENT_NAME;
';
			
  --EXECUTE IMMEDIATE V_SQL;
  
   --写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '第'||LEVEL_FLAG||'次循环开始'||V_GROUP_LEVEL||'层级指数收敛完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');  
  
  END LOOP;

  V_SQL := 'DELETE FROM FIN_DM_OPT_FOI.DM_LEV_PUBLIC_IDX WHERE VERSION_ID = '||V_VERSION||';';
 -- EXECUTE IMMEDIATE V_SQL;	
  
   --写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '公共指数表同版本数据删除完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');  

  V_SQL:='
INSERT INTO FIN_DM_OPT_FOI.DM_LEV_PUBLIC_IDX_T
  (ID,
   VERSION_ID,
   PERIOD_YEAR,
   PERIOD_ID,
   BASE_PERIOD_ID,
   VIEW_FLAG,
   MAPPING_NO1_CODE,
   MAPPING_NO2_CODE,
   MAPPING_NO3_CODE,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   INDEX_RATE,
   PARENT_CODE,
   APPEND_FLAG,
   BUSS_DIM_KEY,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   USER_ID)
  SELECT FIN_DM_OPT_FOI.DM_LEV_PUBLIC_IDX_S.NEXTVAL AS ID,
         VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         BASE_PERIOD_ID,
         VIEW_FLAG,
         MAPPING_NO1_CODE,
         MAPPING_NO2_CODE,
         MAPPING_NO3_CODE,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         INDEX_RATE,
         PARENT_CODE,
         APPEND_FLAG,
         BUSS_DIM_KEY,
         '' - 1 '' AS CREATED_BY,
         CURRENT_DATE AS CREATION_DATE,
         '' - 1 '' AS LAST_UPDATED_BY,
         CURRENT_DATE AS LAST_UPDATE_DATE,
         '' N'' AS DEL_FLAG,
         USER_ID
    FROM DM_BASE_AMT_TEMP
	WHERE VERSION_ID = ''01'';';
    
 -- EXECUTE IMMEDIATE V_SQL;	
  
 --写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '公共指数表插数完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');  
    
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_EXCEPTION_FLAG,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; $$
/

