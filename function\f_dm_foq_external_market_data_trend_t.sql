-- Name: f_dm_foq_external_market_data_trend_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foq_external_market_data_trend_t(p_version_id integer DEFAULT NULL::integer, p_version_code character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：2024-10-15
创建人  ：朱雅欣
背景描述：外部市场数据趋势表,调用该函数将相对应的数据生成导入到目标表中
参数描述：参数(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_foq_external_market_data_trend_t()

*/


DECLARE
	v_sp_name varchar(200) := 'fin_dm_opt_foi.f_dm_foq_external_market_data_trend_t';
	v_tbl_name varchar(200) := 'fin_dm_opt_foi.dm_foq_external_market_data_trend_t';
	v_dml_row_count number default 0 ;
	v_max_version_code varchar(30);


BEGIN
	x_success_flag := '1';                          --1表示成功
	

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,               --sp名称
        p_log_para_list => '',                    --参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '外部市场数据趋势'||v_tbl_name||'：开始运行',--日志描述
        p_log_formula_sql_txt => null,            --错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null                      --错误编码
      ) ;
						
     --从 外部市场数据趋势表 中，删除版本信息表中执行失败的 version_id 数据
        delete from fin_dm_opt_foi.dm_foq_external_market_data_trend_t t1 
		 where t1.version_id in (select distinct t2.version_id from fin_dm_opt_foi.dm_foq_version_info_t t2 where  t2.step='2001' and t2.module_type = 'IT三大件');		 		 
		 

		
		-- 如果p_version_code为空，则取 补录表版本状态信息表 中 的最大版本编码，如果p_version_code不为空，则取传入的p_version_code   
		if (p_version_code is null or p_version_code = '') then
        select max(version_code) as max_version_code into v_max_version_code 
		from fin_dm_opt_foi.apd_foq_version_code_status_info_t 
		where upper(status)='FINAL'
		and upper(source_name) = 'APD_FOQ_EXTERNAL_MARKET_DATA_T';
		else 
		select  p_version_code into v_max_version_code ;
		end if
          ; 
		  
		  
		  
		  DROP TABLE IF EXISTS apd_foq_external_market_data_tmp;
	CREATE TEMPORARY TABLE apd_foq_external_market_data_tmp (
	     query_key        varchar(2000)
        ,version_code     varchar(100)
        ,period_id        numeric	
        ,index_code       varchar(600)
        ,index_name       varchar(1000)
        ,unit_name        varchar(50)
        ,currency         varchar(50)
        ,source_name      varchar(1000)
        ,data_value       numeric	
        ,data_type        varchar(100)
        ,data_date        varchar(100)	
        ,index_short_name varchar(600)
        ,price_type       varchar(600)
        ,date_type        varchar(600)
	) on commit preserve rows distribute by replication
	;
	
	
	
	DROP TABLE IF EXISTS dm_foq_external_market_data_trend_tmp;
	CREATE TEMPORARY TABLE dm_foq_external_market_data_trend_tmp (
	    version_id        int
   ,period_id         numeric			
   ,data_date         varchar(100)			
   ,data_type         varchar(100)
   ,index_code        varchar(600)
   ,index_name        varchar(1000)
   ,index_short_name  varchar(1000)
   ,source_name       varchar(1000)
   ,source_table      varchar(1000)
   ,unit_name         varchar(1000)
   ,currency          varchar(50)
   ,data_value        numeric	
   ,price_type        varchar(600)
   ,date_type         varchar(600)
   ,data_value_yoy    numeric(38,10)
   ,data_value_pp_ptd numeric(38,10)  
	) on commit preserve rows distribute by replication
	;
	
	
	-- 取 外部市场数据补录表 的最大版本的所有时间的实际数和最大会计期的预测数
	INSERT INTO apd_foq_external_market_data_tmp (        
         version_code     
        ,period_id        
        ,index_code       
        ,index_name       
        ,unit_name        
        ,currency         
        ,source_name      
        ,data_value       
        ,data_type        
        ,data_date        
        ,index_short_name 
        ,price_type       
        ,date_type        
     )
	  SELECT version_code     
        ,period_id        
        ,index_code       
        ,index_name       
        ,unit_name        
        ,currency         
        ,source_name      
        ,data_value       
        ,data_type        
        ,data_date        
        ,index_short_name 
        ,price_type       
        ,date_type                                    
    FROM  fin_dm_opt_foi.apd_foq_external_market_data_t 
	 WHERE  version_code = v_max_version_code
	   and  (data_value is not null or data_value <> 0 )
       and  data_type = '实际数'	   
	   union all 
  SELECT version_code     
        ,period_id        
        ,index_code       
        ,index_name       
        ,unit_name        
        ,currency         
        ,source_name      
        ,data_value       
        ,data_type        
        ,data_date        
        ,index_short_name 
        ,price_type       
        ,date_type                                    
    FROM  fin_dm_opt_foi.apd_foq_external_market_data_t t1
	 WHERE  version_code = v_max_version_code
	   and  period_id = ( SELECT max(period_id)                                         
                            FROM  fin_dm_opt_foi.apd_foq_external_market_data_t t2
	                       WHERE  version_code = v_max_version_code
						     and t1.index_code = t2.index_code)
	   and  (data_value is not null or data_value <> 0 )
       and  data_type = '预测数'	   
	 ;
	 
	 v_dml_row_count := sql%rowcount;	-- 收集数据量
     -- 开始记录日志
        perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,               --sp名称
        p_log_para_list => '',                    --参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '外部市场数据补录表最大版本的最大会计期数据 apd_foq_external_market_data_tmp，数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,            --错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null                      --错误编码
      ) ;
	
		-- 计算月度数据的同比和环比
		INSERT INTO dm_foq_external_market_data_trend_tmp
    (   version_id	        -- 版本ID
      , period_id	        -- 会计期
      , data_date	        -- 数据日期
      , data_type	        -- 数据类别（ACT 实际数、FCST 预测数）
      , index_code	        -- 指标编码
      , index_name	        -- 指标名称
      , index_short_name	-- 指标简称
      , source_name	        -- 数据源
      , source_table	    -- 来源表
      , unit_name	        -- 单位
      , currency	        -- 币种
      , data_value	        -- 数据值
      , price_type	        -- 价格类型
      , date_type	        -- 价格时间跨度(月度、季度、年度)
      , data_value_yoy	    -- 同比
      , data_value_pp_ptd	-- 环比
     )
 SELECT p_version_id as version_id -- 版本ID
      , t1.period_id	           -- 会计期
      , t1.data_date	           -- 数据日期
      , t1.data_type	           -- 数据类别（ACT 实际数、FCST 预测数）
      , t1.index_code	           -- 指标编码
      , t1.index_name	           -- 指标名称
      , t1.index_short_name        -- 指标简称
      , t1.source_name	           -- 数据源
      , 'APD_FOQ_EXTERNAL_MARKET_DATA_T' as source_table	        -- 来源表
      , t1.unit_name	           -- 单位
      , t1.currency	               -- 币种
      , t1.data_value	           -- 数据值
      , t1.price_type	           -- 价格类型
      , t1.date_type	           -- 价格时间跨度(月度、季度、年度)
      , (t1.data_value-t2.data_value)/t2.data_value AS data_value_yoy        --同比
      , (t1.data_value-t3.data_value)/t3.data_value AS data_value_pp_ptd     --环比
 FROM  apd_foq_external_market_data_tmp      t1
 LEFT JOIN apd_foq_external_market_data_tmp  t2
   ON  t1.period_id	        = t2.period_id	                
   and t1.index_code	    = t2.index_code	  
   and t1.index_name	    = t2.index_name	  
   and t1.index_short_name	= t2.index_short_name
   and nvl(t1.source_name,'SNULL') = nvl(t2.source_name,'SNULL')  
   and t1.unit_name	        = t2.unit_name	      
   and t1.currency	        = t2.currency	      
   and t1.price_type	    = t2.price_type	  
   and t1.date_type	        = t2.date_type	      
  AND  t2.data_date=TO_CHAR(to_date(t1.data_date,'yyyyMM')- interval '12months' ,'yyyyMM')::numeric
 LEFT JOIN apd_foq_external_market_data_tmp  t3
    ON  t1.period_id	        = t3.period_id	          	      
   and t1.index_code	    = t3.index_code	  
   and t1.index_name	    = t3.index_name	  
   and t1.index_short_name	= t3.index_short_name
   and nvl(t1.source_name,'SNULL') = nvl(t3.source_name,'SNULL')  
   and t1.unit_name	        = t3.unit_name	      
   and t1.currency	        = t3.currency	      
   and t1.price_type	    = t3.price_type	  
   and t1.date_type	        = t3.date_type	 
  AND  t3.data_date=TO_CHAR(to_date(t1.data_date,'yyyyMM')- interval '1months'  ,'yyyyMM')::numeric
  where t1.date_type = '月度'
 ;
 
      v_dml_row_count := sql%rowcount;	-- 收集数据量
	  
     -- 开始记录日志
        perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,               --sp名称
        p_log_para_list => '',                    --参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '月度数据的同比和环比 dm_foq_external_market_data_trend_tmp，数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,            --错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null                      --错误编码
      ) ;
        
      -- 计算 季度 数据的同比（去年同季度对比）、环比（与上个季度对比）
		INSERT INTO dm_foq_external_market_data_trend_tmp
    (   version_id	        -- 版本ID
      , period_id	        -- 会计期
      , data_date	        -- 数据日期
      , data_type	        -- 数据类别（ACT 实际数、FCST 预测数）
      , index_code	        -- 指标编码
      , index_name	        -- 指标名称
      , index_short_name	-- 指标简称
      , source_name	        -- 数据源
      , source_table	    -- 来源表
      , unit_name	        -- 单位
      , currency	        -- 币种
      , data_value	        -- 数据值
      , price_type	        -- 价格类型
      , date_type	        -- 价格时间跨度(月度、季度、年度)
      , data_value_yoy	    -- 同比
      , data_value_pp_ptd	-- 环比
     )
 SELECT p_version_id as version_id -- 版本ID
      , t1.period_id	           -- 会计期
      , t1.data_date	           -- 数据日期
      , t1.data_type	           -- 数据类别（ACT 实际数、FCST 预测数）
      , t1.index_code	           -- 指标编码
      , t1.index_name	           -- 指标名称
      , t1.index_short_name        -- 指标简称
      , t1.source_name	           -- 数据源
      , 'APD_FOQ_EXTERNAL_MARKET_DATA_T' as source_table	        -- 来源表
      , t1.unit_name	           -- 单位
      , t1.currency	               -- 币种
      , t1.data_value	           -- 数据值
      , t1.price_type	           -- 价格类型
      , t1.date_type	           -- 价格时间跨度(月度、季度、年度)
      , (t1.data_value-t2.data_value)/t2.data_value AS data_value_yoy        --同比
      , (t1.data_value-t3.data_value)/t3.data_value AS data_value_pp_ptd     --环比 	 
 FROM  apd_foq_external_market_data_tmp      t1
 LEFT JOIN apd_foq_external_market_data_tmp  t2
   ON  t1.period_id	        = t2.period_id	                  
   and t1.index_code	    = t2.index_code	  
   and t1.index_name	    = t2.index_name	  
   and t1.index_short_name	= t2.index_short_name
   and nvl(t1.source_name,'SNULL') = nvl(t2.source_name,'SNULL')  
   and t1.unit_name	        = t2.unit_name	      
   and t1.currency	        = t2.currency	       
   and t1.price_type	    = t2.price_type	  
   and t1.date_type	        = t2.date_type	      
  AND  t2.data_date=to_char(substr(t1.data_date,1,4)::numeric-1)||substr(t1.data_date,5,2)
 LEFT JOIN apd_foq_external_market_data_tmp  t3
   ON  t1.period_id	        = t3.period_id	                 
   and t1.index_code	    = t3.index_code	  
   and t1.index_name	    = t3.index_name	  
   and t1.index_short_name	= t3.index_short_name
   and nvl(t1.source_name,'SNULL') = nvl(t3.source_name,'SNULL')  
   and t1.unit_name	        = t3.unit_name	      
   and t1.currency	        = t3.currency	      
   and t1.price_type	    = t3.price_type	  
   and t1.date_type	        = t3.date_type	
  AND  t3.data_date = case when substr(t1.data_date,6,1)::numeric = 1 then to_char(substr(t1.data_date,1,4)::numeric - 1)||'Q4' 
                      else substr(t1.data_date,1,5)||to_char(substr(t1.data_date,6,1)::numeric-1)
                      end
  where t1.date_type = '季度'
 ;
		 
		   v_dml_row_count := sql%rowcount;	-- 收集数据量
		   
     -- 开始记录日志
        perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,               --sp名称
        p_log_para_list => '',                    --参数
        p_log_step_num  => 4,
        p_log_cal_log_desc => '季度数据的同比、环比 dm_foq_external_market_data_trend_tmp，数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,            --错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null                      --错误编码
      ) ;
		 
		 
	 -- 计算 年度 数据的同比
		INSERT INTO dm_foq_external_market_data_trend_tmp
    (   version_id	        -- 版本ID
      , period_id	        -- 会计期
      , data_date	        -- 数据日期
      , data_type	        -- 数据类别（ACT 实际数、FCST 预测数）
      , index_code	        -- 指标编码
      , index_name	        -- 指标名称
      , index_short_name	-- 指标简称
      , source_name	        -- 数据源
      , source_table	    -- 来源表
      , unit_name	        -- 单位
      , currency	        -- 币种
      , data_value	        -- 数据值
      , price_type	        -- 价格类型
      , date_type	        -- 价格时间跨度(月度、季度、年度)
      , data_value_yoy	    -- 同比
     )
 SELECT p_version_id as version_id -- 版本ID
      , t1.period_id	           -- 会计期
      , t1.data_date	           -- 数据日期
      , t1.data_type	           -- 数据类别（ACT 实际数、FCST 预测数）
      , t1.index_code	           -- 指标编码
      , t1.index_name	           -- 指标名称
      , t1.index_short_name        -- 指标简称
      , t1.source_name	           -- 数据源
      , 'APD_FOQ_EXTERNAL_MARKET_DATA_T' as source_table	        -- 来源表
      , t1.unit_name	           -- 单位
      , t1.currency	               -- 币种
      , t1.data_value	           -- 数据值
      , t1.price_type	           -- 价格类型
      , t1.date_type	           -- 价格时间跨度(月度、季度、年度)
      , (t1.data_value-t2.data_value)/t2.data_value AS data_value_yoy        --同比
 FROM  apd_foq_external_market_data_tmp      t1
 LEFT JOIN apd_foq_external_market_data_tmp  t2
   ON  t1.period_id	        = t2.period_id	          	      
   and t1.index_code	    = t2.index_code	  
   and t1.index_name	    = t2.index_name	  
   and t1.index_short_name	= t2.index_short_name
   and nvl(t1.source_name,'SNULL') = nvl(t2.source_name,'SNULL')  
   and t1.unit_name	        = t2.unit_name	      
   and t1.currency	        = t2.currency	      
   and t1.price_type	    = t2.price_type	  
   and t1.date_type	        = t2.date_type	      
  AND  t2.data_date=t1.data_date::numeric- 1
  where t1.date_type = '年度'
 ;
		
		 v_dml_row_count := sql%rowcount;	-- 收集数据量
		   
     -- 开始记录日志
        perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,               --sp名称
        p_log_para_list => '',                    --参数
        p_log_step_num  => 5,
        p_log_cal_log_desc => ' 年度 数据的同比 dm_foq_external_market_data_trend_tmp，数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,            --错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null                      --错误编码
      ) ;
		
		delete from fin_dm_opt_foi.dm_foq_external_market_data_trend_t where version_id = p_version_id ;
		
		-- 将月度、季度、年度的计算结果入到目标表
      insert into fin_dm_opt_foi.dm_foq_external_market_data_trend_t
	  (  version_id                 -- 版本ID
       , period_id                  -- 会计期
       , data_date                  -- 数据日期
       , data_type                  -- 数据类别（ACT 实际数、FCST 预测数）
       , index_code                 -- 指标编码
       , index_name                 -- 指标名称
       , index_short_name           -- 指标简称
       , source_name                -- 数据源
       , source_table               -- 来源表
       , unit_name                  -- 单位
       , currency                   -- 币种
       , data_value                 -- 数据值
       , price_type                 -- 价格类型
       , date_type                  -- 价格时间跨度(月度、季度、年度)
       , data_value_yoy             -- 同比
       , data_value_pp_ptd          -- 环比
       , remark                     -- 备注
       , created_by                 -- 创建人
       , creation_date              -- 创建时间
       , last_updated_by            -- 修改人
       , last_update_date           -- 修改时间
       , del_flag                   -- 是否删除	  
	  )
	  select version_id                 -- 版本ID
       , period_id                  -- 会计期
       , data_date                  -- 数据日期
       , data_type                  -- 数据类别（ACT 实际数、FCST 预测数）
       , index_code                 -- 指标编码
       , index_name                 -- 指标名称
       , index_short_name           -- 指标简称
       , source_name                -- 数据源
       , source_table               -- 来源表
       , unit_name                  -- 单位
       , currency                   -- 币种
       , data_value                 -- 数据值
       , price_type                 -- 价格类型
       , date_type                  -- 价格时间跨度(月度、季度、年度)
       , data_value_yoy             -- 同比
       , data_value_pp_ptd          -- 环比
       , '' as remark
  	   , -1 as created_by
  	   , current_timestamp as creation_date
  	   , -1 as last_updated_by
  	   , current_timestamp as last_update_date
  	   , 'N' as del_flag 
	  from dm_foq_external_market_data_trend_tmp
	  ;
		
	v_dml_row_count := nvl(sql%rowcount,0);	-- 收集数据量

  -- 写结束日志
    perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,      --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',         --参数
        p_log_step_num  => 6,
        p_log_cal_log_desc => '外部市场数据趋势'||v_tbl_name||':计算后的数据量:'||v_dml_row_count||'结束运行',--日志描述
        p_log_formula_sql_txt => null, --错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null           --错误编码
      ) ;



    --收集统计信息
    analyse fin_dm_opt_foi.dm_foq_external_market_data_trend_t;

exception
  	when others then

      perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                     --版本
        p_log_sp_name => v_sp_name,                   --sp名称
        p_log_para_list => '',                        --参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,             --错误信息
		p_log_row_count => null,
		p_log_errbuf => sqlstate                      --错误编码
      ) ;
	x_success_flag := '2001';	                      --2001表示失败



 end;
 $$
/

