-- Name: f_dm_foc_made_custom_annual; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_made_custom_annual(f_industry_flag character varying, f_version_id bigint DEFAULT NULL::bigint, f_custom_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2023-08-18
  创建人  ：唐钦
  背景描述：根据用户在页面重新对各层级进行自选组合的维度，计算年度分析页面涉及的单年权重、年度涨跌幅以及状态码数据
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_MADE_CUSTOM_ANNUAL()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_MADE_CUSTOM_ANNUAL'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  
  V_CUSTOM_ID BIGINT := F_CUSTOM_ID;
  V_SQL_CUSTOM_ID TEXT; --筛选条件
  V_SQL TEXT; --执行语句
  V_YEAR BIGINT := YEAR(CURRENT_TIMESTAMP);   -- 当前年份
  V_LAST_YEAR_FLAG varchar(50);
  V_YEAR_FLAG varchar(50);
  V_YEAR_APPEND varchar(50);
  
  -- 202405版本新增
  V_VERSION_TABLE VARCHAR(100);
  V_TO_AMP_TABLE VARCHAR(100);
  V_TO_STATUS_TABLE VARCHAR(100);
  V_TO_WEIGHT_TABLE VARCHAR(100);
  V_FROM_AVG_TABLE VARCHAR(100);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
 -- 判断产业项目为ICT还是数字能源
  IF F_INDUSTRY_FLAG = 'I' THEN   -- ICT  
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T';
     V_FROM_AVG_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_CUS_VIEW_ANNL_COST_T';
     V_TO_AMP_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_CUSTOM_ANNUAL_AMP_T';
     V_TO_STATUS_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_CUSTOM_ANNUAL_STATUS_T';
     V_TO_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_CUSTOM_ANNUAL_WEIGHT_T';
  ELSIF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源 
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T';
     V_FROM_AVG_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_CUS_VIEW_ANNL_COST_T';
     V_TO_AMP_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_CUSTOM_ANNUAL_AMP_T';
     V_TO_STATUS_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_CUSTOM_ANNUAL_STATUS_T';
     V_TO_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_CUSTOM_ANNUAL_WEIGHT_T';
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407版本新增IAS
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T';
     V_FROM_AVG_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_CUS_VIEW_ANNL_COST_T';
     V_TO_AMP_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_CUSTOM_ANNUAL_AMP_T';
     V_TO_STATUS_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_CUSTOM_ANNUAL_STATUS_T';
     V_TO_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_CUSTOM_ANNUAL_WEIGHT_T';
  END IF;
   
  --版本号入参判断，当入参为空，取TOP规格品清单最新版本号
  IF F_VERSION_ID IS NULL THEN
     V_SQL := '
       SELECT VERSION_ID 
       FROM
           '||V_VERSION_TABLE||'
       WHERE
           DEL_FLAG = ''N''
           AND STATUS = 1
           AND UPPER(DATA_TYPE) = ''CATEGORY''
           ORDER BY LAST_UPDATE_DATE DESC
           LIMIT 1';
    EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
  --入参不为空，则以入参为版本号
  ELSE 
    V_VERSION_ID := F_VERSION_ID;
  END IF;
    
  --1.删除目标表数据:
  IF F_CUSTOM_ID IS NULL THEN   -- 月度定时调度
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_WEIGHT_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_AMP_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_STATUS_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;
  ELSE    -- 用户页面保存新的自选组合
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_WEIGHT_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' AND CUSTOM_ID = '||V_CUSTOM_ID;
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_AMP_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' AND CUSTOM_ID = '||V_CUSTOM_ID;
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_STATUS_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' AND CUSTOM_ID = '||V_CUSTOM_ID;
  END IF;
  
  --1.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除单年权重表、年度涨跌幅表、状态码表的版本号为：'||V_VERSION_ID||'中自选组合部分的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 判断CUSTOM_ID是否有入参，有入参时，只计算入参数据，否则全部计算
  IF V_CUSTOM_ID IS NULL THEN
    V_SQL_CUSTOM_ID:='';
    
  ELSE 
    V_SQL_CUSTOM_ID:= ' AND T1.CUSTOM_ID = '||V_CUSTOM_ID ;
  END IF;
  
  -- 创建临时表
  DROP TABLE IF EXISTS DECRYP_CUSTOM_AMT_TMP;
  CREATE TEMPORARY TABLE DECRYP_CUSTOM_AMT_TMP(
      VERSION_ID   BIGINT,
      PERIOD_YEAR   BIGINT,
      CUSTOM_ID   BIGINT,                
      CUSTOM_CN_NAME  VARCHAR(200),   
      PARENT_CODE      VARCHAR(200),  
      PARENT_CN_NAME  VARCHAR(1000),  
      PARENT_LEVEL      VARCHAR(50),  
      GRANULARITY_TYPE  VARCHAR(2),
      GROUP_CODE      VARCHAR(200),
      GROUP_CN_NAME  VARCHAR(1000),
      VIEW_FLAG VARCHAR(2),
      RMB_AVG_AMT  NUMERIC,
      RMB_COST_AMT NUMERIC,
      APPEND_FLAG VARCHAR(2),
      CALIBER_FLAG  VARCHAR(2),
      OVERSEA_FLAG    VARCHAR(2),
      LV0_PROD_LIST_CODE    VARCHAR(50),
      LV0_PROD_LIST_CN_NAME  VARCHAR(200)
  )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY ROUNDROBIN;
  RAISE NOTICE '临时表创建成功';

  V_SQL:='
  INSERT INTO DECRYP_CUSTOM_AMT_TMP
    (VERSION_ID,
     PERIOD_YEAR,
     CUSTOM_ID,
     CUSTOM_CN_NAME,
     PARENT_CODE,
     PARENT_CN_NAME,
     PARENT_LEVEL,
     GRANULARITY_TYPE,
     GROUP_CODE,
     GROUP_CN_NAME,
     VIEW_FLAG,
     RMB_AVG_AMT,
     RMB_COST_AMT,
     APPEND_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME)
  SELECT VERSION_ID,
         PERIOD_YEAR,
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         PARENT_CODE,
         PARENT_CN_NAME,
         PARENT_LEVEL,
         GRANULARITY_TYPE,
         GROUP_CODE,
         GROUP_CN_NAME,
         VIEW_FLAG,
         RMB_AVG_AMT,
         RMB_COST_AMT,
         APPEND_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM '||V_FROM_AVG_TABLE||' T1
   WHERE VERSION_ID = '||V_VERSION_ID|| V_SQL_CUSTOM_ID;
   
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;   
 
  --写入日志
   V_STEP_NUM:=V_STEP_NUM+1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '解密完成',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
  
  -- 权重数据计算
  V_SQL := '
  INSERT INTO '||V_TO_WEIGHT_TABLE||'
    (CUSTOM_ID,
     CUSTOM_CN_NAME,
     VERSION_ID,
     PERIOD_YEAR,
     PARENT_CODE,
     PARENT_CN_NAME,
     PARENT_LEVEL,
     GROUP_CODE,
     GROUP_CN_NAME,
     WEIGHT_RATE,
     ABSOLUTE_WEIGHT,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     GRANULARITY_TYPE,
     VIEW_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME,
     GROUP_LEVEL)

  -- 计算自选组合的相对权重值和绝对权重值
    SELECT T1.CUSTOM_ID,
           T1.CUSTOM_CN_NAME,
           '||V_VERSION_ID||' AS VERSION_ID,
           T1.PERIOD_YEAR,
           T1.PARENT_CODE,
           T1.PARENT_CN_NAME,
           T1.PARENT_LEVEL,
           T1.GROUP_CODE,
           T1.GROUP_CN_NAME,
           NVL(SUM(T1.RMB_COST_AMT) / SUM(SUM(T1.RMB_COST_AMT)) OVER(PARTITION BY T1.PERIOD_YEAR, T1.CUSTOM_ID, T1.PARENT_CODE, T1.PARENT_CN_NAME, T1.PARENT_LEVEL, T1.GRANULARITY_TYPE, T1.VIEW_FLAG, T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE),0) AS WEIGHT_RATE,
           NVL(SUM(T1.RMB_COST_AMT) / T2.ABSOLUTE_PARENT_AMT,0) AS ABSOLUTE_WEIGHT,
           ''-1'' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           ''-1'' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           T1.GRANULARITY_TYPE,
           T1.VIEW_FLAG,
           T1.CALIBER_FLAG,
           T1.OVERSEA_FLAG,
           T1.LV0_PROD_LIST_CODE,
           T1.LV0_PROD_LIST_CN_NAME,
           ''ITEM'' AS GROUP_LEVEL
        FROM DECRYP_CUSTOM_AMT_TMP T1
        LEFT JOIN FIN_DM_OPT_FOI.DM_FOC_ABSOLUTE_AMT_T T2
        ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
        AND T1.VIEW_FLAG = T2.VIEW_FLAG
        AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
        AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
        AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
        AND T1.GRANULARITY_TYPE = T2.GRANULARITY_TYPE
        AND T1.VERSION_ID = T2.VERSION_ID
        WHERE T2.COST_TYPE = ''M''
        AND T2.INDUSTRY_TYPE = '''||F_INDUSTRY_FLAG||'''
        GROUP BY T1.PERIOD_YEAR,
                 T1.GRANULARITY_TYPE,
                 T1.VIEW_FLAG,
                 T1.CALIBER_FLAG,
                 T1.OVERSEA_FLAG,
                 T1.LV0_PROD_LIST_CODE,
                 T1.LV0_PROD_LIST_CN_NAME,
                 T1.PARENT_CODE,
                 T1.PARENT_CN_NAME,
                 T1.PARENT_LEVEL,
                 T1.GROUP_CODE,
                 T1.GROUP_CN_NAME,
                 T1.CUSTOM_ID,
                 T1.CUSTOM_CN_NAME,
                 T2.ABSOLUTE_PARENT_AMT';
     DBMS_OUTPUT.PUT_LINE(V_SQL);      
     EXECUTE IMMEDIATE V_SQL; 
  
     EXECUTE IMMEDIATE 'ANALYSE '||V_TO_WEIGHT_TABLE;
  
  --写入日志
   V_STEP_NUM:=V_STEP_NUM+1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '权重插数完成',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
   
  -- 创建年度涨跌幅临时表
  DROP TABLE IF EXISTS DM_MADE_CUSTOM_MID_AMP_TMP;
  CREATE TEMPORARY TABLE DM_MADE_CUSTOM_MID_AMP_TMP(
      CUSTOM_ID    BIGINT,
      CUSTOM_CN_NAME   VARCHAR(200),
      PERIOD_YEAR    VARCHAR(50),
      PARENT_CODE    VARCHAR(200),
      PARENT_CN_NAME    VARCHAR(1000),
      PARENT_LEVEL    VARCHAR(50),
      GROUP_CODE    VARCHAR(200), 
      GROUP_CN_NAME VARCHAR(1000),
      GROUP_LEVEL VARCHAR(50), 
      ANNUAL_AMP    NUMERIC,
      GRANULARITY_TYPE    VARCHAR(2),
      VIEW_FLAG    VARCHAR(2),
      CALIBER_FLAG    VARCHAR(2),
      OVERSEA_FLAG    VARCHAR(2),
      LV0_PROD_LIST_CODE    VARCHAR(50),
      LV0_PROD_LIST_CN_NAME    VARCHAR(200)
  )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY ROUNDROBIN; 

  -- 自选组合年度涨跌幅数据计算逻辑
  INSERT INTO DM_MADE_CUSTOM_MID_AMP_TMP
    (CUSTOM_ID,
     CUSTOM_CN_NAME,
     PERIOD_YEAR,
     PARENT_CODE,
     PARENT_CN_NAME,
     PARENT_LEVEL,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     ANNUAL_AMP,
     GRANULARITY_TYPE,
     VIEW_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME)

WITH BY_YEAR_AVG_TMP AS(
        SELECT CUSTOM_ID,
               CUSTOM_CN_NAME,
               PARENT_CODE,
               PARENT_CN_NAME,
               PARENT_LEVEL,
               GRANULARITY_TYPE,
               GROUP_CODE,
               GROUP_CN_NAME,
               VIEW_FLAG,
               SUM(CASE WHEN PERIOD_YEAR = V_YEAR-3 THEN RMB_AVG_AMT ELSE 0 END) AS LAST_THREE_YEAR_AVG,
               SUM(CASE WHEN PERIOD_YEAR = V_YEAR-2 THEN RMB_AVG_AMT ELSE 0 END) AS LAST_TWO_YEAR_AVG,
               SUM(CASE WHEN PERIOD_YEAR = V_YEAR-1 THEN RMB_AVG_AMT ELSE 0 END) AS LAST_YEAR_AVG,
               SUM(CASE WHEN PERIOD_YEAR = V_YEAR THEN RMB_AVG_AMT ELSE 0 END) AS YEAR_AVG,
               CALIBER_FLAG,
               OVERSEA_FLAG,
               LV0_PROD_LIST_CODE,
               LV0_PROD_LIST_CN_NAME
           FROM DECRYP_CUSTOM_AMT_TMP 
           GROUP BY CUSTOM_ID,
                    CUSTOM_CN_NAME,
                    PARENT_CODE,
                    PARENT_CN_NAME,
                    PARENT_LEVEL,
                    GRANULARITY_TYPE,
                    GROUP_CODE,
                    GROUP_CN_NAME,
                    VIEW_FLAG,
                    CALIBER_FLAG,
                    OVERSEA_FLAG,
                    LV0_PROD_LIST_CODE,
                    LV0_PROD_LIST_CN_NAME
        )

      SELECT CUSTOM_ID,
             CUSTOM_CN_NAME,
             V_YEAR-2 AS PERIOD_YEAR, 
             PARENT_CODE,
             PARENT_CN_NAME,
             PARENT_LEVEL,
             GROUP_CODE,
             GROUP_CN_NAME,
             'ITEM' AS GROUP_LEVEL,
             ((LAST_TWO_YEAR_AVG/NULLIF(LAST_THREE_YEAR_AVG,0))-1) AS ANNUAL_AMP,
             GRANULARITY_TYPE,
             VIEW_FLAG,
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME
          FROM BY_YEAR_AVG_TMP
      UNION ALL
      SELECT CUSTOM_ID,
             CUSTOM_CN_NAME,
             V_YEAR-1 AS PERIOD_YEAR, 
             PARENT_CODE,
             PARENT_CN_NAME,
             PARENT_LEVEL,
             GROUP_CODE,
             GROUP_CN_NAME,
             'ITEM' AS GROUP_LEVEL,
             ((LAST_YEAR_AVG/NULLIF(LAST_TWO_YEAR_AVG,0))-1) AS ANNUAL_AMP,
             GRANULARITY_TYPE,
             VIEW_FLAG,
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME
         FROM BY_YEAR_AVG_TMP   
      UNION ALL
      SELECT CUSTOM_ID,
             CUSTOM_CN_NAME,
             V_YEAR AS PERIOD_YEAR, 
             PARENT_CODE,
             PARENT_CN_NAME,
             PARENT_LEVEL,
             GROUP_CODE,
             GROUP_CN_NAME,
             'ITEM' AS GROUP_LEVEL,
             ((YEAR_AVG/NULLIF(LAST_YEAR_AVG,0))-1) AS ANNUAL_AMP,
             GRANULARITY_TYPE,
             VIEW_FLAG,
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME
         FROM BY_YEAR_AVG_TMP;
    
  --写入日志
   V_STEP_NUM:=V_STEP_NUM+1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => 'ITEM涨跌幅插入临时表完成',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
    
V_SQL := '
  INSERT INTO DM_MADE_CUSTOM_MID_AMP_TMP
    (CUSTOM_ID,
     CUSTOM_CN_NAME,
     PERIOD_YEAR,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     ANNUAL_AMP,
     GRANULARITY_TYPE,
     VIEW_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME)
    SELECT T1.CUSTOM_ID,
           T1.CUSTOM_CN_NAME,
           T1.PERIOD_YEAR,
           T1.PARENT_CODE AS GROUP_CODE,
           T1.PARENT_CN_NAME AS GROUP_CN_NAME,
           T1.PARENT_LEVEL AS GROUP_LEVEL,
           NVL(SUM(T1.ANNUAL_AMP * T2.WEIGHT_RATE),0) AS ANNUAL_AMP,
           T1.GRANULARITY_TYPE,
           T1.VIEW_FLAG,
           T1.CALIBER_FLAG,
           T1.OVERSEA_FLAG,
           T1.LV0_PROD_LIST_CODE,
           T1.LV0_PROD_LIST_CN_NAME
      FROM DM_MADE_CUSTOM_MID_AMP_TMP T1
      LEFT JOIN '||V_TO_WEIGHT_TABLE||' T2
        ON T1.CUSTOM_ID = T2.CUSTOM_ID
       AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T1.PARENT_CODE = T2.PARENT_CODE
       AND T1.PARENT_LEVEL = T2.PARENT_LEVEL
       AND T1.GRANULARITY_TYPE = T2.GRANULARITY_TYPE
       AND T1.VIEW_FLAG = T2.VIEW_FLAG
       AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
       AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
       WHERE T2.VERSION_ID = '||V_VERSION_ID||'
     GROUP BY T1.CUSTOM_ID,
              T1.CUSTOM_CN_NAME ,
              T1.PERIOD_YEAR,
              T1.PARENT_CODE,
              T1.PARENT_CN_NAME,
              T1.PARENT_LEVEL,
              T1.GRANULARITY_TYPE,
              T1.VIEW_FLAG,
              T1.CALIBER_FLAG,
              T1.OVERSEA_FLAG,
              T1.LV0_PROD_LIST_CODE,
              T1.LV0_PROD_LIST_CN_NAME';  
     DBMS_OUTPUT.PUT_LINE(V_SQL);      
     EXECUTE IMMEDIATE V_SQL; 
            
  --写入日志
   V_STEP_NUM:=V_STEP_NUM+1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '自选组合内其他层级涨跌幅插表成功',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
            
V_SQL := '
  INSERT INTO '||V_TO_AMP_TABLE||'
    (CUSTOM_ID,
     CUSTOM_CN_NAME,
     VERSION_ID,
     PERIOD_YEAR,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     ANNUAL_AMP,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     GRANULARITY_TYPE,
     VIEW_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME,
     PARENT_CODE,
     PARENT_CN_NAME,
     PARENT_LEVEL)
    SELECT CUSTOM_ID,
           CUSTOM_CN_NAME,
           '||V_VERSION_ID||' AS VERSION_ID,
           PERIOD_YEAR,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           ANNUAL_AMP,
           ''-1'' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           ''-1'' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           GRANULARITY_TYPE,
           VIEW_FLAG,
           CALIBER_FLAG,
           OVERSEA_FLAG,
           LV0_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME,
           PARENT_CODE,
           PARENT_CN_NAME,
           PARENT_LEVEL
      FROM DM_MADE_CUSTOM_MID_AMP_TMP';
     DBMS_OUTPUT.PUT_LINE(V_SQL);      
     EXECUTE IMMEDIATE V_SQL; 
      
  --写入日志
   V_STEP_NUM:=V_STEP_NUM+1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '自选组合年度涨跌幅结果表插数成功',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
    
    EXECUTE IMMEDIATE 'ANALYSE '||V_TO_AMP_TABLE;
  
  -- 创建状态码临时表1
  DROP TABLE IF EXISTS DM_MADE_CUSTOM_MID_STATUS_TMP;
  CREATE TEMPORARY TABLE DM_MADE_CUSTOM_MID_STATUS_TMP(
      CUSTOM_ID    BIGINT,
      CUSTOM_CN_NAME   VARCHAR(200),
      PERIOD_YEAR    VARCHAR(50),
      PARENT_CODE    VARCHAR(200),
      PARENT_CN_NAME    VARCHAR(1000),
      PARENT_LEVEL    VARCHAR(50),
      GROUP_CODE    VARCHAR(200), 
      GROUP_CN_NAME VARCHAR(1000),
      GROUP_LEVEL VARCHAR(50), 
      STATUS_CODE    BIGINT,
      APPEND_YEAR    BIGINT,
      GRANULARITY_TYPE    VARCHAR(2),
      VIEW_FLAG    VARCHAR(2),
      CALIBER_FLAG    VARCHAR(2),
      OVERSEA_FLAG    VARCHAR(2),
      LV0_PROD_LIST_CODE    VARCHAR(50),
      LV0_PROD_LIST_CN_NAME    VARCHAR(200)
  )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY ROUNDROBIN;   
  
  -- 创建状态码临时表2
  DROP TABLE IF EXISTS MADE_BY_YEAR_AMP_TMP;
  CREATE TEMPORARY TABLE MADE_BY_YEAR_AMP_TMP(
      CUSTOM_ID    BIGINT,
      CUSTOM_CN_NAME   VARCHAR(200),
      PARENT_CODE    VARCHAR(200),
      PARENT_CN_NAME    VARCHAR(1000),
      PARENT_LEVEL    VARCHAR(50),
      GROUP_CODE    VARCHAR(200), 
      GROUP_CN_NAME VARCHAR(1000),
      GROUP_LEVEL VARCHAR(50), 
      LAST_THREE_YEAR_FLAG    BIGINT,
      LAST_THREE_APPEND_YEAR    BIGINT,
      LAST_TWO_YEAR_FLAG    BIGINT,
      LAST_TWO_APPEND_YEAR    BIGINT,
      LAST_YEAR_FLAG    BIGINT,
      LAST_APPEND_YEAR    BIGINT,
      CURRENT_YEAR_FLAG    BIGINT,
      CURRENT_APPEND_YEAR    BIGINT,
      GRANULARITY_TYPE    VARCHAR(2),
      VIEW_FLAG    VARCHAR(2),
      CALIBER_FLAG    VARCHAR(2),
      OVERSEA_FLAG    VARCHAR(2),
      LV0_PROD_LIST_CODE    VARCHAR(50),
      LV0_PROD_LIST_CN_NAME    VARCHAR(200)
  )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY HASH (GROUP_CODE); 
  
  -- ITEM层级状态码逻辑
 V_SQL := '
   INSERT INTO MADE_BY_YEAR_AMP_TMP(
          CUSTOM_ID,
          CUSTOM_CN_NAME,
          PARENT_CODE,
          PARENT_CN_NAME,
          PARENT_LEVEL,
          GROUP_CODE,
          GROUP_CN_NAME,
          GROUP_LEVEL,
          LAST_THREE_YEAR_FLAG,
          LAST_THREE_APPEND_YEAR,
          LAST_TWO_YEAR_FLAG,
          LAST_TWO_APPEND_YEAR,
          LAST_YEAR_FLAG,
          LAST_APPEND_YEAR,
          CURRENT_YEAR_FLAG,
          CURRENT_APPEND_YEAR,
          GRANULARITY_TYPE,
          VIEW_FLAG,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME)
   SELECT CUSTOM_ID,
          CUSTOM_CN_NAME,
          PARENT_CODE,
          PARENT_CN_NAME,
          PARENT_LEVEL,
          GROUP_CODE,
          GROUP_CN_NAME,
          ''ITEM'' AS GROUP_LEVEL,
          SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-3 THEN DECODE(APPEND_FLAG,''Y'',1,0) ELSE 0 END) AS LAST_THREE_YEAR_FLAG,  -- 为当年-3年时，赋予该年数据为补齐时赋1，否则赋0
          SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-3 THEN APPEND_YEAR ELSE 0 END) AS LAST_THREE_APPEND_YEAR,                                  -- 为当年-3年时，取到对应向前补齐数据的补齐年份，若为原始数据或是向后补齐的，则为NULL
          SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-2 THEN DECODE(APPEND_FLAG,''Y'',1,0) ELSE 0 END) AS LAST_TWO_YEAR_FLAG,       -- 为当年-2年时，赋予该年数据为补齐时赋1，否则赋0
          SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-2 THEN APPEND_YEAR ELSE 0 END) AS LAST_TWO_APPEND_YEAR,                                      -- 为当年-2年时，取到对应向前补齐数据的补齐年份，若为原始数据或是向后补齐的，则为NULL
          SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-1 THEN DECODE(APPEND_FLAG,''Y'',1,0) ELSE 0 END) AS LAST_YEAR_FLAG,  
          SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-1 THEN APPEND_YEAR ELSE 0 END) AS LAST_APPEND_YEAR, 
          SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||' THEN DECODE(APPEND_FLAG,''Y'',1,0) ELSE 0 END) AS CURRENT_YEAR_FLAG,  
          SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||' THEN APPEND_YEAR ELSE 0 END) AS CURRENT_APPEND_YEAR,
          GRANULARITY_TYPE,
          VIEW_FLAG,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
       FROM '||V_FROM_AVG_TABLE||' T1
       WHERE VERSION_ID = '||V_VERSION_ID|| V_SQL_CUSTOM_ID||'
       GROUP BY CUSTOM_ID,
                CUSTOM_CN_NAME,
                PARENT_CODE,
                PARENT_CN_NAME,
                PARENT_LEVEL,
                GROUP_CODE,
                GROUP_CN_NAME,
                GRANULARITY_TYPE,
                VIEW_FLAG,
                CALIBER_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME';
    DBMS_OUTPUT.PUT_LINE(V_SQL); 
    EXECUTE IMMEDIATE V_SQL;          
  
  -- 对ITEM层级的年份进行循环 
     FOR YEAR_FLAG IN V_YEAR-2 .. V_YEAR LOOP                      
     
     IF YEAR_FLAG = V_YEAR-2 THEN
         V_LAST_YEAR_FLAG := 'LAST_THREE_YEAR_FLAG';
         V_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
         V_YEAR_APPEND := 'LAST_THREE_APPEND_YEAR';
     
     ELSIF YEAR_FLAG = V_YEAR-1 THEN
         V_LAST_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
         V_YEAR_FLAG := 'LAST_YEAR_FLAG';
         V_YEAR_APPEND := 'LAST_TWO_APPEND_YEAR';
         
     ELSIF YEAR_FLAG = V_YEAR THEN
         V_LAST_YEAR_FLAG := 'LAST_YEAR_FLAG';
         V_YEAR_FLAG := 'CURRENT_YEAR_FLAG';
         V_YEAR_APPEND := 'LAST_APPEND_YEAR';    
     ELSE NULL;
     END IF;
  
  -- ITEM层级年度涨跌幅状态码数据计算逻辑
  V_SQL := '
   INSERT INTO DM_MADE_CUSTOM_MID_STATUS_TMP
    (CUSTOM_ID,
     CUSTOM_CN_NAME,
     PERIOD_YEAR,
     PARENT_CODE,
     PARENT_CN_NAME,
     PARENT_LEVEL,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     STATUS_CODE,
     APPEND_YEAR,
     GRANULARITY_TYPE,
     VIEW_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME) 
   SELECT CUSTOM_ID,
          CUSTOM_CN_NAME,
          '||YEAR_FLAG||' AS PERIOD_YEAR,   -- 循环的年份即是当次计算的年份
          PARENT_CODE,
          PARENT_CN_NAME,
          PARENT_LEVEL,
          GROUP_CODE,
          GROUP_CN_NAME,
          GROUP_LEVEL,
          CASE WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' = 1 THEN 1
               WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 1 THEN 2
               WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' = 0 THEN 3
               WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 0 AND '||V_YEAR_APPEND||' = 0 THEN 4
               WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 0 AND '||V_YEAR_APPEND||' <> 0 THEN 5
          END AS STATUS_CODE,
          '||V_YEAR_APPEND||' AS APPEND_YEAR,
          GRANULARITY_TYPE,
          VIEW_FLAG,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
       FROM MADE_BY_YEAR_AMP_TMP';
      DBMS_OUTPUT.PUT_LINE(V_SQL);
      EXECUTE IMMEDIATE V_SQL;   
       
  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => 'ITEM层级全维度缺失状态码插入临时表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
       
  END LOOP;   -- 结束循环
  
  -- 其余层级状态码计算逻辑
  V_SQL := '
   INSERT INTO DM_MADE_CUSTOM_MID_STATUS_TMP
    (CUSTOM_ID,
     CUSTOM_CN_NAME,
     PERIOD_YEAR,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     STATUS_CODE,
     GRANULARITY_TYPE,
     VIEW_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME)   
     
  WITH ITEM_STATUS_TMP AS(
   SELECT T1.CUSTOM_ID,
          T1.CUSTOM_CN_NAME,
          T1.PERIOD_YEAR,
          T1.PARENT_CODE AS GROUP_CODE,
          SUM(CASE WHEN T1.STATUS_CODE IN (3,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋3
          SUM(DECODE(T1.STATUS_CODE,1,0,1)) AS STATUS_1, -- 值=0，说明子级都为1，赋1
          SUM(DECODE(T1.STATUS_CODE,4,0,1)) AS STATUS_4,
          T1.GRANULARITY_TYPE,
          T1.VIEW_FLAG,
          T1.CALIBER_FLAG,
          T1.OVERSEA_FLAG,
          T1.LV0_PROD_LIST_CODE,
          T1.LV0_PROD_LIST_CN_NAME
       FROM DM_MADE_CUSTOM_MID_STATUS_TMP T1
       WHERE GROUP_LEVEL = ''ITEM''
       GROUP BY T1.CUSTOM_ID,
                T1.CUSTOM_CN_NAME,
                T1.PERIOD_YEAR,
                T1.PARENT_CODE,
                T1.GRANULARITY_TYPE,
                T1.VIEW_FLAG,
                T1.CALIBER_FLAG,
                T1.OVERSEA_FLAG,
                T1.LV0_PROD_LIST_CODE,
                T1.LV0_PROD_LIST_CN_NAME)
   SELECT T1.CUSTOM_ID,
          T1.CUSTOM_CN_NAME,
          T1.PERIOD_YEAR,
          T1.GROUP_CODE,
          T1.GROUP_CN_NAME,
          T1.GROUP_LEVEL,
          CASE WHEN T2.STATUS_NORMAL > 0 THEN 3
               WHEN T2.STATUS_1 = 0 THEN 1
               WHEN T2.STATUS_4 = 0 THEN 2
          ELSE 4 END AS STATUS_CODE,
          T1.GRANULARITY_TYPE,
          T1.VIEW_FLAG,
          T1.CALIBER_FLAG,
          T1.OVERSEA_FLAG,
          T1.LV0_PROD_LIST_CODE,
          T1.LV0_PROD_LIST_CN_NAME
       FROM '||V_TO_AMP_TABLE||' T1  
       LEFT JOIN ITEM_STATUS_TMP T2
       ON T1.CUSTOM_ID = T2.CUSTOM_ID
       AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
       AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T1.GRANULARITY_TYPE = T2.GRANULARITY_TYPE
       AND T1.VIEW_FLAG = T2.VIEW_FLAG
       AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
       WHERE T1.GROUP_LEVEL <> ''ITEM''
       AND T1.VERSION_ID = '||V_VERSION_ID|| V_SQL_CUSTOM_ID;
       
  EXECUTE IMMEDIATE V_SQL;   
          
  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '其余层级状态码插入临时表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');  
    
  -- 状态码数据插入结果表
V_SQL := '
  INSERT INTO '||V_TO_STATUS_TABLE||'
    (VERSION_ID,
     CUSTOM_ID,
     CUSTOM_CN_NAME,
     PERIOD_YEAR,
     PARENT_CODE,
     PARENT_CN_NAME,
     PARENT_LEVEL,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     STATUS_CODE,
     APPEND_YEAR,
     GRANULARITY_TYPE,
     VIEW_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG)
    
  SELECT '||V_VERSION_ID||' AS VERSION_ID,
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         PERIOD_YEAR,
         PARENT_CODE,
         PARENT_CN_NAME,
         PARENT_LEVEL,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         APPEND_YEAR,
         GRANULARITY_TYPE,
         VIEW_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
      FROM DM_MADE_CUSTOM_MID_STATUS_TMP';
    EXECUTE IMMEDIATE V_SQL;  
      
  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '状态码数据插入结果表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
    
    EXECUTE IMMEDIATE 'ANALYSE '||V_TO_STATUS_TABLE;
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集统计信息完成!');
 
  RETURN 'SUCCESS';
 
 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

END$$
/

