-- Name: f_dm_foi_top_item_weight; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_top_item_weight(f_cate_version bigint, f_item_version bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间：2022-11-22 	
创建人  ：黄心蕊 hwx1187045
背景描述：配置页面规格品清单数据刷新
参数描述：参数一(f_cate_version)：TOP品类清单最新版本号
		  参数二(f_item_version)：规格品清单最新版本号
		  参数三(x_success_flag)  ：运行状态返回值-成功或者失败
事例    ：select FIN_DM_OPT_FOI.f_dm_foi_top_item_weight(1,2) --一个版本的数据
****************************************************************************************************************************************************************/
 declare 
  v_sp_name          varchar(50):= 'FIN_DM_OPT_FOI.F_DM_FOI_TOP_ITEM_WEIGHT';
  v_exception_flag	 varchar(50):= null;
 begin
   --0.日志开始
  perform FIN_DM_OPT_FOI.f_dm_foi_cal_log_t
  (f_sp_name => v_sp_name,
   f_step_num => 0,
   f_cal_log_desc => v_sp_name||'开始执行');
   
 x_success_flag := '1';
 
  v_exception_flag := '1';
TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOI_ITEM_AMT_TMP;
 --6.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => 'ITEM单年金额临时表清空完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => x_success_flag,
  F_ERRBUF => 'SUCCESS');
	
 v_exception_flag := '2';
INSERT INTO FIN_DM_OPT_FOI.DM_FOI_ITEM_AMT_TMP
(year  ,
 group_code ,
 group_cn_name ,
 iten_amt ,
 category_code ,
 category_name ,
 l2_ceg_cn_name ,
 l3_ceg_short_cn_name ,
 l4_ceg_short_cn_name ,
 l2_ceg_code ,
 l3_ceg_code ,
 l4_ceg_code ,
 version_id ,
 l4_ceg_cn_name ,
 l3_ceg_cn_name 
)
 select t.year,
         t.group_code,
         t.group_cn_name,
         sum(t.receive_amt_cny) as iten_amt,
         t.category_code,
         t.category_name,
         t.l2_ceg_cn_name,
         t.l3_ceg_short_cn_name,
         t.l4_ceg_short_cn_name,
         t.l2_ceg_code,
         t.l3_ceg_code,
         t.l4_ceg_code,
         t.version_id,
         t.l4_ceg_cn_name,
         t.l3_ceg_cn_name
    from FIN_DM_OPT_FOI.dm_foi_top_res_rec_amt_t t
   where upper(t.group_level) = 'ITEM'
     and t.version_id = f_item_version
     and t.append_flag = 'N'
     and t.parent_code in
               (select distinct tc.category_code
                  from FIN_DM_OPT_FOI.dm_foi_top_cate_info_t tc
                 where tc.version_id = f_cate_version)--top品类清单版本号
   group by t.year,
            t.group_code,
            t.group_cn_name,
            t.category_code,
            t.category_name,
            t.l2_ceg_cn_name,
            t.l3_ceg_short_cn_name,
            t.l4_ceg_short_cn_name,
            t.l2_ceg_code,
            t.l3_ceg_code,
            t.l4_ceg_code,
            t.version_id,
            t.l4_ceg_cn_name,
            t.l3_ceg_cn_name;
			
  --1.写入日志
 perform FIN_DM_OPT_FOI.f_dm_foi_cal_log_t
 (f_sp_name => v_sp_name,
  f_step_num => 2,
  f_cal_log_desc => '临时表数据插入完成',
  f_dml_row_count => sql%rowcount,
  f_result_status => x_success_flag,
  f_errbuf => 'success');
  
 
---------------------规格品清单处理，top品类下所有item的权重---------------
 v_exception_flag := '3';
delete from FIN_DM_OPT_FOI.dm_foi_group_lev_weight_t where version_id = f_item_version 
																								 and upper(group_level) = 'ITEM'
																								 and upper(parent_level) = 'CATEGORY'
																								 and top_flag is null ;

  --2.写入日志
 perform FIN_DM_OPT_FOI.f_dm_foi_cal_log_t
 (f_sp_name => v_sp_name,
  f_step_num => 3,
  f_cal_log_desc => '同版本规格品清单权重删除完成',
  f_dml_row_count => sql%rowcount,
  f_result_status => x_success_flag,
  f_errbuf => 'success');


 v_exception_flag := '4';
insert into FIN_DM_OPT_FOI.dm_foi_group_lev_weight_t
  (id,
   period,
   period_type,
   version_id,
   group_code,
   group_cn_name,
   group_level,
   parent_code,
   parent_level,
   weight,
   item_code,
   item_name,
   category_code,
   category_name,
   l2_ceg_cn_name,
   l3_ceg_short_cn_name,
   l4_ceg_short_cn_name,
   l2_ceg_code,
   l3_ceg_code,
   l4_ceg_code,
   top_type,
   top_flag,
   created_by,
   creation_date,
   last_updated_by,
   last_update_date,
   del_flag,
   l4_ceg_cn_name,
   l3_ceg_cn_name
   )
select FIN_DM_OPT_FOI.dm_foi_group_lev_weight_s.nextval as id,
			 tt.period, 
       tt.period_type,
       tt.version_id,
       tt.group_code,
       tt.group_cn_name,
       tt.group_level,
       tt.parent_code,
       tt.parent_level,
       tt.weight,
       '' as item_code,
       '' as item_name,
       tt.category_code,
       tt.category_name,
       tt.l2_ceg_cn_name,
       tt.l3_ceg_short_cn_name,
       tt.l4_ceg_short_cn_name,
       tt.l2_ceg_code,
       tt.l3_ceg_code,
       tt.l4_ceg_code,
       '' as top_type,
       '' as top_flag,
       -1 as created_by,
       current_timestamp as creation_date,
       -1 as last_updated_by,
       current_timestamp as last_update_date,
       'N' as del_flag,
       tt.l4_ceg_cn_name,
       tt.l3_ceg_cn_name
from (
--单年权重
select to_char(it.year) as period,
       'U' as period_type,
       f_item_version as version_id,
       it.group_code,
       it.group_cn_name,
       'ITEM' as group_level,
       it.category_code as parent_code,
       'CATEGORY' as parent_level,
       it.iten_amt /
       nullif(sum(it.iten_amt) over(partition by it.category_code,it.year), 0) as weight,
       it.category_code,
       it.category_name,
       it.l2_ceg_cn_name,
       it.l3_ceg_short_cn_name,
       it.l4_ceg_short_cn_name,
       it.l2_ceg_code,
       it.l3_ceg_code,
       it.l4_ceg_code,
       it.l4_ceg_cn_name,
       it.l3_ceg_cn_name
  from FIN_DM_OPT_FOI.DM_FOI_ITEM_AMT_TMP it
union all
--四年综合
select distinct
			 extract(year from current_date) - 3 || '-' ||
       extract(year from current_date) as period, --区间值为四年
       'S' as period_type,
       f_item_version as version_id,
       it.group_code,
       it.group_cn_name,
       'ITEM' as group_level,
       it.category_code as parent_code,
       'CATEGORY' as parent_level,
       sum(it.iten_amt)over(partition by it.group_code,it.category_code)/
       nullif(sum(it.iten_amt) over(partition by it.category_code), 0) as weight,
       it.category_code,
       it.category_name,
       it.l2_ceg_cn_name,
       it.l3_ceg_short_cn_name,
       it.l4_ceg_short_cn_name,
       it.l2_ceg_code,
       it.l3_ceg_code,
       it.l4_ceg_code,
       it.l4_ceg_cn_name,
       it.l3_ceg_cn_name
  from FIN_DM_OPT_FOI.DM_FOI_ITEM_AMT_TMP it
	) tt
	;
	
  --3.写入日志
 perform FIN_DM_OPT_FOI.f_dm_foi_cal_log_t
 (f_sp_name => v_sp_name,
  f_step_num => 4,
  f_cal_log_desc => '规格品清单权重插数完成',
  f_dml_row_count => sql%rowcount,
  f_result_status => x_success_flag,
  f_errbuf => 'success');
 
 v_exception_flag := '5';
TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOI_ITEM_AMT_TMP;
 --6.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 5,
  F_CAL_LOG_DESC => 'ITEM单年金额临时表清空完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => x_success_flag,
  F_ERRBUF => 'SUCCESS');

  --4.写入日志
 perform FIN_DM_OPT_FOI.f_dm_foi_cal_log_t
 (f_sp_name => v_sp_name,
  f_step_num => 6,
  f_cal_log_desc => '函数f_dm_foi_top_item_weight完成',
  f_dml_row_count => sql%rowcount,
  f_result_status => x_success_flag,
  f_errbuf => 'success');
	
return 'SUCCESS';


--异常处理
EXCEPTION
  WHEN OTHERS THEN
  x_success_flag := '2001';
  
perform FIN_DM_OPT_FOI.f_dm_foi_cal_log_t
  (f_sp_name => v_sp_name, 
   f_step_num => v_exception_flag,
   f_cal_log_desc => v_sp_name||'运行失败', 
   f_result_status => x_success_flag, 
   f_errbuf => sqlstate||':'||sqlerrm
   );

 end; $$
/

