-- Name: f_dm_fom_point_index; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_point_index(f_version_id bigint, f_caliber_flag character varying, f_base_period text, f_grouplevel character varying, f_sublevel character varying, f_group_code character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$


/*
创建时间：2023/12/12
创建人  ：许灿烽
修改时间：2024年3月1日16点04分
修改人：唐钦
背景描述：制造单领域 指数计算-基期切换函数 参考 f_dm_foc_total_point_index
修改：默认基期逻辑修改，修复跨年问题(参考产业唐钦代码) 许灿烽 20230104
参数描述:
参数描述:
参数一(f_version_id)：版本ID
参数二(f_base_period)：基期参数
参数三(f_grouplevel)： 
参数四(f_sublevel)：
参数五(f_group_code)：
参数六(x_result_status)：运行状态返回值, 1 为成功，0 为失败
来源表:FIN_DM_OPT_FOI.DM_FOM_MONTH_COST_IDX_T -- 成本指数表(月度分析)
目标表:FIN_DM_OPT_FOI.DM_FOM_MONTH_COST_IDX_T  --成本指数表(月度分析)
事例：fin_dm_opt_foi.f_dm_fom_point_index()  基期切换函数
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOM_POINT_INDEX'; --存储过程名称
  V_TO_TABLE VARCHAR(50) := 'FIN_DM_OPT_FOI.DM_FOM_MONTH_COST_IDX_T'; -- 目标表
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_DEFAULT_PERIOD     TEXT := YEAR(CURRENT_TIMESTAMP) - 1 || '01'; -- 默认基期
  V_VERSION_ID BIGINT := F_VERSION_ID; --版本号ID 
  V_GROUP_CODE         VARCHAR(1000) := ''||replace(f_group_code,',',''',''')||''; -- 需要切换基期的CODE入参
  V_VERSION_NAME VARCHAR2(50) ; --版本中文名称
  V_SQL        TEXT;   --SQL逻辑
BEGIN
  X_RESULT_STATUS = '1';
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  --对入参合法性判断
  IF F_CALIBER_FLAG NOT IN ('M','E') THEN
     DBMS_OUTPUT.PUT_LINE ('入参有误，请重新输入！');
     RETURN '0';
  END IF;

-- 默认基期取值
  V_SQL := '
  SELECT MAX(PERIOD_YEAR)-1||''01''
     FROM FIN_DM_OPT_FOI.DM_FOM_ANNUAL_AMP_T
     WHERE VERSION_ID = (SELECT VERSION_ID FROM DM_FOM_ITEM_EXPS_DTL_T LIMIT 1)';
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL INTO V_DEFAULT_PERIOD; 


DELETE FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_COST_IDX_T
WHERE VERSION_ID = f_version_id
AND CALIBER_FLAG = f_caliber_flag
AND BASE_PERIOD_ID = f_base_period
AND GROUP_CODE IN (f_group_code)
--AND GROUP_LEVEL = f_grouplevel
--AND GROUP_LEVEL = f_sublevel 
;

  --1.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除版本号为：'||F_VERSION_ID||'且基期为：'||f_base_period||'的'||V_TO_TABLE||'表数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');




    -- 取出入参CODE对应的版本号以及默认基期的数据和其子级的指数数据
     WITH CODE_SUB_TMP AS(
     -- 取出 F_GROUP_CODE 本身的指数数据            
                    SELECT 
                     VERSION_ID    --版本ID
                     ,PERIOD_YEAR    --会计年
                     ,PERIOD_ID    --会计期
                     ,BASE_PERIOD_ID    --基期会计期
                     ,LV0_CODE    --重量级团队LV0编码
                     ,LV0_CN_NAME    --重量级团队LV0中文名称
                     ,LV1_CODE    --重量级团队LV1编码
                     ,LV1_CN_NAME    --重量级团队LV1中文名称
                     ,BUSSINESS_OBJECT_CODE    --经营对象编码
                     ,BUSSINESS_OBJECT_CN_NAME    --经营对象名称
                     ,SHIPPING_OBJECT_CODE     --发货对象编码
                     ,SHIPPING_OBJECT_CN_NAME    --发货对象名称
                     ,MANUFACTURE_OBJECT_CODE     --制造对象编码
                     ,MANUFACTURE_OBJECT_CN_NAME    --制造对象名称
                     ,GROUP_CODE    --分层级CODE
                     ,GROUP_CN_NAME    --分层级中文名称
                     ,GROUP_LEVEL    --GROUP层级(ICT/LV1/LV2/BUSSINESS_OBJECT（经营对象）/SHIPPING_OBJECT（发货对象）/MANUFACTURE_OBJECT（制造对象）/ITEM)
                     ,COST_INDEX    --成本指数
                     ,PARENT_CODE    --父级CODE
                     ,PARENT_CN_NAME  --父级中文名称
                     ,CALIBER_FLAG  --业务口径（E：EMS/M：自制）
                     ,APPEND_FLAG
                       FROM  FIN_DM_OPT_FOI.DM_FOM_MONTH_COST_IDX_T
                       WHERE VERSION_ID = f_version_id
                       AND BASE_PERIOD_ID = V_DEFAULT_PERIOD  --取默认的基期数据
                       AND CALIBER_FLAG = f_caliber_flag
                       AND GROUP_LEVEL = f_grouplevel
                       AND GROUP_CODE IN (f_group_code)
                       AND UPPER(DEL_FLAG) = 'N'
                    UNION ALL
     -- 取出 F_GROUP_CODE 子级的指数数据                
                    SELECT 
                     VERSION_ID    --版本ID
                     ,PERIOD_YEAR    --会计年
                     ,PERIOD_ID    --会计期
                     ,BASE_PERIOD_ID    --基期会计期
                     ,LV0_CODE    --重量级团队LV0编码
                     ,LV0_CN_NAME    --重量级团队LV0中文名称
                     ,LV1_CODE    --重量级团队LV1编码
                     ,LV1_CN_NAME    --重量级团队LV1中文名称
                     ,BUSSINESS_OBJECT_CODE    --经营对象编码
                     ,BUSSINESS_OBJECT_CN_NAME    --经营对象名称
                     ,SHIPPING_OBJECT_CODE     --发货对象编码
                     ,SHIPPING_OBJECT_CN_NAME    --发货对象名称
                     ,MANUFACTURE_OBJECT_CODE     --制造对象编码
                     ,MANUFACTURE_OBJECT_CN_NAME    --制造对象名称
                     ,GROUP_CODE    --分层级CODE
                     ,GROUP_CN_NAME    --分层级中文名称
                     ,GROUP_LEVEL    --GROUP层级(ICT/LV1/LV2/BUSSINESS_OBJECT（经营对象）/SHIPPING_OBJECT（发货对象）/MANUFACTURE_OBJECT（制造对象）/ITEM)
                     ,COST_INDEX    --成本指数
                     ,PARENT_CODE    --父级CODE
                     ,PARENT_CN_NAME  --父级中文名称
                     ,CALIBER_FLAG  --业务口径（E：EMS/M：自制）
                     ,APPEND_FLAG
                       FROM  FIN_DM_OPT_FOI.DM_FOM_MONTH_COST_IDX_T
                       WHERE VERSION_ID = f_version_id
                       AND BASE_PERIOD_ID = V_DEFAULT_PERIOD  --取默认的基期数据
                       AND CALIBER_FLAG = F_CALIBER_FLAG
                       AND GROUP_LEVEL = f_sublevel     -- 取GROUP_LEVEL为子级层级的数据
                       AND PARENT_CODE IN (f_group_code)     -- 取父级CODE为入参GROUP_CODE的数据
                       AND UPPER(DEL_FLAG) = 'N'
                )

INSERT INTO FIN_DM_OPT_FOI.DM_FOM_MONTH_COST_IDX_T
SELECT 
 NULL AS ID
,T1.VERSION_ID    --版本ID
,T1.PERIOD_YEAR    --会计年
,T1.PERIOD_ID    --会计期
,f_base_period AS BASE_PERIOD_ID    --基期会计期
,T1.LV0_CODE    --重量级团队LV0编码
,T1.LV0_CN_NAME    --重量级团队LV0中文名称
,T1.LV1_CODE    --重量级团队LV1编码
,T1.LV1_CN_NAME    --重量级团队LV1中文名称
,T1.BUSSINESS_OBJECT_CODE    --经营对象编码
,T1.BUSSINESS_OBJECT_CN_NAME    --经营对象名称
,T1.SHIPPING_OBJECT_CODE     --发货对象编码
,T1.SHIPPING_OBJECT_CN_NAME    --发货对象名称
,T1.MANUFACTURE_OBJECT_CODE     --制造对象编码
,T1.MANUFACTURE_OBJECT_CN_NAME    --制造对象名称
,T1.GROUP_CODE    --分层级CODE
,T1.GROUP_CN_NAME    --分层级中文名称
,T1.GROUP_LEVEL    --GROUP层级(ICT/LV1/LV2/BUSSINESS_OBJECT（经营对象）/SHIPPING_OBJECT（发货对象）/MANUFACTURE_OBJECT（制造对象）/ITEM)
,(T1.COST_INDEX / NULLIF(T2.COST_INDEX,0))*100 AS COST_INDEX    --成本指数
,T1.PARENT_CODE    --父级CODE
,T1.PARENT_CN_NAME    --父级中文名称
, -1 AS CREATED_BY    --创建人
,CURRENT_TIMESTAMP AS CREATION_DATE    --创建日期
, -1 AS LAST_UPDATED_BY    --最后更新人
,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE    --最后更新日期
,'N' AS DEL_FLAG    --删除标识(未删除：N，已删除：Y)
,T1.CALIBER_FLAG    --业务口径（E：EMS/M：自制）
,T1.APPEND_FLAG
                     FROM CODE_SUB_TMP T1
                     LEFT JOIN ( -- 取出切换基期的月份的数据
                                SELECT  LV0_CODE    --重量级团队LV0编码
                                       ,LV1_CODE    --重量级团队LV1编码
                                       ,BUSSINESS_OBJECT_CODE    --经营对象编码
                                       ,SHIPPING_OBJECT_CODE     --发货对象编码
                                       ,MANUFACTURE_OBJECT_CODE     --制造对象编码
                                       ,GROUP_CODE    --分层级CODE
                                       ,GROUP_LEVEL    --GROUP层级(ICT/LV1/LV2/BUSSINESS_OBJECT（经营对象）/SHIPPING_OBJECT（发货对象）/MANUFACTURE_OBJECT（制造对象）/ITEM)
                                       ,COST_INDEX
                                       ,PARENT_CODE    --父级CODE
                                    FROM CODE_SUB_TMP
                                    WHERE PERIOD_ID = f_base_period) T2        
                     ON NVL(T1.LV0_CODE,'SNULL') = NVL(T2.LV0_CODE,'SNULL')
                     AND NVL(T1.LV1_CODE,'SNULL') = NVL(T2.LV1_CODE,'SNULL')
                     AND NVL(T1.BUSSINESS_OBJECT_CODE,'SNULL0') = NVL(T2.BUSSINESS_OBJECT_CODE,'SNULL0')
                     AND NVL(T1.SHIPPING_OBJECT_CODE,'SNULL1') = NVL(T2.SHIPPING_OBJECT_CODE,'SNULL1')
                     AND NVL(T1.MANUFACTURE_OBJECT_CODE,'SNULL2') = NVL(T2.MANUFACTURE_OBJECT_CODE,'SNULL2')
                     AND NVL(T1.GROUP_CODE,'SNULL3') = NVL(T2.GROUP_CODE,'SNULL3')
                     AND NVL(T1.GROUP_LEVEL,'SNULL4') = NVL(T2.GROUP_LEVEL,'SNULL4')
                     AND NVL(T1.PARENT_CODE,'SNULL5') = NVL(T2.PARENT_CODE,'SNULL5')
       --排除原先指数表里已经存在相同版本的数据情况，而不存在的就插入
              WHERE NOT EXISTS (SELECT 1 FROM  FIN_DM_OPT_FOI.DM_FOM_MONTH_COST_IDX_T T3 
                                   WHERE T1.VERSION_ID = T3.VERSION_ID 
                                     AND T1.PERIOD_ID = T3.PERIOD_ID 
                                     AND T1.CALIBER_FLAG = T3.CALIBER_FLAG
                                     AND NVL(T1.LV0_CODE,'SNULL') = NVL(T3.LV0_CODE,'SNULL')
                                     AND NVL(T1.LV1_CODE,'SNULL') = NVL(T3.LV1_CODE,'SNULL')
                                     AND NVL(T1.BUSSINESS_OBJECT_CODE,'SNULL0') = NVL(T3.BUSSINESS_OBJECT_CODE,'SNULL0')
                                     AND NVL(T1.SHIPPING_OBJECT_CODE,'SNULL1') = NVL(T3.SHIPPING_OBJECT_CODE,'SNULL1')
                                     AND NVL(T1.MANUFACTURE_OBJECT_CODE,'SNULL2') = NVL(T3.MANUFACTURE_OBJECT_CODE,'SNULL2')
                                     AND NVL(T1.GROUP_CODE,'SNULL3') = NVL(T3.GROUP_CODE,'SNULL3')
                                     AND NVL(T1.GROUP_LEVEL,'SNULL4') = NVL(T3.GROUP_LEVEL,'SNULL4')
                                     AND NVL(T1.APPEND_FLAG,'SNULL5') = NVL(T3.APPEND_FLAG,'SNULL5')
                                     AND T3.BASE_PERIOD_ID = f_base_period )
;

     --2.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入基期切换为：'''||f_base_period||'''，版本号为：'''||F_VERSION_ID||'''，入参CODE为：'''||V_GROUP_CODE||'''的指数到'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');   



  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END
$$
/

