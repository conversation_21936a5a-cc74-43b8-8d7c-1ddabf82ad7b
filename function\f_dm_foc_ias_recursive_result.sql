-- Name: f_dm_foc_ias_recursive_result; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_ias_recursive_result(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
背景描述：	1.IAS制造对象补全
*/



DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_IAS_RECURSIVE_RESULT'; --存储过程名称
  V_VERSION_ID BIGINT ; --新版本号ID, 取自 FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S
  V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYY-MM'); --新的版本中文名称
  V_DIM_VERSION BIGINT ; --最新的品类-专家团映射维版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 查看是否更新完成
  V_SQL TEXT;



BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
--清空发货目标表
TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_SHIP_RECURSIVE_RESULT;

--发货目标表插数
INSERT INTO FIN_DM_OPT_FOI.DM_FOC_IAS_SHIP_RECURSIVE_RESULT(
	PERIOD_ID,
   FINAL_ITEM_CODE ,
  SPART_CODE ,
  ORIGINAL_ITEM_CODE ,
  STEP ,
  LV0_PROD_RND_TEAM_CODE,  
  LV1_PROD_RND_TEAM_CODE, 
  LV2_PROD_RND_TEAM_CODE,
  LV3_PROD_RND_TEAM_CODE,  
  LV4_PROD_RND_TEAM_CODE,
  LV0_PROD_LIST_CODE,
  PROD_CODE,
  CREATED_BY ,
  CREATION_DATE ,
  LAST_UPDATED_BY ,
  LAST_UPDATE_DATE 
  )

  -- 递归更新 D.ITEM_CODE，直到匹配到 M.ITEM_CODE
WITH Recursive CTE AS (
    -- 第一个层级：选择初始状态的 D.ITEM_CODE
    SELECT 
        D.period_id,
        D.ITEM_CODE :: TEXT AS Original_ITEM_CODE,
        D.ITEM_CODE :: TEXT AS Current_ITEM_CODE,
        D.spart_code ::TEXT,
		D.LV0_PROD_RND_TEAM_CODE,  
        D.LV1_PROD_RND_TEAM_CODE, 
        D.LV2_PROD_RND_TEAM_CODE,
        D.LV3_PROD_RND_TEAM_CODE,  
		D.LV4_PROD_RND_TEAM_CODE,
		D.LV0_PROD_LIST_CODE,
		D.PROD_CODE,
        0 AS Step
    FROM (select distinct period_id, item_code :: TEXT,PARENTPARTNUMBER AS SPART_CODE ,
						CP.LV0_PROD_RND_TEAM_CODE,  
						CP.LV1_PROD_RND_TEAM_CODE, 
						CP.LV2_PROD_RND_TEAM_CODE,
						CP.LV3_PROD_RND_TEAM_CODE,  
						CP.LV4_PROD_RND_TEAM_CODE,
						CP.LV0_PROD_LIST_CODE, 
						CP.PROD_CODE
						from DWL_PROD_BOM_ITEM_SHIP_DIM_I D
INNER JOIN DMDIM.DM_DIM_PRODUCT_D CP 
        ON D.PROD_KEY = CP.PROD_KEY
       AND CP.LV0_PROD_RND_TEAM_CODE = '104210'
       AND CP.LV1_PROD_RND_TEAM_CODE = '144349'
       AND D.SHIP_QUANTITY >= 0 -- 数量非负数
       AND D.RECOGNISE_TYPE_ID = 4 --取供应中心发区域（4）
       ) D--全量的item和spart
    
    UNION ALL

    -- 递归层级：将 spart_code 赋值给 ITEM_CODE
    SELECT 
        CTE.period_id,
        CTE.Original_ITEM_CODE,          -- 保留原始 ITEM_CODE
        CASE 
            WHEN M.ITEM_CODE  IS NULL THEN D.SPART_CODE  -- 如果没匹配到，更新为 spart_code
            ELSE D.ITEM_CODE                             -- 如果已匹配，保留原值
        END AS Current_ITEM_CODE,
        D.spart_code :: TEXT ,
		CTE.LV0_PROD_RND_TEAM_CODE,  
        CTE.LV1_PROD_RND_TEAM_CODE, 
        CTE.LV2_PROD_RND_TEAM_CODE,
        CTE.LV3_PROD_RND_TEAM_CODE,  
		CTE.LV4_PROD_RND_TEAM_CODE,
		CTE.LV0_PROD_LIST_CODE,
		CTE.PROD_CODE,
        CTE.Step + 1 AS Step
    FROM  CTE CTE
    INNER JOIN (SELECT DISTINCT period_id,ITEM_CODE :: TEXT,PARENTPARTNUMBER AS SPART_CODE  ,
							CP.LV0_PROD_RND_TEAM_CODE,  
							CP.LV1_PROD_RND_TEAM_CODE, 
							CP.LV2_PROD_RND_TEAM_CODE,
							CP.LV3_PROD_RND_TEAM_CODE,  
							CP.LV4_PROD_RND_TEAM_CODE,
							CP.LV0_PROD_LIST_CODE,
							CP.PROD_CODE
                FROM DWL_PROD_BOM_ITEM_SHIP_DIM_I D
                INNER JOIN DMDIM.DM_DIM_PRODUCT_D CP 
                ON D.PROD_KEY = CP.PROD_KEY
               AND CP.LV0_PROD_RND_TEAM_CODE = '104210'
               AND CP.LV1_PROD_RND_TEAM_CODE = '144349'
               AND D.SHIP_QUANTITY >= 0 -- 数量非负数
               AND D.RECOGNISE_TYPE_ID = 4 --取供应中心发区域（4）
       ) D 
        ON CTE.Current_ITEM_CODE = D.ITEM_CODE         -- 使用当前的 ITEM_CODE 继续递归
        AND CTE.PERIOD_ID = D.PERIOD_ID
		AND CTE.LV0_PROD_RND_TEAM_CODE = D.LV0_PROD_RND_TEAM_CODE  
        AND CTE.LV1_PROD_RND_TEAM_CODE = D.LV1_PROD_RND_TEAM_CODE 
        AND CTE.LV2_PROD_RND_TEAM_CODE = D.LV2_PROD_RND_TEAM_CODE
        AND CTE.LV3_PROD_RND_TEAM_CODE = D.LV3_PROD_RND_TEAM_CODE  
		AND CTE.LV4_PROD_RND_TEAM_CODE = D.LV4_PROD_RND_TEAM_CODE
		AND CTE.LV0_PROD_LIST_CODE = D.LV0_PROD_LIST_CODE
		AND CTE.PROD_CODE = D.PROD_CODE
		AND CTE.SPART_CODE = D.SPART_CODE
    LEFT JOIN  (SELECT DISTINCT ITEM_CODE :: TEXT,APD_SHIPMENT_OBJECT
                        FROM  FIN_DM_OPT_FOI.DM_FOC_INV_ITEM_MANUFACTURE_T
                WHERE  VERSION_ID = TO_CHAR(CURRENT_TIMESTAMP,'YYYYMM')
          	    AND   APD_MANUFACTURE_PROD_LV0 IN ('IAS') 
          	    AND   APD_MANUFACTURE_PROD_LV1 IN ('IAS') 
          	    AND   APD_SHIPMENT_OBJECT IS NOT NULL
				AND   APD_MANUFACTURE_OBJECT IS NOT NULL
          	    AND ITEM_CODE IS NOT NULL 
	    ) M
        ON CTE.Current_ITEM_CODE = M.ITEM_CODE         -- 检查是否匹配到 M.ITEM_CODE 
    WHERE M.ITEM_CODE IS NULL     
    AND   APD_SHIPMENT_OBJECT IS NULL                      -- 仅在未匹配时继续递归
    AND   D.ITEM_CODE != D.SPART_CODE
) 


-- 从递归结果中获取最终匹配的结果
SELECT DISTINCT
    FinalResult.period_id,
    FinalResult.Current_ITEM_CODE AS FINAL_ITEM_CODE,  -- 最终匹配到的 ITEM_CODE
    FinalResult.spart_code,
    FinalResult.Original_ITEM_CODE,
    FinalResult.Step,
	FinalResult.LV0_PROD_RND_TEAM_CODE,  
    FinalResult.LV1_PROD_RND_TEAM_CODE, 
    FinalResult.LV2_PROD_RND_TEAM_CODE,
    FinalResult.LV3_PROD_RND_TEAM_CODE,  
	FinalResult.LV4_PROD_RND_TEAM_CODE,
	FinalResult.LV0_PROD_LIST_CODE,
	FinalResult.PROD_CODE,
    -1,
    CURRENT_TIMESTAMP,
    -1,
    CURRENT_TIMESTAMP
FROM CTE FinalResult
LEFT JOIN (SELECT DISTINCT ITEM_CODE ,APD_SHIPMENT_OBJECT
                        FROM  FIN_DM_OPT_FOI.DM_FOC_INV_ITEM_MANUFACTURE_T
                WHERE  VERSION_ID = TO_CHAR(CURRENT_TIMESTAMP,'YYYYMM')
          	    AND   APD_MANUFACTURE_PROD_LV0 IN ('IAS') 
          	    AND   APD_MANUFACTURE_PROD_LV1 IN ('IAS') 
          	    AND   APD_SHIPMENT_OBJECT IS NOT NULL
				AND   APD_MANUFACTURE_OBJECT IS NOT NULL
          	    AND   ITEM_CODE IS NOT NULL  
	    )  M
    ON FinalResult.Current_ITEM_CODE = M.ITEM_CODE
WHERE M.ITEM_CODE IS NOT NULL
AND   APD_SHIPMENT_OBJECT IS NOT NULL  ;






ANALYZE  FIN_DM_OPT_FOI.DM_FOC_IAS_SHIP_RECURSIVE_RESULT ;



--3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集DM_FOC_IAS_SHIP_RECURSIVE_RESULT统计信息完成!');
   
   
--清空收入目标表   
TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_INCOME_RECURSIVE_RESULT;

--收入目标表插数
INSERT INTO FIN_DM_OPT_FOI.DM_FOC_IAS_INCOME_RECURSIVE_RESULT(
	PERIOD_ID,
 FINAL_ITEM_CODE ,
  SPART_CODE ,
  ORIGINAL_ITEM_CODE ,
  STEP ,
  LV0_PROD_RND_TEAM_CODE,  
  LV1_PROD_RND_TEAM_CODE, 
  LV2_PROD_RND_TEAM_CODE,
  LV3_PROD_RND_TEAM_CODE,  
  LV4_PROD_RND_TEAM_CODE,
  LV0_PROD_LIST_CODE,
  CREATED_BY ,
  CREATION_DATE ,
  LAST_UPDATED_BY ,
  LAST_UPDATE_DATE 
  )

  -- 递归更新 D.ITEM_CODE，直到匹配到 M.ITEM_CODE
WITH Recursive CTE AS (
    -- 第一个层级：选择初始状态的 D.ITEM_CODE
    SELECT 
        D.period_id,
        D.ITEM_CODE :: TEXT AS Original_ITEM_CODE,
        D.ITEM_CODE :: TEXT AS Current_ITEM_CODE,
        D.spart_code ::TEXT,
		    D.LV0_PROD_RND_TEAM_CODE,  
        D.LV1_PROD_RND_TEAM_CODE, 
        D.LV2_PROD_RND_TEAM_CODE,
        D.LV3_PROD_RND_TEAM_CODE,  
		    D.LV4_PROD_RND_TEAM_CODE,
		    D.LV0_PROD_LIST_CODE,
        0 AS Step
    FROM (select distinct period_id, item_code :: TEXT,PARENTPARTNUMBER AS SPART_CODE ,
						CP.LV0_PROD_RND_TEAM_CODE,  
						CP.LV1_PROD_RND_TEAM_CODE, 
						CP.LV2_PROD_RND_TEAM_CODE,
						CP.LV3_PROD_RND_TEAM_CODE,  
						CP.LV4_PROD_RND_TEAM_CODE,
						CP.LV0_PROD_LIST_CODE from DWL_PROD_BOM_ITEM_REV_DETAIL_I D
INNER JOIN DMDIM.DM_DIM_PRODUCT_D CP 
        ON D.PROD_KEY = CP.PROD_KEY
       AND CP.LV0_PROD_RND_TEAM_CODE = '104210'
       AND CP.LV1_PROD_RND_TEAM_CODE = '144349'
       AND D.QUANTITY >= 0 -- 数量非负数
       --AND D.RECOGNISE_TYPE_ID = 4 --取供应中心发区域（4）
       ) D--全量的item和spart
    
    UNION ALL

    -- 递归层级：将 spart_code 赋值给 ITEM_CODE
    SELECT 
        CTE.period_id,
        CTE.Original_ITEM_CODE,          -- 保留原始 ITEM_CODE
        CASE 
            WHEN M.ITEM_CODE  IS NULL THEN D.SPART_CODE  -- 如果没匹配到，更新为 spart_code
            ELSE D.ITEM_CODE                             -- 如果已匹配，保留原值
        END AS Current_ITEM_CODE,
        D.spart_code :: TEXT ,
		CTE.LV0_PROD_RND_TEAM_CODE,  
        CTE.LV1_PROD_RND_TEAM_CODE, 
        CTE.LV2_PROD_RND_TEAM_CODE,
        CTE.LV3_PROD_RND_TEAM_CODE,  
		CTE.LV4_PROD_RND_TEAM_CODE,
		CTE.LV0_PROD_LIST_CODE,
        CTE.Step + 1 AS Step
    FROM  CTE CTE
    INNER JOIN (SELECT DISTINCT period_id,ITEM_CODE :: TEXT,PARENTPARTNUMBER AS SPART_CODE  ,
							CP.LV0_PROD_RND_TEAM_CODE,  
							CP.LV1_PROD_RND_TEAM_CODE, 
							CP.LV2_PROD_RND_TEAM_CODE,
							CP.LV3_PROD_RND_TEAM_CODE,  
							CP.LV4_PROD_RND_TEAM_CODE,
							CP.LV0_PROD_LIST_CODE
                FROM DWL_PROD_BOM_ITEM_REV_DETAIL_I D
                INNER JOIN DMDIM.DM_DIM_PRODUCT_D CP 
                ON D.PROD_KEY = CP.PROD_KEY
               AND CP.LV0_PROD_RND_TEAM_CODE = '104210'
               AND CP.LV1_PROD_RND_TEAM_CODE = '144349'
               AND D.QUANTITY >= 0 -- 数量非负数
               --AND D.RECOGNISE_TYPE_ID = 4 --取供应中心发区域（4）
       ) D 
        ON CTE.Current_ITEM_CODE = D.ITEM_CODE         -- 使用当前的 ITEM_CODE 继续递归
        AND CTE.PERIOD_ID = D.PERIOD_ID
		AND CTE.LV0_PROD_RND_TEAM_CODE = D.LV0_PROD_RND_TEAM_CODE  
        AND CTE.LV1_PROD_RND_TEAM_CODE = D.LV1_PROD_RND_TEAM_CODE 
        AND CTE.LV2_PROD_RND_TEAM_CODE = D.LV2_PROD_RND_TEAM_CODE
        AND CTE.LV3_PROD_RND_TEAM_CODE = D.LV3_PROD_RND_TEAM_CODE  
		AND CTE.LV4_PROD_RND_TEAM_CODE = D.LV4_PROD_RND_TEAM_CODE
		AND CTE.LV0_PROD_LIST_CODE = D.LV0_PROD_LIST_CODE
    LEFT JOIN  (SELECT DISTINCT ITEM_CODE :: TEXT,APD_SHIPMENT_OBJECT
                        FROM  FIN_DM_OPT_FOI.DM_FOC_INV_ITEM_MANUFACTURE_T
                WHERE  VERSION_ID = TO_CHAR(CURRENT_TIMESTAMP,'YYYYMM')
          	    AND   APD_MANUFACTURE_PROD_LV0 IN ('IAS') 
          	    AND   APD_MANUFACTURE_PROD_LV1 IN ('IAS') 
          	    AND   APD_SHIPMENT_OBJECT IS NOT NULL
				        AND   APD_MANUFACTURE_OBJECT IS NOT NULL
          	    AND ITEM_CODE IS NOT NULL 
	    ) M
        ON CTE.Current_ITEM_CODE = M.ITEM_CODE         -- 检查是否匹配到 M.ITEM_CODE 
    WHERE M.ITEM_CODE IS NULL     
    AND   APD_SHIPMENT_OBJECT IS NULL                      -- 仅在未匹配时继续递归
    AND   D.ITEM_CODE != D.SPART_CODE
) 


-- 从递归结果中获取最终匹配的结果
SELECT DISTINCT
    FinalResult.period_id,
    FinalResult.Current_ITEM_CODE AS FINAL_ITEM_CODE,  -- 最终匹配到的 ITEM_CODE
    FinalResult.spart_code,
    FinalResult.Original_ITEM_CODE,
    FinalResult.Step,
	FinalResult.LV0_PROD_RND_TEAM_CODE,  
    FinalResult.LV1_PROD_RND_TEAM_CODE, 
    FinalResult.LV2_PROD_RND_TEAM_CODE,
    FinalResult.LV3_PROD_RND_TEAM_CODE,  
	FinalResult.LV4_PROD_RND_TEAM_CODE,
	FinalResult.LV0_PROD_LIST_CODE,
    -1,
    CURRENT_TIMESTAMP,
    -1,
    CURRENT_TIMESTAMP
FROM CTE FinalResult
LEFT JOIN (SELECT DISTINCT ITEM_CODE ,APD_SHIPMENT_OBJECT
                        FROM  FIN_DM_OPT_FOI.DM_FOC_INV_ITEM_MANUFACTURE_T
                WHERE  VERSION_ID = TO_CHAR(CURRENT_TIMESTAMP,'YYYYMM')
          	    AND   APD_MANUFACTURE_PROD_LV0 IN ('IAS') 
          	    AND   APD_MANUFACTURE_PROD_LV1 IN ('IAS') 
          	    AND   APD_SHIPMENT_OBJECT IS NOT NULL
				AND   APD_MANUFACTURE_OBJECT IS NOT NULL
          	    AND   ITEM_CODE IS NOT NULL  
	    )  M
    ON FinalResult.Current_ITEM_CODE = M.ITEM_CODE
WHERE M.ITEM_CODE IS NOT NULL
AND   APD_SHIPMENT_OBJECT IS NOT NULL  ;
  -- 仅保留最终匹配成功的记录

  
ANALYZE  FIN_DM_OPT_FOI.DM_FOC_IAS_INCOME_RECURSIVE_RESULT ;



--3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集DM_FOC_IAS_INCOME_RECURSIVE_RESULT统计信息完成!');
      
   
 
  RETURN 'SUCCESS';

  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END
$$
/

