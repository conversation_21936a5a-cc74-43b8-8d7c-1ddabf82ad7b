-- Name: f_dm_fom_annl_cost_bak20240816; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_annl_cost_bak20240816(f_caliber_flag character varying, f_keystr character varying DEFAULT NULL::character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2023-12-5
  创建人  ：唐钦
  背景描述：ITEM的年均本基础表(补齐后，自制数据加密)
  参数描述：f_caliber_flag : 数据口径(自制：M,EMS：E),f_version_id：版本号（可以函数代码中取版本号，也可直接入参），f_keystr：密钥（自制数据需要加密/EMS数据明文展示），x_success_flag ：是否成功
  事例    ：select fin_dm_opt_foi.f_dm_fom_annl_cost('M')
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOM_ANNL_COST'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --新的版本号
  V_KEYSTR  varchar2(50) := F_KEYSTR;  -- 密钥入参
  V_SQL TEXT;
  V_SQL_CONDITION TEXT;   -- 筛选条件
  V_FROM_TABLE VARCHAR2(500);   -- 来源表
  V_IN_AMT VARCHAR2(500);   -- 金额字段
  V_IN_AVG VARCHAR2(500);   -- 均本字段
  V_SQL_AMT_AVG TEXT;   -- 对金额和均本字段进行加密/不加密处理
  
BEGIN
  X_RESULT_STATUS = '1';
  -- 取刷新数据的版本号
  IF F_VERSION_ID IS NULL THEN
     SELECT VERSION_ID INTO V_VERSION_ID
         FROM FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T 
         ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;
  
  -- 开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  -- 清空目标表数据
  DELETE FROM FIN_DM_OPT_FOI.DM_FOM_ANNL_COST_T WHERE CALIBER_FLAG = F_CALIBER_FLAG;
  
  -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除表：DM_FOM_ANNL_COST_T，数据口径为：'||F_CALIBER_FLAG||'的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  IF F_CALIBER_FLAG = 'M' THEN 
  -- 如果是自制数据的话，需要建临时表对数据进行解密
   DROP TABLE IF EXISTS MADE_DECRYPT_TMP;
   CREATE TEMPORARY TABLE MADE_DECRYPT_TMP(
      PERIOD_ID    bigint,
      PERIOD_YEAR  bigint,
      LV0_CODE    varchar(50),
      LV0_CN_NAME    varchar(200),
      LV1_CODE    varchar(50),
      LV1_CN_NAME    varchar(200),
      BUSSINESS_OBJECT_CODE    varchar(50),
      BUSSINESS_OBJECT_CN_NAME    varchar(200),
      SHIPPING_OBJECT_CODE     varchar(50),
      SHIPPING_OBJECT_CN_NAME    varchar(50),
      MANUFACTURE_OBJECT_CODE     varchar(50),
      MANUFACTURE_OBJECT_CN_NAME    varchar(50),
      ITEM_CODE    varchar(50),
      ITEM_CN_NAME    varchar(1000),
      TRANSACTION_QUANTITY NUMERIC,
      RMB_MADE_AMT NUMERIC
   )
   ON COMMIT PRESERVE ROWS
   DISTRIBUTE BY HASH(ITEM_CODE);

  -- 解密数据，并插入临时表
  INSERT INTO MADE_DECRYPT_TMP(
      PERIOD_ID,
      PERIOD_YEAR,
      LV0_CODE,
      LV0_CN_NAME,
      LV1_CODE,
      LV1_CN_NAME,
      BUSSINESS_OBJECT_CODE,
      BUSSINESS_OBJECT_CN_NAME,
      SHIPPING_OBJECT_CODE,
      SHIPPING_OBJECT_CN_NAME,
      MANUFACTURE_OBJECT_CODE,
      MANUFACTURE_OBJECT_CN_NAME,
      ITEM_CODE,
      ITEM_CN_NAME,
      TRANSACTION_QUANTITY,
      RMB_MADE_AMT)
   SELECT PERIOD_ID,
      PERIOD_YEAR,
      LV0_CODE,
      LV0_CN_NAME,
      LV1_CODE,
      LV1_CN_NAME,
      BUSSINESS_OBJECT_CODE,
      BUSSINESS_OBJECT_CN_NAME,
      SHIPPING_OBJECT_CODE,
      SHIPPING_OBJECT_CN_NAME,
      MANUFACTURE_OBJECT_CODE,
      MANUFACTURE_OBJECT_CN_NAME,
      ITEM_CODE,
      ITEM_CN_NAME,
      TRANSACTION_QUANTITY,
      CAST(GS_DECRYPT(RMB_MADE_AMT, V_KEYSTR, 'AES128', 'CBC', 'SHA256') AS NUMBER) AS RMB_MADE_AMT  -- 解密
    FROM FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T
    WHERE VERSION_ID = V_VERSION_ID
    AND ONLY_ITEM_FLAG = 'N'
    AND CALIBER_FLAG = 'M';   -- 自制数据

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM =>  V_STEP_NUM,
   F_CAL_LOG_DESC => '自制数据-加解密数据解密完成，取到版本号：'||V_VERSION_ID||'，且数据口径为：'||F_CALIBER_FLAG||'，并已插入临时表',
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');  
   
  -- 如果是EMS数据的话，直接跳过
  ELSIF F_CALIBER_FLAG = 'E' THEN NULL;
  END IF;
   
  -- 均本数据放入临时表
   DROP TABLE IF EXISTS ANNL_AVG_TMP;
   CREATE TEMPORARY TABLE ANNL_AVG_TMP(
      PERIOD_YEAR    bigint,
      LV0_CODE    varchar(50),
      LV0_CN_NAME    varchar(200),
      LV1_CODE    varchar(50),
      LV1_CN_NAME    varchar(200),
      BUSSINESS_OBJECT_CODE    varchar(50),
      BUSSINESS_OBJECT_CN_NAME    varchar(200),
      SHIPPING_OBJECT_CODE     varchar(50),
      SHIPPING_OBJECT_CN_NAME    varchar(50),
      MANUFACTURE_OBJECT_CODE     varchar(50),
      MANUFACTURE_OBJECT_CN_NAME    varchar(50),
      ITEM_CODE    varchar(50),
      ITEM_CN_NAME    varchar(1000),
      RMB_EMS_AMT NUMERIC,
      RMB_EMS_AVG NUMERIC,
      RMB_MADE_AMT varchar(2000),
      RMB_MADE_AVG varchar(2000),
      NULL_FLAG varchar(2),
      APD_FLAG varchar(2),
      CALIBER_FLAG varchar(2)
   )
   ON COMMIT PRESERVE ROWS
   DISTRIBUTE BY HASH(ITEM_CODE);
   
  IF F_CALIBER_FLAG = 'M' THEN 
     V_FROM_TABLE := 'MADE_DECRYPT_TMP';
     V_SQL_AMT_AVG := 'GS_ENCRYPT(T2.COST_AMT, '''||V_KEYSTR||''', ''AES128'', ''CBC'', ''SHA256''),
                       GS_ENCRYPT(T2.AVG_AMT, '''||V_KEYSTR||''', ''AES128'', ''CBC'', ''SHA256''),';   -- 自制数据需要加密
     V_IN_AMT := 'RMB_MADE_AMT';
     V_IN_AVG := 'RMB_MADE_AVG';
     V_SQL_CONDITION := '-- WHERE '||V_IN_AMT||' > 0';   -- 2024/03/07修改
  ELSIF F_CALIBER_FLAG = 'E' THEN 
        V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T';
        V_SQL_AMT_AVG := 'T2.COST_AMT,
                          T2.AVG_AMT,';   -- EMS数据不需要加密
        V_IN_AMT := 'RMB_EMS_AMT';
        V_IN_AVG := 'RMB_EMS_AVG';
        V_SQL_CONDITION := ' WHERE CALIBER_FLAG = ''E''
--                             AND '||V_IN_AMT||' > 0   -- 2024/03/07修改
                             AND VERSION_ID = '||V_VERSION_ID;
  END IF;
     
  V_SQL := '
  INSERT INTO ANNL_AVG_TMP (
       PERIOD_YEAR,
       LV0_CODE,
       LV0_CN_NAME,
       LV1_CODE,
       LV1_CN_NAME,
       BUSSINESS_OBJECT_CODE,
       BUSSINESS_OBJECT_CN_NAME,
       SHIPPING_OBJECT_CODE,
       SHIPPING_OBJECT_CN_NAME,
       MANUFACTURE_OBJECT_CODE,
       MANUFACTURE_OBJECT_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       '||V_IN_AMT||',
       '||V_IN_AVG||',
       NULL_FLAG,
       APD_FLAG,
       CALIBER_FLAG)
   WITH AVG_COST_TMP AS(
  -- 均本数据处理
     SELECT PERIOD_YEAR,
            LV0_CODE,
            LV0_CN_NAME,
            LV1_CODE,
            LV1_CN_NAME,
            BUSSINESS_OBJECT_CODE,
            BUSSINESS_OBJECT_CN_NAME,
            SHIPPING_OBJECT_CODE,
            SHIPPING_OBJECT_CN_NAME,
            MANUFACTURE_OBJECT_CODE,
            MANUFACTURE_OBJECT_CN_NAME,
            ITEM_CODE,
            ITEM_CN_NAME,
            SUM('||V_IN_AMT||') AS COST_AMT,
            NVL(DECODE(SUM('||V_IN_AMT||'),0,0,SUM('||V_IN_AMT||')/NULLIF(SUM(TRANSACTION_QUANTITY),0)),0) AS AVG_AMT
                    -- 当汇总的金额数据为0时，值为0；否则计算均本数据
          FROM '||V_FROM_TABLE
          ||V_SQL_CONDITION||'
          GROUP BY PERIOD_YEAR,
                   LV0_CODE,
                   LV0_CN_NAME,
                   LV1_CODE,
                   LV1_CN_NAME,
                   BUSSINESS_OBJECT_CODE,
                   BUSSINESS_OBJECT_CN_NAME,
                   SHIPPING_OBJECT_CODE,
                   SHIPPING_OBJECT_CN_NAME,
                   MANUFACTURE_OBJECT_CODE,
                   MANUFACTURE_OBJECT_CN_NAME,
                   ITEM_CODE,
                   ITEM_CN_NAME)
  -- 实际数均价临时表中出现的各个层级维度等，取数范围：三年前至当前系统年(若当年为1月时，当年年份不含)
  , DIM_TEAM_TMP AS(
        SELECT DISTINCT LV0_CODE,
               LV0_CN_NAME,
               LV1_CODE,
               LV1_CN_NAME,
               BUSSINESS_OBJECT_CODE,
               BUSSINESS_OBJECT_CN_NAME,
               SHIPPING_OBJECT_CODE,
               SHIPPING_OBJECT_CN_NAME,
               MANUFACTURE_OBJECT_CODE,
               MANUFACTURE_OBJECT_CN_NAME,
               ITEM_CODE,
               ITEM_CN_NAME
            FROM AVG_COST_TMP
  )                        
    -- 生成连续年份, 三年前的年份+当年
  , PERIOD_YEAR_TMP AS(     
           SELECT CAST(GENERATE_SERIES(YEAR(CURRENT_TIMESTAMP)-3,
                              YEAR(CURRENT_TIMESTAMP),
                              1) AS BIGINT) AS PERIOD_YEAR FROM DUAL
                   )
    -- 生成连续年的发散维
 , CONTIN_DIM_TMP AS(
       SELECT T2.PERIOD_YEAR,
              T1.LV0_CODE,
              T1.LV0_CN_NAME,
              T1.LV1_CODE,
              T1.LV1_CN_NAME,
              T1.BUSSINESS_OBJECT_CODE,
              T1.BUSSINESS_OBJECT_CN_NAME,
              T1.SHIPPING_OBJECT_CODE,
              T1.SHIPPING_OBJECT_CN_NAME,
              T1.MANUFACTURE_OBJECT_CODE,
              T1.MANUFACTURE_OBJECT_CN_NAME,
              T1.ITEM_CODE,
              T1.ITEM_CN_NAME
           FROM DIM_TEAM_TMP T1,PERIOD_YEAR_TMP T2
  )
    SELECT T1.PERIOD_YEAR,
           T1.LV0_CODE,
           T1.LV0_CN_NAME,
           T1.LV1_CODE,
           T1.LV1_CN_NAME,
           T1.BUSSINESS_OBJECT_CODE,
           T1.BUSSINESS_OBJECT_CN_NAME,
           T1.SHIPPING_OBJECT_CODE,
           T1.SHIPPING_OBJECT_CN_NAME,
           T1.MANUFACTURE_OBJECT_CODE,
           T1.MANUFACTURE_OBJECT_CN_NAME,
           T1.ITEM_CODE,
           T1.ITEM_CN_NAME,
           '||V_SQL_AMT_AVG||'
           DECODE(T2.AVG_AMT, NULL, 0, 1) AS NULL_FLAG, --空标识, 用于sum开窗累计
           DECODE(T2.AVG_AMT, NULL, ''Y'', ''N'') AS APD_FLAG, --补齐标识：Y为补齐，N为原始
           '''||F_CALIBER_FLAG||''' AS CALIBER_FLAG
       FROM CONTIN_DIM_TMP T1
       LEFT JOIN AVG_COST_TMP T2
       ON T1.LV0_CODE = T2.LV0_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.BUSSINESS_OBJECT_CODE = T2.BUSSINESS_OBJECT_CODE
       AND NVL(T1.SHIPPING_OBJECT_CODE,''S1'') = NVL(T2.SHIPPING_OBJECT_CODE,''S1'')
       AND NVL(T1.MANUFACTURE_OBJECT_CODE,''S2'') = NVL(T2.MANUFACTURE_OBJECT_CODE,''S2'')
       AND T1.ITEM_CODE = T2.ITEM_CODE
       AND T1.PERIOD_YEAR = T2.PERIOD_YEAR';
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL; 


  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM =>  V_STEP_NUM,
   F_CAL_LOG_DESC => '版本号为：'||V_VERSION_ID||'，且数据口径为：'||F_CALIBER_FLAG||'，均本数据计算完成，并插入临时表',
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');  
   
  -- 均本数据补齐逻辑
  -- 插入补齐后的分视角下年均本数据
  V_SQL := '
  INSERT INTO DM_FOM_ANNL_COST_T (
         VERSION_ID,
         PERIOD_YEAR,
         LV0_CODE,
         LV0_CN_NAME,
         LV1_CODE,
         LV1_CN_NAME,
         BUSSINESS_OBJECT_CODE,
         BUSSINESS_OBJECT_CN_NAME,
         SHIPPING_OBJECT_CODE,
         SHIPPING_OBJECT_CN_NAME,
         MANUFACTURE_OBJECT_CODE,
         MANUFACTURE_OBJECT_CN_NAME,
         ITEM_CODE,
         ITEM_CN_NAME,
         '||V_IN_AMT||',
         '||V_IN_AVG||',
         APPEND_FLAG,
         APPEND_YEAR,
         CALIBER_FLAG,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
         )
 -- 按不同视角，补齐对应的重量级团队，以及采购信息维，前向补齐年均本
 WITH FORWARD_FILLER_TEMP AS
     (
      SELECT SS.PERIOD_YEAR,
             SS.LV0_CODE,
             SS.LV0_CN_NAME,
             SS.LV1_CODE,
             SS.LV1_CN_NAME,
             SS.BUSSINESS_OBJECT_CODE,
             SS.BUSSINESS_OBJECT_CN_NAME,
             SS.SHIPPING_OBJECT_CODE,
             SS.SHIPPING_OBJECT_CN_NAME,
             SS.MANUFACTURE_OBJECT_CODE,
             SS.MANUFACTURE_OBJECT_CN_NAME,
             SS.ITEM_CODE,
             SS.ITEM_CN_NAME,
             SS.'||V_IN_AMT||',
             SS.'||V_IN_AVG||',
             FIRST_VALUE(SS.'||V_IN_AVG||') OVER(PARTITION BY SS.CALIBER_FLAG, SS.LV0_CODE, SS.LV1_CODE, SS.BUSSINESS_OBJECT_CODE, NVL(SS.SHIPPING_OBJECT_CODE,''S1''), NVL(SS.MANUFACTURE_OBJECT_CODE,''S2''), SS.ITEM_CODE, SS.AVG_AMT_FLAG ORDER BY SS.PERIOD_YEAR) AS AVG_AMT_2, --新补齐的均价字段
             FIRST_VALUE(SS.PERIOD_YEAR) OVER(PARTITION BY SS.CALIBER_FLAG, SS.LV0_CODE, SS.LV1_CODE, SS.BUSSINESS_OBJECT_CODE, NVL(SS.SHIPPING_OBJECT_CODE,''S1''), NVL(SS.MANUFACTURE_OBJECT_CODE,''S2''), SS.ITEM_CODE, SS.AVG_AMT_FLAG ORDER BY SS.PERIOD_YEAR) AS PERIOD_YEAR_2, --新补齐的年份字段
             SS.AVG_AMT_FLAG,
             SS.APD_FLAG,
             SS.CALIBER_FLAG
         FROM (SELECT S.LV0_CODE,
                      S.LV0_CN_NAME,
                      S.LV1_CODE,
                      S.LV1_CN_NAME,
                      S.BUSSINESS_OBJECT_CODE,
                      S.BUSSINESS_OBJECT_CN_NAME,
                      S.SHIPPING_OBJECT_CODE,
                      S.SHIPPING_OBJECT_CN_NAME,
                      S.MANUFACTURE_OBJECT_CODE,
                      S.MANUFACTURE_OBJECT_CN_NAME,
                      S.ITEM_CODE,
                      S.ITEM_CN_NAME,
                      S.PERIOD_YEAR,
                      S.'||V_IN_AMT||',
                      S.'||V_IN_AVG||',
                      SUM(S.NULL_FLAG) OVER(PARTITION BY S.CALIBER_FLAG, S.LV0_CODE, S.LV1_CODE, S.BUSSINESS_OBJECT_CODE, NVL(S.SHIPPING_OBJECT_CODE,''S1''), NVL(S.MANUFACTURE_OBJECT_CODE,''S2''), S.ITEM_CODE ORDER BY S.PERIOD_YEAR) AS AVG_AMT_FLAG, --均价标识: 为空不参与累计加1
                      S.APD_FLAG,
                      S.CALIBER_FLAG
                  FROM ANNL_AVG_TMP S) SS)
    
    --向后补齐均价
    SELECT '||V_VERSION_ID||' AS VERSION_ID,
           S.PERIOD_YEAR,
           S.LV0_CODE,
           S.LV0_CN_NAME,
           S.LV1_CODE,
           S.LV1_CN_NAME,
           S.BUSSINESS_OBJECT_CODE,
           S.BUSSINESS_OBJECT_CN_NAME,
           S.SHIPPING_OBJECT_CODE,
           S.SHIPPING_OBJECT_CN_NAME,
           S.MANUFACTURE_OBJECT_CODE,
           S.MANUFACTURE_OBJECT_CN_NAME,
           S.ITEM_CODE,
           S.ITEM_CN_NAME,
           S.'||V_IN_AMT||',
           NVL(S.AVG_AMT_2, S.AVG_AMT_3) AS '||V_IN_AVG||',
           S.APD_FLAG AS APPEND_FLAG,
           S.APPEND_YEAR,
           S.CALIBER_FLAG,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
       FROM (SELECT T1.PERIOD_YEAR,
                    T1.LV0_CODE,
                    T1.LV0_CN_NAME,
                    T1.LV1_CODE,
                    T1.LV1_CN_NAME,
                    T1.BUSSINESS_OBJECT_CODE,
                    T1.BUSSINESS_OBJECT_CN_NAME,
                    T1.SHIPPING_OBJECT_CODE,
                    T1.SHIPPING_OBJECT_CN_NAME,
                    T1.MANUFACTURE_OBJECT_CODE,
                    T1.MANUFACTURE_OBJECT_CN_NAME,
                    T1.ITEM_CODE,
                    T1.ITEM_CN_NAME,
                    T1.'||V_IN_AMT||',
                    T1.AVG_AMT_2,
                    T1.PERIOD_YEAR_2,
                    T2.AVG_AMT_3,
                    T2.PERIOD_YEAR_3,
                    T1.APD_FLAG,
                    T1.CALIBER_FLAG,
                    CASE WHEN T1.APD_FLAG = ''Y'' AND T1.AVG_AMT_2 IS NOT NULL THEN
                           T1.PERIOD_YEAR_2 ELSE NULL END AS APPEND_YEAR  
                FROM FORWARD_FILLER_TEMP T1
                LEFT JOIN (SELECT DISTINCT P.LV0_CODE,
                                  P.LV1_CODE,
                                  P.BUSSINESS_OBJECT_CODE,
                                  P.SHIPPING_OBJECT_CODE,
                                  P.MANUFACTURE_OBJECT_CODE,
                                  P.ITEM_CODE,
                                  FIRST_VALUE(P.PERIOD_YEAR) OVER(PARTITION BY P.CALIBER_FLAG, P.LV0_CODE, P.LV1_CODE, P.BUSSINESS_OBJECT_CODE, NVL(P.SHIPPING_OBJECT_CODE,''S1''), NVL(P.MANUFACTURE_OBJECT_CODE,''S2''), P.ITEM_CODE ORDER BY P.PERIOD_YEAR ASC) AS PERIOD_YEAR_3, --有均价的首条会计期
                                  FIRST_VALUE(P.AVG_AMT_2) OVER(PARTITION BY P.CALIBER_FLAG, P.LV0_CODE, P.LV1_CODE, P.BUSSINESS_OBJECT_CODE, NVL(P.SHIPPING_OBJECT_CODE,''S1''), NVL(P.MANUFACTURE_OBJECT_CODE,''S2''), P.ITEM_CODE ORDER BY P.PERIOD_YEAR ASC) AS AVG_AMT_3, --有均价的首条补齐均价
                                  P.CALIBER_FLAG
                              FROM FORWARD_FILLER_TEMP P
                              WHERE P.AVG_AMT_FLAG > 0) T2
                              ON T1.CALIBER_FLAG = T2.CALIBER_FLAG
                              AND T1.LV0_CODE = T2.LV0_CODE
                              AND T1.LV1_CODE = T2.LV1_CODE
                              AND T1.BUSSINESS_OBJECT_CODE = T2.BUSSINESS_OBJECT_CODE
                              AND NVL(T1.SHIPPING_OBJECT_CODE,''S1'') = NVL(T2.SHIPPING_OBJECT_CODE,''S1'')
                              AND NVL(T1.MANUFACTURE_OBJECT_CODE,''S2'') = NVL(T2.MANUFACTURE_OBJECT_CODE,''S2'')
                              AND T1.ITEM_CODE = T2.ITEM_CODE
                              AND T1.PERIOD_YEAR < T2.PERIOD_YEAR_3) S                          
                   ';                
        EXECUTE IMMEDIATE V_SQL; 
        DBMS_OUTPUT.PUT_LINE(V_SQL);

 --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '补齐年均本数据，并插入版本号为：'||V_VERSION_ID||'，且数据口径为：'||F_CALIBER_FLAG||'，的全量数据到DM_FOM_ANNL_COST_T表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');      
  DBMS_OUTPUT.PUT_LINE('补齐数据并全量保存');    
   
   --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FOM_ANNL_COST_T';

  --3.日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集DM_FOM_ANNL_COST_T统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

