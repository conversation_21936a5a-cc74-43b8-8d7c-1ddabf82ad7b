-- Name: f_dm_foc_repl_decode; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_repl_decode(f_caliber_flag character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
最后修改人:罗若文
背景描述: 求配置发货额均价


事例:FIN_DM_OPT_FOI.F_DM_FOC_REPL_DECODE()
*/
DECLARE
  V_SP_NAME    VARCHAR2(200) := 'FIN_DM_OPT_FOI.F_DM_FOC_REPL_DECODE'; --存储过程名称
  V_VERSION_ID BIGINT ; --版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_SQL  TEXT; -- SQL逻辑
  V_FROM_TABLE VARCHAR(50); --来源表
  V_TO_TABLE VARCHAR(50);   --目标表
  V_VERSION_TABLE VARCHAR(100);
	
	
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   

  
  IF F_CALIBER_FLAG = 'C' THEN 
		V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_MID_MON_GROUP_T';--来源表
		V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_DECODE_T'; --目标表
    
  ELSIF F_CALIBER_FLAG = 'R' THEN 
		V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_MID_MON_GROUP_T';--来源表
		V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_DECODE_T'; --目标表
  
  ELSE 
		NULL;
  
  END IF;

--传入版本号
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 

  
  --1.清空目标表数据:
  EXECUTE IMMEDIATE 'DELETE FROM  '||V_TO_TABLE||' WHERE CALIBER_FLAG = '''||F_CALIBER_FLAG||''' ';
  

  
  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据, 版本号='||V_VERSION_ID||',时点：'||F_CALIBER_FLAG,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
 --创建基线成本收敛临时表
    DROP TABLE IF EXISTS DECODE_DATA_TEMP;
    CREATE TEMPORARY TABLE DECODE_DATA_TEMP (
        VERSION_ID BIGINT,
        PERIOD_YEAR BIGINT,
        PERIOD_ID BIGINT,
        LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
        LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(400),
        LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(400),
        LV3_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(400),
		REPLACEMENT_GROUP_ID VARCHAR(50),
		BINDING_CONFIG_ID VARCHAR(50),
		REPLACEMENT_DESCRIPTION VARCHAR(2000),
		LAST_AMT NUMERIC,
		CURRENT_AMT NUMERIC,
		REPLACING_LY_SHIP_QTY NUMERIC,
		REPLACING_CY_SHIP_QTY NUMERIC,
		RMB_AAA_REP_BASELINE_COST_AMT NUMERIC,
		RMB_AAA_BINDING_CUR_COST_AMT NUMERIC,
		RMB_COST_AMT   NUMERIC,
		VIEW_FLAG VARCHAR(2),
		CALIBER_FLAG VARCHAR(2)
		
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(PERIOD_ID,BINDING_CONFIG_ID);

    --2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
        F_STEP_NUM =>  V_STEP_MUM,
        F_CAL_LOG_DESC => '创建基线成本收敛临时表创建完成, 版本号='||V_VERSION_ID||',时点：'||F_CALIBER_FLAG,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS');  
    

    
    V_SQL := 
   'INSERT INTO DECODE_DATA_TEMP(
                VERSION_ID,
                PERIOD_YEAR,
                PERIOD_ID,
                LV0_PROD_RND_TEAM_CODE,
                LV0_PROD_RD_TEAM_CN_NAME,
                LV1_PROD_RND_TEAM_CODE,
                LV1_PROD_RD_TEAM_CN_NAME,
                LV2_PROD_RND_TEAM_CODE,
                LV2_PROD_RD_TEAM_CN_NAME,
                LV3_PROD_RND_TEAM_CODE,
				LV3_PROD_RD_TEAM_CN_NAME,
				REPLACEMENT_GROUP_ID ,
				BINDING_CONFIG_ID ,
				REPLACEMENT_DESCRIPTION ,
				LAST_AMT,
				CURRENT_AMT,
				REPLACING_LY_SHIP_QTY,
				REPLACING_CY_SHIP_QTY,
				RMB_AAA_REP_BASELINE_COST_AMT,
				RMB_AAA_BINDING_CUR_COST_AMT,
				RMB_COST_AMT,
				VIEW_FLAG,
				CALIBER_FLAG
                
				)
       SELECT 	'||V_VERSION_ID||',
                SUBSTR(PERIOD_ID,0,4) AS PERIOD_YEAR,
                PERIOD_ID,
                LV0_PROD_RND_TEAM_CODE,
                LV0_PROD_RD_TEAM_CN_NAME,
                LV1_PROD_RND_TEAM_CODE,
                LV1_PROD_RD_TEAM_CN_NAME,
                LV2_PROD_RND_TEAM_CODE,
                LV2_PROD_RD_TEAM_CN_NAME,
                LV3_PROD_RND_TEAM_CODE,
				LV3_PROD_RD_TEAM_CN_NAME,
				REPLACEMENT_GROUP_ID ,
				BINDING_CONFIG_ID ,
				REPLACEMENT_DESCRIPTION ,
				LAST_AMT,
				CURRENT_AMT,
				REPLACING_LY_SHIP_QTY,
				REPLACING_CY_SHIP_QTY,
				RMB_AAA_REP_BASELINE_COST_AMT,
				RMB_AAA_BINDING_CUR_COST_AMT,
				RMB_COST_AMT,
				VIEW_FLAG,
				CALIBER_FLAG
            FROM '||V_FROM_TABLE ||' T
            WHERE 1 = 1 
			AND CALIBER_FLAG = '''||F_CALIBER_FLAG||'''  '
			;
    DBMS_OUTPUT.PUT_LINE(V_SQL);   
    EXECUTE IMMEDIATE V_SQL;
           
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '数据插入会话级临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_FORMULA_SQL_TXT => V_SQL,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');  
   


  
  --插入均价数据
  V_SQL := 
  'INSERT INTO '|| V_TO_TABLE ||' 
       (
		VERSION_ID,
		PERIOD_YEAR,
		PERIOD_ID,
		LV0_PROD_RND_TEAM_CODE,
		LV0_PROD_RD_TEAM_CN_NAME,
		LV1_PROD_RND_TEAM_CODE,
		LV1_PROD_RD_TEAM_CN_NAME,
		LV2_PROD_RND_TEAM_CODE,
		LV2_PROD_RD_TEAM_CN_NAME,
		LV3_PROD_RND_TEAM_CODE,
		LV3_PROD_RD_TEAM_CN_NAME,
		REPLACEMENT_GROUP_ID ,
		REPLACEMENT_DESCRIPTION ,
		REPLACING_LY_SHIP_QTY,
		REPLACING_CY_SHIP_QTY,
		RMB_AAA_REP_BASELINE_COST_AMT,
		RMB_AAA_BINDING_CUR_COST_AMT,
		PERIOD_AVG_AMT,
		BASE_PERIOD_AVG_AMT,
		RMB_COST_AMT,
		VIEW_FLAG,
		CALIBER_FLAG,
		CREATED_BY ,
		CREATION_DATE ,
		LAST_UPDATED_BY ,
		LAST_UPDATE_DATE ,
		DEL_FLAG
		)
		
	SELECT 	VERSION_ID,
			PERIOD_YEAR,
			PERIOD_ID,
			LV0_PROD_RND_TEAM_CODE,
			LV0_PROD_RD_TEAM_CN_NAME,
			LV1_PROD_RND_TEAM_CODE,
			LV1_PROD_RD_TEAM_CN_NAME,
			LV2_PROD_RND_TEAM_CODE,
			LV2_PROD_RD_TEAM_CN_NAME,
			LV3_PROD_RND_TEAM_CODE,
			LV3_PROD_RD_TEAM_CN_NAME,
			REPLACEMENT_GROUP_ID ,
			REPLACEMENT_DESCRIPTION ,
			REPLACING_LY_SHIP_QTY ,
			REPLACING_CY_SHIP_QTY,
			RMB_AAA_REP_BASELINE_COST_AMT,
			RMB_AAA_BINDING_CUR_COST_AMT,
			PERIOD_AVG_AMT, --报告期均本
			BASE_PERIOD_AVG_AMT,   --基期均本
			RMB_COST_AMT,
			VIEW_FLAG,
			CALIBER_FLAG,
			CREATED_BY ,
			CREATION_DATE ,
			LAST_UPDATED_BY ,
			LAST_UPDATE_DATE ,
			DEL_FLAG
			FROM (
		
    SELECT 	VERSION_ID,
			PERIOD_YEAR,
			PERIOD_ID,
			LV0_PROD_RND_TEAM_CODE,
			LV0_PROD_RD_TEAM_CN_NAME,
			LV1_PROD_RND_TEAM_CODE,
			LV1_PROD_RD_TEAM_CN_NAME,
			LV2_PROD_RND_TEAM_CODE,
			LV2_PROD_RD_TEAM_CN_NAME,
			LV3_PROD_RND_TEAM_CODE,
			LV3_PROD_RD_TEAM_CN_NAME,
			REPLACEMENT_GROUP_ID ,
			REPLACEMENT_DESCRIPTION ,
			SUM(REPLACING_LY_SHIP_QTY) AS REPLACING_LY_SHIP_QTY ,
			SUM(REPLACING_CY_SHIP_QTY) AS REPLACING_CY_SHIP_QTY,
			SUM(RMB_AAA_REP_BASELINE_COST_AMT) AS RMB_AAA_REP_BASELINE_COST_AMT,
			RMB_AAA_BINDING_CUR_COST_AMT AS RMB_AAA_BINDING_CUR_COST_AMT,
			SUM(CURRENT_AMT ) / NULLIF(SUM(REPLACING_CY_SHIP_QTY),0) AS PERIOD_AVG_AMT, --报告期均本
			SUM(LAST_AMT ) / NULLIF(SUM(REPLACING_LY_SHIP_QTY),0)  AS  BASE_PERIOD_AVG_AMT,   --基期均本
			SUM(RMB_COST_AMT) AS RMB_COST_AMT,
			VIEW_FLAG,
			CALIBER_FLAG,
			-1 AS CREATED_BY ,
			CURRENT_TIMESTAMP AS CREATION_DATE ,
			-1 AS LAST_UPDATED_BY ,
			CURRENT_TIMESTAMP AS LAST_UPDATE_DATE ,
			''N'' AS DEL_FLAG	
        FROM DECODE_DATA_TEMP T
        GROUP BY 	VERSION_ID,
					PERIOD_YEAR,
					PERIOD_ID,
					LV0_PROD_RND_TEAM_CODE,
					LV0_PROD_RD_TEAM_CN_NAME,
					LV1_PROD_RND_TEAM_CODE,
					LV1_PROD_RD_TEAM_CN_NAME,
					LV2_PROD_RND_TEAM_CODE,
					LV2_PROD_RD_TEAM_CN_NAME,
					LV3_PROD_RND_TEAM_CODE,
					LV3_PROD_RD_TEAM_CN_NAME,
					REPLACEMENT_GROUP_ID ,
					REPLACEMENT_DESCRIPTION ,
					RMB_AAA_BINDING_CUR_COST_AMT,
					VIEW_FLAG,
					CALIBER_FLAG
					)
		-- WHERE (RMB_AAA_REP_BASELINE_COST_AMT > 0 AND REPLACING_CY_SHIP_QTY > 0 
		--	 AND RMB_AAA_BINDING_CUR_COST_AMT > 0 AND REPLACING_LY_SHIP_QTY > 0) -- 取金额为非负数的数据			
		
					';					
	DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;                 
                          
  --3.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '收敛均价, 并插数到'||V_TO_TABLE||', 版本号='||V_VERSION_ID||',时点：'||F_CALIBER_FLAG,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_FORMULA_SQL_TXT => V_SQL,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   
  
  --5.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --6.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!, 版本号='||V_VERSION_ID||',时点：'||F_CALIBER_FLAG);
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败, 版本号='||V_VERSION_ID||',时点：'||F_CALIBER_FLAG, 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

