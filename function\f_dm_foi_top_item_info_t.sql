-- Name: f_dm_foi_top_item_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_top_item_info_t(f_caliber_flag character varying, f_cate_version bigint DEFAULT NULL::bigint, f_item_version bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
最近更新时间:2024年6月21日14点50分
修改人:		黄心蕊
修改内容:	202407版本 新增华东采购
更  新  人 ：杨泽宝 ywx1106160
背景描述：TOP品类规格品清单,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一()：导入版本号
		参数二(x_success_flag)：运行状态返回值-成功或者失败
		参数三(F_CALIBER_FLAG):入参 I 代表ICT采购，E代表数字能源
目标表：FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_INFO_T ICT采购 ;
		FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_INFO_T 数字能源
事例    ：
select FIN_DM_OPT_FOI.f_dm_foi_top_item_info_t('IAS');
select FIN_DM_OPT_FOI.f_dm_foi_top_item_info_t('EAST_CHINA_PQC');
****************************************************************************************************************************************************************/
declare
  V_SP_NAME       VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOI_TOP_ITEM_INFO_T';
  V_STEP_NUM      BIGINT := 0; --步骤号
  V_DML_ROW_COUNT NUMBER DEFAULT 0;
  V_YEAR          INT := YEAR(CURRENT_TIMESTAMP); -- 当年
  V_ITEM_RULE     NUMBER; -- 规格品挑选逻辑占比
  V_VERSION       BIGINT; -- 版本号
  V_CATE_VERSION  BIGINT;
  V_EXECUTE_SQL   TEXT := NULL; -- 执行SQL
  V_PART1_PUBLIC  TEXT := NULL; -- 公共部分函数
  V_VERSION_NAME  VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMM'); --新的版本中文名称
  V_CURRENT_FLAG  BIGINT;
  V_SQL           TEXT; --SQL逻辑

  V_ACTUAL_TABLE   VARCHAR(100); -- 来源表1 历史数表
  V_FCST_TABLE     VARCHAR(100); -- 来源表2   预测数汇总表
  V_FROM_TABLE3    VARCHAR(100); -- 来源表3
  V_TOP_CATE_TABLE VARCHAR(100); -- 来源表4  TOP_CATE表
  V_TO_TABLE       VARCHAR(100); -- 目标表
  V_JOIN_TABLE     VARCHAR(100); --补齐表
  V_ID             VARCHAR(10); --目标表字段
  V_INSERT_ID      VARCHAR(100); --目标表字段
  V_APPEND_FLAG    VARCHAR(50); --目标表字段
  --202407版本 新增华东采购与IAS
  V_CALIBER_FLAG  TEXT;
  V_IN_CALIBER    TEXT;
  V_IAS_ECPQC_SQL TEXT; --华东采购IAS表新增CALIBER_FLAG字段
  V_JOIN_CALIBER_FLAG TEXT;
  
begin
	x_success_flag := '1';
 
  --通过F_CALIBER_FLAG传参,确认来源表和目标表
  IF F_CALIBER_FLAG = 'I' THEN
    -- ICT采购
    V_ACTUAL_TABLE   := 'FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_REPORT_T'; --来源表1
    V_FCST_TABLE     := 'FIN_DM_OPT_FOI.DM_FOI_FCST_SUM_T'; --来源表2
    V_FROM_TABLE3    := 'FIN_DM_OPT_FOI.DM_FOI_PLAN_VERSION_T'; --来源表3
    V_TOP_CATE_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_TOP_CATE_INFO_T'; --来源表4
    V_TO_TABLE       := 'FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_INFO_T'; --目标表
  ELSIF F_CALIBER_FLAG = 'E' THEN
    -- 数字能源
    V_ACTUAL_TABLE   := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T'; --来源表1
    V_FCST_TABLE     := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_FCST_SUM_T'; --来源表2   
    V_FROM_TABLE3    := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T'; --来源表3
    V_TOP_CATE_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_CATE_INFO_T'; --来源表4 
    V_TO_TABLE       := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_INFO_T'; --目标表
  ELSE
	--202407版本 新增华东采购与IAS
    V_ACTUAL_TABLE   := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_HIS_ORD_REC_REPORT_T'; --来源表1
    V_FCST_TABLE     := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_FCST_SUM_T'; --来源表2   
    V_TOP_CATE_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_TOP_CATE_INFO_T'; --来源表4 
    V_TO_TABLE       := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_TOP_ITEM_INFO_T'; --目标表
	V_CALIBER_FLAG   := 'CALIBER_FLAG,';
	V_IN_CALIBER     := ''''||F_CALIBER_FLAG||''' AS CALIBER_FLAG,';
	V_IAS_ECPQC_SQL	 := ' AND T1.CALIBER_FLAG = '''||F_CALIBER_FLAG||''' ';
	V_JOIN_CALIBER_FLAG:= 'AND T1.CALIBER_FLAG = T2.CALIBER_FLAG ';
	
    IF F_CALIBER_FLAG = 'IAS' THEN
      -- IAS
      V_FROM_TABLE3	:= 'FIN_DM_OPT_FOI.DM_FOI_IAS_PLAN_VERSION_T';
    ELSIF F_CALIBER_FLAG = 'EAST_CHINA_PQC' THEN
      -- 华东采购
      V_FROM_TABLE3	:= 'FIN_DM_OPT_FOI.DM_FOI_ECPQC_PLAN_VERSION_T';
    END IF;
	
  END IF;

   --日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

-- 将查询到的数据放到变量中的公共sql
	 V_PART1_PUBLIC := '
						SELECT VALUE 
							FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T
							WHERE ENABLE_FLAG = ''Y''
							AND UPPER(PARA_NAME) = ''$PARA_NAME$'';
						  ';        
-- 从变量参数表取出规格品挑选逻辑占比(0.9)
	 V_EXECUTE_SQL := REPLACE( V_PART1_PUBLIC,'$PARA_NAME$','TOP_ITEM');  
	 EXECUTE V_EXECUTE_SQL INTO V_ITEM_RULE;

--F_CALIBER_FLAG: I 代表ICT采购，E代表数字能源
IF F_CALIBER_FLAG = 'I' THEN  
	-- 月度调度时，传参都为NULL，取版本表中可用的、auto版本的规格品版本号作为生成本次数据的版本号	
	  IF  f_cate_version IS NULL AND f_item_version IS NULL THEN
	  -- TOP品类版本号插入到变量参数表（dm_foi_plan_var_para_t）
	  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_PUBLIC_LOGIC_T
	  (F_FLAG => 'SCHEDULE',
	   F_JUD_CONDITION => 'Q',
	   F_PRAR1 => 'CATEGORY');
		 -- 规格品版本号插入到变量参数表（dm_foi_plan_var_para_t）
	  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_PUBLIC_LOGIC_T
	  (F_FLAG => 'SCHEDULE',
	   F_JUD_CONDITION => 'Q',
	   F_PRAR1 => 'ITEM');	
	   -- TOP品类/规格品版本号放到变量中
	   V_EXECUTE_SQL := REPLACE( V_PART1_PUBLIC,'$PARA_NAME$','VERSION_ID-CATEGORY');  -- TOP品类版本号
	   EXECUTE V_EXECUTE_SQL INTO V_CATE_VERSION;

	   V_EXECUTE_SQL := REPLACE( V_PART1_PUBLIC,'$PARA_NAME$','VERSION_ID-ITEM');  -- 规格品版本号
	   EXECUTE V_EXECUTE_SQL INTO V_VERSION;

	-- 业务在前台配置页面调整规格品数据时，Java传参，优先取规格品版本号，若无，则取TOP品类版本号作为生成本次数据的版本号		
	   ELSE v_cate_version := f_cate_version;
		v_version := nvl ( f_item_version, f_cate_version );	
	   END IF;
	   
ELSIF F_CALIBER_FLAG = 'E' THEN
	IF  f_cate_version IS NULL  AND f_item_version IS NULL THEN
	  --查询TOP品类清单中最新的版本号, 作为TOP规格品的父版本ID
	  SELECT VERSION_ID INTO V_VERSION
		FROM FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T
		WHERE DEL_FLAG = 'N'
			AND STATUS = 1
			AND UPPER(DATA_TYPE) = 'ITEM'
			AND UPPER(VERSION_TYPE) IN ('AUTO', 'FINAL')
			ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
			
			  SELECT VERSION_ID INTO V_CATE_VERSION
		FROM FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T
		WHERE DEL_FLAG = 'N'
			AND STATUS = 1
			AND UPPER(DATA_TYPE) = 'CATEGORY'
			AND UPPER(VERSION_TYPE) IN ('AUTO', 'FINAL')
			ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
	ELSE v_cate_version := f_cate_version;
		v_version := nvl ( f_item_version, f_cate_version );	
	END IF;
ELSE
--202407版本 新增华东采购与IAS
	IF f_cate_version IS NULL AND f_item_version IS NULL THEN
	  V_SQL:='
		  SELECT VERSION_ID 
			FROM '||V_FROM_TABLE3||'
			WHERE DEL_FLAG = ''N''
				AND STATUS = 1
				AND UPPER(DATA_TYPE) = ''ITEM''
				AND UPPER(VERSION_TYPE) IN (''AUTO'', ''FINAL'')
				ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;';
		EXECUTE V_SQL INTO V_VERSION;
			
	  V_SQL:='
		SELECT VERSION_ID
		  FROM '||V_FROM_TABLE3||'
		 WHERE DEL_FLAG = ''N''
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = ''CATEGORY''
		   AND UPPER(VERSION_TYPE) IN (''AUTO'', ''FINAL'')
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;';
		EXECUTE V_SQL INTO V_CATE_VERSION;
	
	ELSE v_cate_version := f_cate_version;
		v_version := nvl ( f_item_version, f_cate_version );	
	END IF;

END IF;

  --写入日志

  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '本次跑数 TOP品类的版本号为 '||v_cate_version||'; 本次跑数 规格品清单的版本号为 '||v_version,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');

   
-- 支持重跑，清除目标表对应版本数据

 V_SQL := 'DELETE FROM '||V_TO_TABLE||' T1 WHERE T1.VERSION_ID = '||V_VERSION||V_IAS_ECPQC_SQL||' ';
																	--202407版本 华东采购与IAS新增CALIBER_FLAG字段
 EXECUTE IMMEDIATE V_SQL ; 
 
	
 
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||' 表的数据,并取到version_id='||v_version,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => x_success_flag,
   F_ERRBUF => 'SUCCESS');
 
 
	
--插入目标表数据：分为ICT采购和数字能源，两个模块
-- 取TOP品类清单入参版本的所有实际数
--ICT采购
IF F_CALIBER_FLAG = 'I' THEN

	--V_ID := ' ID, ';
	--V_INSERT_ID := ' FIN_DM_OPT_FOI.DM_FOI_TOP_CATE_INFO_S.NEXTVAL AS ID, ';
	V_APPEND_FLAG := ' AND APPEND_FLAG = ''N'' ';

ELSE							-- 数字能源
								-- 202407版本 新增华东采购与IAS
	V_ID := '';
	V_INSERT_ID := '';
	V_APPEND_FLAG := '';
	
END IF;	

	V_SQL := ' WITH OPT_HIS_FCST_TMP AS(
			SELECT  T2.YEAR
						,	T2.ITEM_CODE  -- ITEM编码
						, T2.ITEM_NAME   -- ITEM中文名称
					  , T2.CATEGORY_CODE     -- 品类编码
					  , T2.CATEGORY_NAME   -- 品类中文名称
						, T2.L4_CEG_CODE    --模块编码
						, T2.L4_CEG_SHORT_CN_NAME    -- 模块中文简称
						, T2.L4_CEG_CN_NAME  -- 模块中文全称
						, T2.L3_CEG_CODE     -- 专家团编码
						, T2.L3_CEG_SHORT_CN_NAME   -- 专家团中文简称
						, T2.L3_CEG_CN_NAME    -- 专家团中文全称
						, T2.L2_CEG_CODE      -- 采购组织编码
						, T2.L2_CEG_CN_NAME     -- 采购组织中文名称
						, SUM(T2.RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY 	               -- 把总金额收敛到单年、ITEM层级
					FROM '||V_ACTUAL_TABLE||' T2                   -- 历史数下单&到货明细表
					INNER JOIN '||V_TOP_CATE_TABLE||' T1                   -- TOP品类清单
					ON T2.CATEGORY_CODE = T1.CATEGORY_CODE
					'||V_JOIN_CALIBER_FLAG||'	--202407版本 华东采购IAS表新增CALIBER_FLAG字段
					WHERE T2.YEAR IN ('||V_YEAR||' -1, '||V_YEAR||' -2, '||V_YEAR||' -3, '||V_YEAR||')        -- 只取当年前3年的数据
					'||V_IAS_ECPQC_SQL||'
					AND T1.VERSION_ID = '||V_CATE_VERSION||V_APPEND_FLAG||'                             -- 限制TOP品类清单的版本号
					GROUP BY T2.YEAR
								 , T2.ITEM_CODE  
								 , T2.ITEM_NAME   
								 , T2.CATEGORY_CODE    
								 , T2.CATEGORY_NAME   
								 , T2.L4_CEG_CODE    
								 , T2.L4_CEG_SHORT_CN_NAME
								 , T2.L4_CEG_CN_NAME  
								 , T2.L3_CEG_CODE     
								 , T2.L3_CEG_SHORT_CN_NAME
								 , T2.L3_CEG_CN_NAME    
								 , T2.L2_CEG_CODE       
								 , T2.L2_CEG_CN_NAME     

			UNION ALL
		  -- 取TOP品类清单入参版本的当年预测数
					 SELECT T2.YEAR
								,	T2.ITEM_CODE
								, T2.ITEM_NAME
								, T2.CATEGORY_CODE
								, T2.CATEGORY_NAME
								, T2.L4_CEG_CODE
								, T2.L4_CEG_SHORT_CN_NAME
								, T2.L4_CEG_CN_NAME
								, T2.L3_CEG_CODE
								, T2.L3_CEG_SHORT_CN_NAME
								, T2.L3_CEG_CN_NAME
								, T2.L2_CEG_CODE
								, T2.L2_CEG_CN_NAME	
								, T2.RECEIVE_AMT_CNY	 	
						FROM '||V_FCST_TABLE||' T2
						INNER JOIN '||V_TOP_CATE_TABLE||' T1
						ON T2.CATEGORY_CODE = T1.CATEGORY_CODE
						'||V_JOIN_CALIBER_FLAG||'	--202407版本 华东采购IAS表新增CALIBER_FLAG字段
						WHERE T2.YEAR = '||V_YEAR||'
						'||V_IAS_ECPQC_SQL||'
						AND T1.VERSION_ID = '||V_CATE_VERSION||'
						AND T2.APPEND_FLAG = ''N''            -- 只取真实数据
					)
		-- 历史3年数据+3年数据，每个ITEM_CODE的总金额
			, OPT_HIS_FCST_SUM_TMP AS(
		SELECT CASE WHEN YEAR = '||V_YEAR||' THEN ''his_fcst''
						 ELSE ''his_3'' END AS TIME_FLAG
				 , ITEM_CODE
				 , ITEM_NAME
				 , CATEGORY_CODE
				 , CATEGORY_NAME
				 , L4_CEG_CODE
				 , L4_CEG_SHORT_CN_NAME
				 , L4_CEG_CN_NAME
				 , L3_CEG_CODE
				 , L3_CEG_SHORT_CN_NAME
				 , L3_CEG_CN_NAME
				 , L2_CEG_CODE
				 , L2_CEG_CN_NAME
				 , SUM(RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY
			 FROM OPT_HIS_FCST_TMP 
			 GROUP BY CASE WHEN YEAR = '||V_YEAR||' THEN ''his_fcst''
						 ELSE ''his_3'' END
							, ITEM_CODE
							, ITEM_NAME
							, CATEGORY_CODE
							, CATEGORY_NAME
							, L4_CEG_CODE
							, L4_CEG_SHORT_CN_NAME
							, L4_CEG_CN_NAME
							, L3_CEG_CODE
							, L3_CEG_SHORT_CN_NAME
							, L3_CEG_CN_NAME
							, L2_CEG_CODE
							, L2_CEG_CN_NAME
						)		 
			, OPT_SUM_AMT_TMP AS(
		-- 历史3年数据+当年数据，根据每个ITEM总金额从高到低金额累积
		SELECT A.TIME_FLAG
				 , A.ITEM_CODE
				 , A.ITEM_NAME
				 , A.CATEGORY_CODE
				 , A.CATEGORY_NAME
				 , A.L4_CEG_CODE
				 , A.L4_CEG_SHORT_CN_NAME
				 , A.L4_CEG_CN_NAME
				 , A.L3_CEG_CODE
				 , A.L3_CEG_SHORT_CN_NAME
				 , A.L3_CEG_CN_NAME
				 , A.L2_CEG_CODE
				 , A.L2_CEG_CN_NAME
				 , A.RECEIVE_AMT_CNY
				 , ROW_NUMBER() OVER(PARTITION BY A.TIME_FLAG,A.CATEGORY_CODE ORDER BY A.RECEIVE_AMT_CNY DESC) AS NUM       -- 根据总金额从高到低排序
				 , SUM(RECEIVE_AMT_CNY) OVER(PARTITION BY A.TIME_FLAG,A.CATEGORY_CODE ORDER BY A.RECEIVE_AMT_CNY DESC ) AS SUM_AMT_CNY         -- 按金额从高到低累计总金额
				 , (SUM(RECEIVE_AMT_CNY) OVER(PARTITION BY A.TIME_FLAG,A.CATEGORY_CODE))* '||V_ITEM_RULE||' AS MAX_AMT_CNY        -- 根据历史3年和当年，分别计算总金额*0.8
		  FROM OPT_HIS_FCST_SUM_TMP A
		 )
		,MAX_NUM_FLAG AS(
		SELECT 
					 T.TIME_FLAG
				 , T.CATEGORY_CODE
				 , CASE WHEN MIN(T.NUM)>8 THEN MIN(T.NUM)
							ELSE 8 END AS MAX_NUM
				 FROM OPT_SUM_AMT_TMP T
				 WHERE T.SUM_AMT_CNY>T.MAX_AMT_CNY
				 GROUP BY 
					 T.TIME_FLAG
				 , T.CATEGORY_CODE
		 )
		   , OPT_TOP_ITEM_TMP AS(      
		-- 取到历史数3年、当年实际数+预测数，满足每个TOP品类下累计金额占总金额80%的筛选条件的所有ITEM  
		SELECT DISTINCT T.ITEM_CODE
				 , T.ITEM_NAME
				 , T.CATEGORY_CODE
				 , T.CATEGORY_NAME
				 , T.L4_CEG_CODE
				 , T.L4_CEG_SHORT_CN_NAME
				 , T.L4_CEG_CN_NAME
				 , T.L3_CEG_CODE
				 , T.L3_CEG_SHORT_CN_NAME
				 , T.L3_CEG_CN_NAME
				 , T.L2_CEG_CODE
				 , T.L2_CEG_CN_NAME
			 FROM (
		SELECT 
					 T.TIME_FLAG
				 , T.ITEM_CODE
				 , T.ITEM_NAME
				 , T.CATEGORY_CODE
				 , T.CATEGORY_NAME
				 , T.L4_CEG_CODE
				 , T.L4_CEG_SHORT_CN_NAME
				 , T.L4_CEG_CN_NAME
				 , T.L3_CEG_CODE
				 , T.L3_CEG_SHORT_CN_NAME
				 , T.L3_CEG_CN_NAME
				 , T.L2_CEG_CODE
				 , T.L2_CEG_CN_NAME
				 , T.RECEIVE_AMT_CNY
				 , T.NUM
				 , T.SUM_AMT_CNY
				 , T.MAX_AMT_CNY
				 FROM OPT_SUM_AMT_TMP T 
				 JOIN MAX_NUM_FLAG MUF 
				 ON T.TIME_FLAG=MUF.TIME_FLAG AND T.CATEGORY_CODE=MUF.CATEGORY_CODE AND T.NUM<=MUF.MAX_NUM		
		 ) T

		 ) 
		 
	  -- 插入数据到目标表
	  INSERT INTO '||V_TO_TABLE||'
		(L2_CEG_CODE,
		 L2_CEG_CN_NAME,
		 L3_CEG_CODE,
		 L3_CEG_SHORT_CN_NAME,
		 L3_CEG_CN_NAME,
		 L4_CEG_CODE,
		 L4_CEG_SHORT_CN_NAME,
		 L4_CEG_CN_NAME,
		 CATEGORY_CODE,
		 CATEGORY_NAME,
		 ITEM_CODE,
		 ITEM_NAME,
		 VERSION_ID,
		 '||V_CALIBER_FLAG||'
		 CREATED_BY,
		 CREATION_DATE,
		 LAST_UPDATED_BY,
		 LAST_UPDATE_DATE,
		 DEL_FLAG)
		 -- 直取临时表内数据插入目标表
		SELECT T.L2_CEG_CODE,
			   T.L2_CEG_CN_NAME,
			   T.L3_CEG_CODE,
			   T.L3_CEG_SHORT_CN_NAME,
			   T.L3_CEG_CN_NAME,
			   T.L4_CEG_CODE,
			   T.L4_CEG_SHORT_CN_NAME,
			   T.L4_CEG_CN_NAME,
			   T.CATEGORY_CODE,
			   T.CATEGORY_NAME,
			   T.ITEM_CODE,
			   T.ITEM_NAME,
			   '||V_VERSION||' AS VERSION_ID,
			   '||V_IN_CALIBER||'
			   -1 AS CREATED_BY,
			   CURRENT_TIMESTAMP AS CREATION_DATE,
			   -1 AS LAST_UPDATED_BY,
			   CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
			   ''N'' AS DEL_FLAG
		  FROM OPT_TOP_ITEM_TMP T ';	
		EXECUTE  IMMEDIATE V_SQL;	   


			   
	--写入日志
	V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入规格品清单数据到 '||V_TO_TABLE||' 表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => x_success_flag,
   F_ERRBUF => 'SUCCESS');
   

   
   
-- 收集信息
  V_SQL :='ANALYSE '||V_TO_TABLE;
  EXECUTE V_SQL;

  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束');

	return 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  x_success_flag := 0;
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => x_success_flag, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

end;
$$
/

