-- Name: f_dm_repl_actual_append; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_repl_actual_append(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
最后修改人:罗若文
背景描述：对实际数bind补齐3+1年连续月份的均价: 前向补齐、后项补齐


		  1、配置基期均本要取上年非补齐的最大月份的均本数据；建议每年的配置基期均本值统一存放一个结果值；
		  
		  
参数描述: x_result_status :是否成功

事例：SELECT FIN_DM_OPT_FOI.F_DM_REPL_ACTUAL_APPEND()
*/

DECLARE
  V_SP_NAME    VARCHAR2(100) := 'FIN_DM_OPT_FOI.F_DM_REPL_ACTUAL_APPEND'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --新的版本号
  V_BEGIN_DATE TIMESTAMP ; 
  
  -- 7月版本需求新增
  V_SQL  TEXT; -- SQL逻辑
  V_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_IN_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_INSERT_LV3_PROD_RND_TEAM_CODE VARCHAR(200);

  
  V_FROM_TABLE VARCHAR(100); --来源表
  V_TO_TABLE VARCHAR(100);   --目标表

  
  V_VERSION_TABLE VARCHAR(100);
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   

  

--判断来源目标表
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_DECODE_T';--来源表
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T';--目标表
	 
IF MONTH(CURRENT_TIMESTAMP) = 1 THEN 
V_BEGIN_DATE := TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)-3)||'01','YYYYMM') ;
 
ELSIF MONTH(CURRENT_TIMESTAMP) != 1 THEN 
V_BEGIN_DATE := TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)-2)||'01','YYYYMM') ;
END IF;

 
--判断版本表

	--新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
 
  --1.清空目标表数据:
  EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_TO_TABLE;
  
  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
 --创建临时表
            DROP TABLE IF EXISTS ACTUAL_APD_TEMP;
            CREATE TEMPORARY TABLE ACTUAL_APD_TEMP (
				VERSION_ID BIGINT,
				PERIOD_YEAR INT,
				PERIOD_ID INT,
                LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
                LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
                LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
                LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
                LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
                LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
                LV3_PROD_RND_TEAM_CODE    VARCHAR(50), -- 7月版本需求新增
                LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
				REPLACEMENT_GROUP_ID VARCHAR(50),
				REPLACEMENT_DESCRIPTION VARCHAR(2000),
				REPLACING_LY_SHIP_QTY NUMERIC,
				REPLACING_CY_SHIP_QTY NUMERIC,
				RMB_AAA_REP_BASELINE_COST_AMT NUMERIC,
				RMB_AAA_BINDING_CUR_COST_AMT NUMERIC,
				PERIOD_AVG_AMT NUMERIC,
				BASE_PERIOD_AVG_AMT NUMERIC,
				NULL_FLAG VARCHAR(10),
			    APD_FLAG VARCHAR(10),
				BASE_NULL_FLAG VARCHAR(10),
			    BASE_APD_FLAG VARCHAR(10),
				VIEW_FLAG VARCHAR(10), 
				CALIBER_FLAG VARCHAR(10)
            )
            ON COMMIT PRESERVE ROWS
            DISTRIBUTE BY HASH(PERIOD_ID,REPLACEMENT_DESCRIPTION);
			
	 --创建临时表，用来存基期均本补齐后的数据
            DROP TABLE IF EXISTS ACTUAL_APD_TEMP2;
            CREATE TEMPORARY TABLE ACTUAL_APD_TEMP2 (
				VERSION_ID BIGINT,
				PERIOD_YEAR INT,
				PERIOD_ID INT,
                LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
                LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
                LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
                LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
                LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
                LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
                LV3_PROD_RND_TEAM_CODE    VARCHAR(50), -- 7月版本需求新增
                LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
				REPLACEMENT_GROUP_ID VARCHAR(50),
				REPLACEMENT_DESCRIPTION VARCHAR(2000),
				REPLACING_LY_SHIP_QTY NUMERIC,
				REPLACING_CY_SHIP_QTY NUMERIC,
				RMB_AAA_REP_BASELINE_COST_AMT NUMERIC,
				RMB_AAA_BINDING_CUR_COST_AMT NUMERIC,
				PERIOD_AVG_AMT NUMERIC,
				BASE_PERIOD_AVG_AMT NUMERIC,
				NULL_FLAG VARCHAR(10),
			    APD_FLAG VARCHAR(10),
				BASE_NULL_FLAG VARCHAR(10),
			    BASE_APD_FLAG VARCHAR(10),
				VIEW_FLAG VARCHAR(10), 
				CALIBER_FLAG VARCHAR(10)
            )
            ON COMMIT PRESERVE ROWS
            DISTRIBUTE BY HASH(PERIOD_ID,REPLACEMENT_DESCRIPTION);		
			

    --2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
        F_STEP_NUM =>  V_STEP_MUM,
        F_CAL_LOG_DESC => '实际数补齐临时表创建完成',
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS');  
        

    V_SQL := 
       'INSERT INTO ACTUAL_APD_TEMP ( 
			 PERIOD_YEAR,
			 PERIOD_ID,
             LV0_PROD_RND_TEAM_CODE,
             LV0_PROD_RD_TEAM_CN_NAME,
             LV1_PROD_RND_TEAM_CODE,
             LV1_PROD_RD_TEAM_CN_NAME,
             LV2_PROD_RND_TEAM_CODE,
             LV2_PROD_RD_TEAM_CN_NAME,
			 LV3_PROD_RND_TEAM_CODE,
             LV3_PROD_RD_TEAM_CN_NAME,
			 REPLACEMENT_GROUP_ID ,
			 REPLACEMENT_DESCRIPTION ,
			 REPLACING_LY_SHIP_QTY ,
			 REPLACING_CY_SHIP_QTY,
			 RMB_AAA_REP_BASELINE_COST_AMT,
			 RMB_AAA_BINDING_CUR_COST_AMT,
			 PERIOD_AVG_AMT, --报告期均本
			 BASE_PERIOD_AVG_AMT,   --基期均本
			 BASE_NULL_FLAG,
			 BASE_APD_FLAG,
			 NULL_FLAG,
			 APD_FLAG,
			 CALIBER_FLAG,
			 VIEW_FLAG
             )
 
    WITH ACTUAL_ITEM_TEMP AS
     (
      --实际数历史表中出现的重量级团队、采购信息维，取数范围：三年前第1月至当前系统月(不含)2023,2022,2021,2020
      SELECT DISTINCT 
					LV0_PROD_RND_TEAM_CODE,
					LV0_PROD_RD_TEAM_CN_NAME,
					LV1_PROD_RND_TEAM_CODE,
					LV1_PROD_RD_TEAM_CN_NAME,
					LV2_PROD_RND_TEAM_CODE,
					LV2_PROD_RD_TEAM_CN_NAME,
					LV3_PROD_RND_TEAM_CODE,
					LV3_PROD_RD_TEAM_CN_NAME,
					REPLACEMENT_GROUP_ID ,
					REPLACEMENT_DESCRIPTION ,
					CALIBER_FLAG,
					VIEW_FLAG
        FROM '||V_FROM_TABLE||' T
       WHERE T.PERIOD_YEAR >= YEAR(CURRENT_TIMESTAMP) - 3
         AND T.PERIOD_ID <
             CAST(TO_CHAR(CURRENT_TIMESTAMP, ''YYYYMM'') AS BIGINT)),
    
    PERIOD_DIM_TEMP AS
     (
      --生成连续月份, 三年前第1月至当前系统月(不含)
      SELECT CAST(TO_CHAR(ADD_MONTHS('''||V_BEGIN_DATE||''',NUM.VAL - 1),''YYYYMM'') AS BIGINT)
          AS PERIOD_ID
        FROM GENERATE_SERIES(1,
                              TO_NUMBER(TIMESTAMPDIFF(MONTH,'''||
                                                      V_BEGIN_DATE||''',
                                                      CURRENT_TIMESTAMP)),
                              1) NUM(VAL)),
    
    CROSS_JOIN_TEMP AS
     (
      --生成连续年月的发散维
      SELECT CAST(SUBSTR(B.PERIOD_ID, 1, 4) AS BIGINT) AS PERIOD_YEAR,
				B.PERIOD_ID,
				T.VIEW_FLAG,
				T.LV0_PROD_RND_TEAM_CODE,
				T.LV0_PROD_RD_TEAM_CN_NAME,
				T.LV1_PROD_RND_TEAM_CODE,
				T.LV1_PROD_RD_TEAM_CN_NAME,
				T.LV2_PROD_RND_TEAM_CODE,
				T.LV2_PROD_RD_TEAM_CN_NAME,
				T.LV3_PROD_RND_TEAM_CODE,
				T.LV3_PROD_RD_TEAM_CN_NAME,
				T.REPLACEMENT_GROUP_ID ,
				T.REPLACEMENT_DESCRIPTION ,
				T.CALIBER_FLAG
        FROM ACTUAL_ITEM_TEMP T, PERIOD_DIM_TEMP B)
                
            SELECT 
				T.PERIOD_YEAR,
				T.PERIOD_ID,
				T.LV0_PROD_RND_TEAM_CODE,
				T.LV0_PROD_RD_TEAM_CN_NAME,
				T.LV1_PROD_RND_TEAM_CODE,
				T.LV1_PROD_RD_TEAM_CN_NAME,
				T.LV2_PROD_RND_TEAM_CODE,
				T.LV2_PROD_RD_TEAM_CN_NAME,
				T.LV3_PROD_RND_TEAM_CODE,
				T.LV3_PROD_RD_TEAM_CN_NAME,
				T.REPLACEMENT_GROUP_ID ,
				T.REPLACEMENT_DESCRIPTION ,
				T2.REPLACING_LY_SHIP_QTY ,
				T2.REPLACING_CY_SHIP_QTY,
				T2.RMB_AAA_REP_BASELINE_COST_AMT,
				T2.RMB_AAA_BINDING_CUR_COST_AMT,
				T2.PERIOD_AVG_AMT,
				T2.BASE_PERIOD_AVG_AMT , 
				DECODE(T2.BASE_PERIOD_AVG_AMT, NULL, 0, 1) AS BASE_NULL_FLAG, --空标识, 用于sum开窗累计
				DECODE(T2.BASE_PERIOD_AVG_AMT, NULL, ''Y'', ''N'') AS BASE_APD_FLAG, --补齐标识：Y为补齐，N为原始
				DECODE(T2.PERIOD_AVG_AMT, NULL, 0, 1) AS NULL_FLAG, --空标识, 用于sum开窗累计
				DECODE(T2.PERIOD_AVG_AMT, NULL, ''Y'', ''N'') AS APD_FLAG, --补齐标识：Y为补齐，N为原始
				T.CALIBER_FLAG,
				T.VIEW_FLAG
        FROM CROSS_JOIN_TEMP T
        LEFT JOIN '||V_FROM_TABLE||' T2
          ON  NVL(T.LV0_PROD_RND_TEAM_CODE,0) = NVL(T2.LV0_PROD_RND_TEAM_CODE,0)
         AND NVL(T.LV1_PROD_RND_TEAM_CODE,1) = NVL(T2.LV1_PROD_RND_TEAM_CODE,1)
         AND NVL(T.LV2_PROD_RND_TEAM_CODE,2) = NVL(T2.LV2_PROD_RND_TEAM_CODE,2)
		 AND NVL(T.LV3_PROD_RND_TEAM_CODE,2) = NVL(T2.LV3_PROD_RND_TEAM_CODE,2)
         AND T.REPLACEMENT_GROUP_ID = T2.REPLACEMENT_GROUP_ID
         AND T.REPLACEMENT_DESCRIPTION = T2.REPLACEMENT_DESCRIPTION
         AND T.PERIOD_ID = T2.PERIOD_ID
         AND T.CALIBER_FLAG = T2.CALIBER_FLAG
		 AND T.VIEW_FLAG =  T2.VIEW_FLAG
		 ';
		DBMS_OUTPUT.PUT_LINE(V_SQL);
         EXECUTE IMMEDIATE V_SQL;
                 
 --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入数据到实际数补齐临时表',
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');   
   

 --2.先对基期均本进行补齐
      V_SQL := 

           'INSERT INTO ACTUAL_APD_TEMP2 
                (
				PERIOD_YEAR,
				PERIOD_ID,
				LV0_PROD_RND_TEAM_CODE,
				LV0_PROD_RD_TEAM_CN_NAME,
				LV1_PROD_RND_TEAM_CODE,
				LV1_PROD_RD_TEAM_CN_NAME,
				LV2_PROD_RND_TEAM_CODE,
				LV2_PROD_RD_TEAM_CN_NAME,
				LV3_PROD_RND_TEAM_CODE,
				LV3_PROD_RD_TEAM_CN_NAME,
				REPLACEMENT_GROUP_ID ,
				REPLACEMENT_DESCRIPTION ,
				REPLACING_LY_SHIP_QTY ,
				REPLACING_CY_SHIP_QTY,
				RMB_AAA_REP_BASELINE_COST_AMT,
				RMB_AAA_BINDING_CUR_COST_AMT,
				PERIOD_AVG_AMT, --报告期均本
				BASE_PERIOD_AVG_AMT , --基期均本
				APD_FLAG,
			    BASE_APD_FLAG,
				NULL_FLAG,
				CALIBER_FLAG,
				VIEW_FLAG

                )
                
    WITH BASE_FORWARD_FILLER_TEMP AS
     (
      --按照重量级团队, 采购信息维补齐, 前向补齐均价
      SELECT  
			  T.PERIOD_YEAR,
              T.PERIOD_ID,
			  T.LV0_PROD_RND_TEAM_CODE,
			  T.LV0_PROD_RD_TEAM_CN_NAME,
			  T.LV1_PROD_RND_TEAM_CODE,
			  T.LV1_PROD_RD_TEAM_CN_NAME,
			  T.LV2_PROD_RND_TEAM_CODE,
			  T.LV2_PROD_RD_TEAM_CN_NAME,
			  T.LV3_PROD_RND_TEAM_CODE,
			  T.LV3_PROD_RD_TEAM_CN_NAME,
			  T.REPLACEMENT_GROUP_ID ,
			  T.REPLACEMENT_DESCRIPTION ,
			  T.REPLACING_LY_SHIP_QTY ,
			  T.REPLACING_CY_SHIP_QTY,
			  T.RMB_AAA_REP_BASELINE_COST_AMT,
			  T.RMB_AAA_BINDING_CUR_COST_AMT,
			  T.PERIOD_AVG_AMT,
              T.BASE_PERIOD_AVG_AMT,
              FIRST_VALUE(T.BASE_PERIOD_AVG_AMT) OVER(PARTITION BY T.LV0_PROD_RND_TEAM_CODE,T.LV1_PROD_RND_TEAM_CODE,T.LV2_PROD_RND_TEAM_CODE,T.LV3_PROD_RND_TEAM_CODE, T.REPLACEMENT_GROUP_ID, T.REPLACEMENT_DESCRIPTION,  T.AVG_AMT_FLAG,T.VIEW_FLAG,T.CALIBER_FLAG ORDER BY T.PERIOD_ID) AS AVG_AMT_2, --新补齐的均价字段
              T.AVG_AMT_FLAG,
              T.APD_FLAG,
			  T.NULL_FLAG,
			  T.BASE_APD_FLAG,
			  T.CALIBER_FLAG,
			  T.VIEW_FLAG
        FROM (SELECT  T.PERIOD_YEAR,
					  T.PERIOD_ID,
					  T.LV0_PROD_RND_TEAM_CODE,
					  T.LV0_PROD_RD_TEAM_CN_NAME,
					  T.LV1_PROD_RND_TEAM_CODE,
					  T.LV1_PROD_RD_TEAM_CN_NAME,
					  T.LV2_PROD_RND_TEAM_CODE,
					  T.LV2_PROD_RD_TEAM_CN_NAME,
					  T.LV3_PROD_RND_TEAM_CODE,
					  T.LV3_PROD_RD_TEAM_CN_NAME,
					  T.REPLACEMENT_GROUP_ID ,
					  T.REPLACEMENT_DESCRIPTION ,
					  T.REPLACING_LY_SHIP_QTY ,
					  T.REPLACING_CY_SHIP_QTY,
					  T.RMB_AAA_REP_BASELINE_COST_AMT,
					  T.RMB_AAA_BINDING_CUR_COST_AMT,
                      T.BASE_PERIOD_AVG_AMT,
					  T.PERIOD_AVG_AMT,
                      SUM(T.BASE_NULL_FLAG) OVER(PARTITION BY T.LV0_PROD_RND_TEAM_CODE,T.LV1_PROD_RND_TEAM_CODE,T.LV2_PROD_RND_TEAM_CODE,T.LV3_PROD_RND_TEAM_CODE,T.REPLACEMENT_GROUP_ID, T.REPLACEMENT_DESCRIPTION,T.VIEW_FLAG,T.CALIBER_FLAG ORDER  BY T.PERIOD_ID) AS AVG_AMT_FLAG, --均价标识: 为空不参与累计加1
                      T.APD_FLAG,
					  T.NULL_FLAG,
					  T.BASE_APD_FLAG,
                      T.CALIBER_FLAG,
					  T.VIEW_FLAG
                 FROM ACTUAL_APD_TEMP T) T)
    
	
	 
		   SELECT  
			  T.PERIOD_YEAR,
              T.PERIOD_ID,
			  T.LV0_PROD_RND_TEAM_CODE,
			  T.LV0_PROD_RD_TEAM_CN_NAME,
			  T.LV1_PROD_RND_TEAM_CODE,
			  T.LV1_PROD_RD_TEAM_CN_NAME,
			  T.LV2_PROD_RND_TEAM_CODE,
			  T.LV2_PROD_RD_TEAM_CN_NAME,
			  T.LV3_PROD_RND_TEAM_CODE,
			  T.LV3_PROD_RD_TEAM_CN_NAME,
			  T.REPLACEMENT_GROUP_ID ,
			  T.REPLACEMENT_DESCRIPTION ,
			  T.REPLACING_LY_SHIP_QTY ,
			  T.REPLACING_CY_SHIP_QTY,
			  T.RMB_AAA_REP_BASELINE_COST_AMT,
			  T.RMB_AAA_BINDING_CUR_COST_AMT,
			  T.PERIOD_AVG_AMT,
              COALESCE(T.BASE_PERIOD_AVG_AMT,T.AVG_AMT_2,T.BASE_PERIOD_AVG_AMT,0) BASE_PERIOD_AVG_AMT,
              T.APD_FLAG, 
			  T.BASE_APD_FLAG,
			  T.NULL_FLAG,
			  CALIBER_FLAG,
			  VIEW_FLAG
	
	FROM  BASE_FORWARD_FILLER_TEMP T
	';
	
	DBMS_OUTPUT.PUT_LINE(V_SQL);
	EXECUTE IMMEDIATE  V_SQL;
	
	  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '基期均本补齐完成',
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
	
	 --3.先对会计期均本进行补齐并插入结果表
	 V_SQL := '
	 INSERT INTO '||V_TO_TABLE||' (
		VERSION_ID,
		PERIOD_YEAR,
		PERIOD_ID,
		LV0_PROD_RND_TEAM_CODE,
		LV0_PROD_RD_TEAM_CN_NAME,
		LV1_PROD_RND_TEAM_CODE,
		LV1_PROD_RD_TEAM_CN_NAME,
		LV2_PROD_RND_TEAM_CODE,
		LV2_PROD_RD_TEAM_CN_NAME,
		LV3_PROD_RND_TEAM_CODE,
		LV3_PROD_RD_TEAM_CN_NAME,
		REPLACEMENT_GROUP_ID ,
		REPLACEMENT_group_cn_name,
		RMB_AAA_BINDING_CUR_COST_AMT,
		PERIOD_AVG_AMT,
		BASE_PERIOD_AVG_AMT,
		VIEW_FLAG,
		CALIBER_FLAG,
		CREATED_BY ,
		CREATION_DATE ,
		LAST_UPDATED_BY ,
		LAST_UPDATE_DATE ,
		DEL_FLAG,
		APPEND_FLAG 
		)
	   WITH FORWARD_FILLER_TEMP AS
     (
      --按照重量级团队, 采购信息维补齐, 前向补齐均价
      SELECT  
			  T.PERIOD_YEAR,
              T.PERIOD_ID,
			  T.LV0_PROD_RND_TEAM_CODE,
			  T.LV0_PROD_RD_TEAM_CN_NAME,
			  T.LV1_PROD_RND_TEAM_CODE,
			  T.LV1_PROD_RD_TEAM_CN_NAME,
			  T.LV2_PROD_RND_TEAM_CODE,
			  T.LV2_PROD_RD_TEAM_CN_NAME,
			  T.LV3_PROD_RND_TEAM_CODE,
			  T.LV3_PROD_RD_TEAM_CN_NAME,
			  T.REPLACEMENT_GROUP_ID ,
			  T.REPLACEMENT_DESCRIPTION ,
			  T.REPLACING_LY_SHIP_QTY ,
			  T.REPLACING_CY_SHIP_QTY,
			  T.RMB_AAA_REP_BASELINE_COST_AMT,
			  T.RMB_AAA_BINDING_CUR_COST_AMT,
			  T.PERIOD_AVG_AMT,
              T.BASE_PERIOD_AVG_AMT,
              FIRST_VALUE(T.PERIOD_AVG_AMT) OVER(PARTITION BY T.LV0_PROD_RND_TEAM_CODE,T.LV1_PROD_RND_TEAM_CODE,T.LV2_PROD_RND_TEAM_CODE,T.LV3_PROD_RND_TEAM_CODE, T.REPLACEMENT_GROUP_ID, T.REPLACEMENT_DESCRIPTION, T.CALIBER_FLAG ,T.VIEW_FLAG, T.AVG_AMT_FLAG ORDER BY T.PERIOD_ID) AS AVG_AMT_2, --新补齐的均价字段
              T.AVG_AMT_FLAG,
              T.APD_FLAG,
			  T.NULL_FLAG,
			  T.CALIBER_FLAG,
			  T.VIEW_FLAG
        FROM (SELECT  T.PERIOD_YEAR,
					  T.PERIOD_ID,
					  T.LV0_PROD_RND_TEAM_CODE,
					  T.LV0_PROD_RD_TEAM_CN_NAME,
					  T.LV1_PROD_RND_TEAM_CODE,
					  T.LV1_PROD_RD_TEAM_CN_NAME,
					  T.LV2_PROD_RND_TEAM_CODE,
					  T.LV2_PROD_RD_TEAM_CN_NAME,
					  T.LV3_PROD_RND_TEAM_CODE,
					  T.LV3_PROD_RD_TEAM_CN_NAME,
					  T.REPLACEMENT_GROUP_ID ,
					  T.REPLACEMENT_DESCRIPTION ,
					  T.REPLACING_LY_SHIP_QTY ,
					  T.REPLACING_CY_SHIP_QTY,
					  T.RMB_AAA_REP_BASELINE_COST_AMT,
					  T.RMB_AAA_BINDING_CUR_COST_AMT,
					  T.PERIOD_AVG_AMT,
                      T.BASE_PERIOD_AVG_AMT,  --不是12月的基期均本置空，以免取到错误月份的值
                      SUM(T.NULL_FLAG) OVER(PARTITION BY T.LV0_PROD_RND_TEAM_CODE,T.LV1_PROD_RND_TEAM_CODE,T.LV2_PROD_RND_TEAM_CODE,T.LV3_PROD_RND_TEAM_CODE,T.REPLACEMENT_GROUP_ID, T.REPLACEMENT_DESCRIPTION ,T.CALIBER_FLAG,T.VIEW_FLAG  ORDER BY T.PERIOD_ID) AS AVG_AMT_FLAG, --均价标识: 为空不参与累计加1
                      T.APD_FLAG,
					  T.NULL_FLAG,
                      T.CALIBER_FLAG,
					  T.VIEW_FLAG
                 FROM ACTUAL_APD_TEMP2 T)T )
    
	
	 
		   SELECT  
				'||V_VERSION_ID||',
			  T.PERIOD_YEAR,
              T.PERIOD_ID,
			  T.LV0_PROD_RND_TEAM_CODE,
			  T.LV0_PROD_RD_TEAM_CN_NAME,
			  T.LV1_PROD_RND_TEAM_CODE,
			  T.LV1_PROD_RD_TEAM_CN_NAME,
			  T.LV2_PROD_RND_TEAM_CODE,
			  T.LV2_PROD_RD_TEAM_CN_NAME,
			  T.LV3_PROD_RND_TEAM_CODE,
			  T.LV3_PROD_RD_TEAM_CN_NAME,
			  T.REPLACEMENT_GROUP_ID ,
			  T.REPLACEMENT_DESCRIPTION ,
			  T.RMB_AAA_BINDING_CUR_COST_AMT,
			  COALESCE(T.PERIOD_AVG_AMT,T.AVG_AMT_2,T.PERIOD_AVG_AMT,0) PERIOD_AVG_AMT,
              T.BASE_PERIOD_AVG_AMT,
			  T.VIEW_FLAG,
			  T.CALIBER_FLAG,
			  -1 AS CREATED_BY ,
			  CURRENT_TIMESTAMP AS CREATION_DATE ,
			  -1 AS LAST_UPDATED_BY ,
			  CURRENT_TIMESTAMP AS LAST_UPDATE_DATE ,
			  ''N'' AS DEL_FLAG,
              T.APD_FLAG
	
	FROM  FORWARD_FILLER_TEMP T
	';
	
	DBMS_OUTPUT.PUT_LINE(V_SQL);
	EXECUTE IMMEDIATE  V_SQL;
	

  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '会计期均本补齐完成，并插入结果表',
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
	

  --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;

  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

